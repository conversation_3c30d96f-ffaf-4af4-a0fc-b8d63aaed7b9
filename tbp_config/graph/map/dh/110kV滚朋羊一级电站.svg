<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549582954498" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:4绕组母线PT_0" viewBox="0,0,25,20">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="0"/>
   <ellipse cx="12.75" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.75" cy="9.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.67" cy="14.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.82" cy="9.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="5.583333333333334" y1="7.38888888888889" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333336" x2="5.583333333333336" y1="12" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="5.5" y1="7.38888888888889" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="12.75" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="12.75" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.75" x2="12.75" y1="7.500000000000002" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.58333333333333" x2="12.58333333333333" y1="12.63888888888889" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.58333333333333" x2="12.58333333333333" y1="12.63888888888889" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="17.25" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="19.31481481481482" y1="10.21612466124661" y2="7.749999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.94444444444444" x2="20.62962962962963" y1="10.21612466124661" y2="7.749999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="21.94444444444444" y1="10.21612466124661" y2="10.21612466124661"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_0" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.416666666666625" x2="14.8889734851558" y1="30.74450652239035" y2="19.51308960898588"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_1" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.8889734851558" x2="14.8889734851558" y1="32.25" y2="19.51308960898588"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_2" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.88620689655174" x2="9.700000000000015" y1="19.49501474926254" y2="32.31404129793511"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.699999999999994" x2="20.24999999999999" y1="19.33333333333333" y2="32.38333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:RT1122_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="22.91666666666667" y2="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="18.16666666666666" y2="20.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="18.16666666666666" y2="20.16666666666666"/>
   <path d="M 13 11 L 17 11 L 15 8 L 13 11 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="15.41666666666666" y2="18.16666666666666"/>
   <ellipse cx="14.95" cy="18.02" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.95" cy="9.92" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:20210316PT_0" viewBox="0,0,15,26">
   <use terminal-index="0" type="0" x="7.45" xlink:href="#terminal" y="0.09999999999999964"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="8.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="6.833333333333331" y2="0"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="4.916666666666666" y1="11.25" y2="13.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="10.5" y1="11.25" y2="13.25"/>
   <ellipse cx="7.53" cy="11.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.5" cy="20.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333333" x2="7.583333333333333" y1="17.5" y2="20.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333332" x2="4.999999999999998" y1="20.5" y2="22.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="10.58333333333334" y1="20.5" y2="22.5"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_0" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.05" x2="7.05" y1="1.166666666666664" y2="26.33333333333334"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="10.66666666666667" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="26.5417774506975" y2="23.30486537657277"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="26.5417774506975" y2="23.30486537657277"/>
   <rect fill-opacity="0" height="6.25" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,8.63) scale(1,1) translate(0,0)" width="2.83" x="5.58" y="5.5"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_1" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.05" x2="7.05" y1="2.833333333333334" y2="14.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="2.617622152595477" y2="5.500000000000003"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="10.66666666666666" y1="2.617622152595477" y2="5.500000000000003"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="10.66666666666667" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="24.65357874079139" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="26.5417774506975" y2="23.30486537657277"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="24.65357874079139" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="26.5417774506975" y2="23.30486537657277"/>
   <rect fill-opacity="0" height="6.25" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,8.63) scale(1,1) translate(0,0)" width="2.83" x="5.58" y="5.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="7.049999999999999" y1="16" y2="18.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.133333333333333" x2="7.133333333333333" y1="18.16666666666667" y2="24.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_2" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.33333333333333" x2="3.333333333333333" y1="9.166666666666668" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.666666666666667" x2="10.83333333333333" y1="9.124593892873616" y2="18.41666666666667"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="12.5" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="26.56537356321841" y2="21.7550287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="26.56537356321841" y2="21.7550287356322"/>
  </symbol>
  <symbol id="Accessory:4绕组PT带接地_0" viewBox="0,0,30,35">
   <use terminal-index="0" type="0" x="14.94238998217831" xlink:href="#terminal" y="31.09945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="13" y1="18.19406992327969" y2="17.19406992327969"/>
   <rect fill-opacity="0" height="14.44" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,4.46,16.81) scale(1,-1) translate(0,-1028.21)" width="5.58" x="1.67" y="9.58"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="13" y1="14.19406992327969" y2="15.19406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.583333333333325" x2="4.583333333333325" y1="30.69406992327969" y2="16.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.19406992327969" y2="14.19406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.583333333333325" x2="3.583333333333325" y1="15.69406992327969" y2="20.69406992327969"/>
   <path d="M 20.6667 26.75 L 20.6667 30.75 L 4.66667 30.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.583333333333325" x2="5.583333333333325" y1="15.69406992327969" y2="20.69406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5" x2="26.5" y1="16.5" y2="16.5"/>
   <path d="M 20.75 22.8333 L 26.5833 22.8333 L 26.5833 6.83333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.59610027172149" x2="4.59610027172149" y1="9.527403256613018" y2="6.379186935280762"/>
   <ellipse cx="20.73" cy="22.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.625884107343747" x2="2.349068262659902" y1="6.31127839976627" y2="6.31127839976627"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.792550774010413" x2="3.182401595993234" y1="5.06127839976627" y2="5.06127839976627"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.209217440677081" x2="3.765734929326568" y1="3.81127839976627" y2="3.81127839976627"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="23.09906826265991" y1="22.70662962336982" y2="23.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="18.29906826265991" y1="22.70662962336982" y2="23.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="20.69906826265991" y1="20.23154965466559" y2="22.7066296233698"/>
   <ellipse cx="14.48" cy="22.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.73" cy="16.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.44906826265991" x2="16.84906826265991" y1="22.70662962336982" y2="23.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.44906826265991" x2="14.44906826265991" y1="20.23154965466559" y2="22.7066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="18.29906826265991" y1="16.45662962336982" y2="17.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="23.09906826265991" y1="16.45662962336982" y2="17.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="20.69906826265991" y1="13.9815496546656" y2="16.4566296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.44906826265991" x2="12.04906826265991" y1="22.70662962336982" y2="23.94416960772192"/>
   <ellipse cx="14.48" cy="16.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="28.79255077401042" x2="24.51573492932657" y1="6.727945066432934" y2="6.727945066432934"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.95921744067708" x2="25.34906826265991" y1="5.477945066432934" y2="5.477945066432934"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.37588410734375" x2="25.93240159599324" y1="4.227945066432941" y2="4.227945066432941"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="Accessory:线路PT11带避雷器_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="23.75" y2="23.75"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,11) scale(1,1) translate(0,0)" width="6" x="17" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="20" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="32" y1="17" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.13040845230975" x2="32.13040845230975" y1="16.76788570496156" y2="23.53635804601289"/>
   <rect fill-opacity="0" height="6.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,32.04,24.56) scale(1,1) translate(0,0)" width="15.34" x="24.37" y="21.53"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.98821300514558" x2="19.98821300514558" y1="1.029523490692871" y2="18.75"/>
   <path d="M 30.0147 23.4481 L 34.2865 23.4481 L 32.115 28.6155 L 30.0147 23.4481" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <ellipse cx="19.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.22179295459144" x2="32.22179295459144" y1="32.22550978083666" y2="36.2625308385742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.01788570496155" x2="35.42570020422134" y1="36.23023467011234" y2="36.23023467011234"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.26384963537316" x2="34.53572596821302" y1="37.52208140858838" y2="37.52208140858838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.15382387138148" x2="33.11176719059974" y1="38.49096646244535" y2="38.49096646244535"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="32.91666666666667" y2="32.91666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV滚朋羊一级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="47.43" xlink:href="logo.png" y="30.71"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.429,62.7143) scale(1,1) translate(0,0)" writing-mode="lr" x="196.43" xml:space="preserve" y="66.20999999999999" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,198.929,60.4046) scale(1,1) translate(0,0)" writing-mode="lr" x="198.93" xml:space="preserve" y="69.40000000000001" zvalue="5">110kV滚朋羊一级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="239" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,183.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="171.25" zvalue="302"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,183.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="187.75" zvalue="302">信号一览</text>
  <line fill="none" id="27" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="381.4285714285714" x2="381.4285714285714" y1="-1.285714285714334" y2="1028.714285714286" zvalue="6"/>
  <line fill="none" id="25" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.428571428571672" x2="374.4285714285712" y1="134.5847783283681" y2="134.5847783283681" zvalue="8"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,712.537,258.505) scale(1,1) translate(-9.35301e-13,0)" writing-mode="lr" x="712.54" xml:space="preserve" y="263" zvalue="38">131</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,703.138,303.866) scale(1,1) translate(0,0)" writing-mode="lr" x="703.14" xml:space="preserve" y="308.37" zvalue="39">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,704.934,207.475) scale(1,1) translate(2.49022e-12,0)" writing-mode="lr" x="704.9299999999999" xml:space="preserve" y="211.98" zvalue="42">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,750.016,304.286) scale(1,1) translate(0,0)" writing-mode="lr" x="750.02" xml:space="preserve" y="308.79" zvalue="49">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,756.016,249.768) scale(1,1) translate(0,0)" writing-mode="lr" x="756.02" xml:space="preserve" y="254.27" zvalue="51">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,756.016,186.093) scale(1,1) translate(0,0)" writing-mode="lr" x="756.02" xml:space="preserve" y="190.59" zvalue="53">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1193.88,243.269) scale(1,1) translate(0,0)" writing-mode="lr" x="1193.88" xml:space="preserve" y="247.77" zvalue="59">132</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1184.42,294.519) scale(1,1) translate(0,0)" writing-mode="lr" x="1184.42" xml:space="preserve" y="299.02" zvalue="60">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1186.22,182.128) scale(1,1) translate(0,0)" writing-mode="lr" x="1186.22" xml:space="preserve" y="186.63" zvalue="63">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1227.24,288.175) scale(1,1) translate(0,0)" writing-mode="lr" x="1227.24" xml:space="preserve" y="292.67" zvalue="70">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1225.77,230.068) scale(1,1) translate(0,0)" writing-mode="lr" x="1225.77" xml:space="preserve" y="234.57" zvalue="72">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1223.53,160.217) scale(1,1) translate(0,0)" writing-mode="lr" x="1223.53" xml:space="preserve" y="164.72" zvalue="74">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1441.27,244.616) scale(1,1) translate(0,0)" writing-mode="lr" x="1441.27" xml:space="preserve" y="249.12" zvalue="80">133</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1431.82,295.866) scale(1,1) translate(0,0)" writing-mode="lr" x="1431.82" xml:space="preserve" y="300.37" zvalue="81">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1433.61,183.475) scale(1,1) translate(0,0)" writing-mode="lr" x="1433.61" xml:space="preserve" y="187.98" zvalue="84">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1472.69,286.286) scale(1,1) translate(0,0)" writing-mode="lr" x="1472.69" xml:space="preserve" y="290.79" zvalue="91">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1468.1,232.546) scale(1,1) translate(5.18733e-12,0)" writing-mode="lr" x="1468.1" xml:space="preserve" y="237.05" zvalue="93">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1468.87,166.035) scale(1,1) translate(5.19005e-12,0)" writing-mode="lr" x="1468.87" xml:space="preserve" y="170.53" zvalue="95">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,560.389,306.111) scale(1,1) translate(0,0)" writing-mode="lr" x="560.39" xml:space="preserve" y="310.61" zvalue="114">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,689.534,113) scale(1,1) translate(0,0)" writing-mode="lr" x="689.53" xml:space="preserve" y="117.5" zvalue="116">110kV滚朋羊一级线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1173.53,88.8) scale(1,1) translate(0,0)" writing-mode="lr" x="1173.53" xml:space="preserve" y="93.3" zvalue="120">110kV滚朋羊一二级线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1419.69,92.7888) scale(1,1) translate(0,0)" writing-mode="lr" x="1419.69" xml:space="preserve" y="97.29000000000001" zvalue="126">110kV滚朋羊小一级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,671.288,370.879) scale(1,1) translate(0,0)" writing-mode="lr" x="671.29" xml:space="preserve" y="375.38" zvalue="131">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,755.611,441.785) scale(1,1) translate(0,0)" writing-mode="lr" x="755.61" xml:space="preserve" y="446.28" zvalue="133">7</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1099.28,375.639) scale(1,1) translate(0,0)" writing-mode="lr" x="1099.28" xml:space="preserve" y="380.14" zvalue="141">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1108.61,424.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1108.61" xml:space="preserve" y="428.97" zvalue="143">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1175.61,404.319) scale(1,1) translate(0,0)" writing-mode="lr" x="1175.61" xml:space="preserve" y="408.82" zvalue="146">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1036.35,539.831) scale(1,1) translate(0,0)" writing-mode="lr" x="1036.35" xml:space="preserve" y="544.33" zvalue="150">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1130.15,642.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1130.15" xml:space="preserve" y="647.13" zvalue="152">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1175.92,450.833) scale(1,1) translate(1.16124e-12,0)" writing-mode="lr" x="1175.92" xml:space="preserve" y="455.33" zvalue="157">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1069.58,464.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1069.58" xml:space="preserve" y="469.28" zvalue="160">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1178.11,496.139) scale(1,1) translate(0,1.08278e-13)" writing-mode="lr" x="1178.11" xml:space="preserve" y="500.64" zvalue="165">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1211.64,569.636) scale(1,1) translate(-1.06238e-12,0)" writing-mode="lr" x="1211.64" xml:space="preserve" y="574.14" zvalue="168">1010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,497.958,659.222) scale(1,1) translate(0,0)" writing-mode="lr" x="497.96" xml:space="preserve" y="663.72" zvalue="170">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,617.217,753.763) scale(1,1) translate(0,0)" writing-mode="lr" x="617.22" xml:space="preserve" y="758.26" zvalue="173">631</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,584.343,931.091) scale(1,1) translate(0,0)" writing-mode="lr" x="584.34" xml:space="preserve" y="935.59" zvalue="175">#1发电机10MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,446.889,838.778) scale(1,1) translate(0,0)" writing-mode="lr" x="446.89" xml:space="preserve" y="843.28" zvalue="180">6911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="189" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,653.778,830.033) scale(1,1) translate(0,0)" writing-mode="lr" x="653.78" xml:space="preserve" y="834.53" zvalue="194">6912</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1094.65,898.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1094.65" xml:space="preserve" y="903.22" zvalue="197">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1117.87,753.958) scale(1,1) translate(0,0)" writing-mode="lr" x="1117.87" xml:space="preserve" y="758.46" zvalue="199">633</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1163.22,820.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1163.22" xml:space="preserve" y="824.83" zvalue="203">63367</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1440.38,622.922) scale(1,1) translate(-1.89596e-12,0)" writing-mode="lr" x="1440.38" xml:space="preserve" y="627.42" zvalue="208">6901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="170" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1505.25,753.763) scale(1,1) translate(0,0)" writing-mode="lr" x="1505.25" xml:space="preserve" y="758.26" zvalue="212">632</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1472.34,931.091) scale(1,1) translate(0,0)" writing-mode="lr" x="1472.34" xml:space="preserve" y="935.59" zvalue="215">#2发电机10MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1334.89,838.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1334.89" xml:space="preserve" y="843.28" zvalue="221">6921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1541.78,830.033) scale(1,1) translate(0,0)" writing-mode="lr" x="1541.78" xml:space="preserve" y="834.53" zvalue="226">6922</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="192" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1651.58,585.833) scale(1,1) translate(0,1.91624e-13)" writing-mode="lr" x="1651.58" xml:space="preserve" y="590.33" zvalue="231">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1673.69,388.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1673.69" xml:space="preserve" y="392.61" zvalue="234">0311</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1713.65,268.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1713.65" xml:space="preserve" y="272.94" zvalue="240">10kV施工电源</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="245" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="271" y2="271"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="245" y2="271"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="245" y2="271"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="245" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="271" y2="271"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="245" y2="271"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="245" y2="271"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="271" y2="271"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="295.25" y2="295.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="271" y2="295.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="271" y2="295.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="271" y2="271"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="295.25" y2="295.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="271" y2="295.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="271" y2="295.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="295.25" y2="295.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="318" y2="318"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="295.25" y2="318"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="295.25" y2="318"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="295.25" y2="295.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="318" y2="318"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="295.25" y2="318"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="295.25" y2="318"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="318" y2="318"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="340.75" y2="340.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="318" y2="340.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="318" y2="340.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="318" y2="318"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="340.75" y2="340.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="318" y2="340.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="318" y2="340.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="340.75" y2="340.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="363.5" y2="363.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="340.75" y2="363.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="340.75" y2="363.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="340.75" y2="340.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="363.5" y2="363.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="340.75" y2="363.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="340.75" y2="363.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="363.5" y2="363.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="386.25" y2="386.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="363.5" y2="386.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="363.5" y2="386.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="363.5" y2="363.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="386.25" y2="386.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="363.5" y2="386.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="363.5" y2="386.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="386.25" y2="386.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="409" y2="409"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="386.25" y2="409"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="386.25" y2="409"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="386.25" y2="386.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="409" y2="409"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="386.25" y2="409"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="386.25" y2="409"/>
  <line fill="none" id="268" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="490.8704926140824" y2="490.8704926140824" zvalue="276"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="930" y2="930"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="969.1632999999999" y2="969.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="930" y2="969.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="930" y2="969.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="930" y2="930"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="969.1632999999999" y2="969.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="930" y2="969.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="930" y2="969.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="969.16327" y2="969.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="997.08167" y2="997.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="969.16327" y2="997.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="969.16327" y2="997.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="969.16327" y2="969.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="997.08167" y2="997.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="969.16327" y2="997.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="969.16327" y2="997.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="969.16327" y2="969.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="997.08167" y2="997.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="969.16327" y2="997.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="969.16327" y2="997.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="969.16327" y2="969.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="997.08167" y2="997.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="969.16327" y2="997.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="969.16327" y2="997.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="997.0816" y2="997.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1025" y2="1025"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="997.0816" y2="1025"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="997.0816" y2="1025"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="997.0816" y2="997.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1025" y2="1025"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="997.0816" y2="1025"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="997.0816" y2="1025"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="997.0816" y2="997.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1025" y2="1025"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="997.0816" y2="1025"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="997.0816" y2="1025"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="997.0816" y2="997.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1025" y2="1025"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="997.0816" y2="1025"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="997.0816" y2="1025"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,950) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="956" zvalue="278">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="265" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,984) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="990" zvalue="279">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,984) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="990" zvalue="280">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="263" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1012) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1018" zvalue="281">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1012) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1018" zvalue="282">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,564.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="569" zvalue="284">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,183.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="188.34" zvalue="285">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,183.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="188.34" zvalue="286">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.054,952) scale(1,1) translate(0,0)" writing-mode="lr" x="237.05" xml:space="preserve" y="958" zvalue="287">GunPengYang1-01-2012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,327.054,984) scale(1,1) translate(0,0)" writing-mode="lr" x="327.05" xml:space="preserve" y="990" zvalue="289">20200806</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,258) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="262.5" zvalue="290">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,258) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="262.5" zvalue="291">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="250" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,331.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="335.75" zvalue="292">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="242" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,355.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="359.75" zvalue="294">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="237" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,284) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="288.5" zvalue="303">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="235" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,284) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="288.5" zvalue="304">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="233" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,377.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="381.75" zvalue="307">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="230" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,376.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="380.75" zvalue="309">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,400.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="404.75" zvalue="311">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,399.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="403.75" zvalue="313">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="227" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,307) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="311.5" zvalue="315">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="226" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,306) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="310.5" zvalue="317">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1034.5,564) scale(1,1) translate(0,0)" writing-mode="lr" x="1034.5" xml:space="preserve" y="568.5" zvalue="329">25MVA</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="171.25" zvalue="302"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BreakerClass">
  <g id="106">
   <use class="kv110" height="20" transform="rotate(0,689.546,259.582) scale(1.31718,1.19743) translate(-164.457,-40.8257)" width="10" x="682.9600634841954" xlink:href="#Breaker:开关_0" y="247.6080698593495" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924485513221" ObjectName="110kV滚朋羊一级线131断路器"/>
   <cge:TPSR_Ref TObjectID="6473924485513221"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,689.546,259.582) scale(1.31718,1.19743) translate(-164.457,-40.8257)" width="10" x="682.9600634841954" y="247.6080698593495"/></g>
  <g id="85">
   <use class="kv110" height="20" transform="rotate(0,1170.89,244.346) scale(1.31718,1.19743) translate(-280.364,-38.3136)" width="10" x="1164.302701606472" xlink:href="#Breaker:开关_0" y="232.372068745884" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924485447685" ObjectName="110kV滚朋羊一二级线132断路器"/>
   <cge:TPSR_Ref TObjectID="6473924485447685"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1170.89,244.346) scale(1.31718,1.19743) translate(-280.364,-38.3136)" width="10" x="1164.302701606472" y="232.372068745884"/></g>
  <g id="103">
   <use class="kv110" height="20" transform="rotate(0,1418.28,245.694) scale(1.31718,1.19743) translate(-339.937,-38.5357)" width="10" x="1411.698181377689" xlink:href="#Breaker:开关_0" y="233.7191809704606" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924485382149" ObjectName="110kV滚朋羊小一级线133断路器"/>
   <cge:TPSR_Ref TObjectID="6473924485382149"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1418.28,245.694) scale(1.31718,1.19743) translate(-339.937,-38.5357)" width="10" x="1411.698181377689" y="233.7191809704606"/></g>
  <g id="185">
   <use class="kv110" height="20" transform="rotate(0,1087.83,425.472) scale(1.22222,1.11111) translate(-196.677,-41.4361)" width="10" x="1081.722222235468" xlink:href="#Breaker:开关_0" y="414.3611111111111" zvalue="142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924485644293" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473924485644293"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1087.83,425.472) scale(1.22222,1.11111) translate(-196.677,-41.4361)" width="10" x="1081.722222235468" y="414.3611111111111"/></g>
  <g id="249">
   <use class="v6300" height="20" transform="rotate(0,1088.5,636.847) scale(2.2625,2.2625) translate(-601.083,-342.743)" width="10" x="1077.187484136886" xlink:href="#Breaker:小车断路器_0" y="614.2222222222223" zvalue="151"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924485578757" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924485578757"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1088.5,636.847) scale(2.2625,2.2625) translate(-601.083,-342.743)" width="10" x="1077.187484136886" y="614.2222222222223"/></g>
  <g id="130">
   <use class="v6300" height="20" transform="rotate(0,585.222,747.556) scale(2.33333,2.33333) translate(-327.746,-413.841)" width="10" x="573.5555555555555" xlink:href="#Breaker:小车断路器_0" y="724.2222222222223" zvalue="172"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924485709829" ObjectName="#1发电机631断路器"/>
   <cge:TPSR_Ref TObjectID="6473924485709829"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,585.222,747.556) scale(2.33333,2.33333) translate(-327.746,-413.841)" width="10" x="573.5555555555555" y="724.2222222222223"/></g>
  <g id="155">
   <use class="v6300" height="20" transform="rotate(0,1088.5,750.958) scale(2.45139,2.45139) translate(-637.209,-430.105)" width="10" x="1076.243039692442" xlink:href="#Breaker:小车断路器_0" y="726.4444444444445" zvalue="198"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924485775365" ObjectName="#1站用变633断路器"/>
   <cge:TPSR_Ref TObjectID="6473924485775365"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1088.5,750.958) scale(2.45139,2.45139) translate(-637.209,-430.105)" width="10" x="1076.243039692442" y="726.4444444444445"/></g>
  <g id="187">
   <use class="v6300" height="20" transform="rotate(0,1473.25,747.556) scale(2.33333,2.33333) translate(-835.192,-413.841)" width="10" x="1461.585858585859" xlink:href="#Breaker:小车断路器_0" y="724.2222222222223" zvalue="211"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924485840901" ObjectName="#2发电机632断路器"/>
   <cge:TPSR_Ref TObjectID="6473924485840901"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1473.25,747.556) scale(2.33333,2.33333) translate(-835.192,-413.841)" width="10" x="1461.585858585859" y="724.2222222222223"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="105">
   <use class="kv110" height="30" transform="rotate(0,689.607,304.944) scale(-1.19743,-0.878118) translate(-1264.03,-654.042)" width="15" x="680.6263985438216" xlink:href="#Disconnector:刀闸_0" y="291.7718986873881" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449651212293" ObjectName="110kV滚朋羊一级线1311隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449651212293"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,689.607,304.944) scale(-1.19743,-0.878118) translate(-1264.03,-654.042)" width="15" x="680.6263985438216" y="291.7718986873881"/></g>
  <g id="104">
   <use class="kv110" height="30" transform="rotate(0,689.607,208.553) scale(-1.19743,-0.878118) translate(-1264.03,-447.882)" width="15" x="680.6263985723708" xlink:href="#Disconnector:刀闸_0" y="195.3813878668661" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449651146757" ObjectName="110kV滚朋羊一级线1316隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449651146757"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,689.607,208.553) scale(-1.19743,-0.878118) translate(-1264.03,-447.882)" width="15" x="680.6263985723708" y="195.3813878668661"/></g>
  <g id="84">
   <use class="kv110" height="30" transform="rotate(0,1170.89,295.597) scale(-1.19743,-0.878118) translate(-2147.24,-634.05)" width="15" x="1161.907835429446" xlink:href="#Disconnector:刀闸_0" y="282.4247864628115" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449650688006" ObjectName="110kV滚朋羊一二级线1321隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449650688006"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1170.89,295.597) scale(-1.19743,-0.878118) translate(-2147.24,-634.05)" width="15" x="1161.907835429446" y="282.4247864628115"/></g>
  <g id="83">
   <use class="kv110" height="30" transform="rotate(0,1170.89,183.206) scale(-1.19743,-0.878118) translate(-2147.24,-393.669)" width="15" x="1161.907835457996" xlink:href="#Disconnector:刀闸_0" y="170.0342756422893" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449650622470" ObjectName="110kV滚朋羊一二级线1326隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449650622470"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1170.89,183.206) scale(-1.19743,-0.878118) translate(-2147.24,-393.669)" width="15" x="1161.907835457996" y="170.0342756422893"/></g>
  <g id="102">
   <use class="kv110" height="30" transform="rotate(0,1418.28,296.944) scale(-1.19743,-0.878118) translate(-2601.24,-636.931)" width="15" x="1409.303315200664" xlink:href="#Disconnector:刀闸_0" y="283.7718986873881" zvalue="79"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449650163718" ObjectName="110kV滚朋羊小一级线1331隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449650163718"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1418.28,296.944) scale(-1.19743,-0.878118) translate(-2601.24,-636.931)" width="15" x="1409.303315200664" y="283.7718986873881"/></g>
  <g id="101">
   <use class="kv110" height="30" transform="rotate(0,1418.28,184.553) scale(-1.19743,-0.878118) translate(-2601.24,-396.55)" width="15" x="1409.303315229213" xlink:href="#Disconnector:刀闸_0" y="171.3813878668661" zvalue="82"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449650098182" ObjectName="110kV滚朋羊小一级线1336隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449650098182"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1418.28,184.553) scale(-1.19743,-0.878118) translate(-2601.24,-396.55)" width="15" x="1409.303315229213" y="171.3813878668661"/></g>
  <g id="121">
   <use class="kv110" height="30" transform="rotate(0,689.575,373.775) scale(-1.19743,-0.878118) translate(-1263.97,-801.258)" width="15" x="680.594531703973" xlink:href="#Disconnector:刀闸_0" y="360.6033884777482" zvalue="130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449651605509" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449651605509"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,689.575,373.775) scale(-1.19743,-0.878118) translate(-1263.97,-801.258)" width="15" x="680.594531703973" y="360.6033884777482"/></g>
  <g id="186">
   <use class="kv110" height="30" transform="rotate(0,1087.83,376.639) scale(1.11111,0.814815) translate(-107.95,82.822)" width="15" x="1079.5" xlink:href="#Disconnector:刀闸_0" y="364.4166666666667" zvalue="140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449651867654" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449651867654"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1087.83,376.639) scale(1.11111,0.814815) translate(-107.95,82.822)" width="15" x="1079.5" y="364.4166666666667"/></g>
  <g id="87">
   <use class="kv110" height="30" transform="rotate(0,1087,468.222) scale(1.11111,0.814815) translate(-107.867,103.636)" width="15" x="1078.666666666667" xlink:href="#Disconnector:刀闸_0" y="456" zvalue="159"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449652064262" ObjectName="#1主变110kV侧1016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449652064262"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1087,468.222) scale(1.11111,0.814815) translate(-107.867,103.636)" width="15" x="1078.666666666667" y="456"/></g>
  <g id="137">
   <use class="v6300" height="27" transform="rotate(0,473.056,831.033) scale(1.46825,1.40985) translate(-147.589,-236.051)" width="14" x="462.7777777777779" xlink:href="#Disconnector:带融断手车刀闸_0" y="812" zvalue="179"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449652588549" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449652588549"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,473.056,831.033) scale(1.46825,1.40985) translate(-147.589,-236.051)" width="14" x="462.7777777777779" y="812"/></g>
  <g id="151">
   <use class="v6300" height="27" transform="rotate(0,681.056,831.033) scale(1.46825,1.40985) translate(-213.924,-236.051)" width="14" x="670.7777777777779" xlink:href="#Disconnector:带融断手车刀闸_0" y="812" zvalue="193"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449652719621" ObjectName="#1发电机6912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449652719621"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,681.056,831.033) scale(1.46825,1.40985) translate(-213.924,-236.051)" width="14" x="670.7777777777779" y="812"/></g>
  <g id="163">
   <use class="v6300" height="27" transform="rotate(0,1471.32,619.922) scale(1.46825,1.40985) translate(-465.954,-174.68)" width="14" x="1461.042307685398" xlink:href="#Disconnector:带融断手车刀闸_0" y="600.8888888888889" zvalue="207"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449653047301" ObjectName="6.3kV母线电压互感器6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449653047301"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,1471.32,619.922) scale(1.46825,1.40985) translate(-465.954,-174.68)" width="14" x="1461.042307685398" y="600.8888888888889"/></g>
  <g id="179">
   <use class="v6300" height="27" transform="rotate(0,1361.06,831.033) scale(1.46825,1.40985) translate(-430.789,-236.051)" width="14" x="1350.777777777778" xlink:href="#Disconnector:带融断手车刀闸_0" y="812" zvalue="219"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449653243909" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449653243909"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,1361.06,831.033) scale(1.46825,1.40985) translate(-430.789,-236.051)" width="14" x="1350.777777777778" y="812"/></g>
  <g id="173">
   <use class="v6300" height="27" transform="rotate(0,1569.06,831.033) scale(1.46825,1.40985) translate(-497.124,-236.051)" width="14" x="1558.777777777778" xlink:href="#Disconnector:带融断手车刀闸_0" y="812" zvalue="225"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449653112837" ObjectName="#2发电机6922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449653112837"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,1569.06,831.033) scale(1.46825,1.40985) translate(-497.124,-236.051)" width="14" x="1558.777777777778" y="812"/></g>
  <g id="194">
   <use class="kv10" height="30" transform="rotate(0,1712.22,483.556) scale(1.40741,1.40741) translate(-492.588,-133.865)" width="15" x="1701.666666666667" xlink:href="#Disconnector:令克_0" y="462.4444444444445" zvalue="231"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449653571590" ObjectName="#2站用变刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449653571590"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1712.22,483.556) scale(1.40741,1.40741) translate(-492.588,-133.865)" width="15" x="1701.666666666667" y="462.4444444444445"/></g>
  <g id="196">
   <use class="kv10" height="30" transform="rotate(0,1713.56,382.444) scale(1.11111,0.814815) translate(-170.522,84.1414)" width="15" x="1705.222222222222" xlink:href="#Disconnector:刀闸_0" y="370.2222222222222" zvalue="233"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449653637126" ObjectName="10kV施工电源0311隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449653637126"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1713.56,382.444) scale(1.11111,0.814815) translate(-170.522,84.1414)" width="15" x="1705.222222222222" y="370.2222222222222"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="100">
   <path class="kv110" d="M 689.5 317.68 L 689.5 336" stroke-width="1" zvalue="41"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="32@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 689.5 317.68 L 689.5 336" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv110" d="M 689.53 292 L 689.63 271.02" stroke-width="1" zvalue="43"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@1" LinkObjectIDznd="106@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 689.53 292 L 689.63 271.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv110" d="M 689.5 248.13 L 689.5 221.29" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@0" LinkObjectIDznd="104@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 689.5 248.13 L 689.5 221.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv110" d="M 689.53 195.61 L 689.53 141.43" stroke-width="1" zvalue="47"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@1" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 689.53 195.61 L 689.53 141.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv110" d="M 1170.78 308.33 L 1170.78 336" stroke-width="1" zvalue="62"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@0" LinkObjectIDznd="32@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1170.78 308.33 L 1170.78 336" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv110" d="M 1170.82 282.65 L 1170.82 255.78" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@1" LinkObjectIDznd="85@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1170.82 282.65 L 1170.82 255.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv110" d="M 1170.84 232.89 L 1170.78 195.94" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@0" LinkObjectIDznd="83@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1170.84 232.89 L 1170.78 195.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv110" d="M 1170.82 170.26 L 1170.8 117.02" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@1" LinkObjectIDznd="63@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1170.82 170.26 L 1170.8 117.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv110" d="M 1418.18 309.68 L 1418.18 336" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="32@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1418.18 309.68 L 1418.18 336" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv110" d="M 1418.21 284 L 1418.21 257.13" stroke-width="1" zvalue="85"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@1" LinkObjectIDznd="103@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1418.21 284 L 1418.21 257.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv110" d="M 1418.24 234.24 L 1418.18 197.29" stroke-width="1" zvalue="86"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="101@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1418.24 234.24 L 1418.18 197.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv110" d="M 1418.21 171.61 L 1418.21 118.19" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@1" LinkObjectIDznd="112@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1418.21 171.61 L 1418.21 118.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv110" d="M 742.78 234.14 L 689.5 234.14" stroke-width="1" zvalue="117"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="98" MaxPinNum="2"/>
   </metadata>
  <path d="M 742.78 234.14 L 689.5 234.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv110" d="M 742.78 170.47 L 689.53 170.47" stroke-width="1" zvalue="118"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="92" MaxPinNum="2"/>
   </metadata>
  <path d="M 742.78 170.47 L 689.53 170.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv110" d="M 1210.3 144.59 L 1170.81 144.59" stroke-width="1" zvalue="121"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75@0" LinkObjectIDznd="78" MaxPinNum="2"/>
   </metadata>
  <path d="M 1210.3 144.59 L 1170.81 144.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv110" d="M 1212.54 214.44 L 1170.82 214.44" stroke-width="1" zvalue="122"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@0" LinkObjectIDznd="80" MaxPinNum="2"/>
   </metadata>
  <path d="M 1212.54 214.44 L 1170.82 214.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv110" d="M 1214.01 272.55 L 1170.82 272.55" stroke-width="1" zvalue="123"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@0" LinkObjectIDznd="81" MaxPinNum="2"/>
   </metadata>
  <path d="M 1214.01 272.55 L 1170.82 272.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv110" d="M 1455.64 150.41 L 1418.21 150.41" stroke-width="1" zvalue="126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@0" LinkObjectIDznd="68" MaxPinNum="2"/>
   </metadata>
  <path d="M 1455.64 150.41 L 1418.21 150.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv110" d="M 1454.87 216.92 L 1418.2 216.92" stroke-width="1" zvalue="127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="69" MaxPinNum="2"/>
   </metadata>
  <path d="M 1454.87 216.92 L 1418.2 216.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="kv110" d="M 1459.46 270.66 L 1418.21 270.66" stroke-width="1" zvalue="128"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@0" LinkObjectIDznd="70" MaxPinNum="2"/>
   </metadata>
  <path d="M 1459.46 270.66 L 1418.21 270.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv110" d="M 689.5 360.83 L 689.5 336" stroke-width="1" zvalue="135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@1" LinkObjectIDznd="32@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 689.5 360.83 L 689.5 336" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv110" d="M 690.88 481.8 L 690.88 386.51" stroke-width="1" zvalue="137"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@0" LinkObjectIDznd="121@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 690.88 481.8 L 690.88 386.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv110" d="M 748.38 421.16 L 690.88 421.16" stroke-width="1" zvalue="138"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="124" MaxPinNum="2"/>
   </metadata>
  <path d="M 748.38 421.16 L 690.88 421.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv110" d="M 1087.9 388.65 L 1087.9 414.84" stroke-width="1" zvalue="144"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@1" LinkObjectIDznd="185@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1087.9 388.65 L 1087.9 414.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv110" d="M 1087.93 364.82 L 1087.93 336" stroke-width="1" zvalue="148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="32@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1087.93 364.82 L 1087.93 336" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="v6300" d="M 1087.02 580.21 L 1087.02 615.92" stroke-width="1" zvalue="153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@1" LinkObjectIDznd="249@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1087.02 580.21 L 1087.02 615.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv110" d="M 1141.11 403.14 L 1087.9 403.14" stroke-width="1" zvalue="154"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@0" LinkObjectIDznd="67" MaxPinNum="2"/>
   </metadata>
  <path d="M 1141.11 403.14 L 1087.9 403.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv110" d="M 1087.1 456.4 L 1087.91 436.08" stroke-width="1" zvalue="160"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@0" LinkObjectIDznd="185@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1087.1 456.4 L 1087.91 436.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv110" d="M 1087.07 480.24 L 1087.07 513.86" stroke-width="1" zvalue="161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@1" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1087.07 480.24 L 1087.07 513.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="kv110" d="M 1141.61 449.11 L 1087.39 449.11" stroke-width="1" zvalue="162"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="88" MaxPinNum="2"/>
   </metadata>
  <path d="M 1141.61 449.11 L 1087.39 449.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv110" d="M 1087.04 532.99 L 1171.48 532.99 L 1171.48 561.79" stroke-width="1" zvalue="168"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@2" LinkObjectIDznd="60@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1087.04 532.99 L 1171.48 532.99 L 1171.48 561.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="v6300" d="M 1088.5 657.21 L 1088.5 693.11" stroke-width="1" zvalue="170"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@1" LinkObjectIDznd="127@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1088.5 657.21 L 1088.5 693.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="v6300" d="M 585.22 725.97 L 585.22 693.11" stroke-width="1" zvalue="173"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@0" LinkObjectIDznd="127@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 585.22 725.97 L 585.22 693.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="v6300" d="M 585.25 864.1 L 585.22 768.56" stroke-width="1" zvalue="175"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="130@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 585.25 864.1 L 585.22 768.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="v6300" d="M 473.06 870.38 L 473.06 849.36" stroke-width="1" zvalue="180"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@0" LinkObjectIDznd="137@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 473.06 870.38 L 473.06 849.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="v6300" d="M 473.06 813.06 L 473.06 792 L 585.23 792" stroke-width="1" zvalue="181"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="134" MaxPinNum="2"/>
   </metadata>
  <path d="M 473.06 813.06 L 473.06 792 L 585.23 792" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="v6300" d="M 518.26 870.75 L 518.26 792" stroke-width="1" zvalue="182"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="136@0" LinkObjectIDznd="141" MaxPinNum="2"/>
   </metadata>
  <path d="M 518.26 870.75 L 518.26 792" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="v6300" d="M 681.58 872.63 L 681.58 849.36" stroke-width="1" zvalue="194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="143@0" LinkObjectIDznd="151@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 681.58 872.63 L 681.58 849.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="v6300" d="M 681.06 813.06 L 681.06 795.31 L 585.23 795.27" stroke-width="1" zvalue="195"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@0" LinkObjectIDznd="134" MaxPinNum="2"/>
   </metadata>
  <path d="M 681.06 813.06 L 681.06 795.31 L 585.23 795.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="v6300" d="M 1088.5 823.13 L 1088.5 773.02" stroke-width="1" zvalue="199"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@0" LinkObjectIDznd="155@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1088.5 823.13 L 1088.5 773.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="v6300" d="M 1088.5 728.28 L 1088.5 693.11" stroke-width="1" zvalue="200"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="127@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1088.5 728.28 L 1088.5 693.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="v6300" d="M 1144.78 795.34 L 1088.5 795.34" stroke-width="1" zvalue="203"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="156" MaxPinNum="2"/>
   </metadata>
  <path d="M 1144.78 795.34 L 1088.5 795.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="v6300" d="M 1471.32 574.75 L 1471.32 617.95" stroke-width="1" zvalue="208"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162@0" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 1471.32 574.75 L 1471.32 617.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="165">
   <path class="v6300" d="M 1471.32 638.25 L 1471.32 693.11" stroke-width="1" zvalue="209"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@1" LinkObjectIDznd="127@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1471.32 638.25 L 1471.32 693.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="184">
   <path class="v6300" d="M 1473.25 725.97 L 1473.25 693.11" stroke-width="1" zvalue="213"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@0" LinkObjectIDznd="165" MaxPinNum="2"/>
   </metadata>
  <path d="M 1473.25 725.97 L 1473.25 693.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="v6300" d="M 1473.25 864.1 L 1473.25 768.56" stroke-width="1" zvalue="216"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@0" LinkObjectIDznd="187@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1473.25 864.1 L 1473.25 768.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="v6300" d="M 1361.06 870.38 L 1361.06 849.36" stroke-width="1" zvalue="220"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@0" LinkObjectIDznd="179@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1361.06 870.38 L 1361.06 849.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="v6300" d="M 1361.06 813.06 L 1361.06 792 L 1473.25 792" stroke-width="1" zvalue="222"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@0" LinkObjectIDznd="182" MaxPinNum="2"/>
   </metadata>
  <path d="M 1361.06 813.06 L 1361.06 792 L 1473.25 792" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="v6300" d="M 1406.26 870.75 L 1406.26 792" stroke-width="1" zvalue="223"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@0" LinkObjectIDznd="176" MaxPinNum="2"/>
   </metadata>
  <path d="M 1406.26 870.75 L 1406.26 792" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="v6300" d="M 1569.58 872.63 L 1569.58 849.36" stroke-width="1" zvalue="226"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="173@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1569.58 872.63 L 1569.58 849.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="v6300" d="M 1569.06 813.06 L 1569.06 795.27 L 1473.25 795.27" stroke-width="1" zvalue="227"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="173@0" LinkObjectIDznd="182" MaxPinNum="2"/>
   </metadata>
  <path d="M 1569.06 813.06 L 1569.06 795.27 L 1473.25 795.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="kv110" d="M 1143.5 494.83 L 1087.07 494.83" stroke-width="1" zvalue="228"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 1143.5 494.83 L 1087.07 494.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv10" d="M 1712.1 556.91 L 1712.1 500.8" stroke-width="1" zvalue="232"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="194@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1712.1 556.91 L 1712.1 500.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="kv10" d="M 1712.34 464.91 L 1712.34 394.46" stroke-width="1" zvalue="235"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@0" LinkObjectIDznd="196@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1712.34 464.91 L 1712.34 394.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv10" d="M 1713.65 370.63 L 1713.65 299.58" stroke-width="1" zvalue="237"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="203@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1713.65 370.63 L 1713.65 299.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="kv10" d="M 1759.64 345.5 L 1713.65 345.5" stroke-width="1" zvalue="238"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="200@0" LinkObjectIDznd="201" MaxPinNum="2"/>
   </metadata>
  <path d="M 1759.64 345.5 L 1713.65 345.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv110" d="M 689.53 170 L 631 170 L 631 162.08" stroke-width="1" zvalue="242"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92" LinkObjectIDznd="119@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 689.53 170 L 631 170 L 631 162.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv110" d="M 1170.81 143.65 L 1107 143.65 L 1107 138.08" stroke-width="1" zvalue="247"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78" LinkObjectIDznd="145@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1170.81 143.65 L 1107 143.65 L 1107 138.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="kv110" d="M 1418.21 151 L 1364 151 L 1364 141.08" stroke-width="1" zvalue="248"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68" LinkObjectIDznd="146@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1418.21 151 L 1364 151 L 1364 141.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv110" d="M 742.78 283.66 L 689.57 283.66" stroke-width="1" zvalue="249"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="99" MaxPinNum="2"/>
   </metadata>
  <path d="M 742.78 283.66 L 689.57 283.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="205">
   <path class="v6300" d="M 1471.32 601.95 L 1471.32 601.95" stroke-width="1" zvalue="253"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@0" LinkObjectIDznd="164" MaxPinNum="2"/>
   </metadata>
  <path d="M 1471.32 601.95 L 1471.32 601.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="91">
   <use class="kv110" height="20" transform="rotate(270,754.459,283.6) scale(-1.19743,1.19743) translate(-1383.54,-44.7857)" width="10" x="748.4717694209038" xlink:href="#GroundDisconnector:地刀_0" y="271.6257892115217" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449651081221" ObjectName="110kV滚朋羊一级线13117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449651081221"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,754.459,283.6) scale(-1.19743,1.19743) translate(-1383.54,-44.7857)" width="10" x="748.4717694209038" y="271.6257892115217"/></g>
  <g id="90">
   <use class="kv110" height="20" transform="rotate(270,754.459,234.081) scale(-1.19743,1.19743) translate(-1383.54,-36.6211)" width="10" x="748.4717694209037" xlink:href="#GroundDisconnector:地刀_0" y="222.1069474337295" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449650950150" ObjectName="110kV滚朋羊一级线13160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449650950150"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,754.459,234.081) scale(-1.19743,1.19743) translate(-1383.54,-36.6211)" width="10" x="748.4717694209037" y="222.1069474337295"/></g>
  <g id="89">
   <use class="kv110" height="20" transform="rotate(270,754.459,170.407) scale(-1.19743,1.19743) translate(-1383.54,-26.1224)" width="10" x="748.4717693243986" xlink:href="#GroundDisconnector:地刀_0" y="158.4327260966151" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449650819078" ObjectName="110kV滚朋羊一级线13167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449650819078"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,754.459,170.407) scale(-1.19743,1.19743) translate(-1383.54,-26.1224)" width="10" x="748.4717693243986" y="158.4327260966151"/></g>
  <g id="77">
   <use class="kv110" height="20" transform="rotate(270,1225.68,272.488) scale(-1.19743,1.19743) translate(-2248.29,-42.9536)" width="10" x="1219.694383730791" xlink:href="#GroundDisconnector:地刀_0" y="260.5139711045918" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449650556934" ObjectName="110kV滚朋羊一二级线13217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449650556934"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1225.68,272.488) scale(-1.19743,1.19743) translate(-2248.29,-42.9536)" width="10" x="1219.694383730791" y="260.5139711045918"/></g>
  <g id="76">
   <use class="kv110" height="20" transform="rotate(270,1224.21,214.381) scale(-1.19743,1.19743) translate(-2245.59,-33.3729)" width="10" x="1218.223795495497" xlink:href="#GroundDisconnector:地刀_0" y="202.4068940326831" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449650425862" ObjectName="110kV滚朋羊一二级线13260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449650425862"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1224.21,214.381) scale(-1.19743,1.19743) translate(-2245.59,-33.3729)" width="10" x="1218.223795495497" y="202.4068940326831"/></g>
  <g id="75">
   <use class="kv110" height="20" transform="rotate(270,1221.98,144.531) scale(-1.19743,1.19743) translate(-2241.48,-21.8559)" width="10" x="1215.988501263653" xlink:href="#GroundDisconnector:地刀_0" y="132.5562021073325" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449650294790" ObjectName="110kV滚朋羊一二级线13267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449650294790"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1221.98,144.531) scale(-1.19743,1.19743) translate(-2241.48,-21.8559)" width="10" x="1215.988501263653" y="132.5562021073325"/></g>
  <g id="95">
   <use class="kv110" height="20" transform="rotate(270,1471.14,270.6) scale(-1.19743,1.19743) translate(-2698.72,-42.6423)" width="10" x="1465.14868703142" xlink:href="#GroundDisconnector:地刀_0" y="258.6257892115217" zvalue="90"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449650032646" ObjectName="110kV滚朋羊小一级线13317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449650032646"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1471.14,270.6) scale(-1.19743,1.19743) translate(-2698.72,-42.6423)" width="10" x="1465.14868703142" y="258.6257892115217"/></g>
  <g id="94">
   <use class="kv110" height="20" transform="rotate(270,1466.55,216.86) scale(-1.19743,1.19743) translate(-2690.3,-33.7816)" width="10" x="1460.560451737302" xlink:href="#GroundDisconnector:地刀_0" y="204.8854342879314" zvalue="92"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449649901574" ObjectName="110kV滚朋羊小一级线13360接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449649901574"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1466.55,216.86) scale(-1.19743,1.19743) translate(-2690.3,-33.7816)" width="10" x="1460.560451737302" y="204.8854342879314"/></g>
  <g id="93">
   <use class="kv110" height="20" transform="rotate(270,1467.31,150.348) scale(-1.19743,1.19743) translate(-2691.71,-22.8151)" width="10" x="1461.325157505459" xlink:href="#GroundDisconnector:地刀_0" y="138.3739025672035" zvalue="94"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449649770502" ObjectName="110kV滚朋羊小一级线13367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449649770502"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1467.31,150.348) scale(-1.19743,1.19743) translate(-2691.71,-22.8151)" width="10" x="1461.325157505459" y="138.3739025672035"/></g>
  <g id="120">
   <use class="kv110" height="20" transform="rotate(270,760.055,421.098) scale(-1.19743,1.19743) translate(-1393.8,-67.4565)" width="10" x="754.0675930993045" xlink:href="#GroundDisconnector:地刀_0" y="409.1239456685484" zvalue="132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449651539973" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449651539973"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,760.055,421.098) scale(-1.19743,1.19743) translate(-1393.8,-67.4565)" width="10" x="754.0675930993045" y="409.1239456685484"/></g>
  <g id="177">
   <use class="kv110" height="20" transform="rotate(270,1151.94,403.083) scale(-1.11111,1.11111) translate(-2188.14,-39.1972)" width="10" x="1146.388888888889" xlink:href="#GroundDisconnector:地刀_0" y="391.9722222222222" zvalue="145"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449651802118" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449651802118"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1151.94,403.083) scale(-1.11111,1.11111) translate(-2188.14,-39.1972)" width="10" x="1146.388888888889" y="391.9722222222222"/></g>
  <g id="74">
   <use class="kv110" height="20" transform="rotate(270,1152.44,449.056) scale(-1.11111,1.11111) translate(-2189.09,-43.7944)" width="10" x="1146.888888888889" xlink:href="#GroundDisconnector:地刀_0" y="437.9444444444444" zvalue="156"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449651998725" ObjectName="#1主变110kV侧10160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449651998725"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1152.44,449.056) scale(-1.11111,1.11111) translate(-2189.09,-43.7944)" width="10" x="1146.888888888889" y="437.9444444444444"/></g>
  <g id="108">
   <use class="kv110" height="20" transform="rotate(270,1154.33,494.778) scale(-1.11111,1.11111) translate(-2192.68,-48.3667)" width="10" x="1148.777777777778" xlink:href="#GroundDisconnector:地刀_0" y="483.6666666666666" zvalue="164"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449652195333" ObjectName="#1主变110kV侧10167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449652195333"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1154.33,494.778) scale(-1.11111,1.11111) translate(-2192.68,-48.3667)" width="10" x="1148.777777777778" y="483.6666666666666"/></g>
  <g id="60">
   <use class="kv110" height="45" transform="rotate(0,1176.82,572.455) scale(0.666667,-0.666667) translate(580.909,-1438.64)" width="45" x="1161.818181818182" xlink:href="#GroundDisconnector:12547_0" y="557.4545454545455" zvalue="167"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449652326405" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449652326405"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,1176.82,572.455) scale(0.666667,-0.666667) translate(580.909,-1438.64)" width="45" x="1161.818181818182" y="557.4545454545455"/></g>
  <g id="158">
   <use class="v6300" height="20" transform="rotate(270,1162.17,795.25) scale(-1.78333,1.78333) translate(-1809.93,-341.482)" width="10" x="1153.25" xlink:href="#GroundDisconnector:地刀_0" y="777.4166666666666" zvalue="202"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449652916229" ObjectName="#1站用变63367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449652916229"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1162.17,795.25) scale(-1.78333,1.78333) translate(-1809.93,-341.482)" width="10" x="1153.25" y="777.4166666666666"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="32">
   <path class="kv110" d="M 535.56 336 L 1567.78 336" stroke-width="6" zvalue="113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674236760069" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674236760069"/></metadata>
  <path d="M 535.56 336 L 1567.78 336" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="v6300" d="M 471.51 693.11 L 1678.89 693.11" stroke-width="6" zvalue="169"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674236825605" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674236825605"/></metadata>
  <path d="M 471.51 693.11 L 1678.89 693.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="123">
   <use class="kv110" height="20" transform="rotate(0,690.876,499.717) scale(1.79192,1.79192) translate(-295.426,-212.925)" width="25" x="668.4766511197415" xlink:href="#Accessory:4绕组母线PT_0" y="481.7979797979798" zvalue="136"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449651671045" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,690.876,499.717) scale(1.79192,1.79192) translate(-295.426,-212.925)" width="25" x="668.4766511197415" y="481.7979797979798"/></g>
  <g id="136">
   <use class="v6300" height="30" transform="rotate(0,518.264,888.319) scale(1.59722,-1.59722) translate(-184.827,-1435.53)" width="30" x="494.3055555555558" xlink:href="#Accessory:RT1122_0" y="864.3611111111113" zvalue="176"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449652457477" ObjectName="#1发电机附属PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,518.264,888.319) scale(1.59722,-1.59722) translate(-184.827,-1435.53)" width="30" x="494.3055555555558" y="864.3611111111113"/></g>
  <g id="138">
   <use class="v6300" height="26" transform="rotate(0,473.131,890.556) scale(1.51282,1.5641) translate(-156.537,-313.851)" width="15" x="461.7850427350429" xlink:href="#Accessory:20210316PT_0" y="870.2222222222224" zvalue="178"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449652523013" ObjectName="#1发电机附属PT1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,473.131,890.556) scale(1.51282,1.5641) translate(-156.537,-313.851)" width="15" x="461.7850427350429" y="870.2222222222224"/></g>
  <g id="143">
   <use class="v6300" height="35" transform="rotate(0,681.667,892.278) scale(1.44444,-1.44444) translate(-203.077,-1502.23)" width="30" x="660" xlink:href="#Accessory:4绕组PT带接地_0" y="867" zvalue="183"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449652654085" ObjectName="#1发电机PT接地"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,681.667,892.278) scale(1.44444,-1.44444) translate(-203.077,-1502.23)" width="30" x="660" y="867"/></g>
  <g id="162">
   <use class="v6300" height="20" transform="rotate(0,1471.32,556.828) scale(1.79192,-1.79192) translate(-640.335,-859.653)" width="25" x="1448.921095564186" xlink:href="#Accessory:4绕组母线PT_0" y="538.9090909090909" zvalue="205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449652981765" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1471.32,556.828) scale(1.79192,-1.79192) translate(-640.335,-859.653)" width="25" x="1448.921095564186" y="538.9090909090909"/></g>
  <g id="181">
   <use class="v6300" height="30" transform="rotate(0,1406.26,888.319) scale(1.59722,-1.59722) translate(-516.862,-1435.53)" width="30" x="1382.305555555556" xlink:href="#Accessory:RT1122_0" y="864.3611111111113" zvalue="217"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449653374981" ObjectName="#2发电机附属PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1406.26,888.319) scale(1.59722,-1.59722) translate(-516.862,-1435.53)" width="30" x="1382.305555555556" y="864.3611111111113"/></g>
  <g id="180">
   <use class="v6300" height="26" transform="rotate(0,1361.13,890.556) scale(1.51282,1.5641) translate(-457.554,-313.851)" width="15" x="1349.785042735043" xlink:href="#Accessory:20210316PT_0" y="870.2222222222224" zvalue="218"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449653309445" ObjectName="#2发电机附属PT1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1361.13,890.556) scale(1.51282,1.5641) translate(-457.554,-313.851)" width="15" x="1349.785042735043" y="870.2222222222224"/></g>
  <g id="174">
   <use class="v6300" height="35" transform="rotate(0,1569.67,892.278) scale(1.44444,-1.44444) translate(-476.308,-1502.23)" width="30" x="1548" xlink:href="#Accessory:4绕组PT带接地_0" y="867" zvalue="224"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449653178373" ObjectName="#2发电机PT接地"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1569.67,892.278) scale(1.44444,-1.44444) translate(-476.308,-1502.23)" width="30" x="1548" y="867"/></g>
  <g id="200">
   <use class="kv10" height="20" transform="rotate(270,1773.06,345.5) scale(1.91667,1.91667) translate(-838.816,-156.072)" width="20" x="1753.888888888889" xlink:href="#Accessory:线路PT3_0" y="326.3333333333334" zvalue="236"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449653702662" ObjectName="10kV施工电源PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1773.06,345.5) scale(1.91667,1.91667) translate(-838.816,-156.072)" width="20" x="1753.888888888889" y="326.3333333333334"/></g>
  <g id="119">
   <use class="kv110" height="40" transform="rotate(0,631,143) scale(1,-1) translate(0,-286)" width="40" x="611" xlink:href="#Accessory:线路PT11带避雷器_0" y="123" zvalue="241"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449653833734" ObjectName="110kV滚朋羊一级线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,631,143) scale(1,-1) translate(0,-286)" width="40" x="611" y="123"/></g>
  <g id="145">
   <use class="kv110" height="40" transform="rotate(0,1107,119) scale(1,-1) translate(0,-238)" width="40" x="1087" xlink:href="#Accessory:线路PT11带避雷器_0" y="99" zvalue="244"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449653899270" ObjectName="110kV滚朋羊一二级线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1107,119) scale(1,-1) translate(0,-238)" width="40" x="1087" y="99"/></g>
  <g id="146">
   <use class="kv110" height="40" transform="rotate(0,1364,122) scale(1,-1) translate(0,-244)" width="40" x="1344" xlink:href="#Accessory:线路PT11带避雷器_0" y="102" zvalue="246"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449653964806" ObjectName="110kV滚朋羊小一级线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1364,122) scale(1,-1) translate(0,-244)" width="40" x="1344" y="102"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="166">
   <g id="1660">
    <use class="kv110" height="50" transform="rotate(0,1087.02,546.97) scale(1.5,1.34878) translate(-354.84,-132.721)" width="30" x="1064.52" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="513.25" zvalue="149"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874425761796" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1661">
    <use class="v6300" height="50" transform="rotate(0,1087.02,546.97) scale(1.5,1.34878) translate(-354.84,-132.721)" width="30" x="1064.52" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="513.25" zvalue="149"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874425827332" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399445118980" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399445118980"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1087.02,546.97) scale(1.5,1.34878) translate(-354.84,-132.721)" width="30" x="1064.52" y="513.25"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="132">
   <use class="v6300" height="30" transform="rotate(0,585.253,888.687) scale(1.66667,1.66667) translate(-224.101,-345.475)" width="30" x="560.2525252525252" xlink:href="#Generator:发电机_0" y="863.6868686868685" zvalue="174"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449652391941" ObjectName="#1发电机10MW"/>
   <cge:TPSR_Ref TObjectID="6192449652391941"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,585.253,888.687) scale(1.66667,1.66667) translate(-224.101,-345.475)" width="30" x="560.2525252525252" y="863.6868686868685"/></g>
  <g id="183">
   <use class="v6300" height="30" transform="rotate(0,1473.25,888.687) scale(1.66667,1.66667) translate(-579.301,-345.475)" width="30" x="1448.252525252525" xlink:href="#Generator:发电机_0" y="863.6868686868685" zvalue="214"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449653440517" ObjectName="#2发电机10MW"/>
   <cge:TPSR_Ref TObjectID="6192449653440517"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1473.25,888.687) scale(1.66667,1.66667) translate(-579.301,-345.475)" width="30" x="1448.252525252525" y="863.6868686868685"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="154">
   <use class="v6300" height="30" transform="rotate(0,1088.31,853.056) scale(2.0633,2.07778) translate(-545.963,-426.327)" width="28" x="1059.424250374034" xlink:href="#EnergyConsumer:站用变DY接地_0" y="821.8888888888889" zvalue="196"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449652785157" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1088.31,853.056) scale(2.0633,2.07778) translate(-545.963,-426.327)" width="28" x="1059.424250374034" y="821.8888888888889"/></g>
  <g id="191">
   <use class="kv10" height="30" transform="rotate(0,1711.92,586.833) scale(2.0633,2.07778) translate(-867.331,-288.234)" width="28" x="1683.029198818164" xlink:href="#EnergyConsumer:站用变DY接地_0" y="555.6666666666666" zvalue="230"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449653506054" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1711.92,586.833) scale(2.0633,2.07778) translate(-867.331,-288.234)" width="28" x="1683.029198818164" y="555.6666666666666"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="203">
   <use class="kv10" height="30" transform="rotate(0,1713.65,291.333) scale(2.06349,0.555556) translate(-879.468,226.4)" width="7" x="1706.430863389845" xlink:href="#ACLineSegment:线路_0" y="283.0000000000001" zvalue="239"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449653768197" ObjectName="10kV施工电源"/>
   <cge:TPSR_Ref TObjectID="6192449653768197_5066549582954498"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1713.65,291.333) scale(2.06349,0.555556) translate(-879.468,226.4)" width="7" x="1706.430863389845" y="283.0000000000001"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="59">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="59" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,690.645,31.6088) scale(1,1) translate(0,3.8901e-14)" writing-mode="lr" x="690.1799999999999" xml:space="preserve" y="36.28" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124512071684" ObjectName="P"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="150" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,690.645,54.8611) scale(1,1) translate(0,8.27869e-14)" writing-mode="lr" x="690.1799999999999" xml:space="preserve" y="59.54" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124512137220" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="198">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="198" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,690.645,78.1134) scale(1,1) translate(0,1.26673e-13)" writing-mode="lr" x="690.1799999999999" xml:space="preserve" y="82.79000000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124512202756" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="206">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="206" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1170.8,24.9438) scale(1,1) translate(0,-2.78699e-14)" writing-mode="lr" x="1170.33" xml:space="preserve" y="29.62" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124513120260" ObjectName="P"/>
   </metadata>
  </g>
  <g id="207">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="207" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1170.8,48.1056) scale(1,1) translate(0,-7.41564e-14)" writing-mode="lr" x="1170.33" xml:space="preserve" y="52.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124513185796" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="208">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="208" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1170.8,71.2673) scale(1,1) translate(0,-1.20443e-13)" writing-mode="lr" x="1170.33" xml:space="preserve" y="75.94" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124513251332" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="209">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="209" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1424.33,20.5229) scale(1,1) translate(0,-1.05751e-14)" writing-mode="lr" x="1423.86" xml:space="preserve" y="25.2" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124514168836" ObjectName="P"/>
   </metadata>
  </g>
  <g id="210">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="210" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1424.33,46.9101) scale(1,1) translate(0,3.98708e-14)" writing-mode="lr" x="1423.86" xml:space="preserve" y="51.58" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124514234372" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="211">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="211" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1424.33,73.2973) scale(1,1) translate(0,6.91665e-14)" writing-mode="lr" x="1423.86" xml:space="preserve" y="77.97" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124514299908" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="212">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="212" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1000.35,394.955) scale(1,1) translate(0,-1.69852e-13)" writing-mode="lr" x="999.8" xml:space="preserve" y="399.65" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124515741700" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="213">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="213" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1000.35,421.347) scale(1,1) translate(0,-1.81572e-13)" writing-mode="lr" x="999.8" xml:space="preserve" y="426.05" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124515807236" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="214">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="214" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1001.46,599.581) scale(1,1) translate(0,-1.3037e-13)" writing-mode="lr" x="1000.91" xml:space="preserve" y="604.28" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124515872772" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="215">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="215" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1001.46,625.9) scale(1,1) translate(0,2.04321e-13)" writing-mode="lr" x="1000.91" xml:space="preserve" y="630.6" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124515938308" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="216">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="216" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1000.35,447.739) scale(1,1) translate(0,-1.93293e-13)" writing-mode="lr" x="999.8" xml:space="preserve" y="452.44" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124516003844" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="217">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="217" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1001.46,652.219) scale(1,1) translate(0,-1.42058e-13)" writing-mode="lr" x="1000.91" xml:space="preserve" y="656.92" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124516331524" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="220">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="220" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,585.253,957.514) scale(1,1) translate(0,1.57319e-12)" writing-mode="lr" x="584.7" xml:space="preserve" y="962.22" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124519346180" ObjectName="P"/>
   </metadata>
  </g>
  <g id="221">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="221" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1473.25,957.514) scale(1,1) translate(0,1.57319e-12)" writing-mode="lr" x="1472.7" xml:space="preserve" y="962.22" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124521115652" ObjectName="P"/>
   </metadata>
  </g>
  <g id="222">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="222" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,585.253,984.677) scale(1,1) translate(0,1.61842e-12)" writing-mode="lr" x="584.7" xml:space="preserve" y="989.38" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124519411716" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="223">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="223" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1473.25,984.677) scale(1,1) translate(0,1.61842e-12)" writing-mode="lr" x="1472.7" xml:space="preserve" y="989.38" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124521181191" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="224">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="224" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,585.253,1011.84) scale(1,1) translate(0,1.66366e-12)" writing-mode="lr" x="584.7" xml:space="preserve" y="1016.54" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124519477252" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="225">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="225" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1473.25,1011.84) scale(1,1) translate(0,1.66366e-12)" writing-mode="lr" x="1472.7" xml:space="preserve" y="1016.54" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124521246726" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="243">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="243" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,330.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="335.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124511940612" ObjectName="F"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,258.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="263.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124524064772" ObjectName="F"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,259.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="264.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124524130308" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,283.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="288.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124523933700" ObjectName="F"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,284.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="289.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124523999236" ObjectName="F"/>
   </metadata>
  </g>
  <g id="232">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="232" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.611,375.389) scale(1,1) translate(0,0)" writing-mode="lr" x="337.77" xml:space="preserve" y="380.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124524392452" ObjectName="F"/>
   </metadata>
  </g>
  <g id="238">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="238" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,156.361,374.639) scale(1,1) translate(0,0)" writing-mode="lr" x="156.52" xml:space="preserve" y="379.55" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124524326916" ObjectName="F"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,307.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="312.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127185350660" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,306.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="311.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127185285124" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="1" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,158.611,353.167) scale(1,1) translate(0,0)" writing-mode="lr" x="158.77" xml:space="preserve" y="358.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124518690823" ObjectName="F"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="2" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,693.556,541.5) scale(1,1) translate(0,0)" writing-mode="lr" x="693.09" xml:space="preserve" y="546.28" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124511547396" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1471.51,462.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1471.05" xml:space="preserve" y="467.39" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124518297604" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="4" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,693.556,570.5) scale(1,1) translate(0,0)" writing-mode="lr" x="693.09" xml:space="preserve" y="575.28" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124511612932" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="5" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1471.51,491.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1471.05" xml:space="preserve" y="496.39" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124518363140" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="6" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,693.556,599.5) scale(1,1) translate(0,0)" writing-mode="lr" x="693.09" xml:space="preserve" y="604.28" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124511678468" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="7" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1471.51,520.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1471.05" xml:space="preserve" y="525.39" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124518428676" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="8" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,558.556,361) scale(1,1) translate(0,0)" writing-mode="lr" x="558.09" xml:space="preserve" y="365.78" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124511809540" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="9" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,503.514,720.111) scale(1,1) translate(0,0)" writing-mode="lr" x="503.05" xml:space="preserve" y="724.89" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124518559748" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="241">
   <use height="30" transform="rotate(0,334.673,184.357) scale(0.708333,0.665547) translate(133.431,87.6271)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="174.37" zvalue="300"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374886502403" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,184.357) scale(0.708333,0.665547) translate(133.431,87.6271)" width="30" x="324.05" y="174.37"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,239.048,184.357) scale(0.708333,0.665547) translate(94.0564,87.6271)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="174.37" zvalue="301"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950211829764" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,184.357) scale(0.708333,0.665547) translate(94.0564,87.6271)" width="30" x="228.42" y="174.37"/></g>
 </g>
</svg>