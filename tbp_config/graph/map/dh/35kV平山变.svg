<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549678571521" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_0" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="3" y1="19" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="4.75" y2="8.75"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="18.75" y2="21.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_1" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="2" y2="24"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_2" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.08333333333333" x2="3.995614035087721" y1="9.083333333333332" y2="17.19627192982456"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.717927631578948" x2="10.16666666666667" y1="9.172423245614038" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="2" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="21.25" y2="24.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
  </symbol>
  <symbol id="Accessory:避雷器PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1746.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2326.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:放电间隙3_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="4.949999999999999" xlink:href="#terminal" y="15.05"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.44623913243002" x2="14.55067444781689" y1="15.11615063829308" y2="15.11615063829308"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.634007781150222" x2="14.5261684245892" y1="15.11615063829308" y2="15.11615063829308"/>
   <path d="M 12.7762 15.1162 L 8.71734 18.634 L 8.71734 11.5538 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 17.7706 15.1007 L 21.8295 18.6186 L 21.8295 11.7019 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.08333333333333" x2="9.833333333333332" y1="16.5" y2="18.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14" y1="9.75" y2="16.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="11" y1="11.58333333333333" y2="9.666666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="9.75" y1="11.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8" x2="15.08333333333333" y1="8.416666666666668" y2="25.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_1" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.96,13) scale(1,1) translate(0,0)" width="4.42" x="12.75" y="9"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="5.416666666666668" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_2" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="20" y1="6" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="10.08333333333333" y1="6.000000000000004" y2="25.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_0" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.05" x2="7.05" y1="1.166666666666664" y2="26.33333333333334"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="10.66666666666667" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="26.5417774506975" y2="23.30486537657277"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="26.5417774506975" y2="23.30486537657277"/>
   <rect fill-opacity="0" height="6.25" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,8.63) scale(1,1) translate(0,0)" width="2.83" x="5.58" y="5.5"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_1" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.05" x2="7.05" y1="2.833333333333334" y2="14.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="2.617622152595477" y2="5.500000000000003"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="10.66666666666666" y1="2.617622152595477" y2="5.500000000000003"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="10.66666666666667" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="24.65357874079139" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="26.5417774506975" y2="23.30486537657277"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="24.65357874079139" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="26.5417774506975" y2="23.30486537657277"/>
   <rect fill-opacity="0" height="6.25" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,8.63) scale(1,1) translate(0,0)" width="2.83" x="5.58" y="5.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="7.049999999999999" y1="16" y2="18.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.133333333333333" x2="7.133333333333333" y1="18.16666666666667" y2="24.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_2" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.33333333333333" x2="3.333333333333333" y1="9.166666666666668" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.666666666666667" x2="10.83333333333333" y1="9.124593892873616" y2="18.41666666666667"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="12.5" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="26.56537356321841" y2="21.7550287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="26.56537356321841" y2="21.7550287356322"/>
  </symbol>
  <symbol id="Compensator:10kV电容器_0" viewBox="0,0,24,40">
   <use terminal-index="0" type="0" x="12" xlink:href="#terminal" y="4.699999999999999"/>
   <path d="M 12 20.1 L 17.85 20.1 L 17.85 24.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="4.749074074074073" y1="35.35833333333333" y2="35.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.72129629629629" x2="4.72129629629629" y1="33.13611111111111" y2="35.37685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="24.85833333333333" y2="30.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.258333333333324" x2="0.258333333333324" y1="22.10833333333333" y2="33.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.75833333333333" x2="4.75833333333333" y1="22.35833333333333" y2="20.15462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="31.85833333333333" y2="35.37685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="20.04351851851851" y2="24.775"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.024239280774554" x2="4.024239280774554" y1="39.35833333333333" y2="37.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.786111111111114" x2="12.00833333333334" y1="20.10833333333333" y2="20.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="35.35833333333333" y2="39.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.84166666666666" x2="4.024239280774546" y1="39.35833333333333" y2="39.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.84166666666667" x2="19.84166666666667" y1="37.35833333333333" y2="39.35833333333333"/>
   <path d="M 4.75833 25.9572 A 2.96392 1.81747 180 0 1 4.75833 22.3223" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 4.75833 29.5921 A 2.96392 1.81747 180 0 1 4.75833 25.9572" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 4.75833 33.1271 A 2.96392 1.81747 180 0 1 4.75833 29.4921" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="30.85833333333333" y2="30.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="31.85833333333333" y2="31.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="12" y1="4.5" y2="7.166666666666668"/>
   <path d="M 7.26667 12.1 A 4.91667 4.75 -450 1 0 12.0167 7.18333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 7.33333 12.0833 L 12 12.1667 L 12 20.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510778" x2="17.14262023217246" y1="27.75922956383932" y2="26.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="29.36667381975841" y2="30.33114037330986"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.50700202690252" x2="18.32283029297954" y1="31.0857461490052" y2="31.0857461490052"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.28450340888154" x2="18.47116270499357" y1="30.7156109584975" y2="30.7156109584975"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.98783858485353" x2="18.76782752902158" y1="30.34547576798984" y2="30.34547576798984"/>
   <rect fill-opacity="0" height="4.29" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,17.83,27.22) scale(1,1) translate(0,0)" width="2.33" x="16.67" y="25.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="24.56666666666668" y2="25.08015580397416"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510781" x2="18.53565505804314" y1="27.75922956383932" y2="26.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="25.08015580397418" y2="27.73243882624067"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.998992968246015" x2="4.998992968246015" y1="14.41666666666666" y2="17.66666666666666"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="1.08333333333333" y2="5.749999999999999"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="6" y2="14"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="14" y2="6"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT7_0" viewBox="0,0,14,18">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1.416666666666664"/>
   <ellipse cx="7.15" cy="6.35" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.15" cy="12.03" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2019.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:线路PT235_0" viewBox="0,0,17,36">
   <use terminal-index="0" type="0" x="8.5" xlink:href="#terminal" y="0.4166666666666643"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,8.5,9.75) scale(1,1) translate(0,0)" width="4" x="6.5" y="6.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="11" y1="21.58333333333333" y2="21.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="8.5" y1="17.25" y2="0.25"/>
   <ellipse cx="8.4" cy="22.18" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.4" cy="30.28" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="11" y1="30.33333333333334" y2="30.33333333333334"/>
  </symbol>
  <symbol id="State:间隔模板_0" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(85,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="State:间隔模板_1" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(185,185,185)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV平山变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="59.4" id="1" preserveAspectRatio="xMidYMid slice" width="189.48" x="79.31" xlink:href="logo.png" y="47.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="457" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,174.046,77.1272) scale(1,1) translate(8.80481e-15,-4.21238e-14)" writing-mode="lr" x="174.05" xml:space="preserve" y="81.63" zvalue="1699"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,185.5,75.3894) scale(1,1) translate(0,5.92644e-15)" writing-mode="lr" x="185.5" xml:space="preserve" y="82.89" zvalue="1700">    35kV平山变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="163" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,208.531,382.25) scale(1,1) translate(0,0)" width="72.88" x="172.09" y="370.25" zvalue="1799"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,208.531,382.25) scale(1,1) translate(0,0)" writing-mode="lr" x="208.53" xml:space="preserve" y="386.75" zvalue="1799">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="162" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,106.625,382.25) scale(1,1) translate(0,0)" width="72.88" x="70.19" y="370.25" zvalue="1800"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,106.625,382.25) scale(1,1) translate(0,0)" writing-mode="lr" x="106.63" xml:space="preserve" y="386.75" zvalue="1800">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="161" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,107.286,341.75) scale(1,1) translate(0,0)" width="72.88" x="70.84999999999999" y="329.75" zvalue="1801"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,107.286,341.75) scale(1,1) translate(0,0)" writing-mode="lr" x="107.29" xml:space="preserve" y="346.25" zvalue="1801">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,547.466,344.091) scale(1,1) translate(0,0)" writing-mode="lr" x="547.47" xml:space="preserve" y="348.59" zvalue="32">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1043.99,435.584) scale(1,1) translate(1.14575e-13,0)" writing-mode="lr" x="1043.99" xml:space="preserve" y="440.08" zvalue="38">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1043.37,381.68) scale(1,1) translate(0,0)" writing-mode="lr" x="1043.37" xml:space="preserve" y="386.18" zvalue="40">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,436.043,657.778) scale(1,1) translate(0,0)" writing-mode="lr" x="436.04" xml:space="preserve" y="662.28" zvalue="205">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="329" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1839,660) scale(1,1) translate(0,0)" writing-mode="lr" x="1839" xml:space="preserve" y="664.5" zvalue="207">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="349" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1139.13,511.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1139.127845911436" xml:space="preserve" y="517.3749965229415" zvalue="368">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="369" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,476.965,828.474) scale(1,1) translate(0,0)" writing-mode="lr" x="476.97" xml:space="preserve" y="832.97" zvalue="392">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="655" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,647.991,533.94) scale(1,1) translate(0,0)" writing-mode="lr" x="647.99" xml:space="preserve" y="538.4400000000001" zvalue="777">10kVⅠ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="654" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,620.199,628.252) scale(1,1) translate(0,0)" writing-mode="lr" x="620.2" xml:space="preserve" y="632.75" zvalue="780">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="196" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,826.667,741.333) scale(1,1) translate(0,0)" writing-mode="lr" x="826.67" xml:space="preserve" y="745.83" zvalue="838">073</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="195" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,874.528,795.924) scale(1,1) translate(0,0)" writing-mode="lr" x="874.53" xml:space="preserve" y="800.42" zvalue="840">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,835.028,849.361) scale(1,1) translate(0,0)" writing-mode="lr" x="835.03" xml:space="preserve" y="853.86" zvalue="842">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,850.278,945.611) scale(1,1) translate(-1.7903e-13,0)" writing-mode="lr" x="850.28" xml:space="preserve" y="950.11" zvalue="846">10kV平建线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="170" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1039.5,607.222) scale(1,1) translate(3.41394e-13,0)" writing-mode="lr" x="1039.5" xml:space="preserve" y="611.72" zvalue="878">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1488,579.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1488" xml:space="preserve" y="584" zvalue="887">012</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1486.5,618.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1486.5" xml:space="preserve" y="623" zvalue="907">10kV分段</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="431" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,469.5,725.333) scale(1,1) translate(0,0)" writing-mode="lr" x="469.5" xml:space="preserve" y="729.83" zvalue="912">071</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="265" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,471.5,773.5) scale(1,1) translate(0,0)" writing-mode="lr" x="471.5" xml:space="preserve" y="778" zvalue="917">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="282" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1731.43,530) scale(1,1) translate(0,0)" writing-mode="lr" x="1731.43" xml:space="preserve" y="534.5" zvalue="965">10kVⅡ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="281" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1709.48,621.312) scale(1,1) translate(0,0)" writing-mode="lr" x="1709.48" xml:space="preserve" y="625.8099999999999" zvalue="967">0902</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,506.53,502.444) scale(1,1) translate(0,0)" writing-mode="lr" x="506.5304650708068" xml:space="preserve" y="506.9444444444444" zvalue="972">10kV#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,469.625,629.389) scale(1,1) translate(0,0)" writing-mode="lr" x="469.63" xml:space="preserve" y="633.89" zvalue="1068">0811</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="485" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1102.84,75.8122) scale(1,1) translate(0,0)" writing-mode="lr" x="1102.84" xml:space="preserve" y="80.31" zvalue="1126">35kV河平线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="483" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1085.55,211.442) scale(1,1) translate(0,0)" writing-mode="lr" x="1085.55" xml:space="preserve" y="215.94" zvalue="1129">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="482" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1078.48,190.939) scale(1,1) translate(0,0)" writing-mode="lr" x="1078.48" xml:space="preserve" y="195.44" zvalue="1131">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="481" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1085.99,307.237) scale(1,1) translate(0,0)" writing-mode="lr" x="1085.99" xml:space="preserve" y="311.74" zvalue="1134">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1079.98,252.02) scale(1,1) translate(7.12414e-13,0)" writing-mode="lr" x="1079.98" xml:space="preserve" y="256.52" zvalue="1153">372</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1395.25,635.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1395.25" xml:space="preserve" y="640.0700000000001" zvalue="1364">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1002.67,741.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1002.67" xml:space="preserve" y="745.83" zvalue="1373">074</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1050.53,795.924) scale(1,1) translate(0,0)" writing-mode="lr" x="1050.53" xml:space="preserve" y="800.42" zvalue="1375">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1011.03,849.361) scale(1,1) translate(0,0)" writing-mode="lr" x="1011.03" xml:space="preserve" y="853.86" zvalue="1377">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1026.28,945.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1026.28" xml:space="preserve" y="950.11" zvalue="1382">10kV平杞线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1385.67,745.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1385.67" xml:space="preserve" y="749.83" zvalue="1390">076</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1433.53,799.924) scale(1,1) translate(0,0)" writing-mode="lr" x="1433.53" xml:space="preserve" y="804.42" zvalue="1392">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1394.03,853.361) scale(1,1) translate(0,0)" writing-mode="lr" x="1394.03" xml:space="preserve" y="857.86" zvalue="1394">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1409.28,945.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1409.28" xml:space="preserve" y="950.11" zvalue="1399">10kV平蚌线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1568.67,749.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1568.67" xml:space="preserve" y="753.83" zvalue="1407">077</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1616.53,801.924) scale(1,1) translate(0,0)" writing-mode="lr" x="1616.53" xml:space="preserve" y="806.42" zvalue="1409">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1577.03,857.361) scale(1,1) translate(0,0)" writing-mode="lr" x="1577.03" xml:space="preserve" y="861.86" zvalue="1411">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1592.28,946.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1592.28" xml:space="preserve" y="951.11" zvalue="1416">10kV平蛮线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1182.67,741.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1182.67" xml:space="preserve" y="745.83" zvalue="1426">075</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1230.53,795.924) scale(1,1) translate(0,0)" writing-mode="lr" x="1230.53" xml:space="preserve" y="800.42" zvalue="1428">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1191.03,849.361) scale(1,1) translate(0,0)" writing-mode="lr" x="1191.03" xml:space="preserve" y="853.86" zvalue="1430">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1206.28,945.611) scale(1,1) translate(3.87116e-13,0)" writing-mode="lr" x="1206.28" xml:space="preserve" y="950.11" zvalue="1435">10kV备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,528.528,782.924) scale(1,1) translate(0,0)" writing-mode="lr" x="528.53" xml:space="preserve" y="787.42" zvalue="1452">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,528.528,836) scale(1,1) translate(0,0)" writing-mode="lr" x="528.53" xml:space="preserve" y="840.5" zvalue="1455">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,500,963.167) scale(1,1) translate(0,0)" writing-mode="lr" x="500" xml:space="preserve" y="967.67" zvalue="1459">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="273" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,632.581,828.474) scale(1,1) translate(0,0)" writing-mode="lr" x="632.58" xml:space="preserve" y="832.97" zvalue="1462">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="271" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,624.5,725.333) scale(1,1) translate(0,0)" writing-mode="lr" x="624.5" xml:space="preserve" y="729.83" zvalue="1465">072</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="270" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,626.5,773.5) scale(1,1) translate(0,0)" writing-mode="lr" x="626.5" xml:space="preserve" y="778" zvalue="1467">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="269" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,683.528,782.924) scale(1,1) translate(0,0)" writing-mode="lr" x="683.53" xml:space="preserve" y="787.42" zvalue="1474">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="268" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,683.528,836) scale(1,1) translate(0,0)" writing-mode="lr" x="683.53" xml:space="preserve" y="840.5" zvalue="1476">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,655.616,964.167) scale(1,1) translate(0,0)" writing-mode="lr" x="655.62" xml:space="preserve" y="968.67" zvalue="1480">10kV2号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1153,158.068) scale(1,1) translate(0,0)" writing-mode="lr" x="1153" xml:space="preserve" y="162.57" zvalue="1490">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1137,208) scale(1,1) translate(0,0)" writing-mode="lr" x="1137" xml:space="preserve" y="212.5" zvalue="1495">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1134.5,266.076) scale(1,1) translate(0,0)" writing-mode="lr" x="1134.5" xml:space="preserve" y="270.58" zvalue="1496">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1285,477) scale(1,1) translate(0,0)" writing-mode="lr" x="1285" xml:space="preserve" y="481.5" zvalue="1501">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1303.43,299.359) scale(1,1) translate(0,0)" writing-mode="lr" x="1303.43" xml:space="preserve" y="303.86" zvalue="1504">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1229.75,310.637) scale(1,1) translate(0,0)" writing-mode="lr" x="1229.75" xml:space="preserve" y="315.14" zvalue="1506">39010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="186" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1297,160.9) scale(1,1) translate(0,0)" writing-mode="lr" x="1297" xml:space="preserve" y="165.4" zvalue="1515">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1231.75,253) scale(1,1) translate(0,0)" writing-mode="lr" x="1231.75" xml:space="preserve" y="257.5" zvalue="1517">39017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1097.5,389) scale(1,1) translate(0,0)" writing-mode="lr" x="1097.5" xml:space="preserve" y="393.5" zvalue="1554">17</text>
  <line fill="none" id="454" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382.9999999999999" x2="382.9999999999999" y1="-10.25" y2="1064.75" zvalue="1701"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="19.57100000000014" x2="107.7853000000001" y1="949" y2="949"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="19.57100000000014" x2="107.7853000000001" y1="988.1632999999999" y2="988.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="19.57100000000014" x2="19.57100000000014" y1="949" y2="988.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.7853000000001" x2="107.7853000000001" y1="949" y2="988.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.7857000000001" x2="372.4287000000002" y1="949" y2="949"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.7857000000001" x2="372.4287000000002" y1="988.1632999999999" y2="988.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.7857000000001" x2="107.7857000000001" y1="949" y2="988.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372.4287000000002" x2="372.4287000000002" y1="949" y2="988.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="19.57100000000014" x2="107.7853000000001" y1="988.16327" y2="988.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="19.57100000000014" x2="107.7853000000001" y1="1016.08167" y2="1016.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="19.57100000000014" x2="19.57100000000014" y1="988.16327" y2="1016.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.7853000000001" x2="107.7853000000001" y1="988.16327" y2="1016.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.7857000000001" x2="196.0000000000001" y1="988.16327" y2="988.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.7857000000001" x2="196.0000000000001" y1="1016.08167" y2="1016.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.7857000000001" x2="107.7857000000001" y1="988.16327" y2="1016.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.0000000000001" x2="196.0000000000001" y1="988.16327" y2="1016.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.0000000000002" x2="284.2143000000002" y1="988.16327" y2="988.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.0000000000002" x2="284.2143000000002" y1="1016.08167" y2="1016.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.0000000000002" x2="196.0000000000002" y1="988.16327" y2="1016.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.2143000000002" x2="284.2143000000002" y1="988.16327" y2="1016.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.2143000000001" x2="372.4286000000001" y1="988.16327" y2="988.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.2143000000001" x2="372.4286000000001" y1="1016.08167" y2="1016.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.2143000000001" x2="284.2143000000001" y1="988.16327" y2="1016.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372.4286000000001" x2="372.4286000000001" y1="988.16327" y2="1016.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="19.57100000000014" x2="107.7853000000001" y1="1016.0816" y2="1016.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="19.57100000000014" x2="107.7853000000001" y1="1044" y2="1044"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="19.57100000000014" x2="19.57100000000014" y1="1016.0816" y2="1044"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.7853000000001" x2="107.7853000000001" y1="1016.0816" y2="1044"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.7857000000001" x2="196.0000000000001" y1="1016.0816" y2="1016.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.7857000000001" x2="196.0000000000001" y1="1044" y2="1044"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="107.7857000000001" x2="107.7857000000001" y1="1016.0816" y2="1044"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.0000000000001" x2="196.0000000000001" y1="1016.0816" y2="1044"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.0000000000002" x2="284.2143000000002" y1="1016.0816" y2="1016.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.0000000000002" x2="284.2143000000002" y1="1044" y2="1044"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.0000000000002" x2="196.0000000000002" y1="1016.0816" y2="1044"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.2143000000002" x2="284.2143000000002" y1="1016.0816" y2="1044"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.2143000000001" x2="372.4286000000001" y1="1016.0816" y2="1016.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.2143000000001" x2="372.4286000000001" y1="1044" y2="1044"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.2143000000001" x2="284.2143000000001" y1="1016.0816" y2="1044"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372.4286000000001" x2="372.4286000000001" y1="1016.0816" y2="1044"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="452" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60,969) scale(1,1) translate(0,0)" writing-mode="lr" x="60" xml:space="preserve" y="975" zvalue="1703">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="451" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,1003) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="1009" zvalue="1704">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="450" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,239,1003) scale(1,1) translate(0,0)" writing-mode="lr" x="239" xml:space="preserve" y="1009" zvalue="1705">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="449" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56,1031) scale(1,1) translate(0,0)" writing-mode="lr" x="56" xml:space="preserve" y="1037" zvalue="1706">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="448" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,238,1031) scale(1,1) translate(0,0)" writing-mode="lr" x="238" xml:space="preserve" y="1037" zvalue="1707">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="445" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,88.2778,618.611) scale(1,1) translate(0,0)" writing-mode="lr" x="88.27777777777783" xml:space="preserve" y="623.1111111111111" zvalue="1709">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="444" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.054,971) scale(1,1) translate(0,0)" writing-mode="lr" x="240.05" xml:space="preserve" y="977" zvalue="1710">PingShan-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="443" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,150.054,1003) scale(1,1) translate(0,0)" writing-mode="lr" x="150.05" xml:space="preserve" y="1009" zvalue="1711">李艳</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="442" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,330.054,1003) scale(1,1) translate(0,0)" writing-mode="lr" x="330.05" xml:space="preserve" y="1009" zvalue="1712">20200818</text>
  <line fill="none" id="440" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="378.9999999999995" y1="132.3741690846741" y2="132.3741690846741" zvalue="1714"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.99999999999977" x2="193.9999999999998" y1="144.5036764705917" y2="144.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.99999999999977" x2="193.9999999999998" y1="170.5036764705917" y2="170.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.99999999999977" x2="12.99999999999977" y1="144.5036764705917" y2="170.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.9999999999998" x2="193.9999999999998" y1="144.5036764705917" y2="170.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.9999999999998" x2="374.9999999999998" y1="144.5036764705917" y2="144.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.9999999999998" x2="374.9999999999998" y1="170.5036764705917" y2="170.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.9999999999998" x2="193.9999999999998" y1="144.5036764705917" y2="170.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="374.9999999999998" x2="374.9999999999998" y1="144.5036764705917" y2="170.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.99999999999977" x2="193.9999999999998" y1="170.5036764705917" y2="170.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.99999999999977" x2="193.9999999999998" y1="194.7536764705917" y2="194.7536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.99999999999977" x2="12.99999999999977" y1="170.5036764705917" y2="194.7536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.9999999999998" x2="193.9999999999998" y1="170.5036764705917" y2="194.7536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.9999999999998" x2="374.9999999999998" y1="170.5036764705917" y2="170.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.9999999999998" x2="374.9999999999998" y1="194.7536764705917" y2="194.7536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.9999999999998" x2="193.9999999999998" y1="170.5036764705917" y2="194.7536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="374.9999999999998" x2="374.9999999999998" y1="170.5036764705917" y2="194.7536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.99999999999977" x2="193.9999999999998" y1="194.7536764705917" y2="194.7536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.99999999999977" x2="193.9999999999998" y1="217.5036764705917" y2="217.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.99999999999977" x2="12.99999999999977" y1="194.7536764705917" y2="217.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.9999999999998" x2="193.9999999999998" y1="194.7536764705917" y2="217.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.9999999999998" x2="374.9999999999998" y1="194.7536764705917" y2="194.7536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.9999999999998" x2="374.9999999999998" y1="217.5036764705917" y2="217.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.9999999999998" x2="193.9999999999998" y1="194.7536764705917" y2="217.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="374.9999999999998" x2="374.9999999999998" y1="194.7536764705917" y2="217.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.99999999999977" x2="193.9999999999998" y1="217.5036764705917" y2="217.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.99999999999977" x2="193.9999999999998" y1="240.2536764705917" y2="240.2536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.99999999999977" x2="12.99999999999977" y1="217.5036764705917" y2="240.2536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.9999999999998" x2="193.9999999999998" y1="217.5036764705917" y2="240.2536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.9999999999998" x2="374.9999999999998" y1="217.5036764705917" y2="217.5036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.9999999999998" x2="374.9999999999998" y1="240.2536764705917" y2="240.2536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.9999999999998" x2="193.9999999999998" y1="217.5036764705917" y2="240.2536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="374.9999999999998" x2="374.9999999999998" y1="217.5036764705917" y2="240.2536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.99999999999977" x2="193.9999999999998" y1="240.2536764705917" y2="240.2536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.99999999999977" x2="193.9999999999998" y1="263.0036764705917" y2="263.0036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.99999999999977" x2="12.99999999999977" y1="240.2536764705917" y2="263.0036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.9999999999998" x2="193.9999999999998" y1="240.2536764705917" y2="263.0036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.9999999999998" x2="374.9999999999998" y1="240.2536764705917" y2="240.2536764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.9999999999998" x2="374.9999999999998" y1="263.0036764705917" y2="263.0036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.9999999999998" x2="193.9999999999998" y1="240.2536764705917" y2="263.0036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="374.9999999999998" x2="374.9999999999998" y1="240.2536764705917" y2="263.0036764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99999999999977" x2="109.7744999999998" y1="424.5036764705916" y2="424.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99999999999977" x2="109.7744999999998" y1="462.7859764705917" y2="462.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99999999999977" x2="63.99999999999977" y1="424.5036764705916" y2="462.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="109.7744999999998" y1="424.5036764705916" y2="462.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="168.5808999999998" y1="424.5036764705916" y2="424.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="168.5808999999998" y1="462.7859764705917" y2="462.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="109.7744999999998" y1="424.5036764705916" y2="462.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="168.5808999999998" y1="424.5036764705916" y2="462.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="227.3872999999998" y1="424.5036764705916" y2="424.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="227.3872999999998" y1="462.7859764705917" y2="462.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="168.5808999999998" y1="424.5036764705916" y2="462.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872999999998" x2="227.3872999999998" y1="424.5036764705916" y2="462.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3871999999998" x2="286.1935999999998" y1="424.5036764705916" y2="424.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3871999999998" x2="286.1935999999998" y1="462.7859764705917" y2="462.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3871999999998" x2="227.3871999999998" y1="424.5036764705916" y2="462.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="286.1935999999998" y1="424.5036764705916" y2="462.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="344.9999999999998" y1="424.5036764705916" y2="424.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="344.9999999999998" y1="462.7859764705917" y2="462.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="286.1935999999998" y1="424.5036764705916" y2="462.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="344.9999999999998" x2="344.9999999999998" y1="424.5036764705916" y2="462.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99999999999977" x2="109.7744999999998" y1="462.7859764705917" y2="462.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99999999999977" x2="109.7744999999998" y1="487.4653764705917" y2="487.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99999999999977" x2="63.99999999999977" y1="462.7859764705917" y2="487.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="109.7744999999998" y1="462.7859764705917" y2="487.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="168.5808999999998" y1="462.7859764705917" y2="462.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="168.5808999999998" y1="487.4653764705917" y2="487.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="109.7744999999998" y1="462.7859764705917" y2="487.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="168.5808999999998" y1="462.7859764705917" y2="487.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="227.3872999999998" y1="462.7859764705917" y2="462.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="227.3872999999998" y1="487.4653764705917" y2="487.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="168.5808999999998" y1="462.7859764705917" y2="487.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872999999998" x2="227.3872999999998" y1="462.7859764705917" y2="487.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3871999999998" x2="286.1935999999998" y1="462.7859764705917" y2="462.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3871999999998" x2="286.1935999999998" y1="487.4653764705917" y2="487.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3871999999998" x2="227.3871999999998" y1="462.7859764705917" y2="487.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="286.1935999999998" y1="462.7859764705917" y2="487.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="344.9999999999998" y1="462.7859764705917" y2="462.7859764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="344.9999999999998" y1="487.4653764705917" y2="487.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="286.1935999999998" y1="462.7859764705917" y2="487.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="344.9999999999998" x2="344.9999999999998" y1="462.7859764705917" y2="487.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99999999999977" x2="109.7744999999998" y1="487.4653764705917" y2="487.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99999999999977" x2="109.7744999999998" y1="512.1447764705916" y2="512.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99999999999977" x2="63.99999999999977" y1="487.4653764705917" y2="512.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="109.7744999999998" y1="487.4653764705917" y2="512.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="168.5808999999998" y1="487.4653764705917" y2="487.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="168.5808999999998" y1="512.1447764705916" y2="512.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="109.7744999999998" y1="487.4653764705917" y2="512.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="168.5808999999998" y1="487.4653764705917" y2="512.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="227.3872999999998" y1="487.4653764705917" y2="487.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="227.3872999999998" y1="512.1447764705916" y2="512.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="168.5808999999998" y1="487.4653764705917" y2="512.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872999999998" x2="227.3872999999998" y1="487.4653764705917" y2="512.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3871999999998" x2="286.1935999999998" y1="487.4653764705917" y2="487.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3871999999998" x2="286.1935999999998" y1="512.1447764705916" y2="512.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3871999999998" x2="227.3871999999998" y1="487.4653764705917" y2="512.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="286.1935999999998" y1="487.4653764705917" y2="512.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="344.9999999999998" y1="487.4653764705917" y2="487.4653764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="344.9999999999998" y1="512.1447764705916" y2="512.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="286.1935999999998" y1="487.4653764705917" y2="512.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="344.9999999999998" x2="344.9999999999998" y1="487.4653764705917" y2="512.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99999999999977" x2="109.7744999999998" y1="512.1447764705916" y2="512.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99999999999977" x2="109.7744999999998" y1="536.8241764705916" y2="536.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99999999999977" x2="63.99999999999977" y1="512.1447764705916" y2="536.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="109.7744999999998" y1="512.1447764705916" y2="536.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="168.5808999999998" y1="512.1447764705916" y2="512.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="168.5808999999998" y1="536.8241764705916" y2="536.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="109.7744999999998" y1="512.1447764705916" y2="536.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="168.5808999999998" y1="512.1447764705916" y2="536.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="227.3872999999998" y1="512.1447764705916" y2="512.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="227.3872999999998" y1="536.8241764705916" y2="536.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="168.5808999999998" y1="512.1447764705916" y2="536.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872999999998" x2="227.3872999999998" y1="512.1447764705916" y2="536.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3871999999998" x2="286.1935999999998" y1="512.1447764705916" y2="512.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3871999999998" x2="286.1935999999998" y1="536.8241764705916" y2="536.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3871999999998" x2="227.3871999999998" y1="512.1447764705916" y2="536.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="286.1935999999998" y1="512.1447764705916" y2="536.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="344.9999999999998" y1="512.1447764705916" y2="512.1447764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="344.9999999999998" y1="536.8241764705916" y2="536.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="286.1935999999998" y1="512.1447764705916" y2="536.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="344.9999999999998" x2="344.9999999999998" y1="512.1447764705916" y2="536.8241764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99999999999977" x2="109.7744999999998" y1="536.8242764705916" y2="536.8242764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99999999999977" x2="109.7744999999998" y1="561.5036764705916" y2="561.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99999999999977" x2="63.99999999999977" y1="536.8242764705916" y2="561.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="109.7744999999998" y1="536.8242764705916" y2="561.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="168.5808999999998" y1="536.8242764705916" y2="536.8242764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="168.5808999999998" y1="561.5036764705916" y2="561.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="109.7744999999998" y1="536.8242764705916" y2="561.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="168.5808999999998" y1="536.8242764705916" y2="561.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="227.3872999999998" y1="536.8242764705916" y2="536.8242764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="227.3872999999998" y1="561.5036764705916" y2="561.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="168.5808999999998" y1="536.8242764705916" y2="561.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872999999998" x2="227.3872999999998" y1="536.8242764705916" y2="561.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3871999999998" x2="286.1935999999998" y1="536.8242764705916" y2="536.8242764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3871999999998" x2="286.1935999999998" y1="561.5036764705916" y2="561.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3871999999998" x2="227.3871999999998" y1="536.8242764705916" y2="561.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="286.1935999999998" y1="536.8242764705916" y2="561.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="344.9999999999998" y1="536.8242764705916" y2="536.8242764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="344.9999999999998" y1="561.5036764705916" y2="561.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="286.1935999999998" y1="536.8242764705916" y2="561.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="344.9999999999998" x2="344.9999999999998" y1="536.8242764705916" y2="561.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99999999999977" x2="109.7744999999998" y1="561.5036764705916" y2="561.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99999999999977" x2="109.7744999999998" y1="586.1830764705917" y2="586.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="63.99999999999977" x2="63.99999999999977" y1="561.5036764705916" y2="586.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="109.7744999999998" y1="561.5036764705916" y2="586.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="168.5808999999998" y1="561.5036764705916" y2="561.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="168.5808999999998" y1="586.1830764705917" y2="586.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7744999999998" x2="109.7744999999998" y1="561.5036764705916" y2="586.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="168.5808999999998" y1="561.5036764705916" y2="586.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="227.3872999999998" y1="561.5036764705916" y2="561.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="227.3872999999998" y1="586.1830764705917" y2="586.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5808999999998" x2="168.5808999999998" y1="561.5036764705916" y2="586.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872999999998" x2="227.3872999999998" y1="561.5036764705916" y2="586.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3871999999998" x2="286.1935999999998" y1="561.5036764705916" y2="561.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3871999999998" x2="286.1935999999998" y1="586.1830764705917" y2="586.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3871999999998" x2="227.3871999999998" y1="561.5036764705916" y2="586.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="286.1935999999998" y1="561.5036764705916" y2="586.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="344.9999999999998" y1="561.5036764705916" y2="561.5036764705916"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="344.9999999999998" y1="586.1830764705917" y2="586.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1935999999998" x2="286.1935999999998" y1="561.5036764705916" y2="586.1830764705917"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="344.9999999999998" x2="344.9999999999998" y1="561.5036764705916" y2="586.1830764705917"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="434" stroke="rgb(255,255,255)" text-anchor="middle" x="136.53125" xml:space="preserve" y="441.8993055555555" zvalue="1718">35kV     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="434" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="136.53125" xml:space="preserve" y="458.8993055555555" zvalue="1718">Ⅰ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="433" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,210.399,299.345) scale(1,1) translate(0,0)" writing-mode="lr" x="210.4" xml:space="preserve" y="303.85" zvalue="1719">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="432" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,315.399,299.345) scale(1,1) translate(0,0)" writing-mode="lr" x="315.4" xml:space="preserve" y="303.85" zvalue="1720">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="430" stroke="rgb(255,255,255)" text-anchor="middle" x="255.9375" xml:space="preserve" y="440.3524305555555" zvalue="1721">10kV     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="430" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="255.9375" xml:space="preserve" y="457.3524305555555" zvalue="1721">Ⅰ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="429" stroke="rgb(255,255,255)" text-anchor="middle" x="315" xml:space="preserve" y="440.3524305555555" zvalue="1722">10kV      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="429" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="315" xml:space="preserve" y="457.3524305555555" zvalue="1722">Ⅱ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="428" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89,475.254) scale(1,1) translate(0,0)" writing-mode="lr" x="88.99999999999977" xml:space="preserve" y="479.7536764705916" zvalue="1723">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="425" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89,500.754) scale(1,1) translate(0,-1.0797e-13)" writing-mode="lr" x="88.99999999999977" xml:space="preserve" y="505.2536764705916" zvalue="1724">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="424" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89,523.754) scale(1,1) translate(0,0)" writing-mode="lr" x="88.99999999999977" xml:space="preserve" y="528.2536764705917" zvalue="1725">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="423" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89,546.754) scale(1,1) translate(0,5.9092e-14)" writing-mode="lr" x="88.99999999999977" xml:space="preserve" y="551.2536764705916" zvalue="1726">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="422" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89,573.754) scale(1,1) translate(0,0)" writing-mode="lr" x="88.99999999999977" xml:space="preserve" y="578.2536764705917" zvalue="1727">Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="421" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51,158.504) scale(1,1) translate(0,0)" writing-mode="lr" x="51" xml:space="preserve" y="164" zvalue="1728">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="420" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231,158.504) scale(1,1) translate(0,0)" writing-mode="lr" x="231" xml:space="preserve" y="164" zvalue="1729">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="419" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.6875,182.754) scale(1,1) translate(0,0)" writing-mode="lr" x="54.69" xml:space="preserve" y="187.25" zvalue="1730">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="418" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.1875,230.504) scale(1,1) translate(0,0)" writing-mode="lr" x="58.19" xml:space="preserve" y="236" zvalue="1731">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="417" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,239.75,230.004) scale(1,1) translate(0,0)" writing-mode="lr" x="239.75" xml:space="preserve" y="235.5" zvalue="1732">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="416" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.1875,253.504) scale(1,1) translate(0,0)" writing-mode="lr" x="58.19" xml:space="preserve" y="259" zvalue="1733">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="415" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,239.75,253.004) scale(1,1) translate(0,0)" writing-mode="lr" x="239.75" xml:space="preserve" y="258.5" zvalue="1734">2号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="414" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6875,206.754) scale(1,1) translate(0,0)" writing-mode="lr" x="55.69" xml:space="preserve" y="211.25" zvalue="1735">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="413" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,238.75,206.504) scale(1,1) translate(0,0)" writing-mode="lr" x="238.75" xml:space="preserve" y="211" zvalue="1736">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,834.836,79.5) scale(1,1) translate(0,0)" writing-mode="lr" x="834.84" xml:space="preserve" y="84" zvalue="1819">35kV厂山线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,817.274,211.13) scale(1,1) translate(0,0)" writing-mode="lr" x="817.27" xml:space="preserve" y="215.63" zvalue="1821">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,809.991,190.626) scale(1,1) translate(0,0)" writing-mode="lr" x="809.99" xml:space="preserve" y="195.13" zvalue="1823">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,817.987,306.925) scale(1,1) translate(0,0)" writing-mode="lr" x="817.99" xml:space="preserve" y="311.42" zvalue="1825">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,811.976,251.708) scale(1,1) translate(0,0)" writing-mode="lr" x="811.98" xml:space="preserve" y="256.21" zvalue="1831">371</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,886.5,157.756) scale(1,1) translate(0,0)" writing-mode="lr" x="886.5" xml:space="preserve" y="162.26" zvalue="1836">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,869.25,207.688) scale(1,1) translate(0,0)" writing-mode="lr" x="869.25" xml:space="preserve" y="212.19" zvalue="1842">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,866.5,265.764) scale(1,1) translate(0,0)" writing-mode="lr" x="866.5" xml:space="preserve" y="270.26" zvalue="1843">17</text>
  <ellipse cx="1122.66" cy="249.66" fill="rgb(255,0,0)" fill-opacity="1" id="257" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1860"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,302.235,383) scale(1,1) translate(0,0)" writing-mode="lr" x="302.2350463867188" xml:space="preserve" y="387.5" zvalue="1864">小电流接地</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,107.286,301.539) scale(1,1) translate(0,3.22453e-13)" writing-mode="lr" x="107.2857055664062" xml:space="preserve" y="306.0388641357421" zvalue="1865">全站公用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1138.5,536.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1138.5" xml:space="preserve" y="541" zvalue="1868">2500kVA</text>
 </g>
 <g id="ButtonClass">
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="172.09" y="370.25" zvalue="1799"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="70.19" y="370.25" zvalue="1800"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="70.84999999999999" y="329.75" zvalue="1801"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="137">
   <path class="kv35" d="M 587.06 349.09 L 1458.39 349.09" stroke-width="6" zvalue="31"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674397978627" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674397978627"/></metadata>
  <path d="M 587.06 349.09 L 1458.39 349.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv10" d="M 428.22 675 L 1455 675" stroke-width="4" zvalue="204"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674398044163" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674398044163"/></metadata>
  <path d="M 428.22 675 L 1455 675" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv10" d="M 1545 677 L 1881 677" stroke-width="4" zvalue="206"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674398109699" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674398109699"/></metadata>
  <path d="M 1545 677 L 1881 677" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="276">
   <use class="kv35" height="20" transform="rotate(180,1067.59,435.209) scale(1.5542,1.35421) translate(-377.911,-110.292)" width="10" x="1059.816710722901" xlink:href="#Breaker:开关_0" y="421.6666962122333" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925086609412" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925086609412"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1067.59,435.209) scale(1.5542,1.35421) translate(-377.911,-110.292)" width="10" x="1059.816710722901" y="421.6666962122333"/></g>
  <g id="207">
   <use class="kv10" height="20" transform="rotate(0,850.278,742.333) scale(2.22222,2.22222) translate(-461.542,-396.061)" width="10" x="839.1666666666666" xlink:href="#Breaker:小车断路器_0" y="720.1111111111111" zvalue="837"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925086674948" ObjectName="10kV平建线073断路器"/>
   <cge:TPSR_Ref TObjectID="6473925086674948"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,850.278,742.333) scale(2.22222,2.22222) translate(-461.542,-396.061)" width="10" x="839.1666666666666" y="720.1111111111111"/></g>
  <g id="233">
   <use class="kv10" height="20" transform="rotate(0,1067.11,608.222) scale(2.22222,2.22222) translate(-580.8,-322.3)" width="10" x="1056" xlink:href="#Breaker:小车断路器_0" y="586" zvalue="877"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925086740484" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925086740484"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1067.11,608.222) scale(2.22222,2.22222) translate(-580.8,-322.3)" width="10" x="1056" y="586"/></g>
  <g id="435">
   <use class="kv10" height="20" transform="rotate(90,1489,599) scale(2,2) translate(-739.5,-289.5)" width="10" x="1479" xlink:href="#Breaker:母联小车开关_0" y="579" zvalue="886"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925086806020" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473925086806020"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1489,599) scale(2,2) translate(-739.5,-289.5)" width="10" x="1479" y="579"/></g>
  <g id="189">
   <use class="kv10" height="20" transform="rotate(0,500.111,726.333) scale(2.22222,2.22222) translate(-268.95,-387.261)" width="10" x="489" xlink:href="#Breaker:小车断路器_0" y="704.1111145019531" zvalue="911"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925086871556" ObjectName="10kV1号电容器071断路器"/>
   <cge:TPSR_Ref TObjectID="6473925086871556"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,500.111,726.333) scale(2.22222,2.22222) translate(-268.95,-387.261)" width="10" x="489" y="704.1111145019531"/></g>
  <g id="507">
   <use class="kv35" height="20" transform="rotate(0,1103.21,255.02) scale(1.5542,1.35421) translate(-390.612,-63.1615)" width="10" x="1095.434034534565" xlink:href="#Breaker:开关_0" y="241.4779663271798" zvalue="1152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925086937092" ObjectName="35kV河平线372断路器"/>
   <cge:TPSR_Ref TObjectID="6473925086937092"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1103.21,255.02) scale(1.5542,1.35421) translate(-390.612,-63.1615)" width="10" x="1095.434034534565" y="241.4779663271798"/></g>
  <g id="53">
   <use class="kv10" height="20" transform="rotate(0,1026.28,742.333) scale(2.22222,2.22222) translate(-558.342,-396.061)" width="10" x="1015.166666666667" xlink:href="#Breaker:小车断路器_0" y="720.1111111111111" zvalue="1372"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925087002628" ObjectName="10kV平杞线074断路器"/>
   <cge:TPSR_Ref TObjectID="6473925087002628"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1026.28,742.333) scale(2.22222,2.22222) translate(-558.342,-396.061)" width="10" x="1015.166666666667" y="720.1111111111111"/></g>
  <g id="70">
   <use class="kv10" height="20" transform="rotate(0,1409.28,746.333) scale(2.22222,2.22222) translate(-768.992,-398.261)" width="10" x="1398.166666666667" xlink:href="#Breaker:小车断路器_0" y="724.1111111111111" zvalue="1389"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925087068164" ObjectName="10kV平蚌线076断路器"/>
   <cge:TPSR_Ref TObjectID="6473925087068164"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1409.28,746.333) scale(2.22222,2.22222) translate(-768.992,-398.261)" width="10" x="1398.166666666667" y="724.1111111111111"/></g>
  <g id="85">
   <use class="kv10" height="20" transform="rotate(0,1592.28,750.333) scale(2.22222,2.22222) translate(-869.642,-400.461)" width="10" x="1581.166666666667" xlink:href="#Breaker:小车断路器_0" y="728.1111111111111" zvalue="1406"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925087133700" ObjectName="10kV平蛮线077断路器"/>
   <cge:TPSR_Ref TObjectID="6473925087133700"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1592.28,750.333) scale(2.22222,2.22222) translate(-869.642,-400.461)" width="10" x="1581.166666666667" y="728.1111111111111"/></g>
  <g id="176">
   <use class="kv10" height="20" transform="rotate(0,1206.28,742.333) scale(2.22222,2.22222) translate(-657.342,-396.061)" width="10" x="1195.166666666667" xlink:href="#Breaker:小车断路器_0" y="720.1111111111111" zvalue="1425"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925087199236" ObjectName="10kV备用线075断路器"/>
   <cge:TPSR_Ref TObjectID="6473925087199236"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1206.28,742.333) scale(2.22222,2.22222) translate(-657.342,-396.061)" width="10" x="1195.166666666667" y="720.1111111111111"/></g>
  <g id="336">
   <use class="kv10" height="20" transform="rotate(0,655.111,726.333) scale(2.22222,2.22222) translate(-354.2,-387.261)" width="10" x="644" xlink:href="#Breaker:小车断路器_0" y="704.1111145019531" zvalue="1464"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925087264772" ObjectName="10kV2号电容器072断路器"/>
   <cge:TPSR_Ref TObjectID="6473925087264772"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,655.111,726.333) scale(2.22222,2.22222) translate(-354.2,-387.261)" width="10" x="644" y="704.1111145019531"/></g>
  <g id="225">
   <use class="kv35" height="20" transform="rotate(0,835.205,254.708) scale(1.5542,1.35421) translate(-295.048,-63.0799)" width="10" x="827.4340345345652" xlink:href="#Breaker:开关_0" y="241.1657309121979" zvalue="1830"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924571168772" ObjectName="35kV厂山线371断路器"/>
   <cge:TPSR_Ref TObjectID="6473924571168772"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,835.205,254.708) scale(1.5542,1.35421) translate(-295.048,-63.0799)" width="10" x="827.4340345345652" y="241.1657309121979"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="272">
   <use class="kv35" height="30" transform="rotate(0,1066.4,381.372) scale(0.947693,-0.6712) translate(58.4668,-954.499)" width="15" x="1059.293211654153" xlink:href="#Disconnector:刀闸_0" y="371.3043387992516" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453737447426" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453737447426"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1066.4,381.372) scale(0.947693,-0.6712) translate(58.4668,-954.499)" width="15" x="1059.293211654153" y="371.3043387992516"/></g>
  <g id="388">
   <use class="kv10" height="30" transform="rotate(180,500.051,827.891) scale(0.833333,-0.833333) translate(98.7602,-1823.86)" width="15" x="493.8009703534215" xlink:href="#Disconnector:刀闸_0" y="815.3910827636716" zvalue="391"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453737512962" ObjectName="10kV1号电容器0716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453737512962"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,500.051,827.891) scale(0.833333,-0.833333) translate(98.7602,-1823.86)" width="15" x="493.8009703534215" y="815.3910827636716"/></g>
  <g id="658">
   <use class="kv10" height="26" transform="rotate(0,655.08,629.252) scale(1.42857,1.42857) translate(-193.524,-183.204)" width="14" x="645.0799317770949" xlink:href="#Disconnector:联体手车刀闸_0" y="610.6807396958916" zvalue="778"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453737578498" ObjectName="10kVⅠ段母线电压互感器0901手车"/>
   <cge:TPSR_Ref TObjectID="6192453737578498"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,655.08,629.252) scale(1.42857,1.42857) translate(-193.524,-183.204)" width="14" x="645.0799317770949" y="610.6807396958916"/></g>
  <g id="205">
   <use class="kv10" height="30" transform="rotate(0,850.278,850.361) scale(-0.833333,0.833333) translate(-1871.86,167.572)" width="15" x="844.0277777777777" xlink:href="#Disconnector:刀闸_0" y="837.8611111111111" zvalue="841"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453737840642" ObjectName="10kV平建线0736隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453737840642"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,850.278,850.361) scale(-0.833333,0.833333) translate(-1871.86,167.572)" width="15" x="844.0277777777777" y="837.8611111111111"/></g>
  <g id="285">
   <use class="kv10" height="26" transform="rotate(0,1739.16,622.312) scale(1.42857,1.42857) translate(-518.749,-181.122)" width="14" x="1729.164868296376" xlink:href="#Disconnector:联体手车刀闸_0" y="603.7405236202904" zvalue="966"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453738430466" ObjectName="10kVⅡ段母线电压互感器0902手车"/>
   <cge:TPSR_Ref TObjectID="6192453738430466"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1739.16,622.312) scale(1.42857,1.42857) translate(-518.749,-181.122)" width="14" x="1729.164868296376" y="603.7405236202904"/></g>
  <g id="426">
   <use class="kv10" height="27" transform="rotate(0,500.111,636.323) scale(1.96627,1.855) translate(-239.002,-281.748)" width="14" x="486.3472222222222" xlink:href="#Disconnector:带融断手车刀闸_0" y="611.2801830681374" zvalue="1067"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453738627074" ObjectName="10kV2号手车站用变0811手车"/>
   <cge:TPSR_Ref TObjectID="6192453738627074"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,500.111,636.323) scale(1.96627,1.855) translate(-239.002,-281.748)" width="14" x="486.3472222222222" y="611.2801830681374"/></g>
  <g id="497">
   <use class="kv35" height="30" transform="rotate(0,1103.46,209.276) scale(-0.947693,0.6712) translate(-2268.22,97.5854)" width="15" x="1096.353492892157" xlink:href="#Disconnector:刀闸_0" y="199.2075541917166" zvalue="1128"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453738954754" ObjectName="35kV河平线3726隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453738954754"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1103.46,209.276) scale(-0.947693,0.6712) translate(-2268.22,97.5854)" width="15" x="1096.353492892157" y="199.2075541917166"/></g>
  <g id="494">
   <use class="kv35" height="30" transform="rotate(180,1103.95,306.544) scale(0.947693,-0.6712) translate(60.5395,-768.187)" width="15" x="1096.845940718963" xlink:href="#Disconnector:刀闸_0" y="296.4763072434744" zvalue="1133"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453738758147" ObjectName="35kV河平线3721隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453738758147"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1103.95,306.544) scale(0.947693,-0.6712) translate(60.5395,-768.187)" width="15" x="1096.845940718963" y="296.4763072434744"/></g>
  <g id="3">
   <use class="kv10" height="26" transform="rotate(0,1409.25,636.571) scale(1.42857,1.42857) translate(-419.774,-185.4)" width="14" x="1399.246598443762" xlink:href="#Disconnector:联体手车刀闸_0" y="618" zvalue="1363"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453739085826" ObjectName="10kV分段0121手车"/>
   <cge:TPSR_Ref TObjectID="6192453739085826"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1409.25,636.571) scale(1.42857,1.42857) translate(-419.774,-185.4)" width="14" x="1399.246598443762" y="618"/></g>
  <g id="49">
   <use class="kv10" height="30" transform="rotate(0,1026.28,850.361) scale(-0.833333,0.833333) translate(-2259.06,167.572)" width="15" x="1020.027777777778" xlink:href="#Disconnector:刀闸_0" y="837.8611111111111" zvalue="1376"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453739347970" ObjectName="10kV平杞线0746隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453739347970"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1026.28,850.361) scale(-0.833333,0.833333) translate(-2259.06,167.572)" width="15" x="1020.027777777778" y="837.8611111111111"/></g>
  <g id="68">
   <use class="kv10" height="30" transform="rotate(0,1409.28,854.361) scale(-0.833333,0.833333) translate(-3101.66,168.372)" width="15" x="1403.027777777778" xlink:href="#Disconnector:刀闸_0" y="841.8611111111111" zvalue="1393"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453739741186" ObjectName="10kV平蚌线0766隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453739741186"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1409.28,854.361) scale(-0.833333,0.833333) translate(-3101.66,168.372)" width="15" x="1403.027777777778" y="841.8611111111111"/></g>
  <g id="83">
   <use class="kv10" height="30" transform="rotate(0,1592.28,858.361) scale(-0.833333,0.833333) translate(-3504.26,169.172)" width="15" x="1586.027777777778" xlink:href="#Disconnector:刀闸_0" y="845.8611111111111" zvalue="1410"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453740134402" ObjectName="10kV平蛮线0776隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453740134402"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1592.28,858.361) scale(-0.833333,0.833333) translate(-3504.26,169.172)" width="15" x="1586.027777777778" y="845.8611111111111"/></g>
  <g id="168">
   <use class="kv10" height="30" transform="rotate(0,1206.28,850.361) scale(-0.833333,0.833333) translate(-2655.06,167.572)" width="15" x="1200.027777777778" xlink:href="#Disconnector:刀闸_0" y="837.8611111111111" zvalue="1429"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453740527618" ObjectName="10kV备用线0756隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453740527618"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1206.28,850.361) scale(-0.833333,0.833333) translate(-2655.06,167.572)" width="15" x="1200.027777777778" y="837.8611111111111"/></g>
  <g id="343">
   <use class="kv10" height="30" transform="rotate(180,655.667,827.891) scale(0.833333,-0.833333) translate(129.883,-1823.86)" width="15" x="649.416666666667" xlink:href="#Disconnector:刀闸_0" y="815.3910827636716" zvalue="1461"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453741707266" ObjectName="10kV2号电容器0726隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453741707266"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,655.667,827.891) scale(0.833333,-0.833333) translate(129.883,-1823.86)" width="15" x="649.416666666667" y="815.3910827636716"/></g>
  <g id="10">
   <use class="kv35" height="30" transform="rotate(180,1278,385.636) scale(-1,-1) translate(-2556,-771.272)" width="30" x="1263" xlink:href="#Disconnector:跌落刀闸_0" y="370.6360267306203" zvalue="1485"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453741838338" ObjectName="35kV1号站用变跌落刀闸"/>
   <cge:TPSR_Ref TObjectID="6192453741838338"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1278,385.636) scale(-1,-1) translate(-2556,-771.272)" width="30" x="1263" y="370.6360267306203"/></g>
  <g id="12">
   <use class="kv35" height="30" transform="rotate(0,1136.11,159.068) scale(-0.947693,0.6712) translate(-2335.31,72.9903)" width="15" x="1129" xlink:href="#Disconnector:刀闸_0" y="148.9999999999999" zvalue="1489"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453741903874" ObjectName="35kV河平线3729隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453741903874"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1136.11,159.068) scale(-0.947693,0.6712) translate(-2335.31,72.9903)" width="15" x="1129" y="148.9999999999999"/></g>
  <g id="145">
   <use class="kv35" height="30" transform="rotate(0,1278.1,300.359) scale(1.11111,-0.814815) translate(-126.976,-671.76)" width="15" x="1269.764196723179" xlink:href="#Disconnector:刀闸_0" y="288.1371662947867" zvalue="1503"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453742493698" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453742493698"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1278.1,300.359) scale(1.11111,-0.814815) translate(-126.976,-671.76)" width="15" x="1269.764196723179" y="288.1371662947867"/></g>
  <g id="239">
   <use class="kv35" height="30" transform="rotate(0,835.461,208.963) scale(-0.947693,0.6712) translate(-1717.43,97.4324)" width="15" x="828.3534928921569" xlink:href="#Disconnector:刀闸_0" y="198.8953187767348" zvalue="1820"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450127331333" ObjectName="35kV厂山线3716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450127331333"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,835.461,208.963) scale(-0.947693,0.6712) translate(-1717.43,97.4324)" width="15" x="828.3534928921569" y="198.8953187767348"/></g>
  <g id="236">
   <use class="kv35" height="30" transform="rotate(180,835.954,306.232) scale(0.947693,-0.6712) translate(45.7474,-767.409)" width="15" x="828.8459407189629" xlink:href="#Disconnector:刀闸_0" y="296.1640718284925" zvalue="1824"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450127134725" ObjectName="35kV厂山线3711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450127134725"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,835.954,306.232) scale(0.947693,-0.6712) translate(45.7474,-767.409)" width="15" x="828.8459407189629" y="296.1640718284925"/></g>
  <g id="221">
   <use class="kv35" height="30" transform="rotate(0,868.108,158.756) scale(-0.947693,0.6712) translate(-1784.52,72.8374)" width="15" x="861.0000000000001" xlink:href="#Disconnector:刀闸_0" y="148.6877645850182" zvalue="1835"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450127003653" ObjectName="35kV厂山线3719隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450127003653"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,868.108,158.756) scale(-0.947693,0.6712) translate(-1784.52,72.8374)" width="15" x="861.0000000000001" y="148.6877645850182"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="136">
   <path class="kv35" d="M 1066.46 371.48 L 1066.46 349.09" stroke-width="1" zvalue="41"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@1" LinkObjectIDznd="137@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1066.46 371.48 L 1066.46 349.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv10" d="M 655.11 645.42 L 655.11 675" stroke-width="1" zvalue="805"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="658@1" LinkObjectIDznd="179@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 655.11 645.42 L 655.11 675" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv10" d="M 655.06 613.11 L 655.06 582.55" stroke-width="1" zvalue="806"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="658@0" LinkObjectIDznd="659@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 655.06 613.11 L 655.06 582.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="kv10" d="M 850.28 762.33 L 850.2 838.27" stroke-width="1" zvalue="843"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@1" LinkObjectIDznd="205@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 850.28 762.33 L 850.2 838.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="203">
   <path class="kv10" d="M 850.23 862.65 L 850.28 905.61" stroke-width="1" zvalue="844"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@1" LinkObjectIDznd="201@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 850.23 862.65 L 850.28 905.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="kv10" d="M 877.75 884.72 L 877.75 872.44 L 850.24 872.44" stroke-width="1" zvalue="847"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="200@0" LinkObjectIDznd="203" MaxPinNum="2"/>
   </metadata>
  <path d="M 877.75 884.72 L 877.75 872.44 L 850.24 872.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="234">
   <path class="kv10" d="M 1067.61 559.07 L 1067.61 587.67" stroke-width="1" zvalue="878"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="348@1" LinkObjectIDznd="233@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1067.61 559.07 L 1067.61 587.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv10" d="M 1067.11 628.22 L 1067.11 675" stroke-width="1" zvalue="898"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@1" LinkObjectIDznd="179@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1067.11 628.22 L 1067.11 675" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv10" d="M 850.28 721.78 L 850.28 675" stroke-width="1" zvalue="905"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="179@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 850.28 721.78 L 850.28 675" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="kv10" d="M 500.11 675 L 500.11 705.78" stroke-width="1" zvalue="912"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@2" LinkObjectIDznd="189@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 500.11 675 L 500.11 705.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="284">
   <path class="kv10" d="M 1739.2 638.48 L 1739.2 677" stroke-width="1" zvalue="968"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="285@1" LinkObjectIDznd="180@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1739.2 638.48 L 1739.2 677" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="283">
   <path class="kv10" d="M 1739.15 606.17 L 1739.15 583.61" stroke-width="1" zvalue="969"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="285@0" LinkObjectIDznd="286@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1739.15 606.17 L 1739.15 583.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="427">
   <path class="kv10" d="M 500.11 660.44 L 500.11 675" stroke-width="1" zvalue="1068"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="426@1" LinkObjectIDznd="192" MaxPinNum="2"/>
   </metadata>
  <path d="M 500.11 660.44 L 500.11 675" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="493">
   <path class="kv35" d="M 1103.38 199.54 L 1103.38 126.81" stroke-width="1" zvalue="1135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="497@0" LinkObjectIDznd="499@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1103.38 199.54 L 1103.38 126.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="492">
   <path class="kv35" d="M 1103.9 316.44 L 1103.9 349.09" stroke-width="1" zvalue="1136"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="494@1" LinkObjectIDznd="137@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1103.9 316.44 L 1103.9 349.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="487">
   <path class="kv35" d="M 1080.53 150.52 L 1103.38 150.52" stroke-width="1" zvalue="1141"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="489@0" LinkObjectIDznd="493" MaxPinNum="2"/>
   </metadata>
  <path d="M 1080.53 150.52 L 1103.38 150.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="510">
   <path class="kv35" d="M 1103.87 296.81 L 1103.87 267.95" stroke-width="1" zvalue="1155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="494@0" LinkObjectIDznd="507@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1103.87 296.81 L 1103.87 267.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="511">
   <path class="kv35" d="M 1103.15 242.06 L 1103.15 219.17" stroke-width="1" zvalue="1156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="507@0" LinkObjectIDznd="497@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1103.15 242.06 L 1103.15 219.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="607">
   <path class="kv35" d="M 1083.07 177 L 1103.38 177" stroke-width="1" zvalue="1255"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="496@0" LinkObjectIDznd="493" MaxPinNum="2"/>
   </metadata>
  <path d="M 1083.07 177 L 1103.38 177" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="kv10" d="M 500.11 559.07 L 500.11 612.67" stroke-width="1" zvalue="1361"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="288@0" LinkObjectIDznd="426@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 500.11 559.07 L 500.11 612.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv10" d="M 1409.23 620.43 L 1409.23 599 L 1471 599" stroke-width="1" zvalue="1365"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="3@0" LinkObjectIDznd="435@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.23 620.43 L 1409.23 599 L 1471 599" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv10" d="M 864.34 783.24 L 850.26 783.24" stroke-width="1" zvalue="1369"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@0" LinkObjectIDznd="204" MaxPinNum="2"/>
   </metadata>
  <path d="M 864.34 783.24 L 850.26 783.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv10" d="M 850.94 799.27 L 850.24 799.27" stroke-width="1" zvalue="1370"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="251@0" LinkObjectIDznd="204" MaxPinNum="2"/>
   </metadata>
  <path d="M 850.94 799.27 L 850.24 799.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv10" d="M 1026.28 762.33 L 1026.28 838.27" stroke-width="1" zvalue="1378"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@1" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1026.28 762.33 L 1026.28 838.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv10" d="M 1026.23 862.65 L 1026.23 905.61" stroke-width="1" zvalue="1379"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@1" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1026.23 862.65 L 1026.23 905.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv10" d="M 1053.75 884.72 L 1053.75 872.44 L 1026.32 872.44" stroke-width="1" zvalue="1383"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="44" MaxPinNum="2"/>
   </metadata>
  <path d="M 1053.75 884.72 L 1053.75 872.44 L 1026.32 872.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="kv10" d="M 1026.28 721.78 L 1026.28 675" stroke-width="1" zvalue="1384"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="179@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1026.28 721.78 L 1026.28 675" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv10" d="M 1040.34 783.24 L 1026.3 783.24" stroke-width="1" zvalue="1386"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="48" MaxPinNum="2"/>
   </metadata>
  <path d="M 1040.34 783.24 L 1026.3 783.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv10" d="M 1026.94 799.27 L 1026.31 799.27" stroke-width="1" zvalue="1387"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31@0" LinkObjectIDznd="48" MaxPinNum="2"/>
   </metadata>
  <path d="M 1026.94 799.27 L 1026.31 799.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv10" d="M 1409.28 766.33 L 1409.28 842.27" stroke-width="1" zvalue="1395"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@1" LinkObjectIDznd="68@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.28 766.33 L 1409.28 842.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv10" d="M 1409.23 866.65 L 1409.28 908.26" stroke-width="1" zvalue="1396"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@1" LinkObjectIDznd="65@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.23 866.65 L 1409.28 908.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv10" d="M 1436.75 888.72 L 1436.75 876.44 L 1409.24 876.44" stroke-width="1" zvalue="1400"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@0" LinkObjectIDznd="66" MaxPinNum="2"/>
   </metadata>
  <path d="M 1436.75 888.72 L 1436.75 876.44 L 1409.24 876.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv10" d="M 1409.28 725.78 L 1409.28 675" stroke-width="1" zvalue="1401"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="179@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.28 725.78 L 1409.28 675" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv10" d="M 1423.34 787.24 L 1409.3 787.24" stroke-width="1" zvalue="1403"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="67" MaxPinNum="2"/>
   </metadata>
  <path d="M 1423.34 787.24 L 1409.3 787.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv10" d="M 1409.94 803.27 L 1409.31 803.27" stroke-width="1" zvalue="1404"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="67" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.94 803.27 L 1409.31 803.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv10" d="M 1592.28 770.33 L 1592.28 846.27" stroke-width="1" zvalue="1412"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@1" LinkObjectIDznd="83@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1592.28 770.33 L 1592.28 846.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv10" d="M 1592.23 870.65 L 1592.28 913.61" stroke-width="1" zvalue="1413"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@1" LinkObjectIDznd="80@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1592.23 870.65 L 1592.28 913.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv10" d="M 1619.75 892.72 L 1619.75 882.69 L 1592.24 882.69" stroke-width="1" zvalue="1417"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="81" MaxPinNum="2"/>
   </metadata>
  <path d="M 1619.75 892.72 L 1619.75 882.69 L 1592.24 882.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv10" d="M 1606.34 791.24 L 1592.3 791.24" stroke-width="1" zvalue="1419"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@0" LinkObjectIDznd="82" MaxPinNum="2"/>
   </metadata>
  <path d="M 1606.34 791.24 L 1592.3 791.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv10" d="M 1592.94 819.27 L 1592.28 819.27" stroke-width="1" zvalue="1420"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@0" LinkObjectIDznd="82" MaxPinNum="2"/>
   </metadata>
  <path d="M 1592.94 819.27 L 1592.28 819.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv10" d="M 1592.28 729.78 L 1592.28 677" stroke-width="1" zvalue="1421"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@0" LinkObjectIDznd="180@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1592.28 729.78 L 1592.28 677" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv10" d="M 1507.1 599.1 L 1592 599 L 1592 677" stroke-width="1" zvalue="1422"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="435@0" LinkObjectIDznd="180@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1507.1 599.1 L 1592 599 L 1592 677" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv10" d="M 1409.28 652.74 L 1409.28 675" stroke-width="1" zvalue="1423"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="3@1" LinkObjectIDznd="179@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.28 652.74 L 1409.28 675" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv10" d="M 1206.28 762.33 L 1206.2 838.27" stroke-width="1" zvalue="1431"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@1" LinkObjectIDznd="168@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1206.28 762.33 L 1206.2 838.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv10" d="M 1206.23 862.65 L 1206.28 905.61" stroke-width="1" zvalue="1432"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@1" LinkObjectIDznd="155@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1206.23 862.65 L 1206.28 905.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv10" d="M 1233.75 884.72 L 1233.75 872.44 L 1206.24 872.44" stroke-width="1" zvalue="1436"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="157" MaxPinNum="2"/>
   </metadata>
  <path d="M 1233.75 884.72 L 1233.75 872.44 L 1206.24 872.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="kv10" d="M 1206.28 721.78 L 1206.28 675" stroke-width="1" zvalue="1437"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@0" LinkObjectIDznd="179@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1206.28 721.78 L 1206.28 675" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv10" d="M 1220.34 783.24 L 1206.26 783.24" stroke-width="1" zvalue="1439"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@0" LinkObjectIDznd="158" MaxPinNum="2"/>
   </metadata>
  <path d="M 1220.34 783.24 L 1206.26 783.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv10" d="M 1206.94 799.27 L 1206.24 799.27" stroke-width="1" zvalue="1440"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="158" MaxPinNum="2"/>
   </metadata>
  <path d="M 1206.94 799.27 L 1206.24 799.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="kv10" d="M 500.94 566.27 L 500.11 566.27" stroke-width="1" zvalue="1445"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@0" LinkObjectIDznd="2" MaxPinNum="2"/>
   </metadata>
  <path d="M 500.94 566.27 L 500.11 566.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="kv10" d="M 477.97 589.17 L 477.97 602 L 500.11 602" stroke-width="1" zvalue="1446"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@0" LinkObjectIDznd="2" MaxPinNum="2"/>
   </metadata>
  <path d="M 477.97 589.17 L 477.97 602 L 500.11 602" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv10" d="M 516.34 852.31 L 500 852.31" stroke-width="1" zvalue="1455"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@0" LinkObjectIDznd="263" MaxPinNum="2"/>
   </metadata>
  <path d="M 516.34 852.31 L 500 852.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="263">
   <path class="kv10" d="M 500 840.18 L 500 872.46" stroke-width="1" zvalue="1459"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="388@1" LinkObjectIDznd="262@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 500 840.18 L 500 872.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="kv10" d="M 671.34 852.31 L 655.62 852.31" stroke-width="1" zvalue="1477"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="279@0" LinkObjectIDznd="274" MaxPinNum="2"/>
   </metadata>
  <path d="M 671.34 852.31 L 655.62 852.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="274">
   <path class="kv10" d="M 655.62 840.18 L 655.62 873.58" stroke-width="1" zvalue="1479"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="343@1" LinkObjectIDznd="275@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 655.62 840.18 L 655.62 873.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="344">
   <path class="kv10" d="M 655.11 705.78 L 655.11 675" stroke-width="1" zvalue="1481"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="336@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 655.11 705.78 L 655.11 675" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="kv10" d="M 1614.5 821.74 L 1614.5 810 L 1592.28 810" stroke-width="1" zvalue="1483"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="4@0" LinkObjectIDznd="82" MaxPinNum="2"/>
   </metadata>
  <path d="M 1614.5 821.74 L 1614.5 810 L 1592.28 810" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv35" d="M 1278 370.64 L 1278 349.09" stroke-width="1" zvalue="1487"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@0" LinkObjectIDznd="137@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1278 370.64 L 1278 349.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv35" d="M 1136.05 168.96 L 1136.05 177 L 1103.38 177" stroke-width="1" zvalue="1490"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="12@1" LinkObjectIDznd="493" MaxPinNum="2"/>
   </metadata>
  <path d="M 1136.05 168.96 L 1136.05 177 L 1103.38 177" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv35" d="M 1136.02 144.33 L 1136.02 149.33" stroke-width="1" zvalue="1492"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="16@0" LinkObjectIDznd="12@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1136.02 144.33 L 1136.02 149.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv35" d="M 1122.31 229.31 L 1103.15 229.31" stroke-width="1" zvalue="1498"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@0" LinkObjectIDznd="511" MaxPinNum="2"/>
   </metadata>
  <path d="M 1122.31 229.31 L 1103.15 229.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv35" d="M 1122.31 282.39 L 1103.87 282.39" stroke-width="1" zvalue="1499"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="27@0" LinkObjectIDznd="510" MaxPinNum="2"/>
   </metadata>
  <path d="M 1122.31 282.39 L 1103.87 282.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv35" d="M 1278.35 416.62 L 1278.35 399.64" stroke-width="1" zvalue="1501"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="10@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1278.35 416.62 L 1278.35 399.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv35" d="M 1278.2 312.18 L 1278.2 349.09" stroke-width="1" zvalue="1509"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="137@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1278.2 312.18 L 1278.2 349.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv35" d="M 1278.17 288.35 L 1278.17 249.08" stroke-width="1" zvalue="1510"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@1" LinkObjectIDznd="185@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1278.17 288.35 L 1278.17 249.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="kv35" d="M 1278.2 326.47 L 1251.33 326.47" stroke-width="1" zvalue="1512"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104" LinkObjectIDznd="110@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1278.2 326.47 L 1251.33 326.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="kv35" d="M 1246.33 269.83 L 1278.17 269.83" stroke-width="1" zvalue="1518"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@0" LinkObjectIDznd="103" MaxPinNum="2"/>
   </metadata>
  <path d="M 1246.33 269.83 L 1278.17 269.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv10" d="M 499.98 815.8 L 499.98 746.33" stroke-width="1" zvalue="1804"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="388@0" LinkObjectIDznd="189@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 499.98 815.8 L 499.98 746.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="165">
   <path class="kv10" d="M 655.59 815.8 L 655.59 746.33" stroke-width="1" zvalue="1805"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="343@0" LinkObjectIDznd="336@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 655.59 815.8 L 655.59 746.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv10" d="M 487.69 759.19 L 499.98 759.19" stroke-width="1" zvalue="1808"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@0" LinkObjectIDznd="122" MaxPinNum="2"/>
   </metadata>
  <path d="M 487.69 759.19 L 499.98 759.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv10" d="M 654.94 766.27 L 655.59 766.27" stroke-width="1" zvalue="1809"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="332@0" LinkObjectIDznd="165" MaxPinNum="2"/>
   </metadata>
  <path d="M 654.94 766.27 L 655.59 766.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv10" d="M 642.69 759.19 L 655.59 759.19" stroke-width="1" zvalue="1810"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="334@0" LinkObjectIDznd="165" MaxPinNum="2"/>
   </metadata>
  <path d="M 642.69 759.19 L 655.59 759.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv10" d="M 671.34 799.24 L 655.59 799.24" stroke-width="1" zvalue="1811"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="294@0" LinkObjectIDznd="165" MaxPinNum="2"/>
   </metadata>
  <path d="M 671.34 799.24 L 655.59 799.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="kv10" d="M 516.34 799.24 L 499.98 799.24" stroke-width="1" zvalue="1812"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="247@0" LinkObjectIDznd="122" MaxPinNum="2"/>
   </metadata>
  <path d="M 516.34 799.24 L 499.98 799.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv10" d="M 500.94 771.27 L 499.98 771.27" stroke-width="1" zvalue="1813"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="241@0" LinkObjectIDznd="122" MaxPinNum="2"/>
   </metadata>
  <path d="M 500.94 771.27 L 499.98 771.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv35" d="M 1066.48 391.11 L 1066.48 422.28" stroke-width="1" zvalue="1814"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="276@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1066.48 391.11 L 1066.48 422.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv35" d="M 1085.31 409.31 L 1066.48 409.31" stroke-width="1" zvalue="1815"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="45" MaxPinNum="2"/>
   </metadata>
  <path d="M 1085.31 409.31 L 1066.48 409.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv35" d="M 1067.64 448.16 L 1067.64 473.16" stroke-width="1" zvalue="1816"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="348@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1067.64 448.16 L 1067.64 473.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="235">
   <path class="kv35" d="M 835.38 199.23 L 835.38 130.49" stroke-width="1" zvalue="1826"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="239@0" LinkObjectIDznd="240@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 835.38 199.23 L 835.38 130.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="kv35" d="M 835.9 316.13 L 835.9 349.09" stroke-width="1" zvalue="1827"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="236@1" LinkObjectIDznd="137@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 835.9 316.13 L 835.9 349.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="kv35" d="M 835.87 296.5 L 835.87 267.64" stroke-width="1" zvalue="1832"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="236@0" LinkObjectIDznd="225@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 835.87 296.5 L 835.87 267.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="223">
   <path class="kv35" d="M 835.15 241.75 L 835.15 218.86" stroke-width="1" zvalue="1833"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="225@0" LinkObjectIDznd="239@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 835.15 241.75 L 835.15 218.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="kv35" d="M 815.07 176.69 L 835.38 176.69" stroke-width="1" zvalue="1834"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="238@0" LinkObjectIDznd="235" MaxPinNum="2"/>
   </metadata>
  <path d="M 815.07 176.69 L 835.38 176.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="220">
   <path class="kv35" d="M 868.05 168.65 L 868.05 176.69 L 835.38 176.69" stroke-width="1" zvalue="1837"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@1" LinkObjectIDznd="222" MaxPinNum="2"/>
   </metadata>
  <path d="M 868.05 168.65 L 868.05 176.69 L 835.38 176.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv35" d="M 867.51 145.11 L 867.51 149.02" stroke-width="1" zvalue="1839"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="221@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 867.51 145.11 L 867.51 149.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="kv35" d="M 854.31 229 L 835.15 229" stroke-width="1" zvalue="1844"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="167@0" LinkObjectIDznd="223" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.31 229 L 835.15 229" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv35" d="M 854.31 282.08 L 835.87 282.08" stroke-width="1" zvalue="1845"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="224" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.31 282.08 L 835.87 282.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="245">
   <path class="kv35" d="M 812.53 150.21 L 835.38 150.21" stroke-width="1" zvalue="1849"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@0" LinkObjectIDznd="235" MaxPinNum="2"/>
   </metadata>
  <path d="M 812.53 150.21 L 835.38 150.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="348">
   <g id="3480">
    <use class="kv35" height="30" transform="rotate(0,1067.61,515.925) scale(3.08333,3.07167) translate(-696.355,-316.887)" width="24" x="1030.61" xlink:href="#PowerTransformer2:可调不带中性点_0" y="469.85" zvalue="367"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874563846146" ObjectName="35"/>
    </metadata>
   </g>
   <g id="3481">
    <use class="kv10" height="30" transform="rotate(0,1067.61,515.925) scale(3.08333,3.07167) translate(-696.355,-316.887)" width="24" x="1030.61" xlink:href="#PowerTransformer2:可调不带中性点_1" y="469.85" zvalue="367"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874563911682" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399523106818" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399523106818"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1067.61,515.925) scale(3.08333,3.07167) translate(-696.355,-316.887)" width="24" x="1030.61" y="469.85"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="659">
   <use class="kv10" height="30" transform="rotate(0,652.296,565.135) scale(-1.25,-1.25) translate(-1170.38,-1013.49)" width="30" x="633.5463306757724" xlink:href="#Accessory:避雷器PT带熔断器_0" y="546.3846605200456" zvalue="776"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453737644034" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,652.296,565.135) scale(-1.25,-1.25) translate(-1170.38,-1013.49)" width="30" x="633.5463306757724" y="546.3846605200456"/></g>
  <g id="200">
   <use class="kv10" height="26" transform="rotate(0,877.722,900.889) scale(0.833333,1.30769) translate(174.544,-207.974)" width="12" x="872.7222222222221" xlink:href="#Accessory:避雷器_0" y="883.8888888888889" zvalue="846"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453737709570" ObjectName="10kV平建线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,877.722,900.889) scale(0.833333,1.30769) translate(174.544,-207.974)" width="12" x="872.7222222222221" y="883.8888888888889"/></g>
  <g id="241">
   <use class="kv10" height="30" transform="rotate(90,501,784) scale(1.26667,1.26667) translate(-101.474,-161.053)" width="30" x="482" xlink:href="#Accessory:放电间隙3_0" y="765" zvalue="921"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453738299394" ObjectName="10kV1号电容器放电间隙"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,501,784) scale(1.26667,1.26667) translate(-101.474,-161.053)" width="30" x="482" y="765"/></g>
  <g id="251">
   <use class="kv10" height="30" transform="rotate(90,851,812) scale(1.26667,1.26667) translate(-175.158,-166.947)" width="30" x="832" xlink:href="#Accessory:放电间隙3_0" y="793" zvalue="935"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453738364930" ObjectName="10kV平建线放电间隙"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,851,812) scale(1.26667,1.26667) translate(-175.158,-166.947)" width="30" x="832" y="793"/></g>
  <g id="286">
   <use class="kv10" height="30" transform="rotate(0,1738.73,566.194) scale(-1.25,-1.25) translate(-3125.97,-1015.4)" width="30" x="1719.981550240434" xlink:href="#Accessory:避雷器PT带熔断器_0" y="547.4444444444445" zvalue="964"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453738496002" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1738.73,566.194) scale(-1.25,-1.25) translate(-3125.97,-1015.4)" width="30" x="1719.981550240434" y="547.4444444444445"/></g>
  <g id="489">
   <use class="kv35" height="26" transform="rotate(90,1069.06,150.494) scale(0.838049,0.927421) translate(205.621,10.834)" width="12" x="1064.028180926365" xlink:href="#Accessory:避雷器1_0" y="138.4375150508462" zvalue="1139"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453738692610" ObjectName="35kV河平线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1069.06,150.494) scale(0.838049,0.927421) translate(205.621,10.834)" width="12" x="1064.028180926365" y="138.4375150508462"/></g>
  <g id="35">
   <use class="kv10" height="26" transform="rotate(0,1053.72,900.889) scale(0.833333,1.30769) translate(209.744,-207.974)" width="12" x="1048.722222222222" xlink:href="#Accessory:避雷器_0" y="883.8888888888889" zvalue="1381"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453739216898" ObjectName="10kV平杞线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1053.72,900.889) scale(0.833333,1.30769) translate(209.744,-207.974)" width="12" x="1048.722222222222" y="883.8888888888889"/></g>
  <g id="31">
   <use class="kv10" height="30" transform="rotate(90,1027,812) scale(1.26667,1.26667) translate(-212.211,-166.947)" width="30" x="1008" xlink:href="#Accessory:放电间隙3_0" y="793" zvalue="1385"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453739151362" ObjectName="10kV平杞线放电间隙"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1027,812) scale(1.26667,1.26667) translate(-212.211,-166.947)" width="30" x="1008" y="793"/></g>
  <g id="64">
   <use class="kv10" height="26" transform="rotate(0,1436.72,904.889) scale(0.833333,1.30769) translate(286.344,-208.915)" width="12" x="1431.722222222222" xlink:href="#Accessory:避雷器_0" y="887.8888888888889" zvalue="1398"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453739610114" ObjectName="10kV平蚌线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1436.72,904.889) scale(0.833333,1.30769) translate(286.344,-208.915)" width="12" x="1431.722222222222" y="887.8888888888889"/></g>
  <g id="60">
   <use class="kv10" height="30" transform="rotate(90,1410,816) scale(1.26667,1.26667) translate(-292.842,-167.789)" width="30" x="1391" xlink:href="#Accessory:放电间隙3_0" y="797" zvalue="1402"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453739544578" ObjectName="10kV平蚌线放电间隙"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1410,816) scale(1.26667,1.26667) translate(-292.842,-167.789)" width="30" x="1391" y="797"/></g>
  <g id="79">
   <use class="kv10" height="26" transform="rotate(0,1619.72,908.889) scale(0.833333,1.30769) translate(322.944,-209.856)" width="12" x="1614.722222222222" xlink:href="#Accessory:避雷器_0" y="891.8888888888889" zvalue="1415"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453740003330" ObjectName="10kV平蛮线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1619.72,908.889) scale(0.833333,1.30769) translate(322.944,-209.856)" width="12" x="1614.722222222222" y="891.8888888888889"/></g>
  <g id="77">
   <use class="kv10" height="30" transform="rotate(90,1593,832) scale(1.26667,1.26667) translate(-331.368,-171.158)" width="30" x="1574" xlink:href="#Accessory:放电间隙3_0" y="813" zvalue="1418"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453739937794" ObjectName="10kV平蛮线放电间隙"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1593,832) scale(1.26667,1.26667) translate(-331.368,-171.158)" width="30" x="1574" y="813"/></g>
  <g id="150">
   <use class="kv10" height="26" transform="rotate(0,1233.72,900.889) scale(0.833333,1.30769) translate(245.744,-207.974)" width="12" x="1228.722222222222" xlink:href="#Accessory:避雷器_0" y="883.8888888888889" zvalue="1434"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453740396546" ObjectName="10kV备用线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1233.72,900.889) scale(0.833333,1.30769) translate(245.744,-207.974)" width="12" x="1228.722222222222" y="883.8888888888889"/></g>
  <g id="131">
   <use class="kv10" height="30" transform="rotate(90,1207,812) scale(1.26667,1.26667) translate(-250.105,-166.947)" width="30" x="1188" xlink:href="#Accessory:放电间隙3_0" y="793" zvalue="1438"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453740331010" ObjectName="10kV备用线放电间隙"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1207,812) scale(1.26667,1.26667) translate(-250.105,-166.947)" width="30" x="1188" y="793"/></g>
  <g id="187">
   <use class="kv10" height="26" transform="rotate(180,478,573) scale(0.833333,1.30769) translate(94.6,-130.824)" width="12" x="473" xlink:href="#Accessory:避雷器_0" y="556" zvalue="1442"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453740724226" ObjectName="10kV#2站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,478,573) scale(0.833333,1.30769) translate(94.6,-130.824)" width="12" x="473" y="556"/></g>
  <g id="188">
   <use class="kv10" height="30" transform="rotate(90,501,579) scale(1.26667,1.26667) translate(-101.474,-117.895)" width="30" x="482" xlink:href="#Accessory:放电间隙3_0" y="560" zvalue="1444"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453740789762" ObjectName="10kV#2站用变放电间隙"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,501,579) scale(1.26667,1.26667) translate(-101.474,-117.895)" width="30" x="482" y="560"/></g>
  <g id="332">
   <use class="kv10" height="30" transform="rotate(90,655,779) scale(1.26667,1.26667) translate(-133.895,-160)" width="30" x="636" xlink:href="#Accessory:放电间隙3_0" y="760" zvalue="1468"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453741510658" ObjectName="10kV2号电容器放电间隙"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,655,779) scale(1.26667,1.26667) translate(-133.895,-160)" width="30" x="636" y="760"/></g>
  <g id="4">
   <use class="kv10" height="18" transform="rotate(0,1614.5,831.077) scale(1.07143,1.23077) translate(-107.133,-153.75)" width="14" x="1607" xlink:href="#Accessory:PT7_0" y="820" zvalue="1482"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453741772802" ObjectName="10kV平蛮线pt"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1614.5,831.077) scale(1.07143,1.23077) translate(-107.133,-153.75)" width="14" x="1607" y="820"/></g>
  <g id="16">
   <use class="kv35" height="36" transform="rotate(0,1136.02,123.819) scale(1.35294,-1.16667) translate(-293.354,-226.95)" width="17" x="1124.524511195822" xlink:href="#Accessory:线路PT235_0" y="102.8189853161861" zvalue="1491"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453741969410" ObjectName="35kV河平线PT"/>
   </metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1136.02,123.819) scale(1.35294,-1.16667) translate(-293.354,-226.95)" width="17" x="1124.524511195822" y="102.8189853161861"/></g>
  <g id="185">
   <use class="kv35" height="42" transform="rotate(0,1292,219.2) scale(1.46667,1.46667) translate(-404.091,-59.9455)" width="30" x="1270" xlink:href="#Accessory:4卷PT带容断器_0" y="188.3999999999997" zvalue="1514"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453742559234" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1292,219.2) scale(1.46667,1.46667) translate(-404.091,-59.9455)" width="30" x="1270" y="188.3999999999997"/></g>
  <g id="231">
   <use class="kv35" height="26" transform="rotate(90,801.056,150.182) scale(0.838049,0.927421) translate(153.831,10.8095)" width="12" x="796.0281809263649" xlink:href="#Accessory:避雷器1_0" y="138.1252796358642" zvalue="1828"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450127069189" ObjectName="35kV厂山线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,801.056,150.182) scale(0.838049,0.927421) translate(153.831,10.8095)" width="12" x="796.0281809263649" y="138.1252796358642"/></g>
  <g id="197">
   <use class="kv35" height="40" transform="rotate(0,867.512,125.688) scale(1.23252,1.05) translate(-160.17,-4.98513)" width="30" x="849.0245111958225" xlink:href="#Accessory:带熔断器的线路PT1_0" y="104.6877645850182" zvalue="1838"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450126938117" ObjectName="35kV厂山线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,867.512,125.688) scale(1.23252,1.05) translate(-160.17,-4.98513)" width="30" x="849.0245111958225" y="104.6877645850182"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="206">
   <use class="kv10" height="20" transform="rotate(270,876.528,783.174) scale(-1.25,1.25) translate(-1576.5,-154.135)" width="10" x="870.2777777777777" xlink:href="#GroundDisconnector:地刀_0" y="770.6736111111111" zvalue="839"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453737971714" ObjectName="10kV平建线07367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453737971714"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,876.528,783.174) scale(-1.25,1.25) translate(-1576.5,-154.135)" width="10" x="870.2777777777777" y="770.6736111111111"/></g>
  <g id="237">
   <use class="kv10" height="20" transform="rotate(90,475.5,759.25) scale(-1.25,1.25) translate(-854.65,-149.35)" width="10" x="469.25" xlink:href="#GroundDisconnector:地刀_0" y="746.75" zvalue="916"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453738233858" ObjectName="10kV1号电容器07117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453738233858"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,475.5,759.25) scale(-1.25,1.25) translate(-854.65,-149.35)" width="10" x="469.25" y="746.75"/></g>
  <g id="496">
   <use class="kv35" height="20" transform="rotate(270,1073.25,176.938) scale(-1.24619,-1.0068) translate(-1933.24,-352.612)" width="10" x="1067.018227141073" xlink:href="#GroundDisconnector:地刀_0" y="166.8696843123374" zvalue="1130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453738889218" ObjectName="35kV河平线37267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453738889218"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1073.25,176.938) scale(-1.24619,-1.0068) translate(-1933.24,-352.612)" width="10" x="1067.018227141073" y="166.8696843123374"/></g>
  <g id="51">
   <use class="kv10" height="20" transform="rotate(270,1052.53,783.174) scale(-1.25,1.25) translate(-1893.3,-154.135)" width="10" x="1046.277777777778" xlink:href="#GroundDisconnector:地刀_0" y="770.6736111111111" zvalue="1374"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453739479042" ObjectName="10kV平杞线07467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453739479042"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1052.53,783.174) scale(-1.25,1.25) translate(-1893.3,-154.135)" width="10" x="1046.277777777778" y="770.6736111111111"/></g>
  <g id="69">
   <use class="kv10" height="20" transform="rotate(270,1435.53,787.174) scale(-1.25,1.25) translate(-2582.7,-154.935)" width="10" x="1429.277777777778" xlink:href="#GroundDisconnector:地刀_0" y="774.6736111111111" zvalue="1391"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453739872258" ObjectName="10kV平蚌线07667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453739872258"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1435.53,787.174) scale(-1.25,1.25) translate(-2582.7,-154.935)" width="10" x="1429.277777777778" y="774.6736111111111"/></g>
  <g id="84">
   <use class="kv10" height="20" transform="rotate(270,1618.53,791.174) scale(-1.25,1.25) translate(-2912.1,-155.735)" width="10" x="1612.277777777778" xlink:href="#GroundDisconnector:地刀_0" y="778.6736111111111" zvalue="1408"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453740265474" ObjectName="10kV平蛮线07767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453740265474"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1618.53,791.174) scale(-1.25,1.25) translate(-2912.1,-155.735)" width="10" x="1612.277777777778" y="778.6736111111111"/></g>
  <g id="175">
   <use class="kv10" height="20" transform="rotate(270,1232.53,783.174) scale(-1.25,1.25) translate(-2217.3,-154.135)" width="10" x="1226.277777777778" xlink:href="#GroundDisconnector:地刀_0" y="770.6736111111111" zvalue="1427"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453740658690" ObjectName="10kV备用线07567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453740658690"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1232.53,783.174) scale(-1.25,1.25) translate(-2217.3,-154.135)" width="10" x="1226.277777777778" y="770.6736111111111"/></g>
  <g id="247">
   <use class="kv10" height="20" transform="rotate(270,528.528,799.174) scale(-1.25,1.25) translate(-950.1,-157.335)" width="10" x="522.2777709960935" xlink:href="#GroundDisconnector:地刀_0" y="786.6736111111111" zvalue="1451"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453740920834" ObjectName="10kV1号电容器07160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453740920834"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,528.528,799.174) scale(-1.25,1.25) translate(-950.1,-157.335)" width="10" x="522.2777709960935" y="786.6736111111111"/></g>
  <g id="249">
   <use class="kv10" height="20" transform="rotate(270,528.528,852.25) scale(-1.25,1.25) translate(-950.1,-167.95)" width="10" x="522.2777709960938" xlink:href="#GroundDisconnector:地刀_0" y="839.75" zvalue="1454"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453741051906" ObjectName="10kV1号电容器07167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453741051906"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,528.528,852.25) scale(-1.25,1.25) translate(-950.1,-167.95)" width="10" x="522.2777709960938" y="839.75"/></g>
  <g id="334">
   <use class="kv10" height="20" transform="rotate(90,630.5,759.25) scale(-1.25,1.25) translate(-1133.65,-149.35)" width="10" x="624.25" xlink:href="#GroundDisconnector:地刀_0" y="746.75" zvalue="1466"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453741641730" ObjectName="10kV2号电容器07217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453741641730"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,630.5,759.25) scale(-1.25,1.25) translate(-1133.65,-149.35)" width="10" x="624.25" y="746.75"/></g>
  <g id="294">
   <use class="kv10" height="20" transform="rotate(270,683.528,799.174) scale(-1.25,1.25) translate(-1229.1,-157.335)" width="10" x="677.2777709960935" xlink:href="#GroundDisconnector:地刀_0" y="786.6736111111111" zvalue="1472"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453741445122" ObjectName="10kV2号电容器07260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453741445122"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,683.528,799.174) scale(-1.25,1.25) translate(-1229.1,-157.335)" width="10" x="677.2777709960935" y="786.6736111111111"/></g>
  <g id="279">
   <use class="kv10" height="20" transform="rotate(270,683.528,852.25) scale(-1.25,1.25) translate(-1229.1,-167.95)" width="10" x="677.2777709960938" xlink:href="#GroundDisconnector:地刀_0" y="839.75" zvalue="1475"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453741314050" ObjectName="10kV2号电容器07267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453741314050"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,683.528,852.25) scale(-1.25,1.25) translate(-1229.1,-167.95)" width="10" x="677.2777709960938" y="839.75"/></g>
  <g id="26">
   <use class="kv35" height="20" transform="rotate(270,1134.5,229.25) scale(-1.25,1.25) translate(-2040.85,-43.35)" width="10" x="1128.25" xlink:href="#GroundDisconnector:地刀_0" y="216.75" zvalue="1494"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453742100482" ObjectName="35kV河平线37260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453742100482"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1134.5,229.25) scale(-1.25,1.25) translate(-2040.85,-43.35)" width="10" x="1128.25" y="216.75"/></g>
  <g id="27">
   <use class="kv35" height="20" transform="rotate(270,1134.5,282.326) scale(-1.25,1.25) translate(-2040.85,-53.9653)" width="10" x="1128.25" xlink:href="#GroundDisconnector:地刀_0" y="269.8263888888889" zvalue="1495"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453742231554" ObjectName="35kV河平线37217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453742231554"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1134.5,282.326) scale(-1.25,1.25) translate(-2040.85,-53.9653)" width="10" x="1128.25" y="269.8263888888889"/></g>
  <g id="110">
   <use class="kv35" height="20" transform="rotate(90,1240.5,326.415) scale(1.11111,1.11111) translate(-123.494,-31.5304)" width="10" x="1234.944444444444" xlink:href="#GroundDisconnector:地刀_0" y="315.3038090136317" zvalue="1505"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453742428162" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453742428162"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1240.5,326.415) scale(1.11111,1.11111) translate(-123.494,-31.5304)" width="10" x="1234.944444444444" y="315.3038090136317"/></g>
  <g id="198">
   <use class="kv35" height="20" transform="rotate(90,1235.5,269.778) scale(1.11111,1.11111) translate(-122.994,-25.8667)" width="10" x="1229.944444444444" xlink:href="#GroundDisconnector:地刀_0" y="258.6666653951009" zvalue="1516"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453742690307" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453742690307"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1235.5,269.778) scale(1.11111,1.11111) translate(-122.994,-25.8667)" width="10" x="1229.944444444444" y="258.6666653951009"/></g>
  <g id="9">
   <use class="kv35" height="20" transform="rotate(270,1097.5,409.25) scale(-1.25,1.25) translate(-1974.25,-79.35)" width="10" x="1091.25" xlink:href="#GroundDisconnector:地刀_0" y="396.75" zvalue="1553"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453757763586" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453757763586"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1097.5,409.25) scale(-1.25,1.25) translate(-1974.25,-79.35)" width="10" x="1091.25" y="396.75"/></g>
  <g id="238">
   <use class="kv35" height="20" transform="rotate(270,805.249,176.625) scale(-1.24619,-1.0068) translate(-1450.19,-351.99)" width="10" x="799.0182271410733" xlink:href="#GroundDisconnector:地刀_0" y="166.5574488973555" zvalue="1822"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450127265797" ObjectName="35kV厂山线37167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450127265797"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,805.249,176.625) scale(-1.24619,-1.0068) translate(-1450.19,-351.99)" width="10" x="799.0182271410733" y="166.5574488973555"/></g>
  <g id="167">
   <use class="kv35" height="20" transform="rotate(270,866.5,228.938) scale(-1.25,1.25) translate(-1558.45,-43.2876)" width="10" x="860.25" xlink:href="#GroundDisconnector:地刀_0" y="216.4377645850182" zvalue="1840"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450126872581" ObjectName="35kV厂山线37160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450126872581"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,866.5,228.938) scale(-1.25,1.25) translate(-1558.45,-43.2876)" width="10" x="860.25" y="216.4377645850182"/></g>
  <g id="166">
   <use class="kv35" height="20" transform="rotate(270,866.5,282.014) scale(-1.25,1.25) translate(-1558.45,-53.9028)" width="10" x="860.2500000000002" xlink:href="#GroundDisconnector:地刀_0" y="269.5141534739071" zvalue="1841"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450126741509" ObjectName="35kV厂山线37117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450126741509"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,866.5,282.014) scale(-1.25,1.25) translate(-1558.45,-53.9028)" width="10" x="860.2500000000002" y="269.5141534739071"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="201">
   <use class="kv10" height="30" transform="rotate(180,850.278,919.111) scale(1,1) translate(0,0)" width="12" x="844.2777777777777" xlink:href="#EnergyConsumer:负荷_0" y="904.1111149258084" zvalue="845"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453737775106" ObjectName="10kV平建线"/>
   <cge:TPSR_Ref TObjectID="6192453737775106"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,850.278,919.111) scale(1,1) translate(0,0)" width="12" x="844.2777777777777" y="904.1111149258084"/></g>
  <g id="288">
   <use class="kv10" height="30" transform="rotate(0,499.97,536.954) scale(1.53571,-1.53571) translate(-166.908,-878.563)" width="28" x="478.4700210507046" xlink:href="#EnergyConsumer:站用变DY接地_0" y="513.9184695514938" zvalue="971"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453738561538" ObjectName="10kV#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,499.97,536.954) scale(1.53571,-1.53571) translate(-166.908,-878.563)" width="28" x="478.4700210507046" y="513.9184695514938"/></g>
  <g id="36">
   <use class="kv10" height="30" transform="rotate(180,1026.28,919.111) scale(1,1) translate(0,0)" width="12" x="1020.277777777778" xlink:href="#EnergyConsumer:负荷_0" y="904.1111149258084" zvalue="1380"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453739282434" ObjectName="10kV平杞线"/>
   <cge:TPSR_Ref TObjectID="6192453739282434"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1026.28,919.111) scale(1,1) translate(0,0)" width="12" x="1020.277777777778" y="904.1111149258084"/></g>
  <g id="80">
   <use class="kv10" height="30" transform="rotate(180,1592.28,927.111) scale(1,1) translate(0,0)" width="12" x="1586.277777777778" xlink:href="#EnergyConsumer:负荷_0" y="912.1111149258084" zvalue="1414"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453740068866" ObjectName="10kV平蛮线"/>
   <cge:TPSR_Ref TObjectID="6192453740068866"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1592.28,927.111) scale(1,1) translate(0,0)" width="12" x="1586.277777777778" y="912.1111149258084"/></g>
  <g id="155">
   <use class="kv10" height="30" transform="rotate(180,1206.28,919.111) scale(1,1) translate(0,0)" width="12" x="1200.277777777778" xlink:href="#EnergyConsumer:负荷_0" y="904.1111149258084" zvalue="1433"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453740462082" ObjectName="10kV备用线"/>
   <cge:TPSR_Ref TObjectID="6192453740462082"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1206.28,919.111) scale(1,1) translate(0,0)" width="12" x="1200.277777777778" y="904.1111149258084"/></g>
  <g id="90">
   <use class="kv35" height="30" transform="rotate(0,1278.18,443.75) scale(1.87021,1.88333) translate(-582.556,-194.881)" width="28" x="1252" xlink:href="#EnergyConsumer:站用变DY接地_0" y="415.5" zvalue="1500"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453742297090" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1278.18,443.75) scale(1.87021,1.88333) translate(-582.556,-194.881)" width="28" x="1252" y="415.5"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="439">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="439" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,850.278,963.611) scale(1,1) translate(-1.78808e-13,0)" writing-mode="lr" x="850.47" xml:space="preserve" y="968.52" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132842876931" ObjectName="P"/>
   </metadata>
  </g>
  <g id="447">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="447" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,850.278,986.611) scale(1,1) translate(-1.78808e-13,0)" writing-mode="lr" x="850.47" xml:space="preserve" y="991.52" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132842942467" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="455">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="455" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,850.278,1009.61) scale(1,1) translate(-1.78808e-13,0)" writing-mode="lr" x="850.47" xml:space="preserve" y="1014.52" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132843008003" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="594">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="594" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1103.38,13.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1103.57" xml:space="preserve" y="18.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132846809091" ObjectName="P"/>
   </metadata>
  </g>
  <g id="597">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="597" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1103.38,36.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1103.57" xml:space="preserve" y="41.41" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132846874628" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="600">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="600" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1103.38,59.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1103.57" xml:space="preserve" y="64.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132846940164" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="208">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="208" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1026.28,963.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1026.47" xml:space="preserve" y="968.52" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132848316419" ObjectName="P"/>
   </metadata>
  </g>
  <g id="209">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="209" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1409.28,963.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1409.47" xml:space="preserve" y="968.52" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132849758211" ObjectName="P"/>
   </metadata>
  </g>
  <g id="210">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="210" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1592.28,963.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1592.47" xml:space="preserve" y="968.52" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132851200003" ObjectName="P"/>
   </metadata>
  </g>
  <g id="211">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="211" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1206.28,963.611) scale(1,1) translate(3.86783e-13,0)" writing-mode="lr" x="1206.47" xml:space="preserve" y="968.52" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132852641795" ObjectName="P"/>
   </metadata>
  </g>
  <g id="212">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="212" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1026.28,986.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1026.47" xml:space="preserve" y="991.52" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132848381955" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="213">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="213" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1409.28,986.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1409.47" xml:space="preserve" y="991.52" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132849823747" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="214">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="214" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1592.28,986.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1592.47" xml:space="preserve" y="991.52" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132851265540" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="215">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="215" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1206.28,986.611) scale(1,1) translate(3.86783e-13,0)" writing-mode="lr" x="1206.47" xml:space="preserve" y="991.52" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132852707331" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="216">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="216" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1026.28,1009.61) scale(1,1) translate(0,0)" writing-mode="lr" x="1026.47" xml:space="preserve" y="1014.52" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132848447491" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="217">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="217" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1409.28,1009.61) scale(1,1) translate(0,0)" writing-mode="lr" x="1409.47" xml:space="preserve" y="1014.52" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132849889284" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="218">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="218" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1592.28,1009.61) scale(1,1) translate(0,0)" writing-mode="lr" x="1592.47" xml:space="preserve" y="1014.52" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132851331076" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="219">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="219" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1206.28,1009.61) scale(1,1) translate(3.86783e-13,0)" writing-mode="lr" x="1206.47" xml:space="preserve" y="1014.52" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132852772867" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="227">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="227" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,655.616,982.5) scale(1,1) translate(0,0)" writing-mode="lr" x="655.8099999999999" xml:space="preserve" y="987.41" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132854345731" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="228">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="228" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,655.616,1005.5) scale(1,1) translate(0,0)" writing-mode="lr" x="655.8099999999999" xml:space="preserve" y="1010.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132854411267" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="229">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="229" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,500,981.5) scale(1,1) translate(0,0)" writing-mode="lr" x="500.2" xml:space="preserve" y="986.41" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132854083587" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="230">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="230" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,500,1004.5) scale(1,1) translate(0,0)" writing-mode="lr" x="500.2" xml:space="preserve" y="1009.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132854149123" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="7" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,974.131,399.35) scale(1,1) translate(0,0)" writing-mode="lr" x="974.37" xml:space="preserve" y="405.84" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132841172996" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="8" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,974.131,426.35) scale(1,1) translate(0,0)" writing-mode="lr" x="974.37" xml:space="preserve" y="432.84" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132841238532" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="38" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,974.131,453.35) scale(1,1) translate(0,0)" writing-mode="lr" x="974.37" xml:space="preserve" y="459.84" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132841435140" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="39" prefix="档位:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,972.03,483.925) scale(1,1) translate(0,0)" writing-mode="lr" x="972.23" xml:space="preserve" y="490.41" zvalue="1">档位:ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132841697284" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="41">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="41" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1184.13,580.5) scale(1,1) translate(-2.50607e-13,0)" writing-mode="lr" x="1184.37" xml:space="preserve" y="586.99" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132841304068" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="42">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="42" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1184.13,607.5) scale(1,1) translate(-2.50607e-13,0)" writing-mode="lr" x="1184.37" xml:space="preserve" y="613.99" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132841369604" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="43">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="43" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1185.13,633.5) scale(1,1) translate(-2.50829e-13,0)" writing-mode="lr" x="1185.37" xml:space="preserve" y="639.99" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132841762819" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="412">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="412" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,139.5,500.754) scale(1,1) translate(1.22125e-14,1.08858e-13)" writing-mode="lr" x="139.63" xml:space="preserve" y="505.66" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132839600132" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="411">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="411" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,139.5,523.754) scale(1,1) translate(1.22125e-14,0)" writing-mode="lr" x="139.63" xml:space="preserve" y="528.66" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132839665668" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="410">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="410" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,139.5,549.879) scale(1,1) translate(1.22125e-14,0)" writing-mode="lr" x="139.63" xml:space="preserve" y="554.79" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132839731203" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="409">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="409" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,139.5,476.5) scale(1,1) translate(0,0)" writing-mode="lr" x="139.63" xml:space="preserve" y="481.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132839862275" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="408">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="408" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,143,180.615) scale(1,1) translate(0,0)" writing-mode="lr" x="143.15" xml:space="preserve" y="187.12" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132839993348" ObjectName="F"/>
   </metadata>
  </g>
  <g id="407">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="407" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,499.879) scale(1,1) translate(2.52622e-14,1.08664e-13)" writing-mode="lr" x="257.17" xml:space="preserve" y="504.79" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132840124419" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="406">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="406" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,257.042,524.504) scale(1,1) translate(2.52622e-14,0)" writing-mode="lr" x="257.17" xml:space="preserve" y="529.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132840189956" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="405">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="405" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,257.042,549.879) scale(1,1) translate(2.52622e-14,0)" writing-mode="lr" x="257.17" xml:space="preserve" y="554.79" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132840255492" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="404">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="404" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,257.042,475.254) scale(1,1) translate(2.52622e-14,1.03196e-13)" writing-mode="lr" x="257.17" xml:space="preserve" y="480.16" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132840386564" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="403">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="403" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,143,206.504) scale(1,1) translate(0,0)" writing-mode="lr" x="143.15" xml:space="preserve" y="213.01" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132840517636" ObjectName="F"/>
   </metadata>
  </g>
  <g id="402">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="402" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,316,500.129) scale(1,1) translate(3.18079e-14,1.08719e-13)" writing-mode="lr" x="316.13" xml:space="preserve" y="505.04" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132840648708" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="401">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="401" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,316,525.004) scale(1,1) translate(3.18079e-14,0)" writing-mode="lr" x="316.13" xml:space="preserve" y="529.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132840714244" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="400">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="400" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,316,549.879) scale(1,1) translate(3.18079e-14,0)" writing-mode="lr" x="316.13" xml:space="preserve" y="554.79" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132840779780" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="399">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="399" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,321,206.754) scale(1,1) translate(0,0)" writing-mode="lr" x="321.15" xml:space="preserve" y="213.26" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132841041924" ObjectName="F"/>
   </metadata>
  </g>
  <g id="398">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="398" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,316,475.254) scale(1,1) translate(3.18079e-14,1.03196e-13)" writing-mode="lr" x="316.13" xml:space="preserve" y="480.16" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132840910852" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="397">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="397" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,143,228.698) scale(1,1) translate(0,0)" writing-mode="lr" x="143.15" xml:space="preserve" y="235.21" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132841631748" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="396">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="396" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,143,251.698) scale(1,1) translate(0,3.71011e-13)" writing-mode="lr" x="143.15" xml:space="preserve" y="258.21" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132841697284" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="393">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="393" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,140.5,574.754) scale(1,1) translate(1.23235e-14,1.25289e-13)" writing-mode="lr" x="140.63" xml:space="preserve" y="579.66" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132840058884" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="392">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="392" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,257.042,574.754) scale(1,1) translate(2.52622e-14,1.25289e-13)" writing-mode="lr" x="257.17" xml:space="preserve" y="579.66" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132840583172" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="391">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="391" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,316,574.754) scale(1,1) translate(3.18079e-14,1.25289e-13)" writing-mode="lr" x="316.13" xml:space="preserve" y="579.66" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132841107460" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="390">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="390" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,143,157) scale(1,1) translate(0,0)" writing-mode="lr" x="143.15" xml:space="preserve" y="163.51" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132855721987" ObjectName=""/>
   </metadata>
  </g>
  <g id="389">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="389" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,320,157.004) scale(1,1) translate(0,0)" writing-mode="lr" x="320.15" xml:space="preserve" y="163.51" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132855787523" ObjectName=""/>
   </metadata>
  </g>
  <g id="242">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="242" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,834.378,18.1878) scale(1,1) translate(-1.75277e-13,-2.04843e-14)" writing-mode="lr" x="833.91" xml:space="preserve" y="22.85" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127281033220" ObjectName="P"/>
   </metadata>
  </g>
  <g id="243">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="243" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,835.378,39.1878) scale(1,1) translate(-1.75499e-13,0)" writing-mode="lr" x="834.91" xml:space="preserve" y="43.85" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127281098756" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="244" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,835.378,59.1878) scale(1,1) translate(-1.75499e-13,0)" writing-mode="lr" x="834.91" xml:space="preserve" y="63.85" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127281164292" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="226">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="226" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1295.5,136.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1295.63" xml:space="preserve" y="141.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132839862275" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="254">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="254" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,655.5,508.5) scale(1,1) translate(6.95e-14,-1.10578e-13)" writing-mode="lr" x="655.63" xml:space="preserve" y="513.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132840386564" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="256" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1730.5,506.5) scale(1,1) translate(1.88849e-13,1.10134e-13)" writing-mode="lr" x="1730.63" xml:space="preserve" y="511.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132840910852" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="499">
   <use class="kv35" height="30" transform="rotate(0,1103.38,107.5) scale(1.98323,1.3) translate(-543.582,-20.3077)" width="7" x="1096.436704179698" xlink:href="#ACLineSegment:线路_0" y="88.00000001157878" zvalue="1124"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249320914948" ObjectName="35kV河平线"/>
   <cge:TPSR_Ref TObjectID="8444249320914948_5066549678571521"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1103.38,107.5) scale(1.98323,1.3) translate(-543.582,-20.3077)" width="7" x="1096.436704179698" y="88.00000001157878"/></g>
  <g id="240">
   <use class="kv35" height="30" transform="rotate(0,835.378,111.188) scale(1.98323,1.3) translate(-410.715,-21.1587)" width="7" x="828.4367041796982" xlink:href="#ACLineSegment:线路_0" y="91.68776459659688" zvalue="1818"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249322225669" ObjectName="35kV厂山线"/>
   <cge:TPSR_Ref TObjectID="8444249322225669_5066549678571521"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,835.378,111.188) scale(1.98323,1.3) translate(-410.715,-21.1587)" width="7" x="828.4367041796982" y="91.68776459659688"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="262">
   <use class="kv10" height="40" transform="rotate(0,500,904.333) scale(2.08333,2.08333) translate(-247,-448.587)" width="24" x="475" xlink:href="#Compensator:10kV电容器_0" y="862.6666666666667" zvalue="1458"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453741117442" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192453741117442"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,500,904.333) scale(2.08333,2.08333) translate(-247,-448.587)" width="24" x="475" y="862.6666666666667"/></g>
  <g id="275">
   <use class="kv10" height="40" transform="rotate(0,655.616,905.833) scale(2.08333,2.10833) translate(-327.92,-454.022)" width="24" x="630.6156963132455" xlink:href="#Compensator:10kV电容器_0" y="863.6666666666669" zvalue="1478"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453741182978" ObjectName="10kV2号电容器"/>
   <cge:TPSR_Ref TObjectID="6192453741182978"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,655.616,905.833) scale(2.08333,2.10833) translate(-327.92,-454.022)" width="24" x="630.6156963132455" y="863.6666666666669"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,348.625,300.004) scale(0.708333,0.665547) translate(139.176,145.742)" width="30" x="338" xlink:href="#State:红绿圆(方形)_0" y="290.02" zvalue="1756"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374921695233" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,348.625,300.004) scale(0.708333,0.665547) translate(139.176,145.742)" width="30" x="338" y="290.02"/></g>
  <g id="1035">
   <use height="30" transform="rotate(0,253,300.004) scale(0.708333,0.665547) translate(99.8015,145.742)" width="30" x="242.37" xlink:href="#State:红绿圆(方形)_0" y="290.02" zvalue="1757"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562958612758532" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,253,300.004) scale(0.708333,0.665547) translate(99.8015,145.742)" width="30" x="242.37" y="290.02"/></g>
  <g id="772">
   <use height="30" transform="rotate(0,302.235,383) scale(0.910937,0.8) translate(25.9871,92.75)" width="80" x="265.8" xlink:href="#State:间隔模板_0" y="371" zvalue="1862"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629499720990723" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,302.235,383) scale(0.910937,0.8) translate(25.9871,92.75)" width="80" x="265.8" y="371"/></g>
  <g id="1082">
   <use height="30" transform="rotate(0,107.286,301.539) scale(0.910937,0.8) translate(6.92684,72.3847)" width="80" x="70.84999999999999" xlink:href="#State:间隔模板_0" y="289.54" zvalue="1863"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629500333555714" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,107.286,301.539) scale(0.910937,0.8) translate(6.92684,72.3847)" width="80" x="70.84999999999999" y="289.54"/></g>
 </g>
</svg>