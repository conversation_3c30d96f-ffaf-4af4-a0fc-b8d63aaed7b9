<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549582888962" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="Accessory:10kV母线PT带消谐装置_0" viewBox="0,0,35,30">
   <use terminal-index="0" type="0" x="17.56245852479325" xlink:href="#terminal" y="28.12373692455963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.61245852479333" x2="34.61245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.86245852479331" x2="30.86245852479331" y1="22.68299618381883" y2="19.84040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.6124585247933" x2="26.6124585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.61245852479332" x2="34.61245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666667" x2="31" y1="22.69225544307809" y2="22.69225544307809"/>
   <rect fill-opacity="0" height="3.55" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,30.81,15.38) scale(1,1) translate(0,0)" width="8.92" x="26.34" y="13.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.647721108666" x2="30.647721108666" y1="10.83674821859629" y2="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.71438777533266" x2="28.71438777533266" y1="7.51612768565195" y2="7.51612768565195"/>
   <ellipse cx="14.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.05048569403981" x2="22.05048569403981" y1="34.53084700683308" y2="34.53084700683308"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.7608382776216" x2="28.48402243293775" y1="7.754208141873304" y2="7.754208141873304"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.01083827762157" x2="29.40068909960439" y1="6.50420814187332" y2="6.50420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.51083827762159" x2="30.06735576627107" y1="5.004208141873296" y2="5.004208141873296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.89410730945214" x2="23.0877735452272" y1="24.15299989806297" y2="21.53208583485582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58713830216066" x2="22.81883560169719" y1="21.54040359122622" y2="21.54040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51457106407813" x2="19.32090482830307" y1="24.05654513693459" y2="21.43563107372743"/>
   <ellipse cx="14.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.3644003886642" x2="22.3644003886642" y1="30.19213090500035" y2="30.19213090500035"/>
   <ellipse cx="21.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.19220382559271" x2="17.19220382559271" y1="23.07076893362116" y2="23.07076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.4422038255927" x2="16.4422038255927" y1="16.82076893362115" y2="16.82076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.92264294445647" x2="20.87303257583197" y1="16.99992879670395" y2="15.47611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.873032575832" x2="20.37063985073817" y1="15.47611741162252" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.87303257583199" x2="23.32581493230132" y1="15.47611741162254" y2="16.60543752773795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.94220382559271" x2="16.94220382559271" y1="16.32076893362115" y2="16.32076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="16.07581493230131" y1="22.72611741162255" y2="23.85543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="13.12063985073816" y1="22.72611741162255" y2="20.07298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583197" y1="24.24992879670398" y2="22.72611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="16.07581493230131" y1="15.47611741162254" y2="16.60543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="13.12063985073816" y1="15.47611741162253" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583196" y1="16.99992879670396" y2="15.47611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.59103363843511" x2="17.59103363843511" y1="25.15822158129307" y2="28.16666666666667"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_0" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="3.333333333333332" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="24.08333333333333" y2="29.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="7" y1="19.5" y2="24.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_1" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="18.25" y2="32.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_2" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="4" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="10" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="25.83333333333333" y2="32.41666666666667"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Coil:立新光伏_0" viewBox="0,0,25,40">
   <use terminal-index="0" type="0" x="14.75" xlink:href="#terminal" y="0.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5" x2="20.5" y1="4" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.666666666666664" x2="1.666666666666664" y1="15.58333333333333" y2="15.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.5" x2="20.5" y1="4" y2="4"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.666666666666664" x2="1.666666666666664" y1="24.91666666666666" y2="24.91666666666666"/>
   <rect fill-opacity="0" height="12" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20.33,15.42) scale(1,1) translate(0,0)" width="5" x="17.83" y="9.42"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.33333333333334" x2="20.33333333333334" y1="21.33333333333333" y2="23.33333333333333"/>
   <path d="M 19.5 13 L 20.5 16 L 21.5 13" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="7" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="14.75" y1="34.75" y2="0.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.26666666666667" x2="16.95" y1="36.66302242688344" y2="36.66302242688344"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.38333333333333" x2="15.81666666666667" y1="38.5686533568213" y2="38.5686533568213"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.15" x2="18.83333333333334" y1="34.67405816361222" y2="34.67405816361222"/>
   <path d="M 2.66667 21.7583 A 1.62917 2 270 0 0 2.66667 18.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7" x2="7" y1="24.83333333333334" y2="31.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.516666666666668" x2="9.200000000000003" y1="33.74635576021678" y2="33.74635576021678"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.633333333333333" x2="8.066666666666666" y1="35.65198669015464" y2="35.65198669015464"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.149999999999997" x2="10.83333333333333" y1="31.75739149694556" y2="31.75739149694556"/>
   <path d="M 2.75 18.4792 A 1.44792 1.91667 270 0 0 2.75 15.5833" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 2.66667 24.925 A 1.5875 2 270 0 0 2.66667 21.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 7.16667 21.8417 A 1.62917 2 270 0 1 7.16667 18.5833" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 7.08333 18.5625 A 1.44792 1.91667 270 0 1 7.08333 15.6667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 7.16667 25.0083 A 1.5875 2 270 0 1 7.16667 21.8333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.68333333333333" x2="22.36666666666666" y1="25.24635576021677" y2="25.24635576021677"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="18.8" x2="21.23333333333333" y1="27.15198669015464" y2="27.15198669015464"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.31666666666667" x2="24" y1="23.25739149694555" y2="23.25739149694555"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变13_0" viewBox="0,0,32,35">
   <use terminal-index="0" type="0" x="16" xlink:href="#terminal" y="3.5"/>
   <ellipse cx="16.04" cy="11.97" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.97" cy="24.14" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067018" x2="16.03525619067018" y1="20.85" y2="24.95894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067017" x2="10.66666666666667" y1="24.98394833233988" y2="28.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.06302903072498" x2="21.16666666666667" y1="24.93990500916851" y2="28.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067018" x2="16.03525619067018" y1="7.6" y2="11.70894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067017" x2="12.05" y1="11.73394833233987" y2="14.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.06302903072498" x2="20.15" y1="11.68990500916851" y2="14.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":光伏发电2_0" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="29.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.000411522633746" x2="5.000411522633746" y1="29.91666666666667" y2="1.116666666666665"/>
   <path d="M 4.91667 14.5224 L 3.5 7.60569 L 5 11.1057 L 6.5 7.43903 L 4.95238 14.739" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷20230624_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="1.416666666666663" y2="28.48902606310013"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV立新光伏电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="48" xlink:href="logo.png" y="43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,176,73) scale(1,1) translate(0,0)" writing-mode="lr" x="176" xml:space="preserve" y="76.5" zvalue="54"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,176.5,72.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="176.5" xml:space="preserve" y="81.69" zvalue="55">35kV立新光伏电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="54" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,69.625,305.75) scale(1,1) translate(0,0)" width="72.88" x="33.19" y="293.75" zvalue="1886"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.625,305.75) scale(1,1) translate(0,0)" writing-mode="lr" x="69.63" xml:space="preserve" y="310.25" zvalue="1886">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,756.841,491.032) scale(1,1) translate(0,0)" writing-mode="lr" x="756.84" xml:space="preserve" y="495.53" zvalue="7">35kV母线</text>
  <line fill="none" id="67" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382" x2="382" y1="11" y2="1041" zvalue="58"/>
  <line fill="none" id="666" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="146.8704926140824" y2="146.8704926140824" zvalue="1056"/>
  <line fill="none" id="613" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="616.8704926140824" y2="616.8704926140824" zvalue="1058"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="999.0816" y2="1027"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="609" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,952) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="958" zvalue="1062">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="608" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50,986) scale(1,1) translate(0,0)" writing-mode="lr" x="50" xml:space="preserve" y="992" zvalue="1063">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="607" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232,986) scale(1,1) translate(0,0)" writing-mode="lr" x="232" xml:space="preserve" y="992" zvalue="1064">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="606" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="49" xml:space="preserve" y="1020" zvalue="1065">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="605" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="231" xml:space="preserve" y="1020" zvalue="1066">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="602" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.5,646.5) scale(1,1) translate(0,0)" writing-mode="lr" x="73.5" xml:space="preserve" y="651" zvalue="1069">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.054,954) scale(1,1) translate(0,0)" writing-mode="lr" x="233.05" xml:space="preserve" y="960" zvalue="1080">LiXin-01-2020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,143.054,986) scale(1,1) translate(0,0)" writing-mode="lr" x="143.05" xml:space="preserve" y="992" zvalue="1081">段勇</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,323.054,986) scale(1,1) translate(0,0)" writing-mode="lr" x="323.05" xml:space="preserve" y="992" zvalue="1082">20201106</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1084.4,441.508) scale(1,1) translate(0,0)" writing-mode="lr" x="1084.4" xml:space="preserve" y="446.01" zvalue="1715">333</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1115.68,165.365) scale(1,1) translate(0,0)" writing-mode="lr" x="1115.68" xml:space="preserve" y="169.87" zvalue="1716">35kV混新线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1097.97,280.079) scale(1,1) translate(0,0)" writing-mode="lr" x="1097.97" xml:space="preserve" y="284.58" zvalue="1721">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1159.83,224.365) scale(1,1) translate(0,0)" writing-mode="lr" x="1159.83" xml:space="preserve" y="228.87" zvalue="1724">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,911.45,567.857) scale(1,1) translate(0,0)" writing-mode="lr" x="911.45" xml:space="preserve" y="572.36" zvalue="1761">332</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,899.19,643.286) scale(1,1) translate(0,0)" writing-mode="lr" x="899.1900000000001" xml:space="preserve" y="647.79" zvalue="1766">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,942.736,859.857) scale(1,1) translate(0,0)" writing-mode="lr" x="942.74" xml:space="preserve" y="864.36" zvalue="1770">35kV光伏Ⅱ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1097.59,565.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1097.59" xml:space="preserve" y="570.39" zvalue="1773">334</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1085.33,641.317) scale(1,1) translate(0,0)" writing-mode="lr" x="1085.33" xml:space="preserve" y="645.8200000000001" zvalue="1778">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1276.82,564.206) scale(1,1) translate(0,0)" writing-mode="lr" x="1276.82" xml:space="preserve" y="568.71" zvalue="1786">335</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1264.56,639.635) scale(1,1) translate(0,0)" writing-mode="lr" x="1264.56" xml:space="preserve" y="644.13" zvalue="1790">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1319.2,853.015) scale(1,1) translate(0,0)" writing-mode="lr" x="1319.2" xml:space="preserve" y="857.51" zvalue="1795">35kV接地兼站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="230" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1381.25,730.492) scale(1,1) translate(0,0)" writing-mode="lr" x="1381.25" xml:space="preserve" y="734.99" zvalue="1810">3350</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1074.56,328.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1074.56" xml:space="preserve" y="332.64" zvalue="1817">60</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1492.67,728.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1492.67" xml:space="preserve" y="733.0599999999999" zvalue="1820">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1460.44,565) scale(1,1) translate(0,0)" writing-mode="lr" x="1460.44" xml:space="preserve" y="569.5" zvalue="1821">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,730.45,567.857) scale(1,1) translate(0,0)" writing-mode="lr" x="730.45" xml:space="preserve" y="572.36" zvalue="1829">331</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,718.19,643.286) scale(1,1) translate(0,0)" writing-mode="lr" x="718.1900000000001" xml:space="preserve" y="647.79" zvalue="1833">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,761.736,859.857) scale(1,1) translate(0,0)" writing-mode="lr" x="761.74" xml:space="preserve" y="864.36" zvalue="1836">35kV光伏Ⅰ回线</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="150" y2="150"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="176" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="1.678571428571331" y1="150" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="150" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="150" y2="150"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="176" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="150" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.6785714285713" x2="363.6785714285713" y1="150" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="176" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="199.5" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="1.678571428571331" y1="176" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="176" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="176" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="199.5" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="176" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.6785714285713" x2="363.6785714285713" y1="176" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="199.5" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="222.25" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="1.678571428571331" y1="199.5" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="199.5" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="199.5" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="222.25" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="199.5" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.6785714285713" x2="363.6785714285713" y1="199.5" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="222.25" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="245" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="1.678571428571331" y1="222.25" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="222.25" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="222.25" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="245" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="222.25" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.6785714285713" x2="363.6785714285713" y1="222.25" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="245" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="267.75" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="1.678571428571331" y1="245" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="245" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="245" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="267.75" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="245" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.6785714285713" x2="363.6785714285713" y1="245" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="430.9166435058594" y2="430.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="468.4066435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="430.9166435058594" y2="430.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="468.4066435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="430.9166435058594" y2="430.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="468.4066435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="430.9166435058594" y2="430.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="468.4066435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="430.9166435058594" y2="430.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="468.4066435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="468.4067435058594" y2="468.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="468.4067435058594" y2="468.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="468.4067435058594" y2="468.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="468.4067435058594" y2="468.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="468.4067435058594" y2="468.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="516.7439435058594" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="516.7439435058594" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="516.7439435058594" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="516.7439435058594" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="516.7439435058594" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="516.7439835058594" y2="516.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="540.9125835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="516.7439835058594" y2="516.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="540.9125835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="516.7439835058594" y2="516.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="540.9125835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="516.7439835058594" y2="516.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="540.9125835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="516.7439835058594" y2="516.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="540.9125835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="540.9127435058595" y2="540.9127435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="540.9127435058595" y2="540.9127435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="540.9127435058595" y2="540.9127435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="540.9127435058595" y2="540.9127435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="540.9127435058595" y2="540.9127435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="589.2499435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="589.2499435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="589.2499435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="589.2499435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="589.2499435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="565.0813435058594" y2="589.2499435058594"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,157.827,303.841) scale(1,1) translate(0,0)" writing-mode="lr" x="157.83" xml:space="preserve" y="308.34" zvalue="1873">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,262.827,303.841) scale(1,1) translate(0,0)" writing-mode="lr" x="262.83" xml:space="preserve" y="308.34" zvalue="1874">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,174.595,451.75) scale(1,1) translate(0,0)" writing-mode="lr" x="174.5952380952378" xml:space="preserve" y="456.2500000000001" zvalue="1875">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.4286,479.5) scale(1,1) translate(0,0)" writing-mode="lr" x="56.42857142857133" xml:space="preserve" y="484.0000000000001" zvalue="1877">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.4286,505) scale(1,1) translate(0,0)" writing-mode="lr" x="56.42857142857133" xml:space="preserve" y="509.5" zvalue="1878">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.4286,530.5) scale(1,1) translate(0,0)" writing-mode="lr" x="56.42857142857133" xml:space="preserve" y="535" zvalue="1879">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.4286,555) scale(1,1) translate(0,0)" writing-mode="lr" x="55.42857142857133" xml:space="preserve" y="559.5" zvalue="1880">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.4286,581.5) scale(1,1) translate(0,0)" writing-mode="lr" x="56.42857142857133" xml:space="preserve" y="586" zvalue="1881">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,38.4286,163) scale(1,1) translate(0,0)" writing-mode="lr" x="38.43" xml:space="preserve" y="168.5" zvalue="1882">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,218.429,163) scale(1,1) translate(0,0)" writing-mode="lr" x="218.43" xml:space="preserve" y="168.5" zvalue="1883">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.5536,188.5) scale(1,1) translate(0,0)" writing-mode="lr" x="55.55" xml:space="preserve" y="193" zvalue="1884">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1137.71,864.105) scale(1,1) translate(0,0)" writing-mode="lr" x="1137.71" xml:space="preserve" y="868.61" zvalue="1921">35kVSVG</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1363.5,920.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1363.5" xml:space="preserve" y="925" zvalue="1927">容量：12.63MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1135,877.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1135" xml:space="preserve" y="881.75" zvalue="1928">±3Mvar</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="33.19" y="293.75" zvalue="1886"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="6">
   <path class="kv35" d="M 673 510.81 L 1593.62 510.81" stroke-width="4" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674236628997" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674236628997"/></metadata>
  <path d="M 673 510.81 L 1593.62 510.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="236">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,181.222,511.167) scale(1,1) translate(-3.42689e-14,0)" writing-mode="lr" x="181.34" xml:space="preserve" y="516.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124491952132" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="237">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="237" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,181.222,533.667) scale(1,1) translate(0,0)" writing-mode="lr" x="181.34" xml:space="preserve" y="538.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124492017668" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="238">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="238" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,181.222,558.167) scale(1,1) translate(-3.42689e-14,0)" writing-mode="lr" x="181.34" xml:space="preserve" y="563.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124492083204" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="235">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="235" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,181.222,485.667) scale(1,1) translate(-3.42689e-14,0)" writing-mode="lr" x="181.34" xml:space="preserve" y="490.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124492214276" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="4" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,679,529.81) scale(1,1) translate(0,0)" writing-mode="lr" x="678.53" xml:space="preserve" y="534.59" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124492148740" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="8" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1114.68,89.0247) scale(1,1) translate(0,-1.21138e-13)" writing-mode="lr" x="1114.21" xml:space="preserve" y="93.7" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124493000708" ObjectName="P"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="17" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1114.68,112.468) scale(1,1) translate(0,-1.57576e-13)" writing-mode="lr" x="1114.21" xml:space="preserve" y="117.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124493066244" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1114.68,135.912) scale(1,1) translate(0,-1.94015e-13)" writing-mode="lr" x="1114.21" xml:space="preserve" y="140.59" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124493131780" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="24">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="24" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,936.164,882.153) scale(1,1) translate(0,9.67163e-14)" writing-mode="lr" x="935.7" xml:space="preserve" y="886.83" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124494573572" ObjectName="P"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="31" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,755.164,884.153) scale(1,1) translate(0,1.64795e-12)" writing-mode="lr" x="754.7" xml:space="preserve" y="888.83" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124498571268" ObjectName="P"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="32" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,936.164,907.432) scale(1,1) translate(0,1.69189e-12)" writing-mode="lr" x="935.7" xml:space="preserve" y="912.11" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124494639108" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="35" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,755.164,907.432) scale(1,1) translate(0,1.69189e-12)" writing-mode="lr" x="754.7" xml:space="preserve" y="912.11" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124498636804" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="36">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="36" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,936.164,930.711) scale(1,1) translate(0,1.73582e-12)" writing-mode="lr" x="935.7" xml:space="preserve" y="935.39" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124494704644" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="52" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,755.164,930.711) scale(1,1) translate(0,1.73582e-12)" writing-mode="lr" x="754.7" xml:space="preserve" y="935.39" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124498702340" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="53" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,109,162) scale(1,1) translate(0,0)" writing-mode="lr" x="109.15" xml:space="preserve" y="168.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124500602884" ObjectName="GEN_PSUM"/>
   </metadata>
  </g>
  <g id="1035">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="1035" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,292,161) scale(1,1) translate(0,0)" writing-mode="lr" x="292.15" xml:space="preserve" y="167.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124500668420" ObjectName="GEN_QSUM"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,140,187.71) scale(1,1) translate(0,0)" writing-mode="lr" x="139.54" xml:space="preserve" y="192.49" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124492345348" ObjectName="F"/>
   </metadata>
  </g>
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="101" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1130.71,895.105) scale(1,1) translate(0,0)" writing-mode="lr" x="1130.35" xml:space="preserve" y="899.8099999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124495556612" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="102" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1130.71,923.105) scale(1,1) translate(0,0)" writing-mode="lr" x="1130.35" xml:space="preserve" y="927.8099999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124495622148" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="BreakerClass">
  <g id="12">
   <use class="kv35" height="20" transform="rotate(0,1115.68,442.508) scale(3.14286,3.14286) translate(-749.978,-280.281)" width="10" x="1099.968253968254" xlink:href="#Breaker:小车断路器_0" y="411.0793650793651" zvalue="1714"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924484923397" ObjectName="35kV混新线333断路器"/>
   <cge:TPSR_Ref TObjectID="6473924484923397"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1115.68,442.508) scale(3.14286,3.14286) translate(-749.978,-280.281)" width="10" x="1099.968253968254" y="411.0793650793651"/></g>
  <g id="146">
   <use class="kv35" height="20" transform="rotate(0,944.164,568.857) scale(3.14286,3.14286) translate(-633.034,-366.429)" width="10" x="928.4499984423206" xlink:href="#Breaker:小车断路器_0" y="537.4285714285714" zvalue="1760"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924484988933" ObjectName="35kV光伏Ⅱ回线332断路器"/>
   <cge:TPSR_Ref TObjectID="6473924484988933"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,944.164,568.857) scale(3.14286,3.14286) translate(-633.034,-366.429)" width="10" x="928.4499984423206" y="537.4285714285714"/></g>
  <g id="164">
   <use class="kv35" height="20" transform="rotate(0,1130.31,566.889) scale(3.14286,3.14286) translate(-759.95,-365.087)" width="10" x="1114.592855585178" xlink:href="#Breaker:小车断路器_0" y="535.4603174603175" zvalue="1772"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924485054469" ObjectName="35kVSVG334断路器"/>
   <cge:TPSR_Ref TObjectID="6473924485054469"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1130.31,566.889) scale(3.14286,3.14286) translate(-759.95,-365.087)" width="10" x="1114.592855585178" y="535.4603174603175"/></g>
  <g id="183">
   <use class="kv35" height="20" transform="rotate(0,1309.53,565.206) scale(3.14286,3.14286) translate(-882.147,-363.939)" width="10" x="1293.8150778074" xlink:href="#Breaker:小车断路器_0" y="533.7777777777778" zvalue="1785"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924485120005" ObjectName="35kV接地兼站用变335断路器"/>
   <cge:TPSR_Ref TObjectID="6473924485120005"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1309.53,565.206) scale(3.14286,3.14286) translate(-882.147,-363.939)" width="10" x="1293.8150778074" y="533.7777777777778"/></g>
  <g id="30">
   <use class="kv35" height="20" transform="rotate(0,763.164,568.857) scale(3.14286,3.14286) translate(-509.625,-366.429)" width="10" x="747.4499984423206" xlink:href="#Breaker:小车断路器_0" y="537.4285714285714" zvalue="1828"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924485185541" ObjectName="35kV光伏Ⅰ回线331断路器"/>
   <cge:TPSR_Ref TObjectID="6473924485185541"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,763.164,568.857) scale(3.14286,3.14286) translate(-509.625,-366.429)" width="10" x="747.4499984423206" y="537.4285714285714"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="18">
   <use class="kv35" height="30" transform="rotate(0,1115.68,193.651) scale(2.65306,0.714286) translate(-689.37,73.1746)" width="7" x="1106.396825396825" xlink:href="#ACLineSegment:线路_0" y="182.9365079365079" zvalue="1715"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249309839366" ObjectName="35kV混新线"/>
   <cge:TPSR_Ref TObjectID="8444249309839366_5066549582888962"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1115.68,193.651) scale(2.65306,0.714286) translate(-689.37,73.1746)" width="7" x="1106.396825396825" y="182.9365079365079"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="39">
   <path class="kv35" d="M 1115.68 470.79 L 1115.68 510.81" stroke-width="1" zvalue="1717"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="12@1" LinkObjectIDznd="6@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1115.68 470.79 L 1115.68 510.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv35" d="M 1115.68 204.26 L 1115.68 265.88" stroke-width="1" zvalue="1721"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@0" LinkObjectIDznd="73@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1115.68 204.26 L 1115.68 265.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv35" d="M 1115.77 296.52 L 1115.68 413.44" stroke-width="1" zvalue="1722"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@1" LinkObjectIDznd="12@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1115.77 296.52 L 1115.68 413.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv35" d="M 1127.47 239.58 L 1115.68 239.58" stroke-width="1" zvalue="1724"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@0" LinkObjectIDznd="82" MaxPinNum="2"/>
   </metadata>
  <path d="M 1127.47 239.58 L 1115.68 239.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv35" d="M 1444.36 607.81 L 1488.82 607.81" stroke-width="1" zvalue="1740"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="15" MaxPinNum="2"/>
   </metadata>
  <path d="M 1444.36 607.81 L 1488.82 607.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv35" d="M 944.16 539.79 L 944.16 510.81" stroke-width="1" zvalue="1762"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="6@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 944.16 539.79 L 944.16 510.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv35" d="M 1130.31 537.82 L 1130.31 510.81" stroke-width="1" zvalue="1774"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@0" LinkObjectIDznd="6@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1130.31 537.82 L 1130.31 510.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv35" d="M 1309.53 536.13 L 1309.53 510.81" stroke-width="1" zvalue="1787"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@0" LinkObjectIDznd="6@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1309.53 536.13 L 1309.53 510.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv35" d="M 1488.82 646.45 L 1488.82 584.02" stroke-width="1" zvalue="1821"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="11@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1488.82 646.45 L 1488.82 584.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv35" d="M 1489.11 532.75 L 1489.11 510.81" stroke-width="1" zvalue="1822"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="11@1" LinkObjectIDznd="6@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1489.11 532.75 L 1489.11 510.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv35" d="M 1355.72 763.99 L 1355.72 748.6" stroke-width="1" zvalue="1826"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@0" LinkObjectIDznd="227@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1355.72 763.99 L 1355.72 748.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv35" d="M 763.16 539.79 L 763.16 510.81" stroke-width="1" zvalue="1830"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="30@0" LinkObjectIDznd="6@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 763.16 539.79 L 763.16 510.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv35" d="M 739.83 633.21 L 739.83 617 L 787.65 617 L 787.65 631.19" stroke-width="1" zvalue="1843"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="27@0" LinkObjectIDznd="28@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 739.83 633.21 L 739.83 617 L 787.65 617 L 787.65 631.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv35" d="M 763.16 597.14 L 763.16 617" stroke-width="1" zvalue="1844"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="30@1" LinkObjectIDznd="37" MaxPinNum="2"/>
   </metadata>
  <path d="M 763.16 597.14 L 763.16 617" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv35" d="M 763.16 617 L 763.16 797.5" stroke-width="1" zvalue="1845"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38" LinkObjectIDznd="25@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 763.16 617 L 763.16 797.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv35" d="M 920.83 633.21 L 920.83 619 L 968.65 619 L 968.65 631.19" stroke-width="1" zvalue="1846"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@0" LinkObjectIDznd="140@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 920.83 633.21 L 920.83 619 L 968.65 619 L 968.65 631.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv35" d="M 944.16 597.14 L 944.16 619" stroke-width="1" zvalue="1847"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@1" LinkObjectIDznd="41" MaxPinNum="2"/>
   </metadata>
  <path d="M 944.16 597.14 L 944.16 619" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv35" d="M 944.16 619 L 944.16 797.5" stroke-width="1" zvalue="1848"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42" LinkObjectIDznd="148@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 944.16 619 L 944.16 797.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv35" d="M 1286.2 629.56 L 1286.2 612 L 1334.02 612 L 1334.02 627.54" stroke-width="1" zvalue="1851"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="172@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1286.2 629.56 L 1286.2 612 L 1334.02 612 L 1334.02 627.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv35" d="M 1309.53 593.49 L 1309.53 612" stroke-width="1" zvalue="1852"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@1" LinkObjectIDznd="46" MaxPinNum="2"/>
   </metadata>
  <path d="M 1309.53 593.49 L 1309.53 612" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv35" d="M 1309.94 612 L 1309.94 685.09" stroke-width="1" zvalue="1853"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46" LinkObjectIDznd="191@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1309.94 612 L 1309.94 685.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv35" d="M 1092.1 343.07 L 1092.1 363 L 1115.72 363" stroke-width="1" zvalue="1854"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2@0" LinkObjectIDznd="85" MaxPinNum="2"/>
   </metadata>
  <path d="M 1092.1 343.07 L 1092.1 363 L 1115.72 363" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv35" d="M 1115.72 363 L 1139.92 363 L 1139.92 345.1" stroke-width="1" zvalue="1855"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85" LinkObjectIDznd="3@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1115.72 363 L 1139.92 363 L 1139.92 345.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv35" d="M 1309.94 685.09 L 1309.94 697 L 1355.76 697 L 1355.76 717.96" stroke-width="1" zvalue="1856"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48" LinkObjectIDznd="227@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1309.94 685.09 L 1309.94 697 L 1355.76 697 L 1355.76 717.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv35" d="M 1130.21 804.61 L 1130.21 595.17" stroke-width="1" zvalue="1917"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="164@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1130.21 804.61 L 1130.21 595.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv35" d="M 1106.98 631.25 L 1106.98 619 L 1130.21 619" stroke-width="1" zvalue="1918"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="26" MaxPinNum="2"/>
   </metadata>
  <path d="M 1106.98 631.25 L 1106.98 619 L 1130.21 619" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv35" d="M 1154.8 629.22 L 1154.8 620 L 1130.21 620" stroke-width="1" zvalue="1919"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160@0" LinkObjectIDznd="26" MaxPinNum="2"/>
   </metadata>
  <path d="M 1154.8 629.22 L 1154.8 620 L 1130.21 620" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="73">
   <use class="kv35" height="30" transform="rotate(0,1115.68,281.079) scale(1.42857,1.04762) translate(-331.49,-12.062)" width="15" x="1104.968253968254" xlink:href="#Disconnector:刀闸_0" y="265.3650793650794" zvalue="1720"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449645903878" ObjectName="35kV混新线3336隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449645903878"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1115.68,281.079) scale(1.42857,1.04762) translate(-331.49,-12.062)" width="15" x="1104.968253968254" y="265.3650793650794"/></g>
  <g id="227">
   <use class="kv35" height="30" transform="rotate(0,1355.63,733.159) scale(1.42857,1.04762) translate(-403.476,-32.6111)" width="15" x="1344.920634920635" xlink:href="#Disconnector:刀闸_0" y="717.4444444444445" zvalue="1809"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449647083526" ObjectName="35kV接地兼站用变3350隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449647083526"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1355.63,733.159) scale(1.42857,1.04762) translate(-403.476,-32.6111)" width="15" x="1344.920634920635" y="717.4444444444445"/></g>
  <g id="11">
   <use class="kv35" height="33" transform="rotate(0,1489.11,558.143) scale(1.5873,-1.5873) translate(-546.86,-900.082)" width="14" x="1478" xlink:href="#Disconnector:手车隔离开关13_0" y="531.9523809523814" zvalue="1820"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449647411206" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449647411206"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,1489.11,558.143) scale(1.5873,-1.5873) translate(-546.86,-900.082)" width="14" x="1478" y="531.9523809523814"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="87">
   <use class="kv35" height="20" transform="rotate(270,1141.4,239.651) scale(1.42857,1.42857) translate(-340.276,-67.6095)" width="10" x="1134.253968253968" xlink:href="#GroundDisconnector:地刀_0" y="225.3650793650793" zvalue="1723"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449646034950" ObjectName="35kV混新线33367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449646034950"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1141.4,239.651) scale(1.42857,1.42857) translate(-340.276,-67.6095)" width="10" x="1134.253968253968" y="225.3650793650793"/></g>
  <g id="139">
   <use class="kv35" height="20" transform="rotate(0,920.762,647.143) scale(1.42857,1.42857) translate(-274.086,-189.857)" width="10" x="913.6190476190475" xlink:href="#GroundDisconnector:地刀_0" y="632.8571428571428" zvalue="1765"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449646231558" ObjectName="35kV光伏Ⅱ回线33267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449646231558"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,920.762,647.143) scale(1.42857,1.42857) translate(-274.086,-189.857)" width="10" x="913.6190476190475" y="632.8571428571428"/></g>
  <g id="158">
   <use class="kv35" height="20" transform="rotate(0,1106.9,645.175) scale(1.42857,1.42857) translate(-329.929,-189.267)" width="10" x="1099.761904761905" xlink:href="#GroundDisconnector:地刀_0" y="630.8888888888888" zvalue="1777"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449646559238" ObjectName="35kVSVG33467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449646559238"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1106.9,645.175) scale(1.42857,1.42857) translate(-329.929,-189.267)" width="10" x="1099.761904761905" y="630.8888888888888"/></g>
  <g id="171">
   <use class="kv35" height="20" transform="rotate(0,1286.13,643.492) scale(1.42857,1.42857) translate(-383.695,-188.762)" width="10" x="1278.984126984127" xlink:href="#GroundDisconnector:地刀_0" y="629.2063492063492" zvalue="1789"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449646755846" ObjectName="35kV接地兼站用变33567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449646755846"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1286.13,643.492) scale(1.42857,1.42857) translate(-383.695,-188.762)" width="10" x="1278.984126984127" y="629.2063492063492"/></g>
  <g id="2">
   <use class="kv35" height="20" transform="rotate(0,1092.03,329.143) scale(1.42857,-1.42857) translate(-325.467,-555.257)" width="10" x="1084.888888888889" xlink:href="#GroundDisconnector:地刀_0" y="314.8571428571429" zvalue="1816"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449647214598" ObjectName="35kV混新线33360接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449647214598"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1092.03,329.143) scale(1.42857,-1.42857) translate(-325.467,-555.257)" width="10" x="1084.888888888889" y="314.8571428571429"/></g>
  <g id="27">
   <use class="kv35" height="20" transform="rotate(0,739.762,647.143) scale(1.42857,1.42857) translate(-219.786,-189.857)" width="10" x="732.6190476190474" xlink:href="#GroundDisconnector:地刀_0" y="632.8571428571428" zvalue="1832"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449647607813" ObjectName="35kV光伏Ⅰ回线33167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449647607813"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,739.762,647.143) scale(1.42857,1.42857) translate(-219.786,-189.857)" width="10" x="732.6190476190474" y="632.8571428571428"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="114">
   <use class="kv35" height="20" transform="rotate(0,1444.36,637.5) scale(2.36905,3.39286) translate(-820.988,-425.677)" width="20" x="1420.666666666667" xlink:href="#Accessory:线路PT3_0" y="603.5714285714286" zvalue="1739"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449646100486" ObjectName="电压互感器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1444.36,637.5) scale(2.36905,3.39286) translate(-820.988,-425.677)" width="20" x="1420.666666666667" y="603.5714285714286"/></g>
  <g id="140">
   <use class="kv35" height="26" transform="rotate(0,968.619,648.857) scale(1.07143,1.42857) translate(-64.146,-189.086)" width="12" x="962.1904761904761" xlink:href="#Accessory:避雷器1_0" y="630.2857142857143" zvalue="1764"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449646297094" ObjectName="光伏Ⅱ回线1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,968.619,648.857) scale(1.07143,1.42857) translate(-64.146,-189.086)" width="12" x="962.1904761904761" y="630.2857142857143"/></g>
  <g id="160">
   <use class="kv35" height="26" transform="rotate(0,1154.76,646.889) scale(1.07143,1.42857) translate(-76.5556,-188.495)" width="12" x="1148.333333333333" xlink:href="#Accessory:避雷器1_0" y="628.3174603174604" zvalue="1776"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449646624774" ObjectName="SVG1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1154.76,646.889) scale(1.07143,1.42857) translate(-76.5556,-188.495)" width="12" x="1148.333333333333" y="628.3174603174604"/></g>
  <g id="172">
   <use class="kv35" height="26" transform="rotate(0,1333.98,645.206) scale(1.07143,1.42857) translate(-88.5037,-187.99)" width="12" x="1327.555555555556" xlink:href="#Accessory:避雷器1_0" y="626.6349206349206" zvalue="1788"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449646821382" ObjectName="接地变1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1333.98,645.206) scale(1.07143,1.42857) translate(-88.5037,-187.99)" width="12" x="1327.555555555556" y="626.6349206349206"/></g>
  <g id="3">
   <use class="kv35" height="26" transform="rotate(0,1139.89,327.429) scale(1.07143,-1.42857) translate(-75.564,-551.057)" width="12" x="1133.460317460317" xlink:href="#Accessory:避雷器1_0" y="308.8571428571428" zvalue="1815"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449647280134" ObjectName="混新线"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1139.89,327.429) scale(1.07143,-1.42857) translate(-75.564,-551.057)" width="12" x="1133.460317460317" y="308.8571428571428"/></g>
  <g id="9">
   <use class="kv35" height="30" transform="rotate(0,1488.67,681.444) scale(2.47619,-2.66667) translate(-861.641,-911.986)" width="35" x="1445.333333333333" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="641.4444444444443" zvalue="1819"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449647345670" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1488.67,681.444) scale(2.47619,-2.66667) translate(-861.641,-911.986)" width="35" x="1445.333333333333" y="641.4444444444443"/></g>
  <g id="28">
   <use class="kv35" height="26" transform="rotate(0,787.619,648.857) scale(1.07143,1.42857) translate(-52.0794,-189.086)" width="12" x="781.1904761904761" xlink:href="#Accessory:避雷器1_0" y="630.2857142857143" zvalue="1831"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449647673349" ObjectName="光伏Ⅰ回线1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,787.619,648.857) scale(1.07143,1.42857) translate(-52.0794,-189.086)" width="12" x="781.1904761904761" y="630.2857142857143"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="191">
   <use class="kv35" height="35" transform="rotate(0,1309.94,705.444) scale(1.30845,1.45397) translate(-303.864,-212.314)" width="32" x="1289" xlink:href="#EnergyConsumer:站用变13_0" y="680" zvalue="1794"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449646886918" ObjectName="35kV接地兼站用变"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1309.94,705.444) scale(1.30845,1.45397) translate(-303.864,-212.314)" width="32" x="1289" y="680"/></g>
  <g id="44">
   <use class="kv35" height="30" transform="rotate(0,1130.21,827.105) scale(1.08333,-1.66667) translate(-86.439,-1313.37)" width="12" x="1123.707141299464" xlink:href="#EnergyConsumer:负荷20230624_0" y="802.1052631578948" zvalue="1920"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449646428166" ObjectName="35kVSVG"/>
   <cge:TPSR_Ref TObjectID="6192449646428166"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1130.21,827.105) scale(1.08333,-1.66667) translate(-86.439,-1313.37)" width="12" x="1123.707141299464" y="802.1052631578948"/></g>
 </g>
 <g id="CoilClass">
  <g id="198">
   <use class="kv35" height="40" transform="rotate(0,1353.6,789.389) scale(0.944444,1.31944) translate(78.9289,-184.726)" width="25" x="1341.791742828088" xlink:href="#Coil:立新光伏_0" y="763" zvalue="1801"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449647017990" ObjectName="接地变消弧线圈"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1353.6,789.389) scale(0.944444,1.31944) translate(78.9289,-184.726)" width="25" x="1341.791742828088" y="763"/></g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,294.339,302.643) scale(0.708333,0.665547) translate(116.824,147.069)" width="30" x="283.71" xlink:href="#State:红绿圆(方形)_0" y="292.66" zvalue="1891"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374886371331" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,294.339,302.643) scale(0.708333,0.665547) translate(116.824,147.069)" width="30" x="283.71" y="292.66"/></g>
  <g id="56">
   <use height="30" transform="rotate(0,198.714,302.643) scale(0.708333,0.665547) translate(77.4485,147.069)" width="30" x="188.09" xlink:href="#State:红绿圆(方形)_0" y="292.66" zvalue="1892"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950180438020" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,198.714,302.643) scale(0.708333,0.665547) translate(77.4485,147.069)" width="30" x="188.09" y="292.66"/></g>
 </g>
</svg>