<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549683486721" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:避雷器PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1754.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2334.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_0" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="3" y1="19" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="4.75" y2="8.75"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="18.75" y2="21.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_1" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="2" y2="24"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_2" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.08333333333333" x2="3.995614035087721" y1="9.083333333333332" y2="17.19627192982456"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.717927631578948" x2="10.16666666666667" y1="9.172423245614038" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="2" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="21.25" y2="24.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
  </symbol>
  <symbol id="Compensator:电容器组2_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="2.300000000000001"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="8" y1="24" y2="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="22" y1="26" y2="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="22" y1="24" y2="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.25" x2="19" y1="18.65" y2="18.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.08333333333333" x2="19.08333333333334" y1="16.65" y2="16.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.91666666666666" x2="25.33333333333334" y1="13.65" y2="13.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.4" x2="25.4" y1="13.65" y2="14.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.09999999999999" x2="25.09999999999999" y1="19.84999999999999" y2="21.84999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15" x2="15" y1="18.73333333333333" y2="26.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15" x2="25" y1="21.65" y2="21.65"/>
   <path d="M 25.2667 14.7333 A 0.85 1.66667 270 0 1 25.2667 16.4333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 25.1833 16.4833 A 0.85 1.66667 270 0 1 25.1833 18.1833" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 25.1833 18.2333 A 0.85 1.66667 270 0 1 25.1833 19.9333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.33333333333333" x2="15" y1="8.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="16.58333333333333" y2="8.583333333333334"/>
   <path d="M 10.5833 8.625 A 4.125 4.375 -90 1 0 14.9583 4.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="4.5" y2="2.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀带电容_0" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="22.44385964912277" xlink:href="#terminal" y="2.404822084808252"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.5" x2="22.5" y1="34.33333333333334" y2="37.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.59657097288676" x2="32.59657097288676" y1="26.16666666666667" y2="34.13038273942627"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.57809675704412" x2="11.74077618288145" y1="34.26823191017643" y2="34.26823191017643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.91666666666667" x2="32.48080808080807" y1="2.425073466891369" y2="2.425073466891369"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.37690058479531" x2="32.51233386496543" y1="12.41913834627738" y2="26.31892973025101"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.50151891516983" x2="32.50151891516983" y1="8.398537532731288" y2="2.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.45249867091971" x2="35.74999999999998" y1="8.498242250204166" y2="8.498242250204166"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.3229709374446" x2="25.20430622009568" y1="41.27556475664245" y2="41.27556475664245"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.21666666666668" x2="27.86685273790538" y1="37.46657451223038" y2="37.46657451223038"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="18.99169767853979" x2="26.09182172603226" y1="39.37106963443644" y2="39.37106963443644"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.81023391812866" x2="11.81023391812866" y1="2.425073466891369" y2="11.5001438746097"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.416666666666664" x2="18.6841013824885" y1="17.86666666666667" y2="17.86666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.416666666666664" x2="18.68410138248849" y1="11.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.81023391812866" x2="11.81023391812866" y1="17.92507346689137" y2="34.25"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀带电容_1" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="22.44385964912277" xlink:href="#terminal" y="2.404822084808252"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.91666666666667" x2="32.48080808080807" y1="2.425073466891369" y2="2.425073466891369"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.57809675704412" x2="11.74077618288145" y1="34.26823191017643" y2="34.26823191017643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.59657097288676" x2="32.59657097288676" y1="26.16666666666667" y2="34.13038273942627"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.51233386496543" x2="32.51233386496543" y1="8.666666666666666" y2="26.318929730251"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.50151891516983" x2="32.50151891516983" y1="8.398537532731288" y2="3.114319320642132"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.45249867091971" x2="35.74999999999998" y1="8.498242250204166" y2="8.498242250204166"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.822970937444602" x2="14.70430622009568" y1="41.27556475664245" y2="41.27556475664245"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="8.491697678539788" x2="15.59182172603226" y1="39.37106963443644" y2="39.37106963443644"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.81023391812866" x2="11.81023391812866" y1="27.35279851087716" y2="37.39281311384654"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.81023391812866" x2="11.81023391812866" y1="2.425073466891369" y2="11.5001438746097"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.862352723190021" x2="14.13838001381733" y1="18.91470614633348" y2="16.97907404038344"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.716666666666674" x2="17.36685273790538" y1="37.46657451223038" y2="37.46657451223038"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.862352723190021" x2="14.13838001381733" y1="21.81815430525855" y2="19.8825221993085"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00036636850368" x2="12.00036636850368" y1="15.04344193443339" y2="23.75378641120859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.862352723189998" x2="12.00036636850365" y1="14.07562588145837" y2="15.04344193443339"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00036636850366" x2="14.13838001381731" y1="23.7537864112086" y2="24.72160246418363"/>
   <rect fill-opacity="0" height="15.49" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,11.91,19.5) scale(1,1) translate(0,0)" width="6.59" x="8.619999999999999" y="11.75"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀带电容_2" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="22.44385964912277" xlink:href="#terminal" y="2.404822084808252"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.23333333333334" x2="36.63333333333333" y1="10.71806722689075" y2="25.13333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.91666666666667" x2="32.48080808080807" y1="2.425073466891369" y2="2.425073466891369"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.57809675704412" x2="11.74077618288145" y1="34.26823191017643" y2="34.26823191017643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.59657097288676" x2="32.59657097288676" y1="26.16666666666667" y2="34.13038273942627"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.50151891516983" x2="32.50151891516983" y1="8.398537532731288" y2="3.114319320642132"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.45249867091971" x2="35.74999999999998" y1="8.498242250204166" y2="8.498242250204166"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.81023391812866" x2="11.81023391812866" y1="2.425073466891369" y2="11.5001438746097"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00036636850368" x2="12.00036636850368" y1="15.04344193443339" y2="23.75378641120859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.862352723189998" x2="12.00036636850365" y1="14.07562588145837" y2="15.04344193443339"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.822970937444602" x2="14.70430622009568" y1="41.27556475664245" y2="41.27556475664245"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="8.491697678539788" x2="15.59182172603226" y1="39.37106963443644" y2="39.37106963443644"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.862352723190021" x2="14.13838001381733" y1="18.91470614633348" y2="16.97907404038344"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00036636850366" x2="14.13838001381731" y1="23.7537864112086" y2="24.72160246418363"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.862352723190021" x2="14.13838001381733" y1="21.81815430525855" y2="19.8825221993085"/>
   <rect fill-opacity="0" height="15.49" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,11.91,19.5) scale(1,1) translate(0,0)" width="6.59" x="8.619999999999999" y="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.716666666666674" x2="17.36685273790538" y1="37.46657451223038" y2="37.46657451223038"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.81023391812866" x2="11.81023391812866" y1="27.35279851087716" y2="37.39281311384654"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀带电容_3" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="22.44385964912277" xlink:href="#terminal" y="2.404822084808252"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.41666666666667" x2="28.91441441441441" y1="10.5" y2="25.13333333333332"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.23333333333334" x2="36.63333333333333" y1="10.71806722689075" y2="25.13333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.91666666666667" x2="32.48080808080807" y1="2.425073466891369" y2="2.425073466891369"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.57809675704412" x2="11.74077618288145" y1="34.26823191017643" y2="34.26823191017643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.59657097288676" x2="32.59657097288676" y1="26.16666666666667" y2="34.13038273942627"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.50151891516983" x2="32.50151891516983" y1="8.398537532731288" y2="3.114319320642132"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.45249867091971" x2="35.74999999999998" y1="8.498242250204166" y2="8.498242250204166"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.81023391812866" x2="11.81023391812866" y1="2.425073466891369" y2="11.5001438746097"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00036636850368" x2="12.00036636850368" y1="15.04344193443339" y2="23.75378641120859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.862352723189998" x2="12.00036636850365" y1="14.07562588145837" y2="15.04344193443339"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.822970937444602" x2="14.70430622009568" y1="41.27556475664245" y2="41.27556475664245"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="8.491697678539788" x2="15.59182172603226" y1="39.37106963443644" y2="39.37106963443644"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.862352723190021" x2="14.13838001381733" y1="18.91470614633348" y2="16.97907404038344"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00036636850366" x2="14.13838001381731" y1="23.7537864112086" y2="24.72160246418363"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.862352723190021" x2="14.13838001381733" y1="21.81815430525855" y2="19.8825221993085"/>
   <rect fill-opacity="0" height="15.49" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,11.91,19.5) scale(1,1) translate(0,0)" width="6.59" x="8.619999999999999" y="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.716666666666674" x2="17.36685273790538" y1="37.46657451223038" y2="37.46657451223038"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.81023391812866" x2="11.81023391812866" y1="27.35279851087716" y2="37.39281311384654"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀带电容_4" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="22.44385964912277" xlink:href="#terminal" y="2.404822084808252"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.5" x2="36.5" y1="18.15" y2="18.15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.41666666666667" x2="28.91441441441441" y1="10.5" y2="25.13333333333332"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.23333333333334" x2="36.63333333333333" y1="10.71806722689075" y2="25.13333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.91666666666667" x2="32.48080808080807" y1="2.425073466891369" y2="2.425073466891369"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.57809675704412" x2="11.74077618288145" y1="34.26823191017643" y2="34.26823191017643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.59657097288676" x2="32.59657097288676" y1="26.16666666666667" y2="34.13038273942627"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.50151891516983" x2="32.50151891516983" y1="8.398537532731288" y2="3.114319320642132"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.45249867091971" x2="35.74999999999998" y1="8.498242250204166" y2="8.498242250204166"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.822970937444602" x2="14.70430622009568" y1="41.27556475664245" y2="41.27556475664245"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="8.491697678539788" x2="15.59182172603226" y1="39.37106963443644" y2="39.37106963443644"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.716666666666674" x2="17.36685273790538" y1="37.46657451223038" y2="37.46657451223038"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.81023391812866" x2="11.81023391812866" y1="2.425073466891369" y2="11.5001438746097"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.862352723190021" x2="14.13838001381733" y1="18.91470614633348" y2="16.97907404038344"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.862352723189998" x2="12.00036636850365" y1="14.07562588145837" y2="15.04344193443339"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.862352723190021" x2="14.13838001381733" y1="21.81815430525855" y2="19.8825221993085"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00036636850368" x2="12.00036636850368" y1="15.04344193443339" y2="23.75378641120859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.81023391812866" x2="11.81023391812866" y1="27.35279851087716" y2="37.39281311384654"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00036636850366" x2="14.13838001381731" y1="23.7537864112086" y2="24.72160246418363"/>
   <rect fill-opacity="0" height="15.49" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,11.91,19.5) scale(1,1) translate(0,0)" width="6.59" x="8.619999999999999" y="11.75"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Breaker:小车母联_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="17.58333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车母联_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="0.8333333333333304" y2="5.749999999999999"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车母联_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="14" y2="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.666666666666666" x2="7.25" y1="5.833333333333333" y2="14.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id="State:间隔模板_0" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(85,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="State:间隔模板_1" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(185,185,185)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸1_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="35.06751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="9" y1="9" y2="9"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.084670781893005" x2="1.626337448559672" y1="4.853614126578682" y2="9.663958954164888"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.084670781893004" x2="12.54300411522634" y1="4.853614126578682" y2="9.663958954164888"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.27315435646374" y2="26.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="31.27315435646374" y2="26.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="7.00133744855967" y1="4.916666666666663" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="1.833333333333333" x2="7.001337448559672" y1="10.75" y2="27"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="7.00133744855967" x2="7.00133744855967" y1="26.75" y2="31.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸1_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="35.06751120764343"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="7.00133744855967" y1="0.7499999999999964" y2="35.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="9" y1="9" y2="9"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸1_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="35.06751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="2.083333333333333" y1="11.16666666666666" y2="25.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.133446357018218" x2="11.79254048508705" y1="11.12564285751284" y2="24.83570582669768"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.27315435646374" y2="27.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.27315435646374" y2="27.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="35.07918883922238" y2="30.26884401163617"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV嘎中变" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="55.31" xlink:href="logo.png" y="28.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="421" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,180.153,69.7136) scale(1,1) translate(-1.22806e-14,0)" writing-mode="lr" x="180.15" xml:space="preserve" y="73.20999999999999" zvalue="761"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,209,69.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="209" xml:space="preserve" y="78.69" zvalue="762">35kV嘎中变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="180" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,289.625,415.5) scale(1,1) translate(0,0)" width="72.88" x="253.19" y="403.5" zvalue="962"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,289.625,415.5) scale(1,1) translate(0,0)" writing-mode="lr" x="289.63" xml:space="preserve" y="420" zvalue="962">信号一览</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="49" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,85.4375,369) scale(1,1) translate(0,0)" width="72.88" x="49" y="357" zvalue="970"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.4375,369) scale(1,1) translate(0,0)" writing-mode="lr" x="85.44" xml:space="preserve" y="373.5" zvalue="970">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="50" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,185.625,415.5) scale(1,1) translate(0,0)" width="72.88" x="149.19" y="403.5" zvalue="972"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,185.625,415.5) scale(1,1) translate(0,0)" writing-mode="lr" x="185.63" xml:space="preserve" y="420" zvalue="972">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="134" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,85.4375,415.5) scale(1,1) translate(0,0)" width="72.88" x="49" y="403.5" zvalue="978"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.4375,415.5) scale(1,1) translate(0,0)" writing-mode="lr" x="85.44" xml:space="preserve" y="420" zvalue="978">小电流接地</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,761.391,632.399) scale(1,1) translate(0,0)" writing-mode="lr" x="761.39" xml:space="preserve" y="636.9" zvalue="45">001</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1486.52,174.929) scale(1,1) translate(0,0)" writing-mode="lr" x="1486.52" xml:space="preserve" y="179.43" zvalue="60">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1446.4,508.766) scale(1,1) translate(-9.31521e-13,0)" writing-mode="lr" x="1446.4" xml:space="preserve" y="513.27" zvalue="62">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1239.27,427.441) scale(1,1) translate(0,0)" writing-mode="lr" x="1239.27" xml:space="preserve" y="431.94" zvalue="72">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1237.84,381.537) scale(1,1) translate(0,0)" writing-mode="lr" x="1237.84" xml:space="preserve" y="386.04" zvalue="74">3021</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1185.16,514.795) scale(1,1) translate(0,0)" writing-mode="lr" x="1185.163105277861" xml:space="preserve" y="519.2946426981971" zvalue="85">#2主变8000kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,560.822,962.482) scale(1,1) translate(0,0)" writing-mode="lr" x="560.8200000000001" xml:space="preserve" y="966.98" zvalue="147">10kV弄坎线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1405.43,391.481) scale(1,1) translate(0,0)" writing-mode="lr" x="1405.43" xml:space="preserve" y="395.98" zvalue="172">3531</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1289.85,626.971) scale(1,1) translate(0,0)" writing-mode="lr" x="1289.85" xml:space="preserve" y="631.47" zvalue="284">002</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,672.372,83.8571) scale(1,1) translate(0,0)" writing-mode="lr" x="672.37" xml:space="preserve" y="88.36" zvalue="299">35kV岭嘎线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,651.595,258.69) scale(1,1) translate(0,0)" writing-mode="lr" x="651.6" xml:space="preserve" y="263.19" zvalue="300">331</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,655.083,195.799) scale(1,1) translate(0,0)" writing-mode="lr" x="655.08" xml:space="preserve" y="200.3" zvalue="302">3316</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,708.017,172.36) scale(1,1) translate(0,0)" writing-mode="lr" x="708.02" xml:space="preserve" y="176.86" zvalue="304">33167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,655.21,317.594) scale(1,1) translate(0,0)" writing-mode="lr" x="655.21" xml:space="preserve" y="322.09" zvalue="307">3311</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,708.017,300.36) scale(1,1) translate(0,0)" writing-mode="lr" x="708.02" xml:space="preserve" y="304.86" zvalue="316">33117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,713.003,122.3) scale(1,1) translate(0,0)" writing-mode="lr" x="713" xml:space="preserve" y="126.8" zvalue="322">3319</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,976.372,81.8571) scale(1,1) translate(0,0)" writing-mode="lr" x="976.37" xml:space="preserve" y="86.36" zvalue="327">35kV勐中线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,955.595,256.69) scale(1,1) translate(0,0)" writing-mode="lr" x="955.6" xml:space="preserve" y="261.19" zvalue="328">332</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,959.083,193.799) scale(1,1) translate(0,0)" writing-mode="lr" x="959.08" xml:space="preserve" y="198.3" zvalue="330">3326</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1012.02,170.36) scale(1,1) translate(0,0)" writing-mode="lr" x="1012.02" xml:space="preserve" y="174.86" zvalue="332">33267</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,959.21,315.594) scale(1,1) translate(0,0)" writing-mode="lr" x="959.21" xml:space="preserve" y="320.09" zvalue="335">3321</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1012.02,298.36) scale(1,1) translate(0,0)" writing-mode="lr" x="1012.02" xml:space="preserve" y="302.86" zvalue="343">33217</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1017,120.3) scale(1,1) translate(0,0)" writing-mode="lr" x="1017" xml:space="preserve" y="124.8" zvalue="347">3329</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1525.63,268.178) scale(1,1) translate(0,0)" writing-mode="lr" x="1525.63" xml:space="preserve" y="272.68" zvalue="354">39017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1525.63,335.178) scale(1,1) translate(0,0)" writing-mode="lr" x="1525.63" xml:space="preserve" y="339.68" zvalue="358">39010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1306.34,379.541) scale(1,1) translate(0,0)" writing-mode="lr" x="1306.34" xml:space="preserve" y="384.04" zvalue="362">30217</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,712.698,427.727) scale(1,1) translate(-9.34849e-13,0)" writing-mode="lr" x="712.7" xml:space="preserve" y="432.23" zvalue="366">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,711.269,385.822) scale(1,1) translate(0,0)" writing-mode="lr" x="711.27" xml:space="preserve" y="390.32" zvalue="368">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,779.769,383.827) scale(1,1) translate(0,0)" writing-mode="lr" x="779.77" xml:space="preserve" y="388.33" zvalue="372">30117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="189" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,588.058,774.621) scale(1,1) translate(0,0)" writing-mode="lr" x="588.0599999999999" xml:space="preserve" y="779.12" zvalue="385">032</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,522.317,845.637) scale(1,1) translate(0,0)" writing-mode="lr" x="522.3200000000001" xml:space="preserve" y="850.14" zvalue="393">0326</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="241" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,595.119,869.862) scale(1,1) translate(0,0)" writing-mode="lr" x="595.12" xml:space="preserve" y="874.36" zvalue="397">03267</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,704.058,774.621) scale(1,1) translate(8.45173e-13,0)" writing-mode="lr" x="704.0599999999999" xml:space="preserve" y="779.12" zvalue="404">033</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,638.317,845.637) scale(1,1) translate(0,0)" writing-mode="lr" x="638.3200000000001" xml:space="preserve" y="850.14" zvalue="411">0336</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,710.286,869.029) scale(1,1) translate(0,0)" writing-mode="lr" x="710.29" xml:space="preserve" y="873.53" zvalue="416">03367</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,415.535,771.621) scale(1,1) translate(0,0)" writing-mode="lr" x="415.54" xml:space="preserve" y="776.12" zvalue="432">031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1735.51,768.621) scale(1,1) translate(0,0)" writing-mode="lr" x="1735.51" xml:space="preserve" y="773.12" zvalue="432">042</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="196" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1829.57,541.16) scale(1,1) translate(-3.90387e-13,0)" writing-mode="lr" x="1829.574116660753" xml:space="preserve" y="545.6595660646243" zvalue="503">10kVⅡ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1810.97,639.331) scale(1,1) translate(0,0)" writing-mode="lr" x="1810.97" xml:space="preserve" y="643.83" zvalue="533">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,990.435,770.263) scale(1,1) translate(0,0)" writing-mode="lr" x="990.4299999999999" xml:space="preserve" y="774.76" zvalue="535">0121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="281" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1198.56,768.891) scale(1,1) translate(0,0)" writing-mode="lr" x="1198.56" xml:space="preserve" y="773.39" zvalue="536">012</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,819.966,518.85) scale(1,1) translate(0,0)" writing-mode="lr" x="819.9661016129114" xml:space="preserve" y="523.3504765568377" zvalue="551">#1主变8000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="292" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,708.017,232.36) scale(1,1) translate(0,0)" writing-mode="lr" x="708.02" xml:space="preserve" y="236.86" zvalue="554">33160</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="295" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1012.02,230.36) scale(1,1) translate(0,0)" writing-mode="lr" x="1012.02" xml:space="preserve" y="234.86" zvalue="558">33260</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="307" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1457.08,286.401) scale(1,1) translate(0,0)" writing-mode="lr" x="1457.08" xml:space="preserve" y="290.9" zvalue="568">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,447.976,933.738) scale(1,1) translate(0,0)" writing-mode="lr" x="447.98" xml:space="preserve" y="938.24" zvalue="578">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="418" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1718.31,987.738) scale(1,1) translate(0,0)" writing-mode="lr" x="1718.31" xml:space="preserve" y="992.24" zvalue="578">10kV2号电容器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="257" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,792.822,962.482) scale(1,1) translate(0,0)" writing-mode="lr" x="792.8200000000001" xml:space="preserve" y="966.98" zvalue="584">10kV嘎中线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="256" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,820.058,774.621) scale(1,1) translate(0,0)" writing-mode="lr" x="820.0599999999999" xml:space="preserve" y="779.12" zvalue="587">034</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,754.317,845.637) scale(1,1) translate(0,0)" writing-mode="lr" x="754.3200000000001" xml:space="preserve" y="850.14" zvalue="594">0346</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,831.286,867.362) scale(1,1) translate(0,0)" writing-mode="lr" x="831.29" xml:space="preserve" y="871.86" zvalue="599">03467</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="321" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,912.822,962.482) scale(1,1) translate(0,0)" writing-mode="lr" x="912.8200000000001" xml:space="preserve" y="966.98" zvalue="605">10kV黑山门东段线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="320" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,940.058,774.621) scale(1,1) translate(0,0)" writing-mode="lr" x="940.0599999999999" xml:space="preserve" y="779.12" zvalue="608">035</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="319" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,874.317,845.637) scale(1,1) translate(0,0)" writing-mode="lr" x="874.3200000000001" xml:space="preserve" y="850.14" zvalue="615">0356</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="318" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,951.286,867.362) scale(1,1) translate(0,0)" writing-mode="lr" x="951.29" xml:space="preserve" y="871.86" zvalue="620">03567</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="344" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1260.32,962.482) scale(1,1) translate(0,0)" writing-mode="lr" x="1260.32" xml:space="preserve" y="966.98" zvalue="629">10kV遮冒线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="343" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1287.58,774.621) scale(1,1) translate(0,0)" writing-mode="lr" x="1287.58" xml:space="preserve" y="779.12" zvalue="632">036</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="342" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1221.82,845.637) scale(1,1) translate(0,0)" writing-mode="lr" x="1221.82" xml:space="preserve" y="850.14" zvalue="639">0366</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="341" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1298.79,867.362) scale(1,1) translate(0,0)" writing-mode="lr" x="1298.79" xml:space="preserve" y="871.86" zvalue="644">03667</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="362" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1380.32,962.482) scale(1,1) translate(0,0)" writing-mode="lr" x="1380.32" xml:space="preserve" y="966.98" zvalue="648">10kV遮放水泥厂线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="361" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1407.56,774.621) scale(1,1) translate(0,0)" writing-mode="lr" x="1407.56" xml:space="preserve" y="779.12" zvalue="651">037</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="360" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1341.82,845.637) scale(1,1) translate(0,0)" writing-mode="lr" x="1341.82" xml:space="preserve" y="850.14" zvalue="658">0376</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="359" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1418.79,867.362) scale(1,1) translate(0,0)" writing-mode="lr" x="1418.79" xml:space="preserve" y="871.86" zvalue="663">03767</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="380" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1500.32,962.482) scale(1,1) translate(0,0)" writing-mode="lr" x="1500.32" xml:space="preserve" y="966.98" zvalue="667">10kV陇遮水泥厂线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="379" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1527.56,774.621) scale(1,1) translate(0,0)" writing-mode="lr" x="1527.56" xml:space="preserve" y="779.12" zvalue="670">038</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="378" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1461.82,845.637) scale(1,1) translate(-2.56829e-12,0)" writing-mode="lr" x="1461.82" xml:space="preserve" y="850.14" zvalue="677">0386</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="377" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1538.79,867.362) scale(1,1) translate(0,0)" writing-mode="lr" x="1538.79" xml:space="preserve" y="871.86" zvalue="682">03867</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="399" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1620.32,962.482) scale(1,1) translate(0,0)" writing-mode="lr" x="1620.32" xml:space="preserve" y="966.98" zvalue="687">10kV户拉线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="398" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1647.56,774.621) scale(1,1) translate(0,0)" writing-mode="lr" x="1647.56" xml:space="preserve" y="779.12" zvalue="690">039</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="397" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1581.82,845.637) scale(1,1) translate(0,0)" writing-mode="lr" x="1581.82" xml:space="preserve" y="850.14" zvalue="697">0396</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="396" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1652.79,868.362) scale(1,1) translate(0,0)" writing-mode="lr" x="1652.79" xml:space="preserve" y="872.86" zvalue="702">03967</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,419.286,876.107) scale(1,1) translate(0,0)" writing-mode="lr" x="419.29" xml:space="preserve" y="880.61" zvalue="727">03117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1694.79,877.982) scale(1,1) translate(0,0)" writing-mode="lr" x="1694.79" xml:space="preserve" y="882.48" zvalue="729">04227</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,481.452,650.476) scale(1,1) translate(0,0)" writing-mode="lr" x="481.45" xml:space="preserve" y="654.98" zvalue="751">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,507.352,532.274) scale(1,1) translate(-1.92447e-13,0)" writing-mode="lr" x="507.35" xml:space="preserve" y="536.77" zvalue="755">10kVⅠ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,950.822,883.482) scale(1,1) translate(0,0)" writing-mode="lr" x="950.8200000000001" xml:space="preserve" y="887.98" zvalue="757">(用户专线)</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1102.82,767.482) scale(1,1) translate(0,0)" writing-mode="lr" x="1102.82" xml:space="preserve" y="771.98" zvalue="759">10kV分段</text>
  <line fill="none" id="415" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.000000000000455" x2="372" y1="154.8704926140825" y2="154.8704926140825" zvalue="764"/>
  <line fill="none" id="414" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="373.0000000000002" x2="373.0000000000002" y1="13" y2="1043" zvalue="765"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000227" x2="187.0000000000002" y1="167.0000000000001" y2="167.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000227" x2="187.0000000000002" y1="193.0000000000001" y2="193.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000227" x2="6.000000000000227" y1="167.0000000000001" y2="193.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000002" x2="187.0000000000002" y1="167.0000000000001" y2="193.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000002" x2="368.0000000000002" y1="167.0000000000001" y2="167.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000002" x2="368.0000000000002" y1="193.0000000000001" y2="193.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000002" x2="187.0000000000002" y1="167.0000000000001" y2="193.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0000000000002" x2="368.0000000000002" y1="167.0000000000001" y2="193.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000227" x2="187.0000000000002" y1="193.0000000000001" y2="193.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000227" x2="187.0000000000002" y1="217.2500000000001" y2="217.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000227" x2="6.000000000000227" y1="193.0000000000001" y2="217.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000002" x2="187.0000000000002" y1="193.0000000000001" y2="217.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000002" x2="368.0000000000002" y1="193.0000000000001" y2="193.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000002" x2="368.0000000000002" y1="217.2500000000001" y2="217.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000002" x2="187.0000000000002" y1="193.0000000000001" y2="217.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0000000000002" x2="368.0000000000002" y1="193.0000000000001" y2="217.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000227" x2="187.0000000000002" y1="217.2500000000001" y2="217.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000227" x2="187.0000000000002" y1="240.0000000000001" y2="240.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000227" x2="6.000000000000227" y1="217.2500000000001" y2="240.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000002" x2="187.0000000000002" y1="217.2500000000001" y2="240.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000002" x2="368.0000000000002" y1="217.2500000000001" y2="217.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000002" x2="368.0000000000002" y1="240.0000000000001" y2="240.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000002" x2="187.0000000000002" y1="217.2500000000001" y2="240.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0000000000002" x2="368.0000000000002" y1="217.2500000000001" y2="240.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000227" x2="187.0000000000002" y1="240.0000000000001" y2="240.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000227" x2="187.0000000000002" y1="262.7500000000001" y2="262.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000227" x2="6.000000000000227" y1="240.0000000000001" y2="262.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000002" x2="187.0000000000002" y1="240.0000000000001" y2="262.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000002" x2="368.0000000000002" y1="240.0000000000001" y2="240.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000002" x2="368.0000000000002" y1="262.7500000000001" y2="262.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000002" x2="187.0000000000002" y1="240.0000000000001" y2="262.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0000000000002" x2="368.0000000000002" y1="240.0000000000001" y2="262.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000227" x2="187.0000000000002" y1="262.7500000000001" y2="262.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000227" x2="187.0000000000002" y1="285.5000000000001" y2="285.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000227" x2="6.000000000000227" y1="262.7500000000001" y2="285.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000002" x2="187.0000000000002" y1="262.7500000000001" y2="285.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000002" x2="368.0000000000002" y1="262.7500000000001" y2="262.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000002" x2="368.0000000000002" y1="285.5000000000001" y2="285.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000002" x2="187.0000000000002" y1="262.7500000000001" y2="285.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0000000000002" x2="368.0000000000002" y1="262.7500000000001" y2="285.5000000000001"/>
  <line fill="none" id="340" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.000000000000455" x2="372" y1="624.8704926140824" y2="624.8704926140824" zvalue="767"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.76923076923094" x2="103.5437307692309" y1="446.2307692307692" y2="446.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.76923076923094" x2="103.5437307692309" y1="484.5130692307692" y2="484.5130692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.76923076923094" x2="57.76923076923094" y1="446.2307692307692" y2="484.5130692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="103.5437307692309" y1="446.2307692307692" y2="484.5130692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="162.350130769231" y1="446.2307692307692" y2="446.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="162.350130769231" y1="484.5130692307692" y2="484.5130692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="103.5437307692309" y1="446.2307692307692" y2="484.5130692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="162.350130769231" y1="446.2307692307692" y2="484.5130692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="221.1565307692309" y1="446.2307692307692" y2="446.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="221.1565307692309" y1="484.5130692307692" y2="484.5130692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="162.350130769231" y1="446.2307692307692" y2="484.5130692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.1565307692309" x2="221.1565307692309" y1="446.2307692307692" y2="484.5130692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.156430769231" x2="279.962830769231" y1="446.2307692307692" y2="446.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.156430769231" x2="279.962830769231" y1="484.5130692307692" y2="484.5130692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.156430769231" x2="221.156430769231" y1="446.2307692307692" y2="484.5130692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="279.962830769231" y1="446.2307692307692" y2="484.5130692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="338.7692307692309" y1="446.2307692307692" y2="446.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="338.7692307692309" y1="484.5130692307692" y2="484.5130692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="279.962830769231" y1="446.2307692307692" y2="484.5130692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="338.7692307692309" x2="338.7692307692309" y1="446.2307692307692" y2="484.5130692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.76923076923094" x2="103.5437307692309" y1="484.5130692307692" y2="484.5130692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.76923076923094" x2="103.5437307692309" y1="509.1924692307692" y2="509.1924692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.76923076923094" x2="57.76923076923094" y1="484.5130692307692" y2="509.1924692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="103.5437307692309" y1="484.5130692307692" y2="509.1924692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="162.350130769231" y1="484.5130692307692" y2="484.5130692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="162.350130769231" y1="509.1924692307692" y2="509.1924692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="103.5437307692309" y1="484.5130692307692" y2="509.1924692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="162.350130769231" y1="484.5130692307692" y2="509.1924692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="221.1565307692309" y1="484.5130692307692" y2="484.5130692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="221.1565307692309" y1="509.1924692307692" y2="509.1924692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="162.350130769231" y1="484.5130692307692" y2="509.1924692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.1565307692309" x2="221.1565307692309" y1="484.5130692307692" y2="509.1924692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.156430769231" x2="279.962830769231" y1="484.5130692307692" y2="484.5130692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.156430769231" x2="279.962830769231" y1="509.1924692307692" y2="509.1924692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.156430769231" x2="221.156430769231" y1="484.5130692307692" y2="509.1924692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="279.962830769231" y1="484.5130692307692" y2="509.1924692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="338.7692307692309" y1="484.5130692307692" y2="484.5130692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="338.7692307692309" y1="509.1924692307692" y2="509.1924692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="279.962830769231" y1="484.5130692307692" y2="509.1924692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="338.7692307692309" x2="338.7692307692309" y1="484.5130692307692" y2="509.1924692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.76923076923094" x2="103.5437307692309" y1="509.1924692307692" y2="509.1924692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.76923076923094" x2="103.5437307692309" y1="533.8718692307692" y2="533.8718692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.76923076923094" x2="57.76923076923094" y1="509.1924692307692" y2="533.8718692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="103.5437307692309" y1="509.1924692307692" y2="533.8718692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="162.350130769231" y1="509.1924692307692" y2="509.1924692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="162.350130769231" y1="533.8718692307692" y2="533.8718692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="103.5437307692309" y1="509.1924692307692" y2="533.8718692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="162.350130769231" y1="509.1924692307692" y2="533.8718692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="221.1565307692309" y1="509.1924692307692" y2="509.1924692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="221.1565307692309" y1="533.8718692307692" y2="533.8718692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="162.350130769231" y1="509.1924692307692" y2="533.8718692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.1565307692309" x2="221.1565307692309" y1="509.1924692307692" y2="533.8718692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.156430769231" x2="279.962830769231" y1="509.1924692307692" y2="509.1924692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.156430769231" x2="279.962830769231" y1="533.8718692307692" y2="533.8718692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.156430769231" x2="221.156430769231" y1="509.1924692307692" y2="533.8718692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="279.962830769231" y1="509.1924692307692" y2="533.8718692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="338.7692307692309" y1="509.1924692307692" y2="509.1924692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="338.7692307692309" y1="533.8718692307692" y2="533.8718692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="279.962830769231" y1="509.1924692307692" y2="533.8718692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="338.7692307692309" x2="338.7692307692309" y1="509.1924692307692" y2="533.8718692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.76923076923094" x2="103.5437307692309" y1="533.8718692307692" y2="533.8718692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.76923076923094" x2="103.5437307692309" y1="558.5512692307692" y2="558.5512692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.76923076923094" x2="57.76923076923094" y1="533.8718692307692" y2="558.5512692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="103.5437307692309" y1="533.8718692307692" y2="558.5512692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="162.350130769231" y1="533.8718692307692" y2="533.8718692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="162.350130769231" y1="558.5512692307692" y2="558.5512692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="103.5437307692309" y1="533.8718692307692" y2="558.5512692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="162.350130769231" y1="533.8718692307692" y2="558.5512692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="221.1565307692309" y1="533.8718692307692" y2="533.8718692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="221.1565307692309" y1="558.5512692307692" y2="558.5512692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="162.350130769231" y1="533.8718692307692" y2="558.5512692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.1565307692309" x2="221.1565307692309" y1="533.8718692307692" y2="558.5512692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.156430769231" x2="279.962830769231" y1="533.8718692307692" y2="533.8718692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.156430769231" x2="279.962830769231" y1="558.5512692307692" y2="558.5512692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.156430769231" x2="221.156430769231" y1="533.8718692307692" y2="558.5512692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="279.962830769231" y1="533.8718692307692" y2="558.5512692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="338.7692307692309" y1="533.8718692307692" y2="533.8718692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="338.7692307692309" y1="558.5512692307692" y2="558.5512692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="279.962830769231" y1="533.8718692307692" y2="558.5512692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="338.7692307692309" x2="338.7692307692309" y1="533.8718692307692" y2="558.5512692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.76923076923094" x2="103.5437307692309" y1="558.5513692307692" y2="558.5513692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.76923076923094" x2="103.5437307692309" y1="583.2307692307692" y2="583.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.76923076923094" x2="57.76923076923094" y1="558.5513692307692" y2="583.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="103.5437307692309" y1="558.5513692307692" y2="583.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="162.350130769231" y1="558.5513692307692" y2="558.5513692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="162.350130769231" y1="583.2307692307692" y2="583.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="103.5437307692309" y1="558.5513692307692" y2="583.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="162.350130769231" y1="558.5513692307692" y2="583.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="221.1565307692309" y1="558.5513692307692" y2="558.5513692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="221.1565307692309" y1="583.2307692307692" y2="583.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="162.350130769231" y1="558.5513692307692" y2="583.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.1565307692309" x2="221.1565307692309" y1="558.5513692307692" y2="583.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.156430769231" x2="279.962830769231" y1="558.5513692307692" y2="558.5513692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.156430769231" x2="279.962830769231" y1="583.2307692307692" y2="583.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.156430769231" x2="221.156430769231" y1="558.5513692307692" y2="583.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="279.962830769231" y1="558.5513692307692" y2="583.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="338.7692307692309" y1="558.5513692307692" y2="558.5513692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="338.7692307692309" y1="583.2307692307692" y2="583.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="279.962830769231" y1="558.5513692307692" y2="583.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="338.7692307692309" x2="338.7692307692309" y1="558.5513692307692" y2="583.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.76923076923094" x2="103.5437307692309" y1="583.2307692307692" y2="583.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.76923076923094" x2="103.5437307692309" y1="607.9101692307693" y2="607.9101692307693"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="57.76923076923094" x2="57.76923076923094" y1="583.2307692307692" y2="607.9101692307693"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="103.5437307692309" y1="583.2307692307692" y2="607.9101692307693"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="162.350130769231" y1="583.2307692307692" y2="583.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="162.350130769231" y1="607.9101692307693" y2="607.9101692307693"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.5437307692309" x2="103.5437307692309" y1="583.2307692307692" y2="607.9101692307693"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="162.350130769231" y1="583.2307692307692" y2="607.9101692307693"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="221.1565307692309" y1="583.2307692307692" y2="583.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="221.1565307692309" y1="607.9101692307693" y2="607.9101692307693"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.350130769231" x2="162.350130769231" y1="583.2307692307692" y2="607.9101692307693"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.1565307692309" x2="221.1565307692309" y1="583.2307692307692" y2="607.9101692307693"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.156430769231" x2="279.962830769231" y1="583.2307692307692" y2="583.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.156430769231" x2="279.962830769231" y1="607.9101692307693" y2="607.9101692307693"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.156430769231" x2="221.156430769231" y1="583.2307692307692" y2="607.9101692307693"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="279.962830769231" y1="583.2307692307692" y2="607.9101692307693"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="338.7692307692309" y1="583.2307692307692" y2="583.2307692307692"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="338.7692307692309" y1="607.9101692307693" y2="607.9101692307693"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="279.962830769231" x2="279.962830769231" y1="583.2307692307692" y2="607.9101692307693"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="338.7692307692309" x2="338.7692307692309" y1="583.2307692307692" y2="607.9101692307693"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000227" x2="95.00000000000023" y1="940" y2="940"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000227" x2="95.00000000000023" y1="979.1632999999999" y2="979.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000227" x2="5.000000000000227" y1="940" y2="979.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000023" x2="95.00000000000023" y1="940" y2="979.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000023" x2="365.0000000000002" y1="940" y2="940"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000023" x2="365.0000000000002" y1="979.1632999999999" y2="979.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000023" x2="95.00000000000023" y1="940" y2="979.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="365.0000000000002" x2="365.0000000000002" y1="940" y2="979.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000227" x2="95.00000000000023" y1="979.16327" y2="979.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000227" x2="95.00000000000023" y1="1007.08167" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000227" x2="5.000000000000227" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000023" x2="95.00000000000023" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000023" x2="185.0000000000002" y1="979.16327" y2="979.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000023" x2="185.0000000000002" y1="1007.08167" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000023" x2="95.00000000000023" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000002" x2="185.0000000000002" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000003" x2="275.0000000000003" y1="979.16327" y2="979.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000003" x2="275.0000000000003" y1="1007.08167" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000003" x2="185.0000000000003" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000003" x2="275.0000000000003" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000002" x2="365.0000000000002" y1="979.16327" y2="979.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000002" x2="365.0000000000002" y1="1007.08167" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000002" x2="275.0000000000002" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="365.0000000000002" x2="365.0000000000002" y1="979.16327" y2="1007.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000227" x2="95.00000000000023" y1="1007.0816" y2="1007.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000227" x2="95.00000000000023" y1="1035" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000227" x2="5.000000000000227" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000023" x2="95.00000000000023" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000023" x2="185.0000000000002" y1="1007.0816" y2="1007.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000023" x2="185.0000000000002" y1="1035" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000023" x2="95.00000000000023" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000002" x2="185.0000000000002" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000003" x2="275.0000000000003" y1="1007.0816" y2="1007.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000003" x2="275.0000000000003" y1="1035" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000003" x2="185.0000000000003" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000003" x2="275.0000000000003" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000002" x2="365.0000000000002" y1="1007.0816" y2="1007.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000002" x2="365.0000000000002" y1="1035" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000002" x2="275.0000000000002" y1="1007.0816" y2="1035"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="365.0000000000002" x2="365.0000000000002" y1="1007.0816" y2="1035"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="317" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50,960) scale(1,1) translate(0,0)" writing-mode="lr" x="50" xml:space="preserve" y="966" zvalue="771">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="316" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47,994) scale(1,1) translate(0,0)" writing-mode="lr" x="47" xml:space="preserve" y="1000" zvalue="772">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229,994) scale(1,1) translate(0,0)" writing-mode="lr" x="229" xml:space="preserve" y="1000" zvalue="773">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="301" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46,1022) scale(1,1) translate(0,0)" writing-mode="lr" x="46" xml:space="preserve" y="1028" zvalue="774">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="300" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228,1022) scale(1,1) translate(0,0)" writing-mode="lr" x="228" xml:space="preserve" y="1028" zvalue="775">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="291" stroke="rgb(255,255,255)" text-anchor="middle" x="129.53125" xml:space="preserve" y="464.3993055555555" zvalue="776">35kV     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="291" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="129.53125" xml:space="preserve" y="481.3993055555555" zvalue="776">Ⅰ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.5,654.5) scale(1,1) translate(0,0)" writing-mode="lr" x="70.50000000000023" xml:space="preserve" y="659" zvalue="778">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="282" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,203.399,321.841) scale(1,1) translate(0,0)" writing-mode="lr" x="203.4" xml:space="preserve" y="326.34" zvalue="779">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="278" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,308.399,321.841) scale(1,1) translate(0,0)" writing-mode="lr" x="308.4" xml:space="preserve" y="326.34" zvalue="780">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="271" stroke="rgb(255,255,255)" text-anchor="middle" x="248.9375" xml:space="preserve" y="462.8368055555555" zvalue="781">10kV     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="271" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="248.9375" xml:space="preserve" y="479.8368055555555" zvalue="781">Ⅰ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="262" stroke="rgb(255,255,255)" text-anchor="middle" x="308.78125" xml:space="preserve" y="462.0711805555555" zvalue="782">10kV      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="262" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="308.78125" xml:space="preserve" y="479.0711805555555" zvalue="782">Ⅱ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="261" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82,497.75) scale(1,1) translate(0,0)" writing-mode="lr" x="82.00000000000023" xml:space="preserve" y="502.25" zvalue="783">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82,523.25) scale(1,1) translate(0,0)" writing-mode="lr" x="82.00000000000023" xml:space="preserve" y="527.75" zvalue="784">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82,546.25) scale(1,1) translate(0,0)" writing-mode="lr" x="82.00000000000023" xml:space="preserve" y="550.75" zvalue="785">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82,569.25) scale(1,1) translate(0,0)" writing-mode="lr" x="82.00000000000023" xml:space="preserve" y="573.75" zvalue="786">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="254" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82,596.25) scale(1,1) translate(0,0)" writing-mode="lr" x="82.00000000000023" xml:space="preserve" y="600.75" zvalue="787">Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,230.054,962) scale(1,1) translate(0,0)" writing-mode="lr" x="230.05" xml:space="preserve" y="968" zvalue="788">GaZhong-01-2023</text>
  
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="250" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44,181) scale(1,1) translate(0,0)" writing-mode="lr" x="44" xml:space="preserve" y="186.5" zvalue="791">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="249" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224,181) scale(1,1) translate(0,0)" writing-mode="lr" x="224" xml:space="preserve" y="186.5" zvalue="792">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="248" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.6875,205.25) scale(1,1) translate(0,0)" writing-mode="lr" x="47.69" xml:space="preserve" y="209.75" zvalue="793">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.1875,253) scale(1,1) translate(0,0)" writing-mode="lr" x="51.19" xml:space="preserve" y="258.5" zvalue="794">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="246" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.75,252.5) scale(1,1) translate(0,0)" writing-mode="lr" x="232.75" xml:space="preserve" y="258" zvalue="795">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="245" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.1875,276) scale(1,1) translate(0,0)" writing-mode="lr" x="51.19" xml:space="preserve" y="281.5" zvalue="796">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="244" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.75,275.5) scale(1,1) translate(0,0)" writing-mode="lr" x="232.75" xml:space="preserve" y="281" zvalue="797">2号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="243" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.6875,229.25) scale(1,1) translate(0,0)" writing-mode="lr" x="48.69" xml:space="preserve" y="233.75" zvalue="798">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="242" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231.75,229) scale(1,1) translate(0,0)" writing-mode="lr" x="231.75" xml:space="preserve" y="233.5" zvalue="799">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,934.5,672) scale(1,1) translate(0,0)" writing-mode="lr" x="934.5" xml:space="preserve" y="676.5" zvalue="964">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1450.75,677) scale(1,1) translate(0,0)" writing-mode="lr" x="1450.75" xml:space="preserve" y="681.5" zvalue="965">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="213" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,570.125,329.5) scale(1,1) translate(0,0)" writing-mode="lr" x="570.13" xml:space="preserve" y="334" zvalue="966">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,139,1023) scale(1,1) translate(0,0)" writing-mode="lr" x="139" xml:space="preserve" y="1029" zvalue="968">李艳</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,676,962.482) scale(1,1) translate(0,0)" writing-mode="lr" x="676" xml:space="preserve" y="966.98" zvalue="980">10kV嘎棒线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,181.286,368.539) scale(1,1) translate(0,0)" writing-mode="lr" x="181.29" xml:space="preserve" y="373.04" zvalue="1014">全站公用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,321,1022) scale(1,1) translate(0,0)" writing-mode="lr" x="321" xml:space="preserve" y="1028" zvalue="1036">2023.04.23</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="231" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1836.61,762.621) scale(1,1) translate(0,0)" writing-mode="lr" x="1836.61" xml:space="preserve" y="767.12" zvalue="1039">0412</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1797.88,893.276) scale(1,1) translate(0,0)" writing-mode="lr" x="1797.88" xml:space="preserve" y="897.78" zvalue="1045">10kV2号站用变</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="253.19" y="403.5" zvalue="962"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="49" y="357" zvalue="970"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="149.19" y="403.5" zvalue="972"/></g>
  <g href="小电流装置20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="49" y="403.5" zvalue="978"/></g>
 </g>
 <g id="BreakerClass">
  <g id="461">
   <use class="kv10" height="20" transform="rotate(180,733.054,628.399) scale(2.86217,2.49388) translate(-467.625,-361.484)" width="10" x="718.7432036745543" xlink:href="#Breaker:小车断路器_0" y="603.4604839871525" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925206343683" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925206343683"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,733.054,628.399) scale(2.86217,2.49388) translate(-467.625,-361.484)" width="10" x="718.7432036745543" y="603.4604839871525"/></g>
  <g id="816">
   <use class="kv35" height="20" transform="rotate(180,1260.87,427.066) scale(1.5542,1.35421) translate(-446.834,-108.162)" width="10" x="1253.102425008615" xlink:href="#Breaker:开关_0" y="413.5238390693762" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925206278147" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925206278147"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1260.87,427.066) scale(1.5542,1.35421) translate(-446.834,-108.162)" width="10" x="1253.102425008615" y="413.5238390693762"/></g>
  <g id="230">
   <use class="kv10" height="20" transform="rotate(180,1261.51,622.971) scale(2.86217,2.49388) translate(-811.449,-358.232)" width="10" x="1247.203444078439" xlink:href="#Breaker:小车断路器_0" y="598.0319125585811" zvalue="283"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925206409219" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473925206409219"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1261.51,622.971) scale(2.86217,2.49388) translate(-811.449,-358.232)" width="10" x="1247.203444078439" y="598.0319125585811"/></g>
  <g id="69">
   <use class="kv35" height="20" transform="rotate(0,672.991,259.065) scale(1.5542,1.35421) translate(-237.205,-64.2195)" width="10" x="665.2197488202794" xlink:href="#Breaker:开关_0" y="245.5228933678039" zvalue="298"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925206474755" ObjectName="35kV岭嘎线331断路器"/>
   <cge:TPSR_Ref TObjectID="6473925206474755"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,672.991,259.065) scale(1.5542,1.35421) translate(-237.205,-64.2195)" width="10" x="665.2197488202794" y="245.5228933678039"/></g>
  <g id="99">
   <use class="kv35" height="20" transform="rotate(0,976.991,257.065) scale(1.5542,1.35421) translate(-345.606,-63.6964)" width="10" x="969.2197488202795" xlink:href="#Breaker:开关_0" y="243.5228933678039" zvalue="326"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925206540291" ObjectName="35kV勐中线332断路器"/>
   <cge:TPSR_Ref TObjectID="6473925206540291"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,976.991,257.065) scale(1.5542,1.35421) translate(-345.606,-63.6964)" width="10" x="969.2197488202795" y="243.5228933678039"/></g>
  <g id="170">
   <use class="kv35" height="20" transform="rotate(180,734.302,427.352) scale(1.5542,1.35421) translate(-259.068,-108.237)" width="10" x="726.5309964371866" xlink:href="#Breaker:开关_0" y="413.8095533550904" zvalue="365"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925206605827" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925206605827"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,734.302,427.352) scale(1.5542,1.35421) translate(-259.068,-108.237)" width="10" x="726.5309964371866" y="413.8095533550904"/></g>
  <g id="214">
   <use class="kv10" height="20" transform="rotate(180,559.721,770.621) scale(2.86217,2.49388) translate(-354.852,-446.677)" width="10" x="545.409870341221" xlink:href="#Breaker:小车断路器_0" y="745.6827062093747" zvalue="384"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925206736899" ObjectName="10kV弄坎线032断路器"/>
   <cge:TPSR_Ref TObjectID="6473925206736899"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,559.721,770.621) scale(2.86217,2.49388) translate(-354.852,-446.677)" width="10" x="545.409870341221" y="745.6827062093747"/></g>
  <g id="66">
   <use class="kv10" height="20" transform="rotate(180,675.721,770.621) scale(2.86217,2.49388) translate(-430.323,-446.677)" width="10" x="661.409870341221" xlink:href="#Breaker:小车断路器_0" y="745.6827062093747" zvalue="403"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925206802435" ObjectName="10kV嘎棒线033断路器"/>
   <cge:TPSR_Ref TObjectID="6473925206802435"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,675.721,770.621) scale(2.86217,2.49388) translate(-430.323,-446.677)" width="10" x="661.409870341221" y="745.6827062093747"/></g>
  <g id="429">
   <use class="kv10" height="20" transform="rotate(180,1714.74,769.621) scale(2.86217,2.49388) translate(-1106.32,-446.078)" width="10" x="1700.427174202544" xlink:href="#Breaker:小车断路器_0" y="744.6827116379602" zvalue="431"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925207261187" ObjectName="10kV2号电容器042断路器"/>
   <cge:TPSR_Ref TObjectID="6473925207261187"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1714.74,769.621) scale(2.86217,2.49388) translate(-1106.32,-446.078)" width="10" x="1700.427174202544" y="744.6827116379602"/></g>
  <g id="233">
   <use class="kv10" height="20" transform="rotate(180,447.738,771.225) scale(2.86217,2.49388) translate(-281.994,-447.039)" width="10" x="433.4271742025441" xlink:href="#Breaker:小车断路器_0" y="746.2860484083155" zvalue="431"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925207326723" ObjectName="10kV1号电容器031断路器"/>
   <cge:TPSR_Ref TObjectID="6473925207326723"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,447.738,771.225) scale(2.86217,2.49388) translate(-281.994,-447.039)" width="10" x="433.4271742025441" y="746.2860484083155"/></g>
  <g id="274">
   <use class="kv10" height="20" transform="rotate(180,1173.4,769.086) scale(2.52088,2.1965) translate(-700.32,-406.979)" width="10" x="1160.791237697962" xlink:href="#Breaker:小车母联_0" y="747.1213828683212" zvalue="535"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925206671363" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473925206671363"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1173.4,769.086) scale(2.52088,2.1965) translate(-700.32,-406.979)" width="10" x="1160.791237697962" y="747.1213828683212"/></g>
  <g id="312">
   <use class="kv10" height="20" transform="rotate(180,791.721,770.621) scale(2.86217,2.49388) translate(-505.794,-446.677)" width="10" x="777.4098703412209" xlink:href="#Breaker:小车断路器_0" y="745.6827062093747" zvalue="586"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925206867971" ObjectName="10kV嘎中线034断路器"/>
   <cge:TPSR_Ref TObjectID="6473925206867971"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,791.721,770.621) scale(2.86217,2.49388) translate(-505.794,-446.677)" width="10" x="777.4098703412209" y="745.6827062093747"/></g>
  <g id="332">
   <use class="kv10" height="20" transform="rotate(180,911.721,770.621) scale(2.86217,2.49388) translate(-583.868,-446.677)" width="10" x="897.409870341221" xlink:href="#Breaker:小车断路器_0" y="745.6827062093747" zvalue="607"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925206933507" ObjectName="10kV黑山门东段线035断路器"/>
   <cge:TPSR_Ref TObjectID="6473925206933507"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,911.721,770.621) scale(2.86217,2.49388) translate(-583.868,-446.677)" width="10" x="897.409870341221" y="745.6827062093747"/></g>
  <g id="355">
   <use class="kv10" height="20" transform="rotate(180,1259.24,770.621) scale(2.86217,2.49388) translate(-809.968,-446.677)" width="10" x="1244.927174202544" xlink:href="#Breaker:小车断路器_0" y="745.6827062093747" zvalue="631"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925206999043" ObjectName="10kV遮冒线036断路器"/>
   <cge:TPSR_Ref TObjectID="6473925206999043"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1259.24,770.621) scale(2.86217,2.49388) translate(-809.968,-446.677)" width="10" x="1244.927174202544" y="745.6827062093747"/></g>
  <g id="373">
   <use class="kv10" height="20" transform="rotate(180,1379.22,770.621) scale(2.86217,2.49388) translate(-888.031,-446.677)" width="10" x="1364.909870341221" xlink:href="#Breaker:小车断路器_0" y="745.6827062093747" zvalue="650"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925207064579" ObjectName="10kV遮放水泥厂线037断路器"/>
   <cge:TPSR_Ref TObjectID="6473925207064579"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1379.22,770.621) scale(2.86217,2.49388) translate(-888.031,-446.677)" width="10" x="1364.909870341221" y="745.6827062093747"/></g>
  <g id="391">
   <use class="kv10" height="20" transform="rotate(180,1499.22,770.621) scale(2.86217,2.49388) translate(-966.104,-446.677)" width="10" x="1484.909870341221" xlink:href="#Breaker:小车断路器_0" y="745.6827062093747" zvalue="669"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925207130115" ObjectName="10kV陇遮水泥厂线038断路器"/>
   <cge:TPSR_Ref TObjectID="6473925207130115"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1499.22,770.621) scale(2.86217,2.49388) translate(-966.104,-446.677)" width="10" x="1484.909870341221" y="745.6827062093747"/></g>
  <g id="410">
   <use class="kv10" height="20" transform="rotate(180,1619.22,770.621) scale(2.86217,2.49388) translate(-1044.18,-446.677)" width="10" x="1604.909870341221" xlink:href="#Breaker:小车断路器_0" y="745.6827062093747" zvalue="689"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925207195651" ObjectName="10kV户拉线039断路器"/>
   <cge:TPSR_Ref TObjectID="6473925207195651"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1619.22,770.621) scale(2.86217,2.49388) translate(-1044.18,-446.677)" width="10" x="1604.909870341221" y="745.6827062093747"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="218">
   <use class="kv35" height="30" transform="rotate(0,1481.52,209.483) scale(-1.25,-1.25) translate(-2662.98,-373.32)" width="30" x="1462.768876275675" xlink:href="#Accessory:避雷器PT带熔断器_0" y="190.7333184057321" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454714458115" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1481.52,209.483) scale(-1.25,-1.25) translate(-2662.98,-373.32)" width="30" x="1462.768876275675" y="190.7333184057321"/></g>
  <g id="157">
   <use class="kv10" height="26" transform="rotate(0,518.342,911.743) scale(-0.838049,0.927421) translate(-1137.82,70.4085)" width="12" x="513.3138952120792" xlink:href="#Accessory:避雷器1_0" y="899.6864428918773" zvalue="144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454717276163" ObjectName="10kV弄坎线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,518.342,911.743) scale(-0.838049,0.927421) translate(-1137.82,70.4085)" width="12" x="513.3138952120792" y="899.6864428918773"/></g>
  <g id="431">
   <use class="kv10" height="26" transform="rotate(0,1686.52,919.531) scale(-0.838049,0.927421) translate(-3699.94,71.018)" width="12" x="1681.495713393898" xlink:href="#Accessory:避雷器1_0" y="907.4743315513825" zvalue="158"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454719700995" ObjectName="10kV2号电容器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1686.52,919.531) scale(-0.838049,0.927421) translate(-3699.94,71.018)" width="12" x="1681.495713393898" y="907.4743315513825"/></g>
  <g id="15">
   <use class="kv35" height="26" transform="rotate(90,638.592,125.823) scale(0.838049,0.927421) translate(122.435,8.90327)" width="12" x="633.5638952120792" xlink:href="#Accessory:避雷器1_0" y="113.7667365889811" zvalue="312"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454714523651" ObjectName="35kV允姐线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,638.592,125.823) scale(0.838049,0.927421) translate(122.435,8.90327)" width="12" x="633.5638952120792" y="113.7667365889811"/></g>
  <g id="35">
   <use class="kv35" height="40" transform="rotate(90,750.876,137.172) scale(1.12267,1.12267) translate(-80.2058,-12.535)" width="30" x="734.035714285714" xlink:href="#Accessory:带熔断器的线路PT1_0" y="114.7189440993789" zvalue="319"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454715047939" ObjectName="35kV岭嘎线3319PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,750.876,137.172) scale(1.12267,1.12267) translate(-80.2058,-12.535)" width="30" x="734.035714285714" y="114.7189440993789"/></g>
  <g id="85">
   <use class="kv35" height="26" transform="rotate(90,942.592,123.823) scale(0.838049,0.927421) translate(181.182,8.74675)" width="12" x="937.5638952120792" xlink:href="#Accessory:避雷器1_0" y="111.7667365889811" zvalue="340"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454715441155" ObjectName="35kV勐中线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,942.592,123.823) scale(0.838049,0.927421) translate(181.182,8.74675)" width="12" x="937.5638952120792" y="111.7667365889811"/></g>
  <g id="81">
   <use class="kv35" height="40" transform="rotate(90,1054.88,135.172) scale(1.12267,1.12267) translate(-113.423,-12.3165)" width="30" x="1038.035714285714" xlink:href="#Accessory:带熔断器的线路PT1_0" y="112.7189440993789" zvalue="345"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454715244547" ObjectName="35kV勐中线3329PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1054.88,135.172) scale(1.12267,1.12267) translate(-113.423,-12.3165)" width="30" x="1038.035714285714" y="112.7189440993789"/></g>
  <g id="171">
   <use class="kv10" height="26" transform="rotate(270,773.164,572.252) scale(-0.838049,0.927421) translate(-1696.71,43.8403)" width="12" x="768.1353237835078" xlink:href="#Accessory:避雷器1_0" y="560.1953080175524" zvalue="375"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454716424195" ObjectName="#1主变低压侧001避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,773.164,572.252) scale(-0.838049,0.927421) translate(-1696.71,43.8403)" width="12" x="768.1353237835078" y="560.1953080175524"/></g>
  <g id="184">
   <use class="kv10" height="26" transform="rotate(270,1304.02,572.252) scale(-0.838049,0.927421) translate(-2861.01,43.8403)" width="12" x="1298.99246664065" xlink:href="#Accessory:避雷器1_0" y="560.1953080175524" zvalue="379"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454716489731" ObjectName="#2主变低压侧002避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1304.02,572.252) scale(-0.838049,0.927421) translate(-2861.01,43.8403)" width="12" x="1298.99246664065" y="560.1953080175524"/></g>
  <g id="106">
   <use class="kv10" height="26" transform="rotate(0,634.342,911.743) scale(-0.838049,0.927421) translate(-1392.24,70.4085)" width="12" x="629.3138952120792" xlink:href="#Accessory:避雷器1_0" y="899.6864428918773" zvalue="399"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454717603843" ObjectName="10kV嘎棒线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,634.342,911.743) scale(-0.838049,0.927421) translate(-1392.24,70.4085)" width="12" x="629.3138952120792" y="899.6864428918773"/></g>
  <g id="205">
   <use class="kv10" height="30" transform="rotate(0,1832.39,577.103) scale(1.25,-1.25) translate(-362.727,-1035.03)" width="30" x="1813.635075437912" xlink:href="#Accessory:避雷器PT带熔断器_0" y="558.3525499727831" zvalue="502"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454719897603" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1832.39,577.103) scale(1.25,-1.25) translate(-362.727,-1035.03)" width="30" x="1813.635075437912" y="558.3525499727831"/></g>
  <g id="315">
   <use class="kv10" height="26" transform="rotate(0,750.342,911.743) scale(-0.838049,0.927421) translate(-1646.66,70.4085)" width="12" x="745.3138952120792" xlink:href="#Accessory:避雷器1_0" y="899.6864428918773" zvalue="582"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454717931523" ObjectName="10kV嘎中线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,750.342,911.743) scale(-0.838049,0.927421) translate(-1646.66,70.4085)" width="12" x="745.3138952120792" y="899.6864428918773"/></g>
  <g id="335">
   <use class="kv10" height="26" transform="rotate(0,870.342,911.743) scale(-0.838049,0.927421) translate(-1909.85,70.4085)" width="12" x="865.3138952120793" xlink:href="#Accessory:避雷器1_0" y="899.6864428918773" zvalue="603"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454718259203" ObjectName="10kV黑山门东段线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,870.342,911.743) scale(-0.838049,0.927421) translate(-1909.85,70.4085)" width="12" x="865.3138952120793" y="899.6864428918773"/></g>
  <g id="358">
   <use class="kv10" height="26" transform="rotate(0,1217.84,911.743) scale(-0.838049,0.927421) translate(-2672,70.4085)" width="12" x="1212.813895212079" xlink:href="#Accessory:避雷器1_0" y="899.6864428918773" zvalue="627"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454718586883" ObjectName="10kV遮冒线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1217.84,911.743) scale(-0.838049,0.927421) translate(-2672,70.4085)" width="12" x="1212.813895212079" y="899.6864428918773"/></g>
  <g id="376">
   <use class="kv10" height="26" transform="rotate(0,1337.84,911.743) scale(-0.838049,0.927421) translate(-2935.19,70.4085)" width="12" x="1332.813895212079" xlink:href="#Accessory:避雷器1_0" y="899.6864428918773" zvalue="646"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454718914563" ObjectName="10kV遮放水泥厂线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1337.84,911.743) scale(-0.838049,0.927421) translate(-2935.19,70.4085)" width="12" x="1332.813895212079" y="899.6864428918773"/></g>
  <g id="394">
   <use class="kv10" height="26" transform="rotate(0,1457.84,911.743) scale(-0.838049,0.927421) translate(-3198.38,70.4085)" width="12" x="1452.813895212079" xlink:href="#Accessory:避雷器1_0" y="899.6864428918773" zvalue="665"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454719242243" ObjectName="10kV陇遮水泥厂线线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1457.84,911.743) scale(-0.838049,0.927421) translate(-3198.38,70.4085)" width="12" x="1452.813895212079" y="899.6864428918773"/></g>
  <g id="413">
   <use class="kv10" height="26" transform="rotate(0,1577.84,911.743) scale(-0.838049,0.927421) translate(-3461.57,70.4085)" width="12" x="1572.813895212079" xlink:href="#Accessory:避雷器1_0" y="899.6864428918773" zvalue="685"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454719569923" ObjectName="10kV户拉线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1577.84,911.743) scale(-0.838049,0.927421) translate(-3461.57,70.4085)" width="12" x="1572.813895212079" y="899.6864428918773"/></g>
  <g id="210">
   <use class="kv10" height="30" transform="rotate(180,514.019,568.607) scale(-1.25,1.25) translate(-921.484,-109.971)" width="30" x="495.2690628964385" xlink:href="#Accessory:避雷器PT带熔断器_0" y="549.8571428571429" zvalue="754"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454720356355" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,514.019,568.607) scale(-1.25,1.25) translate(-921.484,-109.971)" width="30" x="495.2690628964385" y="549.8571428571429"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="533">
   <use class="kv35" height="30" transform="rotate(180,1440.31,468.276) scale(1.53571,-1.53571) translate(-494.933,-765.164)" width="28" x="1418.808441293705" xlink:href="#EnergyConsumer:站用变DY接地_0" y="445.2402588732833" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454716882947" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1440.31,468.276) scale(1.53571,-1.53571) translate(-494.933,-765.164)" width="28" x="1418.808441293705" y="445.2402588732833"/></g>
  <g id="155">
   <use class="kv10" height="30" transform="rotate(0,559.572,928.607) scale(1.25,-1.25) translate(-110.414,-1667.74)" width="12" x="552.0722438267301" xlink:href="#EnergyConsumer:负荷_0" y="909.8571428571428" zvalue="145"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454717210627" ObjectName="10kV弄坎线"/>
   <cge:TPSR_Ref TObjectID="6192454717210627"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,559.572,928.607) scale(1.25,-1.25) translate(-110.414,-1667.74)" width="12" x="552.0722438267301" y="909.8571428571428"/></g>
  <g id="314">
   <use class="kv10" height="30" transform="rotate(0,791.572,928.607) scale(1.25,-1.25) translate(-156.814,-1667.74)" width="12" x="784.07224382673" xlink:href="#EnergyConsumer:负荷_0" y="909.8571428571428" zvalue="583"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454717865987" ObjectName="10kV嘎中线"/>
   <cge:TPSR_Ref TObjectID="6192454717865987"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,791.572,928.607) scale(1.25,-1.25) translate(-156.814,-1667.74)" width="12" x="784.07224382673" y="909.8571428571428"/></g>
  <g id="334">
   <use class="kv10" height="30" transform="rotate(0,911.572,928.607) scale(1.25,-1.25) translate(-180.814,-1667.74)" width="12" x="904.0722438267301" xlink:href="#EnergyConsumer:负荷_0" y="909.8571428571428" zvalue="604"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454718193667" ObjectName="10kV黑山门东段线"/>
   <cge:TPSR_Ref TObjectID="6192454718193667"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,911.572,928.607) scale(1.25,-1.25) translate(-180.814,-1667.74)" width="12" x="904.0722438267301" y="909.8571428571428"/></g>
  <g id="357">
   <use class="kv10" height="30" transform="rotate(0,1259.07,928.607) scale(1.25,-1.25) translate(-250.314,-1667.74)" width="12" x="1251.57224382673" xlink:href="#EnergyConsumer:负荷_0" y="909.8571428571428" zvalue="628"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454718521347" ObjectName="10kV遮冒线"/>
   <cge:TPSR_Ref TObjectID="6192454718521347"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1259.07,928.607) scale(1.25,-1.25) translate(-250.314,-1667.74)" width="12" x="1251.57224382673" y="909.8571428571428"/></g>
  <g id="375">
   <use class="kv10" height="30" transform="rotate(0,1379.07,928.607) scale(1.25,-1.25) translate(-274.314,-1667.74)" width="12" x="1371.57224382673" xlink:href="#EnergyConsumer:负荷_0" y="909.8571428571428" zvalue="647"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454718849027" ObjectName="10kV遮放水泥厂线"/>
   <cge:TPSR_Ref TObjectID="6192454718849027"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1379.07,928.607) scale(1.25,-1.25) translate(-274.314,-1667.74)" width="12" x="1371.57224382673" y="909.8571428571428"/></g>
  <g id="393">
   <use class="kv10" height="30" transform="rotate(0,1499.07,928.607) scale(1.25,-1.25) translate(-298.314,-1667.74)" width="12" x="1491.57224382673" xlink:href="#EnergyConsumer:负荷_0" y="909.8571428571428" zvalue="666"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454719176707" ObjectName="10kV陇遮水泥厂线"/>
   <cge:TPSR_Ref TObjectID="6192454719176707"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1499.07,928.607) scale(1.25,-1.25) translate(-298.314,-1667.74)" width="12" x="1491.57224382673" y="909.8571428571428"/></g>
  <g id="412">
   <use class="kv10" height="30" transform="rotate(0,1619.07,928.607) scale(1.25,-1.25) translate(-322.314,-1667.74)" width="12" x="1611.57224382673" xlink:href="#EnergyConsumer:负荷_0" y="909.8571428571428" zvalue="686"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454719504387" ObjectName="10kV户拉线"/>
   <cge:TPSR_Ref TObjectID="6192454719504387"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1619.07,928.607) scale(1.25,-1.25) translate(-322.314,-1667.74)" width="12" x="1611.57224382673" y="909.8571428571428"/></g>
  <g id="58">
   <use class="kv10" height="30" transform="rotate(180,1793.88,850.276) scale(1.53571,-1.53571) translate(-618.272,-1395.91)" width="28" x="1772.379115938563" xlink:href="#EnergyConsumer:站用变DY接地_0" y="827.2402588732832" zvalue="1044"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450269937669" ObjectName="10kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1793.88,850.276) scale(1.53571,-1.53571) translate(-618.272,-1395.91)" width="28" x="1772.379115938563" y="827.2402588732832"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="815">
   <use class="kv35" height="30" transform="rotate(0,1260.87,381.229) scale(0.947693,-0.6712) translate(69.2005,-954.143)" width="15" x="1253.765724821555" xlink:href="#Disconnector:刀闸_0" y="371.1614816563945" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454714392579" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454714392579"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1260.87,381.229) scale(0.947693,-0.6712) translate(69.2005,-954.143)" width="15" x="1253.765724821555" y="371.1614816563945"/></g>
  <g id="53">
   <use class="kv35" height="30" transform="rotate(0,1440.29,390.481) scale(1,1) translate(0,0)" width="15" x="1432.785714285714" xlink:href="#Disconnector:令克_0" y="375.480518613543" zvalue="171"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454716817411" ObjectName="35kV1号站用变3531隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454716817411"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1440.29,390.481) scale(1,1) translate(0,0)" width="15" x="1432.785714285714" y="375.480518613543"/></g>
  <g id="68">
   <use class="kv35" height="30" transform="rotate(0,672.997,193.633) scale(-0.947693,0.6712) translate(-1383.53,89.9225)" width="15" x="665.8892071778711" xlink:href="#Disconnector:刀闸_0" y="183.5647102362043" zvalue="301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454714785795" ObjectName="35kV岭嘎线3316隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454714785795"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,672.997,193.633) scale(-0.947693,0.6712) translate(-1383.53,89.9225)" width="15" x="665.8892071778711" y="183.5647102362043"/></g>
  <g id="20">
   <use class="kv35" height="30" transform="rotate(180,673.178,316.901) scale(0.947693,-0.6712) translate(36.7631,-793.975)" width="15" x="666.0698477117098" xlink:href="#Disconnector:刀闸_0" y="306.8334480230641" zvalue="306"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454714589187" ObjectName="35kV岭嘎线3311隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454714589187"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,673.178,316.901) scale(0.947693,-0.6712) translate(36.7631,-793.975)" width="15" x="666.0698477117098" y="306.8334480230641"/></g>
  <g id="46">
   <use class="kv35" height="30" transform="rotate(90,714.307,137.582) scale(0.947693,0.6712) translate(39.0333,62.4649)" width="15" x="707.1997565308591" xlink:href="#Disconnector:刀闸_0" y="127.513774186895" zvalue="321"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454715113475" ObjectName="35kV岭嘎线3319隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454715113475"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,714.307,137.582) scale(0.947693,0.6712) translate(39.0333,62.4649)" width="15" x="707.1997565308591" y="127.513774186895"/></g>
  <g id="94">
   <use class="kv35" height="30" transform="rotate(0,976.997,191.633) scale(-0.947693,0.6712) translate(-2008.31,88.9427)" width="15" x="969.8892071778711" xlink:href="#Disconnector:刀闸_0" y="181.5647102362043" zvalue="329"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454715703299" ObjectName="35kV勐中线3326隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454715703299"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,976.997,191.633) scale(-0.947693,0.6712) translate(-2008.31,88.9427)" width="15" x="969.8892071778711" y="181.5647102362043"/></g>
  <g id="90">
   <use class="kv35" height="30" transform="rotate(180,977.178,314.901) scale(0.947693,-0.6712) translate(53.5422,-788.995)" width="15" x="970.0698477117098" xlink:href="#Disconnector:刀闸_0" y="304.8334480230641" zvalue="334"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454715506691" ObjectName="35kV勐中线3321隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454715506691"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,977.178,314.901) scale(0.947693,-0.6712) translate(53.5422,-788.995)" width="15" x="970.0698477117098" y="304.8334480230641"/></g>
  <g id="80">
   <use class="kv35" height="30" transform="rotate(90,1018.31,135.582) scale(0.947693,0.6712) translate(55.8123,61.4852)" width="15" x="1011.199756530859" xlink:href="#Disconnector:刀闸_0" y="125.513774186895" zvalue="346"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454715179011" ObjectName="35kV勐中线3329隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454715179011"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1018.31,135.582) scale(0.947693,0.6712) translate(55.8123,61.4852)" width="15" x="1011.199756530859" y="125.513774186895"/></g>
  <g id="133">
   <use class="kv35" height="30" transform="rotate(0,734.302,385.515) scale(0.947693,-0.6712) translate(40.1369,-964.814)" width="15" x="727.194296250127" xlink:href="#Disconnector:刀闸_0" y="375.4471959421087" zvalue="367"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454716358659" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454716358659"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,734.302,385.515) scale(0.947693,-0.6712) translate(40.1369,-964.814)" width="15" x="727.194296250127" y="375.4471959421087"/></g>
  <g id="216">
   <use class="kv10" height="30" transform="rotate(180,559.68,846.637) scale(-0.947693,0.6712) translate(-1150.64,409.808)" width="15" x="552.5723639013065" xlink:href="#Disconnector:刀闸_0" y="836.5691081582961" zvalue="392"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454717145091" ObjectName="10kV弄坎线0326隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454717145091"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,559.68,846.637) scale(-0.947693,0.6712) translate(-1150.64,409.808)" width="15" x="552.5723639013065" y="836.5691081582961"/></g>
  <g id="43">
   <use class="kv10" height="30" transform="rotate(180,675.68,846.637) scale(-0.947693,0.6712) translate(-1389.05,409.808)" width="15" x="668.5723639013065" xlink:href="#Disconnector:刀闸_0" y="836.5691081582961" zvalue="410"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454717472771" ObjectName="10kV嘎棒线0336隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454717472771"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,675.68,846.637) scale(-0.947693,0.6712) translate(-1389.05,409.808)" width="15" x="668.5723639013065" y="836.5691081582961"/></g>
  <g id="272">
   <use class="kv10" height="26" transform="rotate(180,1836.6,640.331) scale(1.42857,1.42857) translate(-547.981,-186.528)" width="14" x="1826.603896103896" xlink:href="#Disconnector:联体手车刀闸_0" y="621.7597402597403" zvalue="532"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454719832067" ObjectName="10kVⅡ段母线电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454719832067"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1836.6,640.331) scale(1.42857,1.42857) translate(-547.981,-186.528)" width="14" x="1826.603896103896" y="621.7597402597403"/></g>
  <g id="273">
   <use class="kv10" height="26" transform="rotate(0,1022.37,773.172) scale(1.42857,1.42857) translate(-303.711,-226.38)" width="14" x="1012.369047619048" xlink:href="#Disconnector:联体手车刀闸_0" y="754.6006493506493" zvalue="534"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454720421891" ObjectName="10kV分段0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454720421891"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1022.37,773.172) scale(1.42857,1.42857) translate(-303.711,-226.38)" width="14" x="1012.369047619048" y="754.6006493506493"/></g>
  <g id="304">
   <use class="kv35" height="30" transform="rotate(180,1477.85,287.401) scale(0.947693,-0.6712) translate(81.1764,-720.524)" width="15" x="1470.744365079853" xlink:href="#Disconnector:刀闸_0" y="277.3334480230641" zvalue="567"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454716948483" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454716948483"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1477.85,287.401) scale(0.947693,-0.6712) translate(81.1764,-720.524)" width="15" x="1470.744365079853" y="277.3334480230641"/></g>
  <g id="298">
   <use class="kv10" height="30" transform="rotate(180,791.68,846.637) scale(-0.947693,0.6712) translate(-1627.45,409.808)" width="15" x="784.5723639013065" xlink:href="#Disconnector:刀闸_0" y="836.5691081582961" zvalue="593"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454717800451" ObjectName="10kV嘎中线0346隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454717800451"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,791.68,846.637) scale(-0.947693,0.6712) translate(-1627.45,409.808)" width="15" x="784.5723639013065" y="836.5691081582961"/></g>
  <g id="326">
   <use class="kv10" height="30" transform="rotate(180,911.68,846.637) scale(-0.947693,0.6712) translate(-1874.07,409.808)" width="15" x="904.5723639013066" xlink:href="#Disconnector:刀闸_0" y="836.5691081582961" zvalue="614"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454718128131" ObjectName="10kV黑山门东段线0356隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454718128131"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,911.68,846.637) scale(-0.947693,0.6712) translate(-1874.07,409.808)" width="15" x="904.5723639013066" y="836.5691081582961"/></g>
  <g id="349">
   <use class="kv10" height="30" transform="rotate(180,1259.18,846.637) scale(-0.947693,0.6712) translate(-2588.25,409.808)" width="15" x="1252.072363901307" xlink:href="#Disconnector:刀闸_0" y="836.5691081582961" zvalue="638"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454718455811" ObjectName="10kV遮冒线0366隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454718455811"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1259.18,846.637) scale(-0.947693,0.6712) translate(-2588.25,409.808)" width="15" x="1252.072363901307" y="836.5691081582961"/></g>
  <g id="367">
   <use class="kv10" height="30" transform="rotate(180,1379.18,846.637) scale(-0.947693,0.6712) translate(-2834.88,409.808)" width="15" x="1372.072363901307" xlink:href="#Disconnector:刀闸_0" y="836.5691081582961" zvalue="657"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454718783491" ObjectName="10kV遮放水泥厂线0376隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454718783491"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1379.18,846.637) scale(-0.947693,0.6712) translate(-2834.88,409.808)" width="15" x="1372.072363901307" y="836.5691081582961"/></g>
  <g id="385">
   <use class="kv10" height="30" transform="rotate(180,1499.18,846.637) scale(-0.947693,0.6712) translate(-3081.5,409.808)" width="15" x="1492.072363901306" xlink:href="#Disconnector:刀闸_0" y="836.5691081582961" zvalue="676"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454719111171" ObjectName="10kV陇遮水泥厂线0386隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454719111171"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1499.18,846.637) scale(-0.947693,0.6712) translate(-3081.5,409.808)" width="15" x="1492.072363901306" y="836.5691081582961"/></g>
  <g id="404">
   <use class="kv10" height="30" transform="rotate(180,1619.18,846.637) scale(-0.947693,0.6712) translate(-3328.12,409.808)" width="15" x="1612.072363901307" xlink:href="#Disconnector:刀闸_0" y="836.5691081582961" zvalue="696"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454719438851" ObjectName="10kV户拉线0396隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454719438851"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1619.18,846.637) scale(-0.947693,0.6712) translate(-3328.12,409.808)" width="15" x="1612.072363901307" y="836.5691081582961"/></g>
  <g id="202">
   <use class="kv10" height="26" transform="rotate(0,512.619,649.095) scale(1.42857,1.42857) translate(-150.786,-189.157)" width="14" x="502.6190476190476" xlink:href="#Disconnector:联体手车刀闸_0" y="630.5238095238095" zvalue="750"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454720290819" ObjectName="10kVⅠ段母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454720290819"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,512.619,649.095) scale(1.42857,1.42857) translate(-150.786,-189.157)" width="14" x="502.6190476190476" y="630.5238095238095"/></g>
  <g id="40">
   <use class="kv10" height="36" transform="rotate(180,1793.74,763.621) scale(2.04441,1.38549) translate(-909.039,-205.525)" width="14" x="1779.427174202544" xlink:href="#Disconnector:联体手车刀闸1_0" y="738.6827116379602" zvalue="1038"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450270003205" ObjectName="10kV2号站用变0412隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450270003205"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(180,1793.74,763.621) scale(2.04441,1.38549) translate(-909.039,-205.525)" width="14" x="1779.427174202544" y="738.6827116379602"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="98">
   <path class="kv35" d="M 1260.93 371.33 L 1260.93 351.75" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="815@1" LinkObjectIDznd="209@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1260.93 371.33 L 1260.93 351.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv35" d="M 1260.77 414.13 L 1260.77 390.96" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="816@1" LinkObjectIDznd="815@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1260.77 414.13 L 1260.77 390.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv35" d="M 1260.93 440.02 L 1260.93 467.76" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="816@0" LinkObjectIDznd="822@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1260.93 440.02 L 1260.93 467.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv10" d="M 447.74 827.91 L 423.5 827.91 L 423.5 842.83" stroke-width="1" zvalue="170"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="428" LinkObjectIDznd="30@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 447.74 827.91 L 423.5 827.91 L 423.5 842.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv35" d="M 1440.37 377.23 L 1440.37 351.75" stroke-width="1" zvalue="172"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="209@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1440.37 377.23 L 1440.37 351.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv35" d="M 672.94 246.11 L 672.94 203.53" stroke-width="1" zvalue="305"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="68@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 672.94 246.11 L 672.94 203.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv35" d="M 672.91 183.9 L 672.91 111.65" stroke-width="1" zvalue="308"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="70@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 672.91 183.9 L 672.91 111.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv35" d="M 650.06 125.85 L 672.91 125.85" stroke-width="1" zvalue="309"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15@0" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 650.06 125.85 L 672.91 125.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv35" d="M 673.09 272 L 673.09 307.17" stroke-width="1" zvalue="310"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@1" LinkObjectIDznd="20@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 673.09 272 L 673.09 307.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv35" d="M 692.97 158.42 L 672.91 158.42" stroke-width="1" zvalue="311"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 692.97 158.42 L 672.91 158.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv35" d="M 673.12 326.8 L 673.12 351.75" stroke-width="1" zvalue="313"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@1" LinkObjectIDznd="209@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 673.12 326.8 L 673.12 351.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv35" d="M 692.97 286.42 L 673.09 286.42" stroke-width="1" zvalue="317"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="17" MaxPinNum="2"/>
   </metadata>
  <path d="M 692.97 286.42 L 673.09 286.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv35" d="M 724.04 137.66 L 730.11 137.66" stroke-width="1" zvalue="322"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 724.04 137.66 L 730.11 137.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv35" d="M 704.41 137.64 L 672.91 137.64" stroke-width="1" zvalue="323"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@1" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 704.41 137.64 L 672.91 137.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv35" d="M 976.94 244.11 L 976.94 201.53" stroke-width="1" zvalue="333"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="94@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 976.94 244.11 L 976.94 201.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv35" d="M 976.91 181.9 L 976.91 109.65" stroke-width="1" zvalue="336"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="100@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 976.91 181.9 L 976.91 109.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv35" d="M 954.06 123.85 L 976.91 123.85" stroke-width="1" zvalue="337"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@0" LinkObjectIDznd="89" MaxPinNum="2"/>
   </metadata>
  <path d="M 954.06 123.85 L 976.91 123.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv35" d="M 977.09 270 L 977.09 305.17" stroke-width="1" zvalue="338"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@1" LinkObjectIDznd="90@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 977.09 270 L 977.09 305.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv35" d="M 996.97 156.42 L 976.91 156.42" stroke-width="1" zvalue="339"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="89" MaxPinNum="2"/>
   </metadata>
  <path d="M 996.97 156.42 L 976.91 156.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv35" d="M 977.12 324.8 L 977.12 351.75" stroke-width="1" zvalue="341"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@1" LinkObjectIDznd="209@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 977.12 324.8 L 977.12 351.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv35" d="M 996.97 284.42 L 977.09 284.42" stroke-width="1" zvalue="344"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@0" LinkObjectIDznd="87" MaxPinNum="2"/>
   </metadata>
  <path d="M 996.97 284.42 L 977.09 284.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv35" d="M 1028.04 135.66 L 1034.11 135.66" stroke-width="1" zvalue="348"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="81@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1028.04 135.66 L 1034.11 135.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv35" d="M 1008.41 135.64 L 976.91 135.64" stroke-width="1" zvalue="349"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@1" LinkObjectIDznd="89" MaxPinNum="2"/>
   </metadata>
  <path d="M 1008.41 135.64 L 976.91 135.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv35" d="M 1513.79 250.24 L 1477.77 250.24" stroke-width="1" zvalue="355"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="305" MaxPinNum="2"/>
   </metadata>
  <path d="M 1513.79 250.24 L 1477.77 250.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv35" d="M 1513.79 318.24 L 1477.79 318.24" stroke-width="1" zvalue="359"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="306" MaxPinNum="2"/>
   </metadata>
  <path d="M 1513.79 318.24 L 1477.79 318.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="kv35" d="M 1297.88 397.6 L 1260.77 397.6" stroke-width="1" zvalue="362"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="97" MaxPinNum="2"/>
   </metadata>
  <path d="M 1297.88 397.6 L 1260.77 397.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="kv35" d="M 734.36 375.62 L 734.36 351.75" stroke-width="1" zvalue="369"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@1" LinkObjectIDznd="209@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 734.36 375.62 L 734.36 351.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="kv35" d="M 734.2 414.42 L 734.2 395.25" stroke-width="1" zvalue="370"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@1" LinkObjectIDznd="133@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 734.2 414.42 L 734.2 395.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv35" d="M 771.31 401.89 L 734.2 401.89" stroke-width="1" zvalue="373"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="131" MaxPinNum="2"/>
   </metadata>
  <path d="M 771.31 401.89 L 734.2 401.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv10" d="M 1292.55 572.28 L 1261.51 572.28" stroke-width="1" zvalue="381"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@0" LinkObjectIDznd="424" MaxPinNum="2"/>
   </metadata>
  <path d="M 1292.55 572.28 L 1261.51 572.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv10" d="M 518.31 900.27 L 518.31 883.08 L 559.76 883.08" stroke-width="1" zvalue="390"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="224" MaxPinNum="2"/>
   </metadata>
  <path d="M 518.31 900.27 L 518.31 883.08 L 559.76 883.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="kv10" d="M 559.76 856.37 L 559.76 911.73" stroke-width="1" zvalue="394"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="216@0" LinkObjectIDznd="155@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 559.76 856.37 L 559.76 911.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv10" d="M 595.53 835.26 L 595.51 822.03 L 559.73 822.03" stroke-width="1" zvalue="397"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="239@0" LinkObjectIDznd="434" MaxPinNum="2"/>
   </metadata>
  <path d="M 595.53 835.26 L 595.51 822.03 L 559.73 822.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv10" d="M 634.31 900.27 L 634.31 883.08 L 675.76 883.08" stroke-width="1" zvalue="409"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@0" LinkObjectIDznd="39" MaxPinNum="2"/>
   </metadata>
  <path d="M 634.31 900.27 L 634.31 883.08 L 675.76 883.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv10" d="M 675.76 856.37 L 675.76 911.15" stroke-width="1" zvalue="413"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 675.76 856.37 L 675.76 911.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv10" d="M 711.53 835.26 L 711.51 822.03 L 675.73 822.03" stroke-width="1" zvalue="415"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@0" LinkObjectIDznd="436" MaxPinNum="2"/>
   </metadata>
  <path d="M 711.53 835.26 L 711.51 822.03 L 675.73 822.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="286">
   <path class="kv10" d="M 1022.35 757.03 L 1022.35 697" stroke-width="1" zvalue="548"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="273@0" LinkObjectIDznd="181@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1022.35 757.03 L 1022.35 697" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="293">
   <path class="kv35" d="M 692.97 218.42 L 672.94 218.42" stroke-width="1" zvalue="555"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="294@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 692.97 218.42 L 672.94 218.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="296">
   <path class="kv35" d="M 996.97 216.42 L 976.94 216.42" stroke-width="1" zvalue="559"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="297@0" LinkObjectIDznd="91" MaxPinNum="2"/>
   </metadata>
  <path d="M 996.97 216.42 L 976.94 216.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="305">
   <path class="kv35" d="M 1477.77 226.9 L 1477.77 277.67" stroke-width="1" zvalue="568"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@0" LinkObjectIDznd="304@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1477.77 226.9 L 1477.77 277.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="306">
   <path class="kv35" d="M 1477.79 297.3 L 1477.79 351.75" stroke-width="1" zvalue="569"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="304@1" LinkObjectIDznd="209@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1477.79 297.3 L 1477.79 351.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="303">
   <path class="kv10" d="M 750.31 900.27 L 750.31 883.08 L 791.76 883.08" stroke-width="1" zvalue="592"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="315@0" LinkObjectIDznd="277" MaxPinNum="2"/>
   </metadata>
  <path d="M 750.31 900.27 L 750.31 883.08 L 791.76 883.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="277">
   <path class="kv10" d="M 791.76 856.37 L 791.76 911.73" stroke-width="1" zvalue="596"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="298@0" LinkObjectIDznd="314@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 791.76 856.37 L 791.76 911.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="275">
   <path class="kv10" d="M 827.53 835.26 L 827.51 822.03 L 791.73 822.03" stroke-width="1" zvalue="598"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="438" MaxPinNum="2"/>
   </metadata>
  <path d="M 827.53 835.26 L 827.51 822.03 L 791.73 822.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="327">
   <path class="kv10" d="M 870.31 900.27 L 870.31 883.08 L 911.76 883.08" stroke-width="1" zvalue="613"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="335@0" LinkObjectIDznd="324" MaxPinNum="2"/>
   </metadata>
  <path d="M 870.31 900.27 L 870.31 883.08 L 911.76 883.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="324">
   <path class="kv10" d="M 911.76 856.37 L 911.76 911.73" stroke-width="1" zvalue="617"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="326@0" LinkObjectIDznd="334@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 911.76 856.37 L 911.76 911.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="322">
   <path class="kv10" d="M 947.53 835.26 L 947.51 822.03 L 911.73 822.03" stroke-width="1" zvalue="619"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="323@0" LinkObjectIDznd="440" MaxPinNum="2"/>
   </metadata>
  <path d="M 947.53 835.26 L 947.51 822.03 L 911.73 822.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="350">
   <path class="kv10" d="M 1217.81 900.27 L 1217.81 883.08 L 1259.26 883.08" stroke-width="1" zvalue="637"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="358@0" LinkObjectIDznd="347" MaxPinNum="2"/>
   </metadata>
  <path d="M 1217.81 900.27 L 1217.81 883.08 L 1259.26 883.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="347">
   <path class="kv10" d="M 1259.26 856.37 L 1259.26 911.73" stroke-width="1" zvalue="641"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="349@0" LinkObjectIDznd="357@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1259.26 856.37 L 1259.26 911.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="345">
   <path class="kv10" d="M 1295.03 835.26 L 1295.01 822.03 L 1259.24 822.03" stroke-width="1" zvalue="643"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="346@0" LinkObjectIDznd="444" MaxPinNum="2"/>
   </metadata>
  <path d="M 1295.03 835.26 L 1295.01 822.03 L 1259.24 822.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="368">
   <path class="kv10" d="M 1337.81 900.27 L 1337.81 883.08 L 1379.26 883.08" stroke-width="1" zvalue="656"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="376@0" LinkObjectIDznd="365" MaxPinNum="2"/>
   </metadata>
  <path d="M 1337.81 900.27 L 1337.81 883.08 L 1379.26 883.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="365">
   <path class="kv10" d="M 1379.26 856.37 L 1379.26 911.73" stroke-width="1" zvalue="660"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="367@0" LinkObjectIDznd="375@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1379.26 856.37 L 1379.26 911.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="363">
   <path class="kv10" d="M 1415.03 835.26 L 1415.01 822.03 L 1379.23 822.03" stroke-width="1" zvalue="662"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="364@0" LinkObjectIDznd="446" MaxPinNum="2"/>
   </metadata>
  <path d="M 1415.03 835.26 L 1415.01 822.03 L 1379.23 822.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="386">
   <path class="kv10" d="M 1457.81 900.27 L 1457.81 883.08 L 1499.26 883.08" stroke-width="1" zvalue="675"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="394@0" LinkObjectIDznd="383" MaxPinNum="2"/>
   </metadata>
  <path d="M 1457.81 900.27 L 1457.81 883.08 L 1499.26 883.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="383">
   <path class="kv10" d="M 1499.26 856.37 L 1499.26 911.73" stroke-width="1" zvalue="679"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="385@0" LinkObjectIDznd="393@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1499.26 856.37 L 1499.26 911.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="381">
   <path class="kv10" d="M 1535.03 835.26 L 1535.01 824.93 L 1499.23 824.93" stroke-width="1" zvalue="681"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="382@0" LinkObjectIDznd="448" MaxPinNum="2"/>
   </metadata>
  <path d="M 1535.03 835.26 L 1535.01 824.93 L 1499.23 824.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="405">
   <path class="kv10" d="M 1577.81 900.27 L 1577.81 883.08 L 1619.26 883.08" stroke-width="1" zvalue="695"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="413@0" LinkObjectIDznd="402" MaxPinNum="2"/>
   </metadata>
  <path d="M 1577.81 900.27 L 1577.81 883.08 L 1619.26 883.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="402">
   <path class="kv10" d="M 1619.26 856.37 L 1619.26 911.73" stroke-width="1" zvalue="699"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="404@0" LinkObjectIDznd="412@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1619.26 856.37 L 1619.26 911.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="400">
   <path class="kv10" d="M 1655.03 835.26 L 1655.01 824.93 L 1619.23 824.93" stroke-width="1" zvalue="701"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="401@0" LinkObjectIDznd="450" MaxPinNum="2"/>
   </metadata>
  <path d="M 1655.03 835.26 L 1655.01 824.93 L 1619.23 824.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="kv10" d="M 1692.5 839.83 L 1692.5 829.8 L 1714.74 829.8 L 1714.74 833.8" stroke-width="1" zvalue="710"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@0" LinkObjectIDznd="452" MaxPinNum="2"/>
   </metadata>
  <path d="M 1692.5 839.83 L 1692.5 829.8 L 1714.74 829.8 L 1714.74 833.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv10" d="M 1686.5 908.06 L 1686.5 894.11 L 1714.74 894.11" stroke-width="1" zvalue="714"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="431@0" LinkObjectIDznd="452" MaxPinNum="2"/>
   </metadata>
  <path d="M 1686.5 908.06 L 1686.5 894.11 L 1714.74 894.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv35" d="M 1440.2 402.73 L 1440.17 446.16" stroke-width="1" zvalue="723"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@1" LinkObjectIDznd="533@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1440.2 402.73 L 1440.17 446.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv10" d="M 512.65 697 L 512.65 665.26" stroke-width="1" zvalue="746"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@6" LinkObjectIDznd="202@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 512.65 697 L 512.65 665.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="207">
   <path class="kv10" d="M 512.6 632.95 L 512.6 586.02" stroke-width="1" zvalue="752"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="210@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 512.6 632.95 L 512.6 586.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="424">
   <path class="kv10" d="M 1261.51 600.53 L 1261.51 539.41" stroke-width="1" zvalue="828"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@1" LinkObjectIDznd="822@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1261.51 600.53 L 1261.51 539.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="425">
   <path class="kv10" d="M 733.05 651.47 L 733.05 697" stroke-width="1" zvalue="829"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="461@0" LinkObjectIDznd="181@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.05 651.47 L 733.05 697" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="428">
   <path class="kv10" d="M 447.74 794.29 L 447.74 872.96" stroke-width="1" zvalue="830"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@0" LinkObjectIDznd="126@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 447.74 794.29 L 447.74 872.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="430">
   <path class="kv10" d="M 447.74 748.78 L 447.74 697" stroke-width="1" zvalue="831"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@1" LinkObjectIDznd="181@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 447.74 748.78 L 447.74 697" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="433">
   <path class="kv10" d="M 559.72 748.18 L 559.72 697" stroke-width="1" zvalue="832"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@1" LinkObjectIDznd="181@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 559.72 748.18 L 559.72 697" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="434">
   <path class="kv10" d="M 559.72 793.69 L 559.74 836.74" stroke-width="1" zvalue="833"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@0" LinkObjectIDznd="216@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 559.72 793.69 L 559.74 836.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="435">
   <path class="kv10" d="M 675.72 748.18 L 675.72 697" stroke-width="1" zvalue="834"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@1" LinkObjectIDznd="181@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 675.72 748.18 L 675.72 697" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="436">
   <path class="kv10" d="M 675.72 793.69 L 675.74 836.74" stroke-width="1" zvalue="835"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="43@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 675.72 793.69 L 675.74 836.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="437">
   <path class="kv10" d="M 791.72 748.18 L 791.72 697" stroke-width="1" zvalue="836"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="312@1" LinkObjectIDznd="181@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 791.72 748.18 L 791.72 697" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="438">
   <path class="kv10" d="M 791.72 793.69 L 791.74 836.74" stroke-width="1" zvalue="837"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="312@0" LinkObjectIDznd="298@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 791.72 793.69 L 791.74 836.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="439">
   <path class="kv10" d="M 911.72 748.18 L 911.72 697" stroke-width="1" zvalue="838"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="332@1" LinkObjectIDznd="181@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 911.72 748.18 L 911.72 697" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="440">
   <path class="kv10" d="M 911.72 793.69 L 911.74 836.74" stroke-width="1" zvalue="839"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="332@0" LinkObjectIDznd="326@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 911.72 793.69 L 911.74 836.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="441">
   <path class="kv10" d="M 1173.4 749.87 L 1173.4 695.5" stroke-width="1" zvalue="840"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@1" LinkObjectIDznd="198@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1173.4 749.87 L 1173.4 695.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="442">
   <path class="kv10" d="M 1173.4 788.85 L 1173.4 824.61 L 1022.4 824.61 L 1022.4 789.34" stroke-width="1" zvalue="841"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="273@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1173.4 788.85 L 1173.4 824.61 L 1022.4 824.61 L 1022.4 789.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="443">
   <path class="kv10" d="M 1259.24 748.18 L 1259.24 695.5" stroke-width="1" zvalue="842"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="355@1" LinkObjectIDznd="198@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1259.24 748.18 L 1259.24 695.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="444">
   <path class="kv10" d="M 1259.24 793.69 L 1259.24 836.74" stroke-width="1" zvalue="843"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="355@0" LinkObjectIDznd="349@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1259.24 793.69 L 1259.24 836.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="445">
   <path class="kv10" d="M 1379.22 748.18 L 1379.22 695.5" stroke-width="1" zvalue="844"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="373@1" LinkObjectIDznd="198@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1379.22 748.18 L 1379.22 695.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="446">
   <path class="kv10" d="M 1379.22 793.69 L 1379.24 836.74" stroke-width="1" zvalue="845"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="373@0" LinkObjectIDznd="367@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1379.22 793.69 L 1379.24 836.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="447">
   <path class="kv10" d="M 1499.22 748.18 L 1499.22 695.5" stroke-width="1" zvalue="846"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="391@1" LinkObjectIDznd="198@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1499.22 748.18 L 1499.22 695.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="448">
   <path class="kv10" d="M 1499.22 793.69 L 1499.24 836.74" stroke-width="1" zvalue="847"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="391@0" LinkObjectIDznd="385@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1499.22 793.69 L 1499.24 836.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="449">
   <path class="kv10" d="M 1619.22 748.18 L 1619.22 695.5" stroke-width="1" zvalue="848"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="410@1" LinkObjectIDznd="198@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1619.22 748.18 L 1619.22 695.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="450">
   <path class="kv10" d="M 1619.22 793.69 L 1619.24 836.74" stroke-width="1" zvalue="849"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="410@0" LinkObjectIDznd="404@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1619.22 793.69 L 1619.24 836.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="451">
   <path class="kv10" d="M 1714.74 695.5 L 1714.74 747.18" stroke-width="1" zvalue="850"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@0" LinkObjectIDznd="429@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1714.74 695.5 L 1714.74 747.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="452">
   <path class="kv10" d="M 1714.74 923.52 L 1714.74 792.69" stroke-width="1" zvalue="851"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="419@0" LinkObjectIDznd="429@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1714.74 923.52 L 1714.74 792.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="kv10" d="M 1261.51 646.04 L 1261.51 695.5" stroke-width="1" zvalue="976"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="198@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1261.51 646.04 L 1261.51 695.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv35" d="M 734.35 440.31 L 734.35 476.83" stroke-width="1" zvalue="983"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="290@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 734.35 440.31 L 734.35 476.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv10" d="M 734.54 548.49 L 734.54 605.95" stroke-width="1" zvalue="984"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="290@1" LinkObjectIDznd="461@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 734.54 548.49 L 734.54 605.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="kv10" d="M 761.69 572.28 L 734.54 572.28" stroke-width="1" zvalue="985"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="105" MaxPinNum="2"/>
   </metadata>
  <path d="M 761.69 572.28 L 734.54 572.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="283">
   <path class="kv10" d="M 1836.62 656.47 L 1836.62 695.5" stroke-width="1" zvalue="1021"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="198@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1836.62 656.47 L 1836.62 695.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="284">
   <path class="kv10" d="M 1836.14 594.52 L 1836.14 624.16" stroke-width="1" zvalue="1022"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="272@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1836.14 594.52 L 1836.14 624.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv10" d="M 1793.69 695.5 L 1793.69 739.97" stroke-width="1" zvalue="1040"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@8" LinkObjectIDznd="40@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1793.69 695.5 L 1793.69 739.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv10" d="M 1793.76 787.24 L 1793.74 828.16" stroke-width="1" zvalue="1042"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@0" LinkObjectIDznd="58@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1793.76 787.24 L 1793.74 828.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="822">
   <g id="8220">
    <use class="kv35" height="30" transform="rotate(0,1261.51,503.429) scale(2.50149,2.5619) translate(-739.191,-283.494)" width="24" x="1231.5" xlink:href="#PowerTransformer2:可调不带中性点_0" y="465" zvalue="84"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874588553218" ObjectName="35"/>
    </metadata>
   </g>
   <g id="8221">
    <use class="kv10" height="30" transform="rotate(0,1261.51,503.429) scale(2.50149,2.5619) translate(-739.191,-283.494)" width="24" x="1231.5" xlink:href="#PowerTransformer2:可调不带中性点_1" y="465" zvalue="84"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874588618754" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399531560962" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399531560962"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1261.51,503.429) scale(2.50149,2.5619) translate(-739.191,-283.494)" width="24" x="1231.5" y="465"/></g>
  <g id="290">
   <g id="2900">
    <use class="kv35" height="30" transform="rotate(0,734.538,512.5) scale(2.50149,2.5619) translate(-422.88,-289.025)" width="24" x="704.52" xlink:href="#PowerTransformer2:可调不带中性点_0" y="474.07" zvalue="550"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874588684290" ObjectName="35"/>
    </metadata>
   </g>
   <g id="2901">
    <use class="kv10" height="30" transform="rotate(0,734.538,512.5) scale(2.50149,2.5619) translate(-422.88,-289.025)" width="24" x="704.52" xlink:href="#PowerTransformer2:可调不带中性点_1" y="474.07" zvalue="550"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874588749826" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399531626498" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399531626498"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,734.538,512.5) scale(2.50149,2.5619) translate(-422.88,-289.025)" width="24" x="704.52" y="474.07"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="70">
   <use class="kv35" height="30" transform="rotate(0,672.914,103.889) scale(1.98323,0.522926) translate(-330.17,87.6232)" width="7" x="665.9724184654124" xlink:href="#ACLineSegment:线路_0" y="96.04490745373994" zvalue="297"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249330417666" ObjectName="35kV岭嘎线"/>
   <cge:TPSR_Ref TObjectID="8444249330417666_5066549683486721"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,672.914,103.889) scale(1.98323,0.522926) translate(-330.17,87.6232)" width="7" x="665.9724184654124" y="96.04490745373994"/></g>
  <g id="100">
   <use class="kv35" height="30" transform="rotate(0,976.914,101.889) scale(1.98323,0.522926) translate(-480.885,85.7985)" width="7" x="969.9724184654124" xlink:href="#ACLineSegment:线路_0" y="94.04490745373994" zvalue="325"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249330483204" ObjectName="35kV勐中线"/>
   <cge:TPSR_Ref TObjectID="8444249330483204_5066549683486721"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,976.914,101.889) scale(1.98323,0.522926) translate(-480.885,85.7985)" width="7" x="969.9724184654124" y="94.04490745373994"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="67">
   <use class="kv35" height="20" transform="rotate(90,702.785,158.359) scale(1.24619,-1.0068) translate(-137.606,-315.581)" width="10" x="696.5539414267876" xlink:href="#GroundDisconnector:地刀_0" y="148.2914253286721" zvalue="303"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454714720259" ObjectName="35kV岭嘎线33167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454714720259"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,702.785,158.359) scale(1.24619,-1.0068) translate(-137.606,-315.581)" width="10" x="696.5539414267876" y="148.2914253286721"/></g>
  <g id="33">
   <use class="kv35" height="20" transform="rotate(90,702.785,286.359) scale(1.24619,-1.0068) translate(-137.606,-570.717)" width="10" x="696.5539414267876" xlink:href="#GroundDisconnector:地刀_0" y="276.2914253286721" zvalue="315"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454714982403" ObjectName="35kV岭嘎线33117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454714982403"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,702.785,286.359) scale(1.24619,-1.0068) translate(-137.606,-570.717)" width="10" x="696.5539414267876" y="276.2914253286721"/></g>
  <g id="92">
   <use class="kv35" height="20" transform="rotate(90,1006.78,156.359) scale(1.24619,-1.0068) translate(-197.661,-311.595)" width="10" x="1000.553941426787" xlink:href="#GroundDisconnector:地刀_0" y="146.2914253286721" zvalue="331"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454715637763" ObjectName="35kV勐中线33267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454715637763"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1006.78,156.359) scale(1.24619,-1.0068) translate(-197.661,-311.595)" width="10" x="1000.553941426787" y="146.2914253286721"/></g>
  <g id="83">
   <use class="kv35" height="20" transform="rotate(90,1006.78,284.359) scale(1.24619,-1.0068) translate(-197.661,-566.73)" width="10" x="1000.553941426787" xlink:href="#GroundDisconnector:地刀_0" y="274.2914253286721" zvalue="342"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454715375619" ObjectName="35kV勐中线33217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454715375619"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1006.78,284.359) scale(1.24619,-1.0068) translate(-197.661,-566.73)" width="10" x="1000.553941426787" y="274.2914253286721"/></g>
  <g id="103">
   <use class="kv35" height="20" transform="rotate(90,1523.6,250.178) scale(1.24619,-1.0068) translate(-299.76,-498.597)" width="10" x="1517.372123244969" xlink:href="#GroundDisconnector:地刀_0" y="240.1096071468539" zvalue="353"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454715899907" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454715899907"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1523.6,250.178) scale(1.24619,-1.0068) translate(-299.76,-498.597)" width="10" x="1517.372123244969" y="240.1096071468539"/></g>
  <g id="114">
   <use class="kv35" height="20" transform="rotate(90,1523.6,318.178) scale(1.24619,-1.0068) translate(-299.76,-634.138)" width="10" x="1517.372123244969" xlink:href="#GroundDisconnector:地刀_0" y="308.1096071468538" zvalue="357"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454716030979" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454716030979"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1523.6,318.178) scale(1.24619,-1.0068) translate(-299.76,-634.138)" width="10" x="1517.372123244969" y="308.1096071468538"/></g>
  <g id="115">
   <use class="kv35" height="20" transform="rotate(90,1307.69,397.541) scale(1.24619,-1.0068) translate(-257.107,-792.329)" width="10" x="1301.463032335878" xlink:href="#GroundDisconnector:地刀_0" y="387.4732435104902" zvalue="361"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454716162051" ObjectName="#2主变35kV侧30217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454716162051"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1307.69,397.541) scale(1.24619,-1.0068) translate(-257.107,-792.329)" width="10" x="1301.463032335878" y="387.4732435104902"/></g>
  <g id="129">
   <use class="kv35" height="20" transform="rotate(90,781.123,401.827) scale(1.24619,-1.0068) translate(-153.081,-800.872)" width="10" x="774.8916037644499" xlink:href="#GroundDisconnector:地刀_0" y="391.7589577962045" zvalue="371"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454716293123" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454716293123"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,781.123,401.827) scale(1.24619,-1.0068) translate(-153.081,-800.872)" width="10" x="774.8916037644499" y="391.7589577962045"/></g>
  <g id="239">
   <use class="kv10" height="20" transform="rotate(180,595.595,845.073) scale(1.24619,-1.0068) translate(-116.43,-1684.37)" width="10" x="589.3644838255652" xlink:href="#GroundDisconnector:地刀_0" y="835.0049471010174" zvalue="396"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454717079555" ObjectName="10kV弄坎线03267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454717079555"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,595.595,845.073) scale(1.24619,-1.0068) translate(-116.43,-1684.37)" width="10" x="589.3644838255652" y="835.0049471010174"/></g>
  <g id="34">
   <use class="kv10" height="20" transform="rotate(180,711.595,845.073) scale(1.24619,-1.0068) translate(-139.346,-1684.37)" width="10" x="705.3644838255652" xlink:href="#GroundDisconnector:地刀_0" y="835.0049471010174" zvalue="414"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454717407235" ObjectName="10kV嘎棒线03367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454717407235"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,711.595,845.073) scale(1.24619,-1.0068) translate(-139.346,-1684.37)" width="10" x="705.3644838255652" y="835.0049471010174"/></g>
  <g id="294">
   <use class="kv35" height="20" transform="rotate(90,702.785,218.359) scale(1.24619,-1.0068) translate(-137.606,-435.176)" width="10" x="696.5539414267876" xlink:href="#GroundDisconnector:地刀_0" y="208.2914253286721" zvalue="553"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454716620803" ObjectName="35kV岭嘎线33160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454716620803"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,702.785,218.359) scale(1.24619,-1.0068) translate(-137.606,-435.176)" width="10" x="696.5539414267876" y="208.2914253286721"/></g>
  <g id="297">
   <use class="kv35" height="20" transform="rotate(90,1006.78,216.359) scale(1.24619,-1.0068) translate(-197.661,-431.189)" width="10" x="1000.553941426787" xlink:href="#GroundDisconnector:地刀_0" y="206.2914253286721" zvalue="557"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454716751875" ObjectName="35kV勐中线33260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454716751875"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1006.78,216.359) scale(1.24619,-1.0068) translate(-197.661,-431.189)" width="10" x="1000.553941426787" y="206.2914253286721"/></g>
  <g id="276">
   <use class="kv10" height="20" transform="rotate(180,827.595,845.073) scale(1.24619,-1.0068) translate(-162.262,-1684.37)" width="10" x="821.3644838255652" xlink:href="#GroundDisconnector:地刀_0" y="835.0049471010174" zvalue="597"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454717734915" ObjectName="10kV嘎中线03467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454717734915"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,827.595,845.073) scale(1.24619,-1.0068) translate(-162.262,-1684.37)" width="10" x="821.3644838255652" y="835.0049471010174"/></g>
  <g id="323">
   <use class="kv10" height="20" transform="rotate(180,947.595,845.073) scale(1.24619,-1.0068) translate(-185.968,-1684.37)" width="10" x="941.3644838255652" xlink:href="#GroundDisconnector:地刀_0" y="835.0049471010174" zvalue="618"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454718062595" ObjectName="10kV黑山门东段线03567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454718062595"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,947.595,845.073) scale(1.24619,-1.0068) translate(-185.968,-1684.37)" width="10" x="941.3644838255652" y="835.0049471010174"/></g>
  <g id="346">
   <use class="kv10" height="20" transform="rotate(180,1295.1,845.073) scale(1.24619,-1.0068) translate(-254.618,-1684.37)" width="10" x="1288.864483825565" xlink:href="#GroundDisconnector:地刀_0" y="835.0049471010174" zvalue="642"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454718390275" ObjectName="10kV遮冒线03667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454718390275"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1295.1,845.073) scale(1.24619,-1.0068) translate(-254.618,-1684.37)" width="10" x="1288.864483825565" y="835.0049471010174"/></g>
  <g id="364">
   <use class="kv10" height="20" transform="rotate(180,1415.1,845.073) scale(1.24619,-1.0068) translate(-278.324,-1684.37)" width="10" x="1408.864483825565" xlink:href="#GroundDisconnector:地刀_0" y="835.0049471010174" zvalue="661"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454718717955" ObjectName="10kV遮放水泥厂线03767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454718717955"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1415.1,845.073) scale(1.24619,-1.0068) translate(-278.324,-1684.37)" width="10" x="1408.864483825565" y="835.0049471010174"/></g>
  <g id="382">
   <use class="kv10" height="20" transform="rotate(180,1535.1,845.073) scale(1.24619,-1.0068) translate(-302.03,-1684.37)" width="10" x="1528.864483825565" xlink:href="#GroundDisconnector:地刀_0" y="835.0049471010174" zvalue="680"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454719045635" ObjectName="10kV陇遮水泥厂线03867接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454719045635"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1535.1,845.073) scale(1.24619,-1.0068) translate(-302.03,-1684.37)" width="10" x="1528.864483825565" y="835.0049471010174"/></g>
  <g id="401">
   <use class="kv10" height="20" transform="rotate(180,1655.1,845.073) scale(1.24619,-1.0068) translate(-325.737,-1684.37)" width="10" x="1648.864483825565" xlink:href="#GroundDisconnector:地刀_0" y="835.0049471010174" zvalue="700"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454719373315" ObjectName="10kV户拉线03967接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454719373315"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1655.1,845.073) scale(1.24619,-1.0068) translate(-325.737,-1684.37)" width="10" x="1648.864483825565" y="835.0049471010174"/></g>
  <g id="30">
   <use class="kv10" height="45" transform="rotate(0,423.536,855.107) scale(0.611111,0.611111) translate(260.773,535.409)" width="45" x="409.7857142857142" xlink:href="#GroundDisconnector:地刀带电容_0" y="841.3571428571428" zvalue="726"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454720094211" ObjectName="10kV1号电容器03117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454720094211"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,423.536,855.107) scale(0.611111,0.611111) translate(260.773,535.409)" width="45" x="409.7857142857142" y="841.3571428571428"/></g>
  <g id="48">
   <use class="kv10" height="45" transform="rotate(0,1692.54,852.107) scale(0.611111,0.611111) translate(1068.32,533.5)" width="45" x="1678.785714285714" xlink:href="#GroundDisconnector:地刀带电容_0" y="838.3571428571428" zvalue="728"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454720225283" ObjectName="10kV2号电容器04227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454720225283"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,1692.54,852.107) scale(0.611111,0.611111) translate(1068.32,533.5)" width="45" x="1678.785714285714" y="838.3571428571428"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="126">
   <use class="kv10" height="30" transform="rotate(0,447.738,893.524) scale(1.61905,1.61905) translate(-161.908,-332.356)" width="30" x="423.4523115924427" xlink:href="#Compensator:电容器组2_0" y="869.2380952380952" zvalue="577"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454719963139" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192454719963139"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,447.738,893.524) scale(1.61905,1.61905) translate(-161.908,-332.356)" width="30" x="423.4523115924427" y="869.2380952380952"/></g>
  <g id="419">
   <use class="kv10" height="30" transform="rotate(0,1714.74,947.19) scale(1.61905,1.86349) translate(-646.349,-425.95)" width="30" x="1690.452311592443" xlink:href="#Compensator:电容器组2_0" y="919.2380952380952" zvalue="577"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454719635459" ObjectName="10kV2号电容器"/>
   <cge:TPSR_Ref TObjectID="6192454719635459"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1714.74,947.19) scale(1.61905,1.86349) translate(-646.349,-425.95)" width="30" x="1690.452311592443" y="919.2380952380952"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="237">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="237" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,132.002,524.019) scale(1,1) translate(1.18396e-14,0)" writing-mode="lr" x="131.74" xml:space="preserve" y="528.6900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136121970690" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="236">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,132.002,547.019) scale(1,1) translate(1.18396e-14,1.19113e-13)" writing-mode="lr" x="131.74" xml:space="preserve" y="551.6900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136122036226" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="235">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="235" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,132.002,572.433) scale(1,1) translate(1.18396e-14,-1.24756e-13)" writing-mode="lr" x="131.74" xml:space="preserve" y="577.1" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136122101762" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,132.002,496.582) scale(1,1) translate(1.18396e-14,-1.07914e-13)" writing-mode="lr" x="131.74" xml:space="preserve" y="501.25" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136122232834" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="220">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="220" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137,205.111) scale(1,1) translate(0,0)" writing-mode="lr" x="136.64" xml:space="preserve" y="211.29" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136122363906" ObjectName="F"/>
   </metadata>
  </g>
  <g id="219">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="219" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,251.58,524.019) scale(1,1) translate(2.51154e-14,0)" writing-mode="lr" x="251.32" xml:space="preserve" y="528.6900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136122494978" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="217">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="217" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,251.58,547.019) scale(1,1) translate(2.51154e-14,1.19113e-13)" writing-mode="lr" x="251.32" xml:space="preserve" y="551.6900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136122560514" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="212">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="212" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,251.58,572.433) scale(1,1) translate(2.51154e-14,1.24756e-13)" writing-mode="lr" x="251.32" xml:space="preserve" y="577.1" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136122626050" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="206">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="206" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,251.58,498.519) scale(1,1) translate(2.51154e-14,1.08344e-13)" writing-mode="lr" x="251.32" xml:space="preserve" y="503.19" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136122757122" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="199">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="199" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137,231) scale(1,1) translate(0,0)" writing-mode="lr" x="136.64" xml:space="preserve" y="237.18" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136122888194" ObjectName="F"/>
   </metadata>
  </g>
  <g id="195">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="195" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,314.538,524.019) scale(1,1) translate(3.21051e-14,0)" writing-mode="lr" x="314.27" xml:space="preserve" y="528.6900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136127213570" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="192">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="192" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,313.769,547.019) scale(1,1) translate(3.20197e-14,1.19113e-13)" writing-mode="lr" x="313.51" xml:space="preserve" y="551.6900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136127279106" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="191">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="191" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,313.769,572.433) scale(1,1) translate(3.20197e-14,0)" writing-mode="lr" x="313.51" xml:space="preserve" y="577.1" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136127344642" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="190">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="190" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,312,229.25) scale(1,1) translate(0,0)" writing-mode="lr" x="311.64" xml:space="preserve" y="235.43" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136127606786" ObjectName="F"/>
   </metadata>
  </g>
  <g id="188">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,314.538,498.519) scale(1,1) translate(3.21051e-14,1.08344e-13)" writing-mode="lr" x="314.27" xml:space="preserve" y="503.19" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136127475714" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="182">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="182" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,137,252.194) scale(1,1) translate(0,0)" writing-mode="lr" x="136.64" xml:space="preserve" y="258.38" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136129572866" ObjectName="油温"/>
   </metadata>
  </g>
  <g id="176">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,137,275.194) scale(1,1) translate(0,2.04932e-13)" writing-mode="lr" x="136.64" xml:space="preserve" y="281.38" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136129179650" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="175">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="175" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,312,251.194) scale(1,1) translate(0,0)" writing-mode="lr" x="311.64" xml:space="preserve" y="257.38" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136119808002" ObjectName="油温"/>
   </metadata>
  </g>
  <g id="168">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,312,274.194) scale(1,1) translate(0,3.20814e-13)" writing-mode="lr" x="311.64" xml:space="preserve" y="280.38" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136119414786" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="159">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="159" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,132.002,596.25) scale(1,1) translate(1.18396e-14,0)" writing-mode="lr" x="131.74" xml:space="preserve" y="600.92" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136122429442" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="158">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="158" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,251.58,596.25) scale(1,1) translate(2.51154e-14,0)" writing-mode="lr" x="251.32" xml:space="preserve" y="600.92" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136122953730" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="150" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,313.769,596.25) scale(1,1) translate(3.20197e-14,0)" writing-mode="lr" x="313.51" xml:space="preserve" y="600.92" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136127672322" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="145" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,137,177.5) scale(1,1) translate(0,0)" writing-mode="lr" x="136.64" xml:space="preserve" y="183.68" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136146415618" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="124">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="124" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,312,179.5) scale(1,1) translate(0,0)" writing-mode="lr" x="311.64" xml:space="preserve" y="185.68" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136146481154" ObjectName="LOAD_QSUM"/>
   </metadata>
  </g>
  <g id="455">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="455" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,672.914,15.5449) scale(1,1) translate(0,0)" writing-mode="lr" x="672.4400000000001" xml:space="preserve" y="20.21" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136124067842" ObjectName="P"/>
   </metadata>
  </g>
  <g id="456">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="456" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,976.914,13.5449) scale(1,1) translate(0,0)" writing-mode="lr" x="976.4400000000001" xml:space="preserve" y="18.21" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136125640706" ObjectName="P"/>
   </metadata>
  </g>
  <g id="457">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="457" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,672.914,38.5449) scale(1,1) translate(0,0)" writing-mode="lr" x="672.4400000000001" xml:space="preserve" y="43.21" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136124133378" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="458">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="458" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,976.914,36.5449) scale(1,1) translate(0,0)" writing-mode="lr" x="976.4400000000001" xml:space="preserve" y="41.21" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136125706242" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="459">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="459" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,672.914,61.5449) scale(1,1) translate(0,0)" writing-mode="lr" x="672.4400000000001" xml:space="preserve" y="66.20999999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136124198914" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="460">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="460" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,976.914,59.5449) scale(1,1) translate(0,0)" writing-mode="lr" x="976.4400000000001" xml:space="preserve" y="64.20999999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136125771778" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="463">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="463" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,558.144,995.929) scale(1,1) translate(0,0)" writing-mode="lr" x="557.67" xml:space="preserve" y="1000.6" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136131801090" ObjectName="P"/>
   </metadata>
  </g>
  <g id="465">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="465" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,791.572,997.357) scale(1,1) translate(0,0)" writing-mode="lr" x="791.1" xml:space="preserve" y="1002.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136134684674" ObjectName="P"/>
   </metadata>
  </g>
  <g id="466">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="466" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,911.572,997.357) scale(1,1) translate(0,0)" writing-mode="lr" x="911.1" xml:space="preserve" y="1002.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136136126466" ObjectName="P"/>
   </metadata>
  </g>
  <g id="467">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="467" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1259.07,997.357) scale(1,1) translate(0,0)" writing-mode="lr" x="1258.6" xml:space="preserve" y="1002.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136137568258" ObjectName="P"/>
   </metadata>
  </g>
  <g id="468">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="468" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1379.07,997.357) scale(1,1) translate(0,0)" writing-mode="lr" x="1378.6" xml:space="preserve" y="1002.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136139010050" ObjectName="P"/>
   </metadata>
  </g>
  <g id="469">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="469" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1499.07,997.357) scale(1,1) translate(0,0)" writing-mode="lr" x="1498.6" xml:space="preserve" y="1002.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136140451842" ObjectName="P"/>
   </metadata>
  </g>
  <g id="470">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="470" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1619.07,997.357) scale(1,1) translate(0,0)" writing-mode="lr" x="1618.6" xml:space="preserve" y="1002.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136141893634" ObjectName="P"/>
   </metadata>
  </g>
  <g id="471">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="471" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,559.572,1016.36) scale(1,1) translate(0,0)" writing-mode="lr" x="559.1" xml:space="preserve" y="1021.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136131866626" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="473">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="473" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,791.572,1016.36) scale(1,1) translate(0,0)" writing-mode="lr" x="791.1" xml:space="preserve" y="1021.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136134750210" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="474">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="474" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,911.572,1016.36) scale(1,1) translate(0,0)" writing-mode="lr" x="911.1" xml:space="preserve" y="1021.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136136192002" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="475">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="475" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1259.07,1016.36) scale(1,1) translate(0,0)" writing-mode="lr" x="1258.6" xml:space="preserve" y="1021.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136137633794" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="476">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="476" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1379.07,1016.36) scale(1,1) translate(0,0)" writing-mode="lr" x="1378.6" xml:space="preserve" y="1021.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136139075586" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="477">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="477" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1499.07,1016.36) scale(1,1) translate(0,0)" writing-mode="lr" x="1498.6" xml:space="preserve" y="1021.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136140517378" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="478">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="478" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1619.07,1016.36) scale(1,1) translate(0,0)" writing-mode="lr" x="1618.6" xml:space="preserve" y="1021.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136141959170" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="479">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="479" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,559.572,1039.36) scale(1,1) translate(0,0)" writing-mode="lr" x="559.1" xml:space="preserve" y="1044.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136131932162" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="481">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="481" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,791.572,1039.36) scale(1,1) translate(0,0)" writing-mode="lr" x="791.1" xml:space="preserve" y="1044.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136134815746" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="482">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="482" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,911.572,1039.36) scale(1,1) translate(0,0)" writing-mode="lr" x="911.1" xml:space="preserve" y="1044.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136136257538" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="483">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="483" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1259.07,1039.36) scale(1,1) translate(0,0)" writing-mode="lr" x="1258.6" xml:space="preserve" y="1044.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136137699330" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="484">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="484" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1379.07,1039.36) scale(1,1) translate(0,0)" writing-mode="lr" x="1378.6" xml:space="preserve" y="1044.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136139141122" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="485">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="485" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1499.07,1039.36) scale(1,1) translate(0,0)" writing-mode="lr" x="1498.6" xml:space="preserve" y="1044.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136140582914" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="486">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="486" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1619.07,1039.36) scale(1,1) translate(0,0)" writing-mode="lr" x="1618.6" xml:space="preserve" y="1044.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136142024706" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="487">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="487" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1714.74,1010.64) scale(1,1) translate(0,0)" writing-mode="lr" x="1714.27" xml:space="preserve" y="1015.31" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136142811138" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="488">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="488" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,447.738,1005.31) scale(1,1) translate(0,0)" writing-mode="lr" x="447.27" xml:space="preserve" y="1009.98" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136144646146" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="489">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="489" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1714.74,1033.64) scale(1,1) translate(0,0)" writing-mode="lr" x="1714.27" xml:space="preserve" y="1038.31" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136142876674" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="490">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="490" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,447.738,1028.31) scale(1,1) translate(0,0)" writing-mode="lr" x="447.27" xml:space="preserve" y="1032.98" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136144711682" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="491">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="491" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,635.279,395.355) scale(1,1) translate(0,0)" writing-mode="lr" x="634.7" xml:space="preserve" y="401.05" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136128655362" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="492">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="492" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,635.279,422.355) scale(1,1) translate(0,0)" writing-mode="lr" x="634.7" xml:space="preserve" y="428.05" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136128720898" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="493">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="493" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,635.279,449.355) scale(1,1) translate(0,0)" writing-mode="lr" x="634.7" xml:space="preserve" y="455.05" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136128917506" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="494">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="494" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,639.279,581.332) scale(1,1) translate(0,-1.26306e-13)" writing-mode="lr" x="638.7" xml:space="preserve" y="587.03" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136128786434" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="495">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="495" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,639.279,608.332) scale(1,1) translate(0,0)" writing-mode="lr" x="638.7" xml:space="preserve" y="614.03" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136128851970" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="496">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="496" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,639.279,635.332) scale(1,1) translate(0,0)" writing-mode="lr" x="638.7" xml:space="preserve" y="641.03" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136129245186" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="497">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="497" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1158.01,378.38) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.44" xml:space="preserve" y="384.08" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136118890498" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="498">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="498" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1158.01,405.38) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.44" xml:space="preserve" y="411.08" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136118956034" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="499">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="499" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1158.01,432.38) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.44" xml:space="preserve" y="438.08" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136119152642" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="500">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="500" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1158.01,560.357) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.44" xml:space="preserve" y="566.0599999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136119021570" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="501">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="501" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1158.01,587.357) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.44" xml:space="preserve" y="593.0599999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136119087106" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="502">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="502" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1158.01,614.357) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.44" xml:space="preserve" y="620.0599999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136119480322" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="503">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="503" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1097.68,843.764) scale(1,1) translate(0,0)" writing-mode="lr" x="1097.21" xml:space="preserve" y="848.4299999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136127737858" ObjectName="P"/>
   </metadata>
  </g>
  <g id="504">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="504" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1097.68,865.336) scale(1,1) translate(0,0)" writing-mode="lr" x="1097.21" xml:space="preserve" y="870" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136127803394" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="505">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="505" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1097.68,889.193) scale(1,1) translate(0,0)" writing-mode="lr" x="1097.21" xml:space="preserve" y="893.86" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136127868930" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="221">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="221" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,676,997.357) scale(1,1) translate(0,0)" writing-mode="lr" x="675.53" xml:space="preserve" y="1002.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136133242882" ObjectName="P"/>
   </metadata>
  </g>
  <g id="222">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="222" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,676,1016.36) scale(1,1) translate(0,0)" writing-mode="lr" x="675.53" xml:space="preserve" y="1021.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136133308418" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="223">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="223" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,676,1039.36) scale(1,1) translate(0,-2.74143e-12)" writing-mode="lr" x="675.53" xml:space="preserve" y="1044.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136133373954" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="264">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="264" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,930.361,646.582) scale(1,1) translate(0,-1.4122e-13)" writing-mode="lr" x="930.1" xml:space="preserve" y="651.25" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136122757122" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="265">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="265" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1453.36,656.582) scale(1,1) translate(1.5854e-13,3.58602e-13)" writing-mode="lr" x="1453.1" xml:space="preserve" y="661.25" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136127475714" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="270">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="270" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,501,514) scale(1,1) translate(0,0)" writing-mode="lr" x="457.88" xml:space="preserve" y="518.36" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136122691586" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="279">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="279" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,501,459.4) scale(1,1) translate(0,0)" writing-mode="lr" x="457.88" xml:space="preserve" y="463.76" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136122757122" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="280">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="280" prefix="Ubc:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,501,486.7) scale(1,1) translate(0,0)" writing-mode="lr" x="457.88" xml:space="preserve" y="491.06" zvalue="1">Ubc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136122822658" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="310">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="310" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1836.75,514.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1793.63" xml:space="preserve" y="518.86" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136127410178" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="311">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="311" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1836.75,460.3) scale(1,1) translate(0,0)" writing-mode="lr" x="1793.63" xml:space="preserve" y="464.66" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136127475714" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="313">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="313" prefix="Ubc:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1836.75,487.4) scale(1,1) translate(0,0)" writing-mode="lr" x="1793.63" xml:space="preserve" y="491.76" zvalue="1">Ubc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136127541250" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="328">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="328" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1492.75,153.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1449.63" xml:space="preserve" y="158.11" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136122167298" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="329">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="329" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1492.75,105.95) scale(1,1) translate(0,0)" writing-mode="lr" x="1449.63" xml:space="preserve" y="110.31" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136122232834" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="330">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="330" prefix="Ubc:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1492.75,129.85) scale(1,1) translate(0,0)" writing-mode="lr" x="1449.63" xml:space="preserve" y="134.21" zvalue="1">Ubc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136122298370" ObjectName="Ubc"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,341.625,322.5) scale(0.708333,0.665547) translate(136.294,157.047)" width="30" x="331" xlink:href="#State:红绿圆(方形)_0" y="312.52" zvalue="819"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374927527937" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,341.625,322.5) scale(0.708333,0.665547) translate(136.294,157.047)" width="30" x="331" y="312.52"/></g>
  <g id="1035">
   <use height="30" transform="rotate(0,246,322.5) scale(0.708333,0.665547) translate(96.9191,157.047)" width="30" x="235.38" xlink:href="#State:红绿圆(方形)_0" y="312.52" zvalue="820"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562956067799047" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,246,322.5) scale(0.708333,0.665547) translate(96.9191,157.047)" width="30" x="235.38" y="312.52"/></g>
  <g id="165">
   <use height="30" transform="rotate(0,309.812,128.464) scale(1.27778,1.03333) translate(-54.8505,-3.64399)" width="90" x="252.31" xlink:href="#State:全站检修_0" y="112.96" zvalue="996"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549683486721" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,309.812,128.464) scale(1.27778,1.03333) translate(-54.8505,-3.64399)" width="90" x="252.31" y="112.96"/></g>
  <g id="1082">
   <use height="30" transform="rotate(0,181.286,368.539) scale(0.910937,0.8) translate(14.1618,89.1347)" width="80" x="144.85" xlink:href="#State:间隔模板_0" y="356.54" zvalue="1013"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629500497395714" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,181.286,368.539) scale(0.910937,0.8) translate(14.1618,89.1347)" width="80" x="144.85" y="356.54"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="181">
   <path class="kv10" d="M 420 697 L 1039 697" stroke-width="4" zvalue="963"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674419212291" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674419212291"/></metadata>
  <path d="M 420 697 L 1039 697" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="kv10" d="M 1135.75 695.5 L 1904.75 695.5" stroke-width="4" zvalue="964"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674419277827" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674419277827"/></metadata>
  <path d="M 1135.75 695.5 L 1904.75 695.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv35" d="M 578.75 351.75 L 1596.25 351.75" stroke-width="4" zvalue="965"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674419146755" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674419146755"/></metadata>
  <path d="M 578.75 351.75 L 1596.25 351.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
</svg>