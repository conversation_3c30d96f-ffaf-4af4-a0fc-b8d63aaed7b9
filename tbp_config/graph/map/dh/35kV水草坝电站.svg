<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549581512706" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:三圈带电容111_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="19.95" xlink:href="#terminal" y="2.93333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.16666666666667" x2="22.91666666666667" y1="4.583333333333332" y2="7.416666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="13" y1="34.5" y2="33.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="16.91666666666666" y1="4.499999999999996" y2="7.666666666666663"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="17" y1="28.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="13" y1="28.5" y2="30.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,13) scale(1,1) translate(0,0)" width="4" x="18" y="9.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="23" y1="23.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="23.25" y1="3.5" y2="6.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="16.66666666666666" y1="3.499999999999996" y2="6.666666666666663"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="17.41666666666666" y1="23.5" y2="25.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="20" y1="18.91666666666667" y2="3.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="20" y1="20.5" y2="23.5"/>
   <ellipse cx="20.03" cy="23.92" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.5" cy="31.33" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="24.5" cy="31.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.58333333333333" x2="24.58333333333333" y1="28.5" y2="31.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.58333333333333" x2="22" y1="31.5" y2="33.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.58333333333334" x2="27.58333333333334" y1="31.5" y2="33.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000004" y2="0.9166666666666679"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.89694619969018" y2="15.89694619969018"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.73157590710537" y2="17.73157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.48348701738202" y2="19.48348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.05" x2="5.05" y1="3.566666666666666" y2="15.81666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:熔断器12_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1.916666666666668" y2="18.25"/>
   <rect fill-opacity="0" height="16.08" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.06,10.06) scale(1,1) translate(0,0)" width="9.58" x="0.27" y="2.02"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV水草坝电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="273.81" x="38.44" xlink:href="logo.png" y="36.25"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,175.347,66.25) scale(1,1) translate(-8.53638e-15,0)" writing-mode="lr" x="175.35" xml:space="preserve" y="69.75" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,168.833,64.3153) scale(1,1) translate(1.68754e-14,0)" writing-mode="lr" x="168.83" xml:space="preserve" y="73.31999999999999" zvalue="5">35kV水草坝电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="86" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,96.75,307.25) scale(1,1) translate(0,0)" width="97" x="48.25" y="295.25" zvalue="11"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,96.75,307.25) scale(1,1) translate(0,0)" writing-mode="lr" x="96.75" xml:space="preserve" y="311.75" zvalue="11">全站公用</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,622.078,690.902) scale(1,1) translate(0,0)" writing-mode="lr" x="622.08" xml:space="preserve" y="695.4" zvalue="3">6.3kV母线</text>
  <line fill="none" id="91" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="390.25" x2="390.25" y1="4.25" y2="1034.25" zvalue="6"/>
  <line fill="none" id="89" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25000000000045" x2="383.25" y1="140.1204926140824" y2="140.1204926140824" zvalue="8"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.25" x2="198.25" y1="164.25" y2="164.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.25" x2="198.25" y1="190.25" y2="190.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.25" x2="17.25" y1="164.25" y2="190.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.25" x2="198.25" y1="164.25" y2="190.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.25" x2="379.25" y1="164.25" y2="164.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.25" x2="379.25" y1="190.25" y2="190.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.25" x2="198.25" y1="164.25" y2="190.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379.25" x2="379.25" y1="164.25" y2="190.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.25" x2="198.25" y1="190.25" y2="190.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.25" x2="198.25" y1="214.5" y2="214.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.25" x2="17.25" y1="190.25" y2="214.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.25" x2="198.25" y1="190.25" y2="214.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.25" x2="379.25" y1="190.25" y2="190.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.25" x2="379.25" y1="214.5" y2="214.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.25" x2="198.25" y1="190.25" y2="214.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379.25" x2="379.25" y1="190.25" y2="214.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.25" x2="198.25" y1="214.5" y2="214.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.25" x2="198.25" y1="237.25" y2="237.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="17.25" x2="17.25" y1="214.5" y2="237.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.25" x2="198.25" y1="214.5" y2="237.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.25" x2="379.25" y1="214.5" y2="214.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.25" x2="379.25" y1="237.25" y2="237.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="198.25" x2="198.25" y1="214.5" y2="237.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379.25" x2="379.25" y1="214.5" y2="237.25"/>
  <line fill="none" id="87" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25000000000045" x2="383.25" y1="610.1204926140824" y2="610.1204926140824" zvalue="10"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.47236173734677" x2="101.8170617373469" y1="384.9166435058594" y2="384.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.47236173734677" x2="101.8170617373469" y1="422.4066435058594" y2="422.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.47236173734677" x2="53.47236173734677" y1="384.9166435058594" y2="422.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8170617373469" x2="101.8170617373469" y1="384.9166435058594" y2="422.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8173617373468" x2="163.9256617373469" y1="384.9166435058594" y2="384.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8173617373468" x2="163.9256617373469" y1="422.4066435058594" y2="422.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8173617373468" x2="101.8173617373468" y1="384.9166435058594" y2="422.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9256617373469" x2="163.9256617373469" y1="384.9166435058594" y2="422.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9251617373468" x2="227.2499617373469" y1="384.9166435058594" y2="384.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9251617373468" x2="227.2499617373469" y1="422.4066435058594" y2="422.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9251617373468" x2="163.9251617373468" y1="384.9166435058594" y2="422.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2499617373469" x2="227.2499617373469" y1="384.9166435058594" y2="422.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2498617373468" x2="289.3581617373468" y1="384.9166435058594" y2="384.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2498617373468" x2="289.3581617373468" y1="422.4066435058594" y2="422.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2498617373468" x2="227.2498617373468" y1="384.9166435058594" y2="422.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="289.3581617373468" y1="384.9166435058594" y2="422.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="351.4664617373469" y1="384.9166435058594" y2="384.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="351.4664617373469" y1="422.4066435058594" y2="422.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="289.3581617373468" y1="384.9166435058594" y2="422.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="351.4664617373469" x2="351.4664617373469" y1="384.9166435058594" y2="422.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.47236173734677" x2="101.8170617373469" y1="422.4067435058594" y2="422.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.47236173734677" x2="101.8170617373469" y1="446.5753435058594" y2="446.5753435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.47236173734677" x2="53.47236173734677" y1="422.4067435058594" y2="446.5753435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8170617373469" x2="101.8170617373469" y1="422.4067435058594" y2="446.5753435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8173617373468" x2="163.9256617373469" y1="422.4067435058594" y2="422.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8173617373468" x2="163.9256617373469" y1="446.5753435058594" y2="446.5753435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8173617373468" x2="101.8173617373468" y1="422.4067435058594" y2="446.5753435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9256617373469" x2="163.9256617373469" y1="422.4067435058594" y2="446.5753435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9251617373468" x2="227.2499617373469" y1="422.4067435058594" y2="422.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9251617373468" x2="227.2499617373469" y1="446.5753435058594" y2="446.5753435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9251617373468" x2="163.9251617373468" y1="422.4067435058594" y2="446.5753435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2499617373469" x2="227.2499617373469" y1="422.4067435058594" y2="446.5753435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2498617373468" x2="289.3581617373468" y1="422.4067435058594" y2="422.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2498617373468" x2="289.3581617373468" y1="446.5753435058594" y2="446.5753435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2498617373468" x2="227.2498617373468" y1="422.4067435058594" y2="446.5753435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="289.3581617373468" y1="422.4067435058594" y2="446.5753435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="351.4664617373469" y1="422.4067435058594" y2="422.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="351.4664617373469" y1="446.5753435058594" y2="446.5753435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="289.3581617373468" y1="422.4067435058594" y2="446.5753435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="351.4664617373469" x2="351.4664617373469" y1="422.4067435058594" y2="446.5753435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.47236173734677" x2="101.8170617373469" y1="446.5753435058594" y2="446.5753435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.47236173734677" x2="101.8170617373469" y1="470.7439435058594" y2="470.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.47236173734677" x2="53.47236173734677" y1="446.5753435058594" y2="470.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8170617373469" x2="101.8170617373469" y1="446.5753435058594" y2="470.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8173617373468" x2="163.9256617373469" y1="446.5753435058594" y2="446.5753435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8173617373468" x2="163.9256617373469" y1="470.7439435058594" y2="470.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8173617373468" x2="101.8173617373468" y1="446.5753435058594" y2="470.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9256617373469" x2="163.9256617373469" y1="446.5753435058594" y2="470.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9251617373468" x2="227.2499617373469" y1="446.5753435058594" y2="446.5753435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9251617373468" x2="227.2499617373469" y1="470.7439435058594" y2="470.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9251617373468" x2="163.9251617373468" y1="446.5753435058594" y2="470.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2499617373469" x2="227.2499617373469" y1="446.5753435058594" y2="470.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2498617373468" x2="289.3581617373468" y1="446.5753435058594" y2="446.5753435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2498617373468" x2="289.3581617373468" y1="470.7439435058594" y2="470.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2498617373468" x2="227.2498617373468" y1="446.5753435058594" y2="470.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="289.3581617373468" y1="446.5753435058594" y2="470.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="351.4664617373469" y1="446.5753435058594" y2="446.5753435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="351.4664617373469" y1="470.7439435058594" y2="470.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="289.3581617373468" y1="446.5753435058594" y2="470.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="351.4664617373469" x2="351.4664617373469" y1="446.5753435058594" y2="470.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.47236173734677" x2="101.8170617373469" y1="470.7439835058594" y2="470.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.47236173734677" x2="101.8170617373469" y1="494.9125835058594" y2="494.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.47236173734677" x2="53.47236173734677" y1="470.7439835058594" y2="494.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8170617373469" x2="101.8170617373469" y1="470.7439835058594" y2="494.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8173617373468" x2="163.9256617373469" y1="470.7439835058594" y2="470.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8173617373468" x2="163.9256617373469" y1="494.9125835058594" y2="494.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8173617373468" x2="101.8173617373468" y1="470.7439835058594" y2="494.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9256617373469" x2="163.9256617373469" y1="470.7439835058594" y2="494.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9251617373468" x2="227.2499617373469" y1="470.7439835058594" y2="470.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9251617373468" x2="227.2499617373469" y1="494.9125835058594" y2="494.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9251617373468" x2="163.9251617373468" y1="470.7439835058594" y2="494.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2499617373469" x2="227.2499617373469" y1="470.7439835058594" y2="494.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2498617373468" x2="289.3581617373468" y1="470.7439835058594" y2="470.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2498617373468" x2="289.3581617373468" y1="494.9125835058594" y2="494.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2498617373468" x2="227.2498617373468" y1="470.7439835058594" y2="494.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="289.3581617373468" y1="470.7439835058594" y2="494.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="351.4664617373469" y1="470.7439835058594" y2="470.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="351.4664617373469" y1="494.9125835058594" y2="494.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="289.3581617373468" y1="470.7439835058594" y2="494.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="351.4664617373469" x2="351.4664617373469" y1="470.7439835058594" y2="494.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.47236173734677" x2="101.8170617373469" y1="494.9127435058594" y2="494.9127435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.47236173734677" x2="101.8170617373469" y1="519.0813435058594" y2="519.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.47236173734677" x2="53.47236173734677" y1="494.9127435058594" y2="519.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8170617373469" x2="101.8170617373469" y1="494.9127435058594" y2="519.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8173617373468" x2="163.9256617373469" y1="494.9127435058594" y2="494.9127435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8173617373468" x2="163.9256617373469" y1="519.0813435058594" y2="519.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8173617373468" x2="101.8173617373468" y1="494.9127435058594" y2="519.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9256617373469" x2="163.9256617373469" y1="494.9127435058594" y2="519.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9251617373468" x2="227.2499617373469" y1="494.9127435058594" y2="494.9127435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9251617373468" x2="227.2499617373469" y1="519.0813435058594" y2="519.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9251617373468" x2="163.9251617373468" y1="494.9127435058594" y2="519.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2499617373469" x2="227.2499617373469" y1="494.9127435058594" y2="519.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2498617373468" x2="289.3581617373468" y1="494.9127435058594" y2="494.9127435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2498617373468" x2="289.3581617373468" y1="519.0813435058594" y2="519.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2498617373468" x2="227.2498617373468" y1="494.9127435058594" y2="519.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="289.3581617373468" y1="494.9127435058594" y2="519.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="351.4664617373469" y1="494.9127435058594" y2="494.9127435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="351.4664617373469" y1="519.0813435058594" y2="519.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="289.3581617373468" y1="494.9127435058594" y2="519.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="351.4664617373469" x2="351.4664617373469" y1="494.9127435058594" y2="519.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.47236173734677" x2="101.8170617373469" y1="519.0813435058594" y2="519.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.47236173734677" x2="101.8170617373469" y1="543.2499435058594" y2="543.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.47236173734677" x2="53.47236173734677" y1="519.0813435058594" y2="543.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8170617373469" x2="101.8170617373469" y1="519.0813435058594" y2="543.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8173617373468" x2="163.9256617373469" y1="519.0813435058594" y2="519.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8173617373468" x2="163.9256617373469" y1="543.2499435058594" y2="543.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.8173617373468" x2="101.8173617373468" y1="519.0813435058594" y2="543.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9256617373469" x2="163.9256617373469" y1="519.0813435058594" y2="543.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9251617373468" x2="227.2499617373469" y1="519.0813435058594" y2="519.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9251617373468" x2="227.2499617373469" y1="543.2499435058594" y2="543.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.9251617373468" x2="163.9251617373468" y1="519.0813435058594" y2="543.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2499617373469" x2="227.2499617373469" y1="519.0813435058594" y2="543.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2498617373468" x2="289.3581617373468" y1="519.0813435058594" y2="519.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2498617373468" x2="289.3581617373468" y1="543.2499435058594" y2="543.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.2498617373468" x2="227.2498617373468" y1="519.0813435058594" y2="543.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="289.3581617373468" y1="519.0813435058594" y2="543.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="351.4664617373469" y1="519.0813435058594" y2="519.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="351.4664617373469" y1="543.2499435058594" y2="543.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.3581617373468" x2="289.3581617373468" y1="519.0813435058594" y2="543.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="351.4664617373469" x2="351.4664617373469" y1="519.0813435058594" y2="543.2499435058594"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="15.25" x2="105.25" y1="925.25" y2="925.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="15.25" x2="105.25" y1="964.4132999999999" y2="964.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="15.25" x2="15.25" y1="925.25" y2="964.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="105.25" x2="105.25" y1="925.25" y2="964.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="105.25" x2="375.25" y1="925.25" y2="925.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="105.25" x2="375.25" y1="964.4132999999999" y2="964.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="105.25" x2="105.25" y1="925.25" y2="964.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="375.25" x2="375.25" y1="925.25" y2="964.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="15.25" x2="105.25" y1="964.41327" y2="964.41327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="15.25" x2="105.25" y1="992.33167" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="15.25" x2="15.25" y1="964.41327" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="105.25" x2="105.25" y1="964.41327" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="105.25" x2="195.25" y1="964.41327" y2="964.41327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="105.25" x2="195.25" y1="992.33167" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="105.25" x2="105.25" y1="964.41327" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="195.25" x2="195.25" y1="964.41327" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="195.2500000000001" x2="285.2500000000001" y1="964.41327" y2="964.41327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="195.2500000000001" x2="285.2500000000001" y1="992.33167" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="195.2500000000001" x2="195.2500000000001" y1="964.41327" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="285.2500000000001" x2="285.2500000000001" y1="964.41327" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="285.25" x2="375.25" y1="964.41327" y2="964.41327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="285.25" x2="375.25" y1="992.33167" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="285.25" x2="285.25" y1="964.41327" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="375.25" x2="375.25" y1="964.41327" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="15.25" x2="105.25" y1="992.3316" y2="992.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="15.25" x2="105.25" y1="1020.25" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="15.25" x2="15.25" y1="992.3316" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="105.25" x2="105.25" y1="992.3316" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="105.25" x2="195.25" y1="992.3316" y2="992.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="105.25" x2="195.25" y1="1020.25" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="105.25" x2="105.25" y1="992.3316" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="195.25" x2="195.25" y1="992.3316" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="195.2500000000001" x2="285.2500000000001" y1="992.3316" y2="992.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="195.2500000000001" x2="285.2500000000001" y1="1020.25" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="195.2500000000001" x2="195.2500000000001" y1="992.3316" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="285.2500000000001" x2="285.2500000000001" y1="992.3316" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="285.25" x2="375.25" y1="992.3316" y2="992.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="285.25" x2="375.25" y1="1020.25" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="285.25" x2="285.25" y1="992.3316" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="375.25" x2="375.25" y1="992.3316" y2="1020.25"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,61.25,945.25) scale(1,1) translate(0,0)" writing-mode="lr" x="61.25" xml:space="preserve" y="951.25" zvalue="14">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.25,979.25) scale(1,1) translate(0,0)" writing-mode="lr" x="58.25" xml:space="preserve" y="985.25" zvalue="15">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.25,979.25) scale(1,1) translate(0,0)" writing-mode="lr" x="240.25" xml:space="preserve" y="985.25" zvalue="16">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.25,1007.25) scale(1,1) translate(0,0)" writing-mode="lr" x="57.25" xml:space="preserve" y="1013.25" zvalue="17">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,239.25,1007.25) scale(1,1) translate(0,0)" writing-mode="lr" x="239.25" xml:space="preserve" y="1013.25" zvalue="18">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,132.472,401.75) scale(1,1) translate(0,0)" writing-mode="lr" x="132.4722290039062" xml:space="preserve" y="406.25" zvalue="19">35kV  母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,81.75,639.75) scale(1,1) translate(0,0)" writing-mode="lr" x="81.75" xml:space="preserve" y="644.25" zvalue="21">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" x="252.484375" xml:space="preserve" y="399.25" zvalue="22">6.3kV    母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="252.484375" xml:space="preserve" y="415.25" zvalue="22">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.25,434.75) scale(1,1) translate(0,0)" writing-mode="lr" x="77.25" xml:space="preserve" y="439.25" zvalue="23">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.25,460.25) scale(1,1) translate(0,0)" writing-mode="lr" x="77.25" xml:space="preserve" y="464.75" zvalue="24">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.25,485.75) scale(1,1) translate(0,0)" writing-mode="lr" x="77.25" xml:space="preserve" y="490.25" zvalue="25">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,76.25,510.25) scale(1,1) translate(0,0)" writing-mode="lr" x="76.25" xml:space="preserve" y="514.75" zvalue="26">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.25,536.75) scale(1,1) translate(0,0)" writing-mode="lr" x="77.25" xml:space="preserve" y="541.25" zvalue="27">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241.304,947.25) scale(1,1) translate(0,0)" writing-mode="lr" x="241.3" xml:space="preserve" y="953.25" zvalue="28">ShuiCaoBa-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.25,178.25) scale(1,1) translate(0,0)" writing-mode="lr" x="55.25" xml:space="preserve" y="183.75" zvalue="30">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.25,178.25) scale(1,1) translate(0,0)" writing-mode="lr" x="235.25" xml:space="preserve" y="183.75" zvalue="31">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.9375,202.5) scale(1,1) translate(0,0)" writing-mode="lr" x="62.94" xml:space="preserve" y="207" zvalue="32">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,242.375,202.5) scale(1,1) translate(0,0)" writing-mode="lr" x="242.38" xml:space="preserve" y="207" zvalue="33">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" x="698.6015625" xml:space="preserve" y="948.2913731141473" zvalue="35">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="698.6015625" xml:space="preserve" y="964.2913731141473" zvalue="35">3200kW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" x="682.8828125" xml:space="preserve" y="509.2951092371192" zvalue="42">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="682.8828125" xml:space="preserve" y="525.2951092371193" zvalue="42">8000kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,669.532,312.318) scale(1,1) translate(0,0)" writing-mode="lr" x="669.53" xml:space="preserve" y="316.82" zvalue="46">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1055.66,76.5873) scale(1,1) translate(0,0)" writing-mode="lr" x="1055.66" xml:space="preserve" y="81.09" zvalue="57">35kV水大线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,327.304,979.25) scale(1,1) translate(0,0)" writing-mode="lr" x="327.3" xml:space="preserve" y="985.25" zvalue="67">20210704</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,714.279,643.187) scale(1,1) translate(7.75556e-14,0)" writing-mode="lr" x="714.28" xml:space="preserve" y="647.6900000000001" zvalue="77">6111</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,718.656,589.664) scale(1,1) translate(0,-2.58089e-13)" writing-mode="lr" x="718.66" xml:space="preserve" y="594.16" zvalue="79">611</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,719.328,823.664) scale(1,1) translate(-1.57208e-13,0)" writing-mode="lr" x="719.33" xml:space="preserve" y="828.16" zvalue="110">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,714.281,771.187) scale(1,1) translate(0,0)" writing-mode="lr" x="714.28" xml:space="preserve" y="775.6900000000001" zvalue="112">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1137.85,956.302) scale(1,1) translate(0,0)" writing-mode="lr" x="1137.849613381087" xml:space="preserve" y="960.8023768858527" zvalue="117">#2发电机3200kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1159.41,823.664) scale(1,1) translate(0,0)" writing-mode="lr" x="1159.41" xml:space="preserve" y="828.16" zvalue="119">602</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1154.28,771.187) scale(1,1) translate(0,0)" writing-mode="lr" x="1154.28" xml:space="preserve" y="775.6900000000001" zvalue="122">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.149,303.841) scale(1,1) translate(0,0)" writing-mode="lr" x="199.15" xml:space="preserve" y="308.34" zvalue="125">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,304.149,303.841) scale(1,1) translate(0,0)" writing-mode="lr" x="304.15" xml:space="preserve" y="308.34" zvalue="126">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1093,240.286) scale(1,1) translate(1.20126e-13,0)" writing-mode="lr" x="1093" xml:space="preserve" y="244.79" zvalue="131">311</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,721.5,408.143) scale(1,1) translate(0,-3.54954e-13)" writing-mode="lr" x="721.5" xml:space="preserve" y="412.64" zvalue="139">301</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,934,484.5) scale(1,1) translate(0,0)" writing-mode="lr" x="934" xml:space="preserve" y="489" zvalue="143">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1217.5,504) scale(1,1) translate(0,0)" writing-mode="lr" x="1217.5" xml:space="preserve" y="508.5" zvalue="145">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" x="1333.9921875" xml:space="preserve" y="508.2951092371192" zvalue="149">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1333.9921875" xml:space="preserve" y="524.2951092371193" zvalue="149">4000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1361.62,408.143) scale(1,1) translate(0,-3.54954e-13)" writing-mode="lr" x="1361.62" xml:space="preserve" y="412.64" zvalue="151">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1357.47,609.187) scale(1,1) translate(0,0)" writing-mode="lr" x="1357.47" xml:space="preserve" y="613.6900000000001" zvalue="156">6031</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1396.5,738.238) scale(1,1) translate(0,-1.5909e-13)" writing-mode="lr" x="1396.498035884713" xml:space="preserve" y="742.7379576402981" zvalue="159">#3发电机3200kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1171.97,655.187) scale(1,1) translate(0,0)" writing-mode="lr" x="1171.97" xml:space="preserve" y="659.6900000000001" zvalue="162">6121</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1207.06,547) scale(1,1) translate(0,0)" writing-mode="lr" x="1207.06" xml:space="preserve" y="551.5" zvalue="164">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,898.974,771.187) scale(1,1) translate(0,0)" writing-mode="lr" x="898.97" xml:space="preserve" y="775.6900000000001" zvalue="169">6105</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,999,803) scale(1,1) translate(0,0)" writing-mode="lr" x="999" xml:space="preserve" y="807.5" zvalue="171">6104</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,936.25,932.5) scale(1,1) translate(0,0)" writing-mode="lr" x="936.25" xml:space="preserve" y="937" zvalue="172">6.3kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,149,1004) scale(1,1) translate(0,0)" writing-mode="lr" x="149" xml:space="preserve" y="1010" zvalue="226">唐涛</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,328,1008) scale(1,1) translate(0,0)" writing-mode="lr" x="328" xml:space="preserve" y="1014" zvalue="228">20220323</text>
 </g>
 <g id="ButtonClass">
  <g href="35kV户撒河三级电站_全站公用.svg"><rect fill-opacity="0" height="24" width="97" x="48.25" y="295.25" zvalue="11"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="142">
   <path class="v6300" d="M 569.56 712.28 L 1298 712.28" stroke-width="6" zvalue="2"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674232696837" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674232696837"/></metadata>
  <path d="M 569.56 712.28 L 1298 712.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv35" d="M 671.09 335.88 L 1454.43 335.88" stroke-width="6" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674232631300" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674232631300"/></metadata>
  <path d="M 671.09 335.88 L 1454.43 335.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="GeneratorClass">
  <g id="105">
   <use class="v6300" height="30" transform="rotate(0,694.147,898.951) scale(1.63169,1.63169) translate(-259.256,-338.543)" width="30" x="669.6720006099341" xlink:href="#Generator:发电机_0" y="874.4759152805962" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449522696198" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449522696198"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,694.147,898.951) scale(1.63169,1.63169) translate(-259.256,-338.543)" width="30" x="669.6720006099341" y="874.4759152805962"/></g>
  <g id="253">
   <use class="v6300" height="30" transform="rotate(0,1134.15,898.951) scale(1.63169,1.63169) translate(-429.597,-338.543)" width="30" x="1109.672000609934" xlink:href="#Generator:发电机_0" y="874.4759152805962" zvalue="116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449522434054" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449522434054"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1134.15,898.951) scale(1.63169,1.63169) translate(-429.597,-338.543)" width="30" x="1109.672000609934" y="874.4759152805962"/></g>
  <g id="41">
   <use class="v6300" height="30" transform="rotate(0,1397.47,683.951) scale(1.63169,1.63169) translate(-531.538,-255.308)" width="30" x="1372.993229297978" xlink:href="#Generator:发电机_0" y="659.4759152805962" zvalue="158"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449522958342" ObjectName="#3发电机"/>
   <cge:TPSR_Ref TObjectID="6192449522958342"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1397.47,683.951) scale(1.63169,1.63169) translate(-531.538,-255.308)" width="30" x="1372.993229297978" y="659.4759152805962"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="178">
   <g id="1780">
    <use class="kv35" height="60" transform="rotate(0,753.308,512.783) scale(1.23929,1.34844) translate(-140.668,-122.05)" width="40" x="728.52" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="472.33" zvalue="41"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874419077124" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1781">
    <use class="v6300" height="60" transform="rotate(0,753.308,512.783) scale(1.23929,1.34844) translate(-140.668,-122.05)" width="40" x="728.52" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="472.33" zvalue="41"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874419142660" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399441711108" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399441711108"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,753.308,512.783) scale(1.23929,1.34844) translate(-140.668,-122.05)" width="40" x="728.52" y="472.33"/></g>
  <g id="37">
   <g id="370">
    <use class="kv35" height="60" transform="rotate(0,1397.43,507.783) scale(1.23929,1.34844) translate(-265.04,-120.758)" width="40" x="1372.64" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="467.33" zvalue="148"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874419208196" ObjectName="35"/>
    </metadata>
   </g>
   <g id="371">
    <use class="v6300" height="60" transform="rotate(0,1397.43,507.783) scale(1.23929,1.34844) translate(-265.04,-120.758)" width="40" x="1372.64" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="467.33" zvalue="148"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874419273732" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399441776644" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399441776644"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1397.43,507.783) scale(1.23929,1.34844) translate(-265.04,-120.758)" width="40" x="1372.64" y="467.33"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="192">
   <use class="v6300" height="30" transform="rotate(0,753.408,648.187) scale(0.994718,0.72946) translate(3.96099,236.34)" width="15" x="745.9473475640748" xlink:href="#Disconnector:刀闸_0" y="637.2452794382783" zvalue="76"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449522565126" ObjectName="#1主变6.3kV侧6111隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449522565126"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,753.408,648.187) scale(0.994718,0.72946) translate(3.96099,236.34)" width="15" x="745.9473475640748" y="637.2452794382783"/></g>
  <g id="239">
   <use class="v6300" height="30" transform="rotate(0,693.408,772.187) scale(0.994718,0.72946) translate(3.64239,282.329)" width="15" x="685.9473475640748" xlink:href="#Disconnector:刀闸_0" y="761.2452794382783" zvalue="111"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449522499590" ObjectName="#1发电机6011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449522499590"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,693.408,772.187) scale(0.994718,0.72946) translate(3.64239,282.329)" width="15" x="685.9473475640748" y="761.2452794382783"/></g>
  <g id="251">
   <use class="v6300" height="30" transform="rotate(0,1133.41,772.187) scale(0.994718,0.72946) translate(5.9788,282.329)" width="15" x="1125.947347564075" xlink:href="#Disconnector:刀闸_0" y="761.2452794382783" zvalue="120"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449522368518" ObjectName="#2发电机6021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449522368518"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1133.41,772.187) scale(0.994718,0.72946) translate(5.9788,282.329)" width="15" x="1125.947347564075" y="761.2452794382783"/></g>
  <g id="38">
   <use class="v6300" height="30" transform="rotate(0,1397.41,608.187) scale(0.994718,0.72946) translate(7.38064,221.505)" width="15" x="1389.947347564075" xlink:href="#Disconnector:刀闸_0" y="597.2452794382783" zvalue="155"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449522892806" ObjectName="#2主变6.3kV侧6031隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449522892806"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1397.41,608.187) scale(0.994718,0.72946) translate(7.38064,221.505)" width="15" x="1389.947347564075" y="597.2452794382783"/></g>
  <g id="44">
   <use class="v6300" height="30" transform="rotate(0,1197.41,656.187) scale(0.994718,0.72946) translate(6.31864,239.307)" width="15" x="1189.947347564075" xlink:href="#Disconnector:刀闸_0" y="645.2452794382783" zvalue="161"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449523023878" ObjectName="#2站用变6121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449523023878"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1197.41,656.187) scale(0.994718,0.72946) translate(6.31864,239.307)" width="15" x="1189.947347564075" y="645.2452794382783"/></g>
  <g id="54">
   <use class="v6300" height="30" transform="rotate(0,929.408,772.187) scale(0.994718,0.72946) translate(4.89556,282.329)" width="15" x="921.9473475640748" xlink:href="#Disconnector:刀闸_0" y="761.2452794382783" zvalue="168"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449523154950" ObjectName="6.3kV母线电压互感器6105隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449523154950"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,929.408,772.187) scale(0.994718,0.72946) translate(4.89556,282.329)" width="15" x="921.9473475640748" y="761.2452794382783"/></g>
 </g>
 <g id="BreakerClass">
  <g id="190">
   <use class="v6300" height="20" transform="rotate(0,754.074,588.664) scale(1.09419,0.994718) translate(-64.4411,3.07299)" width="10" x="748.6034503054768" xlink:href="#Breaker:开关_0" y="578.7165132681797" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924469391365" ObjectName="#1主变6.3kV侧611断路器"/>
   <cge:TPSR_Ref TObjectID="6473924469391365"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,754.074,588.664) scale(1.09419,0.994718) translate(-64.4411,3.07299)" width="10" x="748.6034503054768" y="578.7165132681797"/></g>
  <g id="238">
   <use class="v6300" height="20" transform="rotate(0,694.074,824.664) scale(1.09419,0.994718) translate(-59.2762,4.32616)" width="10" x="688.6034503054768" xlink:href="#Breaker:开关_0" y="814.7165132681796" zvalue="109"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924469325830" ObjectName="#1发电机601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924469325830"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,694.074,824.664) scale(1.09419,0.994718) translate(-59.2762,4.32616)" width="10" x="688.6034503054768" y="814.7165132681796"/></g>
  <g id="252">
   <use class="v6300" height="20" transform="rotate(0,1134.07,824.664) scale(1.09419,0.994718) translate(-97.1522,4.32616)" width="10" x="1128.603450305477" xlink:href="#Breaker:开关_0" y="814.7165132681796" zvalue="118"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924469260293" ObjectName="#2发电机602断路器"/>
   <cge:TPSR_Ref TObjectID="6473924469260293"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1134.07,824.664) scale(1.09419,0.994718) translate(-97.1522,4.32616)" width="10" x="1128.603450305477" y="814.7165132681796"/></g>
  <g id="97">
   <use class="kv35" height="20" transform="rotate(0,1057.36,235.143) scale(2.52857,2.52857) translate(-631.55,-126.863)" width="10" x="1044.714285714286" xlink:href="#Breaker:小车断路器_0" y="209.8571428571428" zvalue="130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924469456901" ObjectName="35kV水大线311断路器"/>
   <cge:TPSR_Ref TObjectID="6473924469456901"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1057.36,235.143) scale(2.52857,2.52857) translate(-631.55,-126.863)" width="10" x="1044.714285714286" y="209.8571428571428"/></g>
  <g id="6">
   <use class="kv35" height="20" transform="rotate(0,753.357,409.143) scale(2.52857,2.52857) translate(-447.776,-232.049)" width="10" x="740.7142857142856" xlink:href="#Breaker:小车断路器_0" y="383.8571428571428" zvalue="138"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924469522437" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924469522437"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,753.357,409.143) scale(2.52857,2.52857) translate(-447.776,-232.049)" width="10" x="740.7142857142856" y="383.8571428571428"/></g>
  <g id="26">
   <use class="kv35" height="20" transform="rotate(0,1397.48,409.143) scale(2.52857,2.52857) translate(-837.159,-232.049)" width="10" x="1384.832381696757" xlink:href="#Breaker:小车断路器_0" y="383.8571428571428" zvalue="150"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924469587973" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473924469587973"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1397.48,409.143) scale(2.52857,2.52857) translate(-837.159,-232.049)" width="10" x="1384.832381696757" y="383.8571428571428"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="49">
   <path class="v6300" d="M 753.31 552.67 L 753.31 579.15" stroke-width="1" zvalue="80"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@1" LinkObjectIDznd="190@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 753.31 552.67 L 753.31 579.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="v6300" d="M 754.15 598.16 L 754.15 637.61" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@1" LinkObjectIDznd="192@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 754.15 598.16 L 754.15 637.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="v6300" d="M 753.47 658.94 L 753.47 712.28" stroke-width="1" zvalue="82"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@1" LinkObjectIDznd="142@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 753.47 658.94 L 753.47 712.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="v6300" d="M 693.5 761.61 L 693.5 712.28" stroke-width="1" zvalue="113"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="239@0" LinkObjectIDznd="142@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 693.5 761.61 L 693.5 712.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="v6300" d="M 693.47 782.94 L 693.47 815.15" stroke-width="1" zvalue="114"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="239@1" LinkObjectIDznd="238@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 693.47 782.94 L 693.47 815.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="v6300" d="M 694.15 834.16 L 694.15 874.88" stroke-width="1" zvalue="115"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="238@1" LinkObjectIDznd="105@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 694.15 834.16 L 694.15 874.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="v6300" d="M 1133.47 782.94 L 1133.47 815.15" stroke-width="1" zvalue="123"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="251@1" LinkObjectIDznd="252@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1133.47 782.94 L 1133.47 815.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="v6300" d="M 1134.15 834.16 L 1134.15 874.88" stroke-width="1" zvalue="124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="252@1" LinkObjectIDznd="253@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1134.15 834.16 L 1134.15 874.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv35" d="M 753.36 335.88 L 753.36 385.75" stroke-width="1" zvalue="139"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@0" LinkObjectIDznd="6@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 753.36 335.88 L 753.36 385.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv35" d="M 753.36 431.9 L 753.36 473.06" stroke-width="1" zvalue="140"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="6@1" LinkObjectIDznd="178@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 753.36 431.9 L 753.36 473.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv35" d="M 938.42 411.62 L 938.42 335.88" stroke-width="1" zvalue="143"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="11@0" LinkObjectIDznd="195@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 938.42 411.62 L 938.42 335.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv35" d="M 1397.48 335.88 L 1397.48 385.75" stroke-width="1" zvalue="152"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@2" LinkObjectIDznd="26@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1397.48 335.88 L 1397.48 385.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv35" d="M 1397.48 431.9 L 1397.48 468.06" stroke-width="1" zvalue="153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@1" LinkObjectIDznd="37@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1397.48 431.9 L 1397.48 468.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="v6300" d="M 1397.43 547.67 L 1397.5 597.61" stroke-width="1" zvalue="156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@1" LinkObjectIDznd="38@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1397.43 547.67 L 1397.5 597.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="v6300" d="M 1397.47 618.94 L 1397.47 659.88" stroke-width="1" zvalue="159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@1" LinkObjectIDznd="41@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1397.47 618.94 L 1397.47 659.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="v6300" d="M 1197.47 666.94 L 1197.47 712.28" stroke-width="1" zvalue="166"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@1" LinkObjectIDznd="142@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1197.47 666.94 L 1197.47 712.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="v6300" d="M 929.5 761.61 L 929.5 712.28" stroke-width="1" zvalue="169"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@0" LinkObjectIDznd="142@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 929.5 761.61 L 929.5 712.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="v6300" d="M 928.31 853.02 L 928.31 782.94" stroke-width="1" zvalue="172"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="54@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 928.31 853.02 L 928.31 782.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="v6300" d="M 986.8 824.08 L 928.31 824.08" stroke-width="1" zvalue="173"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="60" MaxPinNum="2"/>
   </metadata>
  <path d="M 986.8 824.08 L 928.31 824.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="v6300" d="M 1133.5 761.61 L 1133.5 712.28" stroke-width="1" zvalue="174"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="251@0" LinkObjectIDznd="142@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1133.5 761.61 L 1133.5 712.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv35" d="M 1057.36 257.9 L 1057.36 335.88" stroke-width="1" zvalue="175"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@1" LinkObjectIDznd="195@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1057.36 257.9 L 1057.36 335.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv35" d="M 1057.5 168.59 L 1057.5 211.75" stroke-width="1" zvalue="179"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="269@0" LinkObjectIDznd="97@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1057.5 168.59 L 1057.5 211.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="v6300" d="M 1197.5 592.2 L 1197.5 645.61" stroke-width="1" zvalue="186"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="44@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1197.5 592.2 L 1197.5 645.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv35" d="M 1218.3 449.8 L 1218.3 335.88" stroke-width="1" zvalue="217"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="16@0" LinkObjectIDznd="195@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1218.3 449.8 L 1218.3 335.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv35" d="M 1218 415 L 1218.3 415" stroke-width="1" zvalue="222"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="128" MaxPinNum="2"/>
   </metadata>
  <path d="M 1218 415 L 1218.3 415" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="11">
   <use class="kv35" height="40" transform="rotate(0,938.5,438.5) scale(1.575,1.575) translate(-331.127,-148.587)" width="40" x="907" xlink:href="#Accessory:三圈带电容111_0" y="407" zvalue="142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449522761734" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,938.5,438.5) scale(1.575,1.575) translate(-331.127,-148.587)" width="40" x="907" y="407"/></g>
  <g id="59">
   <use class="v6300" height="18" transform="rotate(0,931.25,879.5) scale(3.16667,3.16667) translate(-620.921,-582.263)" width="15" x="907.5" xlink:href="#Accessory:PT8_0" y="851" zvalue="171"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449523351558" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,931.25,879.5) scale(3.16667,3.16667) translate(-620.921,-582.263)" width="15" x="907.5" y="851"/></g>
  <g id="131">
   <use class="kv35" height="20" transform="rotate(0,1218,415) scale(1,1) translate(0,0)" width="10" x="1213" xlink:href="#Accessory:熔断器12_0" y="405" zvalue="220"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449523482630" ObjectName="#1站用变熔断器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1218,415) scale(1,1) translate(0,0)" width="10" x="1213" y="405"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="16">
   <use class="kv35" height="30" transform="rotate(0,1218.18,469) scale(1.2987,1.33333) translate(-276,-112.25)" width="28" x="1200" xlink:href="#EnergyConsumer:站用变DY接地_0" y="449" zvalue="144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449522827270" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1218.18,469) scale(1.2987,1.33333) translate(-276,-112.25)" width="28" x="1200" y="449"/></g>
  <g id="45">
   <use class="v6300" height="30" transform="rotate(0,1197.38,573) scale(1.2987,-1.33333) translate(-271.215,-997.75)" width="28" x="1179.193912971631" xlink:href="#EnergyConsumer:站用变DY接地_0" y="553" zvalue="163"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449523089414" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1197.38,573) scale(1.2987,-1.33333) translate(-271.215,-997.75)" width="28" x="1179.193912971631" y="553"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="57">
   <use class="v6300" height="20" transform="rotate(270,1002,824) scale(-1.6,1.6) translate(-1625.25,-303)" width="10" x="994" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="808" zvalue="170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449523286022" ObjectName="6.3kV母线电压互感器6104接地刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449523286022"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1002,824) scale(-1.6,1.6) translate(-1625.25,-303)" width="10" x="994" y="808"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="10" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1134.5,101.865) scale(1,1) translate(-2.41918e-13,0)" writing-mode="lr" x="1134.03" xml:space="preserve" y="106.64" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123946168326" ObjectName="P"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="17" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1134.5,122.365) scale(1,1) translate(-2.41918e-13,0)" writing-mode="lr" x="1134.03" xml:space="preserve" y="127.14" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123946233860" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="18" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1134.5,142.865) scale(1,1) translate(-2.41918e-13,0)" writing-mode="lr" x="1134.03" xml:space="preserve" y="147.64" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123946299396" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="33" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,656.087,359.88) scale(1,1) translate(0,0)" writing-mode="lr" x="655.62" xml:space="preserve" y="364.66" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123947413508" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="46">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="46" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,582.556,728.276) scale(1,1) translate(0,0)" writing-mode="lr" x="582.09" xml:space="preserve" y="733.05" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123950624772" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="50">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="50" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,809.308,376.83) scale(1,1) translate(0,0)" writing-mode="lr" x="808.76" xml:space="preserve" y="383.11" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123947741188" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="94" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1463.43,376.83) scale(1,1) translate(0,0)" writing-mode="lr" x="1462.87" xml:space="preserve" y="383.11" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123960389636" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="95" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,809.308,399.83) scale(1,1) translate(0,0)" writing-mode="lr" x="808.76" xml:space="preserve" y="406.11" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123947806724" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="96" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1463.43,404.495) scale(1,1) translate(0,0)" writing-mode="lr" x="1462.87" xml:space="preserve" y="410.77" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123960455172" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="99" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,819.308,567.736) scale(1,1) translate(0,0)" writing-mode="lr" x="818.76" xml:space="preserve" y="574.01" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123947872260" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="102" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,819.308,592.736) scale(1,1) translate(0,-1.27728e-13)" writing-mode="lr" x="818.76" xml:space="preserve" y="599.01" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123947937796" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="104" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,809.308,422.83) scale(1,1) translate(0,0)" writing-mode="lr" x="808.76" xml:space="preserve" y="429.11" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123948003332" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="106">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="106" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1463.43,430.83) scale(1,1) translate(0,0)" writing-mode="lr" x="1462.87" xml:space="preserve" y="437.11" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123960651780" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="113">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="113" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,819.308,617.736) scale(1,1) translate(0,-1.33279e-13)" writing-mode="lr" x="818.76" xml:space="preserve" y="624.01" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123948331012" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="115" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1215.15,884.927) scale(1,1) translate(2.58049e-13,0)" writing-mode="lr" x="1214.6" xml:space="preserve" y="891.2" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123944464388" ObjectName="P"/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="116" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,768.147,880.927) scale(1,1) translate(0,0)" writing-mode="lr" x="767.6" xml:space="preserve" y="887.2" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123949772807" ObjectName="P"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="117" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1472.47,656.927) scale(1,1) translate(0,0)" writing-mode="lr" x="1471.92" xml:space="preserve" y="663.2" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123962421252" ObjectName="P"/>
   </metadata>
  </g>
  <g id="118">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="118" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1215.15,909.427) scale(1,1) translate(-2.58049e-13,0)" writing-mode="lr" x="1214.6" xml:space="preserve" y="915.7" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123944529924" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="119">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="119" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,768.147,903.927) scale(1,1) translate(0,0)" writing-mode="lr" x="767.6" xml:space="preserve" y="910.2" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123949838340" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="120">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="120" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1472.47,687.927) scale(1,1) translate(0,0)" writing-mode="lr" x="1471.92" xml:space="preserve" y="694.2" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123962486788" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="121">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="121" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1215.15,933.927) scale(1,1) translate(-2.58049e-13,0)" writing-mode="lr" x="1214.6" xml:space="preserve" y="940.2" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123944595460" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="122">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="122" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,768.147,926.927) scale(1,1) translate(0,0)" writing-mode="lr" x="767.6" xml:space="preserve" y="933.2" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123949903879" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="123">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="123" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1472.47,718.927) scale(1,1) translate(0,0)" writing-mode="lr" x="1471.92" xml:space="preserve" y="725.2" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123962552324" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
</svg>