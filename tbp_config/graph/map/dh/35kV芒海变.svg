<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549679423489" height="1045" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1045" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:避雷器PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1734.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2314.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Breaker:母联开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill="rgb(170,0,0)" fill-opacity="1" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.333333333333333" x2="9.75" y1="0.4166666666666696" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.666666666666668" x2="0.4166666666666661" y1="0.4999999999999982" y2="19.66666666666667"/>
   <rect fill-opacity="0" height="19.58" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.04,10.04) scale(1,1) translate(0,0)" width="9.58" x="0.25" y="0.25"/>
  </symbol>
  <symbol id="Accessory:PT象达_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15.5" xlink:href="#terminal" y="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="18.25" y1="12.08333333333333" y2="12.08333333333333"/>
   <ellipse cx="15.65" cy="12.68" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.9" cy="18.28" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="18.25" y1="18.33333333333333" y2="18.33333333333333"/>
  </symbol>
  <symbol id="Accessory:pt带容断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <path d="M 15 12.5 L 15 3.08333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,6.21) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.083333333333332" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Compensator:10kV电容器_0" viewBox="0,0,24,40">
   <use terminal-index="0" type="0" x="12" xlink:href="#terminal" y="4.699999999999999"/>
   <path d="M 12 20.1 L 17.85 20.1 L 17.85 24.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="4.749074074074073" y1="35.35833333333333" y2="35.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.72129629629629" x2="4.72129629629629" y1="33.13611111111111" y2="35.37685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="24.85833333333333" y2="30.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.258333333333324" x2="0.258333333333324" y1="22.10833333333333" y2="33.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.75833333333333" x2="4.75833333333333" y1="22.35833333333333" y2="20.15462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="31.85833333333333" y2="35.37685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="20.04351851851851" y2="24.775"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.024239280774554" x2="4.024239280774554" y1="39.35833333333333" y2="37.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.786111111111114" x2="12.00833333333334" y1="20.10833333333333" y2="20.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="35.35833333333333" y2="39.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.84166666666666" x2="4.024239280774546" y1="39.35833333333333" y2="39.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.84166666666667" x2="19.84166666666667" y1="37.35833333333333" y2="39.35833333333333"/>
   <path d="M 4.75833 25.9572 A 2.96392 1.81747 180 0 1 4.75833 22.3223" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 4.75833 29.5921 A 2.96392 1.81747 180 0 1 4.75833 25.9572" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 4.75833 33.1271 A 2.96392 1.81747 180 0 1 4.75833 29.4921" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="30.85833333333333" y2="30.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="31.85833333333333" y2="31.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="12" y1="4.5" y2="7.166666666666668"/>
   <path d="M 7.26667 12.1 A 4.91667 4.75 -450 1 0 12.0167 7.18333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 7.33333 12.0833 L 12 12.1667 L 12 20.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510778" x2="17.14262023217246" y1="27.75922956383932" y2="26.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="29.36667381975841" y2="30.33114037330986"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.50700202690252" x2="18.32283029297954" y1="31.0857461490052" y2="31.0857461490052"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.28450340888154" x2="18.47116270499357" y1="30.7156109584975" y2="30.7156109584975"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.98783858485353" x2="18.76782752902158" y1="30.34547576798984" y2="30.34547576798984"/>
   <rect fill-opacity="0" height="4.29" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,17.83,27.22) scale(1,1) translate(0,0)" width="2.33" x="16.67" y="25.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="24.56666666666668" y2="25.08015580397416"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510781" x2="18.53565505804314" y1="27.75922956383932" y2="26.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="25.08015580397418" y2="27.73243882624067"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV芒海变" InitShowingPlane="" fill="rgb(0,0,0)" height="1045" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="63.02" xlink:href="logo.png" y="33.07"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="347" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,187.868,74.3565) scale(1,1) translate(-1.39935e-14,0)" writing-mode="lr" x="187.87" xml:space="preserve" y="77.86" zvalue="1303"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,216.714,74.3332) scale(1,1) translate(0,0)" writing-mode="lr" x="216.71" xml:space="preserve" y="83.33" zvalue="1304">35kV芒海变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="340" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,85.1518,318.643) scale(1,1) translate(0,0)" width="72.88" x="48.71" y="306.64" zvalue="1310"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.1518,318.643) scale(1,1) translate(0,0)" writing-mode="lr" x="85.15000000000001" xml:space="preserve" y="323.14" zvalue="1310">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="177" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,85.1518,363.5) scale(1,1) translate(0,0)" width="72.88" x="48.71" y="351.5" zvalue="1412"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.1518,363.5) scale(1,1) translate(0,0)" writing-mode="lr" x="85.15000000000001" xml:space="preserve" y="368" zvalue="1412">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="178" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,85.1518,406.25) scale(1,1) translate(0,0)" width="72.88" x="48.71" y="394.25" zvalue="1414"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.1518,406.25) scale(1,1) translate(0,0)" writing-mode="lr" x="85.15000000000001" xml:space="preserve" y="410.75" zvalue="1414">信号一览</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="112" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,219.438,363.5) scale(1,1) translate(0,0)" width="72.88" x="183" y="351.5" zvalue="1416"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,219.438,363.5) scale(1,1) translate(0,0)" writing-mode="lr" x="219.44" xml:space="preserve" y="368" zvalue="1416">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="105" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,218.438,406.25) scale(1,1) translate(0,0)" width="72.88" x="182" y="394.25" zvalue="1418"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,218.438,406.25) scale(1,1) translate(0,0)" writing-mode="lr" x="218.44" xml:space="preserve" y="410.75" zvalue="1418">小电流接地</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,564.076,324.591) scale(1,1) translate(0,0)" writing-mode="lr" x="564.08" xml:space="preserve" y="329.09" zvalue="7">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1309.4,149.988) scale(1,1) translate(0,0)" writing-mode="lr" x="1309.4" xml:space="preserve" y="154.49" zvalue="60">35kV母线PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,904.149,252.409) scale(1,1) translate(0,0)" writing-mode="lr" x="904.15" xml:space="preserve" y="256.91" zvalue="62">35kV#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,997.336,76) scale(1,1) translate(0,0)" writing-mode="lr" x="997.34" xml:space="preserve" y="80.5" zvalue="299">35kV遮海线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,969.309,248.332) scale(1,1) translate(0,0)" writing-mode="lr" x="969.3099999999999" xml:space="preserve" y="252.83" zvalue="300">341</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,972.797,185.442) scale(1,1) translate(0,0)" writing-mode="lr" x="972.8" xml:space="preserve" y="189.94" zvalue="302">3416</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1025.73,162.003) scale(1,1) translate(0,0)" writing-mode="lr" x="1025.73" xml:space="preserve" y="166.5" zvalue="304">34167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,972.925,307.237) scale(1,1) translate(-2.13035e-13,0)" writing-mode="lr" x="972.92" xml:space="preserve" y="311.74" zvalue="307">3411</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1025.73,290.003) scale(1,1) translate(0,0)" writing-mode="lr" x="1025.73" xml:space="preserve" y="294.5" zvalue="316">34117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1103.64,126.768) scale(1,1) translate(0,0)" writing-mode="lr" x="1103.64" xml:space="preserve" y="131.27" zvalue="320">PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1354.84,258.57) scale(1,1) translate(0,0)" writing-mode="lr" x="1354.84" xml:space="preserve" y="263.07" zvalue="354">39017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1354.84,325.57) scale(1,1) translate(0,0)" writing-mode="lr" x="1354.84" xml:space="preserve" y="330.07" zvalue="358">39010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="292" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1025.73,222.003) scale(1,1) translate(0,0)" writing-mode="lr" x="1025.73" xml:space="preserve" y="226.5" zvalue="554">34160</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="307" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1286.12,277.628) scale(1,1) translate(0,0)" writing-mode="lr" x="1286.12" xml:space="preserve" y="282.13" zvalue="568">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,679.103,830.974) scale(1,1) translate(0,0)" writing-mode="lr" x="679.1" xml:space="preserve" y="835.47" zvalue="872">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,684.239,778.618) scale(1,1) translate(0,0)" writing-mode="lr" x="684.24" xml:space="preserve" y="783.12" zvalue="874">042</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,661.679,938.125) scale(1,1) translate(0,0)" writing-mode="lr" x="661.6799999999999" xml:space="preserve" y="942.63" zvalue="878">10kV政府线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,680.603,734.974) scale(1,1) translate(0,0)" writing-mode="lr" x="680.6" xml:space="preserve" y="739.47" zvalue="882">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,815.103,830.974) scale(1,1) translate(0,0)" writing-mode="lr" x="815.1" xml:space="preserve" y="835.47" zvalue="891">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,820.239,778.618) scale(1,1) translate(0,0)" writing-mode="lr" x="820.24" xml:space="preserve" y="783.12" zvalue="893">043</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,816.603,734.974) scale(1,1) translate(0,0)" writing-mode="lr" x="816.6" xml:space="preserve" y="739.47" zvalue="901">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1073.22,668.568) scale(1,1) translate(0,0)" writing-mode="lr" x="1073.22" xml:space="preserve" y="673.0700000000001" zvalue="930">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1227.72,668.568) scale(1,1) translate(0,0)" writing-mode="lr" x="1227.72" xml:space="preserve" y="673.0700000000001" zvalue="932">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1138,608.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1138" xml:space="preserve" y="613" zvalue="936">012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,956.103,830.974) scale(1,1) translate(0,0)" writing-mode="lr" x="956.1" xml:space="preserve" y="835.47" zvalue="1013">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,961.239,778.618) scale(1,1) translate(0,0)" writing-mode="lr" x="961.24" xml:space="preserve" y="783.12" zvalue="1015">044</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,939.679,939.125) scale(1,1) translate(0,0)" writing-mode="lr" x="939.6799999999999" xml:space="preserve" y="943.63" zvalue="1019">10kV赖南线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,957.603,734.974) scale(1,1) translate(0,0)" writing-mode="lr" x="957.6" xml:space="preserve" y="739.47" zvalue="1023">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1405.1,831.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1405.1" xml:space="preserve" y="836.47" zvalue="1049">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1410.24,779.618) scale(1,1) translate(0,0)" writing-mode="lr" x="1410.24" xml:space="preserve" y="784.12" zvalue="1051">045</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1388.68,940.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1388.68" xml:space="preserve" y="944.63" zvalue="1055">10kV勐古线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1406.6,735.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1406.6" xml:space="preserve" y="740.47" zvalue="1059">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1589.1,831.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1589.1" xml:space="preserve" y="836.47" zvalue="1064">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1594.24,779.618) scale(1,1) translate(0,0)" writing-mode="lr" x="1594.24" xml:space="preserve" y="784.12" zvalue="1066">046</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="196" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1572.68,940.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1572.68" xml:space="preserve" y="944.63" zvalue="1070">10kV市场线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1590.6,735.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1590.6" xml:space="preserve" y="740.47" zvalue="1074">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1545.58,580.701) scale(1,1) translate(0,0)" writing-mode="lr" x="1545.58" xml:space="preserve" y="585.2" zvalue="1137">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1496.93,416.529) scale(1,1) translate(-7.2565e-12,0)" writing-mode="lr" x="1496.93" xml:space="preserve" y="421.03" zvalue="1139">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="256" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1493.71,372.124) scale(1,1) translate(-4.92097e-12,0)" writing-mode="lr" x="1493.71" xml:space="preserve" y="376.62" zvalue="1141">3021</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1410.88,494.011) scale(1,1) translate(0,0)" writing-mode="lr" x="1410.875" xml:space="preserve" y="500.0107321787362" zvalue="1147">#2主变 2500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="254" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1540.99,618.939) scale(1,1) translate(0,0)" writing-mode="lr" x="1540.99" xml:space="preserve" y="623.4400000000001" zvalue="1152">0022</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="311" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,862.833,580.451) scale(1,1) translate(0,0)" writing-mode="lr" x="862.83" xml:space="preserve" y="584.95" zvalue="1175">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="310" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,813.755,416.529) scale(1,1) translate(0,0)" writing-mode="lr" x="813.75" xml:space="preserve" y="421.03" zvalue="1177">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="309" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,812.14,375.874) scale(1,1) translate(0,0)" writing-mode="lr" x="812.14" xml:space="preserve" y="380.37" zvalue="1179">3011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="308" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,735.529,487.83) scale(1,1) translate(1.4922e-13,0)" writing-mode="lr" x="735.5290171652056" xml:space="preserve" y="493.8295454545454" zvalue="1185">#1主变 2500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="303" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,857.729,618.689) scale(1,1) translate(0,0)" writing-mode="lr" x="857.73" xml:space="preserve" y="623.1900000000001" zvalue="1189">0011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,444.875,687) scale(1,1) translate(0,0)" writing-mode="lr" x="444.88" xml:space="preserve" y="691.5" zvalue="1200">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1729.25,685.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1729.25" xml:space="preserve" y="690.25" zvalue="1202">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,556.851,662.346) scale(1,1) translate(0,0)" writing-mode="lr" x="556.85" xml:space="preserve" y="666.85" zvalue="1205">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,575,542.75) scale(1,1) translate(0,0)" writing-mode="lr" x="575" xml:space="preserve" y="547.25" zvalue="1209">10kVⅠ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1655.25,554) scale(1,1) translate(0,0)" writing-mode="lr" x="1655.25" xml:space="preserve" y="558.5" zvalue="1217">10kVⅡ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,491.036,942.135) scale(1,1) translate(0,0)" writing-mode="lr" x="491.04" xml:space="preserve" y="946.63" zvalue="1224">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,527.874,831.474) scale(1,1) translate(0,0)" writing-mode="lr" x="527.87" xml:space="preserve" y="835.97" zvalue="1226">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,535.085,772.451) scale(1,1) translate(0,0)" writing-mode="lr" x="535.09" xml:space="preserve" y="776.95" zvalue="1228">041</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,527.701,734.474) scale(1,1) translate(0,0)" writing-mode="lr" x="527.7" xml:space="preserve" y="738.97" zvalue="1232">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,507.25,966.673) scale(1,1) translate(0,0)" writing-mode="lr" x="507.25" xml:space="preserve" y="971.17" zvalue="1236">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,477.651,828.962) scale(1,1) translate(0,0)" writing-mode="lr" x="477.65" xml:space="preserve" y="833.46" zvalue="1242">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="210" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1666.07,671.154) scale(1,1) translate(0,0)" writing-mode="lr" x="1666.07" xml:space="preserve" y="675.65" zvalue="1245">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1758.1,831.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1758.1" xml:space="preserve" y="836.47" zvalue="1252">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1763.24,779.618) scale(1,1) translate(0,0)" writing-mode="lr" x="1763.24" xml:space="preserve" y="784.12" zvalue="1254">047</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1741.68,940.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1741.68" xml:space="preserve" y="944.63" zvalue="1258">10kV黑勐陇线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1759.6,735.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1759.6" xml:space="preserve" y="740.47" zvalue="1262">2</text>
  <line fill="none" id="344" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.71428571428635" x2="383.7142857142859" y1="151.5133497569396" y2="151.5133497569396" zvalue="1306"/>
  <line fill="none" id="343" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384.7142857142859" x2="384.7142857142859" y1="9.642857142857224" y2="1039.642857142857" zvalue="1307"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.71414285714286" x2="195.3571428571429" y1="159.6428285714286" y2="159.6428285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.71414285714286" x2="195.3571428571429" y1="187.2057285714286" y2="187.2057285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.71414285714286" x2="13.71414285714286" y1="159.6428285714286" y2="187.2057285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.3571428571429" x2="195.3571428571429" y1="159.6428285714286" y2="187.2057285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.3571428571429" x2="377.0001428571429" y1="159.6428285714286" y2="159.6428285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.3571428571429" x2="377.0001428571429" y1="187.2057285714286" y2="187.2057285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.3571428571429" x2="195.3571428571429" y1="159.6428285714286" y2="187.2057285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="377.0001428571429" x2="377.0001428571429" y1="159.6428285714286" y2="187.2057285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.71414285714286" x2="195.3571428571429" y1="187.2058285714286" y2="187.2058285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.71414285714286" x2="195.3571428571429" y1="212.9135285714286" y2="212.9135285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.71414285714286" x2="13.71414285714286" y1="187.2058285714286" y2="212.9135285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.3571428571429" x2="195.3571428571429" y1="187.2058285714286" y2="212.9135285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.3571428571429" x2="377.0001428571429" y1="187.2058285714286" y2="187.2058285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.3571428571429" x2="377.0001428571429" y1="212.9135285714286" y2="212.9135285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.3571428571429" x2="195.3571428571429" y1="187.2058285714286" y2="212.9135285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="377.0001428571429" x2="377.0001428571429" y1="187.2058285714286" y2="212.9135285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.71414285714286" x2="195.3571428571429" y1="212.9135285714286" y2="212.9135285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.71414285714286" x2="195.3571428571429" y1="237.0311285714286" y2="237.0311285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.71414285714286" x2="13.71414285714286" y1="212.9135285714286" y2="237.0311285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.3571428571429" x2="195.3571428571429" y1="212.9135285714286" y2="237.0311285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.3571428571429" x2="377.0001428571429" y1="212.9135285714286" y2="212.9135285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.3571428571429" x2="377.0001428571429" y1="237.0311285714286" y2="237.0311285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.3571428571429" x2="195.3571428571429" y1="212.9135285714286" y2="237.0311285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="377.0001428571429" x2="377.0001428571429" y1="212.9135285714286" y2="237.0311285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.71414285714286" x2="195.3571428571429" y1="237.0310285714286" y2="237.0310285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.71414285714286" x2="195.3571428571429" y1="263.3824285714286" y2="263.3824285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.71414285714286" x2="13.71414285714286" y1="237.0310285714286" y2="263.3824285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.3571428571429" x2="195.3571428571429" y1="237.0310285714286" y2="263.3824285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.3571428571429" x2="377.0001428571429" y1="237.0310285714286" y2="237.0310285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.3571428571429" x2="377.0001428571429" y1="263.3824285714286" y2="263.3824285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.3571428571429" x2="195.3571428571429" y1="237.0310285714286" y2="263.3824285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="377.0001428571429" x2="377.0001428571429" y1="237.0310285714286" y2="263.3824285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.71414285714286" x2="195.3571428571429" y1="263.3824285714286" y2="263.3824285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.71414285714286" x2="195.3571428571429" y1="287.5000285714286" y2="287.5000285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.71414285714286" x2="13.71414285714286" y1="263.3824285714286" y2="287.5000285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.3571428571429" x2="195.3571428571429" y1="263.3824285714286" y2="287.5000285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.3571428571429" x2="377.0001428571429" y1="263.3824285714286" y2="263.3824285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.3571428571429" x2="377.0001428571429" y1="287.5000285714286" y2="287.5000285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195.3571428571429" x2="195.3571428571429" y1="263.3824285714286" y2="287.5000285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="377.0001428571429" x2="377.0001428571429" y1="263.3824285714286" y2="287.5000285714286"/>
  <line fill="none" id="341" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.71428571428635" x2="383.7142857142859" y1="621.5133497569396" y2="621.5133497569396" zvalue="1309"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="35.71428571428578" x2="81.48878571428577" y1="437.6428785714286" y2="437.6428785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="35.71428571428578" x2="81.48878571428577" y1="479.4999785714286" y2="479.4999785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="35.71428571428578" x2="35.71428571428578" y1="437.6428785714286" y2="479.4999785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="81.48878571428577" y1="437.6428785714286" y2="479.4999785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="140.2951857142858" y1="437.6428785714286" y2="437.6428785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="140.2951857142858" y1="479.4999785714286" y2="479.4999785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="81.48878571428577" y1="437.6428785714286" y2="479.4999785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="140.2951857142858" y1="437.6428785714286" y2="479.4999785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="199.1015857142858" y1="437.6428785714286" y2="437.6428785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="199.1015857142858" y1="479.4999785714286" y2="479.4999785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="140.2951857142858" y1="437.6428785714286" y2="479.4999785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1015857142858" x2="199.1015857142858" y1="437.6428785714286" y2="479.4999785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1014857142858" x2="257.9078857142858" y1="437.6428785714286" y2="437.6428785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1014857142858" x2="257.9078857142858" y1="479.4999785714286" y2="479.4999785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1014857142858" x2="199.1014857142858" y1="437.6428785714286" y2="479.4999785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="257.9078857142858" y1="437.6428785714286" y2="479.4999785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="316.7142857142858" y1="437.6428785714286" y2="437.6428785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="316.7142857142858" y1="479.4999785714286" y2="479.4999785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="257.9078857142858" y1="437.6428785714286" y2="479.4999785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.7142857142858" x2="316.7142857142858" y1="437.6428785714286" y2="479.4999785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="35.71428571428578" x2="81.48878571428577" y1="479.4999785714286" y2="479.4999785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="35.71428571428578" x2="81.48878571428577" y1="504.1793785714286" y2="504.1793785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="35.71428571428578" x2="35.71428571428578" y1="479.4999785714286" y2="504.1793785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="81.48878571428577" y1="479.4999785714286" y2="504.1793785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="140.2951857142858" y1="479.4999785714286" y2="479.4999785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="140.2951857142858" y1="504.1793785714286" y2="504.1793785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="81.48878571428577" y1="479.4999785714286" y2="504.1793785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="140.2951857142858" y1="479.4999785714286" y2="504.1793785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="199.1015857142858" y1="479.4999785714286" y2="479.4999785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="199.1015857142858" y1="504.1793785714286" y2="504.1793785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="140.2951857142858" y1="479.4999785714286" y2="504.1793785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1015857142858" x2="199.1015857142858" y1="479.4999785714286" y2="504.1793785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1014857142858" x2="257.9078857142858" y1="479.4999785714286" y2="479.4999785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1014857142858" x2="257.9078857142858" y1="504.1793785714286" y2="504.1793785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1014857142858" x2="199.1014857142858" y1="479.4999785714286" y2="504.1793785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="257.9078857142858" y1="479.4999785714286" y2="504.1793785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="316.7142857142858" y1="479.4999785714286" y2="479.4999785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="316.7142857142858" y1="504.1793785714286" y2="504.1793785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="257.9078857142858" y1="479.4999785714286" y2="504.1793785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.7142857142858" x2="316.7142857142858" y1="479.4999785714286" y2="504.1793785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="35.71428571428578" x2="81.48878571428577" y1="504.1793785714286" y2="504.1793785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="35.71428571428578" x2="81.48878571428577" y1="528.8587785714286" y2="528.8587785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="35.71428571428578" x2="35.71428571428578" y1="504.1793785714286" y2="528.8587785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="81.48878571428577" y1="504.1793785714286" y2="528.8587785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="140.2951857142858" y1="504.1793785714286" y2="504.1793785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="140.2951857142858" y1="528.8587785714286" y2="528.8587785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="81.48878571428577" y1="504.1793785714286" y2="528.8587785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="140.2951857142858" y1="504.1793785714286" y2="528.8587785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="199.1015857142858" y1="504.1793785714286" y2="504.1793785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="199.1015857142858" y1="528.8587785714286" y2="528.8587785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="140.2951857142858" y1="504.1793785714286" y2="528.8587785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1015857142858" x2="199.1015857142858" y1="504.1793785714286" y2="528.8587785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1014857142858" x2="257.9078857142858" y1="504.1793785714286" y2="504.1793785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1014857142858" x2="257.9078857142858" y1="528.8587785714286" y2="528.8587785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1014857142858" x2="199.1014857142858" y1="504.1793785714286" y2="528.8587785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="257.9078857142858" y1="504.1793785714286" y2="528.8587785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="316.7142857142858" y1="504.1793785714286" y2="504.1793785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="316.7142857142858" y1="528.8587785714286" y2="528.8587785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="257.9078857142858" y1="504.1793785714286" y2="528.8587785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.7142857142858" x2="316.7142857142858" y1="504.1793785714286" y2="528.8587785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="35.71428571428578" x2="81.48878571428577" y1="528.8587985714286" y2="528.8587985714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="35.71428571428578" x2="81.48878571428577" y1="553.5381985714287" y2="553.5381985714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="35.71428571428578" x2="35.71428571428578" y1="528.8587985714286" y2="553.5381985714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="81.48878571428577" y1="528.8587985714286" y2="553.5381985714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="140.2951857142858" y1="528.8587985714286" y2="528.8587985714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="140.2951857142858" y1="553.5381985714287" y2="553.5381985714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="81.48878571428577" y1="528.8587985714286" y2="553.5381985714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="140.2951857142858" y1="528.8587985714286" y2="553.5381985714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="199.1015857142858" y1="528.8587985714286" y2="528.8587985714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="199.1015857142858" y1="553.5381985714287" y2="553.5381985714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="140.2951857142858" y1="528.8587985714286" y2="553.5381985714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1015857142858" x2="199.1015857142858" y1="528.8587985714286" y2="553.5381985714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1014857142858" x2="257.9078857142858" y1="528.8587985714286" y2="528.8587985714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1014857142858" x2="257.9078857142858" y1="553.5381985714287" y2="553.5381985714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1014857142858" x2="199.1014857142858" y1="528.8587985714286" y2="553.5381985714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="257.9078857142858" y1="528.8587985714286" y2="553.5381985714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="316.7142857142858" y1="528.8587985714286" y2="528.8587985714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="316.7142857142858" y1="553.5381985714287" y2="553.5381985714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="257.9078857142858" y1="528.8587985714286" y2="553.5381985714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.7142857142858" x2="316.7142857142858" y1="528.8587985714286" y2="553.5381985714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="35.71428571428578" x2="81.48878571428577" y1="553.5382785714286" y2="553.5382785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="35.71428571428578" x2="81.48878571428577" y1="578.2176785714286" y2="578.2176785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="35.71428571428578" x2="35.71428571428578" y1="553.5382785714286" y2="578.2176785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="81.48878571428577" y1="553.5382785714286" y2="578.2176785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="140.2951857142858" y1="553.5382785714286" y2="553.5382785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="140.2951857142858" y1="578.2176785714286" y2="578.2176785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="81.48878571428577" y1="553.5382785714286" y2="578.2176785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="140.2951857142858" y1="553.5382785714286" y2="578.2176785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="199.1015857142858" y1="553.5382785714286" y2="553.5382785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="199.1015857142858" y1="578.2176785714286" y2="578.2176785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="140.2951857142858" y1="553.5382785714286" y2="578.2176785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1015857142858" x2="199.1015857142858" y1="553.5382785714286" y2="578.2176785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1014857142858" x2="257.9078857142858" y1="553.5382785714286" y2="553.5382785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1014857142858" x2="257.9078857142858" y1="578.2176785714286" y2="578.2176785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1014857142858" x2="199.1014857142858" y1="553.5382785714286" y2="578.2176785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="257.9078857142858" y1="553.5382785714286" y2="578.2176785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="316.7142857142858" y1="553.5382785714286" y2="553.5382785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="316.7142857142858" y1="578.2176785714286" y2="578.2176785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="257.9078857142858" y1="553.5382785714286" y2="578.2176785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.7142857142858" x2="316.7142857142858" y1="553.5382785714286" y2="578.2176785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="35.71428571428578" x2="81.48878571428577" y1="578.2176785714286" y2="578.2176785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="35.71428571428578" x2="81.48878571428577" y1="602.8970785714287" y2="602.8970785714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="35.71428571428578" x2="35.71428571428578" y1="578.2176785714286" y2="602.8970785714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="81.48878571428577" y1="578.2176785714286" y2="602.8970785714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="140.2951857142858" y1="578.2176785714286" y2="578.2176785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="140.2951857142858" y1="602.8970785714287" y2="602.8970785714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="81.48878571428577" x2="81.48878571428577" y1="578.2176785714286" y2="602.8970785714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="140.2951857142858" y1="578.2176785714286" y2="602.8970785714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="199.1015857142858" y1="578.2176785714286" y2="578.2176785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="199.1015857142858" y1="602.8970785714287" y2="602.8970785714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="140.2951857142858" x2="140.2951857142858" y1="578.2176785714286" y2="602.8970785714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1015857142858" x2="199.1015857142858" y1="578.2176785714286" y2="602.8970785714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1014857142858" x2="257.9078857142858" y1="578.2176785714286" y2="578.2176785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1014857142858" x2="257.9078857142858" y1="602.8970785714287" y2="602.8970785714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="199.1014857142858" x2="199.1014857142858" y1="578.2176785714286" y2="602.8970785714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="257.9078857142858" y1="578.2176785714286" y2="602.8970785714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="316.7142857142858" y1="578.2176785714286" y2="578.2176785714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="316.7142857142858" y1="602.8970785714287" y2="602.8970785714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="257.9078857142858" x2="257.9078857142858" y1="578.2176785714286" y2="602.8970785714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="316.7142857142858" x2="316.7142857142858" y1="578.2176785714286" y2="602.8970785714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.71428571428589" x2="106.7142857142859" y1="936.6428571428572" y2="936.6428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.71428571428589" x2="106.7142857142859" y1="975.8061571428573" y2="975.8061571428573"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.71428571428589" x2="16.71428571428589" y1="936.6428571428572" y2="975.8061571428573"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="106.7142857142859" y1="936.6428571428572" y2="975.8061571428573"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="376.7142857142859" y1="936.6428571428572" y2="936.6428571428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="376.7142857142859" y1="975.8061571428573" y2="975.8061571428573"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="106.7142857142859" y1="936.6428571428572" y2="975.8061571428573"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="376.7142857142859" x2="376.7142857142859" y1="936.6428571428572" y2="975.8061571428573"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.71428571428589" x2="106.7142857142859" y1="975.8061271428572" y2="975.8061271428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.71428571428589" x2="106.7142857142859" y1="1003.724527142857" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.71428571428589" x2="16.71428571428589" y1="975.8061271428572" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="106.7142857142859" y1="975.8061271428572" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="196.7142857142859" y1="975.8061271428572" y2="975.8061271428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="196.7142857142859" y1="1003.724527142857" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="106.7142857142859" y1="975.8061271428572" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.7142857142859" x2="196.7142857142859" y1="975.8061271428572" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.714285714286" x2="286.714285714286" y1="975.8061271428572" y2="975.8061271428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.714285714286" x2="286.714285714286" y1="1003.724527142857" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.714285714286" x2="196.714285714286" y1="975.8061271428572" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.714285714286" x2="286.714285714286" y1="975.8061271428572" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.7142857142859" x2="376.7142857142859" y1="975.8061271428572" y2="975.8061271428572"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.7142857142859" x2="376.7142857142859" y1="1003.724527142857" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.7142857142859" x2="286.7142857142859" y1="975.8061271428572" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="376.7142857142859" x2="376.7142857142859" y1="975.8061271428572" y2="1003.724527142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.71428571428589" x2="106.7142857142859" y1="1003.724457142857" y2="1003.724457142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.71428571428589" x2="106.7142857142859" y1="1031.642857142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="16.71428571428589" x2="16.71428571428589" y1="1003.724457142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="106.7142857142859" y1="1003.724457142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="196.7142857142859" y1="1003.724457142857" y2="1003.724457142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="196.7142857142859" y1="1031.642857142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="106.7142857142859" x2="106.7142857142859" y1="1003.724457142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.7142857142859" x2="196.7142857142859" y1="1003.724457142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.714285714286" x2="286.714285714286" y1="1003.724457142857" y2="1003.724457142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.714285714286" x2="286.714285714286" y1="1031.642857142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="196.714285714286" x2="196.714285714286" y1="1003.724457142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.714285714286" x2="286.714285714286" y1="1003.724457142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.7142857142859" x2="376.7142857142859" y1="1003.724457142857" y2="1003.724457142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.7142857142859" x2="376.7142857142859" y1="1031.642857142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="286.7142857142859" x2="286.7142857142859" y1="1003.724457142857" y2="1031.642857142857"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="376.7142857142859" x2="376.7142857142859" y1="1003.724457142857" y2="1031.642857142857"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="337" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,61.7143,956.643) scale(1,1) translate(0,0)" writing-mode="lr" x="61.71" xml:space="preserve" y="962.64" zvalue="1313">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="336" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.7143,990.643) scale(1,1) translate(0,0)" writing-mode="lr" x="58.71" xml:space="preserve" y="996.64" zvalue="1314">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="335" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.714,990.643) scale(1,1) translate(0,0)" writing-mode="lr" x="240.71" xml:space="preserve" y="996.64" zvalue="1315">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="334" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,108.357,1017.64) scale(1,1) translate(7.48608e-15,0)" writing-mode="lr" x="33.71" xml:space="preserve" y="1023.64" zvalue="1316">更新                杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="333" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,239.714,1018.64) scale(1,1) translate(0,0)" writing-mode="lr" x="239.71" xml:space="preserve" y="1024.64" zvalue="1317">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="329" stroke="rgb(255,255,255)" text-anchor="middle" x="105.625" xml:space="preserve" y="455.6269841269842" zvalue="1318">35kV     母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="329" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="105.625" xml:space="preserve" y="472.6269841269842" zvalue="1318">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="327" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.2143,651.143) scale(1,1) translate(0,0)" writing-mode="lr" x="82.21428571428589" xml:space="preserve" y="655.6428571428572" zvalue="1320">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="326" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,215.113,318.484) scale(1,1) translate(0,0)" writing-mode="lr" x="215.11" xml:space="preserve" y="322.98" zvalue="1321">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="317" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,320.113,318.484) scale(1,1) translate(0,0)" writing-mode="lr" x="320.11" xml:space="preserve" y="322.98" zvalue="1322">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="316" stroke="rgb(255,255,255)" text-anchor="middle" x="220.65625" xml:space="preserve" y="456.6269841269842" zvalue="1323">10kV     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="316" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="220.65625" xml:space="preserve" y="473.6269841269842" zvalue="1323">Ⅰ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="315" stroke="rgb(255,255,255)" text-anchor="middle" x="279.71875" xml:space="preserve" y="456.0714285714286" zvalue="1324">10kV      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="315" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="279.71875" xml:space="preserve" y="473.0714285714286" zvalue="1324">Ⅱ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.7143,494.393) scale(1,1) translate(0,-1.06558e-13)" writing-mode="lr" x="53.71428571428589" xml:space="preserve" y="498.8928571428572" zvalue="1325">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="301" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.7143,519.893) scale(1,1) translate(0,0)" writing-mode="lr" x="53.71428571428589" xml:space="preserve" y="524.3928571428572" zvalue="1326">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="300" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.7143,542.893) scale(1,1) translate(0,5.86634e-14)" writing-mode="lr" x="53.71428571428589" xml:space="preserve" y="547.3928571428572" zvalue="1327">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="299" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.7143,565.893) scale(1,1) translate(0,0)" writing-mode="lr" x="53.71428571428589" xml:space="preserve" y="570.3928571428572" zvalue="1328">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="298" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.7143,592.893) scale(1,1) translate(0,0)" writing-mode="lr" x="53.71428571428589" xml:space="preserve" y="597.3928571428572" zvalue="1329">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="297" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.692,958.643) scale(1,1) translate(0,0)" writing-mode="lr" x="240.69" xml:space="preserve" y="964.64" zvalue="1330">MangHai-01-2021</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="291" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.7143,177.643) scale(1,1) translate(0,0)" writing-mode="lr" x="55.71" xml:space="preserve" y="183.14" zvalue="1333">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="290" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.714,177.643) scale(1,1) translate(0,0)" writing-mode="lr" x="235.71" xml:space="preserve" y="183.14" zvalue="1334">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.4018,201.893) scale(1,1) translate(0,0)" writing-mode="lr" x="59.4" xml:space="preserve" y="206.39" zvalue="1335">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.9018,249.643) scale(1,1) translate(0,0)" writing-mode="lr" x="62.9" xml:space="preserve" y="255.14" zvalue="1336">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,244.464,249.143) scale(1,1) translate(0,0)" writing-mode="lr" x="244.46" xml:space="preserve" y="254.64" zvalue="1337">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="286" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.9018,272.643) scale(1,1) translate(0,-3.49911e-13)" writing-mode="lr" x="62.9" xml:space="preserve" y="278.14" zvalue="1338">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,244.464,276.143) scale(1,1) translate(0,-3.54574e-13)" writing-mode="lr" x="244.46" xml:space="preserve" y="281.64" zvalue="1339">2号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.4018,225.893) scale(1,1) translate(0,0)" writing-mode="lr" x="60.4" xml:space="preserve" y="230.39" zvalue="1340">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,243.464,225.643) scale(1,1) translate(0,0)" writing-mode="lr" x="243.46" xml:space="preserve" y="230.14" zvalue="1341">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,796.429,938.125) scale(1,1) translate(0,0)" writing-mode="lr" x="796.4299999999999" xml:space="preserve" y="942.63" zvalue="1420">10kV电站线</text>
 </g>
 <g id="ButtonClass">
  <g href="35kV芒海变_全站公用.svg"><rect fill-opacity="0" height="24" width="72.88" x="48.71" y="306.64" zvalue="1310"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="48.71" y="351.5" zvalue="1412"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="48.71" y="394.25" zvalue="1414"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="183" y="351.5" zvalue="1416"/></g>
  <g href="小电流装置20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="182" y="394.25" zvalue="1418"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="128">
   <path class="kv35" d="M 573.67 337.59 L 1656 337.59" stroke-width="6" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674400600067" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674400600067"/></metadata>
  <path d="M 573.67 337.59 L 1656 337.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="330">
   <path class="kv10" d="M 443.5 703 L 1098.5 703" stroke-width="6" zvalue="1199"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674400665603" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674400665603"/></metadata>
  <path d="M 443.5 703 L 1098.5 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="331">
   <path class="kv10" d="M 1186 703 L 1821 703" stroke-width="6" zvalue="1201"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674400731139" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674400731139"/></metadata>
  <path d="M 1186 703 L 1821 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="218">
   <use class="kv35" height="30" transform="rotate(0,1305.57,179.876) scale(-1.25,-1.25) translate(-2346.27,-320.027)" width="30" x="1286.816495323294" xlink:href="#Accessory:避雷器PT带熔断器_0" y="161.1261755485893" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453837062146" ObjectName="35kV母线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1305.57,179.876) scale(-1.25,-1.25) translate(-2346.27,-320.027)" width="30" x="1286.816495323294" y="161.1261755485893"/></g>
  <g id="15">
   <use class="kv35" height="26" transform="rotate(90,956.306,115.466) scale(0.838049,0.927421) translate(183.833,8.09273)" width="12" x="951.2781809263648" xlink:href="#Accessory:避雷器1_0" y="103.4095937318383" zvalue="312"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453837127682" ObjectName="35kV遮海线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,956.306,115.466) scale(0.838049,0.927421) translate(183.833,8.09273)" width="12" x="951.2781809263648" y="103.4095937318383"/></g>
  <g id="35">
   <use class="kv35" height="30" transform="rotate(270,1076.07,127.304) scale(1.1536,1.32878) translate(-140.976,-26.5671)" width="30" x="1058.764269678736" xlink:href="#Accessory:PT象达_0" y="107.3723762839971" zvalue="319"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453837651970" ObjectName="35kV遮海线3419PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1076.07,127.304) scale(1.1536,1.32878) translate(-140.976,-26.5671)" width="30" x="1058.764269678736" y="107.3723762839971"/></g>
  <g id="477">
   <use class="kv10" height="26" transform="rotate(0,688.459,891.719) scale(-0.838049,0.927421) translate(-1510.93,68.8415)" width="12" x="683.4303009724935" xlink:href="#Accessory:避雷器1_0" y="879.6626333680678" zvalue="875"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453838897155" ObjectName="10kV政府线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,688.459,891.719) scale(-0.838049,0.927421) translate(-1510.93,68.8415)" width="12" x="683.4303009724935" y="879.6626333680678"/></g>
  <g id="463">
   <use class="kv10" height="26" transform="rotate(0,824.459,891.719) scale(-0.838049,0.927421) translate(-1809.21,68.8415)" width="12" x="819.4303009724936" xlink:href="#Accessory:避雷器1_0" y="879.6626333680678" zvalue="894"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453838635012" ObjectName="10kV电站线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,824.459,891.719) scale(-0.838049,0.927421) translate(-1809.21,68.8415)" width="12" x="819.4303009724936" y="879.6626333680678"/></g>
  <g id="569">
   <use class="kv10" height="40" transform="rotate(180,772.743,895.081) scale(-1.1829,0.970934) translate(-1423.26,26.2138)" width="30" x="755" xlink:href="#Accessory:带熔断器的线路PT1_0" y="875.6626333680678" zvalue="995"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453839028227" ObjectName="10kV电站线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,772.743,895.081) scale(-1.1829,0.970934) translate(-1423.26,26.2138)" width="30" x="755" y="875.6626333680678"/></g>
  <g id="65">
   <use class="kv10" height="26" transform="rotate(0,965.459,891.719) scale(-0.838049,0.927421) translate(-2118.46,68.8415)" width="12" x="960.4303009724933" xlink:href="#Accessory:避雷器1_0" y="879.6626333680678" zvalue="1016"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453839224835" ObjectName="10kV赖南线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,965.459,891.719) scale(-0.838049,0.927421) translate(-2118.46,68.8415)" width="12" x="960.4303009724933" y="879.6626333680678"/></g>
  <g id="238">
   <use class="kv10" height="26" transform="rotate(0,1414.46,892.719) scale(-0.838049,0.927421) translate(-3103.23,68.9197)" width="12" x="1409.430300972494" xlink:href="#Accessory:避雷器1_0" y="880.6626333680678" zvalue="1052"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453839749123" ObjectName="10kV勐古线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1414.46,892.719) scale(-0.838049,0.927421) translate(-3103.23,68.9197)" width="12" x="1409.430300972494" y="880.6626333680678"/></g>
  <g id="217">
   <use class="kv10" height="26" transform="rotate(0,1598.46,892.719) scale(-0.838049,0.927421) translate(-3506.79,68.9197)" width="12" x="1593.430300972494" xlink:href="#Accessory:避雷器1_0" y="880.6626333680678" zvalue="1067"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453839486979" ObjectName="10kV市场线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1598.46,892.719) scale(-0.838049,0.927421) translate(-3506.79,68.9197)" width="12" x="1593.430300972494" y="880.6626333680678"/></g>
  <g id="266">
   <use class="kv10" height="26" transform="rotate(270,1558.35,538.554) scale(-0.838049,0.927421) translate(-3418.82,41.2031)" width="12" x="1553.320092585765" xlink:href="#Accessory:避雷器1_0" y="526.4972560695005" zvalue="1144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453839945731" ObjectName="#2主变低压侧002避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1558.35,538.554) scale(-0.838049,0.927421) translate(-3418.82,41.2031)" width="12" x="1553.320092585765" y="526.4972560695005"/></g>
  <g id="320">
   <use class="kv10" height="26" transform="rotate(270,875.605,541.745) scale(-0.838049,0.927421) translate(-1921.39,41.4529)" width="12" x="870.576841585009" xlink:href="#Accessory:避雷器1_0" y="529.6888482885394" zvalue="1182"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453840142339" ObjectName="#1主变低压侧001避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,875.605,541.745) scale(-0.838049,0.927421) translate(-1921.39,41.4529)" width="12" x="870.576841585009" y="529.6888482885394"/></g>
  <g id="100">
   <use class="kv10" height="30" transform="rotate(180,581,584.25) scale(-1.86667,1.86667) translate(-879.25,-258.259)" width="30" x="553" xlink:href="#Accessory:pt带容断器_0" y="556.25" zvalue="1208"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453840338947" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,581,584.25) scale(-1.86667,1.86667) translate(-879.25,-258.259)" width="30" x="553" y="556.25"/></g>
  <g id="99">
   <use class="kv10" height="26" transform="rotate(0,615.297,606.888) scale(-0.838049,-0.927421) translate(-1350.47,-1262.21)" width="12" x="610.2691068183944" xlink:href="#Accessory:避雷器1_0" y="594.8316691257131" zvalue="1210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453840273411" ObjectName="10kVⅠ段母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,615.297,606.888) scale(-0.838049,-0.927421) translate(-1350.47,-1262.21)" width="12" x="610.2691068183944" y="594.8316691257131"/></g>
  <g id="119">
   <use class="kv10" height="30" transform="rotate(180,1638.25,603.75) scale(-1.86667,1.86667) translate(-2502.88,-267.312)" width="30" x="1610.25" xlink:href="#Accessory:pt带容断器_0" y="575.75" zvalue="1216"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453840535555" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1638.25,603.75) scale(-1.86667,1.86667) translate(-2502.88,-267.312)" width="30" x="1610.25" y="575.75"/></g>
  <g id="116">
   <use class="kv10" height="26" transform="rotate(0,1668.03,630.306) scale(-0.838049,-0.927421) translate(-3659.37,-1310.88)" width="12" x="1663" xlink:href="#Accessory:避雷器1_0" y="618.25" zvalue="1220"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453840470019" ObjectName="10kVⅡ段母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1668.03,630.306) scale(-0.838049,-0.927421) translate(-3659.37,-1310.88)" width="12" x="1663" y="618.25"/></g>
  <g id="175">
   <use class="kv10" height="26" transform="rotate(0,532.129,888.683) scale(-0.561913,1.11694) translate(-1481.75,-91.5185)" width="12" x="528.7571928754576" xlink:href="#Accessory:避雷器1_0" y="874.1626432396941" zvalue="1229"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453840863235" ObjectName="10kV#1无功补偿装置避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,532.129,888.683) scale(-0.561913,1.11694) translate(-1481.75,-91.5185)" width="12" x="528.7571928754576" y="874.1626432396941"/></g>
  <g id="86">
   <use class="kv10" height="26" transform="rotate(0,1767.46,892.719) scale(-0.838049,0.927421) translate(-3877.45,68.9197)" width="12" x="1762.430300972494" xlink:href="#Accessory:避雷器1_0" y="880.6626333680678" zvalue="1255"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453841321987" ObjectName="10kV黑勐陇线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1767.46,892.719) scale(-0.838049,0.927421) translate(-3877.45,68.9197)" width="12" x="1762.430300972494" y="880.6626333680678"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="533">
   <use class="kv35" height="30" transform="rotate(180,935.058,214.919) scale(1.53571,-1.53571) translate(-318.683,-346.83)" width="28" x="913.5577567270731" xlink:href="#EnergyConsumer:站用变DY接地_0" y="191.8831160161405" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453838176258" ObjectName="35kV#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,935.058,214.919) scale(1.53571,-1.53571) translate(-318.683,-346.83)" width="28" x="913.5577567270731" y="191.8831160161405"/></g>
  <g id="476">
   <use class="kv10" height="30" transform="rotate(0,661.429,905.25) scale(1.25,-1.25) translate(-130.786,-1625.7)" width="12" x="653.9287060935333" xlink:href="#EnergyConsumer:负荷_0" y="886.5" zvalue="876"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453838831619" ObjectName="10kV政府线"/>
   <cge:TPSR_Ref TObjectID="6192453838831619"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,661.429,905.25) scale(1.25,-1.25) translate(-130.786,-1625.7)" width="12" x="653.9287060935333" y="886.5"/></g>
  <g id="64">
   <use class="kv10" height="30" transform="rotate(0,938.429,905.25) scale(1.25,-1.25) translate(-186.186,-1625.7)" width="12" x="930.9287060935333" xlink:href="#EnergyConsumer:负荷_0" y="886.5" zvalue="1017"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453839159299" ObjectName="10kV赖南线"/>
   <cge:TPSR_Ref TObjectID="6192453839159299"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,938.429,905.25) scale(1.25,-1.25) translate(-186.186,-1625.7)" width="12" x="930.9287060935333" y="886.5"/></g>
  <g id="237">
   <use class="kv10" height="30" transform="rotate(0,1387.43,906.25) scale(1.25,-1.25) translate(-275.986,-1627.5)" width="12" x="1379.928706093533" xlink:href="#EnergyConsumer:负荷_0" y="887.5" zvalue="1053"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453839683587" ObjectName="10kV勐古线"/>
   <cge:TPSR_Ref TObjectID="6192453839683587"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1387.43,906.25) scale(1.25,-1.25) translate(-275.986,-1627.5)" width="12" x="1379.928706093533" y="887.5"/></g>
  <g id="216">
   <use class="kv10" height="30" transform="rotate(0,1571.43,906.25) scale(1.25,-1.25) translate(-312.786,-1627.5)" width="12" x="1563.928706093533" xlink:href="#EnergyConsumer:负荷_0" y="887.5" zvalue="1068"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453839421443" ObjectName="10kV市场线"/>
   <cge:TPSR_Ref TObjectID="6192453839421443"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1571.43,906.25) scale(1.25,-1.25) translate(-312.786,-1627.5)" width="12" x="1563.928706093533" y="887.5"/></g>
  <g id="72">
   <use class="kv10" height="30" transform="rotate(0,1740.43,906.25) scale(1.25,-1.25) translate(-346.586,-1627.5)" width="12" x="1732.928706093533" xlink:href="#EnergyConsumer:负荷_0" y="887.5" zvalue="1256"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453841256451" ObjectName="10kV黑勐陇线"/>
   <cge:TPSR_Ref TObjectID="6192453841256451"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1740.43,906.25) scale(1.25,-1.25) translate(-346.586,-1627.5)" width="12" x="1732.928706093533" y="887.5"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="53">
   <use class="kv35" height="30" transform="rotate(0,935,164.123) scale(1,1) translate(0,0)" width="15" x="927.5000000000001" xlink:href="#Disconnector:令克_0" y="149.1233757564003" zvalue="171"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453838110722" ObjectName="35kV#1站用变熔断丝"/>
   <cge:TPSR_Ref TObjectID="6192453838110722"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,935,164.123) scale(1,1) translate(0,0)" width="15" x="927.5000000000001" y="149.1233757564003"/></g>
  <g id="68">
   <use class="kv35" height="30" transform="rotate(0,990.711,183.276) scale(-0.947693,0.6712) translate(-2036.5,84.8488)" width="15" x="983.6034928921569" xlink:href="#Disconnector:刀闸_0" y="173.2075673790615" zvalue="301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453837389826" ObjectName="35kV遮海线3416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453837389826"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,990.711,183.276) scale(-0.947693,0.6712) translate(-2036.5,84.8488)" width="15" x="983.6034928921569" y="173.2075673790615"/></g>
  <g id="20">
   <use class="kv35" height="30" transform="rotate(180,990.892,306.544) scale(0.947693,-0.6712) translate(54.2991,-768.187)" width="15" x="983.7841334259955" xlink:href="#Disconnector:刀闸_0" y="296.4763051659212" zvalue="306"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453837193218" ObjectName="35kV遮海线3411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453837193218"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,990.892,306.544) scale(0.947693,-0.6712) translate(54.2991,-768.187)" width="15" x="983.7841334259955" y="296.4763051659212"/></g>
  <g id="304">
   <use class="kv35" height="30" transform="rotate(180,1306.89,278.628) scale(0.947693,-0.6712) translate(71.7404,-698.678)" width="15" x="1299.784133425996" xlink:href="#Disconnector:刀闸_0" y="268.5596384992546" zvalue="567"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453838241795" ObjectName="35kV母线PT3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453838241795"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1306.89,278.628) scale(0.947693,-0.6712) translate(71.7404,-698.678)" width="15" x="1299.784133425996" y="268.5596384992546"/></g>
  <g id="4">
   <use class="kv35" height="30" transform="rotate(270,1031,127.123) scale(1,1) translate(0,0)" width="15" x="1023.5" xlink:href="#Disconnector:令克_0" y="112.1233757564003" zvalue="726"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453838307332" ObjectName="35kV遮海线PT熔断丝"/>
   <cge:TPSR_Ref TObjectID="6192453838307332"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1031,127.123) scale(1,1) translate(0,0)" width="15" x="1023.5" y="112.1233757564003"/></g>
  <g id="479">
   <use class="kv10" height="30" transform="rotate(180,661.371,830.391) scale(-0.947693,-0.6712) translate(-1359.64,-2072.5)" width="15" x="654.2630441166826" xlink:href="#Disconnector:刀闸_0" y="820.323089599609" zvalue="871"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453838962691" ObjectName="10kV政府线0426隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453838962691"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,661.371,830.391) scale(-0.947693,-0.6712) translate(-1359.64,-2072.5)" width="15" x="654.2630441166826" y="820.323089599609"/></g>
  <g id="472">
   <use class="kv10" height="30" transform="rotate(180,659.689,734.391) scale(-0.947693,-0.6712) translate(-1356.18,-1833.47)" width="15" x="652.5809425293291" xlink:href="#Disconnector:刀闸_0" y="724.3230764122643" zvalue="881"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453838766083" ObjectName="10kV政府线0421隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453838766083"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,659.689,734.391) scale(-0.947693,-0.6712) translate(-1356.18,-1833.47)" width="15" x="652.5809425293291" y="724.3230764122643"/></g>
  <g id="465">
   <use class="kv10" height="30" transform="rotate(180,797.371,830.391) scale(-0.947693,-0.6712) translate(-1639.14,-2072.5)" width="15" x="790.2630441166826" xlink:href="#Disconnector:刀闸_0" y="820.3230764122643" zvalue="890"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453838700548" ObjectName="10kV电站线0436隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453838700548"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,797.371,830.391) scale(-0.947693,-0.6712) translate(-1639.14,-2072.5)" width="15" x="790.2630441166826" y="820.3230764122643"/></g>
  <g id="457">
   <use class="kv10" height="30" transform="rotate(180,795.689,734.391) scale(-0.947693,-0.6712) translate(-1635.69,-1833.47)" width="15" x="788.5809425293291" xlink:href="#Disconnector:刀闸_0" y="724.3230764122643" zvalue="900"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453838503940" ObjectName="10kV电站线0431隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453838503940"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,795.689,734.391) scale(-0.947693,-0.6712) translate(-1635.69,-1833.47)" width="15" x="788.5809425293291" y="724.3230764122643"/></g>
  <g id="436">
   <use class="kv10" height="30" transform="rotate(0,1062.11,669.568) scale(0.947693,-0.6712) translate(58.2298,-1672.07)" width="15" x="1055" xlink:href="#Disconnector:刀闸_0" y="659.5" zvalue="928"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453838438404" ObjectName="10kV分段0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453838438404"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1062.11,669.568) scale(0.947693,-0.6712) translate(58.2298,-1672.07)" width="15" x="1055" y="659.5"/></g>
  <g id="434">
   <use class="kv10" height="30" transform="rotate(0,1215.11,669.568) scale(0.947693,-0.6712) translate(66.6745,-1672.07)" width="15" x="1208" xlink:href="#Disconnector:刀闸_0" y="659.5" zvalue="931"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453838372868" ObjectName="10kV分段0122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453838372868"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1215.11,669.568) scale(0.947693,-0.6712) translate(66.6745,-1672.07)" width="15" x="1208" y="659.5"/></g>
  <g id="81">
   <use class="kv10" height="30" transform="rotate(180,938.371,830.391) scale(-0.947693,-0.6712) translate(-1928.93,-2072.5)" width="15" x="931.2630441166826" xlink:href="#Disconnector:刀闸_0" y="820.323089599609" zvalue="1012"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453839290371" ObjectName="10kV赖南线0446隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453839290371"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,938.371,830.391) scale(-0.947693,-0.6712) translate(-1928.93,-2072.5)" width="15" x="931.2630441166826" y="820.323089599609"/></g>
  <g id="60">
   <use class="kv10" height="30" transform="rotate(180,936.689,734.391) scale(-0.947693,-0.6712) translate(-1925.47,-1833.47)" width="15" x="929.5809425293291" xlink:href="#Disconnector:刀闸_0" y="724.3230764122643" zvalue="1022"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453839093763" ObjectName="10kV赖南线0441隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453839093763"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,936.689,734.391) scale(-0.947693,-0.6712) translate(-1925.47,-1833.47)" width="15" x="929.5809425293291" y="724.3230764122643"/></g>
  <g id="240">
   <use class="kv10" height="30" transform="rotate(180,1387.37,831.391) scale(-0.947693,-0.6712) translate(-2851.71,-2074.99)" width="15" x="1380.263044116683" xlink:href="#Disconnector:刀闸_0" y="821.323089599609" zvalue="1048"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453839814659" ObjectName="10kV勐古线0456隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453839814659"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1387.37,831.391) scale(-0.947693,-0.6712) translate(-2851.71,-2074.99)" width="15" x="1380.263044116683" y="821.323089599609"/></g>
  <g id="231">
   <use class="kv10" height="30" transform="rotate(180,1385.69,735.391) scale(-0.947693,-0.6712) translate(-2848.25,-1835.96)" width="15" x="1378.580942529329" xlink:href="#Disconnector:刀闸_0" y="725.3230764122643" zvalue="1058"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453839618051" ObjectName="10kV勐古线0452隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453839618051"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1385.69,735.391) scale(-0.947693,-0.6712) translate(-2848.25,-1835.96)" width="15" x="1378.580942529329" y="725.3230764122643"/></g>
  <g id="222">
   <use class="kv10" height="30" transform="rotate(180,1571.37,831.391) scale(-0.947693,-0.6712) translate(-3229.86,-2074.99)" width="15" x="1564.263044116683" xlink:href="#Disconnector:刀闸_0" y="821.3230764122643" zvalue="1063"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453839552515" ObjectName="10kV市场线0466隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453839552515"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1571.37,831.391) scale(-0.947693,-0.6712) translate(-3229.86,-2074.99)" width="15" x="1564.263044116683" y="821.3230764122643"/></g>
  <g id="211">
   <use class="kv10" height="30" transform="rotate(180,1569.69,735.391) scale(-0.947693,-0.6712) translate(-3226.41,-1835.96)" width="15" x="1562.580942529329" xlink:href="#Disconnector:刀闸_0" y="725.3230764122643" zvalue="1073"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453839355907" ObjectName="10kV市场线0462隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453839355907"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1569.69,735.391) scale(-0.947693,-0.6712) translate(-3226.41,-1835.96)" width="15" x="1562.580942529329" y="725.3230764122643"/></g>
  <g id="269">
   <use class="kv35" height="30" transform="rotate(0,1519.49,371.817) scale(0.947693,-0.6712) translate(83.4744,-930.708)" width="15" x="1512.379065052385" xlink:href="#Disconnector:刀闸_0" y="361.7491439940568" zvalue="1140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453840011267" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453840011267"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1519.49,371.817) scale(0.947693,-0.6712) translate(83.4744,-930.708)" width="15" x="1512.379065052385" y="361.7491439940568"/></g>
  <g id="261">
   <use class="kv10" height="30" transform="rotate(180,1517.39,619.939) scale(-0.947693,0.6712) translate(-3118.92,298.756)" width="15" x="1510.279600513215" xlink:href="#Disconnector:刀闸_0" y="609.8710562102442" zvalue="1150"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453839880195" ObjectName="#2主变10kV侧0022隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453839880195"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1517.39,619.939) scale(-0.947693,0.6712) translate(-3118.92,298.756)" width="15" x="1510.279600513215" y="609.8710562102442"/></g>
  <g id="323">
   <use class="kv35" height="30" transform="rotate(0,835.172,375.567) scale(0.947693,-0.6712) translate(45.7043,-940.045)" width="15" x="828.0647612382003" xlink:href="#Disconnector:刀闸_0" y="365.4991439940569" zvalue="1178"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453840207875" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453840207875"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,835.172,375.567) scale(0.947693,-0.6712) translate(45.7043,-940.045)" width="15" x="828.0647612382003" y="365.4991439940569"/></g>
  <g id="314">
   <use class="kv10" height="30" transform="rotate(180,834.122,619.689) scale(-0.947693,0.6712) translate(-1714.67,298.634)" width="15" x="827.0138817028078" xlink:href="#Disconnector:刀闸_0" y="609.6210562102442" zvalue="1188"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453840076803" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453840076803"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,834.122,619.689) scale(-0.947693,0.6712) translate(-1714.67,298.634)" width="15" x="827.0138817028078" y="609.6210562102442"/></g>
  <g id="107">
   <use class="kv10" height="30" transform="rotate(180,580.965,661.654) scale(0.947693,0.6712) translate(31.6735,319.191)" width="15" x="573.8569348729384" xlink:href="#Disconnector:刀闸_0" y="651.5855178058908" zvalue="1204"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453840404483" ObjectName="10kVⅠ段母线0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453840404483"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,580.965,661.654) scale(0.947693,0.6712) translate(31.6735,319.191)" width="15" x="573.8569348729384" y="651.5855178058908"/></g>
  <g id="185">
   <use class="kv10" height="30" transform="rotate(180,508.5,830.891) scale(-0.947693,0.6712) translate(-1045.46,402.095)" width="15" x="501.3924507585767" xlink:href="#Disconnector:刀闸_0" y="820.8230897637047" zvalue="1225"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453840928771" ObjectName="10kV1号电容器0416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453840928771"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,508.5,830.891) scale(-0.947693,0.6712) translate(-1045.46,402.095)" width="15" x="501.3924507585767" y="820.8230897637047"/></g>
  <g id="170">
   <use class="kv10" height="30" transform="rotate(180,506.787,733.891) scale(-0.947693,-0.6712) translate(-1041.94,-1832.22)" width="15" x="499.6789701270669" xlink:href="#Disconnector:刀闸_0" y="723.8230764122643" zvalue="1231"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453840797699" ObjectName="10kV1号电容器0411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453840797699"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,506.787,733.891) scale(-0.947693,-0.6712) translate(-1041.94,-1832.22)" width="15" x="499.6789701270669" y="723.8230764122643"/></g>
  <g id="191">
   <use class="kv10" height="30" transform="rotate(180,1638.21,672.154) scale(0.947693,0.6712) translate(90.0275,324.334)" width="15" x="1631.106934872938" xlink:href="#Disconnector:刀闸_0" y="662.0855178058908" zvalue="1244"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453841125379" ObjectName="10kVⅡ段母线0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453841125379"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1638.21,672.154) scale(0.947693,0.6712) translate(90.0275,324.334)" width="15" x="1631.106934872938" y="662.0855178058908"/></g>
  <g id="92">
   <use class="kv10" height="30" transform="rotate(180,1740.37,831.391) scale(-0.947693,-0.6712) translate(-3577.19,-2074.99)" width="15" x="1733.263044116683" xlink:href="#Disconnector:刀闸_0" y="821.3230764122643" zvalue="1251"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453841387522" ObjectName="10kV黑勐陇线0476隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453841387522"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1740.37,831.391) scale(-0.947693,-0.6712) translate(-3577.19,-2074.99)" width="15" x="1733.263044116683" y="821.3230764122643"/></g>
  <g id="46">
   <use class="kv10" height="30" transform="rotate(180,1738.69,735.391) scale(-0.947693,-0.6712) translate(-3573.73,-1835.96)" width="15" x="1731.580942529329" xlink:href="#Disconnector:刀闸_0" y="725.3230764122643" zvalue="1261"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453841190915" ObjectName="10kV黑勐陇线0472隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453841190915"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1738.69,735.391) scale(-0.947693,-0.6712) translate(-3573.73,-1835.96)" width="15" x="1731.580942529329" y="725.3230764122643"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="70">
   <use class="kv35" height="30" transform="rotate(0,990.628,93.5317) scale(1.98323,0.522926) translate(-487.684,78.1742)" width="7" x="983.6867041796982" xlink:href="#ACLineSegment:线路_0" y="85.68776459659705" zvalue="297"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249324453892" ObjectName="35kV遮海线"/>
   <cge:TPSR_Ref TObjectID="8444249324453892_5066549679423489"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,990.628,93.5317) scale(1.98323,0.522926) translate(-487.684,78.1742)" width="7" x="983.6867041796982" y="85.68776459659705"/></g>
 </g>
 <g id="BreakerClass">
  <g id="69">
   <use class="kv35" height="20" transform="rotate(0,990.705,248.708) scale(1.5542,1.35421) translate(-350.496,-61.5105)" width="10" x="982.9340345345652" xlink:href="#Breaker:开关_0" y="235.165750510661" zvalue="298"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925099388932" ObjectName="35kV遮海线341断路器"/>
   <cge:TPSR_Ref TObjectID="6473925099388932"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,990.705,248.708) scale(1.5542,1.35421) translate(-350.496,-61.5105)" width="10" x="982.9340345345652" y="235.165750510661"/></g>
  <g id="478">
   <use class="kv10" height="20" transform="rotate(180,661.402,777.868) scale(1.5542,1.35421) translate(-233.073,-199.919)" width="10" x="653.6311232494973" xlink:href="#Breaker:开关_0" y="764.3258941321849" zvalue="873"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925099585540" ObjectName="10kV政府线042断路器"/>
   <cge:TPSR_Ref TObjectID="6473925099585540"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,661.402,777.868) scale(1.5542,1.35421) translate(-233.073,-199.919)" width="10" x="653.6311232494973" y="764.3258941321849"/></g>
  <g id="464">
   <use class="kv10" height="20" transform="rotate(180,797.402,777.868) scale(1.5542,1.35421) translate(-281.568,-199.919)" width="10" x="789.6311232494973" xlink:href="#Breaker:开关_0" y="764.3258941321849" zvalue="892"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925099520004" ObjectName="10kV电站线043断路器"/>
   <cge:TPSR_Ref TObjectID="6473925099520004"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,797.402,777.868) scale(1.5542,1.35421) translate(-281.568,-199.919)" width="10" x="789.6311232494973" y="764.3258941321849"/></g>
  <g id="425">
   <use class="kv10" height="20" transform="rotate(90,1140.5,626.25) scale(1.65,1.25) translate(-446.038,-122.75)" width="10" x="1132.25" xlink:href="#Breaker:母联开关_0" y="613.75" zvalue="934"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925099454468" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473925099454468"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1140.5,626.25) scale(1.65,1.25) translate(-446.038,-122.75)" width="10" x="1132.25" y="613.75"/></g>
  <g id="66">
   <use class="kv10" height="20" transform="rotate(180,938.402,777.868) scale(1.5542,1.35421) translate(-331.846,-199.919)" width="10" x="930.6311232494973" xlink:href="#Breaker:开关_0" y="764.3258941321849" zvalue="1014"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925099651076" ObjectName="10kV赖南线044断路器"/>
   <cge:TPSR_Ref TObjectID="6473925099651076"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,938.402,777.868) scale(1.5542,1.35421) translate(-331.846,-199.919)" width="10" x="930.6311232494973" y="764.3258941321849"/></g>
  <g id="239">
   <use class="kv10" height="20" transform="rotate(180,1387.4,778.868) scale(1.5542,1.35421) translate(-491.951,-200.18)" width="10" x="1379.631123249497" xlink:href="#Breaker:开关_0" y="765.3258941321849" zvalue="1050"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925099782148" ObjectName="10kV勐古线045断路器"/>
   <cge:TPSR_Ref TObjectID="6473925099782148"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1387.4,778.868) scale(1.5542,1.35421) translate(-491.951,-200.18)" width="10" x="1379.631123249497" y="765.3258941321849"/></g>
  <g id="221">
   <use class="kv10" height="20" transform="rotate(180,1571.4,778.868) scale(1.5542,1.35421) translate(-557.563,-200.18)" width="10" x="1563.631123249497" xlink:href="#Breaker:开关_0" y="765.3258941321849" zvalue="1065"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925099716612" ObjectName="10kV市场线046断路器"/>
   <cge:TPSR_Ref TObjectID="6473925099716612"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1571.4,778.868) scale(1.5542,1.35421) translate(-557.563,-200.18)" width="10" x="1563.631123249497" y="765.3258941321849"/></g>
  <g id="272">
   <use class="kv10" height="20" transform="rotate(180,1517.24,576.701) scale(1.5542,1.35421) translate(-538.249,-147.301)" width="10" x="1509.467827355794" xlink:href="#Breaker:开关_0" y="563.1590909090909" zvalue="1136"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925099913220" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473925099913220"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1517.24,576.701) scale(1.5542,1.35421) translate(-538.249,-147.301)" width="10" x="1509.467827355794" y="563.1590909090909"/></g>
  <g id="270">
   <use class="kv35" height="20" transform="rotate(180,1519.49,416.154) scale(1.5542,1.35421) translate(-539.05,-105.308)" width="10" x="1511.715765239444" xlink:href="#Breaker:开关_0" y="402.6115035528057" zvalue="1138"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925099847684" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925099847684"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1519.49,416.154) scale(1.5542,1.35421) translate(-539.05,-105.308)" width="10" x="1511.715765239444" y="402.6115035528057"/></g>
  <g id="325">
   <use class="kv10" height="20" transform="rotate(180,834.496,576.451) scale(1.5542,1.35421) translate(-294.795,-147.236)" width="10" x="826.7245763550374" xlink:href="#Breaker:开关_0" y="562.9090909090909" zvalue="1174"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925100044292" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925100044292"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,834.496,576.451) scale(1.5542,1.35421) translate(-294.795,-147.236)" width="10" x="826.7245763550374" y="562.9090909090909"/></g>
  <g id="324">
   <use class="kv35" height="20" transform="rotate(180,835.359,416.154) scale(1.5542,1.35421) translate(-295.103,-105.308)" width="10" x="827.5882603069475" xlink:href="#Breaker:开关_0" y="402.6115036010742" zvalue="1176"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925099978756" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925099978756"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,835.359,416.154) scale(1.5542,1.35421) translate(-295.103,-105.308)" width="10" x="827.5882603069475" y="402.6115036010742"/></g>
  <g id="176">
   <use class="kv10" height="20" transform="rotate(180,508.5,771.701) scale(1.5542,1.35421) translate(-178.551,-198.306)" width="10" x="500.7291508472352" xlink:href="#Breaker:开关_0" y="758.1592274655184" zvalue="1227"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925100109828" ObjectName="10kV1号电容器041断路器"/>
   <cge:TPSR_Ref TObjectID="6473925100109828"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,508.5,771.701) scale(1.5542,1.35421) translate(-178.551,-198.306)" width="10" x="500.7291508472352" y="758.1592274655184"/></g>
  <g id="91">
   <use class="kv10" height="20" transform="rotate(180,1740.4,778.868) scale(1.5542,1.35421) translate(-617.825,-200.18)" width="10" x="1732.631123249497" xlink:href="#Breaker:开关_0" y="765.3258941321849" zvalue="1253"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925100175364" ObjectName="10kV黑勐陇线047断路器"/>
   <cge:TPSR_Ref TObjectID="6473925100175364"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1740.4,778.868) scale(1.5542,1.35421) translate(-617.825,-200.18)" width="10" x="1732.631123249497" y="765.3258941321849"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="67">
   <use class="kv35" height="20" transform="rotate(90,1020.5,148.002) scale(1.24619,-1.0068) translate(-200.371,-294.937)" width="10" x="1014.268227141073" xlink:href="#GroundDisconnector:地刀_0" y="137.9342824715292" zvalue="303"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453837324290" ObjectName="35kV遮海线34167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453837324290"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1020.5,148.002) scale(1.24619,-1.0068) translate(-200.371,-294.937)" width="10" x="1014.268227141073" y="137.9342824715292"/></g>
  <g id="33">
   <use class="kv35" height="20" transform="rotate(90,1020.5,276.002) scale(1.24619,-1.0068) translate(-200.371,-550.072)" width="10" x="1014.268227141073" xlink:href="#GroundDisconnector:地刀_0" y="265.9342824715292" zvalue="315"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453837586434" ObjectName="35kV遮海线34117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453837586434"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1020.5,276.002) scale(1.24619,-1.0068) translate(-200.371,-550.072)" width="10" x="1014.268227141073" y="265.9342824715292"/></g>
  <g id="103">
   <use class="kv35" height="20" transform="rotate(90,1352.82,240.57) scale(1.24619,-1.0068) translate(-266.021,-479.448)" width="10" x="1346.586408959255" xlink:href="#GroundDisconnector:地刀_0" y="230.502464289711" zvalue="353"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453837783042" ObjectName="35kV母线PT39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453837783042"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1352.82,240.57) scale(1.24619,-1.0068) translate(-266.021,-479.448)" width="10" x="1346.586408959255" y="230.502464289711"/></g>
  <g id="114">
   <use class="kv35" height="20" transform="rotate(90,1352.82,308.57) scale(1.24619,-1.0068) translate(-266.021,-614.989)" width="10" x="1346.586408959255" xlink:href="#GroundDisconnector:地刀_0" y="298.502464289711" zvalue="357"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453837914114" ObjectName="35kV母线PT39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453837914114"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1352.82,308.57) scale(1.24619,-1.0068) translate(-266.021,-614.989)" width="10" x="1346.586408959255" y="298.502464289711"/></g>
  <g id="294">
   <use class="kv35" height="20" transform="rotate(90,1020.5,208.002) scale(1.24619,-1.0068) translate(-200.371,-414.532)" width="10" x="1014.268227141073" xlink:href="#GroundDisconnector:地刀_0" y="197.9342824715292" zvalue="553"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453838045186" ObjectName="35kV遮海线34160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453838045186"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1020.5,208.002) scale(1.24619,-1.0068) translate(-200.371,-414.532)" width="10" x="1014.268227141073" y="197.9342824715292"/></g>
  <g id="190">
   <use class="kv10" height="20" transform="rotate(0,508.646,939.219) scale(-1.24619,1.0068) translate(-915.576,-6.27614)" width="10" x="502.4147090440605" xlink:href="#GroundDisconnector:地刀_0" y="929.1511097754801" zvalue="1223"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453841059843" ObjectName="10kV1号电容器04160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453841059843"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,508.646,939.219) scale(-1.24619,1.0068) translate(-915.576,-6.27614)" width="10" x="502.4147090440605" y="929.1511097754801"/></g>
  <g id="131">
   <use class="kv10" height="20" transform="rotate(270,483.901,808.731) scale(-1.24619,-1.0068) translate(-870.976,-1611.93)" width="10" x="477.6705537030783" xlink:href="#GroundDisconnector:地刀_0" y="798.6629272741211" zvalue="1240"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453840666627" ObjectName="10kV1号电容器04167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453840666627"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,483.901,808.731) scale(-1.24619,-1.0068) translate(-870.976,-1611.93)" width="10" x="477.6705537030783" y="798.6629272741211"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="21">
   <path class="kv35" d="M 990.65 235.75 L 990.65 193.17" stroke-width="1" zvalue="305"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="68@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 990.65 235.75 L 990.65 193.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv35" d="M 990.63 173.54 L 990.63 101.3" stroke-width="1" zvalue="308"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="70@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 990.63 173.54 L 990.63 101.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv35" d="M 967.78 115.49 L 990.63 115.49" stroke-width="1" zvalue="309"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15@0" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 967.78 115.49 L 990.63 115.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv35" d="M 990.81 261.64 L 990.81 296.81" stroke-width="1" zvalue="310"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@1" LinkObjectIDznd="20@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 990.81 261.64 L 990.81 296.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv35" d="M 1010.68 148.06 L 990.63 148.06" stroke-width="1" zvalue="311"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 1010.68 148.06 L 990.63 148.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv35" d="M 990.83 316.44 L 990.83 337.59" stroke-width="1" zvalue="313"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@1" LinkObjectIDznd="128@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 990.83 316.44 L 990.83 337.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv35" d="M 1010.68 276.06 L 990.81 276.06" stroke-width="1" zvalue="317"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="17" MaxPinNum="2"/>
   </metadata>
  <path d="M 1010.68 276.06 L 990.81 276.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv35" d="M 1343 240.63 L 1306.98 240.63" stroke-width="1" zvalue="355"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="305" MaxPinNum="2"/>
   </metadata>
  <path d="M 1343 240.63 L 1306.98 240.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv35" d="M 1343 308.63 L 1306.83 308.63" stroke-width="1" zvalue="359"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="306" MaxPinNum="2"/>
   </metadata>
  <path d="M 1343 308.63 L 1306.83 308.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="293">
   <path class="kv35" d="M 1010.68 208.06 L 990.65 208.06" stroke-width="1" zvalue="555"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="294@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 1010.68 208.06 L 990.65 208.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="305">
   <path class="kv35" d="M 1306.98 197.29 L 1306.98 268.89" stroke-width="1" zvalue="568"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@0" LinkObjectIDznd="304@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1306.98 197.29 L 1306.98 268.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="306">
   <path class="kv35" d="M 1306.83 288.52 L 1306.83 337.59" stroke-width="1" zvalue="569"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="304@1" LinkObjectIDznd="128@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1306.83 288.52 L 1306.83 337.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv35" d="M 934.92 176.37 L 934.92 192.8" stroke-width="1" zvalue="723"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@1" LinkObjectIDznd="533@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 934.92 176.37 L 934.92 192.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv35" d="M 990.63 134.5 L 935.08 134.5 L 935.08 150.87" stroke-width="1" zvalue="724"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="19" LinkObjectIDznd="53@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 990.63 134.5 L 935.08 134.5 L 935.08 150.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv35" d="M 1066.32 126.73 L 1043.25 126.73" stroke-width="1" zvalue="727"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="4@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1066.32 126.73 L 1043.25 126.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv35" d="M 1017.75 127.04 L 990.63 127.04" stroke-width="1" zvalue="728"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="4@0" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 1017.75 127.04 L 990.63 127.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="475">
   <path class="kv10" d="M 661.43 840.29 L 661.43 888.38" stroke-width="1" zvalue="877"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="479@1" LinkObjectIDznd="476@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 661.43 840.29 L 661.43 888.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="474">
   <path class="kv10" d="M 688.43 880.25 L 688.43 866.33 L 661.43 866.33" stroke-width="1" zvalue="879"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="477@0" LinkObjectIDznd="475" MaxPinNum="2"/>
   </metadata>
  <path d="M 688.43 880.25 L 688.43 866.33 L 661.43 866.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="473">
   <path class="kv10" d="M 661.45 790.82 L 661.45 820.66" stroke-width="1" zvalue="880"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="478@0" LinkObjectIDznd="479@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 661.45 790.82 L 661.45 820.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="471">
   <path class="kv10" d="M 659.75 744.29 L 659.75 764.94" stroke-width="1" zvalue="883"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="472@1" LinkObjectIDznd="478@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 659.75 744.29 L 659.75 764.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="470">
   <path class="kv10" d="M 659.77 724.66 L 659.77 703" stroke-width="1" zvalue="884"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="472@0" LinkObjectIDznd="330@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 659.77 724.66 L 659.77 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="460">
   <path class="kv10" d="M 797.43 840.29 L 797.43 889.65" stroke-width="1" zvalue="896"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="465@1" LinkObjectIDznd="155@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 797.43 840.29 L 797.43 889.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="459">
   <path class="kv10" d="M 824.43 880.25 L 824.43 864.97 L 797.43 864.97" stroke-width="1" zvalue="898"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="463@0" LinkObjectIDznd="460" MaxPinNum="2"/>
   </metadata>
  <path d="M 824.43 880.25 L 824.43 864.97 L 797.43 864.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="458">
   <path class="kv10" d="M 797.45 790.82 L 797.45 820.66" stroke-width="1" zvalue="899"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="464@0" LinkObjectIDznd="465@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 797.45 790.82 L 797.45 820.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="456">
   <path class="kv10" d="M 795.75 744.29 L 795.75 764.94" stroke-width="1" zvalue="902"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="457@1" LinkObjectIDznd="464@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 795.75 744.29 L 795.75 764.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="455">
   <path class="kv10" d="M 795.77 724.66 L 795.77 703" stroke-width="1" zvalue="903"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="457@0" LinkObjectIDznd="330@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 795.77 724.66 L 795.77 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="453">
   <path class="kv10" d="M 772.74 877.12 L 772.74 866.33 L 798.4 866.33" stroke-width="1" zvalue="906"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="569@0" LinkObjectIDznd="459" MaxPinNum="2"/>
   </metadata>
  <path d="M 772.74 877.12 L 772.74 866.33 L 798.4 866.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="424">
   <path class="kv10" d="M 1062.17 659.67 L 1062.17 626.41 L 1128.13 626.41" stroke-width="1" zvalue="935"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="436@1" LinkObjectIDznd="425@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.17 659.67 L 1062.17 626.41 L 1128.13 626.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="421">
   <path class="kv10" d="M 1152.77 626.34 L 1215.17 626.34 L 1215.17 659.67" stroke-width="1" zvalue="937"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="425@1" LinkObjectIDznd="434@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1152.77 626.34 L 1215.17 626.34 L 1215.17 659.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv10" d="M 938.43 840.29 L 938.43 888.38" stroke-width="1" zvalue="1018"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@1" LinkObjectIDznd="64@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 938.43 840.29 L 938.43 888.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv10" d="M 965.43 880.25 L 965.43 866.33 L 938.43 866.33" stroke-width="1" zvalue="1020"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="63" MaxPinNum="2"/>
   </metadata>
  <path d="M 965.43 880.25 L 965.43 866.33 L 938.43 866.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv10" d="M 938.45 790.82 L 938.45 820.66" stroke-width="1" zvalue="1021"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="81@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 938.45 790.82 L 938.45 820.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv10" d="M 936.75 744.29 L 936.75 764.94" stroke-width="1" zvalue="1024"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@1" LinkObjectIDznd="66@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 936.75 744.29 L 936.75 764.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv10" d="M 936.77 724.66 L 936.77 703" stroke-width="1" zvalue="1025"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="330@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 936.77 724.66 L 936.77 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv10" d="M 1062.19 679.3 L 1062.19 703" stroke-width="1" zvalue="1044"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="436@0" LinkObjectIDznd="330@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.19 679.3 L 1062.19 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv10" d="M 1215.19 679.3 L 1215.19 703" stroke-width="1" zvalue="1045"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="434@0" LinkObjectIDznd="331@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1215.19 679.3 L 1215.19 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="236">
   <path class="kv10" d="M 1387.43 841.29 L 1387.43 889.38" stroke-width="1" zvalue="1054"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="240@1" LinkObjectIDznd="237@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1387.43 841.29 L 1387.43 889.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="kv10" d="M 1414.43 881.25 L 1414.43 867.33 L 1387.43 867.33" stroke-width="1" zvalue="1056"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="238@0" LinkObjectIDznd="236" MaxPinNum="2"/>
   </metadata>
  <path d="M 1414.43 881.25 L 1414.43 867.33 L 1387.43 867.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="kv10" d="M 1387.45 791.82 L 1387.45 821.66" stroke-width="1" zvalue="1057"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="239@0" LinkObjectIDznd="240@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1387.45 791.82 L 1387.45 821.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="230">
   <path class="kv10" d="M 1385.75 745.29 L 1385.75 765.94" stroke-width="1" zvalue="1060"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@1" LinkObjectIDznd="239@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1385.75 745.29 L 1385.75 765.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="kv10" d="M 1385.77 725.66 L 1385.77 703" stroke-width="1" zvalue="1061"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@0" LinkObjectIDznd="331@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1385.77 725.66 L 1385.77 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv10" d="M 1571.43 841.29 L 1571.43 889.38" stroke-width="1" zvalue="1069"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="222@1" LinkObjectIDznd="216@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1571.43 841.29 L 1571.43 889.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv10" d="M 1598.43 881.25 L 1598.43 867.33 L 1571.43 867.33" stroke-width="1" zvalue="1071"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217@0" LinkObjectIDznd="215" MaxPinNum="2"/>
   </metadata>
  <path d="M 1598.43 881.25 L 1598.43 867.33 L 1571.43 867.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv10" d="M 1571.45 791.82 L 1571.45 821.66" stroke-width="1" zvalue="1072"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="222@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1571.45 791.82 L 1571.45 821.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv10" d="M 1569.75 745.29 L 1569.75 765.94" stroke-width="1" zvalue="1075"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@1" LinkObjectIDznd="221@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1569.75 745.29 L 1569.75 765.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv10" d="M 1569.77 725.66 L 1569.77 703" stroke-width="1" zvalue="1076"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@0" LinkObjectIDznd="331@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1569.77 725.66 L 1569.77 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="267">
   <path class="kv35" d="M 1519.38 403.22 L 1519.38 381.55" stroke-width="1" zvalue="1143"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="270@1" LinkObjectIDznd="269@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1519.38 403.22 L 1519.38 381.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="265">
   <path class="kv10" d="M 1546.88 538.58 L 1517.14 538.58" stroke-width="1" zvalue="1145"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="266@0" LinkObjectIDznd="262" MaxPinNum="2"/>
   </metadata>
  <path d="M 1546.88 538.58 L 1517.14 538.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="263">
   <path class="kv35" d="M 1518.92 459.93 L 1518.92 429.11" stroke-width="1" zvalue="1148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="264@0" LinkObjectIDznd="270@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1518.92 459.93 L 1518.92 429.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="262">
   <path class="kv10" d="M 1517.14 563.77 L 1517.14 523.09" stroke-width="1" zvalue="1149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@1" LinkObjectIDznd="264@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517.14 563.77 L 1517.14 523.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="260">
   <path class="kv10" d="M 1517.45 610.04 L 1517.45 589.66" stroke-width="1" zvalue="1151"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@1" LinkObjectIDznd="272@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517.45 610.04 L 1517.45 589.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="259">
   <path class="kv10" d="M 1517.47 629.67 L 1517.47 703" stroke-width="1" zvalue="1153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@0" LinkObjectIDznd="331@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517.47 629.67 L 1517.47 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="322">
   <path class="kv35" d="M 835.23 365.67 L 835.23 337.59" stroke-width="1" zvalue="1180"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="323@1" LinkObjectIDznd="128@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 835.23 365.67 L 835.23 337.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="321">
   <path class="kv35" d="M 835.26 403.22 L 835.26 385.3" stroke-width="1" zvalue="1181"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="324@1" LinkObjectIDznd="323@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 835.26 403.22 L 835.26 385.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="319">
   <path class="kv10" d="M 864.14 541.77 L 834.39 541.77" stroke-width="1" zvalue="1183"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="320@0" LinkObjectIDznd="242" MaxPinNum="2"/>
   </metadata>
  <path d="M 864.14 541.77 L 834.39 541.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="313">
   <path class="kv10" d="M 834.18 609.79 L 834.18 589.41" stroke-width="1" zvalue="1190"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="314@1" LinkObjectIDznd="325@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 834.18 609.79 L 834.18 589.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="312">
   <path class="kv10" d="M 834.2 629.42 L 834.2 703" stroke-width="1" zvalue="1191"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="314@0" LinkObjectIDznd="330@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 834.2 629.42 L 834.2 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="332">
   <path class="kv35" d="M 1519.54 361.92 L 1519.54 337.59" stroke-width="1" zvalue="1202"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="269@1" LinkObjectIDznd="128@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1519.54 361.92 L 1519.54 337.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv10" d="M 580.91 651.76 L 580.91 611.6" stroke-width="1" zvalue="1206"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@1" LinkObjectIDznd="100@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 580.91 651.76 L 580.91 611.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv10" d="M 580.88 671.39 L 580.88 703" stroke-width="1" zvalue="1207"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="330@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 580.88 671.39 L 580.88 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv10" d="M 615.27 618.36 L 615.27 637 L 580.91 637" stroke-width="1" zvalue="1211"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="102" MaxPinNum="2"/>
   </metadata>
  <path d="M 615.27 618.36 L 615.27 637 L 580.91 637" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv10" d="M 1668 641.78 L 1668 649.5 L 1638.16 649.5" stroke-width="1" zvalue="1221"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="202" MaxPinNum="2"/>
   </metadata>
  <path d="M 1668 641.78 L 1668 649.5 L 1638.16 649.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv10" d="M 508.55 784.66 L 508.56 821" stroke-width="1" zvalue="1230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@0" LinkObjectIDznd="185@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 508.55 784.66 L 508.56 821" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv10" d="M 506.84 743.79 L 506.84 758.77" stroke-width="1" zvalue="1233"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@1" LinkObjectIDznd="176@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 506.84 743.79 L 506.84 758.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="kv10" d="M 506.87 724.16 L 506.87 703" stroke-width="1" zvalue="1234"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="330@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 506.87 724.16 L 506.87 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv10" d="M 532.11 874.87 L 532.11 863 L 508.58 863" stroke-width="1" zvalue="1237"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@0" LinkObjectIDznd="133" MaxPinNum="2"/>
   </metadata>
  <path d="M 532.11 874.87 L 532.11 863 L 508.58 863" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv10" d="M 508.58 880.81 L 508.58 840.63" stroke-width="1" zvalue="1238"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="185@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 508.58 880.81 L 508.58 840.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="kv10" d="M 508.58 929.4 L 508.58 870.81" stroke-width="1" zvalue="1239"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@0" LinkObjectIDznd="133" MaxPinNum="2"/>
   </metadata>
  <path d="M 508.58 929.4 L 508.58 870.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv10" d="M 493.72 808.79 L 508.56 808.79" stroke-width="1" zvalue="1241"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="171" MaxPinNum="2"/>
   </metadata>
  <path d="M 493.72 808.79 L 508.56 808.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="kv10" d="M 1638.16 631.1 L 1638.16 662.26" stroke-width="1" zvalue="1248"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="191@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1638.16 631.1 L 1638.16 662.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="207">
   <path class="kv10" d="M 1638.13 681.89 L 1638.13 703" stroke-width="1" zvalue="1249"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="331@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1638.13 681.89 L 1638.13 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv10" d="M 1740.43 841.29 L 1740.43 889.38" stroke-width="1" zvalue="1257"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@1" LinkObjectIDznd="72@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1740.43 841.29 L 1740.43 889.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv10" d="M 1767.43 881.25 L 1767.43 867.33 L 1740.43 867.33" stroke-width="1" zvalue="1259"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="71" MaxPinNum="2"/>
   </metadata>
  <path d="M 1767.43 881.25 L 1767.43 867.33 L 1740.43 867.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv10" d="M 1740.45 791.82 L 1740.45 821.66" stroke-width="1" zvalue="1260"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="92@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1740.45 791.82 L 1740.45 821.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv10" d="M 1738.75 745.29 L 1738.75 765.94" stroke-width="1" zvalue="1263"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@1" LinkObjectIDznd="91@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1738.75 745.29 L 1738.75 765.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv10" d="M 1738.77 725.66 L 1738.77 703" stroke-width="1" zvalue="1264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="331@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1738.77 725.66 L 1738.77 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="kv10" d="M 834.39 563.52 L 834.39 523.09" stroke-width="1" zvalue="1300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="325@1" LinkObjectIDznd="318@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 834.39 563.52 L 834.39 523.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="243">
   <path class="kv35" d="M 834.42 459.93 L 834.42 429.11" stroke-width="1" zvalue="1301"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="318@0" LinkObjectIDznd="324@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 834.42 459.93 L 834.42 429.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="kv10" d="M 508.58 929.4 L 508.58 899.21 L 508.58 899.21 L 508.58 869.02" stroke-width="1" zvalue="1410"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132" LinkObjectIDznd="133" MaxPinNum="2"/>
   </metadata>
  <path d="M 508.58 929.4 L 508.58 899.21 L 508.58 899.21 L 508.58 869.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="MeasurementClass">
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="2" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,662.429,961) scale(1,1) translate(0,0)" writing-mode="lr" x="661.96" xml:space="preserve" y="965.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133213679620" ObjectName="P"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="7" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,938.429,962) scale(1,1) translate(0,0)" writing-mode="lr" x="937.96" xml:space="preserve" y="966.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133215121412" ObjectName="P"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="22" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1571.43,963) scale(1,1) translate(1.01681e-12,0)" writing-mode="lr" x="1570.96" xml:space="preserve" y="967.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133216563204" ObjectName="P"/>
   </metadata>
  </g>
  <g id="25">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="25" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1387.43,963) scale(1,1) translate(0,0)" writing-mode="lr" x="1386.96" xml:space="preserve" y="967.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133218004996" ObjectName="P"/>
   </metadata>
  </g>
  <g id="26">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="26" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,661.429,985) scale(1,1) translate(0,0)" writing-mode="lr" x="660.96" xml:space="preserve" y="989.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133213745156" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="39" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,938.429,985) scale(1,1) translate(0,0)" writing-mode="lr" x="937.96" xml:space="preserve" y="989.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133215186948" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="41">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="41" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1571.43,986) scale(1,1) translate(1.01681e-12,0)" writing-mode="lr" x="1570.96" xml:space="preserve" y="990.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133216628740" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="43">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="43" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1387.43,986) scale(1,1) translate(0,0)" writing-mode="lr" x="1386.96" xml:space="preserve" y="990.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133218070532" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="44">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="44" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,661.429,1008) scale(1,1) translate(0,0)" writing-mode="lr" x="660.96" xml:space="preserve" y="1012.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133213810692" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="57" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,938.429,1008) scale(1,1) translate(0,0)" writing-mode="lr" x="937.96" xml:space="preserve" y="1012.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133215252483" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="76">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="76" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1571.43,1009) scale(1,1) translate(1.01681e-12,0)" writing-mode="lr" x="1570.96" xml:space="preserve" y="1013.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133216694276" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="77">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="77" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1387.43,1009) scale(1,1) translate(0,0)" writing-mode="lr" x="1386.96" xml:space="preserve" y="1013.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133218136067" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="78" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,989.628,12.6878) scale(1,1) translate(0,5.82938e-15)" writing-mode="lr" x="989.16" xml:space="preserve" y="17.35" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133209681923" ObjectName="P"/>
   </metadata>
  </g>
  <g id="79">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="79" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,989.628,35.6878) scale(1,1) translate(0,0)" writing-mode="lr" x="989.16" xml:space="preserve" y="40.35" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133209747459" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="80" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,989.628,58.6878) scale(1,1) translate(0,0)" writing-mode="lr" x="989.16" xml:space="preserve" y="63.35" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133209812995" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="28">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="28" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1740.43,963) scale(1,1) translate(0,0)" writing-mode="lr" x="1739.96" xml:space="preserve" y="967.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133227900932" ObjectName="P"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="38" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1740.43,986) scale(1,1) translate(0,0)" writing-mode="lr" x="1739.96" xml:space="preserve" y="990.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133227966468" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="93" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1740.43,1009) scale(1,1) translate(0,0)" writing-mode="lr" x="1739.96" xml:space="preserve" y="1013.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133228032004" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="95" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,508.583,983.673) scale(1,1) translate(0,0)" writing-mode="lr" x="508.11" xml:space="preserve" y="988.34" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133226655747" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="96" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,508.583,1006.67) scale(1,1) translate(0,0)" writing-mode="lr" x="508.11" xml:space="preserve" y="1011.34" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133226721283" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="182">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="182" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,727.904,384.631) scale(1,1) translate(1.49304e-13,0)" writing-mode="lr" x="727.33" xml:space="preserve" y="390.33" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133222526979" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="192">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="192" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,727.904,411.631) scale(1,1) translate(1.49304e-13,0)" writing-mode="lr" x="727.33" xml:space="preserve" y="417.33" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133222592515" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="193">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="193" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,727.904,438.631) scale(1,1) translate(1.49304e-13,0)" writing-mode="lr" x="727.33" xml:space="preserve" y="444.33" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133222789124" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="195">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="195" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,734.904,553.131) scale(1,1) translate(1.50858e-13,-1.20044e-13)" writing-mode="lr" x="734.33" xml:space="preserve" y="558.83" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133222658052" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="204">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="204" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,734.904,580.131) scale(1,1) translate(1.50858e-13,0)" writing-mode="lr" x="734.33" xml:space="preserve" y="585.83" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133222723588" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="206">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="206" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,734.904,607.131) scale(1,1) translate(1.50858e-13,0)" writing-mode="lr" x="734.33" xml:space="preserve" y="612.83" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133223116804" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="212">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="212" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1405.39,390.818) scale(1,1) translate(0,0)" writing-mode="lr" x="1404.81" xml:space="preserve" y="396.52" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133219446788" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="219">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="219" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1405.39,417.818) scale(1,1) translate(0,0)" writing-mode="lr" x="1404.81" xml:space="preserve" y="423.52" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133219512324" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="220">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="220" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1405.39,444.818) scale(1,1) translate(0,0)" writing-mode="lr" x="1404.81" xml:space="preserve" y="450.52" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133219708932" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="225">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="225" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1412.89,553.179) scale(1,1) translate(0,-1.20055e-13)" writing-mode="lr" x="1412.31" xml:space="preserve" y="558.88" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133219577860" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="226">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="226" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1412.89,580.179) scale(1,1) translate(0,0)" writing-mode="lr" x="1412.31" xml:space="preserve" y="585.88" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133219643396" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="227">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="227" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1412.89,604.179) scale(1,1) translate(-6.02803e-13,0)" writing-mode="lr" x="1412.31" xml:space="preserve" y="609.88" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133220036612" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="282">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="282" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,106.353,519.893) scale(1,1) translate(8.75402e-15,0)" writing-mode="lr" x="106.07" xml:space="preserve" y="524.59" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133208633347" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="281">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="281" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,106.353,542.893) scale(1,1) translate(8.75402e-15,0)" writing-mode="lr" x="106.07" xml:space="preserve" y="547.59" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133208698883" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="280">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="280" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,106.353,565.893) scale(1,1) translate(8.75402e-15,6.14389e-14)" writing-mode="lr" x="106.07" xml:space="preserve" y="570.59" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133208764419" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="279">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="279" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,108.496,494) scale(1,1) translate(0,0)" writing-mode="lr" x="108.21" xml:space="preserve" y="498.7" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133208895491" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="278">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="278" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,146.714,201.754) scale(1,1) translate(0,2.11145e-14)" writing-mode="lr" x="146.35" xml:space="preserve" y="207.44" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133209026563" ObjectName="F"/>
   </metadata>
  </g>
  <g id="277">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="277" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,225.756,519.893) scale(1,1) translate(2.20104e-14,0)" writing-mode="lr" x="225.47" xml:space="preserve" y="524.59" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133225607172" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="276">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="276" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,225.756,542.893) scale(1,1) translate(2.20104e-14,5.88854e-14)" writing-mode="lr" x="225.47" xml:space="preserve" y="547.59" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133225672708" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="275">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="275" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,225.756,565.893) scale(1,1) translate(2.20104e-14,6.14389e-14)" writing-mode="lr" x="225.47" xml:space="preserve" y="570.59" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133225738244" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="274">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="274" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,225.756,494.393) scale(1,1) translate(2.20104e-14,-1.07002e-13)" writing-mode="lr" x="225.47" xml:space="preserve" y="499.09" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133225869315" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="273">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="273" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,146.714,227.571) scale(1,1) translate(0,-4.79616e-14)" writing-mode="lr" x="146.35" xml:space="preserve" y="233.26" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133226000387" ObjectName="F"/>
   </metadata>
  </g>
  <g id="271">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="271" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,283.714,519.893) scale(1,1) translate(2.84451e-14,0)" writing-mode="lr" x="283.43" xml:space="preserve" y="524.59" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133226131459" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="268">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="268" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,283.714,542.893) scale(1,1) translate(2.84451e-14,5.88854e-14)" writing-mode="lr" x="283.43" xml:space="preserve" y="547.59" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133226196995" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="253">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="253" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,283.714,565.893) scale(1,1) translate(2.84451e-14,6.14389e-14)" writing-mode="lr" x="283.43" xml:space="preserve" y="570.59" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133226262531" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="252">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="252" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,323.714,227.696) scale(1,1) translate(0,-4.79894e-14)" writing-mode="lr" x="323.35" xml:space="preserve" y="233.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133226524675" ObjectName="F"/>
   </metadata>
  </g>
  <g id="251">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="251" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,283.714,494.393) scale(1,1) translate(2.84451e-14,-1.07002e-13)" writing-mode="lr" x="283.43" xml:space="preserve" y="499.09" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133226393603" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="250">
   <text Format="f5.1" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="15" id="250" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,146.714,251.669) scale(1,1) translate(0,-5.33123e-14)" writing-mode="lr" x="146.35" xml:space="preserve" y="257.35" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133222985732" ObjectName="YW1"/>
   </metadata>
  </g>
  <g id="249">
   <text Format="f3.0" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="249" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,146.714,274.169) scale(1,1) translate(0,2.91541e-14)" writing-mode="lr" x="146.35" xml:space="preserve" y="279.85" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133223051268" ObjectName="Tap"/>
   </metadata>
  </g>
  <g id="248">
   <text Format="f5.1" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="15" id="248" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,323.714,252.169) scale(1,1) translate(0,2.67117e-14)" writing-mode="lr" x="323.35" xml:space="preserve" y="257.85" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133219905540" ObjectName="YW1"/>
   </metadata>
  </g>
  <g id="247">
   <text Format="f3.0" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="247" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,323.714,274.169) scale(1,1) translate(0,-5.83083e-14)" writing-mode="lr" x="323.35" xml:space="preserve" y="279.85" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133219971076" ObjectName="Tap"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="246" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,106.353,592.893) scale(1,1) translate(8.75402e-15,0)" writing-mode="lr" x="106.07" xml:space="preserve" y="597.59" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133209092099" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="245" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,225.756,592.893) scale(1,1) translate(2.20104e-14,0)" writing-mode="lr" x="225.47" xml:space="preserve" y="597.59" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133226065923" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="244" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,283.714,592.893) scale(1,1) translate(2.84451e-14,0)" writing-mode="lr" x="283.43" xml:space="preserve" y="597.59" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133226590211" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="241">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="15" id="241" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,146.714,174.071) scale(1,1) translate(0,1.80411e-14)" writing-mode="lr" x="146.35" xml:space="preserve" y="179.76" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133375094787" ObjectName=""/>
   </metadata>
  </g>
  <g id="229">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="229" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,323.714,177.143) scale(1,1) translate(0,1.83821e-14)" writing-mode="lr" x="323.35" xml:space="preserve" y="182.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133375160323" ObjectName=""/>
   </metadata>
  </g>
  <g id="180">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="180" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,798.429,961) scale(1,1) translate(0,0)" writing-mode="lr" x="797.96" xml:space="preserve" y="965.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133212237828" ObjectName="P"/>
   </metadata>
  </g>
  <g id="181">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="181" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,797.429,985) scale(1,1) translate(0,0)" writing-mode="lr" x="796.96" xml:space="preserve" y="989.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133212303364" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="183">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="183" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,797.429,1008) scale(1,1) translate(0,0)" writing-mode="lr" x="796.96" xml:space="preserve" y="1012.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133212368900" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="83">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1308.5,122) scale(1,1) translate(0,0)" writing-mode="lr" x="1308.22" xml:space="preserve" y="126.7" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133208895491" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="134">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="134" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,581.504,517) scale(1,1) translate(6.15064e-14,0)" writing-mode="lr" x="581.22" xml:space="preserve" y="521.7" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133225869315" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="140" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1633.5,533) scale(1,1) translate(1.78302e-13,5.77871e-14)" writing-mode="lr" x="1633.22" xml:space="preserve" y="537.7" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133226393603" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="147">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="147" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1139.5,537.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1138.92" xml:space="preserve" y="542.2" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133211320323" ObjectName="P"/>
   </metadata>
  </g>
  <g id="184">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="184" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1139.5,555.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1138.92" xml:space="preserve" y="560.2" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133211385859" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="186">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="186" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1139.5,573.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1138.92" xml:space="preserve" y="578.2" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133211451395" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="264">
   <g id="2640">
    <use class="kv35" height="30" transform="rotate(0,1518.89,491.373) scale(2.17813,2.25821) translate(-807.416,-254.906)" width="24" x="1492.75" xlink:href="#PowerTransformer2:可调不带中性点_0" y="457.5" zvalue="1146"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874566860802" ObjectName="35"/>
    </metadata>
   </g>
   <g id="2641">
    <use class="kv10" height="30" transform="rotate(0,1518.89,491.373) scale(2.17813,2.25821) translate(-807.416,-254.906)" width="24" x="1492.75" xlink:href="#PowerTransformer2:可调不带中性点_1" y="457.5" zvalue="1146"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874566926338" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399524614146" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399524614146"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1518.89,491.373) scale(2.17813,2.25821) translate(-807.416,-254.906)" width="24" x="1492.75" y="457.5"/></g>
  <g id="318">
   <g id="3180">
    <use class="kv35" height="30" transform="rotate(0,834.392,491.373) scale(2.17813,2.25821) translate(-437.177,-254.906)" width="24" x="808.25" xlink:href="#PowerTransformer2:可调不带中性点_0" y="457.5" zvalue="1184"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874566991874" ObjectName="35"/>
    </metadata>
   </g>
   <g id="3181">
    <use class="kv10" height="30" transform="rotate(0,834.392,491.373) scale(2.17813,2.25821) translate(-437.177,-254.906)" width="24" x="808.25" xlink:href="#PowerTransformer2:可调不带中性点_1" y="457.5" zvalue="1184"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874567057410" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399524679682" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399524679682"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,834.392,491.373) scale(2.17813,2.25821) translate(-437.177,-254.906)" width="24" x="808.25" y="457.5"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="158">
   <use class="kv10" height="40" transform="rotate(0,508.583,899.173) scale(1.18056,1.2) translate(-75.6167,-145.862)" width="24" x="494.4166666666669" xlink:href="#Compensator:10kV电容器_0" y="875.1728099342653" zvalue="1235"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453840732163" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192453840732163"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,508.583,899.173) scale(1.18056,1.2) translate(-75.6167,-145.862)" width="24" x="494.4166666666669" y="875.1728099342653"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,353.339,319.143) scale(0.708333,0.665547) translate(141.118,155.36)" width="30" x="342.71" xlink:href="#State:红绿圆(方形)_0" y="309.16" zvalue="1361"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374922416129" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,353.339,319.143) scale(0.708333,0.665547) translate(141.118,155.36)" width="30" x="342.71" y="309.16"/></g>
  <g id="1035">
   <use height="30" transform="rotate(0,257.714,319.143) scale(0.708333,0.665547) translate(101.743,155.36)" width="30" x="247.09" xlink:href="#State:红绿圆(方形)_0" y="309.16" zvalue="1362"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562958371520516" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,257.714,319.143) scale(0.708333,0.665547) translate(101.743,155.36)" width="30" x="247.09" y="309.16"/></g>
  <g id="830">
   <use height="30" transform="rotate(0,319.812,133.964) scale(1.22222,1.03092) translate(-48.1477,-3.55433)" width="90" x="264.81" xlink:href="#State:全站检修_0" y="118.5" zvalue="1427"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549679423489" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,319.812,133.964) scale(1.22222,1.03092) translate(-48.1477,-3.55433)" width="90" x="264.81" y="118.5"/></g>
 </g>
</svg>