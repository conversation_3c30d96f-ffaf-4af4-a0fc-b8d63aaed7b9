<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549587148802" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.45181617405996" x2="1.714850492606708" y1="5.276370517142858" y2="14.05696281619048"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.583333333333333" x2="8.583333333333334" y1="5.166666666666667" y2="14.16666666666667"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="4.416666666666668" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.249999999999998" y2="20.16666666666667"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="12.0194189818494"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="5.416666666666668" y2="12.02321291373541"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.92126160277786" x2="6.666666666666666" y1="12.10207526123773" y2="18.50000000000001"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.18646053143751" x2="24.5" y1="12.18904818695178" y2="19.00000000000001"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 7.33333 43.5833 L 23 43.5833 L 15 31.5 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="Accessory:PT7_0" viewBox="0,0,14,18">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1.416666666666664"/>
   <ellipse cx="7.15" cy="6.35" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.15" cy="12.03" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV南马河三级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="35" xlink:href="logo.png" y="42"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,184,72) scale(1,1) translate(0,0)" writing-mode="lr" x="184" xml:space="preserve" y="75.5" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,180.5,71.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="180.5" xml:space="preserve" y="80.69" zvalue="3">35kV南马河三级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="1" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,85.4375,315) scale(1,1) translate(0,0)" width="72.88" x="49" y="303" zvalue="171"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.4375,315) scale(1,1) translate(0,0)" writing-mode="lr" x="85.44" xml:space="preserve" y="319.5" zvalue="171">信号一览</text>
  <line fill="none" id="33" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.0000000000001" x2="377.0000000000001" y1="10" y2="1040" zvalue="4"/>
  <line fill="none" id="31" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.000000000000796" x2="370.0000000000003" y1="145.8704926140824" y2="145.8704926140824" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="158.0000000000001" y2="158.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="184.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="4.000000000000114" y1="158.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="158.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="158.0000000000001" y2="158.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="184.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="158.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.0000000000001" x2="366.0000000000001" y1="158.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="184.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="208.2500000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="4.000000000000114" y1="184.0000000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="184.0000000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="184.0000000000001" y2="184.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="208.2500000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="184.0000000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.0000000000001" x2="366.0000000000001" y1="184.0000000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="208.2500000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="231.0000000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="4.000000000000114" y1="208.2500000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="208.2500000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="208.2500000000001" y2="208.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="231.0000000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="208.2500000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.0000000000001" x2="366.0000000000001" y1="208.2500000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="231.0000000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="253.7500000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="4.000000000000114" y1="231.0000000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="231.0000000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="231.0000000000001" y2="231.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="253.7500000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="231.0000000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.0000000000001" x2="366.0000000000001" y1="231.0000000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="253.7500000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="185.0000000000001" y1="276.5000000000001" y2="276.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.000000000000114" x2="4.000000000000114" y1="253.7500000000001" y2="276.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="253.7500000000001" y2="276.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="253.7500000000001" y2="253.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="366.0000000000001" y1="276.5000000000001" y2="276.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="253.7500000000001" y2="276.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.0000000000001" x2="366.0000000000001" y1="253.7500000000001" y2="276.5000000000001"/>
  <line fill="none" id="29" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.000000000000796" x2="370.0000000000003" y1="615.8704926140824" y2="615.8704926140824" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="93.00000000000011" y1="931" y2="931"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="93.00000000000011" y1="970.1632999999999" y2="970.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="3.000000000000114" y1="931" y2="970.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="93.00000000000011" y1="931" y2="970.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="363.0000000000001" y1="931" y2="931"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="363.0000000000001" y1="970.1632999999999" y2="970.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="93.00000000000011" y1="931" y2="970.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.0000000000001" x2="363.0000000000001" y1="931" y2="970.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="93.00000000000011" y1="970.16327" y2="970.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="93.00000000000011" y1="998.08167" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="3.000000000000114" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="93.00000000000011" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="183.0000000000001" y1="970.16327" y2="970.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="183.0000000000001" y1="998.08167" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="93.00000000000011" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000002" x2="273.0000000000002" y1="970.16327" y2="970.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000002" x2="273.0000000000002" y1="998.08167" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000002" x2="183.0000000000002" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000002" x2="273.0000000000002" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000001" x2="363.0000000000001" y1="970.16327" y2="970.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000001" x2="363.0000000000001" y1="998.08167" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000001" x2="273.0000000000001" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.0000000000001" x2="363.0000000000001" y1="970.16327" y2="998.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="93.00000000000011" y1="998.0816" y2="998.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="93.00000000000011" y1="1026" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.000000000000114" x2="3.000000000000114" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="93.00000000000011" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="183.0000000000001" y1="998.0816" y2="998.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="183.0000000000001" y1="1026" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.00000000000011" x2="93.00000000000011" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000002" x2="273.0000000000002" y1="998.0816" y2="998.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000002" x2="273.0000000000002" y1="1026" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000002" x2="183.0000000000002" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000002" x2="273.0000000000002" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000001" x2="363.0000000000001" y1="998.0816" y2="998.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000001" x2="363.0000000000001" y1="1026" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000001" x2="273.0000000000001" y1="998.0816" y2="1026"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.0000000000001" x2="363.0000000000001" y1="998.0816" y2="1026"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48,951) scale(1,1) translate(0,0)" writing-mode="lr" x="48" xml:space="preserve" y="957" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45,985) scale(1,1) translate(0,0)" writing-mode="lr" x="45" xml:space="preserve" y="991" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227,985) scale(1,1) translate(0,0)" writing-mode="lr" x="227" xml:space="preserve" y="991" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44,1013) scale(1,1) translate(0,0)" writing-mode="lr" x="44" xml:space="preserve" y="1019" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226,1013) scale(1,1) translate(0,0)" writing-mode="lr" x="226" xml:space="preserve" y="1019" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,68.5,645.5) scale(1,1) translate(0,-2.78222e-13)" writing-mode="lr" x="68.50000000000011" xml:space="preserve" y="649.9999999999999" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.399,312.841) scale(1,1) translate(0,0)" writing-mode="lr" x="201.4" xml:space="preserve" y="317.34" zvalue="20">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,306.399,312.841) scale(1,1) translate(0,0)" writing-mode="lr" x="306.4" xml:space="preserve" y="317.34" zvalue="21">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.277,953) scale(1,1) translate(1.53826e-14,0)" writing-mode="lr" x="235.28" xml:space="preserve" y="959" zvalue="28">NanMaHesanji-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,138.054,985) scale(1,1) translate(0,0)" writing-mode="lr" x="138.05" xml:space="preserve" y="991" zvalue="29">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,42,172) scale(1,1) translate(0,0)" writing-mode="lr" x="42" xml:space="preserve" y="177.5" zvalue="31">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222,172) scale(1,1) translate(0,0)" writing-mode="lr" x="222" xml:space="preserve" y="177.5" zvalue="32">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.6875,196.25) scale(1,1) translate(0,0)" writing-mode="lr" x="49.69" xml:space="preserve" y="200.75" zvalue="33">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229.125,196.25) scale(1,1) translate(0,0)" writing-mode="lr" x="229.13" xml:space="preserve" y="200.75" zvalue="34">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.1875,244) scale(1,1) translate(0,0)" writing-mode="lr" x="49.19" xml:space="preserve" y="248.5" zvalue="35">35kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,494.857,661.286) scale(1,1) translate(0,0)" writing-mode="lr" x="494.86" xml:space="preserve" y="665.79" zvalue="38">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,492.714,317) scale(1,1) translate(0,0)" writing-mode="lr" x="492.71" xml:space="preserve" y="321.5" zvalue="39">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,738.183,792.222) scale(1,1) translate(0,0)" writing-mode="lr" x="738.1799999999999" xml:space="preserve" y="796.72" zvalue="42">621</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" x="710.7109375" xml:space="preserve" y="953.8055990134186" zvalue="46">#1发电机      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="710.7109375" xml:space="preserve" y="969.8055990134186" zvalue="46">1000KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,693.604,727.221) scale(1,1) translate(0,0)" writing-mode="lr" x="693.6" xml:space="preserve" y="731.72" zvalue="49">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1188.53,793.849) scale(1,1) translate(0,0)" writing-mode="lr" x="1188.53" xml:space="preserve" y="798.35" zvalue="53">622</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" x="1161.0546875" xml:space="preserve" y="955.4305990134186" zvalue="56">#2发电机          </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1161.0546875" xml:space="preserve" y="971.4305990134186" zvalue="56">1000KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1143.96,728.848) scale(1,1) translate(0,0)" writing-mode="lr" x="1143.96" xml:space="preserve" y="733.35" zvalue="59">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1465.87,868.028) scale(1,1) translate(0,0)" writing-mode="lr" x="1465.87" xml:space="preserve" y="872.53" zvalue="63">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1492.46,745.616) scale(1,1) translate(0,0)" writing-mode="lr" x="1492.46" xml:space="preserve" y="750.12" zvalue="66">681</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" x="762.2734375" xml:space="preserve" y="514.5568181818181" zvalue="70">#1主变          </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="762.2734375" xml:space="preserve" y="530.5568181818181" zvalue="70">2500KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,727.751,438.179) scale(1,1) translate(0,0)" writing-mode="lr" x="727.75" xml:space="preserve" y="442.68" zvalue="76">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,683.273,363.03) scale(1,1) translate(0,0)" writing-mode="lr" x="683.27" xml:space="preserve" y="367.53" zvalue="80">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,629.025,436.419) scale(1,1) translate(0,0)" writing-mode="lr" x="629.03" xml:space="preserve" y="440.92" zvalue="84">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,666.545,597.507) scale(1,1) translate(0,0)" writing-mode="lr" x="666.55" xml:space="preserve" y="602.01" zvalue="88">6011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1299.75,600.893) scale(1,1) translate(0,0)" writing-mode="lr" x="1299.75" xml:space="preserve" y="605.39" zvalue="93">6901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1326.54,515.774) scale(1,1) translate(0,0)" writing-mode="lr" x="1326.54" xml:space="preserve" y="520.27" zvalue="96">6.3kV母线PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,719.636,276.961) scale(1,1) translate(0,4.77772e-13)" writing-mode="lr" x="719.64" xml:space="preserve" y="281.46" zvalue="104">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,760.242,212.429) scale(1,1) translate(0,0)" writing-mode="lr" x="760.24" xml:space="preserve" y="216.93" zvalue="107">322</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,679.934,265.571) scale(1,1) translate(0,0)" writing-mode="lr" x="679.9299999999999" xml:space="preserve" y="270.07" zvalue="110">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,717.364,126.961) scale(1,1) translate(0,0)" writing-mode="lr" x="717.36" xml:space="preserve" y="131.46" zvalue="114">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,682.48,208.116) scale(1,1) translate(0,0)" writing-mode="lr" x="682.48" xml:space="preserve" y="212.62" zvalue="116">60</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,738.182,27.2727) scale(1,1) translate(-3.09389e-13,0)" writing-mode="lr" x="738.1799999999999" xml:space="preserve" y="31.77" zvalue="119">35kV南三二线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,682.116,99.0253) scale(1,1) translate(0,0)" writing-mode="lr" x="682.12" xml:space="preserve" y="103.53" zvalue="122">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,786.182,99.3636) scale(1,1) translate(0,0)" writing-mode="lr" x="786.1799999999999" xml:space="preserve" y="103.86" zvalue="124">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1210.91,278.28) scale(1,1) translate(0,0)" writing-mode="lr" x="1210.91" xml:space="preserve" y="282.78" zvalue="129">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1251.51,213.747) scale(1,1) translate(0,0)" writing-mode="lr" x="1251.51" xml:space="preserve" y="218.25" zvalue="132">321</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1171.21,271.357) scale(1,1) translate(0,0)" writing-mode="lr" x="1171.21" xml:space="preserve" y="275.86" zvalue="136">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1208.64,128.28) scale(1,1) translate(0,0)" writing-mode="lr" x="1208.64" xml:space="preserve" y="132.78" zvalue="139">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1173.75,209.434) scale(1,1) translate(0,0)" writing-mode="lr" x="1173.75" xml:space="preserve" y="213.93" zvalue="142">60</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1229.45,28.5909) scale(1,1) translate(0,0)" writing-mode="lr" x="1229.45" xml:space="preserve" y="33.09" zvalue="145">35kV南弄线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1173.39,100.491) scale(1,1) translate(0,0)" writing-mode="lr" x="1173.39" xml:space="preserve" y="104.99" zvalue="148">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1277.45,100.682) scale(1,1) translate(0,0)" writing-mode="lr" x="1277.45" xml:space="preserve" y="105.18" zvalue="151">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1530.5,378.548) scale(1,1) translate(0,0)" writing-mode="lr" x="1530.5" xml:space="preserve" y="383.05" zvalue="156">39010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1453.84,402.484) scale(1,1) translate(0,0)" writing-mode="lr" x="1453.84" xml:space="preserve" y="406.98" zvalue="158">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1532.14,452.843) scale(1,1) translate(0,0)" writing-mode="lr" x="1532.14" xml:space="preserve" y="457.34" zvalue="164">39017</text>
 </g>
 <g id="ButtonClass">
  <g href="35kV南马河三级电站.svg"><rect fill-opacity="0" height="24" width="72.88" x="49" y="303" zvalue="171"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="36">
   <path class="v6300" d="M 531.43 665.14 L 1667.14 665.14" stroke-width="6" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674244231172" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674244231172"/></metadata>
  <path d="M 531.43 665.14 L 1667.14 665.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv35" d="M 527.14 322.29 L 1687.14 322.29" stroke-width="6" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674244296708" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674244296708"/></metadata>
  <path d="M 527.14 322.29 L 1687.14 322.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="341">
   <use class="v6300" height="20" transform="rotate(0,711.169,793.303) scale(2.16108,2.16108) translate(-376.284,-414.607)" width="10" x="700.3636701166021" xlink:href="#Breaker:手车开关_0" y="771.6921172214578" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924513366021" ObjectName="#1发电机621断路器"/>
   <cge:TPSR_Ref TObjectID="6473924513366021"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,711.169,793.303) scale(2.16108,2.16108) translate(-376.284,-414.607)" width="10" x="700.3636701166021" y="771.6921172214578"/></g>
  <g id="54">
   <use class="v6300" height="20" transform="rotate(0,1161.52,794.929) scale(2.16108,2.16108) translate(-618.244,-415.48)" width="10" x="1150.71531846825" xlink:href="#Breaker:手车开关_0" y="773.3184908478314" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924513431557" ObjectName="#2发电机622断路器"/>
   <cge:TPSR_Ref TObjectID="6473924513431557"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1161.52,794.929) scale(2.16108,2.16108) translate(-618.244,-415.48)" width="10" x="1150.71531846825" y="773.3184908478314"/></g>
  <g id="59">
   <use class="v6300" height="20" transform="rotate(0,1460.11,745.707) scale(2.16108,2.16108) translate(-778.668,-389.035)" width="10" x="1449.306818181818" xlink:href="#Breaker:手车开关_0" y="724.0961538461538" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924513497093" ObjectName="#1站用变681断路器"/>
   <cge:TPSR_Ref TObjectID="6473924513497093"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1460.11,745.707) scale(2.16108,2.16108) translate(-778.668,-389.035)" width="10" x="1449.306818181818" y="724.0961538461538"/></g>
  <g id="91">
   <use class="kv35" height="20" transform="rotate(0,703.276,437.361) scale(1.22222,1.11111) translate(-126.757,-42.625)" width="10" x="697.1648634493081" xlink:href="#Breaker:开关_0" y="426.2499999999999" zvalue="75"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924513562629" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924513562629"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,703.276,437.361) scale(1.22222,1.11111) translate(-126.757,-42.625)" width="10" x="697.1648634493081" y="426.2499999999999"/></g>
  <g id="86">
   <use class="kv35" height="20" transform="rotate(0,738.346,213.429) scale(2.16108,2.16108) translate(-390.885,-103.058)" width="10" x="727.5401352693318" xlink:href="#Breaker:手车开关_0" y="191.8181818181816" zvalue="106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924513628165" ObjectName="35kV南三二线322断路器"/>
   <cge:TPSR_Ref TObjectID="6473924513628165"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,738.346,213.429) scale(2.16108,2.16108) translate(-390.885,-103.058)" width="10" x="727.5401352693318" y="191.8181818181816"/></g>
  <g id="138">
   <use class="kv35" height="20" transform="rotate(0,1229.62,214.747) scale(2.16108,2.16108) translate(-654.831,-103.766)" width="10" x="1218.812862542059" xlink:href="#Breaker:手车开关_0" y="193.1363639831541" zvalue="131"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924513693701" ObjectName="35kV南弄线321断路器"/>
   <cge:TPSR_Ref TObjectID="6473924513693701"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1229.62,214.747) scale(2.16108,2.16108) translate(-654.831,-103.766)" width="10" x="1218.812862542059" y="193.1363639831541"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="42">
   <path class="v6300" d="M 709.92 883.57 L 709.92 812.75" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="341@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 709.92 883.57 L 709.92 812.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="v6300" d="M 711.98 746.41 L 711.17 773.31" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@1" LinkObjectIDznd="341@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 711.98 746.41 L 711.17 773.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="v6300" d="M 712.03 710.33 L 712.03 665.14" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 712.03 710.33 L 712.03 665.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="v6300" d="M 1160.27 885.2 L 1160.27 814.38" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="54@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1160.27 885.2 L 1160.27 814.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="v6300" d="M 1162.33 748.04 L 1161.52 774.94" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@1" LinkObjectIDznd="54@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1162.33 748.04 L 1161.52 774.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="v6300" d="M 1162.39 711.95 L 1162.39 665.14" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="36@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1162.39 711.95 L 1162.39 665.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="v6300" d="M 1460.11 725.72 L 1460.11 665.14" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="36@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1460.11 725.72 L 1460.11 665.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="v6300" d="M 1460.11 765.16 L 1461.91 811.42" stroke-width="1" zvalue="67"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@1" LinkObjectIDznd="205@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1460.11 765.16 L 1461.91 811.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv35" d="M 703.36 447.97 L 703.36 478.55" stroke-width="1" zvalue="77"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@1" LinkObjectIDznd="88@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 703.36 447.97 L 703.36 478.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv35" d="M 701.93 382.22 L 703.24 426.73" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@1" LinkObjectIDznd="91@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 701.93 382.22 L 703.24 426.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv35" d="M 641.81 403.89 L 702.57 403.89" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="66" MaxPinNum="2"/>
   </metadata>
  <path d="M 641.81 403.89 L 702.57 403.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="v6300" d="M 702.53 580.61 L 703.31 550.82" stroke-width="1" zvalue="88"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="88@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 702.53 580.61 L 703.31 550.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="v6300" d="M 702.48 616.7 L 702.48 665.14" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@1" LinkObjectIDznd="36@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 702.48 616.7 L 702.48 665.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv35" d="M 701.98 346.13 L 701.98 322.29" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="37@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 701.98 346.13 L 701.98 322.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="v6300" d="M 1329.36 619.79 L 1329.36 665.14" stroke-width="1" zvalue="98"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@0" LinkObjectIDznd="36@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1329.36 619.79 L 1329.36 665.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="v6300" d="M 1326.65 564.6 L 1329.31 583.7" stroke-width="1" zvalue="99"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="73@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1326.65 564.6 L 1329.31 583.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="v6300" d="M 1361.67 636.72 L 1329.36 636.72" stroke-width="1" zvalue="101"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="79" MaxPinNum="2"/>
   </metadata>
  <path d="M 1361.67 636.72 L 1329.36 636.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv35" d="M 738.29 296.15 L 738.29 322.29" stroke-width="1" zvalue="104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@1" LinkObjectIDznd="37@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 738.29 296.15 L 738.29 322.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv35" d="M 738.35 232.88 L 738.35 260.07" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@1" LinkObjectIDznd="84@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 738.35 232.88 L 738.35 260.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv35" d="M 696.36 248.5 L 738.35 248.5" stroke-width="1" zvalue="110"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="87" MaxPinNum="2"/>
   </metadata>
  <path d="M 696.36 248.5 L 738.35 248.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv35" d="M 738.35 193.44 L 738.35 146.15" stroke-width="1" zvalue="111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="93@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 738.35 193.44 L 738.35 146.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv35" d="M 695.26 175.59 L 738.35 175.59" stroke-width="1" zvalue="116"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@0" LinkObjectIDznd="92" MaxPinNum="2"/>
   </metadata>
  <path d="M 695.26 175.59 L 738.35 175.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv35" d="M 738.18 53.48 L 738.18 110.07" stroke-width="1" zvalue="119"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="93@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 738.18 53.48 L 738.18 110.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv35" d="M 695.81 81.05 L 738.18 81.05" stroke-width="1" zvalue="122"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="101" MaxPinNum="2"/>
   </metadata>
  <path d="M 695.81 81.05 L 738.18 81.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv35" d="M 781.24 81.19 L 738.18 81.19" stroke-width="1" zvalue="124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="101" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.24 81.19 L 738.18 81.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv35" d="M 836.18 81.22 L 800.74 81.22" stroke-width="1" zvalue="126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="110@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 836.18 81.22 L 800.74 81.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv35" d="M 1229.57 297.47 L 1229.57 322.29" stroke-width="1" zvalue="130"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@1" LinkObjectIDznd="37@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1229.57 297.47 L 1229.57 322.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv35" d="M 1229.62 234.2 L 1229.62 261.38" stroke-width="1" zvalue="133"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@1" LinkObjectIDznd="140@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1229.62 234.2 L 1229.62 261.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv35" d="M 1187.63 254.29 L 1229.62 254.29" stroke-width="1" zvalue="135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="136@0" LinkObjectIDznd="137" MaxPinNum="2"/>
   </metadata>
  <path d="M 1187.63 254.29 L 1229.62 254.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="kv35" d="M 1229.62 194.76 L 1229.62 147.47" stroke-width="1" zvalue="137"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@0" LinkObjectIDznd="133@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1229.62 194.76 L 1229.62 147.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="kv35" d="M 1186.54 176.91 L 1229.62 176.91" stroke-width="1" zvalue="141"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="134" MaxPinNum="2"/>
   </metadata>
  <path d="M 1186.54 176.91 L 1229.62 176.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv35" d="M 1229.45 54.8 L 1229.45 111.38" stroke-width="1" zvalue="144"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@0" LinkObjectIDznd="133@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1229.45 54.8 L 1229.45 111.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv35" d="M 1187.08 82.51 L 1229.45 82.51" stroke-width="1" zvalue="147"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@0" LinkObjectIDznd="129" MaxPinNum="2"/>
   </metadata>
  <path d="M 1187.08 82.51 L 1229.45 82.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv35" d="M 1272.51 82.51 L 1229.45 82.51" stroke-width="1" zvalue="150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="129" MaxPinNum="2"/>
   </metadata>
  <path d="M 1272.51 82.51 L 1229.45 82.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv35" d="M 1327.45 82.54 L 1292.01 82.54" stroke-width="1" zvalue="153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@0" LinkObjectIDznd="126@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1327.45 82.54 L 1292.01 82.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv35" d="M 1483.4 385.29 L 1483.4 322.29" stroke-width="1" zvalue="166"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@1" LinkObjectIDznd="37@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1483.4 385.29 L 1483.4 322.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv35" d="M 1483.46 421.38 L 1482.56 463.91" stroke-width="1" zvalue="167"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="159@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1483.46 421.38 L 1482.56 463.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv35" d="M 1516.81 360.57 L 1483.4 360.57" stroke-width="1" zvalue="168"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162@0" LinkObjectIDznd="145" MaxPinNum="2"/>
   </metadata>
  <path d="M 1516.81 360.57 L 1483.4 360.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="kv35" d="M 1518.44 437.59 L 1483.11 437.59" stroke-width="1" zvalue="169"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@0" LinkObjectIDznd="146" MaxPinNum="2"/>
   </metadata>
  <path d="M 1518.44 437.59 L 1483.11 437.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v6300" height="30" transform="rotate(0,709.919,910.993) scale(1.85899,1.85899) translate(-315.15,-408.061)" width="30" x="682.0342396117767" xlink:href="#Generator:发电机_0" y="883.1081647427746" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449808039942" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449808039942"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,709.919,910.993) scale(1.85899,1.85899) translate(-315.15,-408.061)" width="30" x="682.0342396117767" y="883.1081647427746"/></g>
  <g id="52">
   <use class="v6300" height="30" transform="rotate(0,1160.27,912.619) scale(1.85899,1.85899) translate(-523.246,-408.812)" width="30" x="1132.385887963425" xlink:href="#Generator:发电机_0" y="884.7345383691483" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449808236550" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449808236550"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1160.27,912.619) scale(1.85899,1.85899) translate(-523.246,-408.812)" width="30" x="1132.385887963425" y="884.7345383691483"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="94">
   <use class="v6300" height="30" transform="rotate(0,711.862,728.221) scale(1.9625,1.2338) translate(-341.911,-134.486)" width="15" x="697.1428571428572" xlink:href="#Disconnector:刀闸_0" y="709.7142857142858" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449808105478" ObjectName="#1发电机6211隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449808105478"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,711.862,728.221) scale(1.9625,1.2338) translate(-341.911,-134.486)" width="15" x="697.1428571428572" y="709.7142857142858"/></g>
  <g id="51">
   <use class="v6300" height="30" transform="rotate(0,1162.21,729.848) scale(1.9625,1.2338) translate(-562.784,-134.794)" width="15" x="1147.494505494506" xlink:href="#Disconnector:刀闸_0" y="711.3406593406593" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449808171014" ObjectName="#2发电机6221隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449808171014"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1162.21,729.848) scale(1.9625,1.2338) translate(-562.784,-134.794)" width="15" x="1147.494505494506" y="711.3406593406593"/></g>
  <g id="68">
   <use class="kv35" height="30" transform="rotate(0,701.81,364.03) scale(1.9625,1.2338) translate(-336.981,-65.4743)" width="15" x="687.0909090909091" xlink:href="#Disconnector:刀闸_0" y="345.5227272727273" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449808498694" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449808498694"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,701.81,364.03) scale(1.9625,1.2338) translate(-336.981,-65.4743)" width="15" x="687.0909090909091" y="345.5227272727273"/></g>
  <g id="74">
   <use class="v6300" height="30" transform="rotate(0,702.355,598.507) scale(1.9625,1.2338) translate(-337.248,-109.906)" width="15" x="687.6363636363636" xlink:href="#Disconnector:刀闸_0" y="580" zvalue="87"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449808564230" ObjectName="#1主变6.3kV侧6011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449808564230"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,702.355,598.507) scale(1.9625,1.2338) translate(-337.248,-109.906)" width="15" x="687.6363636363636" y="580"/></g>
  <g id="73">
   <use class="v6300" height="30" transform="rotate(0,1329.19,601.893) scale(1.9625,-1.2338) translate(-644.678,-1086.22)" width="15" x="1314.473647250707" xlink:href="#Disconnector:刀闸_0" y="583.3863636363637" zvalue="92"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449808695302" ObjectName="6.3kV母线电压互感器6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449808695302"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1329.19,601.893) scale(1.9625,-1.2338) translate(-644.678,-1086.22)" width="15" x="1314.473647250707" y="583.3863636363637"/></g>
  <g id="84">
   <use class="kv35" height="30" transform="rotate(0,738.173,277.961) scale(1.9625,1.2338) translate(-354.815,-49.1649)" width="15" x="723.4545454545454" xlink:href="#Disconnector:刀闸_0" y="259.4545454545454" zvalue="103"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449808826374" ObjectName="35kV南三二线3221隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449808826374"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,738.173,277.961) scale(1.9625,1.2338) translate(-354.815,-49.1649)" width="15" x="723.4545454545454" y="259.4545454545454"/></g>
  <g id="93">
   <use class="kv35" height="30" transform="rotate(0,737.264,127.961) scale(1.9625,1.2338) translate(-354.369,-20.7409)" width="15" x="722.5454545454545" xlink:href="#Disconnector:刀闸_0" y="109.4545454545453" zvalue="113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449809022982" ObjectName="35kV南三二线3226隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449809022982"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,737.264,127.961) scale(1.9625,1.2338) translate(-354.369,-20.7409)" width="15" x="722.5454545454545" y="109.4545454545453"/></g>
  <g id="110">
   <use class="kv35" height="30" transform="rotate(270,790.909,81.2727) scale(0.909091,0.666667) translate(78.4091,35.6364)" width="15" x="784.0909090909089" xlink:href="#Disconnector:刀闸_0" y="71.27272727272708" zvalue="123"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449809416198" ObjectName="35kV南三二线3229隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449809416198"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,790.909,81.2727) scale(0.909091,0.666667) translate(78.4091,35.6364)" width="15" x="784.0909090909089" y="71.27272727272708"/></g>
  <g id="140">
   <use class="kv35" height="30" transform="rotate(0,1229.45,279.28) scale(1.9625,1.2338) translate(-595.758,-49.4147)" width="15" x="1214.727272727273" xlink:href="#Disconnector:刀闸_0" y="260.7727276195179" zvalue="128"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449810202630" ObjectName="35kV南弄线3211隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449810202630"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1229.45,279.28) scale(1.9625,1.2338) translate(-595.758,-49.4147)" width="15" x="1214.727272727273" y="260.7727276195179"/></g>
  <g id="133">
   <use class="kv35" height="30" transform="rotate(0,1228.54,129.28) scale(1.9625,1.2338) translate(-595.312,-20.9907)" width="15" x="1213.818181818182" xlink:href="#Disconnector:刀闸_0" y="110.7727276195179" zvalue="138"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449810006022" ObjectName="35kV南弄线3216隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449810006022"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1228.54,129.28) scale(1.9625,1.2338) translate(-595.312,-20.9907)" width="15" x="1213.818181818182" y="110.7727276195179"/></g>
  <g id="126">
   <use class="kv35" height="30" transform="rotate(270,1282.18,82.5909) scale(0.909091,0.666667) translate(127.536,36.2955)" width="15" x="1275.363636363636" xlink:href="#Disconnector:刀闸_0" y="72.59090943769962" zvalue="149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449809612806" ObjectName="35kV南弄线3219隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449809612806"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1282.18,82.5909) scale(0.909091,0.666667) translate(127.536,36.2955)" width="15" x="1275.363636363636" y="72.59090943769962"/></g>
  <g id="161">
   <use class="kv35" height="30" transform="rotate(0,1483.28,403.484) scale(1.9625,-1.2338) translate(-720.252,-727.004)" width="15" x="1468.565659956499" xlink:href="#Disconnector:刀闸_0" y="384.9772727272729" zvalue="157"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449810464774" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449810464774"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1483.28,403.484) scale(1.9625,-1.2338) translate(-720.252,-727.004)" width="15" x="1468.565659956499" y="384.9772727272729"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="205">
   <use class="v6300" height="30" transform="rotate(0,1461.75,836.028) scale(1.69643,1.70833) translate(-590.337,-336.021)" width="28" x="1438" xlink:href="#EnergyConsumer:站用变DY接地_0" y="810.4029720279718" zvalue="62"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449808302086" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1461.75,836.028) scale(1.69643,1.70833) translate(-590.337,-336.021)" width="28" x="1438" y="810.4029720279718"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="88">
   <g id="880">
    <use class="kv35" height="50" transform="rotate(0,703.309,514.614) scale(1.46909,1.46909) translate(-217.535,-152.592)" width="30" x="681.27" xlink:href="#PowerTransformer2:Y-D_0" y="477.89" zvalue="69"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874438344708" ObjectName="35"/>
    </metadata>
   </g>
   <g id="881">
    <use class="v6300" height="50" transform="rotate(0,703.309,514.614) scale(1.46909,1.46909) translate(-217.535,-152.592)" width="30" x="681.27" xlink:href="#PowerTransformer2:Y-D_1" y="477.89" zvalue="69"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874438410244" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399451410435" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399451410435"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,703.309,514.614) scale(1.46909,1.46909) translate(-217.535,-152.592)" width="30" x="681.27" y="477.89"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="98">
   <use class="kv35" height="30" transform="rotate(90,629.025,403.906) scale(1.11574,0.892592) translate(-64.557,46.9918)" width="12" x="622.3308063853871" xlink:href="#GroundDisconnector:地刀12_0" y="390.5170134218388" zvalue="82"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449808433158" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449808433158"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,629.025,403.906) scale(1.11574,0.892592) translate(-64.557,46.9918)" width="12" x="622.3308063853871" y="390.5170134218388"/></g>
  <g id="89">
   <use class="kv35" height="30" transform="rotate(90,683.571,248.513) scale(1.11574,0.892592) translate(-70.2152,28.293)" width="12" x="676.8762609308415" xlink:href="#GroundDisconnector:地刀12_0" y="235.1237390691582" zvalue="109"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449808957446" ObjectName="35kV南三二线32217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449808957446"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,683.571,248.513) scale(1.11574,0.892592) translate(-70.2152,28.293)" width="12" x="676.8762609308415" y="235.1237390691582"/></g>
  <g id="95">
   <use class="kv35" height="30" transform="rotate(90,682.48,175.604) scale(1.11574,0.892592) translate(-70.1021,19.5197)" width="12" x="675.7853518399324" xlink:href="#GroundDisconnector:地刀12_0" y="162.2146481600673" zvalue="115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449809154054" ObjectName="35kV南三二线32260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449809154054"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,682.48,175.604) scale(1.11574,0.892592) translate(-70.1021,19.5197)" width="12" x="675.7853518399324" y="162.2146481600673"/></g>
  <g id="102">
   <use class="kv35" height="30" transform="rotate(90,683.025,81.0581) scale(1.11574,0.892592) translate(-70.1586,8.14279)" width="12" x="676.3308063853869" xlink:href="#GroundDisconnector:地刀12_0" y="67.66919361461271" zvalue="121"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449809350662" ObjectName="35kV南三二线32267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449809350662"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,683.025,81.0581) scale(1.11574,0.892592) translate(-70.1586,8.14279)" width="12" x="676.3308063853869" y="67.66919361461271"/></g>
  <g id="136">
   <use class="kv35" height="30" transform="rotate(90,1174.84,254.299) scale(1.11574,0.892592) translate(-121.177,28.9892)" width="12" x="1168.148988203569" xlink:href="#GroundDisconnector:地刀12_0" y="240.9096230273483" zvalue="134"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449810137094" ObjectName="35kV南弄线32117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449810137094"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1174.84,254.299) scale(1.11574,0.892592) translate(-121.177,28.9892)" width="12" x="1168.148988203569" y="240.9096230273483"/></g>
  <g id="132">
   <use class="kv35" height="30" transform="rotate(90,1173.75,176.922) scale(1.11574,0.892592) translate(-121.064,19.6783)" width="12" x="1167.05807911266" xlink:href="#GroundDisconnector:地刀12_0" y="163.5328303250398" zvalue="140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449809940486" ObjectName="35kV南弄线32160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449809940486"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1173.75,176.922) scale(1.11574,0.892592) translate(-121.064,19.6783)" width="12" x="1167.05807911266" y="163.5328303250398"/></g>
  <g id="128">
   <use class="kv35" height="30" transform="rotate(90,1174.3,82.5239) scale(1.11574,0.892592) translate(-121.12,8.31917)" width="12" x="1167.603533658114" xlink:href="#GroundDisconnector:地刀12_0" y="69.13502086036925" zvalue="146"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449809743878" ObjectName="35kV南弄线32167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449809743878"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1174.3,82.5239) scale(1.11574,0.892592) translate(-121.12,8.31917)" width="12" x="1167.603533658114" y="69.13502086036925"/></g>
  <g id="162">
   <use class="kv35" height="30" transform="rotate(270,1529.59,360.581) scale(-1.11574,0.892592) translate(-2899.82,41.7784)" width="12" x="1522.896466341886" xlink:href="#GroundDisconnector:地刀12_0" y="347.19192088734" zvalue="155"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449810595846" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449810595846"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1529.59,360.581) scale(-1.11574,0.892592) translate(-2899.82,41.7784)" width="12" x="1522.896466341886" y="347.19192088734"/></g>
  <g id="141">
   <use class="kv35" height="30" transform="rotate(270,1531.23,437.604) scale(-1.11574,0.892592) translate(-2902.92,51.0467)" width="12" x="1524.53282997825" xlink:href="#GroundDisconnector:地刀12_0" y="424.2146481600674" zvalue="163"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449810333702" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449810333702"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1531.23,437.604) scale(-1.11574,0.892592) translate(-2902.92,51.0467)" width="12" x="1524.53282997825" y="424.2146481600674"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="71">
   <use class="v6300" height="18" transform="rotate(0,1324.66,546.731) scale(-2.13675,-2.13675) translate(-1936.08,-792.37)" width="15" x="1308.635260021481" xlink:href="#Accessory:PT8_0" y="527.5" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449808629766" ObjectName="6.3kV母线PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1324.66,546.731) scale(-2.13675,-2.13675) translate(-1936.08,-792.37)" width="15" x="1308.635260021481" y="527.5"/></g>
  <g id="82">
   <use class="v6300" height="20" transform="rotate(270,1369.22,636.719) scale(1.07812,1.07812) translate(-98.4375,-45.3578)" width="20" x="1358.4375" xlink:href="#Accessory:线路PT3_0" y="625.9375" zvalue="100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449808760838" ObjectName="6.3kV母线PT避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1369.22,636.719) scale(1.07812,1.07812) translate(-98.4375,-45.3578)" width="20" x="1358.4375" y="625.9375"/></g>
  <g id="113">
   <use class="kv35" height="18" transform="rotate(270,848.051,81.2171) scale(1.56566,1.56566) translate(-302.433,-24.2521)" width="14" x="837.0909090909092" xlink:href="#Accessory:PT7_0" y="67.12621415990355" zvalue="125"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449809481734" ObjectName="35kV南三二线PT7"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(270,848.051,81.2171) scale(1.56566,1.56566) translate(-302.433,-24.2521)" width="14" x="837.0909090909092" y="67.12621415990355"/></g>
  <g id="124">
   <use class="kv35" height="18" transform="rotate(270,1339.32,82.5353) scale(1.56566,1.56566) translate(-479.925,-24.7283)" width="14" x="1328.363636363637" xlink:href="#Accessory:PT7_0" y="68.4443963248761" zvalue="152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449809547270" ObjectName="35kV南弄线PT7"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(270,1339.32,82.5353) scale(1.56566,1.56566) translate(-479.925,-24.7283)" width="14" x="1328.363636363637" y="68.4443963248761"/></g>
  <g id="159">
   <use class="kv35" height="18" transform="rotate(0,1480.57,481.776) scale(-2.13675,2.13675) translate(-2164.95,-246.074)" width="15" x="1464.545454545455" xlink:href="#Accessory:PT8_0" y="462.5454545454546" zvalue="160"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449810399238" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1480.57,481.776) scale(-2.13675,2.13675) translate(-2164.95,-246.074)" width="15" x="1464.545454545455" y="462.5454545454546"/></g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,338.625,313.5) scale(0.708333,0.665547) translate(135.059,152.525)" width="30" x="328" xlink:href="#State:红绿圆(方形)_0" y="303.52" zvalue="173"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374888337411" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,338.625,313.5) scale(0.708333,0.665547) translate(135.059,152.525)" width="30" x="328" y="303.52"/></g>
  <g id="160">
   <use height="30" transform="rotate(0,243,313.5) scale(0.708333,0.665547) translate(95.6838,152.525)" width="30" x="232.38" xlink:href="#State:红绿圆(方形)_0" y="303.52" zvalue="174"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,243,313.5) scale(0.708333,0.665547) translate(95.6838,152.525)" width="30" x="232.38" y="303.52"/></g>
  <g id="7">
   <use height="30" transform="rotate(0,310.812,125.464) scale(1.22222,1.03092) translate(-46.5114,-3.29938)" width="90" x="255.81" xlink:href="#State:全站检修_0" y="110" zvalue="178"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549587148802" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,310.812,125.464) scale(1.22222,1.03092) translate(-46.5114,-3.29938)" width="90" x="255.81" y="110"/></g>
 </g>
</svg>