<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549678768129" height="1045" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1045" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:避雷器PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1734.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2314.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变3_0" viewBox="0,0,32,35">
   <use terminal-index="0" type="0" x="16" xlink:href="#terminal" y="3.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="30" y1="23.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31" x2="31" y1="24.25" y2="26.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="29" y1="25" y2="25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29" x2="29" y1="22.25" y2="28.25"/>
   <ellipse cx="16.04" cy="11.97" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.97" cy="24.14" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067018" x2="16.03525619067018" y1="20.85" y2="24.95894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067017" x2="10.66666666666667" y1="24.98394833233988" y2="28.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.06302903072498" x2="21.16666666666667" y1="24.93990500916851" y2="28.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067018" x2="16.03525619067018" y1="7.6" y2="11.70894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067017" x2="12.05" y1="11.73394833233987" y2="14.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.06302903072498" x2="20.15" y1="11.68990500916851" y2="14.5"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:间隔模板_0" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(85,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="State:间隔模板_1" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(185,185,185)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV河东变" InitShowingPlane="" fill="rgb(0,0,0)" height="1045" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="59.4" id="1" preserveAspectRatio="xMidYMid slice" width="222.19" x="48.13" xlink:href="logo.png" y="31.03"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="274" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,159.225,60.7343) scale(1,1) translate(-1.06866e-14,0)" writing-mode="lr" x="159.22" xml:space="preserve" y="65.23" zvalue="10384"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,177.196,57.7466) scale(1,1) translate(0,3.96769e-15)" writing-mode="lr" x="177.2" xml:space="preserve" y="65.25" zvalue="10385"> 35kV河东变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="297" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,195.531,408.75) scale(1,1) translate(0,0)" width="72.88" x="159.09" y="396.75" zvalue="10477"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,195.531,408.75) scale(1,1) translate(0,0)" writing-mode="lr" x="195.53" xml:space="preserve" y="413.25" zvalue="10477">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="296" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,93.625,408.75) scale(1,1) translate(0,0)" width="72.88" x="57.19" y="396.75" zvalue="10478"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93.625,408.75) scale(1,1) translate(0,0)" writing-mode="lr" x="93.63" xml:space="preserve" y="413.25" zvalue="10478">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="295" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,93.625,368.25) scale(1,1) translate(0,0)" width="72.88" x="57.19" y="356.25" zvalue="10479"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93.625,368.25) scale(1,1) translate(0,0)" writing-mode="lr" x="93.63" xml:space="preserve" y="372.75" zvalue="10479">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,528.076,358.591) scale(1,1) translate(0,0)" writing-mode="lr" x="528.08" xml:space="preserve" y="363.09" zvalue="7577">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,528.076,675.035) scale(1,1) translate(0,0)" writing-mode="lr" x="528.08" xml:space="preserve" y="679.54" zvalue="7716">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,981.98,423.57) scale(1,1) translate(-2.14713e-13,0)" writing-mode="lr" x="981.98" xml:space="preserve" y="428.07" zvalue="8016">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1029.61,384.447) scale(1,1) translate(0,0)" writing-mode="lr" x="1029.61" xml:space="preserve" y="388.95" zvalue="8030">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1030.61,436.447) scale(1,1) translate(0,0)" writing-mode="lr" x="1030.61" xml:space="preserve" y="440.95" zvalue="8035">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,721.586,108.201) scale(1,1) translate(0,0)" writing-mode="lr" x="721.59" xml:space="preserve" y="112.7" zvalue="8808">35kV西东线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,700.809,284.145) scale(1,1) translate(0,0)" writing-mode="lr" x="700.8099999999999" xml:space="preserve" y="288.64" zvalue="8809">351</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,704.297,221.254) scale(1,1) translate(0,0)" writing-mode="lr" x="704.3" xml:space="preserve" y="225.75" zvalue="8811">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,693.232,197.816) scale(1,1) translate(0,0)" writing-mode="lr" x="693.23" xml:space="preserve" y="202.32" zvalue="8813">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,704.425,343.049) scale(1,1) translate(0,0)" writing-mode="lr" x="704.42" xml:space="preserve" y="347.55" zvalue="8816">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="219" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1004.75,520) scale(1,1) translate(0,0)" writing-mode="lr" x="1004.75" xml:space="preserve" y="524.5" zvalue="9703">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="631" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1375.98,448.084) scale(1,1) translate(0,0)" writing-mode="lr" x="1375.98" xml:space="preserve" y="452.58" zvalue="9723">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="629" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1374.37,403.18) scale(1,1) translate(0,0)" writing-mode="lr" x="1374.37" xml:space="preserve" y="407.68" zvalue="9725">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="628" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1426.11,601.042) scale(1,1) translate(0,0)" writing-mode="lr" x="1426.11" xml:space="preserve" y="605.54" zvalue="9729">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="625" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1375.25,646.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1375.25" xml:space="preserve" y="651.13" zvalue="9735">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="823" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1322.69,509.937) scale(1,1) translate(0,5.51087e-14)" writing-mode="lr" x="1322.686530553582" xml:space="preserve" y="514.4374998410543" zvalue="9740">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="854" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,679.667,654.596) scale(1,1) translate(0,0)" writing-mode="lr" x="679.67" xml:space="preserve" y="659.1" zvalue="9762">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="851" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,699.472,574.167) scale(1,1) translate(-1.40881e-13,0)" writing-mode="lr" x="699.47" xml:space="preserve" y="578.67" zvalue="9772">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1044.2,827.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1044.2" xml:space="preserve" y="832.47" zvalue="9859">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,997.13,781.618) scale(1,1) translate(0,0)" writing-mode="lr" x="997.13" xml:space="preserve" y="786.12" zvalue="9861">052</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1024.54,932.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1024.54" xml:space="preserve" y="936.63" zvalue="9865">10kV河南线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1044.2,735.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1044.2" xml:space="preserve" y="740.47" zvalue="9869">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1244.2,827.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1244.2" xml:space="preserve" y="832.47" zvalue="9874">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1197.13,781.618) scale(1,1) translate(-1.31576e-12,0)" writing-mode="lr" x="1197.13" xml:space="preserve" y="786.12" zvalue="9876">053</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1224.54,932.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1224.54" xml:space="preserve" y="936.63" zvalue="9880">10kV河木线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1244.2,735.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1244.2" xml:space="preserve" y="740.47" zvalue="9884">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1444.2,827.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1444.2" xml:space="preserve" y="832.47" zvalue="9889">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1397.13,781.618) scale(1,1) translate(0,0)" writing-mode="lr" x="1397.13" xml:space="preserve" y="786.12" zvalue="9891">054</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1424.54,932.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1424.54" xml:space="preserve" y="936.63" zvalue="9895">10kV备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1444.2,735.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1444.2" xml:space="preserve" y="740.47" zvalue="9899">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1213.08,403.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1213.08" xml:space="preserve" y="408" zvalue="9923">3811</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,901.586,109.312) scale(1,1) translate(0,0)" writing-mode="lr" x="901.59" xml:space="preserve" y="113.81" zvalue="9967">35kV河平线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,880.809,284.145) scale(1,1) translate(0,0)" writing-mode="lr" x="880.8099999999999" xml:space="preserve" y="288.64" zvalue="9968">352</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,884.297,221.254) scale(1,1) translate(0,0)" writing-mode="lr" x="884.3" xml:space="preserve" y="225.75" zvalue="9970">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,884.425,343.049) scale(1,1) translate(0,0)" writing-mode="lr" x="884.42" xml:space="preserve" y="347.55" zvalue="9975">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1093.59,109.312) scale(1,1) translate(0,0)" writing-mode="lr" x="1093.59" xml:space="preserve" y="113.81" zvalue="9986">35kV邦河线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1072.81,284.145) scale(1,1) translate(5.88867e-13,0)" writing-mode="lr" x="1072.81" xml:space="preserve" y="288.64" zvalue="9987">353</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1076.3,221.254) scale(1,1) translate(0,0)" writing-mode="lr" x="1076.3" xml:space="preserve" y="225.75" zvalue="9989">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1076.42,343.049) scale(1,1) translate(3.09997e-12,0)" writing-mode="lr" x="1076.42" xml:space="preserve" y="347.55" zvalue="9994">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="205" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1273.59,108.312) scale(1,1) translate(0,0)" writing-mode="lr" x="1273.59" xml:space="preserve" y="112.81" zvalue="10004">35kV马茂电站线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1252.81,284.145) scale(1,1) translate(0,0)" writing-mode="lr" x="1252.81" xml:space="preserve" y="288.64" zvalue="10005">354</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1256.3,221.254) scale(1,1) translate(0,0)" writing-mode="lr" x="1256.3" xml:space="preserve" y="225.75" zvalue="10007">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1256.42,343.049) scale(1,1) translate(0,0)" writing-mode="lr" x="1256.42" xml:space="preserve" y="347.55" zvalue="10012">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1193,492.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1193" xml:space="preserve" y="497" zvalue="10020">35kV站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,830.363,827.884) scale(1,1) translate(0,0)" writing-mode="lr" x="830.36" xml:space="preserve" y="832.38" zvalue="10039">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,787.293,781.527) scale(1,1) translate(0,0)" writing-mode="lr" x="787.29" xml:space="preserve" y="786.03" zvalue="10041">051</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,810.699,932.034) scale(1,1) translate(0,0)" writing-mode="lr" x="810.7" xml:space="preserve" y="936.53" zvalue="10045">10kV马河线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,830.363,735.884) scale(1,1) translate(0,0)" writing-mode="lr" x="830.36" xml:space="preserve" y="740.38" zvalue="10049">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,688.999,268.045) scale(1,1) translate(0,-4.61045e-13)" writing-mode="lr" x="689" xml:space="preserve" y="272.55" zvalue="10056">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,688.999,332.962) scale(1,1) translate(0,0)" writing-mode="lr" x="689" xml:space="preserve" y="337.46" zvalue="10061">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,875.232,197.816) scale(1,1) translate(0,0)" writing-mode="lr" x="875.23" xml:space="preserve" y="202.32" zvalue="10064">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1069.3,196.732) scale(1,1) translate(0,0)" writing-mode="lr" x="1069.3" xml:space="preserve" y="201.23" zvalue="10076">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1065.07,266.962) scale(1,1) translate(0,4.30425e-13)" writing-mode="lr" x="1065.07" xml:space="preserve" y="271.46" zvalue="10078">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1065.07,331.878) scale(1,1) translate(0,0)" writing-mode="lr" x="1065.07" xml:space="preserve" y="336.38" zvalue="10080">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1242.3,196.732) scale(1,1) translate(0,0)" writing-mode="lr" x="1242.3" xml:space="preserve" y="201.23" zvalue="10089">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,689,140.5) scale(1,1) translate(0,0)" writing-mode="lr" x="689" xml:space="preserve" y="145" zvalue="10097">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1063,140) scale(1,1) translate(0,0)" writing-mode="lr" x="1063" xml:space="preserve" y="144.5" zvalue="10103">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1426.37,405.217) scale(1,1) translate(0,0)" writing-mode="lr" x="1426.37" xml:space="preserve" y="409.72" zvalue="10111">17</text>
  <line fill="none" id="272" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="373.5714285714286" x2="373.5714285714286" y1="15.35714285714289" y2="1005.357142857143" zvalue="10386"/>
  <line fill="none" id="270" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.47692454998014" x2="314.0881846035993" y1="169.1279458827686" y2="169.1279458827686" zvalue="10388"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="37.57142857142856" x2="109.0114285714285" y1="928.0627909390723" y2="928.0627909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="37.57142857142856" x2="109.0114285714285" y1="979.4022909390724" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="37.57142857142856" x2="37.57142857142856" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="109.0114285714285" x2="109.0114285714285" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="109.0119285714286" x2="343.5719285714285" y1="928.0627909390723" y2="928.0627909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="109.0119285714286" x2="343.5719285714285" y1="979.4022909390724" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="109.0119285714286" x2="109.0119285714286" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="343.5719285714285" x2="343.5719285714285" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="37.57142857142856" x2="109.0114285714285" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="37.57142857142856" x2="109.0114285714285" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="37.57142857142856" x2="37.57142857142856" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="109.0114285714285" x2="109.0114285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="109.0119285714286" x2="180.0472285714286" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="109.0119285714286" x2="180.0472285714286" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="109.0119285714286" x2="109.0119285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.0472285714286" x2="180.0472285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.0472285714286" x2="261.8093285714285" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.0472285714286" x2="261.8093285714285" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.0472285714286" x2="180.0472285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="261.8093285714285" x2="261.8093285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="261.8092285714285" x2="343.5713285714286" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="261.8092285714285" x2="343.5713285714286" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="261.8092285714285" x2="261.8092285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="343.5713285714286" x2="343.5713285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="37.57142857142856" x2="109.0114285714285" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="37.57142857142856" x2="109.0114285714285" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="37.57142857142856" x2="37.57142857142856" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="109.0114285714285" x2="109.0114285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="109.0119285714286" x2="180.0472285714286" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="109.0119285714286" x2="180.0472285714286" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="109.0119285714286" x2="109.0119285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.0472285714286" x2="180.0472285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.0472285714286" x2="261.8093285714285" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.0472285714286" x2="261.8093285714285" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.0472285714286" x2="180.0472285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="261.8093285714285" x2="261.8093285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="261.8092285714285" x2="343.5713285714286" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="261.8092285714285" x2="343.5713285714286" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="261.8092285714285" x2="261.8092285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="343.5713285714286" x2="343.5713285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="268" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,74.9633,957.636) scale(1,1) translate(0,1.05117e-13)" writing-mode="lr" x="42.93" xml:space="preserve" y="963.64" zvalue="10390">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="267" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,71.3269,995.005) scale(1,1) translate(0,1.09266e-13)" writing-mode="lr" x="52.65" xml:space="preserve" y="1001" zvalue="10391">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="266" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,258.452,996.005) scale(1,1) translate(1.26398e-13,-1.53127e-12)" writing-mode="lr" x="189.75" xml:space="preserve" y="1002" zvalue="10392">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="265" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,66.2423,1024.56) scale(1,1) translate(-4.13793e-14,1.01293e-12)" writing-mode="lr" x="66.23999999999999" xml:space="preserve" y="1030.56" zvalue="10393">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="264" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,266.746,1022.56) scale(1,1) translate(0,1.12325e-13)" writing-mode="lr" x="189.92" xml:space="preserve" y="1028.56" zvalue="10394">更新日期    20231205</text>
  <line fill="none" id="263" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.85611355802348" x2="313.4673736116426" y1="619.8945306693694" y2="619.8945306693694" zvalue="10395"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="261" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.9207,640.936) scale(1,1) translate(2.05835e-15,-1.38164e-13)" writing-mode="lr" x="79.92070806204504" xml:space="preserve" y="645.4363811688671" zvalue="10397">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-1.428571428571445" x2="179.5714285714286" y1="172.3571428571429" y2="172.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-1.428571428571445" x2="179.5714285714286" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-1.428571428571445" x2="-1.428571428571445" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="179.5714285714286" x2="179.5714285714286" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="179.5714285714286" x2="360.5714285714286" y1="172.3571428571429" y2="172.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="179.5714285714286" x2="360.5714285714286" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="179.5714285714286" x2="179.5714285714286" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="360.5714285714286" x2="360.5714285714286" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-1.428571428571445" x2="179.5714285714286" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-1.428571428571445" x2="179.5714285714286" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-1.428571428571445" x2="-1.428571428571445" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="179.5714285714286" x2="179.5714285714286" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="179.5714285714286" x2="360.5714285714286" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="179.5714285714286" x2="360.5714285714286" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="179.5714285714286" x2="179.5714285714286" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="360.5714285714286" x2="360.5714285714286" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-1.428571428571445" x2="179.5714285714286" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-1.428571428571445" x2="179.5714285714286" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-1.428571428571445" x2="-1.428571428571445" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="179.5714285714286" x2="179.5714285714286" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="179.5714285714286" x2="360.5714285714286" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="179.5714285714286" x2="360.5714285714286" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="179.5714285714286" x2="179.5714285714286" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="360.5714285714286" x2="360.5714285714286" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-1.428571428571445" x2="179.5714285714286" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-1.428571428571445" x2="179.5714285714286" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-1.428571428571445" x2="-1.428571428571445" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="179.5714285714286" x2="179.5714285714286" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="179.5714285714286" x2="360.5714285714286" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="179.5714285714286" x2="360.5714285714286" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="179.5714285714286" x2="179.5714285714286" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="360.5714285714286" x2="360.5714285714286" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-1.428571428571445" x2="179.5714285714286" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-1.428571428571445" x2="179.5714285714286" y1="290.8571428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-1.428571428571445" x2="-1.428571428571445" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="179.5714285714286" x2="179.5714285714286" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="179.5714285714286" x2="360.5714285714286" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="179.5714285714286" x2="360.5714285714286" y1="290.8571428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="179.5714285714286" x2="179.5714285714286" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="360.5714285714286" x2="360.5714285714286" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="49.57142857142856" x2="95.34592857142854" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="49.57142857142856" x2="95.34592857142854" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="49.57142857142856" x2="49.57142857142856" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="95.34592857142854" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="154.1523285714286" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="154.1523285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="95.34592857142854" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="154.1523285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="212.9587285714285" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="212.9587285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="154.1523285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9587285714285" x2="212.9587285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9586285714286" x2="271.7650285714285" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9586285714286" x2="271.7650285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9586285714286" x2="212.9586285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="271.7650285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="330.5714285714286" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="330.5714285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="271.7650285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="330.5714285714286" x2="330.5714285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="49.57142857142856" x2="95.34592857142854" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="49.57142857142856" x2="95.34592857142854" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="49.57142857142856" x2="49.57142857142856" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="95.34592857142854" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="154.1523285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="154.1523285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="95.34592857142854" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="154.1523285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="212.9587285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="212.9587285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="154.1523285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9587285714285" x2="212.9587285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9586285714286" x2="271.7650285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9586285714286" x2="271.7650285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9586285714286" x2="212.9586285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="271.7650285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="330.5714285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="330.5714285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="271.7650285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="330.5714285714286" x2="330.5714285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="49.57142857142856" x2="95.34592857142854" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="49.57142857142856" x2="95.34592857142854" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="49.57142857142856" x2="49.57142857142856" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="95.34592857142854" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="154.1523285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="154.1523285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="95.34592857142854" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="154.1523285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="212.9587285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="212.9587285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="154.1523285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9587285714285" x2="212.9587285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9586285714286" x2="271.7650285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9586285714286" x2="271.7650285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9586285714286" x2="212.9586285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="271.7650285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="330.5714285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="330.5714285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="271.7650285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="330.5714285714286" x2="330.5714285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="49.57142857142856" x2="95.34592857142854" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="49.57142857142856" x2="95.34592857142854" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="49.57142857142856" x2="49.57142857142856" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="95.34592857142854" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="154.1523285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="154.1523285714286" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="95.34592857142854" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="154.1523285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="212.9587285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="212.9587285714285" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="154.1523285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9587285714285" x2="212.9587285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9586285714286" x2="271.7650285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9586285714286" x2="271.7650285714285" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9586285714286" x2="212.9586285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="271.7650285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="330.5714285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="330.5714285714286" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="271.7650285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="330.5714285714286" x2="330.5714285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="49.57142857142856" x2="95.34592857142854" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="49.57142857142856" x2="95.34592857142854" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="49.57142857142856" x2="49.57142857142856" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="95.34592857142854" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="154.1523285714286" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="154.1523285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="95.34592857142854" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="154.1523285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="212.9587285714285" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="212.9587285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="154.1523285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9587285714285" x2="212.9587285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9586285714286" x2="271.7650285714285" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9586285714286" x2="271.7650285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9586285714286" x2="212.9586285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="271.7650285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="330.5714285714286" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="330.5714285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="271.7650285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="330.5714285714286" x2="330.5714285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="49.57142857142856" x2="95.34592857142854" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="49.57142857142856" x2="95.34592857142854" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="49.57142857142856" x2="49.57142857142856" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="95.34592857142854" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="154.1523285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="154.1523285714286" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="95.34592857142854" x2="95.34592857142854" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="154.1523285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="212.9587285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="212.9587285714285" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="154.1523285714286" x2="154.1523285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9587285714285" x2="212.9587285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9586285714286" x2="271.7650285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9586285714286" x2="271.7650285714285" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="212.9586285714286" x2="212.9586285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="271.7650285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="330.5714285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="330.5714285714286" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="271.7650285714285" x2="271.7650285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="330.5714285714286" x2="330.5714285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="257" stroke="rgb(255,255,255)" text-anchor="middle" x="126.578125" xml:space="preserve" y="467.359375" zvalue="10401">35kV     母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="257" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="126.578125" xml:space="preserve" y="483.359375" zvalue="10401">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="256" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,195.97,327.199) scale(1,1) translate(0,0)" writing-mode="lr" x="195.97" xml:space="preserve" y="331.7" zvalue="10402">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,300.97,327.199) scale(1,1) translate(0,0)" writing-mode="lr" x="300.97" xml:space="preserve" y="331.7" zvalue="10403">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="254" stroke="rgb(255,255,255)" text-anchor="middle" x="243.90625" xml:space="preserve" y="467.359375" zvalue="10404">10kV     母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="254" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="243.90625" xml:space="preserve" y="483.359375" zvalue="10404">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.5714,502.857) scale(1,1) translate(0,0)" writing-mode="lr" x="74.57142857142856" xml:space="preserve" y="507.3571428571429" zvalue="10405">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.5714,528.357) scale(1,1) translate(0,0)" writing-mode="lr" x="74.57142857142856" xml:space="preserve" y="532.8571428571429" zvalue="10406">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.5714,553.857) scale(1,1) translate(0,0)" writing-mode="lr" x="74.57142857142856" xml:space="preserve" y="558.3571428571429" zvalue="10407">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="250" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.5714,579.357) scale(1,1) translate(0,0)" writing-mode="lr" x="74.57142857142856" xml:space="preserve" y="583.8571428571429" zvalue="10408">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="249" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.5714,604.857) scale(1,1) translate(0,0)" writing-mode="lr" x="74.57142857142856" xml:space="preserve" y="609.3571428571429" zvalue="10409">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="248" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,36.5714,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="36.57" xml:space="preserve" y="191.86" zvalue="10410">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.821,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="222.82" xml:space="preserve" y="191.86" zvalue="10411">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="246" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,40.2589,210.607) scale(1,1) translate(0,0)" writing-mode="lr" x="40.26" xml:space="preserve" y="215.11" zvalue="10412">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="245" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,43.7589,258.357) scale(1,1) translate(0,0)" writing-mode="lr" x="43.76" xml:space="preserve" y="262.86" zvalue="10413">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="244" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,43.7589,281.357) scale(1,1) translate(0,0)" writing-mode="lr" x="43.76" xml:space="preserve" y="285.86" zvalue="10414">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="243" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,41.2589,234.607) scale(1,1) translate(0,0)" writing-mode="lr" x="41.26" xml:space="preserve" y="239.11" zvalue="10415">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="276" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224.759,258.357) scale(1,1) translate(0,0)" writing-mode="lr" x="224.76" xml:space="preserve" y="262.86" zvalue="10435">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="277" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224.759,281.357) scale(1,1) translate(0,0)" writing-mode="lr" x="224.76" xml:space="preserve" y="285.86" zvalue="10436">2号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="162" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,220.963,957.636) scale(1,1) translate(-3.50668e-14,1.05117e-13)" writing-mode="lr" x="157.93" xml:space="preserve" y="963.64" zvalue="10481">HeDong-01-2023</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="259" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,145.327,1023) scale(1,1) translate(0,1.12374e-13)" writing-mode="lr" x="126.65" xml:space="preserve" y="1029" zvalue="10483">段勇</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="213" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,300.235,408.5) scale(1,1) translate(0,0)" writing-mode="lr" x="300.2350158691406" xml:space="preserve" y="413" zvalue="10504">小电流/减载</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="299" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,96.2857,327.039) scale(1,1) translate(0,3.50763e-13)" writing-mode="lr" x="96.28570422097823" xml:space="preserve" y="331.5388695954761" zvalue="10506">全站公用</text>
  <ellipse cx="921.66" cy="284.16" fill="rgb(255,0,0)" fill-opacity="1" id="304" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10521"/>
  <ellipse cx="1296.66" cy="284.16" fill="rgb(255,0,0)" fill-opacity="1" id="305" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="10523"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="306" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1321.5,534) scale(1,1) translate(0,0)" writing-mode="lr" x="1321.5" xml:space="preserve" y="539.5" zvalue="10524">5000kVA</text>
 </g>
 <g id="ButtonClass">
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="159.09" y="396.75" zvalue="10477"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="57.19" y="396.75" zvalue="10478"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="57.19" y="356.25" zvalue="10479"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="48">
   <path class="kv35" d="M 537.67 371.59 L 1590 371.59" stroke-width="6" zvalue="7576"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674398437379" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674398437379"/></metadata>
  <path d="M 537.67 371.59 L 1590 371.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv10" d="M 537.67 690.59 L 1589 690.59" stroke-width="6" zvalue="7715"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674398502915" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674398502915"/></metadata>
  <path d="M 537.67 690.59 L 1589 690.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="1453">
   <use class="kv35" height="30" transform="rotate(180,1002.01,424.263) scale(0.947693,-0.6712) translate(54.9128,-1061.29)" width="15" x="994.9024010422277" xlink:href="#Disconnector:刀闸_0" y="414.1951361579611" zvalue="8015"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453753896962" ObjectName="35kV母线3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453753896962"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1002.01,424.263) scale(0.947693,-0.6712) translate(54.9128,-1061.29)" width="15" x="994.9024010422277" y="414.1951361579611"/></g>
  <g id="68">
   <use class="kv35" height="30" transform="rotate(0,722.211,219.088) scale(-0.947693,0.6712) translate(-1484.68,102.392)" width="15" x="715.1034928921569" xlink:href="#Disconnector:刀闸_0" y="209.0197927208583" zvalue="8810"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453753438210" ObjectName="35kV西东线3516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453753438210"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,722.211,219.088) scale(-0.947693,0.6712) translate(-1484.68,102.392)" width="15" x="715.1034928921569" y="209.0197927208583"/></g>
  <g id="65">
   <use class="kv35" height="30" transform="rotate(180,722.392,342.357) scale(0.947693,-0.6712) translate(39.4795,-857.355)" width="15" x="715.2841334259956" xlink:href="#Disconnector:刀闸_0" y="332.2885305077181" zvalue="8815"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453753241602" ObjectName="35kV西东线3511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453753241602"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,722.392,342.357) scale(0.947693,-0.6712) translate(39.4795,-857.355)" width="15" x="715.2841334259956" y="332.2885305077181"/></g>
  <g id="815">
   <use class="kv35" height="30" transform="rotate(0,1397.4,402.872) scale(0.947693,-0.6712) translate(76.736,-1008.03)" width="15" x="1390.293211654153" xlink:href="#Disconnector:刀闸_0" y="392.8043387992517" zvalue="9724"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453754093570" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453754093570"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1397.4,402.872) scale(0.947693,-0.6712) translate(76.736,-1008.03)" width="15" x="1390.293211654153" y="392.8043387992517"/></g>
  <g id="636">
   <use class="kv10" height="30" transform="rotate(0,1398.28,646.318) scale(0.947693,-0.6712) translate(76.7847,-1614.18)" width="15" x="1391.175204945725" xlink:href="#Disconnector:刀闸_0" y="636.25" zvalue="9734"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453754028034" ObjectName="#2主变10kV侧0021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453754028034"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1398.28,646.318) scale(0.947693,-0.6712) translate(76.7847,-1614.18)" width="15" x="1391.175204945725" y="636.25"/></g>
  <g id="864">
   <use class="kv10" height="30" transform="rotate(180,699.697,653.904) scale(0.947693,-0.6712) translate(38.2269,-1633.07)" width="15" x="692.5894229335038" xlink:href="#Disconnector:刀闸_0" y="643.8355178058908" zvalue="9761"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453754224642" ObjectName="10kV母线0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453754224642"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,699.697,653.904) scale(0.947693,-0.6712) translate(38.2269,-1633.07)" width="15" x="692.5894229335038" y="643.8355178058908"/></g>
  <g id="116">
   <use class="kv10" height="30" transform="rotate(180,1023.29,827.391) scale(-0.947693,0.6712) translate(-2103.44,400.38)" width="15" x="1016.178832655726" xlink:href="#Disconnector:刀闸_0" y="817.3230764122643" zvalue="9858"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453754486786" ObjectName="10kV河南线0526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453754486786"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1023.29,827.391) scale(-0.947693,0.6712) translate(-2103.44,400.38)" width="15" x="1016.178832655726" y="817.3230764122643"/></g>
  <g id="109">
   <use class="kv10" height="30" transform="rotate(180,1023.29,735.391) scale(-0.947693,0.6712) translate(-2103.44,355.313)" width="15" x="1016.178822483201" xlink:href="#Disconnector:刀闸_0" y="725.3230764122643" zvalue="9868"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453754290178" ObjectName="10kV河南线0521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453754290178"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1023.29,735.391) scale(-0.947693,0.6712) translate(-2103.44,355.313)" width="15" x="1016.178822483201" y="725.3230764122643"/></g>
  <g id="133">
   <use class="kv10" height="30" transform="rotate(180,1223.29,827.391) scale(-0.947693,0.6712) translate(-2514.48,400.38)" width="15" x="1216.178832655726" xlink:href="#Disconnector:刀闸_0" y="817.3230764122643" zvalue="9873"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453754748930" ObjectName="10kV河木线0536隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453754748930"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1223.29,827.391) scale(-0.947693,0.6712) translate(-2514.48,400.38)" width="15" x="1216.178832655726" y="817.3230764122643"/></g>
  <g id="125">
   <use class="kv10" height="30" transform="rotate(180,1223.29,735.391) scale(-0.947693,0.6712) translate(-2514.48,355.313)" width="15" x="1216.178822483201" xlink:href="#Disconnector:刀闸_0" y="725.3230764122643" zvalue="9883"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453754552322" ObjectName="10kV河木线0531隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453754552322"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1223.29,735.391) scale(-0.947693,0.6712) translate(-2514.48,355.313)" width="15" x="1216.178822483201" y="725.3230764122643"/></g>
  <g id="159">
   <use class="kv10" height="30" transform="rotate(180,1423.29,827.391) scale(-0.947693,0.6712) translate(-2925.52,400.38)" width="15" x="1416.178832655726" xlink:href="#Disconnector:刀闸_0" y="817.3230764122643" zvalue="9888"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453755011074" ObjectName="10kV备用线0546隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453755011074"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1423.29,827.391) scale(-0.947693,0.6712) translate(-2925.52,400.38)" width="15" x="1416.178832655726" y="817.3230764122643"/></g>
  <g id="145">
   <use class="kv10" height="30" transform="rotate(180,1423.29,735.391) scale(-0.947693,0.6712) translate(-2925.52,355.313)" width="15" x="1416.178822483201" xlink:href="#Disconnector:刀闸_0" y="725.3230764122643" zvalue="9898"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453754814466" ObjectName="10kV备用线0541隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453754814466"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1423.29,735.391) scale(-0.947693,0.6712) translate(-2925.52,355.313)" width="15" x="1416.178822483201" y="725.3230764122643"/></g>
  <g id="2">
   <use class="kv35" height="30" transform="rotate(180,1186.08,404.5) scale(1,1) translate(0,0)" width="15" x="1178.583333333333" xlink:href="#Disconnector:令克_0" y="389.5" zvalue="9922"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453755076610" ObjectName="35kV#1站用变3811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453755076610"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1186.08,404.5) scale(1,1) translate(0,0)" width="15" x="1178.583333333333" y="389.5"/></g>
  <g id="99">
   <use class="kv35" height="30" transform="rotate(0,902.211,219.088) scale(-0.947693,0.6712) translate(-1854.61,102.392)" width="15" x="895.1034928921569" xlink:href="#Disconnector:刀闸_0" y="209.0197927208583" zvalue="9969"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453755207682" ObjectName="35kV河平线3526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453755207682"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,902.211,219.088) scale(-0.947693,0.6712) translate(-1854.61,102.392)" width="15" x="895.1034928921569" y="209.0197927208583"/></g>
  <g id="96">
   <use class="kv35" height="30" transform="rotate(180,902.392,342.357) scale(0.947693,-0.6712) translate(49.4144,-857.355)" width="15" x="895.2841334259956" xlink:href="#Disconnector:刀闸_0" y="332.2885305077181" zvalue="9974"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453755142146" ObjectName="35kV河平线3521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453755142146"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,902.392,342.357) scale(0.947693,-0.6712) translate(49.4144,-857.355)" width="15" x="895.2841334259956" y="332.2885305077181"/></g>
  <g id="195">
   <use class="kv35" height="30" transform="rotate(0,1094.21,219.088) scale(-0.947693,0.6712) translate(-2249.21,102.392)" width="15" x="1087.103492892157" xlink:href="#Disconnector:刀闸_0" y="209.0197927208583" zvalue="9988"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453755404290" ObjectName="35kV邦河线3536隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453755404290"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1094.21,219.088) scale(-0.947693,0.6712) translate(-2249.21,102.392)" width="15" x="1087.103492892157" y="209.0197927208583"/></g>
  <g id="192">
   <use class="kv35" height="30" transform="rotate(180,1094.39,342.357) scale(0.947693,-0.6712) translate(60.0117,-857.355)" width="15" x="1087.284133425996" xlink:href="#Disconnector:刀闸_0" y="332.2885305077181" zvalue="9993"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453755338754" ObjectName="35kV邦河线3531隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453755338754"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1094.39,342.357) scale(0.947693,-0.6712) translate(60.0117,-857.355)" width="15" x="1087.284133425996" y="332.2885305077181"/></g>
  <g id="230">
   <use class="kv35" height="30" transform="rotate(0,1274.21,219.088) scale(-0.947693,0.6712) translate(-2619.14,102.392)" width="15" x="1267.103492892157" xlink:href="#Disconnector:刀闸_0" y="209.0197927208583" zvalue="10006"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453755600899" ObjectName="35kV马茂电站线3546隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453755600899"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1274.21,219.088) scale(-0.947693,0.6712) translate(-2619.14,102.392)" width="15" x="1267.103492892157" y="209.0197927208583"/></g>
  <g id="227">
   <use class="kv35" height="30" transform="rotate(180,1274.39,342.357) scale(0.947693,-0.6712) translate(69.9466,-857.355)" width="15" x="1267.284133425996" xlink:href="#Disconnector:刀闸_0" y="332.2885305077181" zvalue="10011"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453755535362" ObjectName="35kV马茂电站线3541隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453755535362"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1274.39,342.357) scale(0.947693,-0.6712) translate(69.9466,-857.355)" width="15" x="1267.284133425996" y="332.2885305077181"/></g>
  <g id="61">
   <use class="kv10" height="30" transform="rotate(180,809.449,827.3) scale(-0.947693,0.6712) translate(-1663.97,400.336)" width="15" x="802.3415919355261" xlink:href="#Disconnector:刀闸_0" y="817.2321673213551" zvalue="10038"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453755994114" ObjectName="10kV马河线0516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453755994114"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,809.449,827.3) scale(-0.947693,0.6712) translate(-1663.97,400.336)" width="15" x="802.3415919355261" y="817.2321673213551"/></g>
  <g id="46">
   <use class="kv10" height="30" transform="rotate(180,809.449,735.3) scale(-0.947693,0.6712) translate(-1663.97,355.268)" width="15" x="802.3415817630006" xlink:href="#Disconnector:刀闸_0" y="725.2321673213551" zvalue="10048"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453755797506" ObjectName="10kV马河线0511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453755797506"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,809.449,735.3) scale(-0.947693,0.6712) translate(-1663.97,355.268)" width="15" x="802.3415817630006" y="725.2321673213551"/></g>
  <g id="75">
   <use class="kv35" height="30" transform="rotate(90,690,161.5) scale(1,1) translate(0,0)" width="15" x="682.5" xlink:href="#Disconnector:令克_0" y="146.5" zvalue="10096"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453757239298" ObjectName="35kV西东线3519隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453757239298"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,690,161.5) scale(1,1) translate(0,0)" width="15" x="682.5" y="146.5"/></g>
  <g id="80">
   <use class="kv35" height="30" transform="rotate(90,1063,161.5) scale(1,1) translate(0,0)" width="15" x="1055.5" xlink:href="#Disconnector:令克_0" y="146.5" zvalue="10102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453757370370" ObjectName="35kV邦河线3539隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453757370370"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1063,161.5) scale(1,1) translate(0,0)" width="15" x="1055.5" y="146.5"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="150">
   <use class="kv35" height="20" transform="rotate(90,1028.37,398.448) scale(1.24619,-1.0068) translate(-201.926,-794.137)" width="10" x="1022.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="388.3800877847166" zvalue="8029"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453753831426" ObjectName="35kV母线39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453753831426"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1028.37,398.448) scale(1.24619,-1.0068) translate(-201.926,-794.137)" width="10" x="1022.143242399862" y="388.3800877847166"/></g>
  <g id="170">
   <use class="kv35" height="20" transform="rotate(90,1029.37,450.448) scale(1.24619,-1.0068) translate(-202.124,-897.786)" width="10" x="1023.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="440.3800877847167" zvalue="8034"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453753700354" ObjectName="35kV母线39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453753700354"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1029.37,450.448) scale(1.24619,-1.0068) translate(-202.124,-897.786)" width="10" x="1023.143242399862" y="440.3800877847167"/></g>
  <g id="67">
   <use class="kv35" height="20" transform="rotate(270,687.999,183.815) scale(-1.24619,-1.0068) translate(-1238.85,-366.319)" width="10" x="681.7682271410732" xlink:href="#GroundDisconnector:地刀_0" y="173.746507813326" zvalue="8812"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453753372674" ObjectName="35kV西东线35167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453753372674"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,687.999,183.815) scale(-1.24619,-1.0068) translate(-1238.85,-366.319)" width="10" x="681.7682271410732" y="173.746507813326"/></g>
  <g id="4">
   <use class="kv35" height="20" transform="rotate(90,688.999,249.815) scale(1.24619,1.0068) translate(-134.882,-1.61942)" width="10" x="682.7682118822842" xlink:href="#GroundDisconnector:地刀_0" y="239.746507813326" zvalue="10055"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453756125186" ObjectName="35kV西东线35160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453756125186"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,688.999,249.815) scale(1.24619,1.0068) translate(-134.882,-1.61942)" width="10" x="682.7682118822842" y="239.746507813326"/></g>
  <g id="10">
   <use class="kv35" height="20" transform="rotate(90,688.999,318.731) scale(1.24619,1.0068) translate(-134.882,-2.08493)" width="10" x="682.7682136255372" xlink:href="#GroundDisconnector:地刀_0" y="308.662927274121" zvalue="10060"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453756256258" ObjectName="35kV西东线35117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453756256258"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,688.999,318.731) scale(1.24619,1.0068) translate(-134.882,-2.08493)" width="10" x="682.7682136255372" y="308.662927274121"/></g>
  <g id="16">
   <use class="kv35" height="20" transform="rotate(270,869.999,183.815) scale(-1.24619,-1.0068) translate(-1566.9,-366.319)" width="10" x="863.7682271410732" xlink:href="#GroundDisconnector:地刀_0" y="173.746507813326" zvalue="10063"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453756387330" ObjectName="35kV河平线35267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453756387330"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,869.999,183.815) scale(-1.24619,-1.0068) translate(-1566.9,-366.319)" width="10" x="863.7682271410732" y="173.746507813326"/></g>
  <g id="30">
   <use class="kv35" height="20" transform="rotate(270,1064.07,182.731) scale(-1.24619,-1.0068) translate(-1916.7,-364.16)" width="10" x="1057.837072725879" xlink:href="#GroundDisconnector:地刀_0" y="172.6629272741209" zvalue="10075"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453756583938" ObjectName="35kV邦河线35367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453756583938"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1064.07,182.731) scale(-1.24619,-1.0068) translate(-1916.7,-364.16)" width="10" x="1057.837072725879" y="172.6629272741209"/></g>
  <g id="31">
   <use class="kv35" height="20" transform="rotate(90,1065.07,248.731) scale(1.24619,1.0068) translate(-209.175,-1.6121)" width="10" x="1058.83705746709" xlink:href="#GroundDisconnector:地刀_0" y="238.6629272741209" zvalue="10077"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453756715010" ObjectName="35kV邦河线35360接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453756715010"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1065.07,248.731) scale(1.24619,1.0068) translate(-209.175,-1.6121)" width="10" x="1058.83705746709" y="238.6629272741209"/></g>
  <g id="32">
   <use class="kv35" height="20" transform="rotate(90,1065.07,317.647) scale(1.24619,1.0068) translate(-209.175,-2.07761)" width="10" x="1058.837059210343" xlink:href="#GroundDisconnector:地刀_0" y="307.5793467349159" zvalue="10079"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453756846082" ObjectName="35kV邦河线35317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453756846082"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1065.07,317.647) scale(1.24619,1.0068) translate(-209.175,-2.07761)" width="10" x="1058.837059210343" y="307.5793467349159"/></g>
  <g id="39">
   <use class="kv35" height="20" transform="rotate(270,1237.07,182.731) scale(-1.24619,-1.0068) translate(-2228.52,-364.16)" width="10" x="1230.837072725879" xlink:href="#GroundDisconnector:地刀_0" y="172.6629272741209" zvalue="10088"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453757042690" ObjectName="35kV马茂电站线35467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453757042690"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1237.07,182.731) scale(-1.24619,-1.0068) translate(-2228.52,-364.16)" width="10" x="1230.837072725879" y="172.6629272741209"/></g>
  <g id="14">
   <use class="kv35" height="20" transform="rotate(90,1426.37,421.448) scale(1.24619,-1.0068) translate(-280.552,-839.981)" width="10" x="1420.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="411.3800877847166" zvalue="10110"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453757632514" ObjectName="#2主变35kV侧30217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453757632514"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1426.37,421.448) scale(1.24619,-1.0068) translate(-280.552,-839.981)" width="10" x="1420.143242399862" y="411.3800877847166"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="151">
   <path class="kv35" d="M 1001.93 414.53 L 1001.93 371.59" stroke-width="1" zvalue="8031"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@0" LinkObjectIDznd="48@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1001.93 414.53 L 1001.93 371.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv35" d="M 1018.56 398.51 L 1001.93 398.51" stroke-width="1" zvalue="8032"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="151" MaxPinNum="2"/>
   </metadata>
  <path d="M 1018.56 398.51 L 1001.93 398.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv35" d="M 1001.95 434.16 L 1001.95 469.33" stroke-width="1" zvalue="8038"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@1" LinkObjectIDznd="218@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1001.95 434.16 L 1001.95 469.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv35" d="M 1019.56 450.51 L 1001.95 450.51" stroke-width="1" zvalue="8039"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 1019.56 450.51 L 1001.95 450.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv35" d="M 722.15 271.56 L 722.15 228.98" stroke-width="1" zvalue="8814"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="68@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 722.15 271.56 L 722.15 228.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv35" d="M 722.13 209.35 L 722.13 136" stroke-width="1" zvalue="8817"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="70@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 722.13 209.35 L 722.13 136" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv35" d="M 722.31 297.45 L 722.31 332.62" stroke-width="1" zvalue="8825"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@1" LinkObjectIDznd="65@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 722.31 297.45 L 722.31 332.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="813">
   <path class="kv35" d="M 1397.46 392.98 L 1397.46 371.59" stroke-width="1" zvalue="9726"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="815@1" LinkObjectIDznd="48@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1397.46 392.98 L 1397.46 371.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="812">
   <path class="kv35" d="M 1397.48 434.78 L 1397.48 412.61" stroke-width="1" zvalue="9727"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="816@1" LinkObjectIDznd="815@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1397.48 434.78 L 1397.48 412.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="809">
   <path class="kv35" d="M 1397.64 460.66 L 1397.69 486.64" stroke-width="1" zvalue="9730"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="816@0" LinkObjectIDznd="822@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1397.64 460.66 L 1397.69 486.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="633">
   <path class="kv10" d="M 1397.82 610 L 1397.82 636.42" stroke-width="1" zvalue="9736"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="811@0" LinkObjectIDznd="636@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1397.82 610 L 1397.82 636.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="632">
   <path class="kv10" d="M 1398.37 656.05 L 1398.37 690.59" stroke-width="1" zvalue="9737"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="636@0" LinkObjectIDznd="166@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1398.37 656.05 L 1398.37 690.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="865">
   <path class="kv10" d="M 699.64 663.8 L 699.64 690.59" stroke-width="1" zvalue="9765"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="864@1" LinkObjectIDznd="166@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 699.64 663.8 L 699.64 690.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="857">
   <path class="kv10" d="M 699.61 644.17 L 699.64 624.83" stroke-width="1" zvalue="9769"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="864@0" LinkObjectIDznd="855@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 699.61 644.17 L 699.64 624.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv10" d="M 1023.37 837.13 L 1023.29 881.38" stroke-width="1" zvalue="9864"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="113@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023.37 837.13 L 1023.29 881.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv10" d="M 1052.03 873.25 L 1052.03 860.55 L 1023.33 860.55" stroke-width="1" zvalue="9866"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="112" MaxPinNum="2"/>
   </metadata>
  <path d="M 1052.03 873.25 L 1052.03 860.55 L 1023.33 860.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv10" d="M 1023.34 791.82 L 1023.34 817.5" stroke-width="1" zvalue="9867"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="116@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023.34 791.82 L 1023.34 817.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv10" d="M 1023.37 745.13 L 1023.37 765.94" stroke-width="1" zvalue="9870"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="115@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023.37 745.13 L 1023.37 765.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv10" d="M 1023.34 725.5 L 1023.34 690.59" stroke-width="1" zvalue="9871"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@1" LinkObjectIDznd="166@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023.34 725.5 L 1023.34 690.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv10" d="M 1223.37 837.13 L 1223.29 881.38" stroke-width="1" zvalue="9879"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@0" LinkObjectIDznd="129@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1223.37 837.13 L 1223.29 881.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv10" d="M 1252.03 873.25 L 1252.03 860.55 L 1223.33 860.55" stroke-width="1" zvalue="9881"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="128" MaxPinNum="2"/>
   </metadata>
  <path d="M 1252.03 873.25 L 1252.03 860.55 L 1223.33 860.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv10" d="M 1223.34 791.82 L 1223.34 817.5" stroke-width="1" zvalue="9882"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="133@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1223.34 791.82 L 1223.34 817.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv10" d="M 1223.37 745.13 L 1223.37 765.94" stroke-width="1" zvalue="9885"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="132@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1223.37 745.13 L 1223.37 765.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv10" d="M 1223.34 725.5 L 1223.34 690.59" stroke-width="1" zvalue="9886"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@1" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1223.34 725.5 L 1223.34 690.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv10" d="M 1423.37 837.13 L 1423.29 881.38" stroke-width="1" zvalue="9894"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="155@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1423.37 837.13 L 1423.29 881.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv10" d="M 1452.03 873.25 L 1452.03 860.55 L 1423.33 860.55" stroke-width="1" zvalue="9896"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="152" MaxPinNum="2"/>
   </metadata>
  <path d="M 1452.03 873.25 L 1452.03 860.55 L 1423.33 860.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv10" d="M 1423.34 791.82 L 1423.34 817.5" stroke-width="1" zvalue="9897"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="159@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1423.34 791.82 L 1423.34 817.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv10" d="M 1423.37 745.13 L 1423.37 765.94" stroke-width="1" zvalue="9900"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="158@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1423.37 745.13 L 1423.37 765.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="kv10" d="M 1423.34 725.5 L 1423.34 690.59" stroke-width="1" zvalue="9901"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@1" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1423.34 725.5 L 1423.34 690.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="kv35" d="M 1186.17 371.59 L 1186.17 392.25" stroke-width="1" zvalue="9923"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@4" LinkObjectIDznd="2@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1186.17 371.59 L 1186.17 392.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv35" d="M 1186 417.75 L 1186 444.5" stroke-width="1" zvalue="9924"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2@0" LinkObjectIDznd="234@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1186 417.75 L 1186 444.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv35" d="M 902.33 352.25 L 902.33 371.59" stroke-width="1" zvalue="9926"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90" LinkObjectIDznd="48@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 902.33 352.25 L 902.33 371.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv35" d="M 722.33 352.25 L 722.33 371.59" stroke-width="1" zvalue="9929"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@1" LinkObjectIDznd="48@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 722.33 352.25 L 722.33 371.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv35" d="M 902.15 271.56 L 902.15 228.98" stroke-width="1" zvalue="9973"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@0" LinkObjectIDznd="99@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 902.15 271.56 L 902.15 228.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv35" d="M 902.13 209.35 L 902.13 137.11" stroke-width="1" zvalue="9976"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="101@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 902.13 209.35 L 902.13 137.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv35" d="M 902.31 297.45 L 902.31 332.62" stroke-width="1" zvalue="9978"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@1" LinkObjectIDznd="96@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 902.31 297.45 L 902.31 332.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv35" d="M 902.33 352.25 L 902.33 371.59" stroke-width="1" zvalue="9981"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@1" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 902.33 352.25 L 902.33 371.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv35" d="M 1094.15 271.56 L 1094.15 228.98" stroke-width="1" zvalue="9992"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="195@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1094.15 271.56 L 1094.15 228.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv35" d="M 1094.13 209.35 L 1094.13 137.11" stroke-width="1" zvalue="9995"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@0" LinkObjectIDznd="198@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1094.13 209.35 L 1094.13 137.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv35" d="M 1094.31 297.45 L 1094.31 332.62" stroke-width="1" zvalue="9997"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@1" LinkObjectIDznd="192@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1094.31 297.45 L 1094.31 332.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="kv35" d="M 1274.15 271.56 L 1274.15 228.98" stroke-width="1" zvalue="10010"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@0" LinkObjectIDznd="230@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1274.15 271.56 L 1274.15 228.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="220">
   <path class="kv35" d="M 1274.13 209.35 L 1274.13 136.11" stroke-width="1" zvalue="10013"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="232@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1274.13 209.35 L 1274.13 136.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="kv35" d="M 1274.31 297.45 L 1274.31 332.62" stroke-width="1" zvalue="10015"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@1" LinkObjectIDznd="227@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1274.31 297.45 L 1274.31 332.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="kv35" d="M 1274.33 352.25 L 1274.33 371.59" stroke-width="1" zvalue="10018"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="227@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1274.33 352.25 L 1274.33 371.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="238">
   <path class="kv35" d="M 1094.33 352.25 L 1094.33 371.59" stroke-width="1" zvalue="10021"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@1" LinkObjectIDznd="48@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1094.33 352.25 L 1094.33 371.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv10" d="M 809.53 837.04 L 809.45 881.28" stroke-width="1" zvalue="10044"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="57@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.53 837.04 L 809.45 881.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv10" d="M 838.19 873.16 L 838.19 860.46 L 809.49 860.46" stroke-width="1" zvalue="10046"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="56" MaxPinNum="2"/>
   </metadata>
  <path d="M 838.19 873.16 L 838.19 860.46 L 809.49 860.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv10" d="M 809.51 791.73 L 809.51 817.4" stroke-width="1" zvalue="10047"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="61@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.51 791.73 L 809.51 817.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv10" d="M 809.53 745.04 L 809.53 765.84" stroke-width="1" zvalue="10050"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="60@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.53 745.04 L 809.53 765.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv10" d="M 809.51 725.4 L 809.51 690.59" stroke-width="1" zvalue="10051"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@1" LinkObjectIDznd="166@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.51 725.4 L 809.51 690.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv35" d="M 697.82 183.88 L 722.13 183.88" stroke-width="1" zvalue="10056"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="64" MaxPinNum="2"/>
   </metadata>
  <path d="M 697.82 183.88 L 722.13 183.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv35" d="M 736.34 147.31 L 722.13 147.31" stroke-width="1" zvalue="10057"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="64" MaxPinNum="2"/>
   </metadata>
  <path d="M 736.34 147.31 L 722.13 147.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="kv35" d="M 698.82 249.88 L 722.15 249.88" stroke-width="1" zvalue="10058"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="4@0" LinkObjectIDznd="66" MaxPinNum="2"/>
   </metadata>
  <path d="M 698.82 249.88 L 722.15 249.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv35" d="M 698.82 318.79 L 722.31 318.79" stroke-width="1" zvalue="10061"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@0" LinkObjectIDznd="58" MaxPinNum="2"/>
   </metadata>
  <path d="M 698.82 318.79 L 722.31 318.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv35" d="M 879.82 183.88 L 902.13 183.88" stroke-width="1" zvalue="10070"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="16@0" LinkObjectIDznd="95" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.82 183.88 L 902.13 183.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv35" d="M 918.34 147.31 L 902.13 147.31" stroke-width="1" zvalue="10073"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="19@0" LinkObjectIDznd="95" MaxPinNum="2"/>
   </metadata>
  <path d="M 918.34 147.31 L 902.13 147.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv35" d="M 1073.88 182.79 L 1094.13 182.79" stroke-width="1" zvalue="10081"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="30@0" LinkObjectIDznd="185" MaxPinNum="2"/>
   </metadata>
  <path d="M 1073.88 182.79 L 1094.13 182.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv35" d="M 1074.88 248.79 L 1094.15 248.79" stroke-width="1" zvalue="10082"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31@0" LinkObjectIDznd="193" MaxPinNum="2"/>
   </metadata>
  <path d="M 1074.88 248.79 L 1094.15 248.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv35" d="M 1074.88 317.71 L 1094.31 317.71" stroke-width="1" zvalue="10083"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="183" MaxPinNum="2"/>
   </metadata>
  <path d="M 1074.88 317.71 L 1094.31 317.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv35" d="M 1111.34 147.31 L 1094.13 147.31" stroke-width="1" zvalue="10086"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="185" MaxPinNum="2"/>
   </metadata>
  <path d="M 1111.34 147.31 L 1094.13 147.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv35" d="M 1246.88 182.79 L 1274.13 182.79" stroke-width="1" zvalue="10090"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@0" LinkObjectIDznd="220" MaxPinNum="2"/>
   </metadata>
  <path d="M 1246.88 182.79 L 1274.13 182.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv35" d="M 1261.5 162.5 L 1274.13 162.5" stroke-width="1" zvalue="10092"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="220" MaxPinNum="2"/>
   </metadata>
  <path d="M 1261.5 162.5 L 1274.13 162.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv35" d="M 1288.34 147.31 L 1274.13 147.31" stroke-width="1" zvalue="10095"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@0" LinkObjectIDznd="220" MaxPinNum="2"/>
   </metadata>
  <path d="M 1288.34 147.31 L 1274.13 147.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv35" d="M 703.25 161.58 L 722.13 161.58" stroke-width="1" zvalue="10097"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75@0" LinkObjectIDznd="64" MaxPinNum="2"/>
   </metadata>
  <path d="M 703.25 161.58 L 722.13 161.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv35" d="M 671.5 161.5 L 677.75 161.42" stroke-width="1" zvalue="10100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@0" LinkObjectIDznd="75@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 671.5 161.5 L 677.75 161.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv35" d="M 1076.25 161.58 L 1094.13 161.58" stroke-width="1" zvalue="10104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="185" MaxPinNum="2"/>
   </metadata>
  <path d="M 1076.25 161.58 L 1094.13 161.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv35" d="M 1050.5 161.5 L 1050.75 161.42" stroke-width="1" zvalue="10105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="80@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1050.5 161.5 L 1050.75 161.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv35" d="M 882.5 160.5 L 902.13 160.5" stroke-width="1" zvalue="10108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@0" LinkObjectIDznd="95" MaxPinNum="2"/>
   </metadata>
  <path d="M 882.5 160.5 L 902.13 160.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv35" d="M 1416.56 421.51 L 1397.48 421.51" stroke-width="1" zvalue="10111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="14@0" LinkObjectIDznd="812" MaxPinNum="2"/>
   </metadata>
  <path d="M 1416.56 421.51 L 1397.48 421.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="301">
   <path class="kv10" d="M 1397.66 555.16 L 1397.66 584.11" stroke-width="1" zvalue="10486"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="822@1" LinkObjectIDznd="811@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1397.66 555.16 L 1397.66 584.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="70">
   <use class="kv35" height="30" transform="rotate(0,722.128,128.233) scale(1.98323,0.522926) translate(-354.569,109.833)" width="7" x="715.1867041796982" xlink:href="#ACLineSegment:线路_0" y="120.3888788272827" zvalue="8806"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249330286595" ObjectName="35kV西东线"/>
   <cge:TPSR_Ref TObjectID="8444249330286595_5066549678768129"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,722.128,128.233) scale(1.98323,0.522926) translate(-354.569,109.833)" width="7" x="715.1867041796982" y="120.3888788272827"/></g>
  <g id="101">
   <use class="kv35" height="30" transform="rotate(0,902.128,129.344) scale(1.98323,0.522926) translate(-443.808,110.846)" width="7" x="895.1867041796982" xlink:href="#ACLineSegment:线路_0" y="121.4999899383938" zvalue="9965"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249320914948" ObjectName="35kV河平线"/>
   <cge:TPSR_Ref TObjectID="8444249320914948_5066549678768129"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,902.128,129.344) scale(1.98323,0.522926) translate(-443.808,110.846)" width="7" x="895.1867041796982" y="121.4999899383938"/></g>
 </g>
 <g id="BreakerClass">
  <g id="69">
   <use class="kv35" height="20" transform="rotate(0,722.205,284.52) scale(1.5542,1.35421) translate(-254.754,-70.8776)" width="10" x="714.4340345345652" xlink:href="#Breaker:开关_0" y="270.9779758524579" zvalue="8807"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925088706564" ObjectName="35kV西东线351断路器"/>
   <cge:TPSR_Ref TObjectID="6473925088706564"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,722.205,284.52) scale(1.5542,1.35421) translate(-254.754,-70.8776)" width="10" x="714.4340345345652" y="270.9779758524579"/></g>
  <g id="816">
   <use class="kv35" height="20" transform="rotate(180,1397.59,447.709) scale(1.5542,1.35421) translate(-495.583,-113.562)" width="10" x="1389.816710722901" xlink:href="#Breaker:开关_0" y="434.1666962122334" zvalue="9722"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925088837636" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925088837636"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1397.59,447.709) scale(1.5542,1.35421) translate(-495.583,-113.562)" width="10" x="1389.816710722901" y="434.1666962122334"/></g>
  <g id="811">
   <use class="kv10" height="20" transform="rotate(180,1397.77,597.042) scale(1.5542,1.35421) translate(-495.648,-152.622)" width="10" x="1389.997344267822" xlink:href="#Breaker:开关_0" y="583.5" zvalue="9728"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925088772100" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473925088772100"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1397.77,597.042) scale(1.5542,1.35421) translate(-495.648,-152.622)" width="10" x="1389.997344267822" y="583.5"/></g>
  <g id="115">
   <use class="kv10" height="20" transform="rotate(180,1023.29,778.868) scale(1.5542,1.35421) translate(-362.117,-200.18)" width="10" x="1015.521691190635" xlink:href="#Breaker:开关_0" y="765.3258941321849" zvalue="9860"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925088903172" ObjectName="10kV河南线052断路器"/>
   <cge:TPSR_Ref TObjectID="6473925088903172"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1023.29,778.868) scale(1.5542,1.35421) translate(-362.117,-200.18)" width="10" x="1015.521691190635" y="765.3258941321849"/></g>
  <g id="132">
   <use class="kv10" height="20" transform="rotate(180,1223.29,778.868) scale(1.5542,1.35421) translate(-433.433,-200.18)" width="10" x="1215.521691190635" xlink:href="#Breaker:开关_0" y="765.3258941321849" zvalue="9875"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925088968708" ObjectName="10kV河木线053断路器"/>
   <cge:TPSR_Ref TObjectID="6473925088968708"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1223.29,778.868) scale(1.5542,1.35421) translate(-433.433,-200.18)" width="10" x="1215.521691190635" y="765.3258941321849"/></g>
  <g id="158">
   <use class="kv10" height="20" transform="rotate(180,1423.29,778.868) scale(1.5542,1.35421) translate(-504.749,-200.18)" width="10" x="1415.521691190635" xlink:href="#Breaker:开关_0" y="765.3258941321849" zvalue="9890"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925089034244" ObjectName="10kV备用线054断路器"/>
   <cge:TPSR_Ref TObjectID="6473925089034244"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1423.29,778.868) scale(1.5542,1.35421) translate(-504.749,-200.18)" width="10" x="1415.521691190635" y="765.3258941321849"/></g>
  <g id="100">
   <use class="kv35" height="20" transform="rotate(0,902.205,284.52) scale(1.5542,1.35421) translate(-318.939,-70.8776)" width="10" x="894.4340345345652" xlink:href="#Breaker:开关_0" y="270.9779758524579" zvalue="9966"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925089099780" ObjectName="35kV河平线352断路器"/>
   <cge:TPSR_Ref TObjectID="6473925089099780"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,902.205,284.52) scale(1.5542,1.35421) translate(-318.939,-70.8776)" width="10" x="894.4340345345652" y="270.9779758524579"/></g>
  <g id="196">
   <use class="kv35" height="20" transform="rotate(0,1094.21,284.52) scale(1.5542,1.35421) translate(-387.403,-70.8776)" width="10" x="1086.434034534565" xlink:href="#Breaker:开关_0" y="270.9779758524579" zvalue="9985"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925089165316" ObjectName="35kV邦河线353断路器"/>
   <cge:TPSR_Ref TObjectID="6473925089165316"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1094.21,284.52) scale(1.5542,1.35421) translate(-387.403,-70.8776)" width="10" x="1086.434034534565" y="270.9779758524579"/></g>
  <g id="231">
   <use class="kv35" height="20" transform="rotate(0,1274.21,284.52) scale(1.5542,1.35421) translate(-451.587,-70.8776)" width="10" x="1266.434034534565" xlink:href="#Breaker:开关_0" y="270.9779758524579" zvalue="10003"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925089230852" ObjectName="35kV马茂电站线354断路器"/>
   <cge:TPSR_Ref TObjectID="6473925089230852"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1274.21,284.52) scale(1.5542,1.35421) translate(-451.587,-70.8776)" width="10" x="1266.434034534565" y="270.9779758524579"/></g>
  <g id="60">
   <use class="kv10" height="20" transform="rotate(180,809.455,778.777) scale(1.5542,1.35421) translate(-285.866,-200.157)" width="10" x="801.6844504704346" xlink:href="#Breaker:开关_0" y="765.2349850412758" zvalue="10040"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925089296388" ObjectName="10kV马河线051断路器"/>
   <cge:TPSR_Ref TObjectID="6473925089296388"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,809.455,778.777) scale(1.5542,1.35421) translate(-285.866,-200.157)" width="10" x="801.6844504704346" y="765.2349850412758"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="171">
   <use class="kv35" height="26" transform="rotate(270,747.806,147.278) scale(-0.838049,0.927421) translate(-1641.1,10.5823)" width="12" x="742.7781809263649" xlink:href="#Accessory:避雷器1_0" y="135.2218190736352" zvalue="9700"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453753569282" ObjectName="35kV西东线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,747.806,147.278) scale(-0.838049,0.927421) translate(-1641.1,10.5823)" width="12" x="742.7781809263649" y="135.2218190736352"/></g>
  <g id="218">
   <use class="kv35" height="30" transform="rotate(0,1003.5,486.75) scale(1.25,1.25) translate(-196.95,-93.6)" width="30" x="984.75" xlink:href="#Accessory:避雷器PT带熔断器_0" y="468" zvalue="9702"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453753962498" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1003.5,486.75) scale(1.25,1.25) translate(-196.95,-93.6)" width="30" x="984.75" y="468"/></g>
  <g id="855">
   <use class="kv10" height="30" transform="rotate(0,698.222,607.417) scale(-1.25,-1.25) translate(-1253.05,-1089.6)" width="30" x="679.4724880605656" xlink:href="#Accessory:避雷器PT带熔断器_0" y="588.6666666666666" zvalue="9771"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453754159106" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,698.222,607.417) scale(-1.25,-1.25) translate(-1253.05,-1089.6)" width="30" x="679.4724880605656" y="588.6666666666666"/></g>
  <g id="114">
   <use class="kv10" height="26" transform="rotate(0,1052.06,884.719) scale(-0.838049,0.927421) translate(-2308.39,68.2937)" width="12" x="1047.028180926365" xlink:href="#Accessory:避雷器1_0" y="872.6626333680676" zvalue="9862"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453754421250" ObjectName="10kV河南线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1052.06,884.719) scale(-0.838049,0.927421) translate(-2308.39,68.2937)" width="12" x="1047.028180926365" y="872.6626333680676"/></g>
  <g id="131">
   <use class="kv10" height="26" transform="rotate(0,1252.06,884.719) scale(-0.838049,0.927421) translate(-2747.04,68.2937)" width="12" x="1247.028180926365" xlink:href="#Accessory:避雷器1_0" y="872.6626333680676" zvalue="9877"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453754683394" ObjectName="10kV河木线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1252.06,884.719) scale(-0.838049,0.927421) translate(-2747.04,68.2937)" width="12" x="1247.028180926365" y="872.6626333680676"/></g>
  <g id="157">
   <use class="kv10" height="26" transform="rotate(0,1452.06,884.719) scale(-0.838049,0.927421) translate(-3185.69,68.2937)" width="12" x="1447.028180926365" xlink:href="#Accessory:避雷器1_0" y="872.6626333680676" zvalue="9892"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453754945538" ObjectName="10kV备用线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1452.06,884.719) scale(-0.838049,0.927421) translate(-3185.69,68.2937)" width="12" x="1447.028180926365" y="872.6626333680676"/></g>
  <g id="59">
   <use class="kv10" height="26" transform="rotate(0,838.219,884.628) scale(-0.838049,0.927421) translate(-1839.39,68.2866)" width="12" x="833.1909402061649" xlink:href="#Accessory:避雷器1_0" y="872.5717242771584" zvalue="10042"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453755928578" ObjectName="10kV马河线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,838.219,884.628) scale(-0.838049,0.927421) translate(-1839.39,68.2866)" width="12" x="833.1909402061649" y="872.5717242771584"/></g>
  <g id="19">
   <use class="kv35" height="26" transform="rotate(270,929.806,147.278) scale(-0.838049,0.927421) translate(-2040.27,10.5823)" width="12" x="924.7781809263649" xlink:href="#Accessory:避雷器1_0" y="135.2218190736352" zvalue="10065"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453756452866" ObjectName="35kV河平线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,929.806,147.278) scale(-0.838049,0.927421) translate(-2040.27,10.5823)" width="12" x="924.7781809263649" y="135.2218190736352"/></g>
  <g id="36">
   <use class="kv35" height="26" transform="rotate(270,1122.81,147.278) scale(-0.838049,0.927421) translate(-2463.56,10.5823)" width="12" x="1117.778180926365" xlink:href="#Accessory:避雷器1_0" y="135.2218190736352" zvalue="10085"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453756911618" ObjectName="35kV邦河线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1122.81,147.278) scale(-0.838049,0.927421) translate(-2463.56,10.5823)" width="12" x="1117.778180926365" y="135.2218190736352"/></g>
  <g id="71">
   <use class="kv35" height="40" transform="rotate(270,1243,162.5) scale(1,1) translate(0,0)" width="30" x="1228" xlink:href="#Accessory:带熔断器的线路PT1_0" y="142.5" zvalue="10091"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453757108226" ObjectName="35kV马茂电站线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,1243,162.5) scale(1,1) translate(0,0)" width="30" x="1228" y="142.5"/></g>
  <g id="73">
   <use class="kv35" height="26" transform="rotate(270,1299.81,147.278) scale(-0.838049,0.927421) translate(-2851.77,10.5823)" width="12" x="1294.778180926365" xlink:href="#Accessory:避雷器1_0" y="135.2218190736352" zvalue="10094"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453757173762" ObjectName="35kV马茂电站线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1299.81,147.278) scale(-0.838049,0.927421) translate(-2851.77,10.5823)" width="12" x="1294.778180926365" y="135.2218190736352"/></g>
  <g id="78">
   <use class="kv35" height="40" transform="rotate(270,653,161.5) scale(1,1) translate(0,0)" width="30" x="638" xlink:href="#Accessory:带熔断器的线路PT1_0" y="141.5" zvalue="10099"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453757304834" ObjectName="35kV沙河线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,653,161.5) scale(1,1) translate(0,0)" width="30" x="638" y="141.5"/></g>
  <g id="86">
   <use class="kv35" height="40" transform="rotate(270,1032,161.5) scale(1,1) translate(0,0)" width="30" x="1017" xlink:href="#Accessory:带熔断器的线路PT1_0" y="141.5" zvalue="10103"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453757435906" ObjectName="35kV邦河线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,1032,161.5) scale(1,1) translate(0,0)" width="30" x="1017" y="141.5"/></g>
  <g id="130">
   <use class="kv35" height="40" transform="rotate(270,864,160.5) scale(1,1) translate(0,0)" width="30" x="849" xlink:href="#Accessory:带熔断器的线路PT1_0" y="140.5" zvalue="10107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453757501442" ObjectName="35kV河平线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,864,160.5) scale(1,1) translate(0,0)" width="30" x="849" y="140.5"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="822">
   <g id="8220">
    <use class="kv35" height="30" transform="rotate(0,1397.66,520.75) scale(2.53125,2.45) translate(-827.126,-286.449)" width="24" x="1367.29" xlink:href="#PowerTransformer2:可调不带中性点_0" y="484" zvalue="9739"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874564239362" ObjectName="35"/>
    </metadata>
   </g>
   <g id="8221">
    <use class="kv10" height="30" transform="rotate(0,1397.66,520.75) scale(2.53125,2.45) translate(-827.126,-286.449)" width="24" x="1367.29" xlink:href="#PowerTransformer2:可调不带中性点_1" y="484" zvalue="9739"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874564304898" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399523303426" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399523303426"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1397.66,520.75) scale(2.53125,2.45) translate(-827.126,-286.449)" width="24" x="1367.29" y="484"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="113">
   <use class="kv10" height="30" transform="rotate(0,1023.29,898.25) scale(1.25,-1.25) translate(-203.157,-1613.1)" width="12" x="1015.786529541016" xlink:href="#EnergyConsumer:负荷_0" y="879.5" zvalue="9863"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453754355714" ObjectName="10kV河南线"/>
   <cge:TPSR_Ref TObjectID="6192453754355714"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1023.29,898.25) scale(1.25,-1.25) translate(-203.157,-1613.1)" width="12" x="1015.786529541016" y="879.5"/></g>
  <g id="129">
   <use class="kv10" height="30" transform="rotate(0,1223.29,898.25) scale(1.25,-1.25) translate(-243.157,-1613.1)" width="12" x="1215.786529541016" xlink:href="#EnergyConsumer:负荷_0" y="879.5" zvalue="9878"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453754617858" ObjectName="10kV河木线"/>
   <cge:TPSR_Ref TObjectID="6192453754617858"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1223.29,898.25) scale(1.25,-1.25) translate(-243.157,-1613.1)" width="12" x="1215.786529541016" y="879.5"/></g>
  <g id="155">
   <use class="kv10" height="30" transform="rotate(0,1423.29,898.25) scale(1.25,-1.25) translate(-283.157,-1613.1)" width="12" x="1415.786529541016" xlink:href="#EnergyConsumer:负荷_0" y="879.5" zvalue="9893"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453754880002" ObjectName="10kV备用线"/>
   <cge:TPSR_Ref TObjectID="6192453754880002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1423.29,898.25) scale(1.25,-1.25) translate(-283.157,-1613.1)" width="12" x="1415.786529541016" y="879.5"/></g>
  <g id="234">
   <use class="kv35" height="35" transform="rotate(0,1186,458.5) scale(1,1) translate(0,0)" width="32" x="1170" xlink:href="#EnergyConsumer:站用变3_0" y="441" zvalue="10019"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453755731971" ObjectName="35kV站用变"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1186,458.5) scale(1,1) translate(0,0)" width="32" x="1170" y="441"/></g>
  <g id="57">
   <use class="kv10" height="30" transform="rotate(0,809.449,898.159) scale(1.25,-1.25) translate(-160.39,-1612.94)" width="12" x="801.9492888208156" xlink:href="#EnergyConsumer:负荷_0" y="879.4090909090909" zvalue="10043"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453755863042" ObjectName="10kV马河线"/>
   <cge:TPSR_Ref TObjectID="6192453755863042"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,809.449,898.159) scale(1.25,-1.25) translate(-160.39,-1612.94)" width="12" x="801.9492888208156" y="879.4090909090909"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="18" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,722.128,41.8889) scale(1,1) translate(-1.50353e-13,0)" writing-mode="lr" x="722.3200000000001" xml:space="preserve" y="46.8" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132897992707" ObjectName="P"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="21" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,902.128,43) scale(1,1) translate(0,0)" writing-mode="lr" x="902.3200000000001" xml:space="preserve" y="47.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132906905603" ObjectName="P"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="22" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1094.13,43) scale(1,1) translate(0,0)" writing-mode="lr" x="1094.32" xml:space="preserve" y="47.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132908412931" ObjectName="P"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="55" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1274.13,42) scale(1,1) translate(0,0)" writing-mode="lr" x="1274.32" xml:space="preserve" y="46.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132909920259" ObjectName="P"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="63" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,722.128,64.8889) scale(1,1) translate(-1.50353e-13,0)" writing-mode="lr" x="722.3200000000001" xml:space="preserve" y="69.8" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132898058243" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="82">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="82" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,902.128,66) scale(1,1) translate(0,0)" writing-mode="lr" x="902.3200000000001" xml:space="preserve" y="70.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132906971140" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="91" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1094.13,66) scale(1,1) translate(0,0)" writing-mode="lr" x="1094.32" xml:space="preserve" y="70.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132908478467" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="92" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1274.13,65) scale(1,1) translate(0,0)" writing-mode="lr" x="1274.32" xml:space="preserve" y="69.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132909985795" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="94" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,722.128,87.8889) scale(1,1) translate(-1.50353e-13,0)" writing-mode="lr" x="722.3200000000001" xml:space="preserve" y="92.8" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132898123779" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="98" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,902.128,89) scale(1,1) translate(0,0)" writing-mode="lr" x="902.3200000000001" xml:space="preserve" y="93.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132907036676" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="103" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1094.13,89) scale(1,1) translate(0,0)" writing-mode="lr" x="1094.32" xml:space="preserve" y="93.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132908544003" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="139">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="139" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1274.13,88) scale(1,1) translate(0,0)" writing-mode="lr" x="1274.32" xml:space="preserve" y="92.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132910051331" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="167">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="167" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1023.29,955) scale(1,1) translate(0,0)" writing-mode="lr" x="1023.48" xml:space="preserve" y="959.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132902055939" ObjectName="P"/>
   </metadata>
  </g>
  <g id="168">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="168" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1223.29,955) scale(1,1) translate(0,0)" writing-mode="lr" x="1223.48" xml:space="preserve" y="959.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132903497732" ObjectName="P"/>
   </metadata>
  </g>
  <g id="172">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="172" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1423.29,955) scale(1,1) translate(0,0)" writing-mode="lr" x="1423.48" xml:space="preserve" y="959.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132904939524" ObjectName="P"/>
   </metadata>
  </g>
  <g id="173">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="173" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,809.449,954.909) scale(1,1) translate(0,0)" writing-mode="lr" x="809.64" xml:space="preserve" y="959.8200000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132911493123" ObjectName="P"/>
   </metadata>
  </g>
  <g id="175">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="175" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1023.29,978) scale(1,1) translate(0,0)" writing-mode="lr" x="1023.48" xml:space="preserve" y="982.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132902121475" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="176">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="176" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1223.29,978) scale(1,1) translate(0,0)" writing-mode="lr" x="1223.48" xml:space="preserve" y="982.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132903563267" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="177">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="177" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1423.29,978) scale(1,1) translate(0,0)" writing-mode="lr" x="1423.48" xml:space="preserve" y="982.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132905005059" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="178">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="178" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,809.449,977.909) scale(1,1) translate(0,0)" writing-mode="lr" x="809.64" xml:space="preserve" y="982.8200000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132911558659" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="181">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="181" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1023.29,1001) scale(1,1) translate(0,0)" writing-mode="lr" x="1023.48" xml:space="preserve" y="1005.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132902187011" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="182">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="182" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1223.29,1001) scale(1,1) translate(0,0)" writing-mode="lr" x="1223.48" xml:space="preserve" y="1005.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132903628803" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="184">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="184" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1423.29,1001) scale(1,1) translate(0,0)" writing-mode="lr" x="1423.48" xml:space="preserve" y="1005.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132905070595" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="189">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="189" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,809.449,1000.91) scale(1,1) translate(0,0)" writing-mode="lr" x="809.64" xml:space="preserve" y="1005.82" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132911624195" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="216">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="216" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1494.66,403.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1494.91" xml:space="preserve" y="409.99" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132900024324" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="217">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="217" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1494.66,430.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1494.91" xml:space="preserve" y="436.99" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132900089860" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="221">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="221" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1287.66,578) scale(1,1) translate(0,0)" writing-mode="lr" x="1287.91" xml:space="preserve" y="584.49" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132900155396" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="222">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="222" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1287.66,605) scale(1,1) translate(0,0)" writing-mode="lr" x="1287.91" xml:space="preserve" y="611.49" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132900220932" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="223">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="223" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1494.66,457.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1494.91" xml:space="preserve" y="463.99" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132900286468" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="226">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="226" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1287.66,632) scale(1,1) translate(0,0)" writing-mode="lr" x="1287.91" xml:space="preserve" y="638.49" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132900614148" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="242">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="242" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,125.071,528.093) scale(1,1) translate(0,0)" writing-mode="lr" x="125.2" xml:space="preserve" y="533" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132896419843" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="241">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="241" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,125.071,553.218) scale(1,1) translate(0,-1.20508e-13)" writing-mode="lr" x="125.2" xml:space="preserve" y="558.13" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132896485379" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,125.071,578.343) scale(1,1) translate(0,-2.52173e-13)" writing-mode="lr" x="125.2" xml:space="preserve" y="583.25" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132896550915" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="239">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="239" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,126.5,502.968) scale(1,1) translate(0,0)" writing-mode="lr" x="126.63" xml:space="preserve" y="507.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132896681987" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="237">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="237" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,127.571,210.468) scale(1,1) translate(0,0)" writing-mode="lr" x="127.72" xml:space="preserve" y="216.9" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132896813059" ObjectName="F"/>
   </metadata>
  </g>
  <g id="235">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="235" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,242.905,526.982) scale(1,1) translate(0,0)" writing-mode="lr" x="243.03" xml:space="preserve" y="531.89" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132896944131" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="229">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="229" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,242.905,552.107) scale(1,1) translate(0,0)" writing-mode="lr" x="243.03" xml:space="preserve" y="557.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132897009667" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="188">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,242.905,577.232) scale(1,1) translate(0,0)" writing-mode="lr" x="243.03" xml:space="preserve" y="582.14" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132897075203" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="187">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="187" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,242.905,501.857) scale(1,1) translate(0,0)" writing-mode="lr" x="243.03" xml:space="preserve" y="506.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132897206276" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="165">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="165" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,127.571,235.357) scale(1,1) translate(0,0)" writing-mode="lr" x="127.72" xml:space="preserve" y="241.79" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132897337348" ObjectName="F"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="148" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,124.321,603.468) scale(1,1) translate(0,0)" writing-mode="lr" x="124.45" xml:space="preserve" y="608.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132896878595" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="140" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,242.613,602.357) scale(1,1) translate(0,0)" writing-mode="lr" x="242.74" xml:space="preserve" y="607.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132897402884" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="122">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="122" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,127.571,186.5) scale(1,1) translate(0,0)" writing-mode="lr" x="127.72" xml:space="preserve" y="192.93" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132914900996" ObjectName=""/>
   </metadata>
  </g>
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="1" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,304.571,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="304.72" xml:space="preserve" y="192.79" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132914966532" ObjectName=""/>
   </metadata>
  </g>
  <g id="278">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="278" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,308.571,257.552) scale(1,1) translate(-5.68663e-14,0)" writing-mode="lr" x="308.8" xml:space="preserve" y="264.04" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132900483075" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="275">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="275" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,308.571,280.552) scale(1,1) translate(0,0)" writing-mode="lr" x="308.77" xml:space="preserve" y="287.04" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132900548612" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="302">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="302" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1005.5,544) scale(1,1) translate(0,0)" writing-mode="lr" x="1005.63" xml:space="preserve" y="548.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132896681987" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="303">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="303" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,699.5,546) scale(1,1) translate(0,0)" writing-mode="lr" x="699.63" xml:space="preserve" y="550.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132897206276" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="StateClass">
  <g id="160">
   <use height="30" transform="rotate(0,334.196,327.857) scale(0.708333,0.665547) translate(133.235,159.739)" width="30" x="323.57" xlink:href="#State:红绿圆(方形)_0" y="317.87" zvalue="10428"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374921629697" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.196,327.857) scale(0.708333,0.665547) translate(133.235,159.739)" width="30" x="323.57" y="317.87"/></g>
  <g id="156">
   <use height="30" transform="rotate(0,238.571,327.857) scale(0.708333,0.665547) translate(93.8603,159.739)" width="30" x="227.95" xlink:href="#State:红绿圆(方形)_0" y="317.87" zvalue="10429"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562958561050628" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,238.571,327.857) scale(0.708333,0.665547) translate(93.8603,159.739)" width="30" x="227.95" y="317.87"/></g>
  <g id="1056">
   <use height="30" transform="rotate(0,300.235,408.5) scale(0.910937,0.8) translate(25.7915,99.125)" width="80" x="263.8" xlink:href="#State:间隔模板_0" y="396.5" zvalue="10500"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629499716861955" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,300.235,408.5) scale(0.910937,0.8) translate(25.7915,99.125)" width="80" x="263.8" y="396.5"/></g>
  <g id="212">
   <use height="30" transform="rotate(0,96.2857,327.039) scale(0.944125,0.866667) translate(3.46338,48.3137)" width="80" x="58.52" xlink:href="#State:间隔模板_0" y="314.04" zvalue="10502"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629500333490178" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,96.2857,327.039) scale(0.944125,0.866667) translate(3.46338,48.3137)" width="80" x="58.52" y="314.04"/></g>
 </g>
</svg>