<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1200" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
 </defs>
 <g id="HeadClass">
  <rect FacName="学习站" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="34.81" xlink:href="logo.png" y="103.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="219" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,159.653,144.714) scale(1,1) translate(-7.72864e-15,0)" writing-mode="lr" x="159.65" xml:space="preserve" y="148.21" zvalue="403"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,188.5,144.69) scale(1,1) translate(0,0)" writing-mode="lr" x="188.5" xml:space="preserve" y="153.69" zvalue="404">学习站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="181" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,303.438,467.25) scale(1,1) translate(0,0)" width="72.88" x="267" y="455.25" zvalue="441"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,303.438,467.25) scale(1,1) translate(0,0)" writing-mode="lr" x="303.44" xml:space="preserve" y="471.75" zvalue="441">小电流接地</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="180" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,93.625,386.25) scale(1,1) translate(0,0)" width="72.88" x="57.19" y="374.25" zvalue="442"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93.625,386.25) scale(1,1) translate(0,0)" writing-mode="lr" x="93.63" xml:space="preserve" y="390.75" zvalue="442">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="179" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,195.531,467.25) scale(1,1) translate(0,0)" width="72.88" x="159.09" y="455.25" zvalue="443"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,195.531,467.25) scale(1,1) translate(0,0)" writing-mode="lr" x="195.53" xml:space="preserve" y="471.75" zvalue="443">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="177" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,93.625,467.25) scale(1,1) translate(0,0)" width="72.88" x="57.19" y="455.25" zvalue="444"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93.625,467.25) scale(1,1) translate(0,0)" writing-mode="lr" x="93.63" xml:space="preserve" y="471.75" zvalue="444">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="176" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,93.625,426.75) scale(1,1) translate(0,0)" width="72.88" x="57.19" y="414.75" zvalue="445"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93.625,426.75) scale(1,1) translate(0,0)" writing-mode="lr" x="93.63" xml:space="preserve" y="431.25" zvalue="445">信号一览</text>
  <line fill="none" id="216" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.50000000000045" x2="373" y1="221.8704926140824" y2="221.8704926140824" zvalue="406"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.5" x2="194.5" y1="234" y2="234"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.5" x2="194.5" y1="260" y2="260"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.5" x2="13.5" y1="234" y2="260"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.5" x2="194.5" y1="234" y2="260"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.5" x2="375.5" y1="234" y2="234"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.5" x2="375.5" y1="260" y2="260"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.5" x2="194.5" y1="234" y2="260"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375.5" x2="375.5" y1="234" y2="260"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.5" x2="194.5" y1="260" y2="260"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.5" x2="194.5" y1="284.25" y2="284.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.5" x2="13.5" y1="260" y2="284.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.5" x2="194.5" y1="260" y2="284.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.5" x2="375.5" y1="260" y2="260"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.5" x2="375.5" y1="284.25" y2="284.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.5" x2="194.5" y1="260" y2="284.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375.5" x2="375.5" y1="260" y2="284.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.5" x2="194.5" y1="284.25" y2="284.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.5" x2="194.5" y1="307" y2="307"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.5" x2="13.5" y1="284.25" y2="307"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.5" x2="194.5" y1="284.25" y2="307"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.5" x2="375.5" y1="284.25" y2="284.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.5" x2="375.5" y1="307" y2="307"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.5" x2="194.5" y1="284.25" y2="307"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375.5" x2="375.5" y1="284.25" y2="307"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.5" x2="194.5" y1="307" y2="307"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.5" x2="194.5" y1="329.75" y2="329.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.5" x2="13.5" y1="307" y2="329.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.5" x2="194.5" y1="307" y2="329.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.5" x2="375.5" y1="307" y2="307"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.5" x2="375.5" y1="329.75" y2="329.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.5" x2="194.5" y1="307" y2="329.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375.5" x2="375.5" y1="307" y2="329.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.5" x2="194.5" y1="329.75" y2="329.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.5" x2="194.5" y1="352.5" y2="352.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13.5" x2="13.5" y1="329.75" y2="352.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.5" x2="194.5" y1="329.75" y2="352.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.5" x2="375.5" y1="329.75" y2="329.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.5" x2="375.5" y1="352.5" y2="352.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.5" x2="194.5" y1="329.75" y2="352.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375.5" x2="375.5" y1="329.75" y2="352.5"/>
  <line fill="none" id="214" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.50000000000045" x2="375" y1="688.8704926140824" y2="688.8704926140824" zvalue="408"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="68.5" x2="114.2745" y1="514" y2="514"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="68.5" x2="114.2745" y1="552.2823" y2="552.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="68.5" x2="68.5" y1="514" y2="552.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="114.2745" y1="514" y2="552.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="173.0809" y1="514" y2="514"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="173.0809" y1="552.2823" y2="552.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="114.2745" y1="514" y2="552.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="173.0809" y1="514" y2="552.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="231.8873" y1="514" y2="514"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="231.8873" y1="552.2823" y2="552.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="173.0809" y1="514" y2="552.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8873" x2="231.8873" y1="514" y2="552.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8872" x2="290.6936000000001" y1="514" y2="514"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8872" x2="290.6936000000001" y1="552.2823" y2="552.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8872" x2="231.8872" y1="514" y2="552.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="290.6936000000001" y1="514" y2="552.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="349.5" y1="514" y2="514"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="349.5" y1="552.2823" y2="552.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="290.6936000000001" y1="514" y2="552.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="349.5" x2="349.5" y1="514" y2="552.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="68.5" x2="114.2745" y1="552.2823" y2="552.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="68.5" x2="114.2745" y1="576.9617" y2="576.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="68.5" x2="68.5" y1="552.2823" y2="576.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="114.2745" y1="552.2823" y2="576.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="173.0809" y1="552.2823" y2="552.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="173.0809" y1="576.9617" y2="576.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="114.2745" y1="552.2823" y2="576.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="173.0809" y1="552.2823" y2="576.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="231.8873" y1="552.2823" y2="552.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="231.8873" y1="576.9617" y2="576.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="173.0809" y1="552.2823" y2="576.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8873" x2="231.8873" y1="552.2823" y2="576.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8872" x2="290.6936000000001" y1="552.2823" y2="552.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8872" x2="290.6936000000001" y1="576.9617" y2="576.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8872" x2="231.8872" y1="552.2823" y2="576.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="290.6936000000001" y1="552.2823" y2="576.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="349.5" y1="552.2823" y2="552.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="349.5" y1="576.9617" y2="576.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="290.6936000000001" y1="552.2823" y2="576.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="349.5" x2="349.5" y1="552.2823" y2="576.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="68.5" x2="114.2745" y1="576.9617" y2="576.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="68.5" x2="114.2745" y1="601.6411000000001" y2="601.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="68.5" x2="68.5" y1="576.9617" y2="601.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="114.2745" y1="576.9617" y2="601.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="173.0809" y1="576.9617" y2="576.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="173.0809" y1="601.6411000000001" y2="601.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="114.2745" y1="576.9617" y2="601.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="173.0809" y1="576.9617" y2="601.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="231.8873" y1="576.9617" y2="576.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="231.8873" y1="601.6411000000001" y2="601.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="173.0809" y1="576.9617" y2="601.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8873" x2="231.8873" y1="576.9617" y2="601.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8872" x2="290.6936000000001" y1="576.9617" y2="576.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8872" x2="290.6936000000001" y1="601.6411000000001" y2="601.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8872" x2="231.8872" y1="576.9617" y2="601.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="290.6936000000001" y1="576.9617" y2="601.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="349.5" y1="576.9617" y2="576.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="349.5" y1="601.6411000000001" y2="601.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="290.6936000000001" y1="576.9617" y2="601.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="349.5" x2="349.5" y1="576.9617" y2="601.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="68.5" x2="114.2745" y1="601.6410999999999" y2="601.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="68.5" x2="114.2745" y1="626.3205" y2="626.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="68.5" x2="68.5" y1="601.6410999999999" y2="626.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="114.2745" y1="601.6410999999999" y2="626.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="173.0809" y1="601.6410999999999" y2="601.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="173.0809" y1="626.3205" y2="626.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="114.2745" y1="601.6410999999999" y2="626.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="173.0809" y1="601.6410999999999" y2="626.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="231.8873" y1="601.6410999999999" y2="601.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="231.8873" y1="626.3205" y2="626.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="173.0809" y1="601.6410999999999" y2="626.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8873" x2="231.8873" y1="601.6410999999999" y2="626.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8872" x2="290.6936000000001" y1="601.6410999999999" y2="601.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8872" x2="290.6936000000001" y1="626.3205" y2="626.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8872" x2="231.8872" y1="601.6410999999999" y2="626.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="290.6936000000001" y1="601.6410999999999" y2="626.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="349.5" y1="601.6410999999999" y2="601.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="349.5" y1="626.3205" y2="626.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="290.6936000000001" y1="601.6410999999999" y2="626.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="349.5" x2="349.5" y1="601.6410999999999" y2="626.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="68.5" x2="114.2745" y1="626.3206" y2="626.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="68.5" x2="114.2745" y1="651" y2="651"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="68.5" x2="68.5" y1="626.3206" y2="651"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="114.2745" y1="626.3206" y2="651"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="173.0809" y1="626.3206" y2="626.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="173.0809" y1="651" y2="651"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="114.2745" y1="626.3206" y2="651"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="173.0809" y1="626.3206" y2="651"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="231.8873" y1="626.3206" y2="626.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="231.8873" y1="651" y2="651"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="173.0809" y1="626.3206" y2="651"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8873" x2="231.8873" y1="626.3206" y2="651"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8872" x2="290.6936000000001" y1="626.3206" y2="626.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8872" x2="290.6936000000001" y1="651" y2="651"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8872" x2="231.8872" y1="626.3206" y2="651"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="290.6936000000001" y1="626.3206" y2="651"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="349.5" y1="626.3206" y2="626.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="349.5" y1="651" y2="651"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="290.6936000000001" y1="626.3206" y2="651"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="349.5" x2="349.5" y1="626.3206" y2="651"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="68.5" x2="114.2745" y1="651" y2="651"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="68.5" x2="114.2745" y1="675.6794" y2="675.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="68.5" x2="68.5" y1="651" y2="675.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="114.2745" y1="651" y2="675.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="173.0809" y1="651" y2="651"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="173.0809" y1="675.6794" y2="675.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="114.2745" x2="114.2745" y1="651" y2="675.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="173.0809" y1="651" y2="675.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="231.8873" y1="651" y2="651"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="231.8873" y1="675.6794" y2="675.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="173.0809" x2="173.0809" y1="651" y2="675.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8873" x2="231.8873" y1="651" y2="675.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8872" x2="290.6936000000001" y1="651" y2="651"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8872" x2="290.6936000000001" y1="675.6794" y2="675.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="231.8872" x2="231.8872" y1="651" y2="675.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="290.6936000000001" y1="651" y2="675.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="349.5" y1="651" y2="651"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="349.5" y1="675.6794" y2="675.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="290.6936000000001" x2="290.6936000000001" y1="651" y2="675.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="349.5" x2="349.5" y1="651" y2="675.6794"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.5" x2="104.5" y1="1007" y2="1007"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.5" x2="104.5" y1="1046.1633" y2="1046.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.5" x2="14.5" y1="1007" y2="1046.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="104.5" y1="1007" y2="1046.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="374.5" y1="1007" y2="1007"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="374.5" y1="1046.1633" y2="1046.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="104.5" y1="1007" y2="1046.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="374.5" x2="374.5" y1="1007" y2="1046.1633"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.5" x2="104.5" y1="1046.16327" y2="1046.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.5" x2="104.5" y1="1074.08167" y2="1074.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.5" x2="14.5" y1="1046.16327" y2="1074.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="104.5" y1="1046.16327" y2="1074.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="194.5" y1="1046.16327" y2="1046.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="194.5" y1="1074.08167" y2="1074.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="104.5" y1="1046.16327" y2="1074.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.5" x2="194.5" y1="1046.16327" y2="1074.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.5000000000001" x2="284.5000000000001" y1="1046.16327" y2="1046.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.5000000000001" x2="284.5000000000001" y1="1074.08167" y2="1074.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.5000000000001" x2="194.5000000000001" y1="1046.16327" y2="1074.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.5000000000001" x2="284.5000000000001" y1="1046.16327" y2="1074.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.5" x2="374.5" y1="1046.16327" y2="1046.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.5" x2="374.5" y1="1074.08167" y2="1074.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.5" x2="284.5" y1="1046.16327" y2="1074.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="374.5" x2="374.5" y1="1046.16327" y2="1074.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.5" x2="104.5" y1="1074.0816" y2="1074.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.5" x2="104.5" y1="1102" y2="1102"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.5" x2="14.5" y1="1074.0816" y2="1102"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="104.5" y1="1074.0816" y2="1102"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="194.5" y1="1074.0816" y2="1074.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="194.5" y1="1102" y2="1102"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="104.5" y1="1074.0816" y2="1102"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.5" x2="194.5" y1="1074.0816" y2="1102"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.5000000000001" x2="284.5000000000001" y1="1074.0816" y2="1074.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.5000000000001" x2="284.5000000000001" y1="1102" y2="1102"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.5000000000001" x2="194.5000000000001" y1="1074.0816" y2="1102"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.5000000000001" x2="284.5000000000001" y1="1074.0816" y2="1102"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.5" x2="374.5" y1="1074.0816" y2="1074.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.5" x2="374.5" y1="1102" y2="1102"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.5" x2="284.5" y1="1074.0816" y2="1102"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="374.5" x2="374.5" y1="1074.0816" y2="1102"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="211" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.5,1027) scale(1,1) translate(0,0)" writing-mode="lr" x="59.5" xml:space="preserve" y="1033" zvalue="411">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="210" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.5,1061) scale(1,1) translate(0,0)" writing-mode="lr" x="56.5" xml:space="preserve" y="1067" zvalue="412">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="209" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,238.5,1061) scale(1,1) translate(0,0)" writing-mode="lr" x="238.5" xml:space="preserve" y="1067" zvalue="413">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.5,1089) scale(1,1) translate(0,0)" writing-mode="lr" x="55.5" xml:space="preserve" y="1095" zvalue="414">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.5,1089) scale(1,1) translate(0,0)" writing-mode="lr" x="237.5" xml:space="preserve" y="1095" zvalue="415">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="206" stroke="rgb(255,255,255)" text-anchor="middle" x="144.25" xml:space="preserve" y="529" zvalue="416">35kV     Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="206" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="144.25" xml:space="preserve" y="545" zvalue="416">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82,721.5) scale(1,1) translate(0,0)" writing-mode="lr" x="82" xml:space="preserve" y="726" zvalue="418">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,214.899,388.841) scale(1,1) translate(0,0)" writing-mode="lr" x="214.9" xml:space="preserve" y="393.34" zvalue="419">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,319.899,388.841) scale(1,1) translate(0,0)" writing-mode="lr" x="319.9" xml:space="preserve" y="393.34" zvalue="420">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" x="261.546875" xml:space="preserve" y="529" zvalue="421">10kV     Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="261.546875" xml:space="preserve" y="545" zvalue="421">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" x="319.5" xml:space="preserve" y="529" zvalue="422">10kV      Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="319.5" xml:space="preserve" y="545" zvalue="422">母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93.5,564.5) scale(1,1) translate(0,0)" writing-mode="lr" x="93.5" xml:space="preserve" y="569" zvalue="423">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93.5,590) scale(1,1) translate(0,0)" writing-mode="lr" x="93.5" xml:space="preserve" y="594.5" zvalue="424">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93.5,615.5) scale(1,1) translate(0,0)" writing-mode="lr" x="93.5" xml:space="preserve" y="620" zvalue="425">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="196" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93.5,637.861) scale(1,1) translate(0,0)" writing-mode="lr" x="93.5" xml:space="preserve" y="642.3611106872559" zvalue="426">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="195" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93.5,661.861) scale(1,1) translate(0,0)" writing-mode="lr" x="93.5" xml:space="preserve" y="666.3611106872559" zvalue="427">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,239.554,1029) scale(1,1) translate(0,0)" writing-mode="lr" x="239.55" xml:space="preserve" y="1035" zvalue="428"> LongBa-01-2018</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,329.554,1061) scale(1,1) translate(0,0)" writing-mode="lr" x="329.55" xml:space="preserve" y="1067" zvalue="429">20200901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="192" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.5,248) scale(1,1) translate(0,0)" writing-mode="lr" x="51.5" xml:space="preserve" y="253.5" zvalue="430">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="191" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231.5,248) scale(1,1) translate(0,0)" writing-mode="lr" x="231.5" xml:space="preserve" y="253.5" zvalue="431">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.1875,272.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.19" xml:space="preserve" y="276.75" zvalue="432">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="189" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.6875,320) scale(1,1) translate(0,0)" writing-mode="lr" x="58.69" xml:space="preserve" y="325.5" zvalue="433">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.25,319.5) scale(1,1) translate(0,0)" writing-mode="lr" x="240.25" xml:space="preserve" y="325" zvalue="434">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.6875,343) scale(1,1) translate(0,0)" writing-mode="lr" x="58.69" xml:space="preserve" y="348.5" zvalue="435">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="186" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.25,342.5) scale(1,1) translate(0,0)" writing-mode="lr" x="240.25" xml:space="preserve" y="348" zvalue="436">2号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.1875,296.25) scale(1,1) translate(0,0)" writing-mode="lr" x="60.19" xml:space="preserve" y="300.75" zvalue="437">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,239.25,296) scale(1,1) translate(0,0)" writing-mode="lr" x="239.25" xml:space="preserve" y="300.5" zvalue="438">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" x="204.25" xml:space="preserve" y="529" zvalue="439">35kV    Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="204.25" xml:space="preserve" y="545" zvalue="439">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="182" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,239.5,272.25) scale(1,1) translate(0,0)" writing-mode="lr" x="239.5" xml:space="preserve" y="276.75" zvalue="440">35kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="175" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,148.5,1061) scale(1,1) translate(0,0)" writing-mode="lr" x="148.5" xml:space="preserve" y="1067" zvalue="446">李文杰</text>
 </g>
 <g id="ButtonClass">
  <g href="小电流装置20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="267" y="455.25" zvalue="441"/></g>
  <g href="全站公用20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="57.19" y="374.25" zvalue="442"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="159.09" y="455.25" zvalue="443"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="57.19" y="455.25" zvalue="444"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="57.19" y="414.75" zvalue="445"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
</svg>