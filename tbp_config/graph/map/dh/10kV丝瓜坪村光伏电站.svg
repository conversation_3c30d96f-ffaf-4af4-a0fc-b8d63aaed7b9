<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549594750978" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2027.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_0" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="3.333333333333332" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="24.08333333333333" y2="29.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="7" y1="19.5" y2="24.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_1" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="18.25" y2="32.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_2" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="4" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="10" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="25.83333333333333" y2="32.41666666666667"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0.25" y2="25.75"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id=":光伏模组_0" viewBox="0,0,80,80">
   <use terminal-index="0" type="0" x="23.86666666666666" xlink:href="#terminal" y="70.93333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="50.8" x2="57.2" y1="10.13333333333333" y2="10.13333333333333"/>
   <path d="M 36.7489 56.5767 L 38.0132 59.1405 L 39.2774 56.5767" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.6" x2="50.66666666666667" y1="21.33333333333333" y2="11.33333333333333"/>
   <path d="M 52.4 16.5333 L 49.2 16.5333 L 49.2 27.7333 L 52.4 27.7333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(170,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.2" x2="55.2" y1="32.53333333333333" y2="43.73333333333333"/>
   <path d="M 38 45.3333 L 38 35.7333 L 66.8 35.7333 L 66.8 30.9333" fill="none" stroke="rgb(170,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="73.59999999999999" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,40.13) scale(1,1) translate(0,0)" width="68.8" x="5.6" y="3.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="66.8" x2="66.8" y1="60.13333333333333" y2="64.93333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="5" x1="14" x2="33.2" y1="70.93333333333332" y2="70.93333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="62" x2="66.8" y1="60.13333333333333" y2="60.13333333333333"/>
   <path d="M 53.7333 10.4 L 53.7333 5.6 L 23.3333 5.6 L 23.7333 71.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="6 2" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.2" x2="55.2" y1="68.13333333333334" y2="79.20000000000002"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="23.6" y1="27.73333333333334" y2="27.73333333333334"/>
   <rect fill-opacity="0" height="6.77" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,37.91,50.21) scale(1,1) translate(0,0)" width="3.37" x="36.22" y="46.82"/>
   <path d="M 36.7489 61.0634 L 38.0132 63.6272 L 39.2774 61.0634" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="65.07886701035602" x2="68.00570387390415" y1="28.93345174664175" y2="28.93345174664175"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="37.9078029848685" x2="37.9078029848685" y1="55.40157383031868" y2="45.46666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.20202391636546" x2="14.96424980017045" y1="38.63150148536967" y2="35.9076739581179"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.20202391636535" x2="14.20202391636535" y1="27.59999999999994" y2="38.17753023082763"/>
   <rect fill-opacity="0" height="8.44" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.27,38.31) scale(1,1) translate(0,0)" width="5.34" x="11.6" y="34.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.20202391636544" x2="13.43979803256045" y1="38.63150148536967" y2="35.9076739581179"/>
   <ellipse cx="55.25" cy="50.35" fill-opacity="0" rx="6.61" ry="6.88" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="63.2906587876924" x2="70.00000000000001" y1="17.85532825265332" y2="17.85532825265332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="64.40888232307701" x2="68.88177646461541" y1="16.86538557734767" y2="16.86538557734767"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="65.40515432248043" x2="67.64160139324963" y1="15.89299537279264" y2="15.89299537279264"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.21175520726185" x2="14.21175520726185" y1="42.19334811824088" y2="44.59300008617765"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="66.58435362585561" x2="66.58435362585561" y1="17.91050011524912" y2="21.03483990886042"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="63.06666666666666" x2="66.60326121012066" y1="27.83642232472767" y2="21.07872108573694"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="66.5726562972967" x2="66.5726562972967" y1="28.99881464929833" y2="31.03687217788674"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="52.85048888888888" x2="54.58955061728395" y1="51.30912086720867" y2="47.91573333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="58.06767407407408" x2="56.32861234567901" y1="51.30912086720867" y2="47.91573333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="52.85048888888888" x2="58.06767407407408" y1="51.30912086720867" y2="51.30912086720867"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.29388998943305" x2="12.96402824408577" y1="44.28768815766198" y2="44.28768815766198"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.83991873489108" x2="13.41799949862773" y1="44.96864503947492" y2="44.96864503947492"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.52213885671171" x2="13.73577937680711" y1="45.64960192128786" y2="45.64960192128786"/>
   <ellipse cx="55.29" cy="61.19" fill-opacity="0" rx="6.67" ry="6.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.25184255358918" x2="51.41184255358919" y1="60.85264603444045" y2="58.87258205947708"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.25184255358919" x2="55.25184255358919" y1="64.81277398436721" y2="60.85264603444048"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.2518425535892" x2="59.09184255358919" y1="60.85264603444045" y2="58.87258205947708"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="70.20141457174999" x2="63.35850922025583" y1="64.84471210629269" y2="64.84471210629269"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="68.86808123841665" x2="64.69184255358917" y1="66.84471210629269" y2="66.84471210629269"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="67.93474790508333" x2="65.62517588692251" y1="68.84471210629269" y2="68.84471210629269"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.06666666666667" x2="55.06666666666667" y1="32.26666666666668" y2="19.86666666666667"/>
   <rect fill-opacity="0" height="8.449999999999999" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,54.93,25.92) scale(1,1) translate(0,0)" width="4.27" x="52.8" y="21.69"/>
   <ellipse cx="37.99" cy="57.88" fill-opacity="0" rx="2.94" ry="2.98" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="37.99" cy="62.37" fill-opacity="0" rx="2.94" ry="2.98" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="54.24" cy="11.23" fill-opacity="0" rx="1.1" ry="1.1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV丝瓜坪村光伏电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="41" xlink:href="logo.png" y="37.5"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,190,69.5) scale(1,1) translate(0,0)" writing-mode="lr" x="190" xml:space="preserve" y="73" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,192.5,67.1903) scale(1,1) translate(0,0)" writing-mode="lr" x="192.5" xml:space="preserve" y="76.19" zvalue="5">10kV丝瓜坪村光伏电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="20" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,81.5,308.5) scale(1,1) translate(0,0)" width="97" x="33" y="296.5" zvalue="10"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,81.5,308.5) scale(1,1) translate(0,0)" writing-mode="lr" x="81.5" xml:space="preserve" y="313" zvalue="10">全站公用</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.22236173734677" x2="101.5670617373469" y1="435.1666435058594" y2="435.1666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.22236173734677" x2="101.5670617373469" y1="472.6566435058594" y2="472.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.22236173734677" x2="53.22236173734677" y1="435.1666435058594" y2="472.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5670617373469" x2="101.5670617373469" y1="435.1666435058594" y2="472.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5673617373468" x2="163.6756617373469" y1="435.1666435058594" y2="435.1666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5673617373468" x2="163.6756617373469" y1="472.6566435058594" y2="472.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5673617373468" x2="101.5673617373468" y1="435.1666435058594" y2="472.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6756617373469" x2="163.6756617373469" y1="435.1666435058594" y2="472.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6751617373468" x2="226.9999617373469" y1="435.1666435058594" y2="435.1666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6751617373468" x2="226.9999617373469" y1="472.6566435058594" y2="472.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6751617373468" x2="163.6751617373468" y1="435.1666435058594" y2="472.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9999617373469" x2="226.9999617373469" y1="435.1666435058594" y2="472.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9998617373468" x2="289.1081617373468" y1="435.1666435058594" y2="435.1666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9998617373468" x2="289.1081617373468" y1="472.6566435058594" y2="472.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9998617373468" x2="226.9998617373468" y1="435.1666435058594" y2="472.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="289.1081617373468" y1="435.1666435058594" y2="472.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="351.2164617373469" y1="435.1666435058594" y2="435.1666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="351.2164617373469" y1="472.6566435058594" y2="472.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="289.1081617373468" y1="435.1666435058594" y2="472.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="351.2164617373469" x2="351.2164617373469" y1="435.1666435058594" y2="472.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.22236173734677" x2="101.5670617373469" y1="472.6567435058594" y2="472.6567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.22236173734677" x2="101.5670617373469" y1="496.8253435058594" y2="496.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.22236173734677" x2="53.22236173734677" y1="472.6567435058594" y2="496.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5670617373469" x2="101.5670617373469" y1="472.6567435058594" y2="496.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5673617373468" x2="163.6756617373469" y1="472.6567435058594" y2="472.6567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5673617373468" x2="163.6756617373469" y1="496.8253435058594" y2="496.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5673617373468" x2="101.5673617373468" y1="472.6567435058594" y2="496.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6756617373469" x2="163.6756617373469" y1="472.6567435058594" y2="496.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6751617373468" x2="226.9999617373469" y1="472.6567435058594" y2="472.6567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6751617373468" x2="226.9999617373469" y1="496.8253435058594" y2="496.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6751617373468" x2="163.6751617373468" y1="472.6567435058594" y2="496.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9999617373469" x2="226.9999617373469" y1="472.6567435058594" y2="496.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9998617373468" x2="289.1081617373468" y1="472.6567435058594" y2="472.6567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9998617373468" x2="289.1081617373468" y1="496.8253435058594" y2="496.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9998617373468" x2="226.9998617373468" y1="472.6567435058594" y2="496.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="289.1081617373468" y1="472.6567435058594" y2="496.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="351.2164617373469" y1="472.6567435058594" y2="472.6567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="351.2164617373469" y1="496.8253435058594" y2="496.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="289.1081617373468" y1="472.6567435058594" y2="496.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="351.2164617373469" x2="351.2164617373469" y1="472.6567435058594" y2="496.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.22236173734677" x2="101.5670617373469" y1="496.8253435058594" y2="496.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.22236173734677" x2="101.5670617373469" y1="520.9939435058594" y2="520.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.22236173734677" x2="53.22236173734677" y1="496.8253435058594" y2="520.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5670617373469" x2="101.5670617373469" y1="496.8253435058594" y2="520.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5673617373468" x2="163.6756617373469" y1="496.8253435058594" y2="496.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5673617373468" x2="163.6756617373469" y1="520.9939435058594" y2="520.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5673617373468" x2="101.5673617373468" y1="496.8253435058594" y2="520.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6756617373469" x2="163.6756617373469" y1="496.8253435058594" y2="520.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6751617373468" x2="226.9999617373469" y1="496.8253435058594" y2="496.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6751617373468" x2="226.9999617373469" y1="520.9939435058594" y2="520.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6751617373468" x2="163.6751617373468" y1="496.8253435058594" y2="520.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9999617373469" x2="226.9999617373469" y1="496.8253435058594" y2="520.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9998617373468" x2="289.1081617373468" y1="496.8253435058594" y2="496.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9998617373468" x2="289.1081617373468" y1="520.9939435058594" y2="520.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9998617373468" x2="226.9998617373468" y1="496.8253435058594" y2="520.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="289.1081617373468" y1="496.8253435058594" y2="520.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="351.2164617373469" y1="496.8253435058594" y2="496.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="351.2164617373469" y1="520.9939435058594" y2="520.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="289.1081617373468" y1="496.8253435058594" y2="520.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="351.2164617373469" x2="351.2164617373469" y1="496.8253435058594" y2="520.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.22236173734677" x2="101.5670617373469" y1="520.9939835058593" y2="520.9939835058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.22236173734677" x2="101.5670617373469" y1="545.1625835058594" y2="545.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.22236173734677" x2="53.22236173734677" y1="520.9939835058593" y2="545.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5670617373469" x2="101.5670617373469" y1="520.9939835058593" y2="545.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5673617373468" x2="163.6756617373469" y1="520.9939835058593" y2="520.9939835058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5673617373468" x2="163.6756617373469" y1="545.1625835058594" y2="545.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5673617373468" x2="101.5673617373468" y1="520.9939835058593" y2="545.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6756617373469" x2="163.6756617373469" y1="520.9939835058593" y2="545.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6751617373468" x2="226.9999617373469" y1="520.9939835058593" y2="520.9939835058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6751617373468" x2="226.9999617373469" y1="545.1625835058594" y2="545.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6751617373468" x2="163.6751617373468" y1="520.9939835058593" y2="545.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9999617373469" x2="226.9999617373469" y1="520.9939835058593" y2="545.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9998617373468" x2="289.1081617373468" y1="520.9939835058593" y2="520.9939835058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9998617373468" x2="289.1081617373468" y1="545.1625835058594" y2="545.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9998617373468" x2="226.9998617373468" y1="520.9939835058593" y2="545.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="289.1081617373468" y1="520.9939835058593" y2="545.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="351.2164617373469" y1="520.9939835058593" y2="520.9939835058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="351.2164617373469" y1="545.1625835058594" y2="545.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="289.1081617373468" y1="520.9939835058593" y2="545.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="351.2164617373469" x2="351.2164617373469" y1="520.9939835058593" y2="545.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.22236173734677" x2="101.5670617373469" y1="545.1627435058593" y2="545.1627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.22236173734677" x2="101.5670617373469" y1="569.3313435058594" y2="569.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.22236173734677" x2="53.22236173734677" y1="545.1627435058593" y2="569.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5670617373469" x2="101.5670617373469" y1="545.1627435058593" y2="569.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5673617373468" x2="163.6756617373469" y1="545.1627435058593" y2="545.1627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5673617373468" x2="163.6756617373469" y1="569.3313435058594" y2="569.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5673617373468" x2="101.5673617373468" y1="545.1627435058593" y2="569.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6756617373469" x2="163.6756617373469" y1="545.1627435058593" y2="569.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6751617373468" x2="226.9999617373469" y1="545.1627435058593" y2="545.1627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6751617373468" x2="226.9999617373469" y1="569.3313435058594" y2="569.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6751617373468" x2="163.6751617373468" y1="545.1627435058593" y2="569.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9999617373469" x2="226.9999617373469" y1="545.1627435058593" y2="569.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9998617373468" x2="289.1081617373468" y1="545.1627435058593" y2="545.1627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9998617373468" x2="289.1081617373468" y1="569.3313435058594" y2="569.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9998617373468" x2="226.9998617373468" y1="545.1627435058593" y2="569.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="289.1081617373468" y1="545.1627435058593" y2="569.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="351.2164617373469" y1="545.1627435058593" y2="545.1627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="351.2164617373469" y1="569.3313435058594" y2="569.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="289.1081617373468" y1="545.1627435058593" y2="569.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="351.2164617373469" x2="351.2164617373469" y1="545.1627435058593" y2="569.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.22236173734677" x2="101.5670617373469" y1="569.3313435058594" y2="569.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.22236173734677" x2="101.5670617373469" y1="593.4999435058594" y2="593.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.22236173734677" x2="53.22236173734677" y1="569.3313435058594" y2="593.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5670617373469" x2="101.5670617373469" y1="569.3313435058594" y2="593.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5673617373468" x2="163.6756617373469" y1="569.3313435058594" y2="569.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5673617373468" x2="163.6756617373469" y1="593.4999435058594" y2="593.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="101.5673617373468" x2="101.5673617373468" y1="569.3313435058594" y2="593.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6756617373469" x2="163.6756617373469" y1="569.3313435058594" y2="593.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6751617373468" x2="226.9999617373469" y1="569.3313435058594" y2="569.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6751617373468" x2="226.9999617373469" y1="593.4999435058594" y2="593.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="163.6751617373468" x2="163.6751617373468" y1="569.3313435058594" y2="593.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9999617373469" x2="226.9999617373469" y1="569.3313435058594" y2="593.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9998617373468" x2="289.1081617373468" y1="569.3313435058594" y2="569.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9998617373468" x2="289.1081617373468" y1="593.4999435058594" y2="593.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="226.9998617373468" x2="226.9998617373468" y1="569.3313435058594" y2="593.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="289.1081617373468" y1="569.3313435058594" y2="593.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="351.2164617373469" y1="569.3313435058594" y2="569.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="351.2164617373469" y1="593.4999435058594" y2="593.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.1081617373468" x2="289.1081617373468" y1="569.3313435058594" y2="593.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="351.2164617373469" x2="351.2164617373469" y1="569.3313435058594" y2="593.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2" x2="183" y1="153.4999999999999" y2="153.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2" x2="183" y1="179.4999999999999" y2="179.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2" x2="2" y1="153.4999999999999" y2="179.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183" x2="183" y1="153.4999999999999" y2="179.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183" x2="364" y1="153.4999999999999" y2="153.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183" x2="364" y1="179.4999999999999" y2="179.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183" x2="183" y1="153.4999999999999" y2="179.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364" x2="364" y1="153.4999999999999" y2="179.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2" x2="183" y1="179.4999999999999" y2="179.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2" x2="183" y1="203.7499999999999" y2="203.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2" x2="2" y1="179.4999999999999" y2="203.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183" x2="183" y1="179.4999999999999" y2="203.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183" x2="364" y1="179.4999999999999" y2="179.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183" x2="364" y1="203.7499999999999" y2="203.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183" x2="183" y1="179.4999999999999" y2="203.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364" x2="364" y1="179.4999999999999" y2="203.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2" x2="183" y1="203.7499999999999" y2="203.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2" x2="183" y1="226.4999999999999" y2="226.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2" x2="2" y1="203.7499999999999" y2="226.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183" x2="183" y1="203.7499999999999" y2="226.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183" x2="364" y1="203.7499999999999" y2="203.7499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183" x2="364" y1="226.4999999999999" y2="226.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183" x2="183" y1="203.7499999999999" y2="226.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364" x2="364" y1="203.7499999999999" y2="226.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2" x2="183" y1="226.4999999999999" y2="226.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2" x2="183" y1="249.2499999999999" y2="249.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2" x2="2" y1="226.4999999999999" y2="249.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183" x2="183" y1="226.4999999999999" y2="249.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183" x2="364" y1="226.4999999999999" y2="226.4999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183" x2="364" y1="249.2499999999999" y2="249.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183" x2="183" y1="226.4999999999999" y2="249.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364" x2="364" y1="226.4999999999999" y2="249.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2" x2="183" y1="249.2499999999999" y2="249.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2" x2="183" y1="271.9999999999999" y2="271.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2" x2="2" y1="249.2499999999999" y2="271.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183" x2="183" y1="249.2499999999999" y2="271.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183" x2="364" y1="249.2499999999999" y2="249.2499999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183" x2="364" y1="271.9999999999999" y2="271.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183" x2="183" y1="249.2499999999999" y2="271.9999999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364" x2="364" y1="249.2499999999999" y2="271.9999999999999"/>
  <line fill="none" id="24" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="375" x2="375" y1="5.5" y2="1035.5" zvalue="6"/>
  <line fill="none" id="22" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.821210263296962e-13" x2="368.0000000000002" y1="141.3704926140823" y2="141.3704926140823" zvalue="8"/>
  <line fill="none" id="21" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.821210263296962e-13" x2="368.0000000000002" y1="611.3704926140823" y2="611.3704926140823" zvalue="9"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1" x2="91" y1="926.5" y2="926.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1" x2="91" y1="965.6632999999999" y2="965.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1" x2="1" y1="926.5" y2="965.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91" x2="91" y1="926.5" y2="965.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91" x2="361" y1="926.5" y2="926.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91" x2="361" y1="965.6632999999999" y2="965.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91" x2="91" y1="926.5" y2="965.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361" x2="361" y1="926.5" y2="965.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1" x2="91" y1="965.66327" y2="965.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1" x2="91" y1="993.58167" y2="993.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1" x2="1" y1="965.66327" y2="993.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91" x2="91" y1="965.66327" y2="993.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91" x2="181" y1="965.66327" y2="965.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91" x2="181" y1="993.58167" y2="993.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91" x2="91" y1="965.66327" y2="993.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181" x2="181" y1="965.66327" y2="993.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000001" x2="271.0000000000001" y1="965.66327" y2="965.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000001" x2="271.0000000000001" y1="993.58167" y2="993.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000001" x2="181.0000000000001" y1="965.66327" y2="993.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000001" x2="271.0000000000001" y1="965.66327" y2="993.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271" x2="361" y1="965.66327" y2="965.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271" x2="361" y1="993.58167" y2="993.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271" x2="271" y1="965.66327" y2="993.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361" x2="361" y1="965.66327" y2="993.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1" x2="91" y1="993.5816" y2="993.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1" x2="91" y1="1021.5" y2="1021.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="1" x2="1" y1="993.5816" y2="1021.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91" x2="91" y1="993.5816" y2="1021.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91" x2="181" y1="993.5816" y2="993.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91" x2="181" y1="1021.5" y2="1021.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="91" x2="91" y1="993.5816" y2="1021.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181" x2="181" y1="993.5816" y2="1021.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000001" x2="271.0000000000001" y1="993.5816" y2="993.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000001" x2="271.0000000000001" y1="1021.5" y2="1021.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="181.0000000000001" x2="181.0000000000001" y1="993.5816" y2="1021.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271.0000000000001" x2="271.0000000000001" y1="993.5816" y2="1021.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271" x2="361" y1="993.5816" y2="993.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271" x2="361" y1="1021.5" y2="1021.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="271" x2="271" y1="993.5816" y2="1021.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361" x2="361" y1="993.5816" y2="1021.5"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46,946.5) scale(1,1) translate(0,0)" writing-mode="lr" x="46" xml:space="preserve" y="952.5" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,43,980.5) scale(1,1) translate(0,0)" writing-mode="lr" x="43" xml:space="preserve" y="986.5" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225,980.5) scale(1,1) translate(0,0)" writing-mode="lr" x="225" xml:space="preserve" y="986.5" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,42,1008.5) scale(1,1) translate(0,0)" writing-mode="lr" x="42" xml:space="preserve" y="1014.5" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224,1008.5) scale(1,1) translate(0,0)" writing-mode="lr" x="224" xml:space="preserve" y="1014.5" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" x="129" xml:space="preserve" y="448.5" zvalue="17">110kV  母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="129" xml:space="preserve" y="464.5" zvalue="17">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,66.5,641) scale(1,1) translate(0,-2.76223e-13)" writing-mode="lr" x="66.5" xml:space="preserve" y="645.4999999999999" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.399,308.341) scale(1,1) translate(0,0)" writing-mode="lr" x="199.4" xml:space="preserve" y="312.84" zvalue="20">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,304.399,308.341) scale(1,1) translate(0,0)" writing-mode="lr" x="304.4" xml:space="preserve" y="312.84" zvalue="21">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78,484) scale(1,1) translate(0,0)" writing-mode="lr" x="78" xml:space="preserve" y="488.5" zvalue="22">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78,509.5) scale(1,1) translate(0,0)" writing-mode="lr" x="78" xml:space="preserve" y="514" zvalue="23">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78,535) scale(1,1) translate(0,0)" writing-mode="lr" x="78" xml:space="preserve" y="539.5" zvalue="24">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77,559.5) scale(1,1) translate(0,6.05072e-14)" writing-mode="lr" x="77" xml:space="preserve" y="563.9999999999999" zvalue="25">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78,586) scale(1,1) translate(0,-1.26898e-13)" writing-mode="lr" x="78" xml:space="preserve" y="590.4999999999999" zvalue="26">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.054,948.5) scale(1,1) translate(0,0)" writing-mode="lr" x="226.05" xml:space="preserve" y="954.5" zvalue="27">SGPCGF-01-2018</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,40,167.5) scale(1,1) translate(0,0)" writing-mode="lr" x="40" xml:space="preserve" y="173" zvalue="28">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,220,167.5) scale(1,1) translate(0,0)" writing-mode="lr" x="220" xml:space="preserve" y="173" zvalue="29">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,511.375,532) scale(1,1) translate(0,0)" writing-mode="lr" x="511.38" xml:space="preserve" y="536.5" zvalue="32">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,816.75,814.911) scale(1,1) translate(0,0)" writing-mode="lr" x="816.75" xml:space="preserve" y="819.41" zvalue="33">10kV#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1055,811.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1055" xml:space="preserve" y="816.38" zvalue="35">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,625.125,673.25) scale(1,1) translate(0,0)" writing-mode="lr" x="625.13" xml:space="preserve" y="677.75" zvalue="37">013</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,845.75,484.5) scale(1,1) translate(0,0)" writing-mode="lr" x="845.75" xml:space="preserve" y="489" zvalue="40">011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,845.125,642) scale(1,1) translate(3.6865e-13,0)" writing-mode="lr" x="845.13" xml:space="preserve" y="646.5" zvalue="46">0121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1016.12,647) scale(1,1) translate(0,0)" writing-mode="lr" x="1016.13" xml:space="preserve" y="651.5" zvalue="49">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,664,738.562) scale(1,1) translate(0,0)" writing-mode="lr" x="664" xml:space="preserve" y="743.0599999999999" zvalue="64">01367</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1443.75,253.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1443.75" xml:space="preserve" y="257.63" zvalue="71">#2箱变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1717.5,258.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1717.5" xml:space="preserve" y="262.63" zvalue="75">#1箱变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,993.857,1012.86) scale(1,1) translate(0,-2.21727e-13)" writing-mode="lr" x="993.86" xml:space="preserve" y="1017.36" zvalue="106">10kV光伏集成线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" x="1473.1640625" xml:space="preserve" y="485.7924107142858" zvalue="109">#2光阵(丝瓜坪</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1473.1640625" xml:space="preserve" y="501.7924107142858" zvalue="109">村)0.5MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" x="1730.6640625" xml:space="preserve" y="494.5424107142858" zvalue="111">#1光阵(三锅疆</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1730.6640625" xml:space="preserve" y="510.5424107142858" zvalue="111">村)0.5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1527.5,447.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1527.5" xml:space="preserve" y="452" zvalue="114">500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1805,446.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1805" xml:space="preserve" y="451" zvalue="115">500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1467.5,446.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1467.5" xml:space="preserve" y="451" zvalue="116">0.38kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1751.5,447) scale(1,1) translate(0,0)" writing-mode="lr" x="1751.5" xml:space="preserve" y="451.5" zvalue="117">0.38kV</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,821.5,312) scale(1,1) translate(0,0)" writing-mode="lr" x="821.5" xml:space="preserve" y="316.5" zvalue="131">10kV河丝线T丝瓜坪村光伏电站</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,845,355.5) scale(1,1) translate(0,0)" writing-mode="lr" x="845" xml:space="preserve" y="360" zvalue="131">#4杆</text>
 </g>
 <g id="ButtonClass">
  <g href="500kV德宏变_全站公用.svg"><rect fill-opacity="0" height="24" width="97" x="33" y="296.5" zvalue="10"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="30">
   <path class="kv10" d="M 502.5 554.25 L 1253.75 554.25" stroke-width="6" zvalue="31"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674258190340" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674258190340"/></metadata>
  <path d="M 502.5 554.25 L 1253.75 554.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="31">
   <use class="kv10" height="30" transform="rotate(0,816.875,767.161) scale(2.63393,2.65241) translate(-483.864,-453.143)" width="28" x="780" xlink:href="#EnergyConsumer:站用变DY接地_0" y="727.375" zvalue="32"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450101313541" ObjectName="10kV#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,816.875,767.161) scale(2.63393,2.65241) translate(-483.864,-453.143)" width="28" x="780" y="727.375"/></g>
  <g id="87">
   <use class="kv10" height="30" transform="rotate(0,817.5,351) scale(1.25,1.23333) translate(-162,-62.9054)" width="12" x="810.0000000019313" xlink:href="#EnergyConsumer:负荷_0" y="332.5" zvalue="130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450389016581" ObjectName="10kV河丝线T丝瓜坪村光伏电站"/>
   <cge:TPSR_Ref TObjectID="6192450389016581"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,817.5,351) scale(1.25,1.23333) translate(-162,-62.9054)" width="12" x="810.0000000019313" y="332.5"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="33">
   <use class="kv10" height="42" transform="rotate(0,1055,771.75) scale(1.25,-1.25) translate(-207.25,-1383.9)" width="30" x="1036.25" xlink:href="#Accessory:4卷PT带容断器_0" y="745.5" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450101379077" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1055,771.75) scale(1.25,-1.25) translate(-207.25,-1383.9)" width="30" x="1036.25" y="745.5"/></g>
  <g id="63">
   <use class="kv10" height="26" transform="rotate(0,543.75,790.5) scale(1.25,1.25) translate(-107.25,-154.85)" width="12" x="536.25" xlink:href="#Accessory:避雷器1_0" y="774.25" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450101772293" ObjectName="013侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,543.75,790.5) scale(1.25,1.25) translate(-107.25,-154.85)" width="12" x="536.25" y="774.25"/></g>
  <g id="65">
   <use class="kv10" height="26" transform="rotate(0,775,411.75) scale(1.25,-1.25) translate(-153.5,-737.9)" width="12" x="767.5" xlink:href="#Accessory:避雷器1_0" y="395.5" zvalue="68"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450101837829" ObjectName="011侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,775,411.75) scale(1.25,-1.25) translate(-153.5,-737.9)" width="12" x="767.5" y="395.5"/></g>
 </g>
 <g id="BreakerClass">
  <g id="35">
   <use class="kv10" height="20" transform="rotate(0,596.25,674.25) scale(2.75,2.75) translate(-370.682,-411.568)" width="10" x="582.5" xlink:href="#Breaker:小车断路器_0" y="646.75" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924568285188" ObjectName="013"/>
   <cge:TPSR_Ref TObjectID="6473924568285188"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,596.25,674.25) scale(2.75,2.75) translate(-370.682,-411.568)" width="10" x="582.5" y="646.75"/></g>
  <g id="37">
   <use class="kv10" height="20" transform="rotate(0,817.5,485.5) scale(2.75,2.75) translate(-511.477,-291.455)" width="10" x="803.7500000019312" xlink:href="#Breaker:小车断路器_0" y="458" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924568350724" ObjectName="011"/>
   <cge:TPSR_Ref TObjectID="6473924568350724"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,817.5,485.5) scale(2.75,2.75) translate(-511.477,-291.455)" width="10" x="803.7500000019312" y="458"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="36">
   <path class="kv10" d="M 596.25 648.81 L 596.25 554.25" stroke-width="1" zvalue="37"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="30@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 596.25 648.81 L 596.25 554.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv10" d="M 817.5 510.25 L 817.5 554.25" stroke-width="1" zvalue="40"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@1" LinkObjectIDznd="30@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 817.5 510.25 L 817.5 554.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv10" d="M 817.12 728.96 L 817.12 663.37" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31@0" LinkObjectIDznd="43@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 817.12 728.96 L 817.12 663.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv10" d="M 817.5 623 L 817.5 554.25" stroke-width="1" zvalue="47"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@1" LinkObjectIDznd="30@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 817.5 623 L 817.5 554.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv10" d="M 1043.1 746.28 L 1043.1 664.21" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="46@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1043.1 746.28 L 1043.1 664.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv10" d="M 1043.75 631.84 L 1043.75 554.25" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@1" LinkObjectIDznd="30@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1043.75 631.84 L 1043.75 554.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv10" d="M 1396.58 438.13 L 1412.5 438.13 L 1412.5 520.5 L 1656.25 520.5 L 1656.25 438.13 L 1675.33 438.13" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="71@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1396.58 438.13 L 1412.5 438.13 L 1412.5 520.5 L 1656.25 520.5 L 1656.25 438.13 L 1675.33 438.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv10" d="M 817.5 460.06 L 817.5 367.65" stroke-width="1" zvalue="80"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="87@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 817.5 460.06 L 817.5 367.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv10" d="M 1396.58 438.13 L 1396.58 990.5 L 596.25 990.5 L 596.25 699" stroke-width="1" zvalue="99"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73" LinkObjectIDznd="35@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1396.58 438.13 L 1396.58 990.5 L 596.25 990.5 L 596.25 699" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv10" d="M 651.81 756.75 L 596.25 756.75" stroke-width="1" zvalue="100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="91" MaxPinNum="2"/>
   </metadata>
  <path d="M 651.81 756.75 L 596.25 756.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv10" d="M 543.79 775.04 L 543.79 755.5 L 596.25 755.5" stroke-width="1" zvalue="101"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="91" MaxPinNum="2"/>
   </metadata>
  <path d="M 543.79 775.04 L 543.79 755.5 L 596.25 755.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv10" d="M 775.04 427.21 L 775.04 439.25 L 817.5 439.25" stroke-width="1" zvalue="102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="78" MaxPinNum="2"/>
   </metadata>
  <path d="M 775.04 427.21 L 775.04 439.25 L 817.5 439.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="43">
   <use class="kv10" height="33" transform="rotate(0,817.5,643) scale(1.25,-1.25) translate(-161.75,-1153.28)" width="14" x="808.7500000019311" xlink:href="#Disconnector:手车隔离开关13_0" y="622.375" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450101444613" ObjectName="10kV#1站用变0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450101444613"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,817.5,643) scale(1.25,-1.25) translate(-161.75,-1153.28)" width="14" x="808.7500000019311" y="622.375"/></g>
  <g id="46">
   <use class="kv10" height="26" transform="rotate(0,1043.75,648) scale(1.25,1.25) translate(-207.25,-126.35)" width="12" x="1036.25" xlink:href="#Disconnector:小车隔刀熔断器_0" y="631.75" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450101510149" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450101510149"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1043.75,648) scale(1.25,1.25) translate(-207.25,-126.35)" width="12" x="1036.25" y="631.75"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="61">
   <use class="kv10" height="20" transform="rotate(270,664,756.812) scale(1.25,1.25) translate(-131.55,-148.863)" width="10" x="657.75" xlink:href="#GroundDisconnector:地刀_0" y="744.3125" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450101706757" ObjectName="01367"/>
   <cge:TPSR_Ref TObjectID="6192450101706757"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,664,756.812) scale(1.25,1.25) translate(-131.55,-148.863)" width="10" x="657.75" y="744.3125"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="64" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,923.5,274.5) scale(1,1) translate(0,0)" writing-mode="lr" x="875.1" xml:space="preserve" y="278.86" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127067975684" ObjectName="GEN_PSUM"/>
   </metadata>
  </g>
 </g>
</svg>