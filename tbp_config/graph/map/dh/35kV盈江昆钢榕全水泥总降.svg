<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549591801858" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:线路PT235_0" viewBox="0,0,17,36">
   <use terminal-index="0" type="0" x="8.5" xlink:href="#terminal" y="0.4166666666666643"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,8.5,9.75) scale(1,1) translate(0,0)" width="4" x="6.5" y="6.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="11" y1="21.58333333333333" y2="21.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="8.5" y1="17.25" y2="0.25"/>
   <ellipse cx="8.4" cy="22.18" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.4" cy="30.28" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="11" y1="30.33333333333334" y2="30.33333333333334"/>
  </symbol>
  <symbol id="Accessory:10kV避雷器PT_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="4"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.75" x2="8.75" y1="18" y2="14"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75" x2="22.75" y1="23" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.75" x2="20.75" y1="11" y2="21"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.66666666666667" x2="23.75" y1="21" y2="21"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.75" x2="21.75" y1="25" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.75" x2="21.5" y1="11" y2="13.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.75" x2="20.75" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.75" x2="8.75" y1="9" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.75" x2="20.75" y1="9" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="4" y2="5"/>
   <rect fill="rgb(0,0,0)" fill-opacity="1" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,8.83,11.58) scale(1,1) translate(0,0)" width="3" x="7.33" y="9.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.79263465688835" x2="8.79263465688835" y1="19.45930132355502" y2="20.45930132355502"/>
   <ellipse cx="8.789999999999999" cy="20.46" fill-opacity="0" rx="2.29" ry="2.29" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20.75,11.58) scale(1,1) translate(0,0)" width="2.33" x="19.58" y="9.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.79263465688835" x2="9.79263465688835" y1="20.45930132355502" y2="19.45930132355502"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.79263465688835" x2="8.79263465688835" y1="20.45930132355502" y2="21.45930132355502"/>
   <ellipse cx="8.81" cy="25.04" fill-opacity="0" rx="2.29" ry="2.29" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="11.21" cy="22.96" fill-opacity="0" rx="2.29" ry="2.29" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="6.54" cy="22.79" fill-opacity="0" rx="2.29" ry="2.29" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.29263465688835" x2="11.29263465688835" y1="23.00930132355501" y2="24.00930132355501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.29263465688835" x2="11.29263465688835" y1="22.00930132355501" y2="23.00930132355501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.29263465688835" x2="12.29263465688835" y1="23.00930132355501" y2="22.00930132355501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.29263465688835" x2="7.29263465688835" y1="23.00930132355501" y2="22.00930132355501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.29263465688835" x2="6.29263465688835" y1="23.00930132355501" y2="24.00930132355501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.29263465688835" x2="6.29263465688835" y1="22.00930132355501" y2="23.00930132355501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.79263465688835" x2="8.79263465688835" y1="25.50930132355501" y2="26.50930132355501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.79263465688835" x2="9.79263465688835" y1="25.50930132355501" y2="24.50930132355501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.79263465688835" x2="8.79263465688835" y1="24.50930132355501" y2="25.50930132355501"/>
  </symbol>
  <symbol id="EnergyConsumer:糖厂负荷_0" viewBox="0,0,21,30">
   <use terminal-index="0" type="0" x="7.166666666666666" xlink:href="#terminal" y="0.1666666666666714"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25" x2="19.25" y1="24" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.75" x2="18.75" y1="25" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="17.75" y1="26.25" y2="26.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="25.25" y2="30.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.25" x2="17.25" y1="19" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.25" x2="17.25" y1="19" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.097025948103791" x2="7.097025948103791" y1="1.79293756914176" y2="0.2382945839350779"/>
   <ellipse cx="6.92" cy="8.35" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7" cy="18.92" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.111718118722353" x2="7.111718118722353" y1="16.35874008086165" y2="18.95772818087859"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.705265608193804" x2="7.11171811872235" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.518170629250881" x2="7.111718118722335" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.111718118722353" x2="7.111718118722353" y1="5.503560677018264" y2="8.102548777035198"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.705265608193804" x2="7.11171811872235" y1="10.70153687705211" y2="8.102548777035178"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.518170629250881" x2="7.111718118722335" y1="10.70153687705211" y2="8.102548777035178"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(130,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(130,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Compensator:并联电容器组_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="19.25" y2="28.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.33333333333334" x2="29" y1="24" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.25" x2="26.25" y1="17.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29" x2="22.58333333333333" y1="24" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="19.91666666666667" y1="2" y2="9.833333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="9.75" y1="2.249999999999996" y2="10.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1" x2="12" y1="24.25" y2="24.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.25" x2="14" y1="8.333333333333332" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="1" y1="15.25" y2="24.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.75" x2="11.5" y1="12.33333333333333" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.5" x2="23.5" y1="13.25" y2="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.4" x2="17.4" y1="19.35" y2="28.93333333333333"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.998992968246015" x2="4.998992968246015" y1="14.41666666666666" y2="17.66666666666666"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="1.08333333333333" y2="5.749999999999999"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:母联小车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.9499999999999993"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="6" y2="14"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="14" y2="6"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸1_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="35.06751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="9" y1="9" y2="9"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.084670781893005" x2="1.626337448559672" y1="4.853614126578682" y2="9.663958954164888"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.084670781893004" x2="12.54300411522634" y1="4.853614126578682" y2="9.663958954164888"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.27315435646374" y2="26.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="31.27315435646374" y2="26.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="7.00133744855967" y1="4.916666666666663" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="1.833333333333333" x2="7.001337448559672" y1="10.75" y2="27"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="7.00133744855967" x2="7.00133744855967" y1="26.75" y2="31.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸1_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="35.06751120764343"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="7.00133744855967" y1="0.7499999999999964" y2="35.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="9" y1="9" y2="9"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸1_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="35.06751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="2.083333333333333" y1="11.16666666666666" y2="25.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.133446357018218" x2="11.79254048508705" y1="11.12564285751284" y2="24.83570582669768"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.27315435646374" y2="27.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.27315435646374" y2="27.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="35.07918883922238" y2="30.26884401163617"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="Accessory:带熔断器四卷PT_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12" y2="1"/>
   <ellipse cx="15.25" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.08" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.25" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="18.82" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="11" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.25" x2="15.25" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25" x2="15.25" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="15.25" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="15" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="15" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="20.94444444444444" y1="21.46612466124661" y2="21.46612466124661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="18.31481481481482" y1="21.46612466124661" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.94444444444444" x2="19.62962962962963" y1="21.46612466124661" y2="19"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,6.21) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="2.5"/>
  </symbol>
  <symbol id="Accessory:10kV母线PT带消谐装置_0" viewBox="0,0,35,30">
   <use terminal-index="0" type="0" x="17.56245852479325" xlink:href="#terminal" y="28.12373692455963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.61245852479333" x2="34.61245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.86245852479331" x2="30.86245852479331" y1="22.68299618381883" y2="19.84040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.6124585247933" x2="26.6124585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.61245852479332" x2="34.61245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666667" x2="31" y1="22.69225544307809" y2="22.69225544307809"/>
   <rect fill-opacity="0" height="3.55" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,30.81,15.38) scale(1,1) translate(0,0)" width="8.92" x="26.34" y="13.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.647721108666" x2="30.647721108666" y1="10.83674821859629" y2="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.71438777533266" x2="28.71438777533266" y1="7.51612768565195" y2="7.51612768565195"/>
   <ellipse cx="14.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.05048569403981" x2="22.05048569403981" y1="34.53084700683308" y2="34.53084700683308"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.7608382776216" x2="28.48402243293775" y1="7.754208141873304" y2="7.754208141873304"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.01083827762157" x2="29.40068909960439" y1="6.50420814187332" y2="6.50420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.51083827762159" x2="30.06735576627107" y1="5.004208141873296" y2="5.004208141873296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.89410730945214" x2="23.0877735452272" y1="24.15299989806297" y2="21.53208583485582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58713830216066" x2="22.81883560169719" y1="21.54040359122622" y2="21.54040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51457106407813" x2="19.32090482830307" y1="24.05654513693459" y2="21.43563107372743"/>
   <ellipse cx="14.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.3644003886642" x2="22.3644003886642" y1="30.19213090500035" y2="30.19213090500035"/>
   <ellipse cx="21.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.19220382559271" x2="17.19220382559271" y1="23.07076893362116" y2="23.07076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.4422038255927" x2="16.4422038255927" y1="16.82076893362115" y2="16.82076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.92264294445647" x2="20.87303257583197" y1="16.99992879670395" y2="15.47611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.873032575832" x2="20.37063985073817" y1="15.47611741162252" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.87303257583199" x2="23.32581493230132" y1="15.47611741162254" y2="16.60543752773795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.94220382559271" x2="16.94220382559271" y1="16.32076893362115" y2="16.32076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="16.07581493230131" y1="22.72611741162255" y2="23.85543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="13.12063985073816" y1="22.72611741162255" y2="20.07298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583197" y1="24.24992879670398" y2="22.72611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="16.07581493230131" y1="15.47611741162254" y2="16.60543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="13.12063985073816" y1="15.47611741162253" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583196" y1="16.99992879670396" y2="15.47611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.59103363843511" x2="17.59103363843511" y1="25.15822158129307" y2="28.16666666666667"/>
  </symbol>
  <symbol id="Disconnector:20210316_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,18.75) scale(1,1) translate(0,0)" width="6" x="3" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="6.66666666666667" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="6.727157046327149" y2="14.02109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="6.734473004260392" y2="14.02109851866368"/>
  </symbol>
  <symbol id="Disconnector:20210316_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,18.75) scale(1,1) translate(0,0)" width="6" x="3" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="0.25" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="0.7271570463271502" y2="8.021098518663681"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="0.7344730042603906" y2="8.021098518663679"/>
  </symbol>
  <symbol id="Disconnector:20210316_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="EnergyConsumer:厂变_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="2.833333333333344" y2="0.9166666666666679"/>
   <ellipse cx="7.38" cy="6.97" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.44" cy="12.96" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀-带电显示_0" viewBox="0,0,21,29">
   <use terminal-index="0" type="0" x="10.57144275301999" xlink:href="#terminal" y="0.9988473696347349"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.5" x2="4.5" y1="4.5" y2="4.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="4.5" y2="8.5"/>
   <ellipse cx="4.5" cy="16.25" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="21.5" y2="19.08333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="1.15" y2="4.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="20.5" y2="21.5"/>
   <path d="M 2.25 14.4167 L 6.58333 18" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="20.41666666666667" y2="18.01429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="21.35" y2="24.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="8.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.166666666666666" x2="13.5" y1="24.24694619969017" y2="24.24694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.583333333333334" x2="12.5" y1="26.08157590710536" y2="26.08157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.274021844661625" x2="11.93079938068318" y1="27.83348701738202" y2="27.83348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.16666666666667" x2="16.33141025641025" y1="8.166666666666666" y2="18.0142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.66666666666667" x2="18" y1="8.04249548976499" y2="8.04249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="8.000000000000004" y2="4.416666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.41666666666666" x2="4.416666666666666" y1="21.41666666666666" y2="21.41666666666666"/>
   <path d="M 6.66667 14.25 L 2.41667 18.3333" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="10.91666666666667" y2="10.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀-带电显示_1" viewBox="0,0,21,29">
   <use terminal-index="0" type="0" x="10.57144275301999" xlink:href="#terminal" y="0.9988473696347349"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.166666666666666" x2="13.5" y1="24.24694619969017" y2="24.24694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.583333333333334" x2="12.5" y1="26.08157590710536" y2="26.08157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.274021844661625" x2="11.93079938068318" y1="27.83348701738202" y2="27.83348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.367489181242" x2="16.367489181242" y1="8.166666666666666" y2="20.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="8.000000000000004" y2="4.416666666666668"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.66666666666667" x2="18" y1="8.04249548976499" y2="8.04249548976499"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="21.5" y2="19.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.367489181242" x2="16.367489181242" y1="20.41666666666667" y2="18.01429160524171"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="1.15" y2="4.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="20.5" y2="21.5"/>
   <ellipse cx="4.5" cy="16.25" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.5" x2="4.5" y1="4.5" y2="4.5"/>
   <path d="M 2.25 14.4167 L 6.58333 18" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="8.5" y2="8.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="4.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="21.35" y2="24.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.367489181242" x2="16.367489181242" y1="8.000000000000004" y2="4.416666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="10.91666666666667" y2="10.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.41666666666666" x2="4.416666666666666" y1="21.41666666666666" y2="21.41666666666666"/>
   <path d="M 6.66667 14.25 L 2.41667 18.3333" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀-带电显示_2" viewBox="0,0,21,29">
   <use terminal-index="0" type="0" x="10.57144275301999" xlink:href="#terminal" y="0.9988473696347349"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.75" x2="12.75" y1="8.5" y2="17.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="20.41666666666667" y2="18.01429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.166666666666666" x2="13.5" y1="24.24694619969017" y2="24.24694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.583333333333334" x2="12.5" y1="26.08157590710536" y2="26.08157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.274021844661625" x2="11.93079938068318" y1="27.83348701738202" y2="27.83348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.75" x2="20" y1="8.383333333333333" y2="17.8"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="8.000000000000004" y2="4.416666666666668"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.66666666666667" x2="18" y1="8.04249548976499" y2="8.04249548976499"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="21.5" y2="19.08333333333333"/>
   <ellipse cx="4.5" cy="16.25" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="1.15" y2="4.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="21.35" y2="24.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.5" x2="4.5" y1="4.5" y2="4.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.367489181242" x2="16.367489181242" y1="20.5" y2="21.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="4.5" y2="8.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="8.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="4.5" y1="11" y2="13.5"/>
   <path d="M 2.25 14.4167 L 6.58333 18" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.41666666666666" x2="4.416666666666666" y1="21.41666666666666" y2="21.41666666666666"/>
   <path d="M 6.66667 14.25 L 2.41667 18.3333" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="6" y1="10.91666666666667" y2="10.91666666666667"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.750000000000004" y2="4.166666666666668"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="32"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="1" y1="27" y2="11"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="27" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.583333333333332" y2="1.050000000000001"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="26.41666666666667" y2="34.83333333333334"/>
   <rect fill-opacity="0" height="17" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,18) scale(1,1) translate(0,0)" width="8" x="3" y="9.5"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="35" y2="1.166666666666664"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <rect fill-opacity="0" height="17" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,18) scale(1,1) translate(0,0)" width="8" x="3" y="9.5"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="9.999999999999995" y2="1.299999999999994"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
  </symbol>
  <symbol id="Accessory:发电机励磁TV_0" viewBox="0,0,25,30">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="0.2666666666666657"/>
   <rect fill-opacity="0" height="6.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,19.5,20.79) scale(1,1) translate(0,0)" width="3" x="18" y="17.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.5" x2="19.5" y1="13" y2="21"/>
   <path d="M 18.5 19 L 19.5 21 L 20.5 19" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="21" y1="26" y2="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.5" x2="20.5" y1="27" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.5" x2="19.5" y1="24" y2="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="19.5" y1="13" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="20" y1="28" y2="28"/>
   <ellipse cx="12.67" cy="13.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.57" cy="5.35" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.58333333333333" x2="12.58333333333333" y1="11.13888888888889" y2="13.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.58333333333333" x2="12.58333333333333" y1="11.13888888888889" y2="13.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="15.75" y2="13.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="12.5" y1="3.222222222222225" y2="5.222222222222225"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="12.5" y1="7.833333333333336" y2="5.222222222222225"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.5" x2="12.5" y1="3.222222222222225" y2="5.222222222222225"/>
  </symbol>
  <symbol id="Accessory:计量PT_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15.08333333333333" xlink:href="#terminal" y="0.7499999999999947"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.96,15.58) scale(1,1) translate(0,0)" width="5.08" x="5.42" y="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="8" y1="0.5" y2="17.5"/>
   <path d="M 7 13.4167 L 8 17.5 L 9 13.4167" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="11" y1="24.75" y2="24.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.333333333333334" x2="8.666666666666668" y1="27.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="8" y1="20.83333333333334" y2="24.83333333333334"/>
   <path d="M 22.3333 5.16667 L 22.3333 0.5 L 8 0.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.36725000000001" x2="22.36725000000001" y1="7.981583793738499" y2="11.6279926335175"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="10" y1="26.04166666666666" y2="26.04166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.36725000000001" x2="25.82975" y1="11.62799263351749" y2="14.16942909760589"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.36725000000001" x2="19.13558333333334" y1="11.6279926335175" y2="14.27992633517496"/>
   <ellipse cx="22.58" cy="22.04" fill-opacity="0" rx="6.93" ry="6.63" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="22.49" cy="11.71" fill-opacity="0" rx="6.93" ry="6.63" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.36725000000001" x2="25.82975" y1="22.01473296500921" y2="24.5561694290976"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.36725000000001" x2="22.36725000000001" y1="18.69981583793738" y2="22.0147329650092"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.36725000000001" x2="19.13558333333334" y1="22.01473296500919" y2="24.66666666666665"/>
  </symbol>
  <symbol id="Accessory:母线电压互感器11_0" viewBox="0,0,45,48">
   <use terminal-index="0" type="0" x="25.25" xlink:href="#terminal" y="47.78457520218115"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.5,30.95) scale(1,-1) translate(0,-1330.05)" width="6" x="10.5" y="23.95"/>
   <path d="M 8.64167 11.4096 L 4.64167 11.4096 L 6.50709 7.52624 L 7.67376 9.52624 L 8.64167 11.4096" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="13.5" y1="19.78457520218114" y2="40.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.25" x2="25.25" y1="40.5" y2="47.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="37.25" x2="13.58333333333333" y1="40.5" y2="40.5"/>
   <ellipse cx="13.56" cy="5.42" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799663" x2="13.56620079799663" y1="3.155388266900378" y2="5.82957386674455"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799661" x2="10.88240159599323" y1="5.829573866744575" y2="7.166666666666657"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799662" x2="16.25" y1="5.829573866744575" y2="7.166666666666657"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,36.96,25.94) scale(1,-1) translate(0,-1129.72)" width="7.58" x="33.17" y="17.87"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="34.01790853551448" y2="24.11790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="40.43457520218114" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.7861111111111" x2="39.3" y1="11.60124186884782" y2="11.60124186884782"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.82777777777777" x2="39.93888888888888" y1="12.98282081621623" y2="12.98282081621623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.55" x2="41.21666666666666" y1="14.36439976358464" y2="14.36439976358464"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="34.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="18.01790853551448" y2="14.41790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="40.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <ellipse cx="13.59" cy="15.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.51" cy="10.09" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.67" cy="10.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490864" x2="10.91228003290526" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="13.59607923490865" y1="12.74178419246191" y2="15.41596979230608"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51274590157532" x2="23.19654510357869" y1="10.49930312563944" y2="11.83639592556152"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51274590157532" x2="20.51274590157532" y1="7.825117525795246" y2="10.49930312563942"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="16.27987843691203" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5127459015753" x2="17.82894669957193" y1="10.49930312563944" y2="11.83639592556152"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="4.583333333333332" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV盈江昆钢榕全水泥总降" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="71.94" y="327" zvalue="31"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="1" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" width="72.88" x="71.94" y="327" zvalue="31"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" writing-mode="lr" x="108.38" xml:space="preserve" y="343.5" zvalue="31">信号一览</text>
  <image height="60" id="2" preserveAspectRatio="xMidYMid slice" width="269.25" x="66.95999999999999" xlink:href="logo.png" y="45.64"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.589,75.6429) scale(1,1) translate(0,0)" writing-mode="lr" x="201.59" xml:space="preserve" y="79.14" zvalue="138"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,203.298,75.3332) scale(1,1) translate(9.43293e-15,0)" writing-mode="lr" x="203.3" xml:space="preserve" y="82.83" zvalue="139">盈江昆钢榕全水泥总降</text>
  <line fill="none" id="26" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="388.75" x2="388.75" y1="11" y2="1039.25" zvalue="6"/>
  <line fill="none" id="24" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.22692454998003" x2="331.8381846035992" y1="168.8779458827686" y2="168.8779458827686" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="927.8127909390723" y2="927.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="979.1522909390724" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="361.3219285714284" y1="927.8127909390723" y2="927.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="361.3219285714284" y1="979.1522909390724" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3219285714284" x2="361.3219285714284" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5593285714284" x2="279.5593285714284" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="279.5592285714284" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3213285714285" x2="361.3213285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5593285714284" x2="279.5593285714284" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="279.5592285714284" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3213285714285" x2="361.3213285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,92.7828,957.386) scale(1,1) translate(-1.07783e-13,1.05089e-13)" writing-mode="lr" x="60.68" xml:space="preserve" y="961.89" zvalue="10">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,132.899,994.755) scale(1,1) translate(-6.25312e-14,-1.52933e-12)" writing-mode="lr" x="70.40000000000001" xml:space="preserve" y="999.25" zvalue="11">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,276.202,995.755) scale(1,1) translate(1.38222e-13,-1.53089e-12)" writing-mode="lr" x="207.5" xml:space="preserve" y="1000.25" zvalue="12">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.9923,1024.31) scale(1,1) translate(-6.10858e-14,-1.57527e-12)" writing-mode="lr" x="83.98999999999999" xml:space="preserve" y="1028.81" zvalue="13">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,280.836,1022.31) scale(1,1) translate(-4.58902e-14,1.12298e-13)" writing-mode="lr" x="206.67" xml:space="preserve" y="1026.81" zvalue="14">更新日期  </text>
  <line fill="none" id="17" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="54.60611355802337" x2="359.2173736116425" y1="623.6445306693694" y2="623.6445306693694" zvalue="15"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,97.6707,640.686) scale(1,1) translate(5.99964e-15,-1.38109e-13)" writing-mode="lr" x="97.67070806204492" xml:space="preserve" y="645.1863811688671" zvalue="17">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="172.1071428571429" y2="172.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="172.1071428571429" y2="172.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="290.6071428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="290.6071428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.32162857142839" x2="85.09612857142838" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.32162857142839" x2="85.09612857142838" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.32162857142839" x2="39.32162857142839" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09612857142838" x2="85.09612857142838" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09562857142839" x2="143.9020285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09562857142839" x2="143.9020285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09562857142839" x2="85.09562857142839" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9020285714284" x2="143.9020285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9023285714284" x2="202.7087285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9023285714284" x2="202.7087285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9023285714284" x2="143.9023285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7087285714284" x2="202.7087285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7086285714284" x2="261.5150285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7086285714284" x2="261.5150285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7086285714284" x2="202.7086285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="261.5150285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="320.3214285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="320.3214285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="261.5150285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3214285714284" x2="320.3214285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3216285714284" x2="379.1280285714283" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3216285714284" x2="379.1280285714283" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3216285714284" x2="320.3216285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379.1280285714283" x2="379.1280285714283" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.32162857142839" x2="85.09612857142838" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.32162857142839" x2="85.09612857142838" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.32162857142839" x2="39.32162857142839" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09612857142838" x2="85.09612857142838" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09562857142839" x2="143.9020285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09562857142839" x2="143.9020285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09562857142839" x2="85.09562857142839" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9020285714284" x2="143.9020285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9023285714284" x2="202.7087285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9023285714284" x2="202.7087285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9023285714284" x2="143.9023285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7087285714284" x2="202.7087285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7086285714284" x2="261.5150285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7086285714284" x2="261.5150285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7086285714284" x2="202.7086285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="261.5150285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="320.3214285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="320.3214285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="261.5150285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3214285714284" x2="320.3214285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3216285714284" x2="379.1280285714283" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3216285714284" x2="379.1280285714283" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3216285714284" x2="320.3216285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379.1280285714283" x2="379.1280285714283" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.32162857142839" x2="85.09612857142838" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.32162857142839" x2="85.09612857142838" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.32162857142839" x2="39.32162857142839" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09612857142838" x2="85.09612857142838" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09562857142839" x2="143.9020285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09562857142839" x2="143.9020285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09562857142839" x2="85.09562857142839" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9020285714284" x2="143.9020285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9023285714284" x2="202.7087285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9023285714284" x2="202.7087285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9023285714284" x2="143.9023285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7087285714284" x2="202.7087285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7086285714284" x2="261.5150285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7086285714284" x2="261.5150285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7086285714284" x2="202.7086285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="261.5150285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="320.3214285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="320.3214285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="261.5150285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3214285714284" x2="320.3214285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3216285714284" x2="379.1280285714283" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3216285714284" x2="379.1280285714283" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3216285714284" x2="320.3216285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379.1280285714283" x2="379.1280285714283" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.32162857142839" x2="85.09612857142838" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.32162857142839" x2="85.09612857142838" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.32162857142839" x2="39.32162857142839" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09612857142838" x2="85.09612857142838" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09562857142839" x2="143.9020285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09562857142839" x2="143.9020285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09562857142839" x2="85.09562857142839" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9020285714284" x2="143.9020285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9023285714284" x2="202.7087285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9023285714284" x2="202.7087285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9023285714284" x2="143.9023285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7087285714284" x2="202.7087285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7086285714284" x2="261.5150285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7086285714284" x2="261.5150285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7086285714284" x2="202.7086285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="261.5150285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="320.3214285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="320.3214285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="261.5150285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3214285714284" x2="320.3214285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3216285714284" x2="379.1280285714283" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3216285714284" x2="379.1280285714283" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3216285714284" x2="320.3216285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379.1280285714283" x2="379.1280285714283" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.32162857142839" x2="85.09612857142838" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.32162857142839" x2="85.09612857142838" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.32162857142839" x2="39.32162857142839" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09612857142838" x2="85.09612857142838" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09562857142839" x2="143.9020285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09562857142839" x2="143.9020285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09562857142839" x2="85.09562857142839" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9020285714284" x2="143.9020285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9023285714284" x2="202.7087285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9023285714284" x2="202.7087285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9023285714284" x2="143.9023285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7087285714284" x2="202.7087285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7086285714284" x2="261.5150285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7086285714284" x2="261.5150285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7086285714284" x2="202.7086285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="261.5150285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="320.3214285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="320.3214285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="261.5150285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3214285714284" x2="320.3214285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3216285714284" x2="379.1280285714283" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3216285714284" x2="379.1280285714283" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3216285714284" x2="320.3216285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379.1280285714283" x2="379.1280285714283" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.32162857142839" x2="85.09612857142838" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.32162857142839" x2="85.09612857142838" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.32162857142839" x2="39.32162857142839" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09612857142838" x2="85.09612857142838" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09562857142839" x2="143.9020285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09562857142839" x2="143.9020285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="85.09562857142839" x2="85.09562857142839" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9020285714284" x2="143.9020285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9023285714284" x2="202.7087285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9023285714284" x2="202.7087285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.9023285714284" x2="143.9023285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7087285714284" x2="202.7087285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7086285714284" x2="261.5150285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7086285714284" x2="261.5150285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="202.7086285714284" x2="202.7086285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="261.5150285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="320.3214285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="320.3214285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="261.5150285714284" x2="261.5150285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3214285714284" x2="320.3214285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3216285714284" x2="379.1280285714283" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3216285714284" x2="379.1280285714283" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="320.3216285714284" x2="320.3216285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="379.1280285714283" x2="379.1280285714283" y1="589.1071428571429" y2="613.7865428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" x="114.5390625" xml:space="preserve" y="467.609375" zvalue="20">35kV母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="114.5390625" xml:space="preserve" y="484.609375" zvalue="20">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,64.3214,502.607) scale(1,1) translate(0,0)" writing-mode="lr" x="64.32142857142844" xml:space="preserve" y="507.1071428571429" zvalue="23">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,64.3214,528.107) scale(1,1) translate(0,0)" writing-mode="lr" x="64.32142857142844" xml:space="preserve" y="532.6071428571429" zvalue="24">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,64.3214,553.607) scale(1,1) translate(0,0)" writing-mode="lr" x="64.32142857142844" xml:space="preserve" y="558.1071428571429" zvalue="25">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,64.3214,579.107) scale(1,1) translate(0,0)" writing-mode="lr" x="64.32142857142844" xml:space="preserve" y="583.6071428571429" zvalue="26">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,64.3214,604.607) scale(1,1) translate(0,0)" writing-mode="lr" x="64.32142857142844" xml:space="preserve" y="609.1071428571429" zvalue="27">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.3214,186.107) scale(1,1) translate(0,0)" writing-mode="lr" x="54.32" xml:space="preserve" y="190.61" zvalue="28">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.571,186.107) scale(1,1) translate(0,0)" writing-mode="lr" x="240.57" xml:space="preserve" y="190.61" zvalue="29">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,65.0089,210.357) scale(1,1) translate(0,0)" writing-mode="lr" x="65.01000000000001" xml:space="preserve" y="214.86" zvalue="30">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,208.399,340.5) scale(1,1) translate(0,0)" writing-mode="lr" x="208.4" xml:space="preserve" y="345" zvalue="379">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,296.399,340.5) scale(1,1) translate(0,0)" writing-mode="lr" x="296.4" xml:space="preserve" y="345" zvalue="380">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,978.662,112.444) scale(1,1) translate(0,0)" writing-mode="lr" x="978.66" xml:space="preserve" y="116.94" zvalue="518">35kV允榕线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,956.961,365.889) scale(1,1) translate(0,0)" writing-mode="lr" x="956.96" xml:space="preserve" y="370.39" zvalue="780">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,957.204,257.889) scale(1,1) translate(0,0)" writing-mode="lr" x="957.2" xml:space="preserve" y="262.39" zvalue="799">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,952.274,311.333) scale(1,1) translate(0,0)" writing-mode="lr" x="952.27" xml:space="preserve" y="315.83" zvalue="827">351</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,929,347.556) scale(1,1) translate(0,0)" writing-mode="lr" x="929" xml:space="preserve" y="352.06" zvalue="859">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,930,284.222) scale(1,1) translate(-2.04614e-13,0)" writing-mode="lr" x="930" xml:space="preserve" y="288.72" zvalue="865">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,930,228.889) scale(1,1) translate(0,0)" writing-mode="lr" x="930" xml:space="preserve" y="233.39" zvalue="868">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1012.89,206.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1012.89" xml:space="preserve" y="211.28" zvalue="872">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,245.005,956.275) scale(1,1) translate(0,1.04966e-13)" writing-mode="lr" x="132.9" xml:space="preserve" y="960.78" zvalue="877">RongQuan-01-2018</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1122.5,389) scale(1,1) translate(0,0)" writing-mode="lr" x="1122.5" xml:space="preserve" y="393.5" zvalue="879">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1342.66,112.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1342.66" xml:space="preserve" y="116.94" zvalue="882">35kV弄榕线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1320.96,365.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1320.96" xml:space="preserve" y="370.39" zvalue="884">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1321.2,257.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1321.2" xml:space="preserve" y="262.39" zvalue="886">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1316.27,311.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1316.27" xml:space="preserve" y="315.83" zvalue="889">352</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1293,347.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1293" xml:space="preserve" y="352.06" zvalue="891">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1294,284.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1294" xml:space="preserve" y="288.72" zvalue="897">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1294,228.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1294" xml:space="preserve" y="233.39" zvalue="900">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1376.89,206.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1376.89" xml:space="preserve" y="211.28" zvalue="903">9</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,693,190.5) scale(1,1) translate(0,0)" writing-mode="lr" x="693" xml:space="preserve" y="195" zvalue="908">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,666.204,334.889) scale(1,1) translate(0,0)" writing-mode="lr" x="666.2" xml:space="preserve" y="339.39" zvalue="911">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,637,369.889) scale(1,1) translate(0,0)" writing-mode="lr" x="637" xml:space="preserve" y="374.39" zvalue="912">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,637,291.111) scale(1,1) translate(0,0)" writing-mode="lr" x="637" xml:space="preserve" y="295.61" zvalue="913">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,741,558.5) scale(1,1) translate(0,0)" writing-mode="lr" x="741" xml:space="preserve" y="563" zvalue="918">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,715.704,442.889) scale(1,1) translate(0,0)" writing-mode="lr" x="715.7" xml:space="preserve" y="447.39" zvalue="921">3811</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,686,469.889) scale(1,1) translate(0,-2.04232e-13)" writing-mode="lr" x="686" xml:space="preserve" y="474.39" zvalue="922">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" x="943" xml:space="preserve" y="583.25" zvalue="926">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="943" xml:space="preserve" y="600.25" zvalue="926">12500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,997.204,447.889) scale(1,1) translate(0,0)" writing-mode="lr" x="997.2" xml:space="preserve" y="452.39" zvalue="929">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1030.27,506.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1030.27" xml:space="preserve" y="510.83" zvalue="930">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,977,461.889) scale(1,1) translate(0,0)" writing-mode="lr" x="977" xml:space="preserve" y="466.39" zvalue="931">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,932.5,694) scale(1,1) translate(0,0)" writing-mode="lr" x="932.5" xml:space="preserve" y="698.5" zvalue="938">10.5kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,809,898.5) scale(1,1) translate(0,0)" writing-mode="lr" x="809" xml:space="preserve" y="903" zvalue="941">#1无功补偿</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,837,763) scale(1,1) translate(0,0)" writing-mode="lr" x="837" xml:space="preserve" y="767.5" zvalue="943">054</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" x="884" xml:space="preserve" y="905.75" zvalue="946">#1水泥磨配电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="884" xml:space="preserve" y="922.75" zvalue="946">站</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="151" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,912,762) scale(1,1) translate(0,0)" writing-mode="lr" x="912" xml:space="preserve" y="766.5" zvalue="949">055</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" x="960" xml:space="preserve" y="905.75" zvalue="953">#2水泥磨配电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="960" xml:space="preserve" y="922.75" zvalue="953">站</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,988,762) scale(1,1) translate(0,0)" writing-mode="lr" x="988" xml:space="preserve" y="766.5" zvalue="955">056</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" x="1107" xml:space="preserve" y="583.25" zvalue="960">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1107" xml:space="preserve" y="600.25" zvalue="960">12500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1162.2,447.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1162.2" xml:space="preserve" y="452.39" zvalue="962">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="205" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1195.77,507.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1195.77" xml:space="preserve" y="511.83" zvalue="963">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1141,464.889) scale(1,1) translate(0,1.51508e-13)" writing-mode="lr" x="1141" xml:space="preserve" y="469.39" zvalue="964">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="175" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1107,695) scale(1,1) translate(0,0)" writing-mode="lr" x="1107" xml:space="preserve" y="699.5" zvalue="969">10.5kVⅢ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1038,669) scale(1,1) translate(0,0)" writing-mode="lr" x="1038" xml:space="preserve" y="673.5" zvalue="972">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1199,667) scale(1,1) translate(0,0)" writing-mode="lr" x="1199" xml:space="preserve" y="671.5" zvalue="976">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1121,765) scale(1,1) translate(0,0)" writing-mode="lr" x="1121" xml:space="preserve" y="769.5" zvalue="979">023</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1067,780) scale(1,1) translate(0,0)" writing-mode="lr" x="1067" xml:space="preserve" y="784.5" zvalue="982">0232</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="189" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1164,908.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1164" xml:space="preserve" y="913.25" zvalue="986">窑头配电站</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1192,762) scale(1,1) translate(0,0)" writing-mode="lr" x="1192" xml:space="preserve" y="766.5" zvalue="988">057</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1437,667) scale(1,1) translate(0,0)" writing-mode="lr" x="1437" xml:space="preserve" y="671.5" zvalue="995">058</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1766,692) scale(1,1) translate(0,0)" writing-mode="lr" x="1766" xml:space="preserve" y="696.5" zvalue="1000">10.5kVⅣ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="209" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1507,667) scale(1,1) translate(0,0)" writing-mode="lr" x="1507" xml:space="preserve" y="671.5" zvalue="1002">063</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="214" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1236,908.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1236" xml:space="preserve" y="913.25" zvalue="1007">原料磨配室</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="213" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1264,762) scale(1,1) translate(0,0)" writing-mode="lr" x="1264" xml:space="preserve" y="766.5" zvalue="1009">059</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="220" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1308,908.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1308" xml:space="preserve" y="913.25" zvalue="1014">#2站用电</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="219" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1336,762) scale(1,1) translate(0,0)" writing-mode="lr" x="1336" xml:space="preserve" y="766.5" zvalue="1016">061</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="226" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1387,898.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1387" xml:space="preserve" y="903" zvalue="1021">#2无功补偿</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1415,763) scale(1,1) translate(0,0)" writing-mode="lr" x="1415" xml:space="preserve" y="767.5" zvalue="1023">062</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1513,898.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1513" xml:space="preserve" y="903" zvalue="1035">10kVⅣ段母线PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1485.5,761) scale(1,1) translate(0,0)" writing-mode="lr" x="1485.5" xml:space="preserve" y="765.5" zvalue="1037">0904</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1609,896.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1609" xml:space="preserve" y="901" zvalue="1040">厂用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1637,764) scale(1,1) translate(0,0)" writing-mode="lr" x="1637" xml:space="preserve" y="768.5" zvalue="1043">064</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1568,829) scale(1,1) translate(0,0)" writing-mode="lr" x="1568" xml:space="preserve" y="833.5" zvalue="1046">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="195" stroke="rgb(255,255,255)" text-anchor="middle" x="1740.5" xml:space="preserve" y="985.25" zvalue="1048">1号发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="195" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1740.5" xml:space="preserve" y="1002.25" zvalue="1048">4500kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1767.5,762) scale(1,1) translate(0,0)" writing-mode="lr" x="1767.5" xml:space="preserve" y="766.5" zvalue="1051">065</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1758.5,869) scale(1,1) translate(0,0)" writing-mode="lr" x="1758.5" xml:space="preserve" y="873.5" zvalue="1054">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="244" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1709,881) scale(1,1) translate(0,0)" writing-mode="lr" x="1709" xml:space="preserve" y="885.5" zvalue="1058">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="248" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1677,953) scale(1,1) translate(0,0)" writing-mode="lr" x="1677" xml:space="preserve" y="957.5" zvalue="1061">励磁变压器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1648.5,880) scale(1,1) translate(0,0)" writing-mode="lr" x="1648.5" xml:space="preserve" y="884.5" zvalue="1063">0912</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1807.75,965.071) scale(1,1) translate(0,0)" writing-mode="lr" x="1807.75" xml:space="preserve" y="969.5700000000001" zvalue="1067">发电机PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1810,726.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1810" xml:space="preserve" y="731" zvalue="1069">发电机励磁TV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="261" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1783.67,806) scale(1,1) translate(0,0)" writing-mode="lr" x="1783.67" xml:space="preserve" y="810.5" zvalue="1072">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="265" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1781.5,882) scale(1,1) translate(0,0)" writing-mode="lr" x="1781.5" xml:space="preserve" y="886.5" zvalue="1076">0913</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="269" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,508,692) scale(1,1) translate(0,0)" writing-mode="lr" x="508" xml:space="preserve" y="696.5" zvalue="1080">10.5kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="273" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,722,813) scale(1,1) translate(0,0)" writing-mode="lr" x="722" xml:space="preserve" y="817.5" zvalue="1083">012</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="277" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,556,896.75) scale(1,1) translate(0,0)" writing-mode="lr" x="556" xml:space="preserve" y="901.25" zvalue="1087">生活Ⅰ回</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="276" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,579,761) scale(1,1) translate(0,0)" writing-mode="lr" x="579" xml:space="preserve" y="765.5" zvalue="1090">052</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,635,896.75) scale(1,1) translate(0,0)" writing-mode="lr" x="635" xml:space="preserve" y="901.25" zvalue="1094">生活Ⅱ回</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="282" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,658,761) scale(1,1) translate(0,0)" writing-mode="lr" x="658" xml:space="preserve" y="765.5" zvalue="1097">053</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,476,944.75) scale(1,1) translate(0,0)" writing-mode="lr" x="476" xml:space="preserve" y="949.25" zvalue="1101">备用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,500.75,761) scale(1,1) translate(0,0)" writing-mode="lr" x="500.75" xml:space="preserve" y="765.5" zvalue="1104">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="295" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,460,837) scale(1,1) translate(0,0)" writing-mode="lr" x="460" xml:space="preserve" y="841.5" zvalue="1108">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="301" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,436,913) scale(1,1) translate(0,0)" writing-mode="lr" x="436" xml:space="preserve" y="917.5" zvalue="1112">计量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="304" stroke="rgb(255,255,255)" text-anchor="middle" x="613.25" xml:space="preserve" y="564.6614583333334" zvalue="1114">10.5kVⅠ段母线电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="304" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="613.25" xml:space="preserve" y="581.6614583333334" zvalue="1114">压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="308" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,583.5,663) scale(1,1) translate(0,0)" writing-mode="lr" x="583.5" xml:space="preserve" y="667.5" zvalue="1117">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="312" stroke="rgb(255,255,255)" text-anchor="middle" x="825.25" xml:space="preserve" y="564.6614583333334" zvalue="1121">10.5kVⅡ段母线电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="312" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="825.25" xml:space="preserve" y="581.6614583333334" zvalue="1121">压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="311" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,795.5,663) scale(1,1) translate(0,0)" writing-mode="lr" x="795.5" xml:space="preserve" y="667.5" zvalue="1123">0902</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="318" stroke="rgb(255,255,255)" text-anchor="middle" x="1329.25" xml:space="preserve" y="567.6614583333334" zvalue="1128">10.5kVⅢ段母线电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="318" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1329.25" xml:space="preserve" y="584.6614583333334" zvalue="1128">压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="317" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1299.5,666) scale(1,1) translate(0,0)" writing-mode="lr" x="1299.5" xml:space="preserve" y="670.5" zvalue="1130">0903</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" x="172.375" xml:space="preserve" y="468.5" zvalue="1141">10.5 </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="172.375" xml:space="preserve" y="485.5" zvalue="1141">kVⅠ母</text>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="34">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="34" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,161.571,210.968) scale(1,1) translate(0,0)" writing-mode="lr" x="161.72" xml:space="preserve" y="217.4" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126463471620" ObjectName="F"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="33" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,161.571,186) scale(1,1) translate(0,0)" writing-mode="lr" x="161.72" xml:space="preserve" y="192.43" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126494797828" ObjectName=""/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="35" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,343.571,185.857) scale(1,1) translate(0,0)" writing-mode="lr" x="343.72" xml:space="preserve" y="192.29" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126494863367" ObjectName=""/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,118.071,528.593) scale(1,1) translate(0,0)" writing-mode="lr" x="118.2" xml:space="preserve" y="533.5" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126463078404" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="143" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,118.071,553.718) scale(1,1) translate(0,-1.20619e-13)" writing-mode="lr" x="118.2" xml:space="preserve" y="558.63" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126463143940" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,119.071,577.843) scale(1,1) translate(0,0)" writing-mode="lr" x="119.2" xml:space="preserve" y="582.75" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126463209476" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,119.5,503.468) scale(1,1) translate(0,1.09461e-13)" writing-mode="lr" x="119.63" xml:space="preserve" y="508.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126463340548" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="148" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,118.321,602.968) scale(1,1) translate(0,0)" writing-mode="lr" x="118.45" xml:space="preserve" y="607.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126463537156" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="31" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,980.955,44.782) scale(1,1) translate(0,0)" writing-mode="lr" x="980.49" xml:space="preserve" y="49.56" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126461505540" ObjectName="P"/>
   </metadata>
  </g>
  <g id="109">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="109" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,980.955,63.782) scale(1,1) translate(0,0)" writing-mode="lr" x="980.49" xml:space="preserve" y="68.56" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126461571076" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="196">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="196" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,980.955,82.782) scale(1,1) translate(0,0)" writing-mode="lr" x="980.49" xml:space="preserve" y="87.56" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126461636612" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="246" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1338.96,53.782) scale(1,1) translate(0,0)" writing-mode="lr" x="1338.49" xml:space="preserve" y="58.56" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126464126980" ObjectName="P"/>
   </metadata>
  </g>
  <g id="249">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="249" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1338.96,73.782) scale(1,1) translate(0,0)" writing-mode="lr" x="1338.49" xml:space="preserve" y="78.56" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126464192516" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="256" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1338.96,93.782) scale(1,1) translate(0,0)" writing-mode="lr" x="1338.49" xml:space="preserve" y="98.56" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126464258052" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="270">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="270" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,174.5,529.486) scale(1,1) translate(0,0)" writing-mode="lr" x="174.63" xml:space="preserve" y="534.4" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126489030660" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="271">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="271" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,174.5,554.611) scale(1,1) translate(0,-1.20817e-13)" writing-mode="lr" x="174.63" xml:space="preserve" y="559.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126489096196" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="291">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="291" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,175.5,578.736) scale(1,1) translate(0,0)" writing-mode="lr" x="175.63" xml:space="preserve" y="583.65" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126489161732" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="259">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="259" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,175.929,504.361) scale(1,1) translate(0,1.09659e-13)" writing-mode="lr" x="176.06" xml:space="preserve" y="509.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126489292804" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="298">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="298" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,174.75,603.861) scale(1,1) translate(0,0)" writing-mode="lr" x="174.88" xml:space="preserve" y="608.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126489489412" ObjectName="U0"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="132">
   <use height="30" transform="rotate(0,329.768,340.5) scale(0.708333,0.665547) translate(131.412,166.093)" width="30" x="319.14" xlink:href="#State:红绿圆(方形)_0" y="330.52" zvalue="381"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374890041347" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.768,340.5) scale(0.708333,0.665547) translate(131.412,166.093)" width="30" x="319.14" y="330.52"/></g>
  <g id="94">
   <use height="30" transform="rotate(0,247.143,340.5) scale(0.708333,0.665547) translate(97.3897,166.093)" width="30" x="236.52" xlink:href="#State:红绿圆(方形)_0" y="330.52" zvalue="382"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562951207059462" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,247.143,340.5) scale(0.708333,0.665547) translate(97.3897,166.093)" width="30" x="236.52" y="330.52"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="82">
   <use class="kv35" height="30" transform="rotate(0,982.294,366.889) scale(1.11111,0.814815) translate(-97.3961,80.6061)" width="15" x="973.9606398781284" xlink:href="#Disconnector:刀闸_0" y="354.6666660308838" zvalue="778"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449994031109" ObjectName="35kV允榕线3511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449994031109"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,982.294,366.889) scale(1.11111,0.814815) translate(-97.3961,80.6061)" width="15" x="973.9606398781284" y="354.6666660308838"/></g>
  <g id="86">
   <use class="kv35" height="30" transform="rotate(0,982.538,258.889) scale(1.11111,0.814815) translate(-97.4204,56.0606)" width="15" x="974.2042617509935" xlink:href="#Disconnector:刀闸_0" y="246.6666660308838" zvalue="798"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449994096645" ObjectName="35kV允榕线3516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449994096645"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,982.538,258.889) scale(1.11111,0.814815) translate(-97.4204,56.0606)" width="15" x="974.2042617509935" y="246.6666660308838"/></g>
  <g id="88">
   <use class="kv35" height="30" transform="rotate(90,1010.44,228.889) scale(1.11111,1.11111) translate(-100.211,-21.2222)" width="15" x="1002.111111111111" xlink:href="#Disconnector:令克_0" y="212.2222222222222" zvalue="871"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449994620933" ObjectName="35kV允榕线3519隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449994620933"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1010.44,228.889) scale(1.11111,1.11111) translate(-100.211,-21.2222)" width="15" x="1002.111111111111" y="212.2222222222222"/></g>
  <g id="74">
   <use class="kv35" height="30" transform="rotate(0,1346.29,366.889) scale(1.11111,0.814815) translate(-133.796,80.6061)" width="15" x="1337.960639878128" xlink:href="#Disconnector:刀闸_0" y="354.6666660308838" zvalue="883"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449995276293" ObjectName="35kV弄榕线3521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449995276293"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1346.29,366.889) scale(1.11111,0.814815) translate(-133.796,80.6061)" width="15" x="1337.960639878128" y="354.6666660308838"/></g>
  <g id="73">
   <use class="kv35" height="30" transform="rotate(0,1346.54,258.889) scale(1.11111,0.814815) translate(-133.82,56.0606)" width="15" x="1338.204261750994" xlink:href="#Disconnector:刀闸_0" y="246.6666660308838" zvalue="885"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449995210757" ObjectName="35kV弄榕线3526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449995210757"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1346.54,258.889) scale(1.11111,0.814815) translate(-133.82,56.0606)" width="15" x="1338.204261750994" y="246.6666660308838"/></g>
  <g id="52">
   <use class="kv35" height="30" transform="rotate(90,1374.44,228.889) scale(1.11111,1.11111) translate(-136.611,-21.2222)" width="15" x="1366.111111111111" xlink:href="#Disconnector:令克_0" y="212.2222222222222" zvalue="902"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449994686469" ObjectName="35kV弄榕线3529隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449994686469"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1374.44,228.889) scale(1.11111,1.11111) translate(-136.611,-21.2222)" width="15" x="1366.111111111111" y="212.2222222222222"/></g>
  <g id="97">
   <use class="kv35" height="30" transform="rotate(0,690.538,335.889) scale(1.11111,0.814815) translate(-68.2204,73.5606)" width="15" x="682.2042617509935" xlink:href="#Disconnector:刀闸_0" y="323.6666660308838" zvalue="910"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449995472901" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449995472901"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,690.538,335.889) scale(1.11111,0.814815) translate(-68.2204,73.5606)" width="15" x="682.2042617509935" y="323.6666660308838"/></g>
  <g id="110">
   <use class="kv35" height="30" transform="rotate(0,739.538,443.889) scale(1.11111,0.814815) translate(-73.1204,98.1061)" width="15" x="731.2042617509935" xlink:href="#Disconnector:刀闸_0" y="431.6666660308837" zvalue="920"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449995866118" ObjectName="#1站用变3811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449995866118"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,739.538,443.889) scale(1.11111,0.814815) translate(-73.1204,98.1061)" width="15" x="731.2042617509935" y="431.6666660308837"/></g>
  <g id="121">
   <use class="kv35" height="30" transform="rotate(0,1009.54,448.889) scale(1.11111,0.814815) translate(-100.12,99.2424)" width="15" x="1001.204261750994" xlink:href="#Disconnector:刀闸_0" y="436.6666660308837" zvalue="928"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449996062725" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449996062725"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1009.54,448.889) scale(1.11111,0.814815) translate(-100.12,99.2424)" width="15" x="1001.204261750994" y="436.6666660308837"/></g>
  <g id="168">
   <use class="kv35" height="30" transform="rotate(0,1174.54,448.889) scale(1.11111,0.814815) translate(-116.62,99.2424)" width="15" x="1166.204261750994" xlink:href="#Disconnector:刀闸_0" y="436.6666660308837" zvalue="961"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449996587013" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449996587013"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1174.54,448.889) scale(1.11111,0.814815) translate(-116.62,99.2424)" width="15" x="1166.204261750994" y="436.6666660308837"/></g>
  <g id="183">
   <use class="v10500" height="36" transform="rotate(90,1064,805) scale(1,1) translate(0,0)" width="14" x="1057" xlink:href="#Disconnector:联体手车刀闸1_0" y="787" zvalue="981"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449996652549" ObjectName="10kV分段0232隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449996652549"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(90,1064,805) scale(1,1) translate(0,0)" width="14" x="1057" y="787"/></g>
  <g id="41">
   <use class="v10500" height="26" transform="rotate(0,1509,762) scale(1,1) translate(0,0)" width="12" x="1503" xlink:href="#Disconnector:20210316_0" y="749" zvalue="1036"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449997242373" ObjectName="10kVⅣ段母线PT0904隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449997242373"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1509,762) scale(1,1) translate(0,0)" width="12" x="1503" y="749"/></g>
  <g id="239">
   <use class="v10500" height="36" transform="rotate(0,1741,871) scale(1,1) translate(0,0)" width="14" x="1734" xlink:href="#Disconnector:手车刀闸_0" y="853" zvalue="1053"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449997570053" ObjectName="1号发电机0656隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449997570053"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1741,871) scale(1,1) translate(0,0)" width="14" x="1734" y="853"/></g>
  <g id="250">
   <use class="v10500" height="36" transform="rotate(0,1676,881) scale(1,1) translate(0,0)" width="14" x="1669" xlink:href="#Disconnector:联体小车刀闸2_0" y="863" zvalue="1062"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449997832197" ObjectName="励磁变压器0912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449997832197"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1676,881) scale(1,1) translate(0,0)" width="14" x="1669" y="863"/></g>
  <g id="260">
   <use class="v10500" height="26" transform="rotate(0,1807.67,806) scale(1,-1) translate(0,-1612)" width="12" x="1801.665932776084" xlink:href="#Disconnector:20210316_0" y="793" zvalue="1071"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449998028805" ObjectName="发电机励磁TV0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449998028805"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1807.67,806) scale(1,-1) translate(0,-1612)" width="12" x="1801.665932776084" y="793"/></g>
  <g id="264">
   <use class="v10500" height="26" transform="rotate(0,1807,878) scale(1,1) translate(0,0)" width="12" x="1801" xlink:href="#Disconnector:20210316_0" y="865" zvalue="1075"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449998094341" ObjectName="发电机PT0913隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449998094341"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1807,878) scale(1,1) translate(0,0)" width="12" x="1801" y="865"/></g>
  <g id="294">
   <use class="v10500" height="36" transform="rotate(0,475,838) scale(1,1) translate(0,0)" width="14" x="468" xlink:href="#Disconnector:手车刀闸_0" y="820" zvalue="1107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449998356485" ObjectName="备用0516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449998356485"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,475,838) scale(1,1) translate(0,0)" width="14" x="468" y="820"/></g>
  <g id="307">
   <use class="v10500" height="26" transform="rotate(0,609,664) scale(1,-1) translate(0,-1328)" width="12" x="603" xlink:href="#Disconnector:小车隔刀熔断器_0" y="651" zvalue="1116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449998553093" ObjectName="10.5kVⅠ段母线0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449998553093"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,609,664) scale(1,-1) translate(0,-1328)" width="12" x="603" y="651"/></g>
  <g id="314">
   <use class="v10500" height="26" transform="rotate(0,821,664) scale(1,-1) translate(0,-1328)" width="12" x="815" xlink:href="#Disconnector:小车隔刀熔断器_0" y="651" zvalue="1122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449998618629" ObjectName="10.5kVⅡ段母线0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449998618629"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,821,664) scale(1,-1) translate(0,-1328)" width="12" x="815" y="651"/></g>
  <g id="320">
   <use class="v10500" height="26" transform="rotate(0,1325,667) scale(1,-1) translate(0,-1334)" width="12" x="1319" xlink:href="#Disconnector:小车隔刀熔断器_0" y="654" zvalue="1129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449998749701" ObjectName="10.5kVⅢ段母线0903隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449998749701"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1325,667) scale(1,-1) translate(0,-1334)" width="12" x="1319" y="654"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="87">
   <path class="kv35" d="M 982.96 178.41 L 982.96 247.07" stroke-width="1" zvalue="803"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="86@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 982.96 178.41 L 982.96 247.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv35" d="M 982.39 355.07 L 982.39 325.13" stroke-width="1" zvalue="860"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="11@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 982.39 355.07 L 982.39 325.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv35" d="M 982.61 297.51 L 982.61 270.9" stroke-width="1" zvalue="861"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="11@0" LinkObjectIDznd="86@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 982.61 297.51 L 982.61 270.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv35" d="M 962.75 341.94 L 982.39 341.94" stroke-width="1" zvalue="862"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@0" LinkObjectIDznd="48" MaxPinNum="2"/>
   </metadata>
  <path d="M 962.75 341.94 L 982.39 341.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv35" d="M 962.75 281.94 L 982.61 281.94" stroke-width="1" zvalue="865"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="54" MaxPinNum="2"/>
   </metadata>
  <path d="M 962.75 281.94 L 982.61 281.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv35" d="M 962.75 228.16 L 982.96 228.16" stroke-width="1" zvalue="868"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@0" LinkObjectIDznd="87" MaxPinNum="2"/>
   </metadata>
  <path d="M 962.75 228.16 L 982.96 228.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv35" d="M 1031.98 228.98 L 1025.17 228.98" stroke-width="1" zvalue="872"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@0" LinkObjectIDznd="88@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1031.98 228.98 L 1025.17 228.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv35" d="M 996.83 228.8 L 982.96 228.8" stroke-width="1" zvalue="873"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@1" LinkObjectIDznd="81" MaxPinNum="2"/>
   </metadata>
  <path d="M 996.83 228.8 L 982.96 228.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="kv35" d="M 982.36 378.9 L 982.36 405" stroke-width="1" zvalue="879"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@1" LinkObjectIDznd="28@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 982.36 378.9 L 982.36 405" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv35" d="M 1346.96 178.41 L 1346.96 247.07" stroke-width="1" zvalue="887"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75@0" LinkObjectIDznd="73@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1346.96 178.41 L 1346.96 247.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv35" d="M 1346.39 355.07 L 1346.39 325.13" stroke-width="1" zvalue="892"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="69@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1346.39 355.07 L 1346.39 325.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv35" d="M 1346.61 297.51 L 1346.61 270.9" stroke-width="1" zvalue="893"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="73@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1346.61 297.51 L 1346.61 270.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv35" d="M 1326.75 341.94 L 1346.39 341.94" stroke-width="1" zvalue="894"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="67" MaxPinNum="2"/>
   </metadata>
  <path d="M 1326.75 341.94 L 1346.39 341.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv35" d="M 1326.75 281.94 L 1346.61 281.94" stroke-width="1" zvalue="896"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@0" LinkObjectIDznd="66" MaxPinNum="2"/>
   </metadata>
  <path d="M 1326.75 281.94 L 1346.61 281.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv35" d="M 1326.75 228.16 L 1346.96 228.16" stroke-width="1" zvalue="899"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="72" MaxPinNum="2"/>
   </metadata>
  <path d="M 1326.75 228.16 L 1346.96 228.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv35" d="M 1395.98 228.98 L 1389.17 228.98" stroke-width="1" zvalue="904"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="52@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1395.98 228.98 L 1389.17 228.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv35" d="M 1360.83 228.8 L 1346.96 228.8" stroke-width="1" zvalue="905"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@1" LinkObjectIDznd="72" MaxPinNum="2"/>
   </metadata>
  <path d="M 1360.83 228.8 L 1346.96 228.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv35" d="M 1346.36 378.9 L 1346.36 405" stroke-width="1" zvalue="906"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@1" LinkObjectIDznd="28@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1346.36 378.9 L 1346.36 405" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv35" d="M 690.5 267.33 L 690.5 324.07" stroke-width="1" zvalue="913"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@0" LinkObjectIDznd="97@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 690.5 267.33 L 690.5 324.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv35" d="M 690.61 347.9 L 690.61 405" stroke-width="1" zvalue="914"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@1" LinkObjectIDznd="28@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 690.61 347.9 L 690.61 405" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv35" d="M 672.75 292.16 L 690.5 292.16" stroke-width="1" zvalue="915"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="103" MaxPinNum="2"/>
   </metadata>
  <path d="M 672.75 292.16 L 690.5 292.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv35" d="M 672.75 368.94 L 690.61 368.94" stroke-width="1" zvalue="916"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="104" MaxPinNum="2"/>
   </metadata>
  <path d="M 672.75 368.94 L 690.61 368.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv35" d="M 740.4 487.3 L 740.4 455.9" stroke-width="1" zvalue="922"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="110@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 740.4 487.3 L 740.4 455.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv35" d="M 739.64 432.07 L 739.64 405" stroke-width="1" zvalue="923"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="28@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 739.64 432.07 L 739.64 405" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="kv35" d="M 719.75 469.94 L 740.4 469.94" stroke-width="1" zvalue="924"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="113" MaxPinNum="2"/>
   </metadata>
  <path d="M 719.75 469.94 L 740.4 469.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv35" d="M 1009.64 437.07 L 1009.64 405" stroke-width="1" zvalue="932"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@0" LinkObjectIDznd="28@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1009.64 437.07 L 1009.64 405" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv35" d="M 1011.03 550.8 L 1011.03 521.13" stroke-width="1" zvalue="933"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@0" LinkObjectIDznd="120@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1011.03 550.8 L 1011.03 521.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv35" d="M 1009.61 493.51 L 1009.61 460.9" stroke-width="1" zvalue="934"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="121@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1009.61 493.51 L 1009.61 460.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv35" d="M 994.75 476.94 L 1009.61 476.94" stroke-width="1" zvalue="935"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@0" LinkObjectIDznd="126" MaxPinNum="2"/>
   </metadata>
  <path d="M 994.75 476.94 L 1009.61 476.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="v10500" d="M 809 844 L 809 782.8" stroke-width="1" zvalue="943"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@0" LinkObjectIDznd="136@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 809 844 L 809 782.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="v10500" d="M 809 742.65 L 809 713" stroke-width="1" zvalue="944"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="136@0" LinkObjectIDznd="128@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 809 742.65 L 809 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="v10500" d="M 884 839.35 L 884 782.8" stroke-width="1" zvalue="949"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="147@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 884 839.35 L 884 782.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="v10500" d="M 884 742.65 L 884 713" stroke-width="1" zvalue="950"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="147@0" LinkObjectIDznd="128@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 884 742.65 L 884 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="v10500" d="M 960 839.35 L 960 782.8" stroke-width="1" zvalue="956"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="157@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 960 839.35 L 960 782.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="v10500" d="M 960 742.65 L 960 713" stroke-width="1" zvalue="957"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="128@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 960 742.65 L 960 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv35" d="M 1174.64 437.07 L 1174.64 405" stroke-width="1" zvalue="964"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@0" LinkObjectIDznd="28@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1174.64 437.07 L 1174.64 405" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv35" d="M 1175.03 550.8 L 1175.03 522.13" stroke-width="1" zvalue="965"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@0" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1175.03 550.8 L 1175.03 522.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv35" d="M 1174.61 494.51 L 1174.61 460.9" stroke-width="1" zvalue="966"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="168@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1174.61 494.51 L 1174.61 460.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="kv35" d="M 1158.75 476.94 L 1174.61 476.94" stroke-width="1" zvalue="967"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="162" MaxPinNum="2"/>
   </metadata>
  <path d="M 1158.75 476.94 L 1174.61 476.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="v10500" d="M 1011 623.52 L 1011 648.65" stroke-width="1" zvalue="972"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@1" LinkObjectIDznd="171@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1011 623.52 L 1011 648.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="v10500" d="M 1011 688.8 L 1011 713" stroke-width="1" zvalue="973"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@1" LinkObjectIDznd="128@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1011 688.8 L 1011 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="v10500" d="M 1175 623.52 L 1175 647.65" stroke-width="1" zvalue="976"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@1" LinkObjectIDznd="176@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1175 623.52 L 1175 647.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="v10500" d="M 1175 687.8 L 1175 713" stroke-width="1" zvalue="977"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@1" LinkObjectIDznd="170@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1175 687.8 L 1175 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="181">
   <path class="v10500" d="M 1097.11 743.09 L 1097.11 713" stroke-width="1" zvalue="979"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@0" LinkObjectIDznd="170@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.11 743.09 L 1097.11 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="v10500" d="M 1097 782.8 L 1097 804.99 L 1081.05 804.99" stroke-width="1" zvalue="982"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@1" LinkObjectIDznd="183@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097 782.8 L 1097 804.99 L 1081.05 804.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="v10500" d="M 1046.93 805.02 L 1030 805.02 L 1030 713" stroke-width="1" zvalue="983"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@1" LinkObjectIDznd="128@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1046.93 805.02 L 1030 805.02 L 1030 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="v10500" d="M 1164 839.35 L 1164 782.8" stroke-width="1" zvalue="989"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@0" LinkObjectIDznd="192@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1164 839.35 L 1164 782.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="v10500" d="M 1164 742.65 L 1164 713" stroke-width="1" zvalue="990"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@0" LinkObjectIDznd="170@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1164 742.65 L 1164 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="v10500" d="M 1409 687.8 L 1409 713" stroke-width="1" zvalue="1002"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@1" LinkObjectIDznd="170@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409 687.8 L 1409 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="v10500" d="M 1483 687.8 L 1483 713" stroke-width="1" zvalue="1003"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@1" LinkObjectIDznd="206@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1483 687.8 L 1483 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="v10500" d="M 1409 647.65 L 1409 555 L 1483 555 L 1483 647.65" stroke-width="1" zvalue="1004"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@0" LinkObjectIDznd="208@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409 647.65 L 1409 555 L 1483 555 L 1483 647.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="v10500" d="M 1236 839.35 L 1236 782.8" stroke-width="1" zvalue="1010"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@0" LinkObjectIDznd="217@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1236 839.35 L 1236 782.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="v10500" d="M 1236 742.65 L 1236 713" stroke-width="1" zvalue="1011"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217@0" LinkObjectIDznd="170@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1236 742.65 L 1236 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="v10500" d="M 1308 839.35 L 1308 782.8" stroke-width="1" zvalue="1017"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="224@0" LinkObjectIDznd="223@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1308 839.35 L 1308 782.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="v10500" d="M 1308 742.65 L 1308 713" stroke-width="1" zvalue="1018"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@0" LinkObjectIDznd="170@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1308 742.65 L 1308 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="v10500" d="M 1387 844 L 1387 782.8" stroke-width="1" zvalue="1024"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="229@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1387 844 L 1387 782.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="227">
   <path class="v10500" d="M 1387 742.65 L 1387 713" stroke-width="1" zvalue="1025"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@0" LinkObjectIDznd="170@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1387 742.65 L 1387 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="v10500" d="M 1492.94 632.5 L 1483 632.5" stroke-width="1" zvalue="1027"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@0" LinkObjectIDznd="212" MaxPinNum="2"/>
   </metadata>
  <path d="M 1492.94 632.5 L 1483 632.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="234">
   <path class="v10500" d="M 1493 595 L 1483 595" stroke-width="1" zvalue="1029"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@0" LinkObjectIDznd="212" MaxPinNum="2"/>
   </metadata>
  <path d="M 1493 595 L 1483 595" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="237">
   <path class="v10500" d="M 1492.94 555 L 1479 555" stroke-width="1" zvalue="1033"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@0" LinkObjectIDznd="212" MaxPinNum="2"/>
   </metadata>
  <path d="M 1492.94 555 L 1479 555" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="v10500" d="M 1510.35 839.98 L 1510.35 774.97" stroke-width="1" zvalue="1037"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@0" LinkObjectIDznd="41@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1510.35 839.98 L 1510.35 774.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="v10500" d="M 1509 749.08 L 1509 713" stroke-width="1" zvalue="1038"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@1" LinkObjectIDznd="206@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1509 749.08 L 1509 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="v10500" d="M 1609.75 842.99 L 1609.75 784.8" stroke-width="1" zvalue="1043"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@0" LinkObjectIDznd="119@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1609.75 842.99 L 1609.75 784.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="v10500" d="M 1609 744.65 L 1609 713" stroke-width="1" zvalue="1044"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="206@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1609 744.65 L 1609 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="v10500" d="M 1588.07 814.5 L 1588.07 801 L 1609.75 801" stroke-width="1" zvalue="1046"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@0" LinkObjectIDznd="123" MaxPinNum="2"/>
   </metadata>
  <path d="M 1588.07 814.5 L 1588.07 801 L 1609.75 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="236">
   <path class="v10500" d="M 1740 742.65 L 1740 713" stroke-width="1" zvalue="1052"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="206@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1740 742.65 L 1740 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="241">
   <path class="v10500" d="M 1741.25 931.76 L 1741.25 888" stroke-width="1" zvalue="1054"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="239@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1741.25 931.76 L 1741.25 888" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="v10500" d="M 1741 854 L 1741 782.8" stroke-width="1" zvalue="1055"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="239@0" LinkObjectIDznd="197@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1741 854 L 1741 782.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="245">
   <path class="v10500" d="M 1726 903.57 L 1741.25 903.57" stroke-width="1" zvalue="1058"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="243@0" LinkObjectIDznd="241" MaxPinNum="2"/>
   </metadata>
  <path d="M 1726 903.57 L 1741.25 903.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="252">
   <path class="v10500" d="M 1676 920.48 L 1676 898" stroke-width="1" zvalue="1063"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="247@0" LinkObjectIDznd="250@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1676 920.48 L 1676 898" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="253">
   <path class="v10500" d="M 1676 864 L 1676 841 L 1741 841" stroke-width="1" zvalue="1064"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@0" LinkObjectIDznd="242" MaxPinNum="2"/>
   </metadata>
  <path d="M 1676 864 L 1676 841 L 1741 841" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="262">
   <path class="v10500" d="M 1807.75 781.79 L 1807.75 793.03" stroke-width="1" zvalue="1072"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="257@0" LinkObjectIDznd="260@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1807.75 781.79 L 1807.75 793.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="263">
   <path class="v10500" d="M 1807.67 818.93 L 1807.67 841" stroke-width="1" zvalue="1073"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="260@1" LinkObjectIDznd="267" MaxPinNum="2"/>
   </metadata>
  <path d="M 1807.67 818.93 L 1807.67 841" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="v10500" d="M 1807.08 922.61 L 1807.08 890.97" stroke-width="1" zvalue="1076"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="254@0" LinkObjectIDznd="264@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1807.08 922.61 L 1807.08 890.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="267">
   <path class="v10500" d="M 1807 865.08 L 1807 841 L 1741 841" stroke-width="1" zvalue="1077"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="264@1" LinkObjectIDznd="253" MaxPinNum="2"/>
   </metadata>
  <path d="M 1807 865.08 L 1807 841 L 1741 841" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="274">
   <path class="v10500" d="M 766 713 L 766 791.11 L 743.91 791.11" stroke-width="1" zvalue="1083"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@5" LinkObjectIDznd="272@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 766 713 L 766 791.11 L 743.91 791.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="275">
   <path class="v10500" d="M 704.2 791 L 682 791 L 682 712" stroke-width="1" zvalue="1084"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@1" LinkObjectIDznd="268@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 704.2 791 L 682 791 L 682 712" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="279">
   <path class="v10500" d="M 556 839.35 L 556 782.8" stroke-width="1" zvalue="1089"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="281@0" LinkObjectIDznd="280@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 556 839.35 L 556 782.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="v10500" d="M 556 742.65 L 556 712" stroke-width="1" zvalue="1091"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@0" LinkObjectIDznd="268@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 556 742.65 L 556 712" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="285">
   <path class="v10500" d="M 635 839.35 L 635 782.8" stroke-width="1" zvalue="1096"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="287@0" LinkObjectIDznd="286@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 635 839.35 L 635 782.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="284">
   <path class="v10500" d="M 635 742.65 L 635 712" stroke-width="1" zvalue="1098"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="286@0" LinkObjectIDznd="268@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 635 742.65 L 635 712" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="290">
   <path class="v10500" d="M 476 742.65 L 476 712" stroke-width="1" zvalue="1105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="292@0" LinkObjectIDznd="268@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 476 742.65 L 476 712" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="296">
   <path class="v10500" d="M 476 887.35 L 476 855" stroke-width="1" zvalue="1108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="293@0" LinkObjectIDznd="294@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 476 887.35 L 476 855" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="297">
   <path class="v10500" d="M 475 821 L 475 782.8" stroke-width="1" zvalue="1109"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="294@0" LinkObjectIDznd="292@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 475 821 L 475 782.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="302">
   <path class="v10500" d="M 435.08 870.25 L 435.08 804 L 475 804" stroke-width="1" zvalue="1112"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="300@0" LinkObjectIDznd="297" MaxPinNum="2"/>
   </metadata>
  <path d="M 435.08 870.25 L 435.08 804 L 475 804" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="309">
   <path class="v10500" d="M 609.44 629.81 L 609.44 651.03" stroke-width="1" zvalue="1117"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="303@0" LinkObjectIDznd="307@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 609.44 629.81 L 609.44 651.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="310">
   <path class="v10500" d="M 609 676.93 L 609 712" stroke-width="1" zvalue="1118"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="307@1" LinkObjectIDznd="268@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 609 676.93 L 609 712" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="313">
   <path class="v10500" d="M 821.44 629.81 L 821.44 651.03" stroke-width="1" zvalue="1124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="315@0" LinkObjectIDznd="314@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 821.44 629.81 L 821.44 651.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="316">
   <path class="v10500" d="M 821 676.93 L 821 713" stroke-width="1" zvalue="1125"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="314@1" LinkObjectIDznd="128@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 821 676.93 L 821 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="319">
   <path class="v10500" d="M 1325.44 632.81 L 1325.44 654.03" stroke-width="1" zvalue="1131"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="321@0" LinkObjectIDznd="320@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1325.44 632.81 L 1325.44 654.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="322">
   <path class="v10500" d="M 1325 679.93 L 1325 713" stroke-width="1" zvalue="1132"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="320@1" LinkObjectIDznd="170@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1325 679.93 L 1325 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="11">
   <use class="kv35" height="20" transform="rotate(0,982.663,311.333) scale(1.72222,1.44444) translate(-408.473,-91.3504)" width="10" x="974.0518518518519" xlink:href="#Breaker:开关_0" y="296.8888888888889" zvalue="826"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924551114756" ObjectName="35kV允榕线351断路器"/>
   <cge:TPSR_Ref TObjectID="6473924551114756"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,982.663,311.333) scale(1.72222,1.44444) translate(-408.473,-91.3504)" width="10" x="974.0518518518519" y="296.8888888888889"/></g>
  <g id="69">
   <use class="kv35" height="20" transform="rotate(0,1346.66,311.333) scale(1.72222,1.44444) translate(-561.119,-91.3504)" width="10" x="1338.051851851852" xlink:href="#Breaker:开关_0" y="296.8888888888889" zvalue="888"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924551180292" ObjectName="35kV弄榕线352断路器"/>
   <cge:TPSR_Ref TObjectID="6473924551180292"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1346.66,311.333) scale(1.72222,1.44444) translate(-561.119,-91.3504)" width="10" x="1338.051851851852" y="296.8888888888889"/></g>
  <g id="120">
   <use class="kv35" height="20" transform="rotate(0,1009.66,507.333) scale(1.72222,1.44444) translate(-419.796,-151.658)" width="10" x="1001.051851851852" xlink:href="#Breaker:开关_0" y="492.8888888888889" zvalue="929"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924551245828" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924551245828"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1009.66,507.333) scale(1.72222,1.44444) translate(-419.796,-151.658)" width="10" x="1001.051851851852" y="492.8888888888889"/></g>
  <g id="136">
   <use class="v10500" height="20" transform="rotate(0,809,763) scale(2.2,2.2) translate(-435.273,-404.182)" width="10" x="798" xlink:href="#Breaker:小车断路器_0" y="741" zvalue="942"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924551311364" ObjectName="#1无功补偿054断路器"/>
   <cge:TPSR_Ref TObjectID="6473924551311364"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,809,763) scale(2.2,2.2) translate(-435.273,-404.182)" width="10" x="798" y="741"/></g>
  <g id="147">
   <use class="v10500" height="20" transform="rotate(0,884,763) scale(2.2,2.2) translate(-476.182,-404.182)" width="10" x="873" xlink:href="#Breaker:小车断路器_0" y="741" zvalue="948"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924551376900" ObjectName="#1水泥磨配电站055断路器"/>
   <cge:TPSR_Ref TObjectID="6473924551376900"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,884,763) scale(2.2,2.2) translate(-476.182,-404.182)" width="10" x="873" y="741"/></g>
  <g id="157">
   <use class="v10500" height="20" transform="rotate(0,960,763) scale(2.2,2.2) translate(-517.636,-404.182)" width="10" x="949" xlink:href="#Breaker:小车断路器_0" y="741" zvalue="954"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924551442436" ObjectName="#2水泥磨配电站056断路器"/>
   <cge:TPSR_Ref TObjectID="6473924551442436"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,960,763) scale(2.2,2.2) translate(-517.636,-404.182)" width="10" x="949" y="741"/></g>
  <g id="166">
   <use class="kv35" height="20" transform="rotate(0,1174.66,508.333) scale(1.72222,1.44444) translate(-488.989,-151.966)" width="10" x="1166.051851851852" xlink:href="#Breaker:开关_0" y="493.8888888888889" zvalue="962"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924551507972" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473924551507972"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1174.66,508.333) scale(1.72222,1.44444) translate(-488.989,-151.966)" width="10" x="1166.051851851852" y="493.8888888888889"/></g>
  <g id="171">
   <use class="v10500" height="20" transform="rotate(0,1011,669) scale(2.2,2.2) translate(-545.455,-352.909)" width="10" x="1000" xlink:href="#Breaker:小车断路器_0" y="647" zvalue="971"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924551573508" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924551573508"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1011,669) scale(2.2,2.2) translate(-545.455,-352.909)" width="10" x="1000" y="647"/></g>
  <g id="176">
   <use class="v10500" height="20" transform="rotate(0,1175,668) scale(2.2,2.2) translate(-634.909,-352.364)" width="10" x="1164" xlink:href="#Breaker:小车断路器_0" y="646" zvalue="975"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924551639044" ObjectName="#1主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473924551639044"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1175,668) scale(2.2,2.2) translate(-634.909,-352.364)" width="10" x="1164" y="646"/></g>
  <g id="180">
   <use class="v10500" height="20" transform="rotate(0,1097,763) scale(2.2,2.2) translate(-592.364,-404.182)" width="10" x="1086" xlink:href="#Breaker:母联小车开关_0" y="741" zvalue="978"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924551704580" ObjectName="10kV分段023断路器"/>
   <cge:TPSR_Ref TObjectID="6473924551704580"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1097,763) scale(2.2,2.2) translate(-592.364,-404.182)" width="10" x="1086" y="741"/></g>
  <g id="192">
   <use class="v10500" height="20" transform="rotate(0,1164,763) scale(2.2,2.2) translate(-628.909,-404.182)" width="10" x="1153" xlink:href="#Breaker:小车断路器_0" y="741" zvalue="987"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924551770116" ObjectName="窑头配电站057断路器"/>
   <cge:TPSR_Ref TObjectID="6473924551770116"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1164,763) scale(2.2,2.2) translate(-628.909,-404.182)" width="10" x="1153" y="741"/></g>
  <g id="198">
   <use class="v10500" height="20" transform="rotate(0,1409,668) scale(2.2,2.2) translate(-762.545,-352.364)" width="10" x="1398" xlink:href="#Breaker:小车断路器_0" y="646" zvalue="994"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924551835652" ObjectName="发电机联络柜058断路器"/>
   <cge:TPSR_Ref TObjectID="6473924551835652"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1409,668) scale(2.2,2.2) translate(-762.545,-352.364)" width="10" x="1398" y="646"/></g>
  <g id="208">
   <use class="v10500" height="20" transform="rotate(0,1483,668) scale(2.2,2.2) translate(-802.909,-352.364)" width="10" x="1472" xlink:href="#Breaker:小车断路器_0" y="646" zvalue="1001"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924551901188" ObjectName="发电机联络柜063断路器"/>
   <cge:TPSR_Ref TObjectID="6473924551901188"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1483,668) scale(2.2,2.2) translate(-802.909,-352.364)" width="10" x="1472" y="646"/></g>
  <g id="217">
   <use class="v10500" height="20" transform="rotate(0,1236,763) scale(2.2,2.2) translate(-668.182,-404.182)" width="10" x="1225" xlink:href="#Breaker:小车断路器_0" y="741" zvalue="1008"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924551966724" ObjectName="原料磨配室059断路器"/>
   <cge:TPSR_Ref TObjectID="6473924551966724"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1236,763) scale(2.2,2.2) translate(-668.182,-404.182)" width="10" x="1225" y="741"/></g>
  <g id="223">
   <use class="v10500" height="20" transform="rotate(0,1308,763) scale(2.2,2.2) translate(-707.455,-404.182)" width="10" x="1297" xlink:href="#Breaker:小车断路器_0" y="741" zvalue="1015"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924552032260" ObjectName="#2站用电061断路器"/>
   <cge:TPSR_Ref TObjectID="6473924552032260"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1308,763) scale(2.2,2.2) translate(-707.455,-404.182)" width="10" x="1297" y="741"/></g>
  <g id="229">
   <use class="v10500" height="20" transform="rotate(0,1387,763) scale(2.2,2.2) translate(-750.545,-404.182)" width="10" x="1376" xlink:href="#Breaker:小车断路器_0" y="741" zvalue="1022"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924552097796" ObjectName="#2无功补偿062断路器"/>
   <cge:TPSR_Ref TObjectID="6473924552097796"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1387,763) scale(2.2,2.2) translate(-750.545,-404.182)" width="10" x="1376" y="741"/></g>
  <g id="119">
   <use class="v10500" height="20" transform="rotate(0,1609,765) scale(2.2,2.2) translate(-871.636,-405.273)" width="10" x="1598" xlink:href="#Breaker:小车断路器_0" y="743" zvalue="1042"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924552163332" ObjectName="厂用变064断路器"/>
   <cge:TPSR_Ref TObjectID="6473924552163332"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1609,765) scale(2.2,2.2) translate(-871.636,-405.273)" width="10" x="1598" y="743"/></g>
  <g id="197">
   <use class="v10500" height="20" transform="rotate(0,1740,763) scale(2.2,2.2) translate(-943.091,-404.182)" width="10" x="1729" xlink:href="#Breaker:小车断路器_0" y="741" zvalue="1050"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924552228868" ObjectName="1号发电机组065断路器"/>
   <cge:TPSR_Ref TObjectID="6473924552228868"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1740,763) scale(2.2,2.2) translate(-943.091,-404.182)" width="10" x="1729" y="741"/></g>
  <g id="272">
   <use class="v10500" height="20" transform="rotate(90,724,791) scale(2.2,2.2) translate(-388.909,-419.455)" width="10" x="713" xlink:href="#Breaker:母联小车开关_0" y="769" zvalue="1082"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924552294404" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473924552294404"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,724,791) scale(2.2,2.2) translate(-388.909,-419.455)" width="10" x="713" y="769"/></g>
  <g id="280">
   <use class="v10500" height="20" transform="rotate(0,556,763) scale(2.2,2.2) translate(-297.273,-404.182)" width="10" x="545" xlink:href="#Breaker:小车断路器_0" y="741" zvalue="1088"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924552359940" ObjectName="生活Ⅰ回052断路器"/>
   <cge:TPSR_Ref TObjectID="6473924552359940"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,556,763) scale(2.2,2.2) translate(-297.273,-404.182)" width="10" x="545" y="741"/></g>
  <g id="286">
   <use class="v10500" height="20" transform="rotate(0,635,763) scale(2.2,2.2) translate(-340.364,-404.182)" width="10" x="624" xlink:href="#Breaker:小车断路器_0" y="741" zvalue="1095"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924552425476" ObjectName="生活Ⅱ回053断路器"/>
   <cge:TPSR_Ref TObjectID="6473924552425476"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,635,763) scale(2.2,2.2) translate(-340.364,-404.182)" width="10" x="624" y="741"/></g>
  <g id="292">
   <use class="v10500" height="20" transform="rotate(0,476,763) scale(2.2,2.2) translate(-253.636,-404.182)" width="10" x="465" xlink:href="#Breaker:小车断路器_0" y="741" zvalue="1102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924552491012" ObjectName="备用051断路器"/>
   <cge:TPSR_Ref TObjectID="6473924552491012"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,476,763) scale(2.2,2.2) translate(-253.636,-404.182)" width="10" x="465" y="741"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="40">
   <use class="kv35" height="20" transform="rotate(90,953,341.889) scale(1,1) translate(0,0)" width="10" x="948.0000000529819" xlink:href="#GroundDisconnector:地刀_0" y="331.8888888888889" zvalue="858"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449994227717" ObjectName="35kV允榕线35117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449994227717"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,953,341.889) scale(1,1) translate(0,0)" width="10" x="948.0000000529819" y="331.8888888888889"/></g>
  <g id="57">
   <use class="kv35" height="20" transform="rotate(90,953,281.889) scale(1,1) translate(0,0)" width="10" x="948" xlink:href="#GroundDisconnector:地刀_0" y="271.8888888888889" zvalue="864"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449994358789" ObjectName="35kV允榕线35160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449994358789"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,953,281.889) scale(1,1) translate(0,0)" width="10" x="948" y="271.8888888888889"/></g>
  <g id="78">
   <use class="kv35" height="20" transform="rotate(90,953,228.111) scale(1,1) translate(0,0)" width="10" x="947.9999998410543" xlink:href="#GroundDisconnector:地刀_0" y="218.1111111111111" zvalue="867"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449994489861" ObjectName="35kV允榕线35167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449994489861"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,953,228.111) scale(1,1) translate(0,0)" width="10" x="947.9999998410543" y="218.1111111111111"/></g>
  <g id="68">
   <use class="kv35" height="20" transform="rotate(90,1317,341.889) scale(1,1) translate(0,0)" width="10" x="1312.000000052982" xlink:href="#GroundDisconnector:地刀_0" y="331.8888888888889" zvalue="890"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449995145221" ObjectName="35kV弄榕线35217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449995145221"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1317,341.889) scale(1,1) translate(0,0)" width="10" x="1312.000000052982" y="331.8888888888889"/></g>
  <g id="64">
   <use class="kv35" height="20" transform="rotate(90,1317,281.889) scale(1,1) translate(0,0)" width="10" x="1312" xlink:href="#GroundDisconnector:地刀_0" y="271.8888888888889" zvalue="895"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449995014149" ObjectName="35kV弄榕线35260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449995014149"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1317,281.889) scale(1,1) translate(0,0)" width="10" x="1312" y="271.8888888888889"/></g>
  <g id="59">
   <use class="kv35" height="20" transform="rotate(90,1317,228.111) scale(1,1) translate(0,0)" width="10" x="1311.999999841054" xlink:href="#GroundDisconnector:地刀_0" y="218.1111111111111" zvalue="898"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449994883077" ObjectName="35kV弄榕线35267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449994883077"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1317,228.111) scale(1,1) translate(0,0)" width="10" x="1311.999999841054" y="218.1111111111111"/></g>
  <g id="98">
   <use class="kv35" height="20" transform="rotate(90,663,368.889) scale(1,1) translate(0,0)" width="10" x="658" xlink:href="#GroundDisconnector:地刀_0" y="358.8888888888889" zvalue="911"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449995603973" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449995603973"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,663,368.889) scale(1,1) translate(0,0)" width="10" x="658" y="358.8888888888889"/></g>
  <g id="99">
   <use class="kv35" height="20" transform="rotate(90,663,292.111) scale(1,1) translate(0,0)" width="10" x="658" xlink:href="#GroundDisconnector:地刀_0" y="282.1111111111111" zvalue="912"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449995735046" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449995735046"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,663,292.111) scale(1,1) translate(0,0)" width="10" x="658" y="282.1111111111111"/></g>
  <g id="111">
   <use class="kv35" height="20" transform="rotate(90,710,469.889) scale(1,1) translate(0,0)" width="10" x="705" xlink:href="#GroundDisconnector:地刀_0" y="459.8888888888889" zvalue="921"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449995997189" ObjectName="#1站用变38117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449995997189"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,710,469.889) scale(1,1) translate(0,0)" width="10" x="705" y="459.8888888888889"/></g>
  <g id="122">
   <use class="kv35" height="20" transform="rotate(90,985,476.889) scale(1,1) translate(0,0)" width="10" x="980" xlink:href="#GroundDisconnector:地刀_0" y="466.8888888888889" zvalue="930"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449996193798" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449996193798"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,985,476.889) scale(1,1) translate(0,0)" width="10" x="980" y="466.8888888888889"/></g>
  <g id="165">
   <use class="kv35" height="20" transform="rotate(90,1149,476.889) scale(1,1) translate(0,0)" width="10" x="1144" xlink:href="#GroundDisconnector:地刀_0" y="466.8888888888889" zvalue="963"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449996521477" ObjectName="#2主变35kV侧30217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449996521477"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1149,476.889) scale(1,1) translate(0,0)" width="10" x="1144" y="466.8888888888889"/></g>
  <g id="135">
   <use class="v10500" height="29" transform="rotate(0,1588,828) scale(1,1) translate(0,0)" width="21" x="1577.5" xlink:href="#GroundDisconnector:地刀-带电显示_0" y="813.5" zvalue="1045"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449997438981" ObjectName="厂用变06467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449997438981"/></metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1588,828) scale(1,1) translate(0,0)" width="21" x="1577.5" y="813.5"/></g>
  <g id="243">
   <use class="v10500" height="29" transform="rotate(90,1712.5,903.5) scale(1,1) translate(0,0)" width="21" x="1702" xlink:href="#GroundDisconnector:地刀-带电显示_0" y="889" zvalue="1057"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449997701125" ObjectName="1号发电机06567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449997701125"/></metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(90,1712.5,903.5) scale(1,1) translate(0,0)" width="21" x="1702" y="889"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="83">
   <use class="kv35" height="36" transform="rotate(270,1054.78,228.981) scale(1.35948,1.2963) translate(-275.852,-47.0053)" width="17" x="1043.222222222222" xlink:href="#Accessory:线路PT235_0" y="205.6481481481482" zvalue="869"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449994555397" ObjectName="351PT"/>
   </metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(270,1054.78,228.981) scale(1.35948,1.2963) translate(-275.852,-47.0053)" width="17" x="1043.222222222222" y="205.6481481481482"/></g>
  <g id="53">
   <use class="kv35" height="36" transform="rotate(270,1418.78,228.981) scale(1.35948,1.2963) translate(-372.102,-47.0053)" width="17" x="1407.222222222222" xlink:href="#Accessory:线路PT235_0" y="205.6481481481482" zvalue="901"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449994752005" ObjectName="352PT"/>
   </metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(270,1418.78,228.981) scale(1.35948,1.2963) translate(-372.102,-47.0053)" width="17" x="1407.222222222222" y="205.6481481481482"/></g>
  <g id="84">
   <use class="kv35" height="30" transform="rotate(0,690.5,243.5) scale(2.16667,-2.16667) translate(-354.308,-338.385)" width="30" x="658" xlink:href="#Accessory:10kV避雷器PT_0" y="211" zvalue="907"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449995407365" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,690.5,243.5) scale(2.16667,-2.16667) translate(-354.308,-338.385)" width="30" x="658" y="211"/></g>
  <g id="231">
   <use class="v10500" height="20" transform="rotate(270,1506.5,632.5) scale(1.55,1.55) translate(-529.065,-218.935)" width="20" x="1491" xlink:href="#Accessory:线路PT3_0" y="617" zvalue="1026"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449996980229" ObjectName="063避雷器1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1506.5,632.5) scale(1.55,1.55) translate(-529.065,-218.935)" width="20" x="1491" y="617"/></g>
  <g id="233">
   <use class="v10500" height="30" transform="rotate(270,1507,595) scale(1,1) translate(0,0)" width="30" x="1492" xlink:href="#Accessory:带熔断器四卷PT_0" y="580" zvalue="1028"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449997045766" ObjectName="063pt"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1507,595) scale(1,1) translate(0,0)" width="30" x="1492" y="580"/></g>
  <g id="235">
   <use class="v10500" height="20" transform="rotate(270,1506.5,555) scale(1.55,1.55) translate(-529.065,-191.435)" width="20" x="1491" xlink:href="#Accessory:线路PT3_0" y="539.5" zvalue="1031"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449997111302" ObjectName="063避雷器2"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1506.5,555) scale(1.55,1.55) translate(-529.065,-191.435)" width="20" x="1491" y="539.5"/></g>
  <g id="10">
   <use class="v10500" height="30" transform="rotate(0,1510.25,860.786) scale(1.58571,-1.58571) translate(-547.59,-1394.84)" width="35" x="1482.5" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="837" zvalue="1034"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449997176837" ObjectName="10kVⅣ段母线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1510.25,860.786) scale(1.58571,-1.58571) translate(-547.59,-1394.84)" width="35" x="1482.5" y="837"/></g>
  <g id="254">
   <use class="v10500" height="30" transform="rotate(0,1807,939.857) scale(1.31429,-1.31429) translate(-426.609,-1650.25)" width="35" x="1784" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="920.142857142857" zvalue="1066"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449997897733" ObjectName="发电机PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1807,939.857) scale(1.31429,-1.31429) translate(-426.609,-1650.25)" width="35" x="1784" y="920.142857142857"/></g>
  <g id="257">
   <use class="v10500" height="30" transform="rotate(0,1807.75,759.1) scale(1.54,-1.54) translate(-627.136,-1243.92)" width="25" x="1788.5" xlink:href="#Accessory:发电机励磁TV_0" y="736" zvalue="1068"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449997963269" ObjectName="发电机励磁TV"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1807.75,759.1) scale(1.54,-1.54) translate(-627.136,-1243.92)" width="25" x="1788.5" y="736"/></g>
  <g id="300">
   <use class="v10500" height="29" transform="rotate(0,435,884) scale(1,1) translate(0,0)" width="30" x="420" xlink:href="#Accessory:计量PT_0" y="869.5" zvalue="1111"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449998422021" ObjectName="计量"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,435,884) scale(1,1) translate(0,0)" width="30" x="420" y="869.5"/></g>
  <g id="303">
   <use class="v10500" height="48" transform="rotate(0,607,608.667) scale(0.888889,0.888889) translate(73.375,73.4167)" width="45" x="587" xlink:href="#Accessory:母线电压互感器11_0" y="587.3333333333334" zvalue="1113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449998487557" ObjectName="10.5kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,607,608.667) scale(0.888889,0.888889) translate(73.375,73.4167)" width="45" x="587" y="587.3333333333334"/></g>
  <g id="315">
   <use class="v10500" height="48" transform="rotate(0,819,608.667) scale(0.888889,0.888889) translate(99.875,73.4167)" width="45" x="799" xlink:href="#Accessory:母线电压互感器11_0" y="587.3333333333334" zvalue="1120"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449998684165" ObjectName="10.5kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,819,608.667) scale(0.888889,0.888889) translate(99.875,73.4167)" width="45" x="799" y="587.3333333333334"/></g>
  <g id="321">
   <use class="v10500" height="48" transform="rotate(0,1323,611.667) scale(0.888889,0.888889) translate(162.875,73.7917)" width="45" x="1303" xlink:href="#Accessory:母线电压互感器11_0" y="590.3333333333334" zvalue="1127"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449998815237" ObjectName="10.5kVⅢ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,1323,611.667) scale(0.888889,0.888889) translate(162.875,73.7917)" width="45" x="1303" y="590.3333333333334"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="28">
   <path class="kv35" d="M 620 405 L 1501 405" stroke-width="6" zvalue="878"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674252619780" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674252619780"/></metadata>
  <path d="M 620 405 L 1501 405" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="v10500" d="M 748 713 L 1044 713" stroke-width="6" zvalue="937"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674252685316" ObjectName="10.5kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674252685316"/></metadata>
  <path d="M 748 713 L 1044 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="v10500" d="M 1081 713 L 1428 713" stroke-width="6" zvalue="968"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674252750852" ObjectName="10.5kVⅢ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674252750852"/></metadata>
  <path d="M 1081 713 L 1428 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="206">
   <path class="v10500" d="M 1464 713 L 1863 713" stroke-width="6" zvalue="999"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674252816388" ObjectName="10.5kVⅣ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674252816388"/></metadata>
  <path d="M 1464 713 L 1863 713" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="268">
   <path class="v10500" d="M 437 712 L 701 712" stroke-width="6" zvalue="1079"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674252881924" ObjectName="10.5kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674252881924"/></metadata>
  <path d="M 437 712 L 701 712" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="107">
   <use class="kv35" height="30" transform="rotate(0,746.4,514) scale(1.8,1.8) translate(-323.333,-216.444)" width="21" x="727.5" xlink:href="#EnergyConsumer:糖厂负荷_0" y="487" zvalue="917"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449995800582" ObjectName="#1站用变"/>
   <cge:TPSR_Ref TObjectID="6192449995800582"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,746.4,514) scale(1.8,1.8) translate(-323.333,-216.444)" width="21" x="727.5" y="487"/></g>
  <g id="140">
   <use class="v10500" height="30" transform="rotate(0,884,856) scale(1.25,-1.23333) translate(-175.3,-1546.55)" width="12" x="876.5" xlink:href="#EnergyConsumer:负荷_0" y="837.5" zvalue="945"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449996324870" ObjectName="#1水泥磨配电站"/>
   <cge:TPSR_Ref TObjectID="6192449996324870"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,884,856) scale(1.25,-1.23333) translate(-175.3,-1546.55)" width="12" x="876.5" y="837.5"/></g>
  <g id="158">
   <use class="v10500" height="30" transform="rotate(0,960,856) scale(1.25,-1.23333) translate(-190.5,-1546.55)" width="12" x="952.5" xlink:href="#EnergyConsumer:负荷_0" y="837.5" zvalue="952"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449996390406" ObjectName="#2水泥磨配电站"/>
   <cge:TPSR_Ref TObjectID="6192449996390406"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,960,856) scale(1.25,-1.23333) translate(-190.5,-1546.55)" width="12" x="952.5" y="837.5"/></g>
  <g id="193">
   <use class="v10500" height="30" transform="rotate(0,1164,856) scale(1.25,-1.23333) translate(-231.3,-1546.55)" width="12" x="1156.5" xlink:href="#EnergyConsumer:负荷_0" y="837.5" zvalue="985"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449996718085" ObjectName="窑头配电站"/>
   <cge:TPSR_Ref TObjectID="6192449996718085"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1164,856) scale(1.25,-1.23333) translate(-231.3,-1546.55)" width="12" x="1156.5" y="837.5"/></g>
  <g id="218">
   <use class="v10500" height="30" transform="rotate(0,1236,856) scale(1.25,-1.23333) translate(-245.7,-1546.55)" width="12" x="1228.5" xlink:href="#EnergyConsumer:负荷_0" y="837.5" zvalue="1006"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449996783621" ObjectName="原料磨配室"/>
   <cge:TPSR_Ref TObjectID="6192449996783621"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1236,856) scale(1.25,-1.23333) translate(-245.7,-1546.55)" width="12" x="1228.5" y="837.5"/></g>
  <g id="224">
   <use class="v10500" height="30" transform="rotate(0,1308,856) scale(1.25,-1.23333) translate(-260.1,-1546.55)" width="12" x="1300.5" xlink:href="#EnergyConsumer:负荷_0" y="837.5" zvalue="1013"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449996849157" ObjectName="#2站用电"/>
   <cge:TPSR_Ref TObjectID="6192449996849157"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1308,856) scale(1.25,-1.23333) translate(-260.1,-1546.55)" width="12" x="1300.5" y="837.5"/></g>
  <g id="93">
   <use class="v10500" height="18" transform="rotate(0,1609.75,860.5) scale(2.16667,2.16667) translate(-858.038,-452.846)" width="15" x="1593.5" xlink:href="#EnergyConsumer:厂变_0" y="841" zvalue="1039"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449997307909" ObjectName="厂用变"/>
   <cge:TPSR_Ref TObjectID="6192449997307909"/></metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1609.75,860.5) scale(2.16667,2.16667) translate(-858.038,-452.846)" width="15" x="1593.5" y="841"/></g>
  <g id="247">
   <use class="v10500" height="30" transform="rotate(0,1676,929.25) scale(1.25,-0.65) translate(-333.7,-2364.12)" width="12" x="1668.5" xlink:href="#EnergyConsumer:负荷_0" y="919.5" zvalue="1060"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449997766661" ObjectName="励磁变压器"/>
   <cge:TPSR_Ref TObjectID="6192449997766661"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1676,929.25) scale(1.25,-0.65) translate(-333.7,-2364.12)" width="12" x="1668.5" y="919.5"/></g>
  <g id="281">
   <use class="v10500" height="30" transform="rotate(0,556,856) scale(1.25,-1.23333) translate(-109.7,-1546.55)" width="12" x="548.5" xlink:href="#EnergyConsumer:负荷_0" y="837.5" zvalue="1086"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449998159877" ObjectName="生活Ⅰ回"/>
   <cge:TPSR_Ref TObjectID="6192449998159877"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,556,856) scale(1.25,-1.23333) translate(-109.7,-1546.55)" width="12" x="548.5" y="837.5"/></g>
  <g id="287">
   <use class="v10500" height="30" transform="rotate(0,635,856) scale(1.25,-1.23333) translate(-125.5,-1546.55)" width="12" x="627.5" xlink:href="#EnergyConsumer:负荷_0" y="837.5" zvalue="1093"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449998225413" ObjectName="生活Ⅱ回"/>
   <cge:TPSR_Ref TObjectID="6192449998225413"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,635,856) scale(1.25,-1.23333) translate(-125.5,-1546.55)" width="12" x="627.5" y="837.5"/></g>
  <g id="293">
   <use class="v10500" height="30" transform="rotate(0,476,904) scale(1.25,-1.23333) translate(-93.7,-1633.47)" width="12" x="468.5" xlink:href="#EnergyConsumer:负荷_0" y="885.5" zvalue="1100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449998290949" ObjectName="备用"/>
   <cge:TPSR_Ref TObjectID="6192449998290949"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,476,904) scale(1.25,-1.23333) translate(-93.7,-1633.47)" width="12" x="468.5" y="885.5"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="117">
   <g id="1170">
    <use class="kv35" height="30" transform="rotate(0,1011,587) scale(2.58333,2.6) translate(-600.645,-337.231)" width="24" x="980" xlink:href="#PowerTransformer2:可调不带中性点_0" y="548" zvalue="925"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874451910659" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1171">
    <use class="v10500" height="30" transform="rotate(0,1011,587) scale(2.58333,2.6) translate(-600.645,-337.231)" width="24" x="980" xlink:href="#PowerTransformer2:可调不带中性点_1" y="548" zvalue="925"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874451976195" ObjectName="10.5"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399457898499" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399457898499"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1011,587) scale(2.58333,2.6) translate(-600.645,-337.231)" width="24" x="980" y="548"/></g>
  <g id="169">
   <g id="1690">
    <use class="kv35" height="30" transform="rotate(0,1175,587) scale(2.58333,2.6) translate(-701.161,-337.231)" width="24" x="1144" xlink:href="#PowerTransformer2:可调不带中性点_0" y="548" zvalue="959"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874452041731" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1691">
    <use class="v10500" height="30" transform="rotate(0,1175,587) scale(2.58333,2.6) translate(-701.161,-337.231)" width="24" x="1144" xlink:href="#PowerTransformer2:可调不带中性点_1" y="548" zvalue="959"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874452107267" ObjectName="10.5"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399457964035" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399457964035"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1175,587) scale(2.58333,2.6) translate(-701.161,-337.231)" width="24" x="1144" y="548"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="133">
   <use class="v10500" height="30" transform="rotate(0,809,857) scale(1,1) translate(0,0)" width="30" x="794" xlink:href="#Compensator:并联电容器组_0" y="842" zvalue="940"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449996259334" ObjectName="#1无功补偿"/>
   <cge:TPSR_Ref TObjectID="6192449996259334"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,809,857) scale(1,1) translate(0,0)" width="30" x="794" y="842"/></g>
  <g id="230">
   <use class="v10500" height="30" transform="rotate(0,1387,857) scale(1,1) translate(0,0)" width="30" x="1372" xlink:href="#Compensator:并联电容器组_0" y="842" zvalue="1020"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449996914693" ObjectName="#2无功补偿"/>
   <cge:TPSR_Ref TObjectID="6192449996914693"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1387,857) scale(1,1) translate(0,0)" width="30" x="1372" y="842"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="182">
   <use class="v10500" height="30" transform="rotate(0,1741.25,947.25) scale(1.05,1.05) translate(-82.1667,-44.3571)" width="30" x="1725.5" xlink:href="#Generator:发电机_0" y="931.5" zvalue="1047"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449997504517" ObjectName="1号发电机"/>
   <cge:TPSR_Ref TObjectID="6192449997504517"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1741.25,947.25) scale(1.05,1.05) translate(-82.1667,-44.3571)" width="30" x="1725.5" y="931.5"/></g>
 </g>
</svg>