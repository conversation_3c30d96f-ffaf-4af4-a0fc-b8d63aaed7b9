<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549581578242" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸333_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,17.5) scale(1,1) translate(0,0)" width="6" x="3" y="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="12.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:刀闸333_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="4.583333333333332" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,17.5) scale(1,1) translate(0,0)" width="6" x="3" y="12"/>
  </symbol>
  <symbol id="Disconnector:刀闸333_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:PT6_0" viewBox="0,0,15,20">
   <use terminal-index="0" type="0" x="7.560000000000002" xlink:href="#terminal" y="0.42361930658363"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.160000000000005" x2="8.559999999999999" y1="15.48035578286757" y2="14.24281579851547"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.060000000000002" x2="8.459999999999996" y1="16.09912577504353" y2="17.33666575939564"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.459999999999999" x2="5.060000000000007" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000005" x2="9.859999999999998" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.859999999999999" x2="8.859999999999999" y1="14.65532912663279" y2="17.13040909533699"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000003" x2="7.460000000000003" y1="2.279929283111803" y2="4.755009251816007"/>
   <ellipse cx="7.5" cy="6.51" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.7" cy="13.44" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_0" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="3" y1="19" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="4.75" y2="8.75"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="18.75" y2="21.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_1" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="2" y2="24"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_2" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.08333333333333" x2="3.995614035087721" y1="9.083333333333332" y2="17.19627192982456"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.717927631578948" x2="10.16666666666667" y1="9.172423245614038" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="2" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="21.25" y2="24.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(130,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(130,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:110kVXIANLUPT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="12.90667828048158" xlink:href="#terminal" y="37.28457520218116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27" x2="13" y1="26.75" y2="26.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.08333333333334" x2="29.08333333333334" y1="14.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27" x2="27" y1="22.75" y2="26.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.93916361389983" x2="12.93916361389983" y1="33.96630546659506" y2="37.26790853551449"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.93916361389983" x2="12.93916361389983" y1="30.79012345679012" y2="22.71704491718448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.91963465760507" x2="12.91963465760507" y1="19.74560215515698" y2="13.06430394728183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.01963465760507" x2="13.01963465760507" y1="22.75831495554599" y2="22.75831495554599"/>
   <ellipse cx="23.37" cy="20.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.556517488649495" x2="16.39655620359946" y1="22.67498162221265" y2="22.67498162221265"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.01963465760507" x2="13.01963465760507" y1="33.90122531314907" y2="33.90122531314907"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982819" x2="16.47988953693279" y1="19.66306207843401" y2="19.66306207843401"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982824" x2="16.47988953693279" y1="33.90122531314905" y2="33.90122531314905"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982824" x2="16.47988953693279" y1="30.80597243603708" y2="30.80597243603708"/>
   <ellipse cx="26.96" cy="14.43" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="10.72318415531615" x2="15" y1="12.99639541176734" y2="12.99639541176734"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.47318415531615" x2="14.08333333333333" y1="11.74639541176734" y2="11.74639541176734"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.97318415531615" x2="13.41666666666667" y1="10.24639541176734" y2="10.24639541176734"/>
   <ellipse cx="30.62" cy="20.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.25" x2="25.25" y1="20.5" y2="20.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.08333333333334" x2="33.08333333333334" y1="20.41666666666667" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:母线电压互感器11_0" viewBox="0,0,45,48">
   <use terminal-index="0" type="0" x="25.25" xlink:href="#terminal" y="47.78457520218115"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.5,30.95) scale(1,-1) translate(0,-1334.05)" width="6" x="10.5" y="23.95"/>
   <path d="M 8.64167 11.4096 L 4.64167 11.4096 L 6.50709 7.52624 L 7.67376 9.52624 L 8.64167 11.4096" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="13.5" y1="19.78457520218114" y2="40.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.25" x2="25.25" y1="40.5" y2="47.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="37.25" x2="13.58333333333333" y1="40.5" y2="40.5"/>
   <ellipse cx="13.56" cy="5.42" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799663" x2="13.56620079799663" y1="3.155388266900378" y2="5.82957386674455"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799661" x2="10.88240159599323" y1="5.829573866744575" y2="7.166666666666657"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799662" x2="16.25" y1="5.829573866744575" y2="7.166666666666657"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,36.96,25.94) scale(1,-1) translate(0,-1133.72)" width="7.58" x="33.17" y="17.87"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="34.01790853551448" y2="24.11790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="40.43457520218114" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.7861111111111" x2="39.3" y1="11.60124186884782" y2="11.60124186884782"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.82777777777777" x2="39.93888888888888" y1="12.98282081621623" y2="12.98282081621623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.55" x2="41.21666666666666" y1="14.36439976358464" y2="14.36439976358464"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="34.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="18.01790853551448" y2="14.41790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="40.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <ellipse cx="13.59" cy="15.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.51" cy="10.09" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.67" cy="10.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490864" x2="10.91228003290526" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="13.59607923490865" y1="12.74178419246191" y2="15.41596979230608"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51274590157532" x2="23.19654510357869" y1="10.49930312563944" y2="11.83639592556152"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51274590157532" x2="20.51274590157532" y1="7.825117525795246" y2="10.49930312563942"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="16.27987843691203" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5127459015753" x2="17.82894669957193" y1="10.49930312563944" y2="11.83639592556152"/>
  </symbol>
  <symbol id="Accessory:四卷带壁雷器母线PT_0" viewBox="0,0,40,35">
   <use terminal-index="0" type="0" x="22.10905664884498" xlink:href="#terminal" y="34.51612485684674"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="28.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <rect fill-opacity="0" height="15.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,30,14.36) scale(1,-1) translate(0,-930.43)" width="7" x="26.5" y="6.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="16.27740325661302" y2="15.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="7.166666666666671" y1="16.27740325661302" y2="12.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="12.27740325661302" y2="13.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="30.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="22" y1="28.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="14" y1="21.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="30" y1="28.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="30" y1="28.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.92943360505483" x2="29.92943360505483" y1="6.610736589946352" y2="3.462520268614096"/>
   <ellipse cx="13.81" cy="17.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.95921744067709" x2="27.68240159599324" y1="3.3946117330996" y2="3.3946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.12588410734375" x2="28.51573492932657" y1="2.144611733099605" y2="2.144611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="30.54255077401042" x2="29.09906826265991" y1="0.8946117330996053" y2="0.8946117330996053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599325" x2="16.18240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="13.78240159599324" y1="15.23154965466559" y2="17.7066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="11.38240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <ellipse cx="20.23" cy="13.99" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.48" cy="10.41" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="17.79906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="20.19906826265991" y1="11.73154965466559" y2="14.2066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="22.59906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="8.148216321332258" y2="10.62329629003646"/>
   <ellipse cx="6.64" cy="14.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:带电容电阻接地_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.75"/>
   <rect fill-opacity="0" height="11.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.94,6.29) scale(1,1) translate(0,0)" width="6.08" x="2.9" y="0.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666663" x2="10.5" y1="20.49453511141348" y2="20.49453511141348"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.603429610654176" x2="7.693139016796801" y1="25.38116790988687" y2="25.38116790988687"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.965686274509802" x2="9.330882352941176" y1="22.90451817731685" y2="22.90451817731685"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.916666666666667" x2="5.916666666666667" y1="14.58333333333333" y2="11.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.083333333333333" x2="6.083333333333333" y1="20.58333333333334" y2="16.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.833333333333333" x2="9" y1="16.16120177808014" y2="16.16120177808014"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.833333333333333" x2="9.25" y1="14.66120177808014" y2="14.66120177808014"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(0,255,127)" fill-opacity="1" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <rect height="12.17" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.75,25.42) scale(1,1) translate(0,0)" width="6" x="8.75" y="19.33"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="16.33333333333333" y2="31.58333333333333"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_1" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <rect fill-opacity="0" height="8" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(255,0,0)" fill-opacity="1" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="7.333333333333334" y2="41.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
   <rect height="12.17" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.75,25.42) scale(1,1) translate(0,0)" width="6" x="8.75" y="19.33"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_2" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.75" x2="14.75" y1="17" y2="32"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.65" x2="8.65" y1="17.1" y2="32.1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(0,255,127)" fill-opacity="1" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="32.35" y2="41.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.71666666666667" x2="11.71666666666667" y1="17.05" y2="7.466666666666663"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
  </symbol>
  <symbol id="Accessory:4绕组母线PT_0" viewBox="0,0,25,20">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="0"/>
   <ellipse cx="12.75" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.75" cy="9.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.67" cy="14.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.82" cy="9.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="5.583333333333334" y1="7.38888888888889" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333336" x2="5.583333333333336" y1="12" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="5.5" y1="7.38888888888889" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="12.75" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="12.75" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.75" x2="12.75" y1="7.500000000000002" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.58333333333333" x2="12.58333333333333" y1="12.63888888888889" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.58333333333333" x2="12.58333333333333" y1="12.63888888888889" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="17.25" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="19.31481481481482" y1="10.21612466124661" y2="7.749999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.94444444444444" x2="20.62962962962963" y1="10.21612466124661" y2="7.749999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="21.94444444444444" y1="10.21612466124661" y2="10.21612466124661"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变1接点_0" viewBox="0,0,26,38">
   <use terminal-index="0" type="0" x="9.116666666666667" xlink:href="#terminal" y="0.2500000000000071"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.45" x2="25.45" y1="30" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.45" x2="24.45" y1="31" y2="31"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="33" y2="38"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.45" x2="23.45" y1="32" y2="32"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="23" y1="24" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="19" y1="24" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="23" y1="24" y2="24"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="2.25" y2="0.25"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="9" y1="30" y2="37"/>
   <ellipse cx="9.210000000000001" cy="10.97" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="9.201922857336841" y1="6.6" y2="10.70894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="5.216666666666669" y1="10.73394833233988" y2="13.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.22969569739165" x2="13.31666666666667" y1="10.68990500916851" y2="13.5"/>
   <ellipse cx="9.210000000000001" cy="24.47" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="9.201922857336841" y1="20.1" y2="24.20894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="5.216666666666669" y1="24.23394833233988" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.22969569739165" x2="13.31666666666667" y1="24.18990500916851" y2="27"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV朗外河电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="49.14" xlink:href="logo.png" y="44.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="280" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,198.143,76.4286) scale(1,1) translate(0,0)" writing-mode="lr" x="198.14" xml:space="preserve" y="79.93000000000001" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,200.643,74.1189) scale(1,1) translate(0,0)" writing-mode="lr" x="200.64" xml:space="preserve" y="83.12" zvalue="5">110kV朗外河电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="45" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="520"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="520">信号一览</text>
  <line fill="none" id="276" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="383.1428571428571" x2="383.1428571428571" y1="12.42857142857133" y2="1042.428571428571" zvalue="6"/>
  <line fill="none" id="274" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.142857142857565" x2="376.1428571428571" y1="148.2990640426538" y2="148.2990640426538" zvalue="8"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="332" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,708.875,267.341) scale(1,1) translate(0,0)" writing-mode="lr" x="708.88" xml:space="preserve" y="271.84" zvalue="34">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,990.937,215.48) scale(1,1) translate(0,0)" writing-mode="lr" x="990.9400000000001" xml:space="preserve" y="219.98" zvalue="36">131</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,982.159,263.035) scale(1,1) translate(0,0)" writing-mode="lr" x="982.16" xml:space="preserve" y="267.54" zvalue="37">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,983.826,155.035) scale(1,1) translate(0,0)" writing-mode="lr" x="983.83" xml:space="preserve" y="159.54" zvalue="40">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,965.536,32.8131) scale(1,1) translate(0,0)" writing-mode="lr" x="965.54" xml:space="preserve" y="37.31" zvalue="44">110kV朗外河电站线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1018.66,250.48) scale(1,1) translate(-4.49489e-13,0)" writing-mode="lr" x="1018.66" xml:space="preserve" y="254.98" zvalue="47">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1018.66,201.702) scale(1,1) translate(-4.49045e-13,0)" writing-mode="lr" x="1018.66" xml:space="preserve" y="206.2" zvalue="49">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1018.66,147.258) scale(1,1) translate(0,0)" writing-mode="lr" x="1018.66" xml:space="preserve" y="151.76" zvalue="51">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1500.47,221.327) scale(1,1) translate(0,0)" writing-mode="lr" x="1500.47" xml:space="preserve" y="225.83" zvalue="85">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1516.76,182.855) scale(1,1) translate(0,0)" writing-mode="lr" x="1516.76" xml:space="preserve" y="187.36" zvalue="87">7</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1525.26,278.772) scale(1,1) translate(0,-4.80987e-13)" writing-mode="lr" x="1525.26" xml:space="preserve" y="283.27" zvalue="89">0</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,841.327,357.444) scale(1,1) translate(0,0)" writing-mode="lr" x="841.33" xml:space="preserve" y="361.94" zvalue="96">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,850.661,410.556) scale(1,1) translate(0,0)" writing-mode="lr" x="850.66" xml:space="preserve" y="415.06" zvalue="98">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,872.994,395.556) scale(1,1) translate(0,0)" writing-mode="lr" x="872.99" xml:space="preserve" y="400.06" zvalue="101">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,904.226,489.158) scale(1,1) translate(0,0)" writing-mode="lr" x="904.23" xml:space="preserve" y="493.66" zvalue="103">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="294" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,700.958,512.812) scale(1,1) translate(0,0)" writing-mode="lr" x="700.96" xml:space="preserve" y="517.3099999999999" zvalue="118">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,867.428,586.09) scale(1,1) translate(0,0)" writing-mode="lr" x="867.4299999999999" xml:space="preserve" y="590.59" zvalue="120">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1434.66,364.27) scale(1,1) translate(0,0)" writing-mode="lr" x="1434.66" xml:space="preserve" y="368.77" zvalue="122">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1443.99,417.381) scale(1,1) translate(0,0)" writing-mode="lr" x="1443.99" xml:space="preserve" y="421.88" zvalue="124">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1466.33,402.381) scale(1,1) translate(0,3.50281e-13)" writing-mode="lr" x="1466.33" xml:space="preserve" y="406.88" zvalue="127">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1500.73,490.32) scale(1,1) translate(1.63992e-13,0)" writing-mode="lr" x="1500.73" xml:space="preserve" y="494.82" zvalue="130">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1302,514.637) scale(1,1) translate(0,-1.12496e-13)" writing-mode="lr" x="1302" xml:space="preserve" y="519.14" zvalue="145">1020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1461.26,587.916) scale(1,1) translate(0,2.57534e-13)" writing-mode="lr" x="1461.26" xml:space="preserve" y="592.42" zvalue="146">0021</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,521.667,611.833) scale(1,1) translate(0,0)" writing-mode="lr" x="521.67" xml:space="preserve" y="616.33" zvalue="149">10.5kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,618.4,921.667) scale(1,1) translate(0,0)" writing-mode="lr" x="618.4" xml:space="preserve" y="926.17" zvalue="169">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,716.325,685.222) scale(1,1) translate(0,0)" writing-mode="lr" x="716.3200000000001" xml:space="preserve" y="689.72" zvalue="181">#1励磁变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="297" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1784.46,615.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1784.46" xml:space="preserve" y="619.83" zvalue="299">10.5kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,655.606,687) scale(1,1) translate(-1.42822e-13,0)" writing-mode="lr" x="655.61" xml:space="preserve" y="691.5" zvalue="303">031</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="315" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,662.494,755.528) scale(1,1) translate(0,0)" writing-mode="lr" x="662.49" xml:space="preserve" y="760.03" zvalue="315">03167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="318" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,529.659,804.5) scale(1,1) translate(0,0)" writing-mode="lr" x="529.66" xml:space="preserve" y="809" zvalue="318">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="325" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,687.787,793.176) scale(1,1) translate(0,0)" writing-mode="lr" x="687.79" xml:space="preserve" y="797.6799999999999" zvalue="325">0912</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="338" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,858.15,925.417) scale(1,1) translate(0,0)" writing-mode="lr" x="858.15" xml:space="preserve" y="929.92" zvalue="334">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="337" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,952.325,685.222) scale(1,1) translate(0,0)" writing-mode="lr" x="952.3200000000001" xml:space="preserve" y="689.72" zvalue="336">#2励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="336" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,890.356,687) scale(1,1) translate(-1.94947e-13,0)" writing-mode="lr" x="890.36" xml:space="preserve" y="691.5" zvalue="338">032</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="335" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,882.494,755.528) scale(1,1) translate(0,0)" writing-mode="lr" x="882.49" xml:space="preserve" y="760.03" zvalue="345">03267</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="334" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,765.659,804.5) scale(1,1) translate(0,0)" writing-mode="lr" x="765.66" xml:space="preserve" y="809" zvalue="349">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="333" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,924.828,793.176) scale(1,1) translate(0,0)" writing-mode="lr" x="924.83" xml:space="preserve" y="797.6799999999999" zvalue="355">0922</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="365" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1479.4,922.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1479.4" xml:space="preserve" y="927.42" zvalue="362">#3发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="364" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1572.32,685.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1572.32" xml:space="preserve" y="689.72" zvalue="364">#3励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="363" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1511.61,687) scale(1,1) translate(-3.32892e-13,0)" writing-mode="lr" x="1511.61" xml:space="preserve" y="691.5" zvalue="366">033</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="362" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1518.49,763.528) scale(1,1) translate(0,0)" writing-mode="lr" x="1518.49" xml:space="preserve" y="768.03" zvalue="373">03367</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="361" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1385.66,804.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1385.66" xml:space="preserve" y="809" zvalue="377">0931</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="360" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1544.83,801.176) scale(1,1) translate(0,0)" writing-mode="lr" x="1544.83" xml:space="preserve" y="805.6799999999999" zvalue="383">0932</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="389" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1023.75,449.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1023.75" xml:space="preserve" y="453.88" zvalue="390">10.5kVⅠ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="391" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,990.039,585.902) scale(1,1) translate(0,2.5664e-13)" writing-mode="lr" x="990.04" xml:space="preserve" y="590.4" zvalue="392">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="395" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1739.75,457.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1739.75" xml:space="preserve" y="461.88" zvalue="396">10.5kVⅡ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="394" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1707.29,585.902) scale(1,1) translate(0,2.5664e-13)" writing-mode="lr" x="1707.29" xml:space="preserve" y="590.4" zvalue="398">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="405" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1060.42,671.722) scale(1,1) translate(1.16018e-13,0)" writing-mode="lr" x="1060.42" xml:space="preserve" y="676.22" zvalue="403">0341</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="406" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1070.71,709.73) scale(1,1) translate(0,0)" writing-mode="lr" x="1070.71" xml:space="preserve" y="714.23" zvalue="405">034</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="415" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1054.88,878.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1054.88" xml:space="preserve" y="882.75" zvalue="415">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="419" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1172.39,811.712) scale(1,1) translate(0,8.87901e-14)" writing-mode="lr" x="1172.39" xml:space="preserve" y="816.21" zvalue="420">03467</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="425" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1656.42,671.722) scale(1,1) translate(-5.83e-12,0)" writing-mode="lr" x="1656.42" xml:space="preserve" y="676.22" zvalue="424">0351</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="424" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1666.71,709.73) scale(1,1) translate(0,0)" writing-mode="lr" x="1666.71" xml:space="preserve" y="714.23" zvalue="426">035</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="423" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1650.88,886.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1650.88" xml:space="preserve" y="890.75" zvalue="430">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="422" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1772.39,823.712) scale(1,1) translate(0,9.01224e-14)" writing-mode="lr" x="1772.39" xml:space="preserve" y="828.21" zvalue="434">03567</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="85" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="498"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="500">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="501">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="502">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="503">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="504">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="506">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="507">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="508">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="509">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="510">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="511">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241.625,334) scale(1,1) translate(0,0)" writing-mode="lr" x="241.63" xml:space="preserve" y="338.5" zvalue="512">10.5kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,359.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="363.75" zvalue="513">10.5kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="521">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="522">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="525">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="527">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="529">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="530">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="531">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="533">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,208.5,953) scale(1,1) translate(0,0)" writing-mode="lr" x="208.5" xml:space="preserve" y="959" zvalue="538">LangWaiHe 01-2018</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,154,987) scale(1,1) translate(0,0)" writing-mode="lr" x="154" xml:space="preserve" y="993" zvalue="540">李宏梅</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,326,986) scale(1,1) translate(0,0)" writing-mode="lr" x="326" xml:space="preserve" y="992" zvalue="542">20211205</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,907.5,514) scale(1,1) translate(0,0)" writing-mode="lr" x="907.5" xml:space="preserve" y="518.5" zvalue="567">63MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1504,512) scale(1,1) translate(0,0)" writing-mode="lr" x="1504" xml:space="preserve" y="516.5" zvalue="568">40MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,621,941.5) scale(1,1) translate(0,0)" writing-mode="lr" x="621" xml:space="preserve" y="946" zvalue="569">15MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,857,941.5) scale(1,1) translate(0,0)" writing-mode="lr" x="857" xml:space="preserve" y="946" zvalue="571">30MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1476,937.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1476" xml:space="preserve" y="942" zvalue="573">30MW</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="520"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="230">
   <path class="kv110" d="M 687.5 315.84 L 1611.25 315.84" stroke-width="6" zvalue="33"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674232827908" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674232827908"/></metadata>
  <path d="M 687.5 315.84 L 1611.25 315.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="v10500" d="M 515 631.33 L 1157.5 631.33" stroke-width="6" zvalue="148"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674232762373" ObjectName="10.5kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674232762373"/></metadata>
  <path d="M 515 631.33 L 1157.5 631.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="296">
   <path class="v10500" d="M 1373.75 638.33 L 1806.83 638.33" stroke-width="6" zvalue="298"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674232893444" ObjectName="10.5kVⅡ母"/>
   <cge:TPSR_Ref TObjectID="9288674232893444"/></metadata>
  <path d="M 1373.75 638.33 L 1806.83 638.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="273">
   <use class="kv110" height="20" transform="rotate(0,969.604,216.48) scale(1.22222,1.11111) translate(-175.18,-20.5369)" width="10" x="963.4924242424242" xlink:href="#Breaker:开关_0" y="205.3686866567593" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924469850117" ObjectName="110kV郎外河电站线131断路器"/>
   <cge:TPSR_Ref TObjectID="6473924469850117"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,969.604,216.48) scale(1.22222,1.11111) translate(-175.18,-20.5369)" width="10" x="963.4924242424242" y="205.3686866567593"/></g>
  <g id="173">
   <use class="kv110" height="20" transform="rotate(0,829.883,411.556) scale(1.22222,1.11111) translate(-149.777,-40.0444)" width="10" x="823.7718357195831" xlink:href="#Breaker:开关_0" y="400.4444444444444" zvalue="97"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924469784581" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473924469784581"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,829.883,411.556) scale(1.22222,1.11111) translate(-149.777,-40.0444)" width="10" x="823.7718357195831" y="400.4444444444444"/></g>
  <g id="150">
   <use class="kv110" height="20" transform="rotate(0,1423.22,418.381) scale(1.22222,1.11111) translate(-257.655,-40.727)" width="10" x="1417.105169052916" xlink:href="#Breaker:开关_0" y="407.270202020202" zvalue="123"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924469719045" ObjectName="#2主变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473924469719045"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1423.22,418.381) scale(1.22222,1.11111) translate(-257.655,-40.727)" width="10" x="1417.105169052916" y="407.270202020202"/></g>
  <g id="301">
   <use class="v10500" height="20" transform="rotate(0,619.356,684.289) scale(2.3789,2.3789) translate(-352.107,-382.85)" width="10" x="607.4610155244857" xlink:href="#Breaker:小车断路器_0" y="660.5" zvalue="302"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924469915653" ObjectName="#1发电机031断路器"/>
   <cge:TPSR_Ref TObjectID="6473924469915653"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,619.356,684.289) scale(2.3789,2.3789) translate(-352.107,-382.85)" width="10" x="607.4610155244857" y="660.5"/></g>
  <g id="357">
   <use class="v10500" height="20" transform="rotate(0,854.106,684.289) scale(2.3789,2.3789) translate(-488.177,-382.85)" width="10" x="842.2110155244857" xlink:href="#Breaker:小车断路器_0" y="660.5" zvalue="337"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924469981189" ObjectName="#2发电机032断路器"/>
   <cge:TPSR_Ref TObjectID="6473924469981189"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,854.106,684.289) scale(2.3789,2.3789) translate(-488.177,-382.85)" width="10" x="842.2110155244857" y="660.5"/></g>
  <g id="384">
   <use class="v10500" height="20" transform="rotate(0,1475.36,684.289) scale(2.3789,2.3789) translate(-848.277,-382.85)" width="10" x="1463.461015524486" xlink:href="#Breaker:小车断路器_0" y="660.5" zvalue="365"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924470046725" ObjectName="#3发电机033断路器"/>
   <cge:TPSR_Ref TObjectID="6473924470046725"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1475.36,684.289) scale(2.3789,2.3789) translate(-848.277,-382.85)" width="10" x="1463.461015524486" y="660.5"/></g>
  <g id="401">
   <use class="v10500" height="20" transform="rotate(0,1102.1,710.73) scale(1.22222,1.11111) translate(-199.271,-69.9619)" width="10" x="1095.992424242424" xlink:href="#Breaker:开关_0" y="699.6186828613281" zvalue="404"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924470112261" ObjectName="#1站用变034断路器"/>
   <cge:TPSR_Ref TObjectID="6473924470112261"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1102.1,710.73) scale(1.22222,1.11111) translate(-199.271,-69.9619)" width="10" x="1095.992424242424" y="699.6186828613281"/></g>
  <g id="434">
   <use class="v10500" height="20" transform="rotate(0,1698.1,710.73) scale(1.22222,1.11111) translate(-307.635,-69.9619)" width="10" x="1691.992424242424" xlink:href="#Breaker:开关_0" y="699.618682842062" zvalue="425"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924470177797" ObjectName="#2站用变035断路器"/>
   <cge:TPSR_Ref TObjectID="6473924470177797"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1698.1,710.73) scale(1.22222,1.11111) translate(-307.635,-69.9619)" width="10" x="1691.992424242424" y="699.618682842062"/></g>
  <g id="436">
   <use class="v10500" height="20" transform="rotate(0,588.106,578.039) scale(2.3789,2.3789) translate(-333.993,-321.264)" width="10" x="576.2110155244857" xlink:href="#Breaker:小车断路器_0" y="554.2500034610807" zvalue="438"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924470243333" ObjectName="备用1"/>
   <cge:TPSR_Ref TObjectID="6473924470243333"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,588.106,578.039) scale(2.3789,2.3789) translate(-333.993,-321.264)" width="10" x="576.2110155244857" y="554.2500034610807"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="272">
   <use class="kv110" height="30" transform="rotate(0,969.604,264.035) scale(-1.11111,-0.814815) translate(-1841.41,-590.857)" width="15" x="961.270202020202" xlink:href="#Disconnector:刀闸_0" y="251.8131446645718" zvalue="35"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449525645317" ObjectName="110kV郎外河电站线1311隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449525645317"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,969.604,264.035) scale(-1.11111,-0.814815) translate(-1841.41,-590.857)" width="15" x="961.270202020202" y="251.8131446645718"/></g>
  <g id="271">
   <use class="kv110" height="30" transform="rotate(0,969.604,156.035) scale(-1.11111,-0.814815) translate(-1841.41,-350.311)" width="15" x="961.270202046693" xlink:href="#Disconnector:刀闸_0" y="143.8131311012038" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449525579781" ObjectName="110kV郎外河电站线1316隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449525579781"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,969.604,156.035) scale(-1.11111,-0.814815) translate(-1841.41,-350.311)" width="15" x="961.270202046693" y="143.8131311012038"/></g>
  <g id="188">
   <use class="kv110" height="30" transform="rotate(0,1475.13,222.327) scale(-1.11111,-0.814815) translate(-2801.92,-497.962)" width="15" x="1466.799519377523" xlink:href="#Disconnector:刀闸_0" y="210.1051723994906" zvalue="84"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449524924422" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449524924422"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1475.13,222.327) scale(-1.11111,-0.814815) translate(-2801.92,-497.962)" width="15" x="1466.799519377523" y="210.1051723994906"/></g>
  <g id="174">
   <use class="kv110" height="30" transform="rotate(0,829.883,358.444) scale(1.11111,0.814815) translate(-82.155,78.6869)" width="15" x="821.5496134841156" xlink:href="#Disconnector:刀闸_0" y="346.2222222222222" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449524531206" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449524531206"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,829.883,358.444) scale(1.11111,0.814815) translate(-82.155,78.6869)" width="15" x="821.5496134841156" y="346.2222222222222"/></g>
  <g id="156">
   <use class="v10500" height="26" transform="rotate(0,830.3,584.578) scale(1.32692,1.32692) translate(-202.278,-139.776)" width="14" x="821.0116097822633" xlink:href="#Disconnector:联体手车刀闸_0" y="567.3279724121094" zvalue="118"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449524203526" ObjectName="#1主变10.5kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449524203526"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,830.3,584.578) scale(1.32692,1.32692) translate(-202.278,-139.776)" width="14" x="821.0116097822633" y="567.3279724121094"/></g>
  <g id="151">
   <use class="kv110" height="30" transform="rotate(0,1423.22,365.27) scale(1.11111,0.814815) translate(-141.488,80.2382)" width="15" x="1414.882946817449" xlink:href="#Disconnector:刀闸_0" y="353.0479797979798" zvalue="121"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449524137990" ObjectName="#2主变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449524137990"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1423.22,365.27) scale(1.11111,0.814815) translate(-141.488,80.2382)" width="15" x="1414.882946817449" y="353.0479797979798"/></g>
  <g id="130">
   <use class="v10500" height="26" transform="rotate(0,1424.15,583.616) scale(1.40093,1.40093) translate(-404.771,-161.813)" width="14" x="1414.344943115597" xlink:href="#Disconnector:联体手车刀闸_0" y="565.4037299878669" zvalue="144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449523810310" ObjectName="#2主变10.5kV侧0021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449523810310"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1424.15,583.616) scale(1.40093,1.40093) translate(-404.771,-161.813)" width="14" x="1414.344943115597" y="565.4037299878669"/></g>
  <g id="317">
   <use class="v10500" height="42" transform="rotate(0,566.034,805.5) scale(1.25,1.25) translate(-109.457,-155.85)" width="30" x="547.2836538461538" xlink:href="#Disconnector:手车刀闸带避雷器_0" y="779.25" zvalue="317"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449525972997" ObjectName="#1发电机0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449525972997"/></metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,566.034,805.5) scale(1.25,1.25) translate(-109.457,-155.85)" width="30" x="547.2836538461538" y="779.25"/></g>
  <g id="324">
   <use class="v10500" height="26" transform="rotate(0,715.273,794.176) scale(1.43519,1.14815) translate(-214.278,-100.548)" width="12" x="706.6621170571469" xlink:href="#Disconnector:刀闸333_0" y="779.2500003224989" zvalue="324"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449526104069" ObjectName="#1发电机0912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449526104069"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,715.273,794.176) scale(1.43519,1.14815) translate(-214.278,-100.548)" width="12" x="706.6621170571469" y="779.2500003224989"/></g>
  <g id="349">
   <use class="v10500" height="42" transform="rotate(0,802.034,805.5) scale(1.25,1.25) translate(-156.657,-155.85)" width="30" x="783.2836538461538" xlink:href="#Disconnector:手车刀闸带避雷器_0" y="779.25" zvalue="347"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449526431749" ObjectName="#2发电机0921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449526431749"/></metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,802.034,805.5) scale(1.25,1.25) translate(-156.657,-155.85)" width="30" x="783.2836538461538" y="779.25"/></g>
  <g id="343">
   <use class="v10500" height="26" transform="rotate(0,952.314,794.176) scale(1.43519,1.14815) translate(-286.155,-100.548)" width="12" x="943.7026168867719" xlink:href="#Disconnector:刀闸333_0" y="779.2500003224989" zvalue="354"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449526300677" ObjectName="#2发电机0922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449526300677"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,952.314,794.176) scale(1.43519,1.14815) translate(-286.155,-100.548)" width="12" x="943.7026168867719" y="779.2500003224989"/></g>
  <g id="376">
   <use class="v10500" height="42" transform="rotate(0,1422.03,805.5) scale(1.25,1.25) translate(-280.657,-155.85)" width="30" x="1403.283653846154" xlink:href="#Disconnector:手车刀闸带避雷器_0" y="779.25" zvalue="375"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449527087110" ObjectName="#3发电机0931隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449527087110"/></metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1422.03,805.5) scale(1.25,1.25) translate(-280.657,-155.85)" width="30" x="1403.283653846154" y="779.25"/></g>
  <g id="370">
   <use class="v10500" height="26" transform="rotate(0,1572.31,802.176) scale(1.43519,1.14815) translate(-474.155,-101.581)" width="12" x="1563.702616886772" xlink:href="#Disconnector:刀闸333_0" y="787.2500003224989" zvalue="382"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449526956038" ObjectName="#3发电机0932隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449526956038"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1572.31,802.176) scale(1.43519,1.14815) translate(-474.155,-101.581)" width="12" x="1563.702616886772" y="787.2500003224989"/></g>
  <g id="390">
   <use class="v10500" height="26" transform="rotate(0,1019.81,586.902) scale(1.43519,1.14815) translate(-306.623,-73.8034)" width="12" x="1011.202616886772" xlink:href="#Disconnector:刀闸333_0" y="571.9761203176159" zvalue="391"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449527611398" ObjectName="10.5kVⅠ母电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449527611398"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1019.81,586.902) scale(1.43519,1.14815) translate(-306.623,-73.8034)" width="12" x="1011.202616886772" y="571.9761203176159"/></g>
  <g id="398">
   <use class="v10500" height="26" transform="rotate(0,1735.81,586.902) scale(1.43519,1.14815) translate(-523.732,-73.8034)" width="12" x="1727.202616886772" xlink:href="#Disconnector:刀闸333_0" y="571.9761207944531" zvalue="397"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449527676934" ObjectName="10.5kVⅡ母电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449527676934"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1735.81,586.902) scale(1.43519,1.14815) translate(-523.732,-73.8034)" width="12" x="1727.202616886772" y="571.9761207944531"/></g>
  <g id="400">
   <use class="v10500" height="30" transform="rotate(0,1101.38,672.722) scale(-1.11111,-0.814815) translate(-2091.79,-1501.11)" width="15" x="1093.049519377523" xlink:href="#Disconnector:刀闸_0" y="660.5000000037844" zvalue="402"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449527808006" ObjectName="#1站用变0341隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449527808006"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1101.38,672.722) scale(-1.11111,-0.814815) translate(-2091.79,-1501.11)" width="15" x="1093.049519377523" y="660.5000000037844"/></g>
  <g id="435">
   <use class="v10500" height="30" transform="rotate(0,1697.38,672.722) scale(-1.11111,-0.814815) translate(-3224.19,-1501.11)" width="15" x="1689.049519377523" xlink:href="#Disconnector:刀闸_0" y="660.500000242203" zvalue="423"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449528397829" ObjectName="#2站用变0351隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449528397829"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1697.38,672.722) scale(-1.11111,-0.814815) translate(-3224.19,-1501.11)" width="15" x="1689.049519377523" y="660.500000242203"/></g>
  <g id="437">
   <use class="v10500" height="30" transform="rotate(0,618.354,510.285) scale(-1.11111,-0.814815) translate(-1174.04,-1139.32)" width="15" x="610.0202020202019" xlink:href="#Disconnector:刀闸_0" y="498.0631446645718" zvalue="440"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449528463365" ObjectName="备用2"/>
   <cge:TPSR_Ref TObjectID="6192449528463365"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,618.354,510.285) scale(-1.11111,-0.814815) translate(-1174.04,-1139.32)" width="15" x="610.0202020202019" y="498.0631446645718"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="229">
   <path class="kv110" d="M 969.51 275.85 L 969.51 315.84" stroke-width="1" zvalue="39"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="230@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 969.51 275.85 L 969.51 315.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="kv110" d="M 969.54 252.02 L 969.54 227.09" stroke-width="1" zvalue="41"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@1" LinkObjectIDznd="273@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 969.54 252.02 L 969.54 227.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv110" d="M 969.56 205.85 L 969.51 167.85" stroke-width="1" zvalue="42"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="273@0" LinkObjectIDznd="271@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 969.56 205.85 L 969.51 167.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv110" d="M 969.54 144.02 L 969.54 91.59" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@1" LinkObjectIDznd="267@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 969.54 144.02 L 969.54 91.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="kv110" d="M 1006.38 132.76 L 969.54 132.76" stroke-width="1" zvalue="52"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@0" LinkObjectIDznd="225" MaxPinNum="2"/>
   </metadata>
  <path d="M 1006.38 132.76 L 969.54 132.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="223">
   <path class="kv110" d="M 1006.38 187.2 L 969.53 187.2" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="264@0" LinkObjectIDznd="226" MaxPinNum="2"/>
   </metadata>
  <path d="M 1006.38 187.2 L 969.53 187.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="kv110" d="M 1006.38 235.98 L 969.54 235.98" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265@0" LinkObjectIDznd="228" MaxPinNum="2"/>
   </metadata>
  <path d="M 1006.38 235.98 L 969.54 235.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="218">
   <path class="kv110" d="M 959.96 103.68 L 969.54 103.68" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@0" LinkObjectIDznd="225" MaxPinNum="2"/>
   </metadata>
  <path d="M 959.96 103.68 L 969.54 103.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv110" d="M 1475.06 145.7 L 1475.06 210.31" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@0" LinkObjectIDznd="188@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1475.06 145.7 L 1475.06 210.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv110" d="M 1475.04 234.15 L 1475.04 315.84" stroke-width="1" zvalue="91"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@0" LinkObjectIDznd="230@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1475.04 234.15 L 1475.04 315.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv110" d="M 1514.43 265.27 L 1475.04 265.27" stroke-width="1" zvalue="92"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@0" LinkObjectIDznd="180" MaxPinNum="2"/>
   </metadata>
  <path d="M 1514.43 265.27 L 1475.04 265.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv110" d="M 1505.93 166.69 L 1475.06 166.69" stroke-width="1" zvalue="93"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@0" LinkObjectIDznd="183" MaxPinNum="2"/>
   </metadata>
  <path d="M 1505.93 166.69 L 1475.06 166.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv110" d="M 829.95 370.46 L 829.95 400.93" stroke-width="1" zvalue="99"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@1" LinkObjectIDznd="173@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 829.95 370.46 L 829.95 400.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv110" d="M 862.16 383.39 L 829.95 383.39" stroke-width="1" zvalue="104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="172" MaxPinNum="2"/>
   </metadata>
  <path d="M 862.16 383.39 L 829.95 383.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv110" d="M 829.98 346.63 L 829.98 315.84" stroke-width="1" zvalue="106"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="230@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 829.98 346.63 L 829.98 315.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="v10500" d="M 830.28 569.58 L 830.28 537.51" stroke-width="1" zvalue="119"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="156@0" LinkObjectIDznd="170@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 830.28 569.58 L 830.28 537.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv110" d="M 1423.28 377.28 L 1423.28 407.75" stroke-width="1" zvalue="125"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@1" LinkObjectIDznd="150@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1423.28 377.28 L 1423.28 407.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv110" d="M 1455.49 390.21 L 1423.28 390.21" stroke-width="1" zvalue="129"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="148@0" LinkObjectIDznd="149" MaxPinNum="2"/>
   </metadata>
  <path d="M 1455.49 390.21 L 1423.28 390.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv110" d="M 1423.31 353.45 L 1423.31 315.84" stroke-width="1" zvalue="132"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@0" LinkObjectIDznd="230@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1423.31 353.45 L 1423.31 315.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="v10500" d="M 830.33 599.6 L 830.33 631.33" stroke-width="1" zvalue="152"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="156@1" LinkObjectIDznd="133@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 830.33 599.6 L 830.33 631.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="286">
   <path class="kv110" d="M 829.73 446.33 L 829.73 422.17" stroke-width="1" zvalue="292"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="173@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 829.73 446.33 L 829.73 422.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="288">
   <path class="kv110" d="M 969.54 134.3 L 954.75 134.3" stroke-width="1" zvalue="293"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="225" LinkObjectIDznd="217@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 969.54 134.3 L 954.75 134.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="290">
   <path class="kv110" d="M 1423.3 449.15 L 1423.3 428.99" stroke-width="1" zvalue="294"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="147@0" LinkObjectIDznd="150@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1423.3 449.15 L 1423.3 428.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="291">
   <path class="v10500" d="M 1424.14 567.79 L 1424.14 540.34" stroke-width="1" zvalue="295"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@0" LinkObjectIDznd="147@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1424.14 567.79 L 1424.14 540.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="303">
   <path class="v10500" d="M 619.36 865.69 L 619.36 705.7" stroke-width="1" zvalue="303"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@0" LinkObjectIDznd="301@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 619.36 865.69 L 619.36 705.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="304">
   <path class="v10500" d="M 619.36 662.28 L 619.36 631.33" stroke-width="1" zvalue="304"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="301@0" LinkObjectIDznd="133@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 619.36 662.28 L 619.36 631.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="308">
   <path class="v10500" d="M 1424.18 599.47 L 1424.18 638.33" stroke-width="1" zvalue="308"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@1" LinkObjectIDznd="296@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1424.18 599.47 L 1424.18 638.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="312">
   <path class="v10500" d="M 626.01 721.94 L 619.36 721.94" stroke-width="1" zvalue="312"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="311@0" LinkObjectIDznd="303" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.01 721.94 L 619.36 721.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="314">
   <path class="v10500" d="M 661.91 741.14 L 619.36 741.14" stroke-width="1" zvalue="315"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="313@0" LinkObjectIDznd="303" MaxPinNum="2"/>
   </metadata>
  <path d="M 661.91 741.14 L 619.36 741.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="323">
   <path class="v10500" d="M 562.97 837 L 562.97 831.44" stroke-width="1" zvalue="322"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="322@0" LinkObjectIDznd="317@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 562.97 837 L 562.97 831.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="327">
   <path class="v10500" d="M 715.28 732.88 L 715.28 768 L 619.36 768" stroke-width="1" zvalue="326"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@0" LinkObjectIDznd="303" MaxPinNum="2"/>
   </metadata>
  <path d="M 715.28 732.88 L 715.28 768 L 619.36 768" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="330">
   <path class="v10500" d="M 715.28 768 L 715.28 779.34" stroke-width="1" zvalue="330"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="327" LinkObjectIDznd="324@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 715.28 768 L 715.28 779.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="331">
   <path class="v10500" d="M 715.39 809.06 L 715.39 837" stroke-width="1" zvalue="331"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="324@0" LinkObjectIDznd="329@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 715.39 809.06 L 715.39 837" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="356">
   <path class="v10500" d="M 855.36 865.69 L 855.36 705.7" stroke-width="1" zvalue="339"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="359@0" LinkObjectIDznd="357@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 855.36 865.69 L 855.36 705.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="355">
   <path class="v10500" d="M 854.11 662.28 L 854.11 631.33" stroke-width="1" zvalue="340"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="357@0" LinkObjectIDznd="133@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 854.11 662.28 L 854.11 631.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="352">
   <path class="v10500" d="M 862.01 721.94 L 855.36 721.94" stroke-width="1" zvalue="343"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="353@0" LinkObjectIDznd="356" MaxPinNum="2"/>
   </metadata>
  <path d="M 862.01 721.94 L 855.36 721.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="350">
   <path class="v10500" d="M 897.91 741.14 L 855.36 741.14" stroke-width="1" zvalue="346"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="351@0" LinkObjectIDznd="356" MaxPinNum="2"/>
   </metadata>
  <path d="M 897.91 741.14 L 855.36 741.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="344">
   <path class="v10500" d="M 798.97 837 L 798.97 831.44" stroke-width="1" zvalue="353"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="345@0" LinkObjectIDznd="349@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 798.97 837 L 798.97 831.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="342">
   <path class="v10500" d="M 951.28 732.88 L 951.28 768 L 855.36 768" stroke-width="1" zvalue="356"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="358@0" LinkObjectIDznd="356" MaxPinNum="2"/>
   </metadata>
  <path d="M 951.28 732.88 L 951.28 768 L 855.36 768" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="340">
   <path class="v10500" d="M 951.28 768 L 951.28 779.34" stroke-width="1" zvalue="358"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="342" LinkObjectIDznd="343@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 951.28 768 L 951.28 779.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="339">
   <path class="v10500" d="M 952.43 809.06 L 952.43 837" stroke-width="1" zvalue="359"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="343@0" LinkObjectIDznd="341@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 952.43 809.06 L 952.43 837" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="383">
   <path class="v10500" d="M 1475.36 865.69 L 1475.36 705.7" stroke-width="1" zvalue="367"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="386@0" LinkObjectIDznd="384@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1475.36 865.69 L 1475.36 705.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="379">
   <path class="v10500" d="M 1482.01 729.94 L 1475.36 729.94" stroke-width="1" zvalue="371"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="380@0" LinkObjectIDznd="383" MaxPinNum="2"/>
   </metadata>
  <path d="M 1482.01 729.94 L 1475.36 729.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="377">
   <path class="v10500" d="M 1517.91 749.14 L 1475.36 749.14" stroke-width="1" zvalue="374"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="378@0" LinkObjectIDznd="383" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517.91 749.14 L 1475.36 749.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="371">
   <path class="v10500" d="M 1418.97 837 L 1418.97 831.44" stroke-width="1" zvalue="381"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="372@0" LinkObjectIDznd="376@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1418.97 837 L 1418.97 831.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="369">
   <path class="v10500" d="M 1571.28 732.88 L 1571.28 776 L 1475.36 776" stroke-width="1" zvalue="384"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="385@0" LinkObjectIDznd="383" MaxPinNum="2"/>
   </metadata>
  <path d="M 1571.28 732.88 L 1571.28 776 L 1475.36 776" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="367">
   <path class="v10500" d="M 1571.28 732.88 L 1571.28 787.34" stroke-width="1" zvalue="386"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="369" LinkObjectIDznd="370@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1571.28 732.88 L 1571.28 787.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="366">
   <path class="v10500" d="M 1572.43 817.06 L 1572.43 837" stroke-width="1" zvalue="387"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="370@0" LinkObjectIDznd="368@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1572.43 817.06 L 1572.43 837" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="392">
   <path class="v10500" d="M 1020.68 521.53 L 1020.68 572.06" stroke-width="1" zvalue="392"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="388@0" LinkObjectIDznd="390@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1020.68 521.53 L 1020.68 572.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="393">
   <path class="v10500" d="M 1019.93 601.79 L 1019.93 631.33" stroke-width="1" zvalue="393"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="390@0" LinkObjectIDznd="133@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1019.93 601.79 L 1019.93 631.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="397">
   <path class="v10500" d="M 1736.68 529.53 L 1736.68 572.06" stroke-width="1" zvalue="399"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="399@0" LinkObjectIDznd="398@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1736.68 529.53 L 1736.68 572.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="396">
   <path class="v10500" d="M 1735.93 601.79 L 1735.93 638.33" stroke-width="1" zvalue="400"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="398@0" LinkObjectIDznd="296@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1735.93 601.79 L 1735.93 638.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="407">
   <path class="v10500" d="M 1101.31 660.71 L 1101.31 631.33" stroke-width="1" zvalue="408"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="400@1" LinkObjectIDznd="133@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1101.31 660.71 L 1101.31 631.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="408">
   <path class="v10500" d="M 1101.29 684.54 L 1101.29 700.1" stroke-width="1" zvalue="409"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="400@0" LinkObjectIDznd="401@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1101.29 684.54 L 1101.29 700.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="416">
   <path class="v10500" d="M 1102.19 721.34 L 1102.19 855.81" stroke-width="1" zvalue="415"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="401@1" LinkObjectIDznd="413@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1102.19 721.34 L 1102.19 855.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="420">
   <path class="v10500" d="M 1136.75 798.98 L 1136.75 779.25 L 1102.19 779.25" stroke-width="1" zvalue="420"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="418@0" LinkObjectIDznd="416" MaxPinNum="2"/>
   </metadata>
  <path d="M 1136.75 798.98 L 1136.75 779.25 L 1102.19 779.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="421">
   <path class="v10500" d="M 1079.19 791.26 L 1079.19 781.75 L 1102.19 781.75" stroke-width="1" zvalue="421"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="417@0" LinkObjectIDznd="416" MaxPinNum="2"/>
   </metadata>
  <path d="M 1079.19 791.26 L 1079.19 781.75 L 1102.19 781.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="433">
   <path class="v10500" d="M 1697.31 660.71 L 1697.31 638.33" stroke-width="1" zvalue="427"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="435@1" LinkObjectIDznd="296@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1697.31 660.71 L 1697.31 638.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="432">
   <path class="v10500" d="M 1697.29 684.54 L 1697.29 700.1" stroke-width="1" zvalue="428"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="435@0" LinkObjectIDznd="434@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1697.29 684.54 L 1697.29 700.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="430">
   <path class="v10500" d="M 1698.19 721.34 L 1698.19 863.81" stroke-width="1" zvalue="431"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="434@1" LinkObjectIDznd="431@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1698.19 721.34 L 1698.19 863.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="427">
   <path class="v10500" d="M 1736.75 810.98 L 1736.75 787.25 L 1698.19 787.25" stroke-width="1" zvalue="435"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="428@0" LinkObjectIDznd="430" MaxPinNum="2"/>
   </metadata>
  <path d="M 1736.75 810.98 L 1736.75 787.25 L 1698.19 787.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="426">
   <path class="v10500" d="M 1679.19 799.26 L 1679.19 789.75 L 1698.19 789.75" stroke-width="1" zvalue="436"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="429@0" LinkObjectIDznd="430" MaxPinNum="2"/>
   </metadata>
  <path d="M 1679.19 799.26 L 1679.19 789.75 L 1698.19 789.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="439">
   <path class="v10500" d="M 588.11 599.45 L 588.11 631.33" stroke-width="1" zvalue="443"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="436@1" LinkObjectIDznd="133@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 588.11 599.45 L 588.11 631.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="440">
   <path class="v10500" d="M 588.11 556.03 L 588.11 471.44" stroke-width="1" zvalue="444"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="436@0" LinkObjectIDznd="452@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 588.11 556.03 L 588.11 471.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="441">
   <path class="v10500" d="M 588.11 538 L 618.26 538 L 618.26 522.1" stroke-width="1" zvalue="445"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="440" LinkObjectIDznd="437@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 588.11 538 L 618.26 538 L 618.26 522.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="442">
   <path class="v10500" d="M 576.69 527.03 L 576.69 538 L 588.11 538" stroke-width="1" zvalue="446"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="438@0" LinkObjectIDznd="441" MaxPinNum="2"/>
   </metadata>
  <path d="M 576.69 527.03 L 576.69 538 L 588.11 538" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="443">
   <path class="v10500" d="M 561.97 753.36 L 561.97 766.75 L 619.36 766.75" stroke-width="1" zvalue="447"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="309@0" LinkObjectIDznd="303" MaxPinNum="2"/>
   </metadata>
  <path d="M 561.97 753.36 L 561.97 766.75 L 619.36 766.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="444">
   <path class="v10500" d="M 561.97 788.31 L 561.97 766.75" stroke-width="1" zvalue="448"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="317@0" LinkObjectIDznd="443" MaxPinNum="2"/>
   </metadata>
  <path d="M 561.97 788.31 L 561.97 766.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="445">
   <path class="v10500" d="M 797.97 753.36 L 797.97 763 L 855.36 763" stroke-width="1" zvalue="449"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="354@0" LinkObjectIDznd="356" MaxPinNum="2"/>
   </metadata>
  <path d="M 797.97 753.36 L 797.97 763 L 855.36 763" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="448">
   <path class="v10500" d="M 1417.97 753.36 L 1417.97 774.25 L 1475.36 774.25" stroke-width="1" zvalue="452"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="381@0" LinkObjectIDznd="383" MaxPinNum="2"/>
   </metadata>
  <path d="M 1417.97 753.36 L 1417.97 774.25 L 1475.36 774.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="449">
   <path class="v10500" d="M 1417.97 788.31 L 1417.97 774.25" stroke-width="1" zvalue="453"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="376@0" LinkObjectIDznd="448" MaxPinNum="2"/>
   </metadata>
  <path d="M 1417.97 788.31 L 1417.97 774.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="450">
   <path class="v10500" d="M 1475.36 662.28 L 1475.36 638.33" stroke-width="1" zvalue="454"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="384@0" LinkObjectIDznd="296@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1475.36 662.28 L 1475.36 638.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="451">
   <path class="v10500" d="M 797.97 788.31 L 797.97 753.36" stroke-width="1" zvalue="455"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="349@0" LinkObjectIDznd="445" MaxPinNum="2"/>
   </metadata>
  <path d="M 797.97 788.31 L 797.97 753.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="453">
   <path class="v10500" d="M 618.29 498.27 L 618.29 482.69" stroke-width="1" zvalue="457"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="437@1" LinkObjectIDznd="454@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 618.29 498.27 L 618.29 482.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv110" d="M 760.73 501.61 L 760.73 473 L 829.7 472.61" stroke-width="1" zvalue="486"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="170@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 760.73 501.61 L 760.73 473 L 829.7 472.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv110" d="M 1354.07 503.44 L 1354.07 476 L 1423.27 475.44" stroke-width="1" zvalue="487"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="147@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1354.07 503.44 L 1354.07 476 L 1423.27 475.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="267">
   <use class="kv110" height="30" transform="rotate(0,969.536,69.5909) scale(6.34921,1.48148) translate(-798.111,-15.3948)" width="7" x="947.3133526865755" xlink:href="#ACLineSegment:线路_0" y="47.36868665675928" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249315278854" ObjectName="110kV朗外河电站线"/>
   <cge:TPSR_Ref TObjectID="8444249315278854_5066549581578242"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,969.536,69.5909) scale(6.34921,1.48148) translate(-798.111,-15.3948)" width="7" x="947.3133526865755" y="47.36868665675928"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="265">
   <use class="kv110" height="20" transform="rotate(270,1017.21,235.924) scale(-1.11111,1.11111) translate(-1932.15,-22.4813)" width="10" x="1011.659090909091" xlink:href="#GroundDisconnector:地刀_0" y="224.8131363993942" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449525448709" ObjectName="110kV郎外河电站线13117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449525448709"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1017.21,235.924) scale(-1.11111,1.11111) translate(-1932.15,-22.4813)" width="10" x="1011.659090909091" y="224.8131363993942"/></g>
  <g id="264">
   <use class="kv110" height="20" transform="rotate(270,1017.21,187.146) scale(-1.11111,1.11111) translate(-1932.15,-17.6035)" width="10" x="1011.659090909091" xlink:href="#GroundDisconnector:地刀_0" y="176.035353323426" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449525317638" ObjectName="110kV郎外河电站线13160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449525317638"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1017.21,187.146) scale(-1.11111,1.11111) translate(-1932.15,-17.6035)" width="10" x="1011.659090909091" y="176.035353323426"/></g>
  <g id="263">
   <use class="kv110" height="20" transform="rotate(270,1017.21,132.702) scale(-1.11111,1.11111) translate(-1932.15,-12.1591)" width="10" x="1011.659091015055" xlink:href="#GroundDisconnector:地刀_0" y="121.5909088789814" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449525186566" ObjectName="110kV郎外河电站线13167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449525186566"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1017.21,132.702) scale(-1.11111,1.11111) translate(-1932.15,-12.1591)" width="10" x="1011.659091015055" y="121.5909088789814"/></g>
  <g id="187">
   <use class="kv110" height="20" transform="rotate(270,1516.76,166.633) scale(-1.11111,1.11111) translate(-2881.29,-15.5522)" width="10" x="1511.206355988033" xlink:href="#GroundDisconnector:地刀_0" y="155.5218151183355" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449524858886" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449524858886"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1516.76,166.633) scale(-1.11111,1.11111) translate(-2881.29,-15.5522)" width="10" x="1511.206355988033" y="155.5218151183355"/></g>
  <g id="185">
   <use class="kv110" height="20" transform="rotate(270,1525.26,265.216) scale(-1.11111,1.11111) translate(-2897.44,-25.4105)" width="10" x="1519.706355988033" xlink:href="#GroundDisconnector:地刀_0" y="254.1051587301588" zvalue="88"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449524727814" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449524727814"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1525.26,265.216) scale(-1.11111,1.11111) translate(-2897.44,-25.4105)" width="10" x="1519.706355988033" y="254.1051587301588"/></g>
  <g id="171">
   <use class="kv110" height="20" transform="rotate(270,872.994,383.333) scale(-1.11111,1.11111) translate(-1658.13,-37.2222)" width="10" x="867.4385023730042" xlink:href="#GroundDisconnector:地刀_0" y="372.2222222222222" zvalue="100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449524465670" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449524465670"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,872.994,383.333) scale(-1.11111,1.11111) translate(-1658.13,-37.2222)" width="10" x="867.4385023730042" y="372.2222222222222"/></g>
  <g id="157">
   <use class="kv110" height="40" transform="rotate(0,747.333,513.812) scale(-1,-1) translate(-1494.67,-1027.62)" width="40" x="727.3333333333334" xlink:href="#GroundDisconnector:中性点地刀12_0" y="493.8117249482564" zvalue="117"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449524334598" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449524334598"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,747.333,513.812) scale(-1,-1) translate(-1494.67,-1027.62)" width="40" x="727.3333333333334" y="493.8117249482564"/></g>
  <g id="148">
   <use class="kv110" height="20" transform="rotate(270,1466.33,390.159) scale(-1.11111,1.11111) translate(-2785.47,-37.9048)" width="10" x="1460.771835706338" xlink:href="#GroundDisconnector:地刀_0" y="379.0479797979798" zvalue="126"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449524072454" ObjectName="#2主变110kV侧10217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449524072454"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1466.33,390.159) scale(-1.11111,1.11111) translate(-2785.47,-37.9048)" width="10" x="1460.771835706338" y="379.0479797979798"/></g>
  <g id="131">
   <use class="kv110" height="40" transform="rotate(0,1340.67,515.637) scale(-1,-1) translate(-2681.33,-1031.27)" width="40" x="1320.666666666667" xlink:href="#GroundDisconnector:中性点地刀12_0" y="495.6374825240141" zvalue="143"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449523941382" ObjectName="#2主变110kV侧1020中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449523941382"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1340.67,515.637) scale(-1,-1) translate(-2681.33,-1031.27)" width="40" x="1320.666666666667" y="495.6374825240141"/></g>
  <g id="313">
   <use class="v10500" height="20" transform="rotate(270,672.744,741.083) scale(-1.11111,1.11111) translate(-1277.66,-72.9972)" width="10" x="667.1885023730042" xlink:href="#GroundDisconnector:地刀_0" y="729.9722222222222" zvalue="314"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449525907461" ObjectName="#1发电机03167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449525907461"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,672.744,741.083) scale(-1.11111,1.11111) translate(-1277.66,-72.9972)" width="10" x="667.1885023730042" y="729.9722222222222"/></g>
  <g id="351">
   <use class="v10500" height="20" transform="rotate(270,908.744,741.083) scale(-1.11111,1.11111) translate(-1726.06,-72.9972)" width="10" x="903.1885023730042" xlink:href="#GroundDisconnector:地刀_0" y="729.9722222222222" zvalue="344"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449526562822" ObjectName="#2发电机03267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449526562822"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,908.744,741.083) scale(-1.11111,1.11111) translate(-1726.06,-72.9972)" width="10" x="903.1885023730042" y="729.9722222222222"/></g>
  <g id="378">
   <use class="v10500" height="20" transform="rotate(270,1528.74,749.083) scale(-1.11111,1.11111) translate(-2904.06,-73.7972)" width="10" x="1523.188502373004" xlink:href="#GroundDisconnector:地刀_0" y="737.9722222222222" zvalue="372"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449527218182" ObjectName="#3发电机03367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449527218182"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1528.74,749.083) scale(-1.11111,1.11111) translate(-2904.06,-73.7972)" width="10" x="1523.188502373004" y="737.9722222222222"/></g>
  <g id="418">
   <use class="v10500" height="20" transform="rotate(0,1136.83,815.404) scale(-1.68409,1.68409) translate(-1808.45,-324.383)" width="10" x="1128.409090909091" xlink:href="#GroundDisconnector:地刀_0" y="798.5631363993944" zvalue="419"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449528070149" ObjectName="#1站用变03467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449528070149"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1136.83,815.404) scale(-1.68409,1.68409) translate(-1808.45,-324.383)" width="10" x="1128.409090909091" y="798.5631363993944"/></g>
  <g id="428">
   <use class="v10500" height="20" transform="rotate(0,1736.83,827.404) scale(-1.68409,1.68409) translate(-2764.72,-329.257)" width="10" x="1728.409090909091" xlink:href="#GroundDisconnector:地刀_0" y="810.5631363993944" zvalue="433"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449528201222" ObjectName="#2站用变03567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449528201222"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1736.83,827.404) scale(-1.68409,1.68409) translate(-2764.72,-329.257)" width="10" x="1728.409090909091" y="810.5631363993944"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="220">
   <use class="kv110" height="40" transform="rotate(270,938.354,94.8131) scale(1.25,1.25) translate(-182.671,-13.9626)" width="40" x="913.3535353535353" xlink:href="#Accessory:110kVXIANLUPT_0" y="69.81313131313141" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449525055494" ObjectName="110kV郎外河电站线线路PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,938.354,94.8131) scale(1.25,1.25) translate(-182.671,-13.9626)" width="40" x="913.3535353535353" y="69.81313131313141"/></g>
  <g id="217">
   <use class="kv110" height="26" transform="rotate(90,941.39,134.339) scale(-1.08054,1.08054) translate(-1812.13,-8.9664)" width="12" x="934.9071026678929" xlink:href="#Accessory:避雷器1_0" y="120.2917995746942" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449524989958" ObjectName="110kV郎外河电站线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,941.39,134.339) scale(-1.08054,1.08054) translate(-1812.13,-8.9664)" width="12" x="934.9071026678929" y="120.2917995746942"/></g>
  <g id="176">
   <use class="kv110" height="35" transform="rotate(0,1478.03,116.656) scale(-1.40514,1.70715) translate(-2521.8,-35.9471)" width="40" x="1449.92555095398" xlink:href="#Accessory:四卷带壁雷器母线PT_0" y="86.78059889581436" zvalue="94"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449524596742" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1478.03,116.656) scale(-1.40514,1.70715) translate(-2521.8,-35.9471)" width="40" x="1449.92555095398" y="86.78059889581436"/></g>
  <g id="168">
   <use class="v10500" height="20" transform="rotate(0,715.19,719.08) scale(1.44131,-1.44131) translate(-215.672,-1213.57)" width="15" x="704.3803554228138" xlink:href="#Accessory:PT6_0" y="704.6666661171369" zvalue="179"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449523679238" ObjectName="#1励磁变"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,715.19,719.08) scale(1.44131,-1.44131) translate(-215.672,-1213.57)" width="15" x="704.3803554228138" y="704.6666661171369"/></g>
  <g id="309">
   <use class="v10500" height="26" transform="rotate(0,561.971,730.625) scale(1.54647,-1.85577) translate(-195.304,-1113.2)" width="12" x="552.6923076923076" xlink:href="#Accessory:带电容电阻接地_0" y="706.5" zvalue="309"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449525710853" ObjectName="#1发电机设备1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,561.971,730.625) scale(1.54647,-1.85577) translate(-195.304,-1113.2)" width="12" x="552.6923076923076" y="706.5"/></g>
  <g id="311">
   <use class="v10500" height="20" transform="rotate(270,642.688,721.938) scale(1.90625,1.90625) translate(-296.477,-334.154)" width="20" x="623.625" xlink:href="#Accessory:线路PT3_0" y="702.875" zvalue="311"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449525776389" ObjectName="#1发电机设备2"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,642.688,721.938) scale(1.90625,1.90625) translate(-296.477,-334.154)" width="20" x="623.625" y="702.875"/></g>
  <g id="322">
   <use class="v10500" height="20" transform="rotate(0,562.971,849.5) scale(1.25,1.25) translate(-109.469,-167.4)" width="25" x="547.3461538461538" xlink:href="#Accessory:4绕组母线PT_0" y="837" zvalue="321"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449526038533" ObjectName="#1发电机母线PT1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,562.971,849.5) scale(1.25,1.25) translate(-109.469,-167.4)" width="25" x="547.3461538461538" y="837"/></g>
  <g id="329">
   <use class="v10500" height="20" transform="rotate(0,715.394,849.5) scale(1.25,1.25) translate(-139.954,-167.4)" width="25" x="699.7688802025813" xlink:href="#Accessory:4绕组母线PT_0" y="837" zvalue="329"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449526169605" ObjectName="#1发电机母线PT2"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,715.394,849.5) scale(1.25,1.25) translate(-139.954,-167.4)" width="25" x="699.7688802025813" y="837"/></g>
  <g id="358">
   <use class="v10500" height="20" transform="rotate(0,951.19,719.08) scale(1.44131,-1.44131) translate(-287.932,-1213.57)" width="15" x="940.3803554228138" xlink:href="#Accessory:PT6_0" y="704.6666661171369" zvalue="335"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449526759430" ObjectName="#2励磁变"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,951.19,719.08) scale(1.44131,-1.44131) translate(-287.932,-1213.57)" width="15" x="940.3803554228138" y="704.6666661171369"/></g>
  <g id="354">
   <use class="v10500" height="26" transform="rotate(0,797.971,730.625) scale(1.54647,-1.85577) translate(-278.699,-1113.2)" width="12" x="788.6923076923076" xlink:href="#Accessory:带电容电阻接地_0" y="706.5" zvalue="341"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449526693894" ObjectName="#2发电机设备1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,797.971,730.625) scale(1.54647,-1.85577) translate(-278.699,-1113.2)" width="12" x="788.6923076923076" y="706.5"/></g>
  <g id="353">
   <use class="v10500" height="20" transform="rotate(270,878.688,721.938) scale(1.90625,1.90625) translate(-408.674,-334.154)" width="20" x="859.625" xlink:href="#Accessory:线路PT3_0" y="702.875" zvalue="342"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449526628358" ObjectName="#2发电机设备2"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,878.688,721.938) scale(1.90625,1.90625) translate(-408.674,-334.154)" width="20" x="859.625" y="702.875"/></g>
  <g id="345">
   <use class="v10500" height="20" transform="rotate(0,798.971,849.5) scale(1.25,1.25) translate(-156.669,-167.4)" width="25" x="783.3461538461538" xlink:href="#Accessory:4绕组母线PT_0" y="837" zvalue="352"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449526366213" ObjectName="#2发电机母线PT1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,798.971,849.5) scale(1.25,1.25) translate(-156.669,-167.4)" width="25" x="783.3461538461538" y="837"/></g>
  <g id="341">
   <use class="v10500" height="20" transform="rotate(0,952.721,849.5) scale(1.25,1.25) translate(-187.419,-167.4)" width="25" x="937.0961538461538" xlink:href="#Accessory:4绕组母线PT_0" y="837" zvalue="357"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449526235141" ObjectName="#2发电机母线PT2"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,952.721,849.5) scale(1.25,1.25) translate(-187.419,-167.4)" width="25" x="937.0961538461538" y="837"/></g>
  <g id="385">
   <use class="v10500" height="20" transform="rotate(0,1571.19,719.08) scale(1.44131,-1.44131) translate(-477.767,-1213.57)" width="15" x="1560.380355422814" xlink:href="#Accessory:PT6_0" y="704.6666657129925" zvalue="363"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449527414790" ObjectName="#3励磁变"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1571.19,719.08) scale(1.44131,-1.44131) translate(-477.767,-1213.57)" width="15" x="1560.380355422814" y="704.6666657129925"/></g>
  <g id="381">
   <use class="v10500" height="26" transform="rotate(0,1417.97,730.625) scale(1.54647,-1.85577) translate(-497.787,-1113.2)" width="12" x="1408.692307692308" xlink:href="#Accessory:带电容电阻接地_0" y="706.5" zvalue="369"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449527349254" ObjectName="#3发电机设备1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1417.97,730.625) scale(1.54647,-1.85577) translate(-497.787,-1113.2)" width="12" x="1408.692307692308" y="706.5"/></g>
  <g id="380">
   <use class="v10500" height="20" transform="rotate(270,1498.69,729.938) scale(1.90625,1.90625) translate(-703.428,-337.957)" width="20" x="1479.625" xlink:href="#Accessory:线路PT3_0" y="710.875" zvalue="370"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449527283718" ObjectName="#3发电机设备2"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1498.69,729.938) scale(1.90625,1.90625) translate(-703.428,-337.957)" width="20" x="1479.625" y="710.875"/></g>
  <g id="372">
   <use class="v10500" height="20" transform="rotate(0,1418.97,849.5) scale(1.25,1.25) translate(-280.669,-167.4)" width="25" x="1403.346153846154" xlink:href="#Accessory:4绕组母线PT_0" y="837" zvalue="380"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449527021574" ObjectName="#3发电机母线PT1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1418.97,849.5) scale(1.25,1.25) translate(-280.669,-167.4)" width="25" x="1403.346153846154" y="837"/></g>
  <g id="368">
   <use class="v10500" height="20" transform="rotate(0,1572.72,849.5) scale(1.25,1.25) translate(-311.419,-167.4)" width="25" x="1557.096153846154" xlink:href="#Accessory:4绕组母线PT_0" y="837" zvalue="385"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449526890502" ObjectName="#3发电机母线PT2"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1572.72,849.5) scale(1.25,1.25) translate(-311.419,-167.4)" width="25" x="1557.096153846154" y="837"/></g>
  <g id="388">
   <use class="v10500" height="48" transform="rotate(0,1017.81,496.75) scale(1.04167,1.04167) translate(-39.775,-18.87)" width="45" x="994.375" xlink:href="#Accessory:母线电压互感器11_0" y="471.75" zvalue="389"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449527545862" ObjectName="10.5kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,1017.81,496.75) scale(1.04167,1.04167) translate(-39.775,-18.87)" width="45" x="994.375" y="471.75"/></g>
  <g id="399">
   <use class="v10500" height="48" transform="rotate(0,1733.81,504.75) scale(1.04167,1.04167) translate(-68.415,-19.19)" width="45" x="1710.375" xlink:href="#Accessory:母线电压互感器11_0" y="479.75" zvalue="395"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449527742470" ObjectName="10.5kVⅡ母电压互感器"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,1733.81,504.75) scale(1.04167,1.04167) translate(-68.415,-19.19)" width="45" x="1710.375" y="479.75"/></g>
  <g id="417">
   <use class="v10500" height="20" transform="rotate(180,1079.19,807.938) scale(-1.90625,-1.90625) translate(-1636.26,-1222.71)" width="20" x="1060.125" xlink:href="#Accessory:线路PT3_0" y="788.875" zvalue="417"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449527939077" ObjectName="#1站用变设备1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1079.19,807.938) scale(-1.90625,-1.90625) translate(-1636.26,-1222.71)" width="20" x="1060.125" y="788.875"/></g>
  <g id="429">
   <use class="v10500" height="20" transform="rotate(180,1679.19,815.938) scale(-1.90625,-1.90625) translate(-2551.01,-1234.91)" width="20" x="1660.125" xlink:href="#Accessory:线路PT3_0" y="796.875" zvalue="432"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449528266757" ObjectName="#2站用变设备1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1679.19,815.938) scale(-1.90625,-1.90625) translate(-2551.01,-1234.91)" width="20" x="1660.125" y="796.875"/></g>
  <g id="438">
   <use class="v10500" height="20" transform="rotate(180,576.688,513.688) scale(1.90625,1.90625) translate(-265.1,-235.15)" width="20" x="557.625" xlink:href="#Accessory:线路PT3_0" y="494.625" zvalue="442"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449528528901" ObjectName="备用3"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,576.688,513.688) scale(1.90625,1.90625) translate(-265.1,-235.15)" width="20" x="557.625" y="494.625"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="170">
   <g id="1700">
    <use class="kv110" height="50" transform="rotate(0,829.664,491.828) scale(1.97491,1.85355) translate(-394.937,-205.145)" width="30" x="800.04" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="445.49" zvalue="102"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874419470340" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1701">
    <use class="v10500" height="50" transform="rotate(0,829.664,491.828) scale(1.97491,1.85355) translate(-394.937,-205.145)" width="30" x="800.04" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="445.49" zvalue="102"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874419535876" ObjectName="10.5"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399441907716" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399441907716"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,829.664,491.828) scale(1.97491,1.85355) translate(-394.937,-205.145)" width="30" x="800.04" y="445.49"/></g>
  <g id="147">
   <g id="1470">
    <use class="kv110" height="50" transform="rotate(0,1423.23,494.653) scale(1.97491,1.85355) translate(-687.951,-206.446)" width="30" x="1393.61" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="448.31" zvalue="128"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874419339268" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1471">
    <use class="v10500" height="50" transform="rotate(0,1423.23,494.653) scale(1.97491,1.85355) translate(-687.951,-206.446)" width="30" x="1393.61" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="448.31" zvalue="128"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874419404804" ObjectName="10.5"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399441842180" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399441842180"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1423.23,494.653) scale(1.97491,1.85355) translate(-687.951,-206.446)" width="30" x="1393.61" y="448.31"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="414">
   <use class="v10500" height="30" transform="rotate(0,619.356,886.75) scale(1.42778,1.42778) translate(-179.149,-259.263)" width="30" x="597.938841095576" xlink:href="#Generator:发电机_0" y="865.333323160807" zvalue="168"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449523744774" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449523744774"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,619.356,886.75) scale(1.42778,1.42778) translate(-179.149,-259.263)" width="30" x="597.938841095576" y="865.333323160807"/></g>
  <g id="359">
   <use class="v10500" height="30" transform="rotate(0,855.356,886.75) scale(1.42778,1.42778) translate(-249.857,-259.263)" width="30" x="833.9388410955761" xlink:href="#Generator:发电机_0" y="865.333323160807" zvalue="333"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449526824966" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449526824966"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,855.356,886.75) scale(1.42778,1.42778) translate(-249.857,-259.263)" width="30" x="833.9388410955761" y="865.333323160807"/></g>
  <g id="386">
   <use class="v10500" height="30" transform="rotate(0,1475.36,886.75) scale(1.42778,1.42778) translate(-435.616,-259.263)" width="30" x="1453.938841095576" xlink:href="#Generator:发电机_0" y="865.333322842916" zvalue="361"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449527480326" ObjectName="#3发电机"/>
   <cge:TPSR_Ref TObjectID="6192449527480326"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1475.36,886.75) scale(1.42778,1.42778) translate(-435.616,-259.263)" width="30" x="1453.938841095576" y="865.333322842916"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="413">
   <use class="v10500" height="38" transform="rotate(0,1106.25,879.25) scale(1.25,1.25) translate(-218,-171.1)" width="26" x="1090" xlink:href="#EnergyConsumer:站用变1接点_0" y="855.5" zvalue="414"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449527873541" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,1106.25,879.25) scale(1.25,1.25) translate(-218,-171.1)" width="26" x="1090" y="855.5"/></g>
  <g id="431">
   <use class="v10500" height="38" transform="rotate(0,1702.25,887.25) scale(1.25,1.25) translate(-337.2,-172.7)" width="26" x="1686" xlink:href="#EnergyConsumer:站用变1接点_0" y="863.5" zvalue="429"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449528332293" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,1702.25,887.25) scale(1.25,1.25) translate(-337.2,-172.7)" width="26" x="1686" y="863.5"/></g>
  <g id="452">
   <use class="v10500" height="30" transform="rotate(0,588.106,463) scale(1.35417,0.625) translate(-151.687,272.175)" width="12" x="579.9805077622427" xlink:href="#EnergyConsumer:负荷_0" y="453.625" zvalue="456"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449528594437" ObjectName="备用"/>
   <cge:TPSR_Ref TObjectID="6192449528594437"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,588.106,463) scale(1.35417,0.625) translate(-151.687,272.175)" width="12" x="579.9805077622427" y="453.625"/></g>
  <g id="454">
   <use class="v10500" height="30" transform="rotate(0,618.286,474.25) scale(1.35417,0.625) translate(-159.58,278.925)" width="12" x="610.1605748823066" xlink:href="#EnergyConsumer:负荷_0" y="464.875" zvalue="458"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449528659973" ObjectName="备用4"/>
   <cge:TPSR_Ref TObjectID="6192449528659973"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,618.286,474.25) scale(1.35417,0.625) translate(-159.58,278.925)" width="12" x="610.1605748823066" y="464.875"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1024.54,55.0631) scale(1,1) translate(0,0)" writing-mode="lr" x="1024.07" xml:space="preserve" y="59.84" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123988373510" ObjectName="P"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="2" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1024.54,75.1881) scale(1,1) translate(0,0)" writing-mode="lr" x="1024.07" xml:space="preserve" y="79.97" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123988439044" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1024.54,95.3131) scale(1,1) translate(0,0)" writing-mode="lr" x="1024.07" xml:space="preserve" y="100.09" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123988504580" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="4" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1341.86,361.565) scale(1,1) translate(0,0)" writing-mode="lr" x="1341.3" xml:space="preserve" y="366.34" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123983261700" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="5" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,749.664,361.239) scale(1,1) translate(-1.5469e-13,0)" writing-mode="lr" x="749.11" xml:space="preserve" y="366.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123985817604" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="6" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1341.86,386.065) scale(1,1) translate(0,0)" writing-mode="lr" x="1341.3" xml:space="preserve" y="390.84" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123983327236" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="7" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,749.664,384.574) scale(1,1) translate(-1.5469e-13,0)" writing-mode="lr" x="749.11" xml:space="preserve" y="389.35" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123985883140" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="8" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1341.86,560.492) scale(1,1) translate(0,0)" writing-mode="lr" x="1341.3" xml:space="preserve" y="565.27" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123983392772" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="9" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,749.664,551.245) scale(1,1) translate(-1.5469e-13,0)" writing-mode="lr" x="749.11" xml:space="preserve" y="556.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123985948676" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="10" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1341.86,585.492) scale(1,1) translate(0,0)" writing-mode="lr" x="1341.3" xml:space="preserve" y="590.27" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123983458308" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="11" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,749.664,574.581) scale(1,1) translate(-1.5469e-13,0)" writing-mode="lr" x="749.11" xml:space="preserve" y="579.36" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123986014212" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="12" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1341.86,410.565) scale(1,1) translate(0,-1.74556e-13)" writing-mode="lr" x="1341.3" xml:space="preserve" y="415.34" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123983523844" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="13" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,749.664,407.91) scale(1,1) translate(-1.5469e-13,1.73377e-13)" writing-mode="lr" x="749.11" xml:space="preserve" y="412.69" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123986079748" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="14" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1341.86,610.492) scale(1,1) translate(0,0)" writing-mode="lr" x="1341.3" xml:space="preserve" y="615.27" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123983851524" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="15" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,749.664,597.916) scale(1,1) translate(-1.5469e-13,0)" writing-mode="lr" x="749.11" xml:space="preserve" y="602.6900000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123986407428" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="16" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,614.356,969.667) scale(1,1) translate(0,0)" writing-mode="lr" x="613.8" xml:space="preserve" y="973.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123982082052" ObjectName="P"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="17" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,855.356,966.667) scale(1,1) translate(8.90794e-14,0)" writing-mode="lr" x="854.8" xml:space="preserve" y="970.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123992043524" ObjectName="P"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="18" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1478.36,958.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1477.8" xml:space="preserve" y="962.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123993223172" ObjectName="P"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="19" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,615.356,992.667) scale(1,1) translate(0,0)" writing-mode="lr" x="614.8" xml:space="preserve" y="996.9400000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123982147588" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="20" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,857.356,989.667) scale(1,1) translate(8.93014e-14,0)" writing-mode="lr" x="856.8" xml:space="preserve" y="993.9400000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123992109060" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="21" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1480.36,983.802) scale(1,1) translate(0,1.07137e-12)" writing-mode="lr" x="1479.8" xml:space="preserve" y="988.1" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123993288708" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="22" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,615.356,1013.67) scale(1,1) translate(0,0)" writing-mode="lr" x="614.8" xml:space="preserve" y="1017.94" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123982213124" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="23" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,859.356,1013.67) scale(1,1) translate(8.95234e-14,0)" writing-mode="lr" x="858.8" xml:space="preserve" y="1017.94" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123992174596" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="24">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="24" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1483.36,1010.36) scale(1,1) translate(0,1.10086e-12)" writing-mode="lr" x="1482.8" xml:space="preserve" y="1014.66" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123993354244" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="117" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123990339588" ObjectName="F"/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="116" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123998859268" ObjectName="F"/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="115" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123998924804" ObjectName="F"/>
   </metadata>
  </g>
  <g id="114">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="114" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.222,335.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="340.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123983130628" ObjectName="F"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="39" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123998728196" ObjectName="F"/>
   </metadata>
  </g>
  <g id="110">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="110" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123998793732" ObjectName="F"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123999121412" ObjectName="F"/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="96" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="337.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123999252484" ObjectName="F"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127183122437" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="90">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127183056901" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="134">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="134" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.972,353.917) scale(1,1) translate(0,0)" writing-mode="lr" x="156.13" xml:space="preserve" y="358.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123990863876" ObjectName="F"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,399.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="404.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123999186948" ObjectName="F"/>
   </metadata>
  </g>
  <g id="25">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="25" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1030,365.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1029.53" xml:space="preserve" y="370.61" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123982737412" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="26">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="26" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1397.5,105.341) scale(1,1) translate(0,0)" writing-mode="lr" x="1397.03" xml:space="preserve" y="110.12" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123989946375" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="27">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="27" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1740.75,366.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1740.28" xml:space="preserve" y="371.61" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123990470660" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="76">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="76" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1030,394.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1029.53" xml:space="preserve" y="399.61" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123982802948" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="77">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="77" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1397.5,134.341) scale(1,1) translate(0,0)" writing-mode="lr" x="1397.03" xml:space="preserve" y="139.12" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123990011908" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="78" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1740.75,395.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1740.28" xml:space="preserve" y="400.61" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123990536196" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="87" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1030,423.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1029.53" xml:space="preserve" y="428.61" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123982868484" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="88" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1397.5,163.341) scale(1,1) translate(0,0)" writing-mode="lr" x="1397.03" xml:space="preserve" y="168.12" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123990077444" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="91" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1740.75,424.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1740.28" xml:space="preserve" y="429.61" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123990601732" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="93" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,481,653.333) scale(1,1) translate(0,0)" writing-mode="lr" x="480.53" xml:space="preserve" y="658.11" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123982999556" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="94" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,635.5,316.841) scale(1,1) translate(0,0)" writing-mode="lr" x="635.03" xml:space="preserve" y="321.62" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123990208519" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="95" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1351.75,657.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1351.28" xml:space="preserve" y="662.11" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123990732804" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="161">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="518"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374884274180" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="160">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="519"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950028525576" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
 </g>
</svg>