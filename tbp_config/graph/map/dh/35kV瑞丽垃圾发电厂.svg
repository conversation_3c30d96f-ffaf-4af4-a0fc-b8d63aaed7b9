<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549684797441" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_0" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="3" y1="19" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="4.75" y2="8.75"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="18.75" y2="21.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_1" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="2" y2="24"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_2" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.08333333333333" x2="3.995614035087721" y1="9.083333333333332" y2="17.19627192982456"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.717927631578948" x2="10.16666666666667" y1="9.172423245614038" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="2" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="21.25" y2="24.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.750000000000004" y2="4.166666666666668"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="32"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="1" y1="27" y2="11"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="27" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Accessory:三卷PT带容断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,6.5) scale(1,1) translate(0,0)" width="4" x="13" y="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
  </symbol>
  <symbol id="Accessory:消谐装置_0" viewBox="0,0,20,29">
   <use terminal-index="0" type="0" x="10.15872800538976" xlink:href="#terminal" y="25.96480127873437"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.12399698478855" x2="10.12399698478855" y1="26.28498263464221" y2="23.3231983778117"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.938434782209253" x2="14.28859387834882" y1="21.48754948791521" y2="14.19403516164853"/>
   <rect fill-opacity="0" height="5.22" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,10.11,17.58) scale(1,1) translate(0,0)" width="11.46" x="4.38" y="14.97"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.945791858126634" x2="5.945791858126634" y1="23.57968743439848" y2="21.49582619832228"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.28123680243143" x2="14.28123680243143" y1="14.18575845124145" y2="12.10189721516526"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.15025549608711" x2="10.15025549608711" y1="23.41666666666667" y2="4.867706583258689"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.135856301213458" x2="8.135856301213458" y1="4.455199311740065" y2="4.455199311740065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.659702116379664" x2="8.659702116379664" y1="29.26065666272731" y2="29.26065666272731"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.35591008128069" x2="7.891898490280618" y1="4.703700522631296" y2="4.703700522631296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.57292939360264" x2="8.848534280964973" y1="3.398988163859496" y2="3.398988163859496"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.05089117767898" x2="9.544227599895001" y1="1.833333333333339" y2="1.833333333333339"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.986779448160899" x2="8.986779448160899" y1="29.68994241064946" y2="29.68994241064946"/>
  </symbol>
  <symbol id="Accessory:1144_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="0" x="20.33086799123559" xlink:href="#terminal" y="58.519350916143"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.39999997711181" x2="18.69999995040893" y1="14.99999942779539" y2="18.03288345142636"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.79999991607665" x2="18.59999994659423" y1="15.73288336368831" y2="18.1999995498657"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.49999998092651" x2="24.30000016403199" y1="14.99999942779539" y2="14.99999942779539"/>
   <rect fill-opacity="0" height="15.6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,46.2) scale(1,1) translate(0,0)" width="7.2" x="16.4" y="38.4"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.20000000762939" x2="20.20000000762939" y1="58.80000109863285" y2="25.8999998435974"/>
   <rect fill-opacity="0" height="18.6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20.3,16.93) scale(1,-1) translate(0,-620.43)" width="8.4" x="16.1" y="7.63"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.59999990844726" x2="16.39999986267089" y1="15.59999945068357" y2="15.59999945068357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.21532033427961" x2="20.21532033427961" y1="7.632883054697825" y2="3.855023324985204"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="22.65106102994246" x2="17.51888182054474" y1="3.773533079259202" y2="3.773533079259202"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="21.65106099179548" x2="18.51888185869172" y1="2.273533022038748" y2="2.273533022038748"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.9510609650926" x2="19.2188818853946" y1="0.7735329648182869" y2="0.7735329648182869"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="EnergyConsumer:引风机_0" viewBox="0,0,20,35">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="2" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.821376755454764" x2="6.375541720075685" y1="6.645671339793678" y2="4.293314380606484"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="18.86589288145782" x2="0.9834162520729741" y1="2.214487300394504" y2="14.33624452162286"/>
   <rect fill-opacity="0" height="12.38" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,9.98,8.23) scale(1,1) translate(0,0)" width="17.87" x="1.04" y="2.04"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.02690758243771" x2="16.58107254705863" y1="12.66332867724928" y2="10.31097171806209"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.02493910770032" x2="10.02493910770032" y1="14.28585917272065" y2="16.5"/>
   <ellipse cx="9.94" cy="25.1" fill-opacity="0" rx="7.92" ry="8.529999999999999" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 6.89489 28.2392 L 8.72274 22.331 L 9.94131 28.2392 L 11.1599 22.331 L 12.9877 28.2939" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:水泵_0" viewBox="0,0,27,28">
   <use terminal-index="0" type="0" x="13.5" xlink:href="#terminal" y="0.5"/>
   <path d="M 8.41642 18.5331 L 11.4164 9.53309 L 13.4164 18.5331 L 15.4164 9.53309 L 18.4164 18.6164" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.42" cy="13.75" fill-opacity="0" rx="13" ry="13" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:20210316_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,18.75) scale(1,1) translate(0,0)" width="6" x="3" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="6.66666666666667" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="6.727157046327149" y2="14.02109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="6.734473004260392" y2="14.02109851866368"/>
  </symbol>
  <symbol id="Disconnector:20210316_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,18.75) scale(1,1) translate(0,0)" width="6" x="3" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="0.25" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="0.7271570463271502" y2="8.021098518663681"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="0.7344730042603906" y2="8.021098518663679"/>
  </symbol>
  <symbol id="Disconnector:20210316_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:PT象达_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15.5" xlink:href="#terminal" y="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="18.25" y1="12.08333333333333" y2="12.08333333333333"/>
   <ellipse cx="15.65" cy="12.68" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.9" cy="18.28" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="18.25" y1="18.33333333333333" y2="18.33333333333333"/>
  </symbol>
  <symbol id="Accessory:20210316PT_0" viewBox="0,0,15,26">
   <use terminal-index="0" type="0" x="7.45" xlink:href="#terminal" y="0.09999999999999964"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="8.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="6.833333333333331" y2="0"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="4.916666666666666" y1="11.25" y2="13.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="10.5" y1="11.25" y2="13.25"/>
   <ellipse cx="7.53" cy="11.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.5" cy="20.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333333" x2="7.583333333333333" y1="17.5" y2="20.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333332" x2="4.999999999999998" y1="20.5" y2="22.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="10.58333333333334" y1="20.5" y2="22.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="ACLineSegment:线路带壁雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="14.875" xlink:href="#terminal" y="39.83880854456296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.25" x2="6.25" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="5.75" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.25" x2="7.25" y1="32.25" y2="32.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="8.25" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="6.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="4.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.91022336769755" x2="14.91022336769755" y1="39.75" y2="0.8333333333333321"/>
   <path d="M 14.75 9.25 L 5.75 9.25 L 5.75 21.25 L 5.75 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.79,21.17) scale(1,1) translate(0,0)" width="6.08" x="2.75" y="14"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV瑞丽垃圾发电厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="277.57" x="31.43" xlink:href="logo.png" y="35"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,170.214,65) scale(1,1) translate(-6.97854e-15,0)" writing-mode="lr" x="170.21" xml:space="preserve" y="68.5" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,168.214,65.6903) scale(1,1) translate(-7.20059e-15,0)" writing-mode="lr" x="168.21" xml:space="preserve" y="74.69" zvalue="3">35kV瑞丽垃圾发电厂</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="111" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,66.625,387.75) scale(1,1) translate(0,0)" width="72.88" x="30.19" y="375.75" zvalue="486"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,66.625,387.75) scale(1,1) translate(0,0)" writing-mode="lr" x="66.63" xml:space="preserve" y="392.25" zvalue="486">信号一览</text>
  <line fill="none" id="36" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.4285714285713" x2="377.4285714285713" y1="3" y2="1033" zvalue="4"/>
  <line fill="none" id="34" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.428571428572013" x2="370.4285714285716" y1="138.8704926140825" y2="138.8704926140825" zvalue="6"/>
  <line fill="none" id="32" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.428571428572013" x2="370.4285714285716" y1="608.8704926140824" y2="608.8704926140824" zvalue="8"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.6509331659181" x2="104.9956331659182" y1="431.6666435058594" y2="431.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.6509331659181" x2="104.9956331659182" y1="469.1566435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.6509331659181" x2="56.6509331659181" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9956331659182" x2="104.9956331659182" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9959331659181" x2="167.1042331659182" y1="431.6666435058594" y2="431.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9959331659181" x2="167.1042331659182" y1="469.1566435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9959331659181" x2="104.9959331659181" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1042331659182" x2="167.1042331659182" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1037331659181" x2="230.4285331659182" y1="431.6666435058594" y2="431.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1037331659181" x2="230.4285331659182" y1="469.1566435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1037331659181" x2="167.1037331659181" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4285331659182" x2="230.4285331659182" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4284331659181" x2="292.5367331659181" y1="431.6666435058594" y2="431.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4284331659181" x2="292.5367331659181" y1="469.1566435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4284331659181" x2="230.4284331659181" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="292.5367331659181" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="354.6450331659182" y1="431.6666435058594" y2="431.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="354.6450331659182" y1="469.1566435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="292.5367331659181" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.6450331659182" x2="354.6450331659182" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.6509331659181" x2="104.9956331659182" y1="469.1567435058594" y2="469.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.6509331659181" x2="104.9956331659182" y1="493.3253435058595" y2="493.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.6509331659181" x2="56.6509331659181" y1="469.1567435058594" y2="493.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9956331659182" x2="104.9956331659182" y1="469.1567435058594" y2="493.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9959331659181" x2="167.1042331659182" y1="469.1567435058594" y2="469.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9959331659181" x2="167.1042331659182" y1="493.3253435058595" y2="493.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9959331659181" x2="104.9959331659181" y1="469.1567435058594" y2="493.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1042331659182" x2="167.1042331659182" y1="469.1567435058594" y2="493.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1037331659181" x2="230.4285331659182" y1="469.1567435058594" y2="469.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1037331659181" x2="230.4285331659182" y1="493.3253435058595" y2="493.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1037331659181" x2="167.1037331659181" y1="469.1567435058594" y2="493.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4285331659182" x2="230.4285331659182" y1="469.1567435058594" y2="493.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4284331659181" x2="292.5367331659181" y1="469.1567435058594" y2="469.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4284331659181" x2="292.5367331659181" y1="493.3253435058595" y2="493.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4284331659181" x2="230.4284331659181" y1="469.1567435058594" y2="493.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="292.5367331659181" y1="469.1567435058594" y2="493.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="354.6450331659182" y1="469.1567435058594" y2="469.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="354.6450331659182" y1="493.3253435058595" y2="493.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="292.5367331659181" y1="469.1567435058594" y2="493.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.6450331659182" x2="354.6450331659182" y1="469.1567435058594" y2="493.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.6509331659181" x2="104.9956331659182" y1="493.3253435058595" y2="493.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.6509331659181" x2="104.9956331659182" y1="517.4939435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.6509331659181" x2="56.6509331659181" y1="493.3253435058595" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9956331659182" x2="104.9956331659182" y1="493.3253435058595" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9959331659181" x2="167.1042331659182" y1="493.3253435058595" y2="493.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9959331659181" x2="167.1042331659182" y1="517.4939435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9959331659181" x2="104.9959331659181" y1="493.3253435058595" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1042331659182" x2="167.1042331659182" y1="493.3253435058595" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1037331659181" x2="230.4285331659182" y1="493.3253435058595" y2="493.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1037331659181" x2="230.4285331659182" y1="517.4939435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1037331659181" x2="167.1037331659181" y1="493.3253435058595" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4285331659182" x2="230.4285331659182" y1="493.3253435058595" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4284331659181" x2="292.5367331659181" y1="493.3253435058595" y2="493.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4284331659181" x2="292.5367331659181" y1="517.4939435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4284331659181" x2="230.4284331659181" y1="493.3253435058595" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="292.5367331659181" y1="493.3253435058595" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="354.6450331659182" y1="493.3253435058595" y2="493.3253435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="354.6450331659182" y1="517.4939435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="292.5367331659181" y1="493.3253435058595" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.6450331659182" x2="354.6450331659182" y1="493.3253435058595" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.6509331659181" x2="104.9956331659182" y1="517.4939835058594" y2="517.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.6509331659181" x2="104.9956331659182" y1="541.6625835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.6509331659181" x2="56.6509331659181" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9956331659182" x2="104.9956331659182" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9959331659181" x2="167.1042331659182" y1="517.4939835058594" y2="517.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9959331659181" x2="167.1042331659182" y1="541.6625835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9959331659181" x2="104.9959331659181" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1042331659182" x2="167.1042331659182" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1037331659181" x2="230.4285331659182" y1="517.4939835058594" y2="517.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1037331659181" x2="230.4285331659182" y1="541.6625835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1037331659181" x2="167.1037331659181" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4285331659182" x2="230.4285331659182" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4284331659181" x2="292.5367331659181" y1="517.4939835058594" y2="517.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4284331659181" x2="292.5367331659181" y1="541.6625835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4284331659181" x2="230.4284331659181" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="292.5367331659181" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="354.6450331659182" y1="517.4939835058594" y2="517.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="354.6450331659182" y1="541.6625835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="292.5367331659181" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.6450331659182" x2="354.6450331659182" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.6509331659181" x2="104.9956331659182" y1="541.6627435058595" y2="541.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.6509331659181" x2="104.9956331659182" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.6509331659181" x2="56.6509331659181" y1="541.6627435058595" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9956331659182" x2="104.9956331659182" y1="541.6627435058595" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9959331659181" x2="167.1042331659182" y1="541.6627435058595" y2="541.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9959331659181" x2="167.1042331659182" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9959331659181" x2="104.9959331659181" y1="541.6627435058595" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1042331659182" x2="167.1042331659182" y1="541.6627435058595" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1037331659181" x2="230.4285331659182" y1="541.6627435058595" y2="541.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1037331659181" x2="230.4285331659182" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1037331659181" x2="167.1037331659181" y1="541.6627435058595" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4285331659182" x2="230.4285331659182" y1="541.6627435058595" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4284331659181" x2="292.5367331659181" y1="541.6627435058595" y2="541.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4284331659181" x2="292.5367331659181" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4284331659181" x2="230.4284331659181" y1="541.6627435058595" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="292.5367331659181" y1="541.6627435058595" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="354.6450331659182" y1="541.6627435058595" y2="541.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="354.6450331659182" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="292.5367331659181" y1="541.6627435058595" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.6450331659182" x2="354.6450331659182" y1="541.6627435058595" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.6509331659181" x2="104.9956331659182" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.6509331659181" x2="104.9956331659182" y1="589.9999435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.6509331659181" x2="56.6509331659181" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9956331659182" x2="104.9956331659182" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9959331659181" x2="167.1042331659182" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9959331659181" x2="167.1042331659182" y1="589.9999435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="104.9959331659181" x2="104.9959331659181" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1042331659182" x2="167.1042331659182" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1037331659181" x2="230.4285331659182" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1037331659181" x2="230.4285331659182" y1="589.9999435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1037331659181" x2="167.1037331659181" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4285331659182" x2="230.4285331659182" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4284331659181" x2="292.5367331659181" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4284331659181" x2="292.5367331659181" y1="589.9999435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4284331659181" x2="230.4284331659181" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="292.5367331659181" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="354.6450331659182" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="354.6450331659182" y1="589.9999435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.5367331659181" x2="292.5367331659181" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.6450331659182" x2="354.6450331659182" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.428571428571331" x2="93.42857142857133" y1="924" y2="924"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.428571428571331" x2="93.42857142857133" y1="963.1632999999999" y2="963.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.428571428571331" x2="3.428571428571331" y1="924" y2="963.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.42857142857133" x2="93.42857142857133" y1="924" y2="963.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.42857142857133" x2="363.4285714285713" y1="924" y2="924"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.42857142857133" x2="363.4285714285713" y1="963.1632999999999" y2="963.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.42857142857133" x2="93.42857142857133" y1="924" y2="963.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.4285714285713" x2="363.4285714285713" y1="924" y2="963.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.428571428571331" x2="93.42857142857133" y1="963.16327" y2="963.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.428571428571331" x2="93.42857142857133" y1="991.08167" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.428571428571331" x2="3.428571428571331" y1="963.16327" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.42857142857133" x2="93.42857142857133" y1="963.16327" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.42857142857133" x2="183.4285714285713" y1="963.16327" y2="963.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.42857142857133" x2="183.4285714285713" y1="991.08167" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.42857142857133" x2="93.42857142857133" y1="963.16327" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.4285714285713" x2="183.4285714285713" y1="963.16327" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.4285714285714" x2="273.4285714285714" y1="963.16327" y2="963.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.4285714285714" x2="273.4285714285714" y1="991.08167" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.4285714285714" x2="183.4285714285714" y1="963.16327" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.4285714285714" x2="273.4285714285714" y1="963.16327" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.4285714285713" x2="363.4285714285713" y1="963.16327" y2="963.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.4285714285713" x2="363.4285714285713" y1="991.08167" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.4285714285713" x2="273.4285714285713" y1="963.16327" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.4285714285713" x2="363.4285714285713" y1="963.16327" y2="991.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.428571428571331" x2="93.42857142857133" y1="991.0816" y2="991.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.428571428571331" x2="93.42857142857133" y1="1019" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.428571428571331" x2="3.428571428571331" y1="991.0816" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.42857142857133" x2="93.42857142857133" y1="991.0816" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.42857142857133" x2="183.4285714285713" y1="991.0816" y2="991.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.42857142857133" x2="183.4285714285713" y1="1019" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.42857142857133" x2="93.42857142857133" y1="991.0816" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.4285714285713" x2="183.4285714285713" y1="991.0816" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.4285714285714" x2="273.4285714285714" y1="991.0816" y2="991.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.4285714285714" x2="273.4285714285714" y1="1019" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.4285714285714" x2="183.4285714285714" y1="991.0816" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.4285714285714" x2="273.4285714285714" y1="991.0816" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.4285714285713" x2="363.4285714285713" y1="991.0816" y2="991.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.4285714285713" x2="363.4285714285713" y1="1019" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.4285714285713" x2="273.4285714285713" y1="991.0816" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.4285714285713" x2="363.4285714285713" y1="991.0816" y2="1019"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.4286,944) scale(1,1) translate(0,0)" writing-mode="lr" x="48.43" xml:space="preserve" y="950" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.4286,978) scale(1,1) translate(0,0)" writing-mode="lr" x="45.43" xml:space="preserve" y="984" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.429,978) scale(1,1) translate(0,0)" writing-mode="lr" x="227.43" xml:space="preserve" y="984" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.4286,1006) scale(1,1) translate(0,0)" writing-mode="lr" x="44.43" xml:space="preserve" y="1012" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.429,1006) scale(1,1) translate(0,0)" writing-mode="lr" x="226.43" xml:space="preserve" y="1012" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,68.9286,638.5) scale(1,1) translate(0,0)" writing-mode="lr" x="68.92857142857133" xml:space="preserve" y="643" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,161.827,387.75) scale(1,1) translate(0,0)" writing-mode="lr" x="161.83" xml:space="preserve" y="392.25" zvalue="20">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,266.827,387.75) scale(1,1) translate(0,0)" writing-mode="lr" x="266.83" xml:space="preserve" y="392.25" zvalue="21">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" x="194.609375" xml:space="preserve" y="447.265625" zvalue="22">10kVⅠ段母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="194.609375" xml:space="preserve" y="463.265625" zvalue="22">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" x="256.015625" xml:space="preserve" y="448.515625" zvalue="23">10kVⅡ段母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="256.015625" xml:space="preserve" y="464.515625" zvalue="23">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.4286,481.5) scale(1,1) translate(0,0)" writing-mode="lr" x="80.42857142857133" xml:space="preserve" y="486.0000000000001" zvalue="25">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.4286,507) scale(1,1) translate(0,0)" writing-mode="lr" x="80.42857142857133" xml:space="preserve" y="511.5" zvalue="26">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.4286,532.5) scale(1,1) translate(0,0)" writing-mode="lr" x="80.42857142857133" xml:space="preserve" y="537" zvalue="27">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.4286,557) scale(1,1) translate(0,0)" writing-mode="lr" x="79.42857142857133" xml:space="preserve" y="561.5" zvalue="28">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.4286,583.5) scale(1,1) translate(0,0)" writing-mode="lr" x="80.42857142857133" xml:space="preserve" y="588" zvalue="29">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.483,946) scale(1,1) translate(0,0)" writing-mode="lr" x="228.48" xml:space="preserve" y="952" zvalue="30">RLLaJi-01-2020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,469.633,577.877) scale(1,1) translate(0,0)" writing-mode="lr" x="469.63" xml:space="preserve" y="582.38" zvalue="42">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,533.532,657.254) scale(1,1) translate(0,0)" writing-mode="lr" x="533.53" xml:space="preserve" y="661.75" zvalue="44">071</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" x="504.5859375" xml:space="preserve" y="890.4078404792378" zvalue="47">#1汽轮发电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="504.5859375" xml:space="preserve" y="906.4078404792378" zvalue="47">机15MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1709.5,583.46) scale(1,1) translate(0,1.90834e-13)" writing-mode="lr" x="1709.5" xml:space="preserve" y="587.96" zvalue="54">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,877.03,652.683) scale(1,1) translate(0,-5.72149e-13)" writing-mode="lr" x="877.03" xml:space="preserve" y="657.1799999999999" zvalue="60">073</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,836,722.321) scale(1,1) translate(0,0)" writing-mode="lr" x="836" xml:space="preserve" y="726.8200000000001" zvalue="71">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,596.106,786.822) scale(1,1) translate(0,0)" writing-mode="lr" x="596.11" xml:space="preserve" y="791.3200000000001" zvalue="106">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,491.641,786.822) scale(1,1) translate(0,0)" writing-mode="lr" x="491.64" xml:space="preserve" y="791.3200000000001" zvalue="114">0912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,442.852,786.822) scale(1,1) translate(0,0)" writing-mode="lr" x="442.85" xml:space="preserve" y="791.3200000000001" zvalue="120">0913</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,687.177,653.133) scale(1,1) translate(0,5.72549e-13)" writing-mode="lr" x="687.1799999999999" xml:space="preserve" y="657.63" zvalue="131">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="303" stroke="rgb(255,255,255)" text-anchor="middle" x="658.5390625" xml:space="preserve" y="808.125" zvalue="134">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="303" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="658.5390625" xml:space="preserve" y="824.125" zvalue="134">电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="304" stroke="rgb(255,255,255)" text-anchor="middle" x="747.09375" xml:space="preserve" y="797.25" zvalue="139">10kV#1全电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="304" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="747.09375" xml:space="preserve" y="813.25" zvalue="139">检测柜</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,782.855,654.133) scale(1,1) translate(0,5.73437e-13)" writing-mode="lr" x="782.85" xml:space="preserve" y="658.63" zvalue="141">0721</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,983.03,653.683) scale(1,1) translate(-2.15501e-13,-5.73037e-13)" writing-mode="lr" x="983.03" xml:space="preserve" y="658.1799999999999" zvalue="162">074</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,938.75,721.821) scale(1,1) translate(0,0)" writing-mode="lr" x="938.75" xml:space="preserve" y="726.3200000000001" zvalue="167">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1082.03,653.683) scale(1,1) translate(0,-5.73037e-13)" writing-mode="lr" x="1082.03" xml:space="preserve" y="658.1799999999999" zvalue="186">075</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1037.75,721.821) scale(1,1) translate(0,0)" writing-mode="lr" x="1037.75" xml:space="preserve" y="726.3200000000001" zvalue="191">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1183.03,652.683) scale(1,1) translate(0,-5.72149e-13)" writing-mode="lr" x="1183.03" xml:space="preserve" y="657.1799999999999" zvalue="200">076</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="166" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1138.75,720.071) scale(1,1) translate(0,0)" writing-mode="lr" x="1138.75" xml:space="preserve" y="724.5700000000001" zvalue="205">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="310" stroke="rgb(255,255,255)" text-anchor="middle" x="1158.3046875" xml:space="preserve" y="841.078125" zvalue="227">10kV#0厂用</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="310" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1158.3046875" xml:space="preserve" y="857.078125" zvalue="227">备用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1279.03,652.683) scale(1,1) translate(0,-5.72149e-13)" writing-mode="lr" x="1279.03" xml:space="preserve" y="657.1799999999999" zvalue="229">077</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1235,720.071) scale(1,1) translate(0,0)" writing-mode="lr" x="1235" xml:space="preserve" y="724.5700000000001" zvalue="234">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="311" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1266.14,842.312) scale(1,1) translate(0,0)" writing-mode="lr" x="1266.139784792642" xml:space="preserve" y="846.8125" zvalue="241">10kV#1厂用工作变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="214" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1430,647.571) scale(1,1) translate(0,5.67609e-13)" writing-mode="lr" x="1430" xml:space="preserve" y="652.0700000000001" zvalue="248">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1364.53,652.683) scale(1,1) translate(0,-5.72149e-13)" writing-mode="lr" x="1364.53" xml:space="preserve" y="657.1799999999999" zvalue="253">012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="329" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1687.71,651.281) scale(1,1) translate(0,5.70904e-13)" writing-mode="lr" x="1687.71" xml:space="preserve" y="655.78" zvalue="277">0902</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="328" stroke="rgb(255,255,255)" text-anchor="middle" x="1648.9296875" xml:space="preserve" y="797.3652659306125" zvalue="279">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="328" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1648.9296875" xml:space="preserve" y="813.3652659306125" zvalue="279">电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="241" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1549,722.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1549" xml:space="preserve" y="727.0700000000001" zvalue="290">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="330" stroke="rgb(255,255,255)" text-anchor="middle" x="1744.8984375" xml:space="preserve" y="796.8194444444445" zvalue="302">10kV#2全</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="330" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1744.8984375" xml:space="preserve" y="812.8194444444445" zvalue="302">电压检测柜</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="331" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1774.33,652.281) scale(1,1) translate(0,5.71793e-13)" writing-mode="lr" x="1774.33" xml:space="preserve" y="656.78" zvalue="303">0812</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="333" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1304.08,548.209) scale(1,1) translate(-2.8666e-13,0)" writing-mode="lr" x="1304.08" xml:space="preserve" y="552.71" zvalue="320">001</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="335" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1360.99,386.578) scale(1,1) translate(0,0)" writing-mode="lr" x="1360.99" xml:space="preserve" y="391.08" zvalue="326">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="337" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1298.58,275.183) scale(1,1) translate(0,0)" writing-mode="lr" x="1298.58" xml:space="preserve" y="279.68" zvalue="332">371</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="338" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1293.33,203.281) scale(1,1) translate(0,0)" writing-mode="lr" x="1293.33" xml:space="preserve" y="207.78" zvalue="337">8</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1166,122.035) scale(1,1) translate(0,0)" writing-mode="lr" x="1166" xml:space="preserve" y="126.54" zvalue="338">35kV丽宏线垃圾发电厂T线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="339" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1224.58,188.056) scale(1,1) translate(0,0)" writing-mode="lr" x="1224.58" xml:space="preserve" y="192.56" zvalue="340">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,853.82,838.964) scale(1,1) translate(8.9797e-14,-1.83171e-13)" writing-mode="lr" x="853.8199017784839" xml:space="preserve" y="843.4638157894738" zvalue="352">10kV#1引风机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="307" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,956.641,851.465) scale(1,1) translate(0,0)" writing-mode="lr" x="956.640625" xml:space="preserve" y="855.96484375" zvalue="354">10kV#1循环水泵</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="309" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1061.12,851.812) scale(1,1) translate(0,0)" writing-mode="lr" x="1061.13" xml:space="preserve" y="856.3099999999999" zvalue="356">10kV#2循环水泵</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="313" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1489.28,652.683) scale(1,1) translate(0,-5.72149e-13)" writing-mode="lr" x="1489.28" xml:space="preserve" y="657.1799999999999" zvalue="358">078</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="326" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1445.12,719.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1445.13" xml:space="preserve" y="724.0700000000001" zvalue="363">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="312" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1465.19,842.562) scale(1,1) translate(6.24254e-13,0)" writing-mode="lr" x="1465.194892396321" xml:space="preserve" y="847.0625" zvalue="369">10kV#2厂用工作变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="336" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1360.49,412.078) scale(1,1) translate(0,0)" writing-mode="lr" x="1360.49" xml:space="preserve" y="416.58" zvalue="372">（SF11-17000/35）</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1211,264) scale(1,1) translate(0,0)" writing-mode="lr" x="1211" xml:space="preserve" y="268.5" zvalue="388">9</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1566,785.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1566" xml:space="preserve" y="790" zvalue="407">备用</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1824,783.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1824" xml:space="preserve" y="788" zvalue="409">备用电源</text>
  <text fill="rgb(255,0,0)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1421.5,144) scale(1,1) translate(0,0)" writing-mode="lr" x="1421.5" xml:space="preserve" y="148.5" zvalue="411">注：3716为外墙刀闸，无法采集信号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="232" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,824,150.5) scale(1,1) translate(0,0)" writing-mode="lr" x="824" xml:space="preserve" y="155" zvalue="524">容量：15MW</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="155" y2="155"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="181" y2="181"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="155" y2="181"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="155" y2="181"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="155" y2="155"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="181" y2="181"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="155" y2="181"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="155" y2="181"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="181" y2="181"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="205.25" y2="205.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="181" y2="205.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="181" y2="205.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="181" y2="181"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="205.25" y2="205.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="181" y2="205.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="181" y2="205.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="205.25" y2="205.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="228" y2="228"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="205.25" y2="228"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="205.25" y2="228"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="205.25" y2="205.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="228" y2="228"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="205.25" y2="228"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="205.25" y2="228"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="228" y2="228"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="250.75" y2="250.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="228" y2="250.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="228" y2="250.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="228" y2="228"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="250.75" y2="250.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="228" y2="250.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="228" y2="250.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="250.75" y2="250.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="273.5" y2="273.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="250.75" y2="273.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="250.75" y2="273.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="250.75" y2="250.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="273.5" y2="273.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="250.75" y2="273.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="250.75" y2="273.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="273.5" y2="273.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="296.25" y2="296.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="273.5" y2="296.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="273.5" y2="296.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="273.5" y2="273.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="296.25" y2="296.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="273.5" y2="296.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="273.5" y2="296.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="296.25" y2="296.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="187" y1="319" y2="319"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6" x2="6" y1="296.25" y2="319"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="296.25" y2="319"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="296.25" y2="296.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="368" y1="319" y2="319"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187" x2="187" y1="296.25" y2="319"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368" x2="368" y1="296.25" y2="319"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="319" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,52.5,168) scale(1,1) translate(0,0)" writing-mode="lr" x="10" xml:space="preserve" y="172.5" zvalue="527">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="308" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,233,168) scale(1,1) translate(0,0)" writing-mode="lr" x="190.5" xml:space="preserve" y="172.5" zvalue="528">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="298" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6875,241.25) scale(1,1) translate(0,0)" writing-mode="lr" x="55.69" xml:space="preserve" y="245.75" zvalue="529">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="293" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.625,240) scale(1,1) translate(0,0)" writing-mode="lr" x="234.63" xml:space="preserve" y="244.5" zvalue="530">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="268" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,51.5,194) scale(1,1) translate(0,0)" writing-mode="lr" x="9" xml:space="preserve" y="198.5" zvalue="535">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="266" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,232,194) scale(1,1) translate(0,0)" writing-mode="lr" x="189.5" xml:space="preserve" y="198.5" zvalue="536">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="256" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.6875,287.25) scale(1,1) translate(0,0)" writing-mode="lr" x="52.69" xml:space="preserve" y="291.75" zvalue="539">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,220.688,286.25) scale(1,1) translate(0,0)" writing-mode="lr" x="220.69" xml:space="preserve" y="290.75" zvalue="541">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="249" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.6875,310.25) scale(1,1) translate(0,0)" writing-mode="lr" x="52.69" xml:space="preserve" y="314.75" zvalue="542">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="248" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,220.688,309.25) scale(1,1) translate(0,0)" writing-mode="lr" x="220.69" xml:space="preserve" y="313.75" zvalue="543">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="243" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,51.5,217) scale(1,1) translate(0,0)" writing-mode="lr" x="9" xml:space="preserve" y="221.5" zvalue="544">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="242" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,231.5,216) scale(1,1) translate(0,0)" writing-mode="lr" x="189" xml:space="preserve" y="220.5" zvalue="546">厂用电率</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="30.19" y="375.75" zvalue="486"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="85">
   <path class="kv10" d="M 439 602.21 L 1361 602.21" stroke-width="4" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674422685699" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674422685699"/></metadata>
  <path d="M 439 602.21 L 1361 602.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="315">
   <path class="kv10" d="M 1396 605.46 L 1849 605.46" stroke-width="4" zvalue="53"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674422620163" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674422620163"/></metadata>
  <path d="M 1396 605.46 L 1849 605.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="341">
   <use class="kv10" height="20" transform="rotate(0,507.364,655.947) scale(2.00527,2.00527) translate(-249.323,-318.783)" width="10" x="497.3377935958412" xlink:href="#Breaker:小车断路器_0" y="635.8945658309567" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925221089283" ObjectName="10kV#1汽轮发电机071断路器"/>
   <cge:TPSR_Ref TObjectID="6473925221089283"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,507.364,655.947) scale(2.00527,2.00527) translate(-249.323,-318.783)" width="10" x="497.3377935958412" y="635.8945658309567"/></g>
  <g id="88">
   <use class="kv10" height="20" transform="rotate(0,852.03,653.683) scale(2,2) translate(-421.015,-316.841)" width="10" x="842.0295695852833" xlink:href="#Breaker:小车断路器_0" y="633.6825413779607" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925221023747" ObjectName="10kV#1引风机073断路器"/>
   <cge:TPSR_Ref TObjectID="6473925221023747"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,852.03,653.683) scale(2,2) translate(-421.015,-316.841)" width="10" x="842.0295695852833" y="633.6825413779607"/></g>
  <g id="147">
   <use class="kv10" height="20" transform="rotate(0,958.03,654.683) scale(2,2) translate(-474.015,-317.341)" width="10" x="948.0295695852833" xlink:href="#Breaker:小车断路器_0" y="634.6825413779607" zvalue="161"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925221154819" ObjectName="10kV#1循环水泵074断路器"/>
   <cge:TPSR_Ref TObjectID="6473925221154819"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,958.03,654.683) scale(2,2) translate(-474.015,-317.341)" width="10" x="948.0295695852833" y="634.6825413779607"/></g>
  <g id="165">
   <use class="kv10" height="20" transform="rotate(0,1057.03,654.683) scale(2,2) translate(-523.515,-317.341)" width="10" x="1047.029569585283" xlink:href="#Breaker:小车断路器_0" y="634.6825413779607" zvalue="185"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925221220355" ObjectName="10kV#2循环水泵075断路器"/>
   <cge:TPSR_Ref TObjectID="6473925221220355"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1057.03,654.683) scale(2,2) translate(-523.515,-317.341)" width="10" x="1047.029569585283" y="634.6825413779607"/></g>
  <g id="178">
   <use class="kv10" height="20" transform="rotate(0,1158.03,653.683) scale(2,2) translate(-574.015,-316.841)" width="10" x="1148.029569585283" xlink:href="#Breaker:小车断路器_0" y="633.6825413779607" zvalue="199"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925221285891" ObjectName="10kV#0厂用备用变076断路器"/>
   <cge:TPSR_Ref TObjectID="6473925221285891"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1158.03,653.683) scale(2,2) translate(-574.015,-316.841)" width="10" x="1148.029569585283" y="633.6825413779607"/></g>
  <g id="205">
   <use class="kv10" height="20" transform="rotate(0,1254.03,653.683) scale(2,2) translate(-622.015,-316.841)" width="10" x="1244.029569585283" xlink:href="#Breaker:小车断路器_0" y="633.6825413779607" zvalue="228"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925221351427" ObjectName="10kV#1厂用工作变077断路器"/>
   <cge:TPSR_Ref TObjectID="6473925221351427"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1254.03,653.683) scale(2,2) translate(-622.015,-316.841)" width="10" x="1244.029569585283" y="633.6825413779607"/></g>
  <g id="215">
   <use class="kv10" height="20" transform="rotate(0,1342.03,653.683) scale(2,2) translate(-666.015,-316.841)" width="10" x="1332.029569585283" xlink:href="#Breaker:小车断路器_0" y="633.6825413779607" zvalue="252"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925221416963" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473925221416963"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1342.03,653.683) scale(2,2) translate(-666.015,-316.841)" width="10" x="1332.029569585283" y="633.6825413779607"/></g>
  <g id="254">
   <use class="kv10" height="20" transform="rotate(0,1567.03,655.683) scale(2,2) translate(-778.515,-317.841)" width="10" x="1557.029569585283" xlink:href="#Breaker:小车断路器_0" y="635.6825413779607" zvalue="284"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925221482499" ObjectName="10kV备用079断路器"/>
   <cge:TPSR_Ref TObjectID="6473925221482499"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1567.03,655.683) scale(2,2) translate(-778.515,-317.841)" width="10" x="1557.029569585283" y="635.6825413779607"/></g>
  <g id="274">
   <use class="kv10" height="20" transform="rotate(0,1827.03,657.683) scale(2,2) translate(-908.515,-318.841)" width="10" x="1817.029569585283" xlink:href="#Breaker:小车断路器_0" y="637.6825413779607" zvalue="309"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925221548035" ObjectName="10kV备用电源线080断路器"/>
   <cge:TPSR_Ref TObjectID="6473925221548035"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1827.03,657.683) scale(2,2) translate(-908.515,-318.841)" width="10" x="1817.029569585283" y="637.6825413779607"/></g>
  <g id="275">
   <use class="kv10" height="20" transform="rotate(0,1273.79,544.209) scale(2,2) translate(-631.896,-262.104)" width="10" x="1263.792109383835" xlink:href="#Breaker:小车断路器_0" y="524.2088571674343" zvalue="319"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925221613571" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925221613571"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1273.79,544.209) scale(2,2) translate(-631.896,-262.104)" width="10" x="1263.792109383835" y="524.2088571674343"/></g>
  <g id="282">
   <use class="kv35" height="20" transform="rotate(0,1272.83,273.683) scale(2,2) translate(-631.415,-126.841)" width="10" x="1262.829803466797" xlink:href="#Breaker:小车断路器_0" y="253.6825413779606" zvalue="331"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925221679107" ObjectName="35kV丽宏线垃圾发电厂T线371断路器"/>
   <cge:TPSR_Ref TObjectID="6473925221679107"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1272.83,273.683) scale(2,2) translate(-631.415,-126.841)" width="10" x="1262.829803466797" y="253.6825413779606"/></g>
  <g id="325">
   <use class="kv10" height="20" transform="rotate(0,1464.28,653.683) scale(2,2) translate(-727.14,-316.841)" width="10" x="1454.279569585283" xlink:href="#Breaker:小车断路器_0" y="633.6825413779607" zvalue="357"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925221744643" ObjectName="10kV#2厂用工作变078断路器"/>
   <cge:TPSR_Ref TObjectID="6473925221744643"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1464.28,653.683) scale(2,2) translate(-727.14,-316.841)" width="10" x="1454.279569585283" y="633.6825413779607"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="84">
   <path class="kv10" d="M 507.36 637.4 L 507.36 602.21" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@0" LinkObjectIDznd="85@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 507.36 637.4 L 507.36 602.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv10" d="M 507.36 673.99 L 507.36 815.68" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@1" LinkObjectIDznd="850@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 507.36 673.99 L 507.36 815.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv10" d="M 852.03 635.18 L 852.03 602.21" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@0" LinkObjectIDznd="85@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 852.03 635.18 L 852.03 602.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv10" d="M 574.71 797.35 L 574.71 813.74" stroke-width="1" zvalue="110"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@0" LinkObjectIDznd="91@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 574.71 797.35 L 574.71 813.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv10" d="M 472.96 797.35 L 472.96 824.17" stroke-width="1" zvalue="116"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@0" LinkObjectIDznd="98@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 472.96 797.35 L 472.96 824.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv10" d="M 424.17 797.35 L 424.17 832.04" stroke-width="1" zvalue="122"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="104@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 424.17 797.35 L 424.17 832.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv10" d="M 659.83 665.38 L 659.83 698.62 L 628.08 698.62 L 628.08 728.35" stroke-width="1" zvalue="132"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="283@1" LinkObjectIDznd="281@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 659.83 665.38 L 659.83 698.62 L 628.08 698.62 L 628.08 728.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv10" d="M 659.83 637.18 L 659.83 602.21" stroke-width="1" zvalue="134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="283@0" LinkObjectIDznd="85@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 659.83 637.18 L 659.83 602.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv10" d="M 659.83 738.54 L 659.83 679.33" stroke-width="1" zvalue="136"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@0" LinkObjectIDznd="120" MaxPinNum="2"/>
   </metadata>
  <path d="M 659.83 738.54 L 659.83 679.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv10" d="M 755.83 637.18 L 755.83 602.21" stroke-width="1" zvalue="142"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="85@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.83 637.18 L 755.83 602.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv10" d="M 755.83 731.2 L 755.83 665.38" stroke-width="1" zvalue="145"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@0" LinkObjectIDznd="57@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.83 731.2 L 755.83 665.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv10" d="M 733.28 710.26 L 733.28 701 L 755.83 701" stroke-width="1" zvalue="146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@0" LinkObjectIDznd="59" MaxPinNum="2"/>
   </metadata>
  <path d="M 733.28 710.26 L 733.28 701 L 755.83 701" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv10" d="M 818.05 710.82 L 818.05 693 L 852.03 693" stroke-width="1" zvalue="158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="43" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.05 710.82 L 818.05 693 L 852.03 693" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv10" d="M 880.03 708.2 L 880.03 691 L 852.03 691" stroke-width="1" zvalue="159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="43" MaxPinNum="2"/>
   </metadata>
  <path d="M 880.03 708.2 L 880.03 691 L 852.03 691" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv10" d="M 958.03 636.18 L 958.03 602.21" stroke-width="1" zvalue="163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="147@0" LinkObjectIDznd="85@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 958.03 636.18 L 958.03 602.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv10" d="M 958.03 672.68 L 958 789.92" stroke-width="1" zvalue="180"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="147@1" LinkObjectIDznd="305@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 958.03 672.68 L 958 789.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv10" d="M 924.05 711.82 L 924.05 689 L 958.03 689" stroke-width="1" zvalue="182"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="143@0" LinkObjectIDznd="149" MaxPinNum="2"/>
   </metadata>
  <path d="M 924.05 711.82 L 924.05 689 L 958.03 689" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv10" d="M 986.03 707.82 L 986.03 686 L 958.03 686" stroke-width="1" zvalue="183"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="149" MaxPinNum="2"/>
   </metadata>
  <path d="M 986.03 707.82 L 986.03 686 L 958.03 686" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv10" d="M 1057.03 636.18 L 1057.03 602.21" stroke-width="1" zvalue="187"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="85@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1057.03 636.18 L 1057.03 602.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv10" d="M 1057.03 672.68 L 1057.03 787.42" stroke-width="1" zvalue="195"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@1" LinkObjectIDznd="306@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1057.03 672.68 L 1057.03 787.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv10" d="M 1023.05 711.82 L 1023.05 689 L 1057.03 689" stroke-width="1" zvalue="196"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="157" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023.05 711.82 L 1023.05 689 L 1057.03 689" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv10" d="M 1085.03 707.82 L 1085.03 686 L 1057.03 686" stroke-width="1" zvalue="197"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@0" LinkObjectIDznd="157" MaxPinNum="2"/>
   </metadata>
  <path d="M 1085.03 707.82 L 1085.03 686 L 1057.03 686" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv10" d="M 1158.03 635.18 L 1158.03 602.21" stroke-width="1" zvalue="201"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="85@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1158.03 635.18 L 1158.03 602.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv10" d="M 1158.03 671.68 L 1158.03 791.5" stroke-width="1" zvalue="209"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@1" LinkObjectIDznd="192@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1158.03 671.68 L 1158.03 791.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv10" d="M 1124.05 710.82 L 1124.05 688 L 1158.03 688" stroke-width="1" zvalue="210"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="170" MaxPinNum="2"/>
   </metadata>
  <path d="M 1124.05 710.82 L 1124.05 688 L 1158.03 688" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv10" d="M 1186.03 706.82 L 1186.03 685 L 1158.03 685" stroke-width="1" zvalue="211"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@0" LinkObjectIDznd="170" MaxPinNum="2"/>
   </metadata>
  <path d="M 1186.03 706.82 L 1186.03 685 L 1158.03 685" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="kv10" d="M 1254.03 635.18 L 1254.03 602.21" stroke-width="1" zvalue="230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="85@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1254.03 635.18 L 1254.03 602.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="kv10" d="M 1254.03 671.68 L 1254.03 791.5" stroke-width="1" zvalue="237"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@1" LinkObjectIDznd="195@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1254.03 671.68 L 1254.03 791.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv10" d="M 1220.05 710.82 L 1220.05 688 L 1254.03 688" stroke-width="1" zvalue="238"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201@0" LinkObjectIDznd="198" MaxPinNum="2"/>
   </metadata>
  <path d="M 1220.05 710.82 L 1220.05 688 L 1254.03 688" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv10" d="M 1282.03 706.82 L 1282.03 684 L 1254.03 684" stroke-width="1" zvalue="239"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@0" LinkObjectIDznd="198" MaxPinNum="2"/>
   </metadata>
  <path d="M 1282.03 706.82 L 1282.03 684 L 1254.03 684" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="kv10" d="M 1409.99 634.27 L 1409.99 605.46" stroke-width="1" zvalue="249"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="210@0" LinkObjectIDznd="315@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.99 634.27 L 1409.99 605.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv10" d="M 1410.02 656.89 L 1410.02 706.57 L 1342.03 706.57 L 1342.03 671.68" stroke-width="1" zvalue="253"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="210@1" LinkObjectIDznd="215@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1410.02 656.89 L 1410.02 706.57 L 1342.03 706.57 L 1342.03 671.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv10" d="M 1342.03 635.18 L 1342.03 602.21" stroke-width="1" zvalue="254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@0" LinkObjectIDznd="85@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1342.03 635.18 L 1342.03 602.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="kv10" d="M 1322.03 706.82 L 1322.03 688 L 1342.03 688" stroke-width="1" zvalue="260"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@0" LinkObjectIDznd="216" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.03 706.82 L 1322.03 688 L 1342.03 688" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="239">
   <path class="kv10" d="M 1659.83 666.38 L 1659.83 699.62 L 1632.08 699.62 L 1632.08 729.35" stroke-width="1" zvalue="277"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="240@1" LinkObjectIDznd="238@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1659.83 666.38 L 1659.83 699.62 L 1632.08 699.62 L 1632.08 729.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="237">
   <path class="kv10" d="M 1659.83 638.18 L 1659.83 605.46" stroke-width="1" zvalue="279"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="240@0" LinkObjectIDznd="315@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1659.83 638.18 L 1659.83 605.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="236">
   <path class="kv10" d="M 1660.16 739.54 L 1660.16 679.74" stroke-width="1" zvalue="280"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@0" LinkObjectIDznd="239" MaxPinNum="2"/>
   </metadata>
  <path d="M 1660.16 739.54 L 1660.16 679.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="253">
   <path class="kv10" d="M 1567.03 637.18 L 1567.03 605.46" stroke-width="1" zvalue="286"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="254@0" LinkObjectIDznd="315@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1567.03 637.18 L 1567.03 605.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="245">
   <path class="kv10" d="M 1533.05 712.82 L 1533.05 690.39 L 1567.02 690.39" stroke-width="1" zvalue="294"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@0" LinkObjectIDznd="265" MaxPinNum="2"/>
   </metadata>
  <path d="M 1533.05 712.82 L 1533.05 690.39 L 1567.02 690.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="244">
   <path class="kv10" d="M 1595.03 708.82 L 1595.03 687 L 1567.02 687" stroke-width="1" zvalue="295"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="252@0" LinkObjectIDznd="265" MaxPinNum="2"/>
   </metadata>
  <path d="M 1595.03 708.82 L 1595.03 687 L 1567.02 687" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="262">
   <path class="kv10" d="M 1749.83 639.18 L 1749.83 605.46" stroke-width="1" zvalue="303"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@0" LinkObjectIDznd="315@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1749.83 639.18 L 1749.83 605.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="260">
   <path class="kv10" d="M 1749.83 733.2 L 1749.83 667.38" stroke-width="1" zvalue="305"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@0" LinkObjectIDznd="263@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1749.83 733.2 L 1749.83 667.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="259">
   <path class="kv10" d="M 1727.28 712.26 L 1727.28 703 L 1749.83 703" stroke-width="1" zvalue="306"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="264@0" LinkObjectIDznd="260" MaxPinNum="2"/>
   </metadata>
  <path d="M 1727.28 712.26 L 1727.28 703 L 1749.83 703" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="265">
   <path class="kv10" d="M 1567.03 673.68 L 1567 738.5" stroke-width="1" zvalue="307"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="254@1" LinkObjectIDznd="44@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1567.03 673.68 L 1567 738.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="273">
   <path class="kv10" d="M 1827.03 639.18 L 1827.03 605.46" stroke-width="1" zvalue="311"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="315@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1827.03 639.18 L 1827.03 605.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="kv10" d="M 1793 715.5 L 1793 692 L 1827.03 692" stroke-width="1" zvalue="314"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="267" MaxPinNum="2"/>
   </metadata>
  <path d="M 1793 715.5 L 1793 692 L 1827.03 692" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv10" d="M 1855.03 710.82 L 1855.03 689 L 1827.03 689" stroke-width="1" zvalue="315"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="267" MaxPinNum="2"/>
   </metadata>
  <path d="M 1855.03 710.82 L 1855.03 689 L 1827.03 689" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="267">
   <path class="kv10" d="M 1827.03 675.68 L 1827.03 739.5" stroke-width="1" zvalue="317"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@1" LinkObjectIDznd="47@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1827.03 675.68 L 1827.03 739.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="290">
   <path class="kv35" d="M 1190.58 168.12 L 1212.77 168.12" stroke-width="1" zvalue="340"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="287@0" LinkObjectIDznd="289@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1190.58 168.12 L 1212.77 168.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="291">
   <path class="kv35" d="M 1236.6 168.15 L 1272.83 168.15 L 1272.83 186.18" stroke-width="1" zvalue="341"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="289@1" LinkObjectIDznd="286@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1236.6 168.15 L 1272.83 168.15 L 1272.83 186.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="292">
   <path class="kv35" d="M 1272.83 214.38 L 1272.83 255.18" stroke-width="1" zvalue="342"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="286@1" LinkObjectIDznd="282@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1272.83 214.38 L 1272.83 255.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="294">
   <path class="kv35" d="M 1272.83 291.68 L 1272.83 374.01" stroke-width="1" zvalue="344"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="282@1" LinkObjectIDznd="278@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1272.83 291.68 L 1272.83 374.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="295">
   <path class="kv10" d="M 1273.79 429.76 L 1273.79 525.71" stroke-width="1" zvalue="345"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="278@1" LinkObjectIDznd="275@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1273.79 429.76 L 1273.79 525.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="296">
   <path class="kv10" d="M 1273.79 562.21 L 1273.79 602.21" stroke-width="1" zvalue="346"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="275@1" LinkObjectIDznd="85@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1273.79 562.21 L 1273.79 602.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="297">
   <path class="kv10" d="M 1228.03 472.82 L 1228.03 443.75 L 1273.79 443.75" stroke-width="1" zvalue="347"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="277@0" LinkObjectIDznd="295" MaxPinNum="2"/>
   </metadata>
  <path d="M 1228.03 472.82 L 1228.03 443.75 L 1273.79 443.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="299">
   <path class="kv35" d="M 1193 246.08 L 1193 236.25 L 1272.83 236.25" stroke-width="1" zvalue="349"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@1" LinkObjectIDznd="292" MaxPinNum="2"/>
   </metadata>
  <path d="M 1193 246.08 L 1193 236.25 L 1272.83 236.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="300">
   <path class="kv35" d="M 1336.1 268.44 L 1336.1 228.75 L 1272.83 228.75" stroke-width="1" zvalue="350"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="285@0" LinkObjectIDznd="292" MaxPinNum="2"/>
   </metadata>
  <path d="M 1336.1 268.44 L 1336.1 228.75 L 1272.83 228.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="324">
   <path class="kv10" d="M 1464.28 635.18 L 1464.28 605.46" stroke-width="1" zvalue="359"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="325@0" LinkObjectIDznd="315@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1464.28 635.18 L 1464.28 605.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="318">
   <path class="kv10" d="M 1464.28 671.68 L 1464.28 791.5" stroke-width="1" zvalue="365"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="325@1" LinkObjectIDznd="314@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1464.28 671.68 L 1464.28 791.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="317">
   <path class="kv10" d="M 1430.3 710.82 L 1430.3 688 L 1464.28 688" stroke-width="1" zvalue="366"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="321@0" LinkObjectIDznd="318" MaxPinNum="2"/>
   </metadata>
  <path d="M 1430.3 710.82 L 1430.3 688 L 1464.28 688" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="316">
   <path class="kv10" d="M 1492.28 706.82 L 1492.28 685 L 1464.28 685" stroke-width="1" zvalue="367"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="323@0" LinkObjectIDznd="318" MaxPinNum="2"/>
   </metadata>
  <path d="M 1492.28 706.82 L 1492.28 685 L 1464.28 685" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv10" d="M 852.03 671.68 L 852.03 767.08" stroke-width="1" zvalue="386"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@1" LinkObjectIDznd="301@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 852.03 671.68 L 852.03 767.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv35" d="M 1193.08 275.35 L 1193.08 271.97" stroke-width="1" zvalue="389"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="23@0" LinkObjectIDznd="5@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1193.08 275.35 L 1193.08 271.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv35" d="M 1218.51 340 L 1218.51 324 L 1272.83 324" stroke-width="1" zvalue="390"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@0" LinkObjectIDznd="294" MaxPinNum="2"/>
   </metadata>
  <path d="M 1218.51 340 L 1218.51 324 L 1272.83 324" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv10" d="M 574.62 778.33 L 574.64 747 L 507.36 747" stroke-width="1" zvalue="402"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@1" LinkObjectIDznd="83" MaxPinNum="2"/>
   </metadata>
  <path d="M 574.62 778.33 L 574.64 747 L 507.36 747" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv10" d="M 424.08 778.33 L 424.1 747 L 507.36 747" stroke-width="1" zvalue="403"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@1" LinkObjectIDznd="68" MaxPinNum="2"/>
   </metadata>
  <path d="M 424.08 778.33 L 424.1 747 L 507.36 747" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv10" d="M 528.3 784.71 L 528.3 747" stroke-width="1" zvalue="404"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="68" MaxPinNum="2"/>
   </metadata>
  <path d="M 528.3 784.71 L 528.3 747" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv10" d="M 472.87 778.33 L 472.87 747" stroke-width="1" zvalue="405"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@1" LinkObjectIDznd="69" MaxPinNum="2"/>
   </metadata>
  <path d="M 472.87 778.33 L 472.87 747" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="kv10" height="30" transform="rotate(0,507.364,830.134) scale(0.979773,0.979773) translate(10.1709,16.8343)" width="30" x="492.6675564336012" xlink:href="#Generator:发电机_0" y="815.4376034133448" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454820364290" ObjectName="10kV#1汽轮发电机"/>
   <cge:TPSR_Ref TObjectID="6192454820364290"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,507.364,830.134) scale(0.979773,0.979773) translate(10.1709,16.8343)" width="30" x="492.6675564336012" y="815.4376034133448"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="79">
   <use class="kv10" height="26" transform="rotate(0,528.281,792.783) scale(0.653182,0.653182) translate(278.419,416.433)" width="12" x="524.361474790046" xlink:href="#Accessory:避雷器1_0" y="784.2917163009599" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454820298754" ObjectName="#1发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,528.281,792.783) scale(0.653182,0.653182) translate(278.419,416.433)" width="12" x="524.361474790046" y="784.2917163009599"/></g>
  <g id="80">
   <use class="kv10" height="26" transform="rotate(0,880,720.571) scale(1,1) translate(0,0)" width="12" x="874" xlink:href="#Accessory:避雷器1_0" y="707.5714285714287" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454820233218" ObjectName="10kV#1引风机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,880,720.571) scale(1,1) translate(0,0)" width="12" x="874" y="707.5714285714287"/></g>
  <g id="91">
   <use class="kv10" height="30" transform="rotate(0,574.765,835.608) scale(1.11961,1.49282) translate(-59.6097,-268.463)" width="30" x="557.9708221020389" xlink:href="#Accessory:PT789_0" y="813.2155403135894" zvalue="108"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454820626434" ObjectName="PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,574.765,835.608) scale(1.11961,1.49282) translate(-59.6097,-268.463)" width="30" x="557.9708221020389" y="813.2155403135894"/></g>
  <g id="98">
   <use class="kv10" height="26" transform="rotate(0,473.041,840.521) scale(1.64787,1.2676) translate(-181.12,-173.959)" width="15" x="460.6815582534488" xlink:href="#Accessory:20210316PT_0" y="824.0425141874769" zvalue="115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454820691970" ObjectName="PT2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,473.041,840.521) scale(1.64787,1.2676) translate(-181.12,-173.959)" width="15" x="460.6815582534488" y="824.0425141874769"/></g>
  <g id="104">
   <use class="kv10" height="30" transform="rotate(0,423.582,843.521) scale(1.17394,1.56525) translate(-60.1512,-296.137)" width="30" x="405.9725709620681" xlink:href="#Accessory:PT象达_0" y="820.0425141874764" zvalue="121"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454820954114" ObjectName="10kVⅠ段母线PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,423.582,843.521) scale(1.17394,1.56525) translate(-60.1512,-296.137)" width="30" x="405.9725709620681" y="820.0425141874764"/></g>
  <g id="281">
   <use class="kv10" height="30" transform="rotate(0,628.141,745.176) scale(1.2284,1.14858) translate(-113.365,-94.1649)" width="30" x="609.7152659679655" xlink:href="#Accessory:三卷PT带容断器_0" y="727.9471748335609" zvalue="133"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454821216258" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,628.141,745.176) scale(1.2284,1.14858) translate(-113.365,-94.1649)" width="30" x="609.7152659679655" y="727.9471748335609"/></g>
  <g id="39">
   <use class="kv10" height="29" transform="rotate(0,659.671,750) scale(1,-1) translate(0,-1500)" width="20" x="649.6710623841695" xlink:href="#Accessory:消谐装置_0" y="735.5" zvalue="137"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454821347330" ObjectName="10kVⅠ段母线消谐"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,659.671,750) scale(1,-1) translate(0,-1500)" width="20" x="649.6710623841695" y="735.5"/></g>
  <g id="54">
   <use class="kv10" height="60" transform="rotate(0,733,734.5) scale(0.85,-0.85) translate(126.353,-1603.12)" width="40" x="716" xlink:href="#Accessory:1144_0" y="709" zvalue="138"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454821412866" ObjectName="10kV#1全电压检测柜"/>
   </metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,733,734.5) scale(0.85,-0.85) translate(126.353,-1603.12)" width="40" x="716" y="709"/></g>
  <g id="58">
   <use class="kv10" height="26" transform="rotate(0,755.796,743.571) scale(1,1) translate(0,0)" width="12" x="749.796457056226" xlink:href="#Accessory:避雷器1_0" y="730.5714285714287" zvalue="144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454821543938" ObjectName="10kV#1全电压检测柜避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,755.796,743.571) scale(1,1) translate(0,0)" width="12" x="749.796457056226" y="730.5714285714287"/></g>
  <g id="145">
   <use class="kv10" height="26" transform="rotate(0,986,720.188) scale(1,1) translate(0,0)" width="12" x="980" xlink:href="#Accessory:避雷器1_0" y="707.1880952380952" zvalue="164"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454821740546" ObjectName="10kV#1循环水泵避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,986,720.188) scale(1,1) translate(0,0)" width="12" x="980" y="707.1880952380952"/></g>
  <g id="163">
   <use class="kv10" height="26" transform="rotate(0,1085,720.188) scale(1,1) translate(0,0)" width="12" x="1079" xlink:href="#Accessory:避雷器1_0" y="707.1880952380952" zvalue="188"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454821937154" ObjectName="10kV#2循环水泵避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1085,720.188) scale(1,1) translate(0,0)" width="12" x="1079" y="707.1880952380952"/></g>
  <g id="176">
   <use class="kv10" height="26" transform="rotate(0,1186,719.188) scale(1,1) translate(0,0)" width="12" x="1180" xlink:href="#Accessory:避雷器1_0" y="706.1880952380952" zvalue="202"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454822133762" ObjectName="10kV#0厂用备用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1186,719.188) scale(1,1) translate(0,0)" width="12" x="1180" y="706.1880952380952"/></g>
  <g id="203">
   <use class="kv10" height="26" transform="rotate(0,1282,719.188) scale(1,1) translate(0,0)" width="12" x="1276" xlink:href="#Accessory:避雷器1_0" y="706.1880952380952" zvalue="231"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454822461442" ObjectName="10kV#1厂用工作变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1282,719.188) scale(1,1) translate(0,0)" width="12" x="1276" y="706.1880952380952"/></g>
  <g id="219">
   <use class="kv10" height="26" transform="rotate(0,1322,719.188) scale(1,1) translate(0,0)" width="12" x="1316" xlink:href="#Accessory:避雷器1_0" y="706.1880952380952" zvalue="258"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454822592514" ObjectName="10kV分段避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1322,719.188) scale(1,1) translate(0,0)" width="12" x="1316" y="706.1880952380952"/></g>
  <g id="238">
   <use class="kv10" height="30" transform="rotate(0,1632.14,746.176) scale(1.2284,1.14858) translate(-300.039,-94.2942)" width="30" x="1613.715265967965" xlink:href="#Accessory:三卷PT带容断器_0" y="728.9471748335609" zvalue="278"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454822723586" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1632.14,746.176) scale(1.2284,1.14858) translate(-300.039,-94.2942)" width="30" x="1613.715265967965" y="728.9471748335609"/></g>
  <g id="235">
   <use class="kv10" height="29" transform="rotate(0,1660,751) scale(1,-1) translate(0,-1502)" width="20" x="1650" xlink:href="#Accessory:消谐装置_0" y="736.5" zvalue="281"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454822658050" ObjectName="10kVⅡ段母线互感器消谐"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1660,751) scale(1,-1) translate(0,-1502)" width="20" x="1650" y="736.5"/></g>
  <g id="252">
   <use class="kv10" height="26" transform="rotate(0,1595,721.188) scale(1,1) translate(0,0)" width="12" x="1589" xlink:href="#Accessory:避雷器1_0" y="708.1880952380952" zvalue="287"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454822985730" ObjectName="备用避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1595,721.188) scale(1,1) translate(0,0)" width="12" x="1589" y="708.1880952380952"/></g>
  <g id="264">
   <use class="kv10" height="60" transform="rotate(0,1727,736.5) scale(0.85,-0.85) translate(301.765,-1607.47)" width="40" x="1710" xlink:href="#Accessory:1144_0" y="711" zvalue="301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454823247874" ObjectName="10kV#2全电压检测柜"/>
   </metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1727,736.5) scale(0.85,-0.85) translate(301.765,-1607.47)" width="40" x="1710" y="711"/></g>
  <g id="261">
   <use class="kv10" height="26" transform="rotate(0,1749.8,745.571) scale(1,1) translate(0,0)" width="12" x="1743.796457056226" xlink:href="#Accessory:避雷器1_0" y="732.5714285714287" zvalue="304"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454823116802" ObjectName="10kV#2全电压检测柜避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1749.8,745.571) scale(1,1) translate(0,0)" width="12" x="1743.796457056226" y="732.5714285714287"/></g>
  <g id="272">
   <use class="kv10" height="26" transform="rotate(0,1855,723.188) scale(1,1) translate(0,0)" width="12" x="1849" xlink:href="#Accessory:避雷器1_0" y="710.1880952380952" zvalue="312"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454823510018" ObjectName="备用电源避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1855,723.188) scale(1,1) translate(0,0)" width="12" x="1849" y="710.1880952380952"/></g>
  <g id="277">
   <use class="kv10" height="26" transform="rotate(0,1228,485.188) scale(1,1) translate(0,0)" width="12" x="1222" xlink:href="#Accessory:避雷器1_0" y="472.1880952380953" zvalue="323"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454823575554" ObjectName="#1主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1228,485.188) scale(1,1) translate(0,0)" width="12" x="1222" y="472.1880952380953"/></g>
  <g id="280">
   <use class="kv35" height="26" transform="rotate(0,1218.47,352.367) scale(1,1) translate(0,0)" width="12" x="1212.473684210526" xlink:href="#Accessory:避雷器1_0" y="339.3667045720218" zvalue="329"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454823641090" ObjectName="#1站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1218.47,352.367) scale(1,1) translate(0,0)" width="12" x="1212.473684210526" y="339.3667045720218"/></g>
  <g id="285">
   <use class="kv35" height="40" transform="rotate(0,1336.1,285.705) scale(0.933117,-0.933117) translate(94.7642,-593.226)" width="30" x="1322.105648702319" xlink:href="#Accessory:带熔断器的线路PT1_0" y="267.0425141874764" zvalue="334"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454823772162" ObjectName="35kV丽宏线垃圾发电厂T线PT3"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1336.1,285.705) scale(0.933117,-0.933117) translate(94.7642,-593.226)" width="30" x="1322.105648702319" y="267.0425141874764"/></g>
  <g id="323">
   <use class="kv10" height="26" transform="rotate(0,1492.25,719.188) scale(1,1) translate(0,0)" width="12" x="1486.25" xlink:href="#Accessory:避雷器1_0" y="706.1880952380952" zvalue="360"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454824427522" ObjectName="10kV#2厂用工作变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1492.25,719.188) scale(1,1) translate(0,0)" width="12" x="1486.25" y="706.1880952380952"/></g>
  <g id="23">
   <use class="kv35" height="30" transform="rotate(0,1193.13,290) scale(1,1) translate(0,0)" width="30" x="1178.134067223916" xlink:href="#Accessory:PT789_0" y="275" zvalue="388"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454862766082" ObjectName="35kV丽宏线垃圾发电厂T线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1193.13,290) scale(1,1) translate(0,0)" width="30" x="1178.134067223916" y="275"/></g>
  <g id="63">
   <use class="kv10" height="40" transform="rotate(0,1793,734) scale(1,-1) translate(0,-1468)" width="30" x="1778" xlink:href="#Accessory:带熔断器的线路PT1_0" y="714" zvalue="413"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454927646722" ObjectName="备用电源线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1793,734) scale(1,-1) translate(0,-1468)" width="30" x="1778" y="714"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="99">
   <use class="kv10" height="20" transform="rotate(0,818,720.571) scale(1,1) translate(0,0)" width="10" x="813" xlink:href="#GroundDisconnector:地刀_0" y="710.5714285714287" zvalue="68"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454820167682" ObjectName="10kV#1引风机07367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454820167682"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,818,720.571) scale(1,1) translate(0,0)" width="10" x="813" y="710.5714285714287"/></g>
  <g id="143">
   <use class="kv10" height="20" transform="rotate(0,924,721.571) scale(1,1) translate(0,0)" width="10" x="919" xlink:href="#GroundDisconnector:地刀_0" y="711.5714285714287" zvalue="166"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454821675010" ObjectName="10kV#1循环水泵07467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454821675010"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,924,721.571) scale(1,1) translate(0,0)" width="10" x="919" y="711.5714285714287"/></g>
  <g id="161">
   <use class="kv10" height="20" transform="rotate(0,1023,721.571) scale(1,1) translate(0,0)" width="10" x="1018" xlink:href="#GroundDisconnector:地刀_0" y="711.5714285714287" zvalue="190"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454821871618" ObjectName="10kV#2循环水泵07567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454821871618"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1023,721.571) scale(1,1) translate(0,0)" width="10" x="1018" y="711.5714285714287"/></g>
  <g id="174">
   <use class="kv10" height="20" transform="rotate(0,1124,720.571) scale(1,1) translate(0,0)" width="10" x="1119" xlink:href="#GroundDisconnector:地刀_0" y="710.5714285714287" zvalue="204"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454822068226" ObjectName="10kV#0厂用备用变07667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454822068226"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1124,720.571) scale(1,1) translate(0,0)" width="10" x="1119" y="710.5714285714287"/></g>
  <g id="201">
   <use class="kv10" height="20" transform="rotate(0,1220,720.571) scale(1,1) translate(0,0)" width="10" x="1215" xlink:href="#GroundDisconnector:地刀_0" y="710.5714285714287" zvalue="233"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454822395906" ObjectName="10kV#1厂用工作变07767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454822395906"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1220,720.571) scale(1,1) translate(0,0)" width="10" x="1215" y="710.5714285714287"/></g>
  <g id="250">
   <use class="kv10" height="20" transform="rotate(0,1533,722.571) scale(1,1) translate(0,0)" width="10" x="1528" xlink:href="#GroundDisconnector:地刀_0" y="712.5714285714287" zvalue="289"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454822920194" ObjectName="10kV备用07967接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454822920194"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1533,722.571) scale(1,1) translate(0,0)" width="10" x="1528" y="712.5714285714287"/></g>
  <g id="321">
   <use class="kv10" height="20" transform="rotate(0,1430.25,720.571) scale(1,1) translate(0,0)" width="10" x="1425.25" xlink:href="#GroundDisconnector:地刀_0" y="710.5714285714287" zvalue="362"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454824361986" ObjectName="10kV#2厂用工作变07867接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454824361986"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1430.25,720.571) scale(1,1) translate(0,0)" width="10" x="1425.25" y="710.5714285714287"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="246">
   <use class="kv10" height="26" transform="rotate(0,574.618,787.822) scale(1.08526,0.734636) translate(-44.6304,281.126)" width="12" x="568.1062326408542" xlink:href="#Disconnector:20210316_0" y="778.2713904937907" zvalue="105"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454820429826" ObjectName="10kV#1汽轮发电机0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454820429826"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,574.618,787.822) scale(1.08526,0.734636) translate(-44.6304,281.126)" width="12" x="568.1062326408542" y="778.2713904937907"/></g>
  <g id="101">
   <use class="kv10" height="26" transform="rotate(0,472.867,787.822) scale(1.08526,0.734636) translate(-36.6369,281.126)" width="12" x="466.3554404496329" xlink:href="#Disconnector:20210316_0" y="778.2713905271618" zvalue="113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454820888578" ObjectName="10kV#1汽轮发电机0912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454820888578"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,472.867,787.822) scale(1.08526,0.734636) translate(-36.6369,281.126)" width="12" x="466.3554404496329" y="778.2713905271618"/></g>
  <g id="108">
   <use class="kv10" height="26" transform="rotate(0,424.077,787.822) scale(1.08526,0.734636) translate(-32.8039,281.126)" width="12" x="417.5658154454402" xlink:href="#Disconnector:20210316_0" y="778.2713905182843" zvalue="119"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454821150722" ObjectName="10kV#1汽轮发电机0913隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454821150722"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,424.077,787.822) scale(1.08526,0.734636) translate(-32.8039,281.126)" width="12" x="417.5658154454402" y="778.2713905182843"/></g>
  <g id="283">
   <use class="kv10" height="36" transform="rotate(0,659.83,651.281) scale(1.05291,0.829527) translate(-32.7875,130.774)" width="14" x="652.4594126952742" xlink:href="#Disconnector:手车刀闸_0" y="636.3496699684119" zvalue="130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454907854851" ObjectName="10kVⅠ段母线0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454907854851"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,659.83,651.281) scale(1.05291,0.829527) translate(-32.7875,130.774)" width="14" x="652.4594126952742" y="636.3496699684119"/></g>
  <g id="57">
   <use class="kv10" height="36" transform="rotate(0,755.83,651.281) scale(1.05291,0.829527) translate(-37.6117,130.774)" width="14" x="748.4594126952742" xlink:href="#Disconnector:手车刀闸_0" y="636.3496699684119" zvalue="140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454821478402" ObjectName="10kV#1全电压检测柜0721隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454821478402"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,755.83,651.281) scale(1.05291,0.829527) translate(-37.6117,130.774)" width="14" x="748.4594126952742" y="636.3496699684119"/></g>
  <g id="210">
   <use class="kv10" height="26" transform="rotate(0,1410,645.571) scale(1,1) translate(0,0)" width="14" x="1403" xlink:href="#Disconnector:联体手车刀闸_0" y="632.5714285714286" zvalue="247"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454822526978" ObjectName="10kV分段0122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454822526978"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1410,645.571) scale(1,1) translate(0,0)" width="14" x="1403" y="632.5714285714286"/></g>
  <g id="240">
   <use class="kv10" height="36" transform="rotate(0,1659.83,652.281) scale(1.05291,0.829527) translate(-83.0397,130.979)" width="14" x="1652.459412695274" xlink:href="#Disconnector:手车刀闸_0" y="637.3496699684119" zvalue="276"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454907920387" ObjectName="10kVⅡ段母线0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454907920387"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1659.83,652.281) scale(1.05291,0.829527) translate(-83.0397,130.979)" width="14" x="1652.459412695274" y="637.3496699684119"/></g>
  <g id="263">
   <use class="kv10" height="36" transform="rotate(0,1749.83,653.281) scale(1.05291,0.829527) translate(-87.5624,131.185)" width="14" x="1742.459412695274" xlink:href="#Disconnector:手车刀闸_0" y="638.3496699684119" zvalue="302"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454907985923" ObjectName="10kV#2全电压检测柜0812隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454907985923"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1749.83,653.281) scale(1.05291,0.829527) translate(-87.5624,131.185)" width="14" x="1742.459412695274" y="638.3496699684119"/></g>
  <g id="286">
   <use class="kv35" height="36" transform="rotate(0,1272.83,200.281) scale(1.05291,0.829527) translate(-63.5921,38.0905)" width="14" x="1265.459425772512" xlink:href="#Disconnector:手车刀闸_0" y="185.3496699684121" zvalue="336"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454823837698" ObjectName="35kV丽宏线垃圾发电厂T线3718隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454823837698"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1272.83,200.281) scale(1.05291,0.829527) translate(-63.5921,38.0905)" width="14" x="1265.459425772512" y="185.3496699684121"/></g>
  <g id="289">
   <use class="kv35" height="30" transform="rotate(270,1224.58,168.222) scale(1.11111,0.814815) translate(-121.625,35.4545)" width="15" x="1216.250010172526" xlink:href="#Disconnector:刀闸_0" y="156.000006781684" zvalue="339"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454823968770" ObjectName="35kV丽宏线垃圾发电厂T线3716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454823968770"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1224.58,168.222) scale(1.11111,0.814815) translate(-121.625,35.4545)" width="15" x="1216.250010172526" y="156.000006781684"/></g>
  <g id="5">
   <use class="kv35" height="26" transform="rotate(0,1193,259) scale(1,1) translate(0,0)" width="12" x="1187" xlink:href="#Disconnector:20210316_0" y="246" zvalue="387"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454862700546" ObjectName="35kV丽宏线垃圾发电厂T线3719隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454862700546"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1193,259) scale(1,1) translate(0,0)" width="12" x="1187" y="246"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="192">
   <use class="kv10" height="30" transform="rotate(0,1158.03,805) scale(1,-1) translate(0,-1610)" width="12" x="1152.029569585283" xlink:href="#EnergyConsumer:负荷_0" y="790" zvalue="226"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454822199298" ObjectName="10kV#0厂用备用变"/>
   <cge:TPSR_Ref TObjectID="6192454822199298"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1158.03,805) scale(1,-1) translate(0,-1610)" width="12" x="1152.029569585283" y="790"/></g>
  <g id="195">
   <use class="kv10" height="30" transform="rotate(0,1254.03,805) scale(1,-1) translate(0,-1610)" width="12" x="1248.029569585283" xlink:href="#EnergyConsumer:负荷_0" y="790" zvalue="240"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454822264834" ObjectName="10kV#1厂用工作变"/>
   <cge:TPSR_Ref TObjectID="6192454822264834"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1254.03,805) scale(1,-1) translate(0,-1610)" width="12" x="1248.029569585283" y="790"/></g>
  <g id="301">
   <use class="kv10" height="35" transform="rotate(0,852.03,783.17) scale(1.52675,0.946549) translate(-288.695,43.2896)" width="20" x="836.7620494276455" xlink:href="#EnergyConsumer:引风机_0" y="766.6052631578949" zvalue="351"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454824034306" ObjectName="10kV#1引风机"/>
   <cge:TPSR_Ref TObjectID="6192454824034306"/></metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,852.03,783.17) scale(1.52675,0.946549) translate(-288.695,43.2896)" width="20" x="836.7620494276455" y="766.6052631578949"/></g>
  <g id="305">
   <use class="kv10" height="28" transform="rotate(0,958,808) scale(1.38889,1.33929) translate(-262.99,-199.943)" width="27" x="939.25" xlink:href="#EnergyConsumer:水泵_0" y="789.25" zvalue="353"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454824099842" ObjectName="10kV#1循环水泵"/>
   <cge:TPSR_Ref TObjectID="6192454824099842"/></metadata>
  <rect fill="white" height="28" opacity="0" stroke="white" transform="rotate(0,958,808) scale(1.38889,1.33929) translate(-262.99,-199.943)" width="27" x="939.25" y="789.25"/></g>
  <g id="306">
   <use class="kv10" height="28" transform="rotate(0,1057.25,805.5) scale(1.38889,1.33929) translate(-290.78,-199.31)" width="27" x="1038.5" xlink:href="#EnergyConsumer:水泵_0" y="786.75" zvalue="355"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454824165378" ObjectName="10kV#2循环水泵"/>
   <cge:TPSR_Ref TObjectID="6192454824165378"/></metadata>
  <rect fill="white" height="28" opacity="0" stroke="white" transform="rotate(0,1057.25,805.5) scale(1.38889,1.33929) translate(-290.78,-199.31)" width="27" x="1038.5" y="786.75"/></g>
  <g id="314">
   <use class="kv10" height="30" transform="rotate(0,1464.28,805) scale(1,-1) translate(0,-1610)" width="12" x="1458.279569585283" xlink:href="#EnergyConsumer:负荷_0" y="790" zvalue="368"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454824230914" ObjectName="10kV#2厂用工作变"/>
   <cge:TPSR_Ref TObjectID="6192454824230914"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1464.28,805) scale(1,-1) translate(0,-1610)" width="12" x="1458.279569585283" y="790"/></g>
  <g id="44">
   <use class="kv10" height="30" transform="rotate(0,1567,752) scale(1,-1) translate(0,-1504)" width="12" x="1561" xlink:href="#EnergyConsumer:负荷_0" y="737" zvalue="406"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454906544130" ObjectName="10kV备用"/>
   <cge:TPSR_Ref TObjectID="6192454906544130"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1567,752) scale(1,-1) translate(0,-1504)" width="12" x="1561" y="737"/></g>
  <g id="47">
   <use class="kv10" height="30" transform="rotate(0,1828,753) scale(1,-1) translate(0,-1506)" width="12" x="1822" xlink:href="#EnergyConsumer:负荷_0" y="738" zvalue="408"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454906609666" ObjectName="10kV备用电源线"/>
   <cge:TPSR_Ref TObjectID="6192454906609666"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1828,753) scale(1,-1) translate(0,-1506)" width="12" x="1822" y="738"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="278">
   <g id="2780">
    <use class="kv35" height="60" transform="rotate(0,1273.79,401.828) scale(0.858717,0.944457) translate(206.748,21.9649)" width="40" x="1256.62" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="373.49" zvalue="325"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874594385922" ObjectName="35"/>
    </metadata>
   </g>
   <g id="2781">
    <use class="kv10" height="60" transform="rotate(0,1273.79,401.828) scale(0.858717,0.944457) translate(206.748,21.9649)" width="40" x="1256.62" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="373.49" zvalue="325"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874594451458" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399534247938" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399534247938"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1273.79,401.828) scale(0.858717,0.944457) translate(206.748,21.9649)" width="40" x="1256.62" y="373.49"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="287">
   <use class="kv35" height="40" transform="rotate(270,1169.25,168.035) scale(0.716667,1.075) translate(458.012,-10.2234)" width="30" x="1158.5" xlink:href="#ACLineSegment:线路带壁雷器_0" y="146.5351156140609" zvalue="337"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249322356741" ObjectName="35kV丽宏线垃圾发电厂T线"/>
   <cge:TPSR_Ref TObjectID="8444249322356741_5066549684797441"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,1169.25,168.035) scale(0.716667,1.075) translate(458.012,-10.2234)" width="30" x="1158.5" y="146.5351156140609"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="8" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1067.25,151.035) scale(1,1) translate(0,0)" writing-mode="lr" x="1066.79" xml:space="preserve" y="155.81" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136528162818" ObjectName="P"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="9" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1067.25,168.035) scale(1,1) translate(0,0)" writing-mode="lr" x="1066.79" xml:space="preserve" y="172.81" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136528228354" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="31" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1067.25,185.035) scale(1,1) translate(0,0)" writing-mode="lr" x="1066.79" xml:space="preserve" y="189.81" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136528293890" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="64" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1417.29,301.994) scale(1,1) translate(0,0)" writing-mode="lr" x="1416.75" xml:space="preserve" y="308.27" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136525606914" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="65" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1417.29,322.994) scale(1,1) translate(0,0)" writing-mode="lr" x="1416.75" xml:space="preserve" y="329.27" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136525672450" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="66" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1417.29,469.661) scale(1,1) translate(0,1.004e-13)" writing-mode="lr" x="1416.75" xml:space="preserve" y="475.94" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136525737986" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="67">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="67" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1417.29,494.661) scale(1,1) translate(0,0)" writing-mode="lr" x="1416.75" xml:space="preserve" y="500.94" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136525803522" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="72">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="72" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1417.29,343.994) scale(1,1) translate(0,0)" writing-mode="lr" x="1416.75" xml:space="preserve" y="350.27" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136525869058" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="73" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1417.29,515.661) scale(1,1) translate(0,0)" writing-mode="lr" x="1416.75" xml:space="preserve" y="521.9400000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136526196738" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="74">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="74" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,491.864,928.331) scale(1,1) translate(0,0)" writing-mode="lr" x="491.32" xml:space="preserve" y="934.61" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136517873666" ObjectName="P"/>
   </metadata>
  </g>
  <g id="77">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="77" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,491.864,960.331) scale(1,1) translate(0,0)" writing-mode="lr" x="491.32" xml:space="preserve" y="966.61" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136517939202" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="78" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,490.864,984.331) scale(1,1) translate(0,0)" writing-mode="lr" x="490.32" xml:space="preserve" y="990.61" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136518004738" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,198,503.71) scale(1,1) translate(0,-1.08816e-13)" writing-mode="lr" x="197.67" xml:space="preserve" y="508.43" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136519053314" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="87" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,198,530.71) scale(1,1) translate(0,5.74058e-14)" writing-mode="lr" x="197.67" xml:space="preserve" y="535.4299999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136519118850" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="90">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,198,554.71) scale(1,1) translate(0,-1.20141e-13)" writing-mode="lr" x="197.67" xml:space="preserve" y="559.4299999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136519184386" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="94" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,581,584.21) scale(1,1) translate(0,0)" writing-mode="lr" x="580.54" xml:space="preserve" y="588.99" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136519249922" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,199.5,481.355) scale(1,1) translate(0,5.19263e-14)" writing-mode="lr" x="199.17" xml:space="preserve" y="486.07" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136519315458" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="96" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,198,578.71) scale(1,1) translate(0,6.27349e-14)" writing-mode="lr" x="197.67" xml:space="preserve" y="583.4299999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136519512066" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,262,505.96) scale(1,1) translate(0,-1.09316e-13)" writing-mode="lr" x="261.67" xml:space="preserve" y="510.68" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136517349378" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="102" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,262,530.96) scale(1,1) translate(0,-1.14867e-13)" writing-mode="lr" x="261.67" xml:space="preserve" y="535.6799999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136517414914" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,262,552.96) scale(1,1) translate(0,-1.19752e-13)" writing-mode="lr" x="261.67" xml:space="preserve" y="557.6799999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136517480450" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="106">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="106" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1794,590.46) scale(1,1) translate(0,1.27223e-13)" writing-mode="lr" x="1793.54" xml:space="preserve" y="595.24" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136517545986" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,261,481.98) scale(1,1) translate(0,5.19957e-14)" writing-mode="lr" x="260.67" xml:space="preserve" y="486.7" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136517611522" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="109">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="109" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,262,580.96) scale(1,1) translate(0,-1.25969e-13)" writing-mode="lr" x="261.67" xml:space="preserve" y="585.6799999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136517808130" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="112">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="112" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,848.03,879.234) scale(1,1) translate(0,0)" writing-mode="lr" x="847.5700000000001" xml:space="preserve" y="884.01" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136773660673" ObjectName="P"/>
   </metadata>
  </g>
  <g id="113">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="113" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,848.03,900.234) scale(1,1) translate(0,0)" writing-mode="lr" x="847.5700000000001" xml:space="preserve" y="905.01" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136773726209" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="114">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="114" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,848.03,920.25) scale(1,1) translate(0,0)" writing-mode="lr" x="847.5700000000001" xml:space="preserve" y="925.03" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136773791745" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,853.797,947.17) scale(1,1) translate(-1.77924e-13,0)" writing-mode="lr" x="853.25" xml:space="preserve" y="953.45" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136774184961" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="116" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1158.03,879.234) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.57" xml:space="preserve" y="884.01" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136521150466" ObjectName="P"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="117" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1254.03,879.234) scale(1,1) translate(0,0)" writing-mode="lr" x="1253.57" xml:space="preserve" y="884.01" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136522067970" ObjectName="P"/>
   </metadata>
  </g>
  <g id="121">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="121" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,958,879.234) scale(1,1) translate(0,0)" writing-mode="lr" x="957.54" xml:space="preserve" y="884.01" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136774578177" ObjectName="P"/>
   </metadata>
  </g>
  <g id="122">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="122" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1057.25,879.234) scale(1,1) translate(0,0)" writing-mode="lr" x="1056.79" xml:space="preserve" y="884.01" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136775495681" ObjectName="P"/>
   </metadata>
  </g>
  <g id="123">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="123" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1464.28,879.234) scale(1,1) translate(0,0)" writing-mode="lr" x="1463.82" xml:space="preserve" y="884.01" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136529080322" ObjectName="P"/>
   </metadata>
  </g>
  <g id="124">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="124" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1567,879.234) scale(1,1) translate(0,0)" writing-mode="lr" x="1566.54" xml:space="preserve" y="884.01" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136776413185" ObjectName="P"/>
   </metadata>
  </g>
  <g id="125">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="125" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1828,879.234) scale(1,1) translate(0,0)" writing-mode="lr" x="1827.54" xml:space="preserve" y="884.01" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136777330689" ObjectName="P"/>
   </metadata>
  </g>
  <g id="126">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="126" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1158.03,900.234) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.57" xml:space="preserve" y="905.01" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136521216002" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="127">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="127" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1254.03,900.234) scale(1,1) translate(0,0)" writing-mode="lr" x="1253.57" xml:space="preserve" y="905.01" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136522133506" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="132">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="132" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,958,900.234) scale(1,1) translate(0,0)" writing-mode="lr" x="957.54" xml:space="preserve" y="905.01" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136774643713" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="133">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="133" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1057.25,900.234) scale(1,1) translate(0,0)" writing-mode="lr" x="1056.79" xml:space="preserve" y="905.01" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136775561217" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="134">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="134" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1464.28,900.234) scale(1,1) translate(0,0)" writing-mode="lr" x="1463.82" xml:space="preserve" y="905.01" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136529145858" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="135">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="135" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1567,900.234) scale(1,1) translate(0,0)" writing-mode="lr" x="1566.54" xml:space="preserve" y="905.01" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136776478721" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="136" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1828,900.234) scale(1,1) translate(0,0)" writing-mode="lr" x="1827.54" xml:space="preserve" y="905.01" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136777396225" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="137" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1158.03,920.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.57" xml:space="preserve" y="925.03" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136521281538" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="138" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1254.03,920.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1253.57" xml:space="preserve" y="925.03" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136522199042" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="139">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="139" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,958,920.25) scale(1,1) translate(0,0)" writing-mode="lr" x="957.54" xml:space="preserve" y="925.03" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136774709249" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="140" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1057.25,920.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1056.79" xml:space="preserve" y="925.03" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136775626753" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="141" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1464.28,920.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1463.82" xml:space="preserve" y="925.03" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136529211394" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="142" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1567,920.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1566.54" xml:space="preserve" y="925.03" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136776544257" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="144" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1828,920.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1827.54" xml:space="preserve" y="925.03" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136777461761" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1164.53,946.17) scale(1,1) translate(0,0)" writing-mode="lr" x="1163.98" xml:space="preserve" y="952.45" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136521674754" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="150" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1259.53,946.17) scale(1,1) translate(0,0)" writing-mode="lr" x="1258.98" xml:space="preserve" y="952.45" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136522592258" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="158">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1069.75,212.785) scale(1,1) translate(0,0)" writing-mode="lr" x="1069.2" xml:space="preserve" y="219.06" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136528687106" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="159">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,961.25,946.17) scale(1,1) translate(0,0)" writing-mode="lr" x="960.7" xml:space="preserve" y="952.45" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136775102465" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="160">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1052.5,947.17) scale(1,1) translate(0,0)" writing-mode="lr" x="1051.95" xml:space="preserve" y="953.45" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136776019969" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="162">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1469.78,944.17) scale(1,1) translate(0,0)" writing-mode="lr" x="1469.23" xml:space="preserve" y="950.45" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136529604610" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="171">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="171" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1572.5,945.17) scale(1,1) translate(0,0)" writing-mode="lr" x="1571.95" xml:space="preserve" y="951.45" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136776937473" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="172">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1836.5,947.17) scale(1,1) translate(0,0)" writing-mode="lr" x="1835.95" xml:space="preserve" y="953.45" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136777854977" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="284">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="284" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,152.611,240.167) scale(1,1) translate(0,0)" writing-mode="lr" x="152.77" xml:space="preserve" y="245.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136528424962" ObjectName="F"/>
   </metadata>
  </g>
  <g id="279">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="279" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,152.611,168.167) scale(1,1) translate(0,0)" writing-mode="lr" x="152.77" xml:space="preserve" y="173.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136530653186" ObjectName="GEN_PSUM"/>
   </metadata>
  </g>
  <g id="276">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="276" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,330.222,169.167) scale(1,1) translate(0,0)" writing-mode="lr" x="330.38" xml:space="preserve" y="174.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136530718722" ObjectName="GEN_QSUM"/>
   </metadata>
  </g>
  <g id="271">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="271" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,330.222,241.167) scale(1,1) translate(0,0)" writing-mode="lr" x="330.38" xml:space="preserve" y="246.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136519446530" ObjectName="F"/>
   </metadata>
  </g>
  <g id="258">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="258" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,152.611,193.167) scale(1,1) translate(0,0)" writing-mode="lr" x="152.77" xml:space="preserve" y="198.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136530522114" ObjectName="F"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,330.222,194.167) scale(1,1) translate(0,0)" writing-mode="lr" x="330.38" xml:space="preserve" y="199.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136530587650" ObjectName="F"/>
   </metadata>
  </g>
  <g id="247">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="247" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,152.611,217.167) scale(1,1) translate(0,0)" writing-mode="lr" x="152.77" xml:space="preserve" y="222.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127189938181" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,330.222,216.167) scale(1,1) translate(0,0)" writing-mode="lr" x="330.38" xml:space="preserve" y="221.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127189872645" ObjectName="厂用电率"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,298.339,387.75) scale(0.708333,0.665547) translate(118.471,189.837)" width="30" x="287.71" xlink:href="#State:红绿圆(方形)_0" y="377.77" zvalue="448"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374930149377" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,298.339,387.75) scale(0.708333,0.665547) translate(118.471,189.837)" width="30" x="287.71" y="377.77"/></g>
  <g id="3">
   <use height="30" transform="rotate(0,202.714,387.75) scale(0.708333,0.665547) translate(79.0956,189.837)" width="30" x="192.09" xlink:href="#State:红绿圆(方形)_0" y="377.77" zvalue="449"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562962223923201" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,202.714,387.75) scale(0.708333,0.665547) translate(79.0956,189.837)" width="30" x="192.09" y="377.77"/></g>
 </g>
</svg>