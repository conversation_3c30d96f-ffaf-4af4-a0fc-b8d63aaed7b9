<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549591343106" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:硅厂炉变YY_0" viewBox="0,0,17,30">
   <use terminal-index="0" type="0" x="8.666666666666666" xlink:href="#terminal" y="0.1666666666666714"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="8.5" y1="25.25" y2="30.08333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.597025948103791" x2="8.597025948103791" y1="1.79293756914176" y2="0.2382945839350779"/>
   <ellipse cx="8.42" cy="8.35" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.5" cy="18.92" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.611718118722354" x2="8.611718118722354" y1="16.35874008086165" y2="18.95772818087859"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.2052656081938" x2="8.611718118722351" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.018170629250881" x2="8.611718118722335" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.611718118722354" x2="8.611718118722354" y1="5.503560677018264" y2="8.102548777035198"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.2052656081938" x2="8.611718118722351" y1="10.70153687705211" y2="8.102548777035178"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.018170629250881" x2="8.611718118722335" y1="10.70153687705211" y2="8.102548777035178"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:三卷PT带容断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,6.5) scale(1,1) translate(0,0)" width="4" x="13" y="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Disconnector:20210316_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,18.75) scale(1,1) translate(0,0)" width="6" x="3" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="6.66666666666667" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="6.727157046327149" y2="14.02109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="6.734473004260392" y2="14.02109851866368"/>
  </symbol>
  <symbol id="Disconnector:20210316_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,18.75) scale(1,1) translate(0,0)" width="6" x="3" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="0.25" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="0.7271570463271502" y2="8.021098518663681"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="0.7344730042603906" y2="8.021098518663679"/>
  </symbol>
  <symbol id="Disconnector:20210316_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="EnergyConsumer:Y-Y站用_0" viewBox="0,0,17,26">
   <use terminal-index="0" type="0" x="8.666666666666666" xlink:href="#terminal" y="0.1666666666666714"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.597025948103791" x2="8.597025948103791" y1="1.79293756914176" y2="0.2382945839350814"/>
   <ellipse cx="8.42" cy="8.35" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.5" cy="18.92" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.611718118722354" x2="8.611718118722354" y1="16.35874008086165" y2="18.95772818087859"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.2052656081938" x2="8.611718118722351" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.018170629250881" x2="8.611718118722335" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.611718118722354" x2="8.611718118722354" y1="5.503560677018262" y2="8.102548777035198"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.2052656081938" x2="8.611718118722351" y1="10.70153687705211" y2="8.102548777035178"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.018170629250881" x2="8.611718118722335" y1="10.70153687705211" y2="8.102548777035178"/>
  </symbol>
  <symbol id="Compensator:遮放35kV电容_0" viewBox="0,0,60,40">
   <use terminal-index="0" type="0" x="0.6873530692755132" xlink:href="#terminal" y="23.15315325549626"/>
   <path d="M 20.4238 13.4601 L 20.4238 23.2699 L 30.2336 23.2699" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 22.0587 7.73774 L 3.84051 7.73774 L 3.84051 23.1532" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.96429701311517" x2="1.154486884904586" y1="23.15315325549626" y2="23.15315325549626"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.05839172695363" x2="27.25558883317917" y1="7.796129066690407" y2="7.796129066690407"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.45979317384086" x2="31.45979317384086" y1="9.197530513577634" y2="6.394727619803181"/>
   <rect fill-opacity="0" height="12.38" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,20.95,7.8) scale(1,1) translate(0,0)" width="5.61" x="18.15" y="1.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.11711686125934" x2="17.91291252059766" y1="7.796129066690407" y2="9.197530513577634"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.05839172695363" x2="30.05839172695363" y1="9.898231237021246" y2="5.694026896359567"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.86119462072809" x2="32.86119462072809" y1="8.49682979013402" y2="7.095428343246795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38.11645004655519" x2="56.33466885608912" y1="37.4591263591367" y2="37.4591263591367"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.11711686125934" x2="17.91291252059766" y1="7.796129066690407" y2="6.394727619803181"/>
   <rect fill-opacity="0" height="5.61" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,40.34,23.33) scale(1,1) translate(0,0)" width="2.8" x="38.93" y="20.53"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="56.13613698444676" x2="56.13613698444676" y1="23.44511189026444" y2="31.73673711768053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="59.17250678603577" x2="59.17250678603577" y1="12.43243218680899" y2="34.59899781143184"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.90257007663224" x2="59.37103865767813" y1="23.29329340018499" y2="23.29329340018499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="57.26893648734728" x2="59.28929023994304" y1="12.31564873290172" y2="12.31564873290172"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="59.28929023994304" x2="57.38571994125455" y1="34.48221435752456" y2="34.48221435752456"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="53.0219115469196" x2="56.16208886309282" y1="31.75360583880046" y2="31.75360583880046"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.76476491941656" x2="34.76476491941656" y1="31.73673711768053" y2="23.29329340018499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.23356690781454" x2="49.82983047345427" y1="23.29329340018499" y2="23.29329340018499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="37.91791817491283" x2="34.82964461603171" y1="31.70170208150834" y2="31.70170208150834"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="51.23123192034149" x2="56.16208886309283" y1="23.29329340018499" y2="23.29329340018499"/>
   <path d="M 42.9614 31.7017 A 4.15364 2.547 -90 0 1 37.8674 31.7017" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 48.0554 31.7017 A 4.15364 2.547 -90 0 1 42.9614 31.7017" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 53.0092 31.7017 A 4.15364 2.547 -90 0 1 47.9152 31.7017" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="51.23123192034147" x2="51.23123192034147" y1="21.19119122985414" y2="25.39539557051582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="49.82983047345425" x2="49.82983047345425" y1="21.19119122985414" y2="25.39539557051582"/>
   <path d="M 20.5873 13.1682 A 9.58792 9.95831 -360 1 1 10.9993 23.1265" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="4.583333333333332" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV允罕水泥厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="71.94" y="327" zvalue="31"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="1" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" width="72.88" x="71.94" y="327" zvalue="31"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" writing-mode="lr" x="108.38" xml:space="preserve" y="343.5" zvalue="31">信号一览</text>
  <image height="60" id="2" preserveAspectRatio="xMidYMid slice" width="269.25" x="66.95999999999999" xlink:href="logo.png" y="45.64"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.589,75.6429) scale(1,1) translate(0,0)" writing-mode="lr" x="201.59" xml:space="preserve" y="79.14" zvalue="138"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,203.298,75.3332) scale(1,1) translate(9.43293e-15,0)" writing-mode="lr" x="203.3" xml:space="preserve" y="84.33" zvalue="139">35kV允罕水泥厂</text>
  <line fill="none" id="26" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="388.75" x2="388.75" y1="11" y2="1039.25" zvalue="6"/>
  <line fill="none" id="24" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.22692454998003" x2="331.8381846035992" y1="168.8779458827686" y2="168.8779458827686" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="927.8127909390723" y2="927.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="979.1522909390724" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="361.3219285714284" y1="927.8127909390723" y2="927.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="361.3219285714284" y1="979.1522909390724" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3219285714284" x2="361.3219285714284" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5593285714284" x2="279.5593285714284" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="279.5592285714284" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3213285714285" x2="361.3213285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5593285714284" x2="279.5593285714284" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="279.5592285714284" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3213285714285" x2="361.3213285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,208.338,957.386) scale(1,1) translate(0,1.05089e-13)" writing-mode="lr" x="60.68" xml:space="preserve" y="963.39" zvalue="10">参考图号      YunHan-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,132.899,994.755) scale(1,1) translate(-6.25312e-14,-1.52933e-12)" writing-mode="lr" x="70.40000000000001" xml:space="preserve" y="1000.75" zvalue="11">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,276.202,995.755) scale(1,1) translate(1.38222e-13,-1.53089e-12)" writing-mode="lr" x="207.5" xml:space="preserve" y="1001.75" zvalue="12">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.9923,1024.31) scale(1,1) translate(-6.10858e-14,-1.57527e-12)" writing-mode="lr" x="83.98999999999999" xml:space="preserve" y="1030.31" zvalue="13">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,280.836,1022.31) scale(1,1) translate(-4.58902e-14,1.12298e-13)" writing-mode="lr" x="206.67" xml:space="preserve" y="1028.31" zvalue="14">更新日期  </text>
  <line fill="none" id="17" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="54.60611355802337" x2="359.2173736116425" y1="623.6445306693694" y2="623.6445306693694" zvalue="15"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,97.6707,640.686) scale(1,1) translate(5.99964e-15,-1.38109e-13)" writing-mode="lr" x="97.67070806204492" xml:space="preserve" y="645.1863811688671" zvalue="17">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="172.1071428571429" y2="172.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="172.1071428571429" y2="172.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="290.6071428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="290.6071428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" x="139.5390625" xml:space="preserve" y="467.109375" zvalue="20">35kV母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="139.5390625" xml:space="preserve" y="483.109375" zvalue="20">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,502.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="507.1071428571429" zvalue="23">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,528.107) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="532.6071428571429" zvalue="24">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,553.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="558.1071428571429" zvalue="25">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,579.107) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="583.6071428571429" zvalue="26">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,604.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="609.1071428571429" zvalue="27">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.3214,186.107) scale(1,1) translate(0,0)" writing-mode="lr" x="54.32" xml:space="preserve" y="191.61" zvalue="28">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.571,186.107) scale(1,1) translate(0,0)" writing-mode="lr" x="240.57" xml:space="preserve" y="191.61" zvalue="29">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,65.0089,210.357) scale(1,1) translate(0,0)" writing-mode="lr" x="65.01000000000001" xml:space="preserve" y="214.86" zvalue="30">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,208.399,340.5) scale(1,1) translate(0,0)" writing-mode="lr" x="208.4" xml:space="preserve" y="345" zvalue="379">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,296.399,340.5) scale(1,1) translate(0,0)" writing-mode="lr" x="296.4" xml:space="preserve" y="345" zvalue="380">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1123.94,327.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1123.94" xml:space="preserve" y="332.06" zvalue="492">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1030.52,111.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1030.52" xml:space="preserve" y="115.61" zvalue="518">35kV平罕线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" x="852.5703125" xml:space="preserve" y="505.2638864517211" zvalue="774">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="852.5703125" xml:space="preserve" y="521.2638864517212" zvalue="774">3150kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,886.216,395.222) scale(1,1) translate(0,0)" writing-mode="lr" x="886.22" xml:space="preserve" y="399.72" zvalue="776">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,853,436) scale(1,1) translate(0,0)" writing-mode="lr" x="853" xml:space="preserve" y="440.5" zvalue="783">3016</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1005.2,285.889) scale(1,1) translate(-2.1987e-13,0)" writing-mode="lr" x="1005.2" xml:space="preserve" y="290.39" zvalue="799">3511</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1322.54,220.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.54" xml:space="preserve" y="224.61" zvalue="812">PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1605.5,638.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1605.5" xml:space="preserve" y="643.28" zvalue="834">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,885.529,602.838) scale(1,1) translate(9.6929e-14,0)" writing-mode="lr" x="885.53" xml:space="preserve" y="607.34" zvalue="837">001</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1501,523.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1501" xml:space="preserve" y="528" zvalue="840">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1476.52,414) scale(1,1) translate(0,0)" writing-mode="lr" x="1476.52" xml:space="preserve" y="418.5" zvalue="842">3811</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" x="1220.5546875" xml:space="preserve" y="505.2638864517211" zvalue="846">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1220.5546875" xml:space="preserve" y="521.2638864517212" zvalue="846">12500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1254.22,395.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1254.22" xml:space="preserve" y="399.72" zvalue="848">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1221,436) scale(1,1) translate(0,0)" writing-mode="lr" x="1221" xml:space="preserve" y="440.5" zvalue="851">3026</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1254.53,603.838) scale(1,1) translate(0,0)" writing-mode="lr" x="1254.53" xml:space="preserve" y="608.34" zvalue="856">002</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" x="543" xml:space="preserve" y="952.75" zvalue="859">石灰石破碎</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="543" xml:space="preserve" y="968.75" zvalue="859">机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,567.167,735.838) scale(1,1) translate(0,0)" writing-mode="lr" x="567.17" xml:space="preserve" y="740.34" zvalue="862">051</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,631,955.25) scale(1,1) translate(0,0)" writing-mode="lr" x="631" xml:space="preserve" y="959.75" zvalue="873">原料排风机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,655.167,735.838) scale(1,1) translate(0,0)" writing-mode="lr" x="655.17" xml:space="preserve" y="740.34" zvalue="876">052</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,723,955.25) scale(1,1) translate(0,0)" writing-mode="lr" x="723" xml:space="preserve" y="959.75" zvalue="880">原料磨电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,747.167,735.838) scale(1,1) translate(0,0)" writing-mode="lr" x="747.17" xml:space="preserve" y="740.34" zvalue="883">053</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,815,955.25) scale(1,1) translate(0,0)" writing-mode="lr" x="815" xml:space="preserve" y="959.75" zvalue="887">窑尾排风机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,839.167,735.838) scale(1,1) translate(-5.51004e-13,0)" writing-mode="lr" x="839.17" xml:space="preserve" y="740.34" zvalue="890">054</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,907,955.25) scale(1,1) translate(0,0)" writing-mode="lr" x="907" xml:space="preserve" y="959.75" zvalue="894">高温风机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,931.167,735.838) scale(1,1) translate(0,0)" writing-mode="lr" x="931.17" xml:space="preserve" y="740.34" zvalue="897">055</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1003,955.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1003" xml:space="preserve" y="959.75" zvalue="901">窑头排风机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1027.17,735.838) scale(1,1) translate(0,0)" writing-mode="lr" x="1027.17" xml:space="preserve" y="740.34" zvalue="904">056</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1099,955.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1099" xml:space="preserve" y="959.75" zvalue="908">煤磨电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1123.17,735.838) scale(1,1) translate(0,0)" writing-mode="lr" x="1123.17" xml:space="preserve" y="740.34" zvalue="911">057</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="175" stroke="rgb(255,255,255)" text-anchor="middle" x="1192.6953125" xml:space="preserve" y="952.75" zvalue="915">摇尾变压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="175" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1192.6953125" xml:space="preserve" y="968.75" zvalue="915">器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1215.17,735.838) scale(1,1) translate(0,0)" writing-mode="lr" x="1215.17" xml:space="preserve" y="740.34" zvalue="918">061</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1280.72,955.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1280.71696244856" xml:space="preserve" y="959.75" zvalue="922">摇头变压器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1303.17,735.838) scale(1,1) translate(0,0)" writing-mode="lr" x="1303.17" xml:space="preserve" y="740.34" zvalue="925">062</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1376.72,955.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1376.71696244856" xml:space="preserve" y="959.75" zvalue="929">煤磨变压器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="186" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1399.17,735.838) scale(1,1) translate(0,0)" writing-mode="lr" x="1399.17" xml:space="preserve" y="740.34" zvalue="932">063</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" x="1472.703125" xml:space="preserve" y="952.75" zvalue="936">原料磨变压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1472.703125" xml:space="preserve" y="968.75" zvalue="936">器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="192" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1495.17,735.838) scale(1,1) translate(0,0)" writing-mode="lr" x="1495.17" xml:space="preserve" y="740.34" zvalue="939">064</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" x="1568" xml:space="preserve" y="952.75" zvalue="943">#1水泥窑变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1568" xml:space="preserve" y="968.75" zvalue="943">压器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1591.17,735.838) scale(1,1) translate(0,0)" writing-mode="lr" x="1591.17" xml:space="preserve" y="740.34" zvalue="946">066</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="205" stroke="rgb(255,255,255)" text-anchor="middle" x="1671.203125" xml:space="preserve" y="951.75" zvalue="950">#1无功补偿</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="205" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1671.203125" xml:space="preserve" y="967.75" zvalue="950">装置</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1687.17,735.838) scale(1,1) translate(0,0)" writing-mode="lr" x="1687.17" xml:space="preserve" y="740.34" zvalue="953">067</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1689.74,809.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1689.74" xml:space="preserve" y="813.72" zvalue="957">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="215" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1633,795) scale(1,1) translate(0,0)" writing-mode="lr" x="1633" xml:space="preserve" y="799.5" zvalue="961">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="220" stroke="rgb(255,255,255)" text-anchor="middle" x="1758.84375" xml:space="preserve" y="952.75" zvalue="967">#2水泥磨变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="220" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1758.84375" xml:space="preserve" y="968.75" zvalue="967">压器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="219" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1779.17,735.838) scale(1,1) translate(0,0)" writing-mode="lr" x="1779.17" xml:space="preserve" y="740.34" zvalue="970">065</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="230" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,644.286,610) scale(1,1) translate(0,0)" writing-mode="lr" x="644.29" xml:space="preserve" y="614.5" zvalue="977">0901</text>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="34">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="34" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,161.571,210.968) scale(1,1) translate(0,0)" writing-mode="lr" x="161.72" xml:space="preserve" y="217.4" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="33" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,161.571,186) scale(1,1) translate(0,0)" writing-mode="lr" x="161.72" xml:space="preserve" y="192.43" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126352060420" ObjectName=""/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="35" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,343.571,185.857) scale(1,1) translate(0,0)" writing-mode="lr" x="343.72" xml:space="preserve" y="192.29" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126352125956" ObjectName=""/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,146.071,528.593) scale(1,1) translate(0,0)" writing-mode="lr" x="146.2" xml:space="preserve" y="533.5" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="143" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,146.071,553.718) scale(1,1) translate(0,-1.20619e-13)" writing-mode="lr" x="146.2" xml:space="preserve" y="558.63" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,147.071,577.843) scale(1,1) translate(0,0)" writing-mode="lr" x="147.2" xml:space="preserve" y="582.75" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,147.5,503.468) scale(1,1) translate(0,1.09461e-13)" writing-mode="lr" x="147.63" xml:space="preserve" y="508.38" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="148" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,146.321,602.968) scale(1,1) translate(0,0)" writing-mode="lr" x="146.45" xml:space="preserve" y="607.88" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="132">
   <use height="30" transform="rotate(0,329.768,340.5) scale(0.708333,0.665547) translate(131.412,166.093)" width="30" x="319.14" xlink:href="#State:红绿圆(方形)_0" y="330.52" zvalue="381"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.768,340.5) scale(0.708333,0.665547) translate(131.412,166.093)" width="30" x="319.14" y="330.52"/></g>
  <g id="94">
   <use height="30" transform="rotate(0,247.143,340.5) scale(0.708333,0.665547) translate(97.3897,166.093)" width="30" x="236.52" xlink:href="#State:红绿圆(方形)_0" y="330.52" zvalue="382"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,247.143,340.5) scale(0.708333,0.665547) translate(97.3897,166.093)" width="30" x="236.52" y="330.52"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="27">
   <path class="kv35" d="M 784 350.78 L 1555.11 350.78" stroke-width="6" zvalue="491"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674251833348" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674251833348"/></metadata>
  <path d="M 784 350.78 L 1555.11 350.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv10" d="M 498 655.78 L 1809 655.78" stroke-width="6" zvalue="833"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674251898884" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674251898884"/></metadata>
  <path d="M 498 655.78 L 1809 655.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="85">
   <g id="850">
    <use class="kv35" height="60" transform="rotate(0,919.331,506.111) scale(1.54084,1.52963) translate(-311.873,-159.351)" width="40" x="888.51" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="460.22" zvalue="773"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874450599939" ObjectName="35"/>
    </metadata>
   </g>
   <g id="851">
    <use class="kv10" height="60" transform="rotate(0,919.331,506.111) scale(1.54084,1.52963) translate(-311.873,-159.351)" width="40" x="888.51" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="460.22" zvalue="773"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874450665475" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399457243139" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399457243139"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,919.331,506.111) scale(1.54084,1.52963) translate(-311.873,-159.351)" width="40" x="888.51" y="460.22"/></g>
  <g id="116">
   <g id="1160">
    <use class="kv35" height="60" transform="rotate(0,1287.33,506.111) scale(1.54084,1.52963) translate(-441.043,-159.351)" width="40" x="1256.51" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="460.22" zvalue="845"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874450731011" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1161">
    <use class="kv10" height="60" transform="rotate(0,1287.33,506.111) scale(1.54084,1.52963) translate(-441.043,-159.351)" width="40" x="1256.51" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="460.22" zvalue="845"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874450796547" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399457308675" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399457308675"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1287.33,506.111) scale(1.54084,1.52963) translate(-441.043,-159.351)" width="40" x="1256.51" y="460.22"/></g>
 </g>
 <g id="BreakerClass">
  <g id="84">
   <use class="kv35" height="20" transform="rotate(0,919.392,397.838) scale(2.13344,2.08378) translate(-482.782,-196.079)" width="10" x="908.7242919389978" xlink:href="#Breaker:小车断路器_0" y="377.0000000000001" zvalue="775"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924546854917" ObjectName="#1主变301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924546854917"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,919.392,397.838) scale(2.13344,2.08378) translate(-482.782,-196.079)" width="10" x="908.7242919389978" y="377.0000000000001"/></g>
  <g id="52">
   <use class="kv10" height="20" transform="rotate(0,919.392,606.838) scale(2.13344,2.08378) translate(-482.782,-304.781)" width="10" x="908.7242919389978" xlink:href="#Breaker:小车断路器_0" y="586" zvalue="836"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924546920452" ObjectName="#1主变001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924546920452"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,919.392,606.838) scale(2.13344,2.08378) translate(-482.782,-304.781)" width="10" x="908.7242919389978" y="586"/></g>
  <g id="115">
   <use class="kv35" height="20" transform="rotate(0,1287.39,397.838) scale(2.13344,2.08378) translate(-678.29,-196.079)" width="10" x="1276.724291938998" xlink:href="#Breaker:小车断路器_0" y="377.0000000000001" zvalue="847"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924547051525" ObjectName="#2主变302断路器"/>
   <cge:TPSR_Ref TObjectID="6473924547051525"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1287.39,397.838) scale(2.13344,2.08378) translate(-678.29,-196.079)" width="10" x="1276.724291938998" y="377.0000000000001"/></g>
  <g id="110">
   <use class="kv10" height="20" transform="rotate(0,1288.39,607.838) scale(2.13344,2.08378) translate(-678.822,-305.301)" width="10" x="1277.724291938998" xlink:href="#Breaker:小车断路器_0" y="587" zvalue="854"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924546985989" ObjectName="#2主变002断路器"/>
   <cge:TPSR_Ref TObjectID="6473924546985989"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1288.39,607.838) scale(2.13344,2.08378) translate(-678.822,-305.301)" width="10" x="1277.724291938998" y="587"/></g>
  <g id="120">
   <use class="kv10" height="20" transform="rotate(0,544,736.838) scale(2.13344,2.08378) translate(-283.346,-372.394)" width="10" x="533.332788671024" xlink:href="#Breaker:小车断路器_0" y="716" zvalue="861"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924547117061" ObjectName="石灰石破碎机051断路器"/>
   <cge:TPSR_Ref TObjectID="6473924547117061"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,544,736.838) scale(2.13344,2.08378) translate(-283.346,-372.394)" width="10" x="533.332788671024" y="716"/></g>
  <g id="135">
   <use class="kv10" height="20" transform="rotate(0,632,736.838) scale(2.13344,2.08378) translate(-330.098,-372.394)" width="10" x="621.332788671024" xlink:href="#Breaker:小车断路器_0" y="716" zvalue="874"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924547182597" ObjectName="原料排风机052断路器"/>
   <cge:TPSR_Ref TObjectID="6473924547182597"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,632,736.838) scale(2.13344,2.08378) translate(-330.098,-372.394)" width="10" x="621.332788671024" y="716"/></g>
  <g id="145">
   <use class="kv10" height="20" transform="rotate(0,724,736.838) scale(2.13344,2.08378) translate(-378.975,-372.394)" width="10" x="713.332788671024" xlink:href="#Breaker:小车断路器_0" y="716" zvalue="881"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924547248133" ObjectName="原料磨电机053断路器"/>
   <cge:TPSR_Ref TObjectID="6473924547248133"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,724,736.838) scale(2.13344,2.08378) translate(-378.975,-372.394)" width="10" x="713.332788671024" y="716"/></g>
  <g id="152">
   <use class="kv10" height="20" transform="rotate(0,816,736.838) scale(2.13344,2.08378) translate(-427.852,-372.394)" width="10" x="805.332788671024" xlink:href="#Breaker:小车断路器_0" y="716" zvalue="888"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924547313668" ObjectName="窑尾排风机054断路器"/>
   <cge:TPSR_Ref TObjectID="6473924547313668"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,816,736.838) scale(2.13344,2.08378) translate(-427.852,-372.394)" width="10" x="805.332788671024" y="716"/></g>
  <g id="159">
   <use class="kv10" height="20" transform="rotate(0,908,736.838) scale(2.13344,2.08378) translate(-476.73,-372.394)" width="10" x="897.332788671024" xlink:href="#Breaker:小车断路器_0" y="716" zvalue="895"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924547379204" ObjectName="高温风机055断路器"/>
   <cge:TPSR_Ref TObjectID="6473924547379204"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,908,736.838) scale(2.13344,2.08378) translate(-476.73,-372.394)" width="10" x="897.332788671024" y="716"/></g>
  <g id="165">
   <use class="kv10" height="20" transform="rotate(0,1004,736.838) scale(2.13344,2.08378) translate(-527.732,-372.394)" width="10" x="993.332788671024" xlink:href="#Breaker:小车断路器_0" y="716" zvalue="902"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924547444740" ObjectName="窑头排风机056断路器"/>
   <cge:TPSR_Ref TObjectID="6473924547444740"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1004,736.838) scale(2.13344,2.08378) translate(-527.732,-372.394)" width="10" x="993.332788671024" y="716"/></g>
  <g id="172">
   <use class="kv10" height="20" transform="rotate(0,1100,736.838) scale(2.13344,2.08378) translate(-578.734,-372.394)" width="10" x="1089.332788671024" xlink:href="#Breaker:小车断路器_0" y="716" zvalue="909"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924547510276" ObjectName="煤磨电机057断路器"/>
   <cge:TPSR_Ref TObjectID="6473924547510276"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1100,736.838) scale(2.13344,2.08378) translate(-578.734,-372.394)" width="10" x="1089.332788671024" y="716"/></g>
  <g id="178">
   <use class="kv10" height="20" transform="rotate(0,1192,736.838) scale(2.13344,2.08378) translate(-627.611,-372.394)" width="10" x="1181.332788671024" xlink:href="#Breaker:小车断路器_0" y="716" zvalue="916"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924547575812" ObjectName="摇尾变压器061断路器"/>
   <cge:TPSR_Ref TObjectID="6473924547575812"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1192,736.838) scale(2.13344,2.08378) translate(-627.611,-372.394)" width="10" x="1181.332788671024" y="716"/></g>
  <g id="184">
   <use class="kv10" height="20" transform="rotate(0,1280,736.838) scale(2.13344,2.08378) translate(-674.363,-372.394)" width="10" x="1269.332788671024" xlink:href="#Breaker:小车断路器_0" y="716" zvalue="923"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924547641348" ObjectName="摇头变压器062断路器"/>
   <cge:TPSR_Ref TObjectID="6473924547641348"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1280,736.838) scale(2.13344,2.08378) translate(-674.363,-372.394)" width="10" x="1269.332788671024" y="716"/></g>
  <g id="190">
   <use class="kv10" height="20" transform="rotate(0,1376,736.838) scale(2.13344,2.08378) translate(-725.366,-372.394)" width="10" x="1365.332788671024" xlink:href="#Breaker:小车断路器_0" y="716" zvalue="930"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924547706884" ObjectName="煤磨变压器063断路器"/>
   <cge:TPSR_Ref TObjectID="6473924547706884"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1376,736.838) scale(2.13344,2.08378) translate(-725.366,-372.394)" width="10" x="1365.332788671024" y="716"/></g>
  <g id="196">
   <use class="kv10" height="20" transform="rotate(0,1472,736.838) scale(2.13344,2.08378) translate(-776.368,-372.394)" width="10" x="1461.332788671024" xlink:href="#Breaker:小车断路器_0" y="716" zvalue="937"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924547772420" ObjectName="原料磨变压器064断路器"/>
   <cge:TPSR_Ref TObjectID="6473924547772420"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1472,736.838) scale(2.13344,2.08378) translate(-776.368,-372.394)" width="10" x="1461.332788671024" y="716"/></g>
  <g id="202">
   <use class="kv10" height="20" transform="rotate(0,1568,736.838) scale(2.13344,2.08378) translate(-827.37,-372.394)" width="10" x="1557.332788671024" xlink:href="#Breaker:小车断路器_0" y="716" zvalue="944"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924547837956" ObjectName="#1水泥窑变压器066断路器"/>
   <cge:TPSR_Ref TObjectID="6473924547837956"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1568,736.838) scale(2.13344,2.08378) translate(-827.37,-372.394)" width="10" x="1557.332788671024" y="716"/></g>
  <g id="208">
   <use class="kv10" height="20" transform="rotate(0,1664,736.838) scale(2.13344,2.08378) translate(-878.373,-372.394)" width="10" x="1653.332788671024" xlink:href="#Breaker:小车断路器_0" y="716" zvalue="951"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924547903492" ObjectName="#1无功补偿装置067断路器"/>
   <cge:TPSR_Ref TObjectID="6473924547903492"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1664,736.838) scale(2.13344,2.08378) translate(-878.373,-372.394)" width="10" x="1653.332788671024" y="716"/></g>
  <g id="223">
   <use class="kv10" height="20" transform="rotate(0,1756,736.838) scale(2.13344,2.08378) translate(-927.25,-372.394)" width="10" x="1745.332788671024" xlink:href="#Breaker:小车断路器_0" y="716" zvalue="968"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924547969028" ObjectName="#2水泥磨变压器065断路器"/>
   <cge:TPSR_Ref TObjectID="6473924547969028"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1756,736.838) scale(2.13344,2.08378) translate(-927.25,-372.394)" width="10" x="1745.332788671024" y="716"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="83">
   <path class="kv35" d="M 919.39 461.05 L 919.39 416.59" stroke-width="1" zvalue="777"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@0" LinkObjectIDznd="84@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.39 461.05 L 919.39 416.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv35" d="M 1030.68 177.08 L 1030.64 275.07" stroke-width="1" zvalue="803"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="86@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1030.68 177.08 L 1030.64 275.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv35" d="M 1030.61 298.9 L 1030.61 350.78" stroke-width="1" zvalue="804"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@1" LinkObjectIDznd="27@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1030.61 298.9 L 1030.61 350.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv35" d="M 1322.64 321.04 L 1322.64 350.78" stroke-width="1" zvalue="818"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="27@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.64 321.04 L 1322.64 350.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv35" d="M 1322.64 285.75 L 1322.61 297.21" stroke-width="1" zvalue="825"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="104@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.64 285.75 L 1322.61 297.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv35" d="M 1322.64 332 L 1348.06 332 L 1348.09 322.75" stroke-width="1" zvalue="827"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101" LinkObjectIDznd="29@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.64 332 L 1348.06 332 L 1348.09 322.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv35" d="M 1348.06 289.75 L 1348.06 301.3" stroke-width="1" zvalue="829"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@0" LinkObjectIDznd="29@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1348.06 289.75 L 1348.06 301.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv35" d="M 919.39 378.56 L 919.39 350.78" stroke-width="1" zvalue="830"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@0" LinkObjectIDznd="27@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.39 378.56 L 919.39 350.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv35" d="M 898.75 435.05 L 919.39 435.05" stroke-width="1" zvalue="831"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@0" LinkObjectIDznd="83" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.75 435.05 L 919.39 435.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv10" d="M 919.33 551.35 L 919.39 587.56" stroke-width="1" zvalue="837"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@1" LinkObjectIDznd="52@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.33 551.35 L 919.39 587.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv35" d="M 1502.68 452.31 L 1502.68 430.95" stroke-width="1" zvalue="842"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@0" LinkObjectIDznd="67@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1502.68 452.31 L 1502.68 430.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv35" d="M 1502.56 394.11 L 1502.56 350.78" stroke-width="1" zvalue="843"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@1" LinkObjectIDznd="27@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1502.56 394.11 L 1502.56 350.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv35" d="M 1287.39 461.05 L 1287.39 416.59" stroke-width="1" zvalue="849"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="115@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1287.39 461.05 L 1287.39 416.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv35" d="M 1287.39 378.56 L 1287.39 350.78" stroke-width="1" zvalue="852"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="27@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1287.39 378.56 L 1287.39 350.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv35" d="M 1266.75 435.05 L 1287.39 435.05" stroke-width="1" zvalue="853"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="114" MaxPinNum="2"/>
   </metadata>
  <path d="M 1266.75 435.05 L 1287.39 435.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv10" d="M 1287.33 551.35 L 1287.33 588.56" stroke-width="1" zvalue="855"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@1" LinkObjectIDznd="110@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1287.33 551.35 L 1287.33 588.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv10" d="M 1288.39 626.59 L 1288.39 655.78" stroke-width="1" zvalue="857"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@1" LinkObjectIDznd="45@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1288.39 626.59 L 1288.39 655.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv10" d="M 544 894.35 L 544 755.59" stroke-width="1" zvalue="862"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@0" LinkObjectIDznd="120@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 544 894.35 L 544 755.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv10" d="M 544 717.56 L 544 655.78" stroke-width="1" zvalue="863"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="45@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 544 717.56 L 544 655.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="kv10" d="M 632 894.35 L 632 755.59" stroke-width="1" zvalue="875"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="136@0" LinkObjectIDznd="135@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 632 894.35 L 632 755.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv10" d="M 632 717.56 L 632 655.78" stroke-width="1" zvalue="877"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@0" LinkObjectIDznd="45@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 632 717.56 L 632 655.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="kv10" d="M 724 894.35 L 724 755.59" stroke-width="1" zvalue="882"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="145@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 724 894.35 L 724 755.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv10" d="M 724 717.56 L 724 655.78" stroke-width="1" zvalue="884"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="45@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 724 717.56 L 724 655.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv10" d="M 816 894.35 L 816 755.59" stroke-width="1" zvalue="889"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@0" LinkObjectIDznd="152@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 816 894.35 L 816 755.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv10" d="M 816 717.56 L 816 655.78" stroke-width="1" zvalue="891"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="45@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 816 717.56 L 816 655.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv10" d="M 908 894.35 L 908 755.59" stroke-width="1" zvalue="896"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160@0" LinkObjectIDznd="159@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 908 894.35 L 908 755.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv10" d="M 908 717.56 L 908 655.78" stroke-width="1" zvalue="898"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="45@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 908 717.56 L 908 655.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv10" d="M 1004 894.35 L 1004 755.59" stroke-width="1" zvalue="903"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="165@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004 894.35 L 1004 755.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv10" d="M 1004 717.56 L 1004 655.78" stroke-width="1" zvalue="905"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="45@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004 717.56 L 1004 655.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv10" d="M 1100 894.35 L 1100 755.59" stroke-width="1" zvalue="910"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="173@0" LinkObjectIDznd="172@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100 894.35 L 1100 755.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv10" d="M 1100 717.56 L 1100 655.78" stroke-width="1" zvalue="912"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@0" LinkObjectIDznd="45@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1100 717.56 L 1100 655.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv10" d="M 1192.42 861.31 L 1192.42 755.59" stroke-width="1" zvalue="917"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@0" LinkObjectIDznd="178@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1192.42 861.31 L 1192.42 755.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="kv10" d="M 1192 717.56 L 1192 655.78" stroke-width="1" zvalue="919"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="45@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1192 717.56 L 1192 655.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv10" d="M 1280.42 861.31 L 1280.42 755.59" stroke-width="1" zvalue="924"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@0" LinkObjectIDznd="184@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1280.42 861.31 L 1280.42 755.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv10" d="M 1280 717.56 L 1280 655.78" stroke-width="1" zvalue="926"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@0" LinkObjectIDznd="45@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1280 717.56 L 1280 655.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv10" d="M 1376.42 861.31 L 1376.42 755.59" stroke-width="1" zvalue="931"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="190@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1376.42 861.31 L 1376.42 755.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv10" d="M 1376 717.56 L 1376 655.78" stroke-width="1" zvalue="933"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@0" LinkObjectIDznd="45@10" MaxPinNum="2"/>
   </metadata>
  <path d="M 1376 717.56 L 1376 655.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv10" d="M 1472.42 861.31 L 1472.42 755.59" stroke-width="1" zvalue="938"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="196@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1472.42 861.31 L 1472.42 755.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv10" d="M 1472 717.56 L 1472 655.78" stroke-width="1" zvalue="940"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="45@11" MaxPinNum="2"/>
   </metadata>
  <path d="M 1472 717.56 L 1472 655.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv10" d="M 1568 894.35 L 1568 755.59" stroke-width="1" zvalue="945"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@0" LinkObjectIDznd="202@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1568 894.35 L 1568 755.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="200">
   <path class="kv10" d="M 1568 717.56 L 1568 655.78" stroke-width="1" zvalue="947"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="45@12" MaxPinNum="2"/>
   </metadata>
  <path d="M 1568 717.56 L 1568 655.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="206">
   <path class="kv10" d="M 1664 717.56 L 1664 655.78" stroke-width="1" zvalue="954"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@0" LinkObjectIDznd="45@13" MaxPinNum="2"/>
   </metadata>
  <path d="M 1664 717.56 L 1664 655.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="kv10" d="M 1664 868.2 L 1664 822.04" stroke-width="1" zvalue="957"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="209@0" LinkObjectIDznd="210@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1664 868.2 L 1664 822.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv10" d="M 1663.97 798.21 L 1664 755.59" stroke-width="1" zvalue="958"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="210@1" LinkObjectIDznd="208@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1663.97 798.21 L 1664 755.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv10" d="M 1642.75 776.05 L 1663.99 776.05" stroke-width="1" zvalue="961"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@0" LinkObjectIDznd="213" MaxPinNum="2"/>
   </metadata>
  <path d="M 1642.75 776.05 L 1663.99 776.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="218">
   <path class="kv10" d="M 1654.81 843 L 1664 843" stroke-width="1" zvalue="964"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217@0" LinkObjectIDznd="212" MaxPinNum="2"/>
   </metadata>
  <path d="M 1654.81 843 L 1664 843" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="kv10" d="M 1756.42 861.31 L 1756.42 755.59" stroke-width="1" zvalue="969"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="224@0" LinkObjectIDznd="223@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1756.42 861.31 L 1756.42 755.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="kv10" d="M 1756 717.56 L 1756 655.78" stroke-width="1" zvalue="971"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@0" LinkObjectIDznd="45@14" MaxPinNum="2"/>
   </metadata>
  <path d="M 1756 717.56 L 1756 655.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv10" d="M 919.39 625.59 L 919.39 655.78" stroke-width="1" zvalue="973"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@1" LinkObjectIDznd="45@15" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.39 625.59 L 919.39 655.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="kv10" d="M 675.17 577.77 L 675.17 596.09" stroke-width="1" zvalue="977"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="227@0" LinkObjectIDznd="229@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 675.17 577.77 L 675.17 596.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="kv10" d="M 675.27 627.96 L 675.27 655.78" stroke-width="1" zvalue="978"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@0" LinkObjectIDznd="45@16" MaxPinNum="2"/>
   </metadata>
  <path d="M 675.27 627.96 L 675.27 655.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="10">
   <use class="kv35" height="20" transform="rotate(90,889,435) scale(1,1) translate(0,0)" width="10" x="884" xlink:href="#GroundDisconnector:地刀_0" y="425" zvalue="782"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449972535301" ObjectName="#1主变3016接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449972535301"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,889,435) scale(1,1) translate(0,0)" width="10" x="884" y="425"/></g>
  <g id="113">
   <use class="kv35" height="20" transform="rotate(90,1257,435) scale(1,1) translate(0,0)" width="10" x="1252" xlink:href="#GroundDisconnector:地刀_0" y="425" zvalue="850"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449973125126" ObjectName="#2主变3026接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449973125126"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1257,435) scale(1,1) translate(0,0)" width="10" x="1252" y="425"/></g>
  <g id="214">
   <use class="kv10" height="20" transform="rotate(90,1633,776) scale(1,1) translate(0,0)" width="10" x="1628" xlink:href="#GroundDisconnector:地刀_0" y="766.0000305175781" zvalue="960"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449974173701" ObjectName="#1无功补偿装置06767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449974173701"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1633,776) scale(1,1) translate(0,0)" width="10" x="1628" y="766.0000305175781"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="86">
   <use class="kv35" height="30" transform="rotate(0,1030.54,286.889) scale(1.11111,0.814815) translate(-102.22,62.4242)" width="15" x="1022.204261750994" xlink:href="#Disconnector:刀闸_0" y="274.6666660308838" zvalue="798"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449972600837" ObjectName="35kV平罕线3511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449972600837"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1030.54,286.889) scale(1.11111,0.814815) translate(-102.22,62.4242)" width="15" x="1022.204261750994" y="274.6666660308838"/></g>
  <g id="104">
   <use class="kv35" height="30" transform="rotate(0,1322.54,309.222) scale(1.11111,-0.814815) translate(-131.42,-691.5)" width="15" x="1314.204261750994" xlink:href="#Disconnector:刀闸_0" y="297" zvalue="813"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449972666373" ObjectName="PT隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449972666373"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1322.54,309.222) scale(1.11111,-0.814815) translate(-131.42,-691.5)" width="15" x="1314.204261750994" y="297"/></g>
  <g id="29">
   <use class="kv35" height="30" transform="rotate(0,1348,312.111) scale(1,-0.733333) translate(0,-741.717)" width="15" x="1340.5" xlink:href="#Disconnector:刀闸_0" y="301.1111104753282" zvalue="826"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449972797445" ObjectName="PT避雷器刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449972797445"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1348,312.111) scale(1,-0.733333) translate(0,-741.717)" width="15" x="1340.5" y="301.1111104753282"/></g>
  <g id="67">
   <use class="kv35" height="26" transform="rotate(0,1502.56,412.5) scale(1.42308,1.42308) translate(-444.168,-117.135)" width="12" x="1494.019682112975" xlink:href="#Disconnector:20210316_0" y="394" zvalue="841"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449972994053" ObjectName="#1站用变3811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449972994053"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1502.56,412.5) scale(1.42308,1.42308) translate(-444.168,-117.135)" width="12" x="1494.019682112975" y="394"/></g>
  <g id="210">
   <use class="kv10" height="30" transform="rotate(0,1663.9,810.222) scale(1.11111,-0.814815) translate(-165.557,-1807.36)" width="15" x="1655.569136610155" xlink:href="#Disconnector:刀闸_0" y="798" zvalue="956"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449974042629" ObjectName="#1无功补偿装置0676隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449974042629"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1663.9,810.222) scale(1.11111,-0.814815) translate(-165.557,-1807.36)" width="15" x="1655.569136610155" y="798"/></g>
  <g id="229">
   <use class="kv10" height="26" transform="rotate(0,675.17,612) scale(1.23077,1.23077) translate(-125.21,-111.75)" width="12" x="667.78563052655" xlink:href="#Disconnector:小车隔刀熔断器_0" y="596" zvalue="976"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449974435845" ObjectName="10kV母线PT0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449974435845"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,675.17,612) scale(1.23077,1.23077) translate(-125.21,-111.75)" width="12" x="667.78563052655" y="596"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="105">
   <use class="kv35" height="30" transform="rotate(0,1322.71,261.141) scale(1.45556,-1.67949) translate(-407.144,-406.437)" width="30" x="1300.874569585283" xlink:href="#Accessory:三卷PT带容断器_0" y="235.9487075805666" zvalue="811"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449972731910" ObjectName="PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1322.71,261.141) scale(1.45556,-1.67949) translate(-407.144,-406.437)" width="30" x="1300.874569585283" y="235.9487075805666"/></g>
  <g id="39">
   <use class="kv35" height="20" transform="rotate(0,1348.06,274) scale(1.45,-1.8) translate(-413.864,-418.222)" width="20" x="1333.561164424106" xlink:href="#Accessory:线路PT3_0" y="256" zvalue="828"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449972862982" ObjectName="35kV母线PT避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1348.06,274) scale(1.45,-1.8) translate(-413.864,-418.222)" width="20" x="1333.561164424106" y="256"/></g>
  <g id="217">
   <use class="kv10" height="20" transform="rotate(270,1639.06,843) scale(1.45,-1.8) translate(-504.174,-1303.33)" width="20" x="1624.561164424106" xlink:href="#Accessory:线路PT3_0" y="825" zvalue="963"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449974239237" ObjectName="#1无功补偿装置避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1639.06,843) scale(1.45,-1.8) translate(-504.174,-1303.33)" width="20" x="1624.561164424106" y="825"/></g>
  <g id="227">
   <use class="kv10" height="18" transform="rotate(0,677.25,559.1) scale(2.23333,-2.23333) translate(-364.754,-798.343)" width="15" x="660.5" xlink:href="#Accessory:PT8_0" y="539" zvalue="974"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449974370309" ObjectName="10kV母线PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,677.25,559.1) scale(2.23333,-2.23333) translate(-364.754,-798.343)" width="15" x="660.5" y="539"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="64">
   <use class="kv35" height="30" transform="rotate(0,1502.37,480) scale(1.86667,1.86667) translate(-690.161,-209.857)" width="17" x="1486.5" xlink:href="#EnergyConsumer:硅厂炉变YY_0" y="452" zvalue="839"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449972928517" ObjectName="#1站用变"/>
   <cge:TPSR_Ref TObjectID="6192449972928517"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1502.37,480) scale(1.86667,1.86667) translate(-690.161,-209.857)" width="17" x="1486.5" y="452"/></g>
  <g id="117">
   <use class="kv10" height="30" transform="rotate(0,544,911) scale(1.25,-1.23333) translate(-107.3,-1646.15)" width="12" x="536.5" xlink:href="#EnergyConsumer:负荷_0" y="892.5" zvalue="858"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449973190661" ObjectName="石灰石破碎机"/>
   <cge:TPSR_Ref TObjectID="6192449973190661"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,544,911) scale(1.25,-1.23333) translate(-107.3,-1646.15)" width="12" x="536.5" y="892.5"/></g>
  <g id="136">
   <use class="kv10" height="30" transform="rotate(0,632,911) scale(1.25,-1.23333) translate(-124.9,-1646.15)" width="12" x="624.5" xlink:href="#EnergyConsumer:负荷_0" y="892.5" zvalue="872"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449973256197" ObjectName="原料排风机"/>
   <cge:TPSR_Ref TObjectID="6192449973256197"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,632,911) scale(1.25,-1.23333) translate(-124.9,-1646.15)" width="12" x="624.5" y="892.5"/></g>
  <g id="146">
   <use class="kv10" height="30" transform="rotate(0,724,911) scale(1.25,-1.23333) translate(-143.3,-1646.15)" width="12" x="716.5" xlink:href="#EnergyConsumer:负荷_0" y="892.5" zvalue="879"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449973321733" ObjectName="原料磨电机"/>
   <cge:TPSR_Ref TObjectID="6192449973321733"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,724,911) scale(1.25,-1.23333) translate(-143.3,-1646.15)" width="12" x="716.5" y="892.5"/></g>
  <g id="154">
   <use class="kv10" height="30" transform="rotate(0,816,911) scale(1.25,-1.23333) translate(-161.7,-1646.15)" width="12" x="808.5" xlink:href="#EnergyConsumer:负荷_0" y="892.5" zvalue="886"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449973387269" ObjectName="窑尾排风机"/>
   <cge:TPSR_Ref TObjectID="6192449973387269"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,816,911) scale(1.25,-1.23333) translate(-161.7,-1646.15)" width="12" x="808.5" y="892.5"/></g>
  <g id="160">
   <use class="kv10" height="30" transform="rotate(0,908,911) scale(1.25,-1.23333) translate(-180.1,-1646.15)" width="12" x="900.5" xlink:href="#EnergyConsumer:负荷_0" y="892.5" zvalue="893"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449973452805" ObjectName="高温风机"/>
   <cge:TPSR_Ref TObjectID="6192449973452805"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,908,911) scale(1.25,-1.23333) translate(-180.1,-1646.15)" width="12" x="900.5" y="892.5"/></g>
  <g id="166">
   <use class="kv10" height="30" transform="rotate(0,1004,911) scale(1.25,-1.23333) translate(-199.3,-1646.15)" width="12" x="996.5" xlink:href="#EnergyConsumer:负荷_0" y="892.5" zvalue="900"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449973518341" ObjectName="窑头排风机"/>
   <cge:TPSR_Ref TObjectID="6192449973518341"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1004,911) scale(1.25,-1.23333) translate(-199.3,-1646.15)" width="12" x="996.5" y="892.5"/></g>
  <g id="173">
   <use class="kv10" height="30" transform="rotate(0,1100,911) scale(1.25,-1.23333) translate(-218.5,-1646.15)" width="12" x="1092.5" xlink:href="#EnergyConsumer:负荷_0" y="892.5" zvalue="907"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449973583877" ObjectName="煤磨电机"/>
   <cge:TPSR_Ref TObjectID="6192449973583877"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1100,911) scale(1.25,-1.23333) translate(-218.5,-1646.15)" width="12" x="1092.5" y="892.5"/></g>
  <g id="179">
   <use class="kv10" height="26" transform="rotate(0,1192.11,885.5) scale(1.86959,1.88462) translate(-547.086,-404.143)" width="17" x="1176.21696244856" xlink:href="#EnergyConsumer:Y-Y站用_0" y="861" zvalue="914"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449973649413" ObjectName="摇尾变压器"/>
   <cge:TPSR_Ref TObjectID="6192449973649413"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1192.11,885.5) scale(1.86959,1.88462) translate(-547.086,-404.143)" width="17" x="1176.21696244856" y="861"/></g>
  <g id="185">
   <use class="kv10" height="26" transform="rotate(0,1280.11,885.5) scale(1.86959,1.88462) translate(-588.017,-404.143)" width="17" x="1264.21696244856" xlink:href="#EnergyConsumer:Y-Y站用_0" y="861" zvalue="921"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449973714949" ObjectName="摇头变压器"/>
   <cge:TPSR_Ref TObjectID="6192449973714949"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1280.11,885.5) scale(1.86959,1.88462) translate(-588.017,-404.143)" width="17" x="1264.21696244856" y="861"/></g>
  <g id="191">
   <use class="kv10" height="26" transform="rotate(0,1376.11,885.5) scale(1.86959,1.88462) translate(-632.669,-404.143)" width="17" x="1360.21696244856" xlink:href="#EnergyConsumer:Y-Y站用_0" y="861" zvalue="928"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449973780485" ObjectName="煤磨变压器"/>
   <cge:TPSR_Ref TObjectID="6192449973780485"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1376.11,885.5) scale(1.86959,1.88462) translate(-632.669,-404.143)" width="17" x="1360.21696244856" y="861"/></g>
  <g id="197">
   <use class="kv10" height="26" transform="rotate(0,1472.11,885.5) scale(1.86959,1.88462) translate(-677.321,-404.143)" width="17" x="1456.21696244856" xlink:href="#EnergyConsumer:Y-Y站用_0" y="861" zvalue="935"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449973846021" ObjectName="原料磨变压器"/>
   <cge:TPSR_Ref TObjectID="6192449973846021"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1472.11,885.5) scale(1.86959,1.88462) translate(-677.321,-404.143)" width="17" x="1456.21696244856" y="861"/></g>
  <g id="203">
   <use class="kv10" height="30" transform="rotate(0,1568,911) scale(1.25,-1.23333) translate(-312.1,-1646.15)" width="12" x="1560.5" xlink:href="#EnergyConsumer:负荷_0" y="892.5" zvalue="942"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449973911557" ObjectName="#1水泥窑变压器"/>
   <cge:TPSR_Ref TObjectID="6192449973911557"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1568,911) scale(1.25,-1.23333) translate(-312.1,-1646.15)" width="12" x="1560.5" y="892.5"/></g>
  <g id="224">
   <use class="kv10" height="26" transform="rotate(0,1756.11,885.5) scale(1.86959,1.88462) translate(-809.416,-404.143)" width="17" x="1740.21696244856" xlink:href="#EnergyConsumer:Y-Y站用_0" y="861" zvalue="966"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449974304773" ObjectName="#2水泥磨变压器"/>
   <cge:TPSR_Ref TObjectID="6192449974304773"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1756.11,885.5) scale(1.86959,1.88462) translate(-809.416,-404.143)" width="17" x="1740.21696244856" y="861"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="209">
   <use class="kv10" height="40" transform="rotate(90,1660.97,898.27) scale(1.02566,-0.9625) translate(-40.7784,-1832.29)" width="60" x="1630.195404952215" xlink:href="#Compensator:遮放35kV电容_0" y="879.0196850393701" zvalue="949"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449973977093" ObjectName="#1无功补偿装置"/>
   <cge:TPSR_Ref TObjectID="6192449973977093"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1660.97,898.27) scale(1.02566,-0.9625) translate(-40.7784,-1832.29)" width="60" x="1630.195404952215" y="879.0196850393701"/></g>
 </g>
</svg>