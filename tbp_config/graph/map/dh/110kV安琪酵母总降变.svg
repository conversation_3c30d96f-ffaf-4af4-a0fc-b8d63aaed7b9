<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549596848130" height="1045" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1045" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000004" y2="0.9166666666666679"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.89694619969018" y2="15.89694619969018"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.73157590710537" y2="17.73157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.48348701738202" y2="19.48348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.05" x2="5.05" y1="3.566666666666666" y2="15.81666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.750000000000004" y2="4.166666666666668"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="32"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="1" y1="27" y2="11"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="27" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变13_0" viewBox="0,0,32,35">
   <use terminal-index="0" type="0" x="16" xlink:href="#terminal" y="3.5"/>
   <ellipse cx="16.04" cy="11.97" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.97" cy="24.14" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067018" x2="16.03525619067018" y1="20.85" y2="24.95894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067017" x2="10.66666666666667" y1="24.98394833233988" y2="28.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.06302903072498" x2="21.16666666666667" y1="24.93990500916851" y2="28.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067018" x2="16.03525619067018" y1="7.6" y2="11.70894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067017" x2="12.05" y1="11.73394833233987" y2="14.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.06302903072498" x2="20.15" y1="11.68990500916851" y2="14.5"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2032.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2032.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2032.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2032.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2032.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_0" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="3" y1="19" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="4.75" y2="8.75"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="18.75" y2="21.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_1" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="2" y2="24"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_2" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.08333333333333" x2="3.995614035087721" y1="9.083333333333332" y2="17.19627192982456"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.717927631578948" x2="10.16666666666667" y1="9.172423245614038" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="2" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="21.25" y2="24.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
  </symbol>
  <symbol id="Compensator:电容带电感_0" viewBox="0,0,25,40">
   <use terminal-index="0" type="0" x="12.60833333333334" xlink:href="#terminal" y="39.35833333333333"/>
   <use terminal-index="1" type="0" x="12.525" xlink:href="#terminal" y="0.44166666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.5" x2="20.5" y1="25.5" y2="22.5"/>
   <path d="M 12.5 17 L 19.5 17 L 19.5 25.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="4" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12.58,25.58) scale(1,1) translate(0,0)" width="2" x="11.58" y="23.58"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,19.5,24.67) scale(1,1) translate(0,0)" width="4" x="17.5" y="20.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.5" x2="18.5" y1="25.5" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="20" y1="33.16666666666667" y2="33.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.5" x2="19.5" y1="31.16666666666667" y2="29.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.5" x2="20.5" y1="32.16666666666667" y2="32.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="21" y1="31.16666666666667" y2="31.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.35833333333333" x2="4.540905947441219" y1="39.27500000000001" y2="39.27500000000001"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.624239280774555" x2="4.624239280774555" y1="39.35833333333333" y2="38"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.44166666666666" x2="20.44166666666666" y1="37.91666666666667" y2="39.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.583333333333333" x2="12.60833333333334" y1="21.60833333333333" y2="21.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="6.583333333333331" y1="36.85833333333333" y2="36.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.608333333333335" x2="6.608333333333335" y1="23.85833333333334" y2="21.65462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.571296296296294" x2="6.571296296296294" y1="34.63611111111112" y2="36.87685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.60833333333334" x2="12.60833333333334" y1="33.35833333333333" y2="36.87685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.60833333333334" x2="12.60833333333334" y1="36.94166666666667" y2="39.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.60833333333334" x2="12.60833333333334" y1="14.75" y2="32.35833333333334"/>
   <path d="M 6.60833 27.4572 A 2.96392 1.81747 -180 0 1 6.60833 23.8223" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.60833 31.0921 A 2.96392 1.81747 -180 0 1 6.60833 27.4572" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.60833 34.6271 A 2.96392 1.81747 -180 0 1 6.60833 30.9921" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.10833333333334" x2="11.10833333333334" y1="33.35833333333333" y2="33.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.10833333333334" x2="11.10833333333334" y1="32.35833333333333" y2="32.35833333333333"/>
   <path d="M 19.75 7.99167 A 6.84167 7.10597 -270 1 0 12.644 14.8333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 19.7047 7.99897 L 12.5541 7.99897 L 12.5541 0.0833333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路带壁雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="14.875" xlink:href="#terminal" y="39.83880854456296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.25" x2="6.25" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="5.75" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.25" x2="7.25" y1="32.25" y2="32.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="8.25" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="6.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="4.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.91022336769755" x2="14.91022336769755" y1="39.75" y2="0.8333333333333321"/>
   <path d="M 14.75 9.25 L 5.75 9.25 L 5.75 21.25 L 5.75 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.79,21.17) scale(1,1) translate(0,0)" width="6.08" x="2.75" y="14"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="PowerTransformer2:接地可调两卷变_0" viewBox="0,0,22,30">
   <use terminal-index="0" type="1" x="11.00731595793324" xlink:href="#terminal" y="1.08736282578875"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.02194787379973" x2="6.955871323769093" y1="7.640146319158664" y2="3.944602328551218"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.23333333333333" x2="11.01463191586648" y1="3.694602328551216" y2="7.640146319158664"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.00935070873343" x2="11.00935070873343" y1="7.654778235025146" y2="11.68728637061797"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="21.53783721993598" x2="20.53783721993598" y1="2.175582990397805" y2="5.175582990397805"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.5394375857338822" x2="21.53943758573388" y1="13.09064929126657" y2="2.173982624599901"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="18.53052126200274" x2="21.53052126200274" y1="1.175582990397805" y2="2.175582990397805"/>
   <ellipse cx="11.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="11" xlink:href="#terminal" y="7.7"/>
  </symbol>
  <symbol id="PowerTransformer2:接地可调两卷变_1" viewBox="0,0,22,30">
   <use terminal-index="1" type="1" x="11" xlink:href="#terminal" y="29.05121170553269"/>
   <path d="M 6.75 25.8333 L 15.8333 25.8333 L 11.0833 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:5卷PT带壁雷器_0" viewBox="0,0,30,35">
   <use terminal-index="0" type="0" x="25.27572331551165" xlink:href="#terminal" y="34.09945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.25" x2="25.0990682626599" y1="27.2315496546656" y2="27.2315496546656"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666666" x2="25.16666666666666" y1="34.02740325661302" y2="14.61073658994635"/>
   <path d="M 10.5 13.9441 L 4.5 13.9441 L 4.5 6.94407" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="15.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25.25,14.36) scale(1,-1) translate(0,-919.43)" width="7" x="21.75" y="6.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="22.19406992327969" y2="18.19406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666666" x2="26.16666666666666" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="5.5" y1="18.19406992327969" y2="19.19406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666666" x2="24.16666666666666" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="5.5" y1="22.19406992327969" y2="21.19406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.17943360505483" x2="25.17943360505483" y1="6.610736589946352" y2="3.462520268614096"/>
   <ellipse cx="13.98" cy="27.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.39" cy="13.83" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.20921744067709" x2="22.93240159599324" y1="3.3946117330996" y2="3.3946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.37588410734375" x2="23.76573492932657" y1="2.144611733099605" y2="2.144611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.79255077401042" x2="24.34906826265991" y1="0.8946117330996053" y2="0.8946117330996053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="16.34906826265991" y1="27.45662962336982" y2="28.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="13.94906826265991" y1="24.98154965466559" y2="27.4566296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="11.54906826265991" y1="27.45662962336982" y2="28.69416960772192"/>
   <ellipse cx="13.98" cy="20.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.98" cy="27.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.94906826265991" x2="6.94906826265991" y1="24.98154965466559" y2="27.4566296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.949068262659907" x2="4.549068262659912" y1="27.45662962336982" y2="28.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="16.34906826265991" y1="20.45662962336982" y2="21.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="11.54906826265991" y1="20.45662962336982" y2="21.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.949068262659912" x2="9.349068262659905" y1="27.45662962336982" y2="28.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.94906826265991" x2="13.94906826265991" y1="17.9815496546656" y2="20.4566296233698"/>
   <ellipse cx="6.98" cy="20.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.48240159599324" x2="12.88240159599324" y1="13.92329629003649" y2="15.16083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.48240159599324" x2="8.082401595993243" y1="13.92329629003649" y2="15.16083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.48240159599324" x2="10.48240159599324" y1="11.44821632133226" y2="13.92329629003646"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.542550774010419" x2="2.265734929326571" y1="6.8946117330996" y2="6.8946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.709217440677083" x2="3.099068262659904" y1="5.644611733099605" y2="5.644611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.125884107343751" x2="3.682401595993237" y1="4.394611733099605" y2="4.394611733099605"/>
  </symbol>
  <symbol id="Accessory:母线PT带保险_0" viewBox="0,0,44,51">
   <use terminal-index="0" type="0" x="23.21996232536395" xlink:href="#terminal" y="50.54273758135757"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.81333347460429" x2="37.91333348509471" y1="15.35514348552543" y2="20.85514353797752"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.89666669429144" x2="24.89666669429144" y1="17.55514350650627" y2="13.1551434645446"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.81333347460429" x2="35.71333346411387" y1="15.35514348552543" y2="20.85514353797752"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.89666669429144" x2="27.09666671527227" y1="13.1551434645446" y2="14.25514347503501"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.89666669429144" x2="27.09666671527227" y1="17.55514350650627" y2="16.45514349601585"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.97499997115135" x2="18.97499997115135" y1="23.84999998426438" y2="43.92500017571449"/>
   <rect fill-opacity="0" height="9.9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,18.97,32.93) scale(1,1) translate(0,0)" width="5.5" x="16.22" y="27.98"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.88333330361049" x2="36.85000014162064" y1="43.92500017571449" y2="43.92500017571449"/>
   <path d="M 11.55 15.875 L 6.05 15.875 L 6.05 27.975" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.85000014162064" x2="36.85000014162064" y1="43.92500017571449" y2="15.04999990034103"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.10000001049042" x2="23.10000001049042" y1="43.92500017571449" y2="50.525000238657"/>
   <rect fill-opacity="0" height="17.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,36.85,16) scale(1,-1) translate(0,-699.43)" width="7.7" x="33" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.099999800682067" x2="10.99999989509582" y1="37.60000011539459" y2="28.80000003147125"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.77237710644068" x2="36.77237710644068" y1="7.471810077010772" y2="4.008772090519184"/>
   <ellipse cx="19.04" cy="19.44" fill-opacity="0" rx="4.59" ry="4.59" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="39.00513934691844" x2="34.30064187290063" y1="3.934072700740849" y2="3.934072700740849"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="38.08847267150976" x2="35.2173085483093" y1="2.559072687627836" y2="2.559072687627836"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.44680599872369" x2="35.85897522109538" y1="1.184072674514812" y2="1.184072674514812"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.01064172708383" x2="21.65064175226082" y1="19.67729253017713" y2="21.03858652594676"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.01064172708382" x2="19.01064172708382" y1="16.95470453863786" y2="19.67729253017711"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.01064172708382" x2="16.37064170190682" y1="19.67729253017713" y2="21.03858652594676"/>
   <ellipse cx="18.67" cy="11.65" fill-opacity="0" rx="4.59" ry="4.59" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.8" cy="15.59" fill-opacity="0" rx="4.59" ry="4.59" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.76897499135525" x2="14.40897501653224" y1="15.82729249346067" y2="17.1885864892303"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.76897499135524" x2="9.128974966178244" y1="15.82729249346067" y2="17.1885864892303"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.64397505692036" x2="21.28397508209735" y1="11.88562578920334" y2="13.24691978497297"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.64397505692035" x2="16.00397503174336" y1="11.88562578920334" y2="13.24691978497297"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.64397505692035" x2="18.64397505692035" y1="9.163037797664071" y2="11.88562578920332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.76897499135524" x2="11.76897499135524" y1="13.1047045019214" y2="15.82729249346065"/>
   <ellipse cx="25.46" cy="15.41" fill-opacity="0" rx="4.59" ry="4.59" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <rect fill-opacity="0" height="9.77" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6.19,32.74) scale(1,1) translate(0,0)" width="5.78" x="3.3" y="27.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.129282572815271" x2="6.129282572815271" y1="37.59454254183299" y2="39.80000013637542"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.387769126221219" x2="4.691566274612683" y1="39.65334678993617" y2="39.65334678993617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.862416669798145" x2="5.216918731035754" y1="40.44137547457077" y2="40.44137547457077"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.494669950301995" x2="5.584665450531904" y1="41.22940415920537" y2="41.22940415920537"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV安琪酵母总降变" InitShowingPlane="" fill="rgb(0,0,0)" height="1045" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <line fill="none" id="558" stroke="rgb(0,39,45)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.79365079365175" x2="323.3811994317853" y1="65.414843687847" y2="65.414843687847" zvalue="906"/>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="528" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,101.196,313.964) scale(1,1) translate(0,0)" width="72.88" x="64.76000000000001" y="301.96" zvalue="936"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,101.196,313.964) scale(1,1) translate(0,0)" writing-mode="lr" x="101.2" xml:space="preserve" y="318.46" zvalue="936">信号一览</text>
  <image height="60" id="2" preserveAspectRatio="xMidYMid slice" width="273.81" x="48.02" xlink:href="logo.png" y="38.46"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="372" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,184.919,68.4643) scale(1,1) translate(-1.06617e-14,0)" writing-mode="lr" x="184.92" xml:space="preserve" y="71.95999999999999" zvalue="1166"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,178.405,66.5296) scale(1,1) translate(2.00633e-14,0)" writing-mode="lr" x="178.4" xml:space="preserve" y="75.53" zvalue="1167">110kV安琪酵母总降变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="370" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,616.647,292.305) scale(1,1) translate(0,0)" writing-mode="lr" x="616.65" xml:space="preserve" y="296.81" zvalue="903">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="369" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,485.076,633.464) scale(1,1) translate(0,0)" writing-mode="lr" x="485.08" xml:space="preserve" y="637.96" zvalue="905">10kVⅠ段母线</text>
  <line fill="none" id="557" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="379.5714285714289" x2="379.5714285714289" y1="38.21428571428567" y2="1028.214285714286" zvalue="907"/>
  <line fill="none" id="555" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.04835312140904" x2="322.6596131750282" y1="157.8422315970544" y2="157.8422315970544" zvalue="909"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="46.14285714285745" x2="117.5828571428574" y1="916.7770766533581" y2="916.7770766533581"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="46.14285714285745" x2="117.5828571428574" y1="968.1165766533582" y2="968.1165766533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="46.14285714285745" x2="46.14285714285745" y1="916.7770766533581" y2="968.1165766533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5828571428574" x2="117.5828571428574" y1="916.7770766533581" y2="968.1165766533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5833571428575" x2="352.1433571428574" y1="916.7770766533581" y2="916.7770766533581"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5833571428575" x2="352.1433571428574" y1="968.1165766533582" y2="968.1165766533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5833571428575" x2="117.5833571428575" y1="916.7770766533581" y2="968.1165766533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="352.1433571428574" x2="352.1433571428574" y1="916.7770766533581" y2="968.1165766533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="46.14285714285745" x2="117.5828571428574" y1="968.1165566533581" y2="968.1165566533581"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="46.14285714285745" x2="117.5828571428574" y1="995.5940566533582" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="46.14285714285745" x2="46.14285714285745" y1="968.1165566533581" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5828571428574" x2="117.5828571428574" y1="968.1165566533581" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5833571428575" x2="188.6186571428575" y1="968.1165566533581" y2="968.1165566533581"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5833571428575" x2="188.6186571428575" y1="995.5940566533582" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5833571428575" x2="117.5833571428575" y1="968.1165566533581" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.6186571428575" x2="188.6186571428575" y1="968.1165566533581" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.6186571428575" x2="270.3807571428574" y1="968.1165566533581" y2="968.1165566533581"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.6186571428575" x2="270.3807571428574" y1="995.5940566533582" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.6186571428575" x2="188.6186571428575" y1="968.1165566533581" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="270.3807571428574" x2="270.3807571428574" y1="968.1165566533581" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="270.3806571428574" x2="352.1427571428575" y1="968.1165566533581" y2="968.1165566533581"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="270.3806571428574" x2="352.1427571428575" y1="995.5940566533582" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="270.3806571428574" x2="270.3806571428574" y1="968.1165566533581" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="352.1427571428575" x2="352.1427571428575" y1="968.1165566533581" y2="995.5940566533582"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="46.14285714285745" x2="117.5828571428574" y1="995.5939766533581" y2="995.5939766533581"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="46.14285714285745" x2="117.5828571428574" y1="1023.071476653358" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="46.14285714285745" x2="46.14285714285745" y1="995.5939766533581" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5828571428574" x2="117.5828571428574" y1="995.5939766533581" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5833571428575" x2="188.6186571428575" y1="995.5939766533581" y2="995.5939766533581"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5833571428575" x2="188.6186571428575" y1="1023.071476653358" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="117.5833571428575" x2="117.5833571428575" y1="995.5939766533581" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.6186571428575" x2="188.6186571428575" y1="995.5939766533581" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.6186571428575" x2="270.3807571428574" y1="995.5939766533581" y2="995.5939766533581"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.6186571428575" x2="270.3807571428574" y1="1023.071476653358" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.6186571428575" x2="188.6186571428575" y1="995.5939766533581" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="270.3807571428574" x2="270.3807571428574" y1="995.5939766533581" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="270.3806571428574" x2="352.1427571428575" y1="995.5939766533581" y2="995.5939766533581"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="270.3806571428574" x2="352.1427571428575" y1="1023.071476653358" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="270.3806571428574" x2="270.3806571428574" y1="995.5939766533581" y2="1023.071476653358"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="352.1427571428575" x2="352.1427571428575" y1="995.5939766533581" y2="1023.071476653358"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="553" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,193.195,946.351) scale(1,1) translate(-1.14349e-14,1.03864e-13)" writing-mode="lr" x="51.5" xml:space="preserve" y="952.35" zvalue="911">参考图号       LongZhunPo-01-2017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="552" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,123.72,983.719) scale(1,1) translate(5.4379e-14,-1.51218e-12)" writing-mode="lr" x="61.23" xml:space="preserve" y="989.72" zvalue="912">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="551" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,267.024,984.719) scale(1,1) translate(1.32108e-13,-1.51373e-12)" writing-mode="lr" x="198.32" xml:space="preserve" y="990.72" zvalue="913">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="550" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,121.671,1010.78) scale(1,1) translate(0,1.11017e-13)" writing-mode="lr" x="121.67" xml:space="preserve" y="1016.78" zvalue="914">更新          许方杰</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="549" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,275.318,1011.28) scale(1,1) translate(0,1.11072e-13)" writing-mode="lr" x="198.49" xml:space="preserve" y="1017.28" zvalue="915">更新日期    20231219</text>
  <line fill="none" id="548" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="45.42754212945226" x2="350.0388021830714" y1="612.6088163836553" y2="612.6088163836553" zvalue="916"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="546" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,88.4921,629.651) scale(1,1) translate(3.96159e-15,-1.35658e-13)" writing-mode="lr" x="88.49213663347393" xml:space="preserve" y="634.1506668831529" zvalue="918">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.142857142857338" x2="190.1428571428573" y1="164.3214285714287" y2="164.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.142857142857338" x2="190.1428571428573" y1="190.3214285714287" y2="190.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.142857142857338" x2="9.142857142857338" y1="164.3214285714287" y2="190.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.1428571428573" x2="190.1428571428573" y1="164.3214285714287" y2="190.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.1428571428573" x2="371.1428571428573" y1="164.3214285714287" y2="164.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.1428571428573" x2="371.1428571428573" y1="190.3214285714287" y2="190.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.1428571428573" x2="190.1428571428573" y1="164.3214285714287" y2="190.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.1428571428573" x2="371.1428571428573" y1="164.3214285714287" y2="190.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.142857142857338" x2="190.1428571428573" y1="190.3214285714287" y2="190.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.142857142857338" x2="190.1428571428573" y1="214.5714285714287" y2="214.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.142857142857338" x2="9.142857142857338" y1="190.3214285714287" y2="214.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.1428571428573" x2="190.1428571428573" y1="190.3214285714287" y2="214.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.1428571428573" x2="371.1428571428573" y1="190.3214285714287" y2="190.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.1428571428573" x2="371.1428571428573" y1="214.5714285714287" y2="214.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.1428571428573" x2="190.1428571428573" y1="190.3214285714287" y2="214.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.1428571428573" x2="371.1428571428573" y1="190.3214285714287" y2="214.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.142857142857338" x2="190.1428571428573" y1="214.5714285714287" y2="214.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.142857142857338" x2="190.1428571428573" y1="237.3214285714287" y2="237.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.142857142857338" x2="9.142857142857338" y1="214.5714285714287" y2="237.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.1428571428573" x2="190.1428571428573" y1="214.5714285714287" y2="237.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.1428571428573" x2="371.1428571428573" y1="214.5714285714287" y2="214.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.1428571428573" x2="371.1428571428573" y1="237.3214285714287" y2="237.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.1428571428573" x2="190.1428571428573" y1="214.5714285714287" y2="237.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.1428571428573" x2="371.1428571428573" y1="214.5714285714287" y2="237.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.142857142857338" x2="190.1428571428573" y1="237.3214285714287" y2="237.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.142857142857338" x2="190.1428571428573" y1="260.0714285714287" y2="260.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.142857142857338" x2="9.142857142857338" y1="237.3214285714287" y2="260.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.1428571428573" x2="190.1428571428573" y1="237.3214285714287" y2="260.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.1428571428573" x2="371.1428571428573" y1="237.3214285714287" y2="237.3214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.1428571428573" x2="371.1428571428573" y1="260.0714285714287" y2="260.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.1428571428573" x2="190.1428571428573" y1="237.3214285714287" y2="260.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.1428571428573" x2="371.1428571428573" y1="237.3214285714287" y2="260.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.142857142857338" x2="190.1428571428573" y1="260.0714285714287" y2="260.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.142857142857338" x2="190.1428571428573" y1="282.8214285714287" y2="282.8214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9.142857142857338" x2="9.142857142857338" y1="260.0714285714287" y2="282.8214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.1428571428573" x2="190.1428571428573" y1="260.0714285714287" y2="282.8214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.1428571428573" x2="371.1428571428573" y1="260.0714285714287" y2="260.0714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.1428571428573" x2="371.1428571428573" y1="282.8214285714287" y2="282.8214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190.1428571428573" x2="190.1428571428573" y1="260.0714285714287" y2="282.8214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.1428571428573" x2="371.1428571428573" y1="260.0714285714287" y2="282.8214285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.39262857142865" x2="105.1671285714286" y1="438.5714285714287" y2="438.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.39262857142865" x2="105.1671285714286" y1="476.8537285714287" y2="476.8537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.39262857142865" x2="59.39262857142865" y1="438.5714285714287" y2="476.8537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1671285714286" x2="105.1671285714286" y1="438.5714285714287" y2="476.8537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1673285714287" x2="162.4999285714287" y1="438.5714285714287" y2="438.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1673285714287" x2="162.4999285714287" y1="476.8537285714287" y2="476.8537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1673285714287" x2="105.1673285714287" y1="438.5714285714287" y2="476.8537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.4999285714287" x2="162.4999285714287" y1="438.5714285714287" y2="476.8537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5000285714286" x2="221.3064285714287" y1="438.5714285714287" y2="438.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5000285714286" x2="221.3064285714287" y1="476.8537285714287" y2="476.8537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5000285714286" x2="162.5000285714286" y1="438.5714285714287" y2="476.8537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3064285714287" x2="221.3064285714287" y1="438.5714285714287" y2="476.8537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3063285714286" x2="281.2500285714286" y1="438.5714285714287" y2="438.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3063285714286" x2="281.2500285714286" y1="476.8537285714287" y2="476.8537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3063285714286" x2="221.3063285714286" y1="438.5714285714287" y2="476.8537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="281.2500285714286" y1="438.5714285714287" y2="476.8537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="340.0564285714286" y1="438.5714285714287" y2="438.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="340.0564285714286" y1="476.8537285714287" y2="476.8537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="281.2500285714286" y1="438.5714285714287" y2="476.8537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="340.0564285714286" x2="340.0564285714286" y1="438.5714285714287" y2="476.8537285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.39262857142865" x2="105.1671285714286" y1="476.8537285714286" y2="476.8537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.39262857142865" x2="105.1671285714286" y1="501.5331285714287" y2="501.5331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.39262857142865" x2="59.39262857142865" y1="476.8537285714286" y2="501.5331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1671285714286" x2="105.1671285714286" y1="476.8537285714286" y2="501.5331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1673285714287" x2="162.4999285714287" y1="476.8537285714286" y2="476.8537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1673285714287" x2="162.4999285714287" y1="501.5331285714287" y2="501.5331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1673285714287" x2="105.1673285714287" y1="476.8537285714286" y2="501.5331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.4999285714287" x2="162.4999285714287" y1="476.8537285714286" y2="501.5331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5000285714286" x2="221.3064285714287" y1="476.8537285714286" y2="476.8537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5000285714286" x2="221.3064285714287" y1="501.5331285714287" y2="501.5331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5000285714286" x2="162.5000285714286" y1="476.8537285714286" y2="501.5331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3064285714287" x2="221.3064285714287" y1="476.8537285714286" y2="501.5331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3063285714286" x2="281.2500285714286" y1="476.8537285714286" y2="476.8537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3063285714286" x2="281.2500285714286" y1="501.5331285714287" y2="501.5331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3063285714286" x2="221.3063285714286" y1="476.8537285714286" y2="501.5331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="281.2500285714286" y1="476.8537285714286" y2="501.5331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="340.0564285714286" y1="476.8537285714286" y2="476.8537285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="340.0564285714286" y1="501.5331285714287" y2="501.5331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="281.2500285714286" y1="476.8537285714286" y2="501.5331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="340.0564285714286" x2="340.0564285714286" y1="476.8537285714286" y2="501.5331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.39262857142865" x2="105.1671285714286" y1="501.5331285714287" y2="501.5331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.39262857142865" x2="105.1671285714286" y1="526.2125285714287" y2="526.2125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.39262857142865" x2="59.39262857142865" y1="501.5331285714287" y2="526.2125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1671285714286" x2="105.1671285714286" y1="501.5331285714287" y2="526.2125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1673285714287" x2="162.4999285714287" y1="501.5331285714287" y2="501.5331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1673285714287" x2="162.4999285714287" y1="526.2125285714287" y2="526.2125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1673285714287" x2="105.1673285714287" y1="501.5331285714287" y2="526.2125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.4999285714287" x2="162.4999285714287" y1="501.5331285714287" y2="526.2125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5000285714286" x2="221.3064285714287" y1="501.5331285714287" y2="501.5331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5000285714286" x2="221.3064285714287" y1="526.2125285714287" y2="526.2125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5000285714286" x2="162.5000285714286" y1="501.5331285714287" y2="526.2125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3064285714287" x2="221.3064285714287" y1="501.5331285714287" y2="526.2125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3063285714286" x2="281.2500285714286" y1="501.5331285714287" y2="501.5331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3063285714286" x2="281.2500285714286" y1="526.2125285714287" y2="526.2125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3063285714286" x2="221.3063285714286" y1="501.5331285714287" y2="526.2125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="281.2500285714286" y1="501.5331285714287" y2="526.2125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="340.0564285714286" y1="501.5331285714287" y2="501.5331285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="340.0564285714286" y1="526.2125285714287" y2="526.2125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="281.2500285714286" y1="501.5331285714287" y2="526.2125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="340.0564285714286" x2="340.0564285714286" y1="501.5331285714287" y2="526.2125285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.39262857142865" x2="105.1671285714286" y1="526.2125285714286" y2="526.2125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.39262857142865" x2="105.1671285714286" y1="550.8919285714287" y2="550.8919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.39262857142865" x2="59.39262857142865" y1="526.2125285714286" y2="550.8919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1671285714286" x2="105.1671285714286" y1="526.2125285714286" y2="550.8919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1673285714287" x2="162.4999285714287" y1="526.2125285714286" y2="526.2125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1673285714287" x2="162.4999285714287" y1="550.8919285714287" y2="550.8919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1673285714287" x2="105.1673285714287" y1="526.2125285714286" y2="550.8919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.4999285714287" x2="162.4999285714287" y1="526.2125285714286" y2="550.8919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5000285714286" x2="221.3064285714287" y1="526.2125285714286" y2="526.2125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5000285714286" x2="221.3064285714287" y1="550.8919285714287" y2="550.8919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5000285714286" x2="162.5000285714286" y1="526.2125285714286" y2="550.8919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3064285714287" x2="221.3064285714287" y1="526.2125285714286" y2="550.8919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3063285714286" x2="281.2500285714286" y1="526.2125285714286" y2="526.2125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3063285714286" x2="281.2500285714286" y1="550.8919285714287" y2="550.8919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3063285714286" x2="221.3063285714286" y1="526.2125285714286" y2="550.8919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="281.2500285714286" y1="526.2125285714286" y2="550.8919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="340.0564285714286" y1="526.2125285714286" y2="526.2125285714286"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="340.0564285714286" y1="550.8919285714287" y2="550.8919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="281.2500285714286" y1="526.2125285714286" y2="550.8919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="340.0564285714286" x2="340.0564285714286" y1="526.2125285714286" y2="550.8919285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.39262857142865" x2="105.1671285714286" y1="550.8920285714287" y2="550.8920285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.39262857142865" x2="105.1671285714286" y1="575.5714285714287" y2="575.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.39262857142865" x2="59.39262857142865" y1="550.8920285714287" y2="575.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1671285714286" x2="105.1671285714286" y1="550.8920285714287" y2="575.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1673285714287" x2="162.4999285714287" y1="550.8920285714287" y2="550.8920285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1673285714287" x2="162.4999285714287" y1="575.5714285714287" y2="575.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1673285714287" x2="105.1673285714287" y1="550.8920285714287" y2="575.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.4999285714287" x2="162.4999285714287" y1="550.8920285714287" y2="575.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5000285714286" x2="221.3064285714287" y1="550.8920285714287" y2="550.8920285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5000285714286" x2="221.3064285714287" y1="575.5714285714287" y2="575.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5000285714286" x2="162.5000285714286" y1="550.8920285714287" y2="575.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3064285714287" x2="221.3064285714287" y1="550.8920285714287" y2="575.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3063285714286" x2="281.2500285714286" y1="550.8920285714287" y2="550.8920285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3063285714286" x2="281.2500285714286" y1="575.5714285714287" y2="575.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3063285714286" x2="221.3063285714286" y1="550.8920285714287" y2="575.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="281.2500285714286" y1="550.8920285714287" y2="575.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="340.0564285714286" y1="550.8920285714287" y2="550.8920285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="340.0564285714286" y1="575.5714285714287" y2="575.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="281.2500285714286" y1="550.8920285714287" y2="575.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="340.0564285714286" x2="340.0564285714286" y1="550.8920285714287" y2="575.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.39262857142865" x2="105.1671285714286" y1="575.5714285714287" y2="575.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.39262857142865" x2="105.1671285714286" y1="600.2508285714287" y2="600.2508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.39262857142865" x2="59.39262857142865" y1="575.5714285714287" y2="600.2508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1671285714286" x2="105.1671285714286" y1="575.5714285714287" y2="600.2508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1673285714287" x2="162.4999285714287" y1="575.5714285714287" y2="575.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1673285714287" x2="162.4999285714287" y1="600.2508285714287" y2="600.2508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.1673285714287" x2="105.1673285714287" y1="575.5714285714287" y2="600.2508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.4999285714287" x2="162.4999285714287" y1="575.5714285714287" y2="600.2508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5000285714286" x2="221.3064285714287" y1="575.5714285714287" y2="575.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5000285714286" x2="221.3064285714287" y1="600.2508285714287" y2="600.2508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5000285714286" x2="162.5000285714286" y1="575.5714285714287" y2="600.2508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3064285714287" x2="221.3064285714287" y1="575.5714285714287" y2="600.2508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3063285714286" x2="281.2500285714286" y1="575.5714285714287" y2="575.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3063285714286" x2="281.2500285714286" y1="600.2508285714287" y2="600.2508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3063285714286" x2="221.3063285714286" y1="575.5714285714287" y2="600.2508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="281.2500285714286" y1="575.5714285714287" y2="600.2508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="340.0564285714286" y1="575.5714285714287" y2="575.5714285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="340.0564285714286" y1="600.2508285714287" y2="600.2508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="281.2500285714286" x2="281.2500285714286" y1="575.5714285714287" y2="600.2508285714287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="340.0564285714286" x2="340.0564285714286" y1="575.5714285714287" y2="600.2508285714287"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="543" stroke="rgb(255,255,255)" text-anchor="middle" x="135.15625" xml:space="preserve" y="456.078125" zvalue="921">110kV     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="543" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="135.15625" xml:space="preserve" y="472.078125" zvalue="921">母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="542" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,204.541,315.913) scale(1,1) translate(0,0)" writing-mode="lr" x="204.54" xml:space="preserve" y="320.41" zvalue="922">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="541" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,309.541,315.913) scale(1,1) translate(0,0)" writing-mode="lr" x="309.54" xml:space="preserve" y="320.41" zvalue="923">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="540" stroke="rgb(255,255,255)" text-anchor="middle" x="252.484375" xml:space="preserve" y="456.078125" zvalue="924">10kVI段     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="540" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="252.484375" xml:space="preserve" y="472.078125" zvalue="924">母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="539" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.1429,491.571) scale(1,1) translate(0,0)" writing-mode="lr" x="83.14285714285745" xml:space="preserve" y="496.0714285714287" zvalue="925">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="538" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,81.8929,515.821) scale(1,1) translate(0,0)" writing-mode="lr" x="81.89285714285734" xml:space="preserve" y="520.3214285714287" zvalue="926">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="537" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.1429,542.571) scale(1,1) translate(0,5.86277e-14)" writing-mode="lr" x="83.14285714285745" xml:space="preserve" y="547.0714285714286" zvalue="927">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="536" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.1429,568.071) scale(1,1) translate(0,0)" writing-mode="lr" x="83.14285714285745" xml:space="preserve" y="572.5714285714286" zvalue="928">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="535" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.1429,593.571) scale(1,1) translate(0,0)" writing-mode="lr" x="83.14285714285745" xml:space="preserve" y="598.0714285714286" zvalue="929">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="534" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.1429,177.294) scale(1,1) translate(0,0)" writing-mode="lr" x="45.14" xml:space="preserve" y="182.79" zvalue="930">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="533" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.948,178.405) scale(1,1) translate(0,0)" writing-mode="lr" x="226.95" xml:space="preserve" y="183.9" zvalue="931">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="532" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.6081,202.655) scale(1,1) translate(0,0)" writing-mode="lr" x="56.61" xml:space="preserve" y="207.15" zvalue="932">110kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="529" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.0526,227.766) scale(1,1) translate(0,0)" writing-mode="lr" x="52.05" xml:space="preserve" y="232.27" zvalue="935">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="362" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,586.821,609.214) scale(1,1) translate(0,0)" writing-mode="lr" x="586.8200000000001" xml:space="preserve" y="613.71" zvalue="959">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="361" stroke="rgb(255,255,255)" text-anchor="middle" x="560.2421875" xml:space="preserve" y="506.96875" zvalue="961">10kVⅠ段母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="361" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="560.2421875" xml:space="preserve" y="522.96875" zvalue="961">互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="358" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1845.98,640.019) scale(1,1) translate(0,0)" writing-mode="lr" x="1845.98" xml:space="preserve" y="644.52" zvalue="970">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="356" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1527.07,590.123) scale(1,1) translate(0,0)" writing-mode="lr" x="1527.07" xml:space="preserve" y="594.62" zvalue="974">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="502" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1425.12,485.481) scale(1,1) translate(-3.04994e-13,0)" writing-mode="lr" x="1425.13" xml:space="preserve" y="489.98" zvalue="975">(SZ20-10000/110)</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="353" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1256.07,361.214) scale(1,1) translate(0,0)" writing-mode="lr" x="1256.07" xml:space="preserve" y="365.71" zvalue="986">1901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="352" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1221.69,465.714) scale(1,1) translate(0,-2.026e-13)" writing-mode="lr" x="1221.69" xml:space="preserve" y="470.21" zvalue="988">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="351" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1809.95,610.214) scale(1,1) translate(0,0)" writing-mode="lr" x="1809.95" xml:space="preserve" y="614.71" zvalue="993">0902</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="350" stroke="rgb(255,255,255)" text-anchor="middle" x="1773.2265625" xml:space="preserve" y="515.71875" zvalue="995">10kVⅡ段母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="350" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1773.2265625" xml:space="preserve" y="531.71875" zvalue="995">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,955.183,603.214) scale(1,1) translate(0,0)" writing-mode="lr" x="955.1799999999999" xml:space="preserve" y="607.71" zvalue="1098">012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="215" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1119.07,602.214) scale(1,1) translate(0,0)" writing-mode="lr" x="1119.07" xml:space="preserve" y="606.71" zvalue="1100">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1093.85,206.854) scale(1,1) translate(0,0)" writing-mode="lr" x="1093.85" xml:space="preserve" y="211.35" zvalue="1172">151</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1097.34,145.963) scale(1,1) translate(0,0)" writing-mode="lr" x="1097.34" xml:space="preserve" y="150.46" zvalue="1174">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1147.94,131.524) scale(1,1) translate(0,0)" writing-mode="lr" x="1147.94" xml:space="preserve" y="136.02" zvalue="1176">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1097.27,275.758) scale(1,1) translate(0,0)" writing-mode="lr" x="1097.27" xml:space="preserve" y="280.26" zvalue="1179">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1147.94,186.77) scale(1,1) translate(0,0)" writing-mode="lr" x="1147.94" xml:space="preserve" y="191.27" zvalue="1181">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1147.94,258.81) scale(1,1) translate(0,0)" writing-mode="lr" x="1147.94" xml:space="preserve" y="263.31" zvalue="1184">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1073.97,123.806) scale(1,1) translate(0,0)" writing-mode="lr" x="1073.97" xml:space="preserve" y="128.31" zvalue="1193">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1265.07,333) scale(1,1) translate(0,0)" writing-mode="lr" x="1265.07" xml:space="preserve" y="337.5" zvalue="1199">10</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1429.99,463.149) scale(1,1) translate(0,0)" writing-mode="lr" x="1429.99" xml:space="preserve" y="467.65" zvalue="1206">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1562.57,511.048) scale(1,1) translate(0,0)" writing-mode="lr" x="1562.57" xml:space="preserve" y="515.55" zvalue="1207">1020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1518.02,363.614) scale(1,1) translate(0,0)" writing-mode="lr" x="1518.02" xml:space="preserve" y="368.11" zvalue="1217">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1490.5,321.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1490.5" xml:space="preserve" y="325.64" zvalue="1221">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1531.64,326.786) scale(1,1) translate(0,0)" writing-mode="lr" x="1531.64" xml:space="preserve" y="331.29" zvalue="1225">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1533.64,383.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1533.64" xml:space="preserve" y="388.14" zvalue="1228">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1493.93,416.963) scale(1,1) translate(0,0)" writing-mode="lr" x="1493.93" xml:space="preserve" y="421.46" zvalue="1231">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1534.21,421.357) scale(1,1) translate(0,0)" writing-mode="lr" x="1534.21" xml:space="preserve" y="425.86" zvalue="1235">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,927.71,588.806) scale(1,1) translate(0,0)" writing-mode="lr" x="927.71" xml:space="preserve" y="593.3099999999999" zvalue="1268">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,827.982,485.532) scale(1,1) translate(0,0)" writing-mode="lr" x="827.98" xml:space="preserve" y="490.03" zvalue="1269">(SZ20-10000/110)</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,832.846,463.201) scale(1,1) translate(0,1.51113e-13)" writing-mode="lr" x="832.85" xml:space="preserve" y="467.7" zvalue="1273">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,965.424,511.1) scale(1,1) translate(0,0)" writing-mode="lr" x="965.42" xml:space="preserve" y="515.6" zvalue="1274">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,920.882,363.665) scale(1,1) translate(-2.02367e-13,0)" writing-mode="lr" x="920.88" xml:space="preserve" y="368.17" zvalue="1278">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,893.357,321.191) scale(1,1) translate(0,0)" writing-mode="lr" x="893.36" xml:space="preserve" y="325.69" zvalue="1281">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,934.497,326.838) scale(1,1) translate(1.03028e-13,0)" writing-mode="lr" x="934.5" xml:space="preserve" y="331.34" zvalue="1285">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,936.497,383.695) scale(1,1) translate(0,0)" writing-mode="lr" x="936.5" xml:space="preserve" y="388.19" zvalue="1288">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,896.786,417.015) scale(1,1) translate(0,0)" writing-mode="lr" x="896.79" xml:space="preserve" y="421.51" zvalue="1291">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,937.068,418.552) scale(1,1) translate(1.03203e-13,0)" writing-mode="lr" x="937.0700000000001" xml:space="preserve" y="423.05" zvalue="1294">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,481.75,737.584) scale(1,1) translate(0,0)" writing-mode="lr" x="481.75" xml:space="preserve" y="742.08" zvalue="1298">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,504,802.792) scale(1,1) translate(0,0)" writing-mode="lr" x="504" xml:space="preserve" y="807.29" zvalue="1300">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" x="458.5" xml:space="preserve" y="926.21875" zvalue="1304">Ⅰ-1S循环水</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="458.5" xml:space="preserve" y="942.21875" zvalue="1304">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,550.375,740.282) scale(1,1) translate(0,0)" writing-mode="lr" x="550.38" xml:space="preserve" y="744.78" zvalue="1307">052</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,573.5,802.532) scale(1,1) translate(0,0)" writing-mode="lr" x="573.5" xml:space="preserve" y="807.03" zvalue="1310">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" x="527.5" xml:space="preserve" y="926.21875" zvalue="1314">Ⅰ-2S老厂#1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="527.5" xml:space="preserve" y="942.21875" zvalue="1314">出线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="150" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,627.375,742.763) scale(1,1) translate(0,0)" writing-mode="lr" x="627.38" xml:space="preserve" y="747.26" zvalue="1317">053</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,644.5,804.256) scale(1,1) translate(0,0)" writing-mode="lr" x="644.5" xml:space="preserve" y="808.76" zvalue="1320">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" x="604.5" xml:space="preserve" y="926.21875" zvalue="1324">Ⅰ-3S老厂#2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="604.5" xml:space="preserve" y="942.21875" zvalue="1324">出线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,693.375,745.244) scale(1,1) translate(0,0)" writing-mode="lr" x="693.38" xml:space="preserve" y="749.74" zvalue="1327">054</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,712,807.805) scale(1,1) translate(0,0)" writing-mode="lr" x="712" xml:space="preserve" y="812.3099999999999" zvalue="1329">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,670.5,928.724) scale(1,1) translate(0,0)" writing-mode="lr" x="670.5" xml:space="preserve" y="933.224059990474" zvalue="1333">Ⅰ-4S备用线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,752.5,860.695) scale(1,1) translate(0,0)" writing-mode="lr" x="752.5" xml:space="preserve" y="865.1900000000001" zvalue="1340">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,726,693) scale(1,1) translate(0,0)" writing-mode="lr" x="726" xml:space="preserve" y="697.5" zvalue="1343">0551</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,780.75,770.5) scale(1,1) translate(0,0)" writing-mode="lr" x="780.75" xml:space="preserve" y="775" zvalue="1348">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,864.112,805.669) scale(1,1) translate(0,0)" writing-mode="lr" x="864.11" xml:space="preserve" y="810.17" zvalue="1351">8</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="191" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,820.581,702.313) scale(1,1) translate(0,0)" writing-mode="lr" x="820.58" xml:space="preserve" y="706.8099999999999" zvalue="1355">056</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="222" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,888.864,738.773) scale(1,1) translate(0,0)" writing-mode="lr" x="888.86" xml:space="preserve" y="743.27" zvalue="1372">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,842.727,891.318) scale(1,1) translate(1.8135e-13,0)" writing-mode="lr" x="842.73" xml:space="preserve" y="895.8200000000001" zvalue="1375">#1电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,822.75,856.159) scale(1,1) translate(0,0)" writing-mode="lr" x="822.75" xml:space="preserve" y="860.66" zvalue="1377">87</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="232" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,953.375,729.997) scale(1,1) translate(0,0)" writing-mode="lr" x="953.38" xml:space="preserve" y="734.5" zvalue="1380">057</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="231" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,971.818,813.468) scale(1,1) translate(0,0)" writing-mode="lr" x="971.8200000000001" xml:space="preserve" y="817.97" zvalue="1382">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="230" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,930.318,928.724) scale(1,1) translate(0,0)" writing-mode="lr" x="930.3181818181819" xml:space="preserve" y="933.2240599099688" zvalue="1387">Ⅰ-5S备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1115.74,724.542) scale(1,1) translate(0,0)" writing-mode="lr" x="1115.74" xml:space="preserve" y="729.04" zvalue="1391">059</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1136.18,808.013) scale(1,1) translate(0,0)" writing-mode="lr" x="1136.18" xml:space="preserve" y="812.51" zvalue="1393">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="243" stroke="rgb(255,255,255)" text-anchor="middle" x="1094.671875" xml:space="preserve" y="926.21875" zvalue="1398">Ⅱ-1S前工段</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="243" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1094.671875" xml:space="preserve" y="942.21875" zvalue="1398">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="261" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1180.37,728.261) scale(1,1) translate(0,-1.11829e-12)" writing-mode="lr" x="1180.37" xml:space="preserve" y="732.76" zvalue="1401">061</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1199.64,810.948) scale(1,1) translate(2.25001e-12,0)" writing-mode="lr" x="1199.64" xml:space="preserve" y="815.45" zvalue="1403">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" x="1158.125" xml:space="preserve" y="926.21875" zvalue="1408">Ⅱ-2S后工段</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1158.125" xml:space="preserve" y="942.21875" zvalue="1408">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="270" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1244.73,731.196) scale(1,1) translate(0,0)" writing-mode="lr" x="1244.73" xml:space="preserve" y="735.7" zvalue="1411">062</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="269" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1264,813.883) scale(1,1) translate(0,0)" writing-mode="lr" x="1264" xml:space="preserve" y="818.38" zvalue="1413">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="268" stroke="rgb(255,255,255)" text-anchor="middle" x="1222.5" xml:space="preserve" y="926.21875" zvalue="1418">Ⅱ-3S干燥车</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="268" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1222.5" xml:space="preserve" y="942.21875" zvalue="1418">间线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1314.73,732.313) scale(1,1) translate(0,0)" writing-mode="lr" x="1314.73" xml:space="preserve" y="736.8099999999999" zvalue="1421">063</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="280" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1334,815) scale(1,1) translate(0,0)" writing-mode="lr" x="1334" xml:space="preserve" y="819.5" zvalue="1423">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="279" stroke="rgb(255,255,255)" text-anchor="middle" x="1290.8125" xml:space="preserve" y="926.5456348765983" zvalue="1428">Ⅱ-4S环保车间</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="279" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1290.8125" xml:space="preserve" y="942.5456348765983" zvalue="1428">MVR#1线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="296" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1371.95,863.63) scale(1,1) translate(0,0)" writing-mode="lr" x="1371.95" xml:space="preserve" y="868.13" zvalue="1431">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="295" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1342.7,696.581) scale(1,1) translate(1.47377e-13,-9.16514e-13)" writing-mode="lr" x="1342.7" xml:space="preserve" y="701.08" zvalue="1433">0641</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="294" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1400.2,773.435) scale(1,1) translate(0,0)" writing-mode="lr" x="1400.2" xml:space="preserve" y="777.9400000000001" zvalue="1438">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="308" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1472.13,806.786) scale(1,1) translate(0,0)" writing-mode="lr" x="1472.13" xml:space="preserve" y="811.29" zvalue="1441">8</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="306" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1427.7,702.521) scale(1,1) translate(0,0)" writing-mode="lr" x="1427.7" xml:space="preserve" y="707.02" zvalue="1445">065</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="305" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1496.89,739.89) scale(1,1) translate(0,0)" writing-mode="lr" x="1496.89" xml:space="preserve" y="744.39" zvalue="1452">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="304" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1450.75,892.435) scale(1,1) translate(0,0)" writing-mode="lr" x="1450.75" xml:space="preserve" y="896.9400000000001" zvalue="1454">#2电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="303" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1430.77,857.276) scale(1,1) translate(0,0)" writing-mode="lr" x="1430.77" xml:space="preserve" y="861.78" zvalue="1457">87</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="323" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1551.82,735.248) scale(1,1) translate(0,0)" writing-mode="lr" x="1551.82" xml:space="preserve" y="739.75" zvalue="1460">066</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="322" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1571.09,817.935) scale(1,1) translate(0,0)" writing-mode="lr" x="1571.09" xml:space="preserve" y="822.4400000000001" zvalue="1462">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="321" stroke="rgb(255,255,255)" text-anchor="middle" x="1527.90625" xml:space="preserve" y="926.5456348765983" zvalue="1467">Ⅱ-5S环保车间</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="321" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1527.90625" xml:space="preserve" y="942.5456348765983" zvalue="1467">MVR#2线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="561" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1629.1,737.274) scale(1,1) translate(0,0)" writing-mode="lr" x="1629.1" xml:space="preserve" y="741.77" zvalue="1470">067</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="331" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1648.36,819.961) scale(1,1) translate(0,0)" writing-mode="lr" x="1648.36" xml:space="preserve" y="824.46" zvalue="1472">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="330" stroke="rgb(255,255,255)" text-anchor="middle" x="1605.171875" xml:space="preserve" y="926.5456348765983" zvalue="1477">Ⅱ-6S环保生化</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="330" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1605.171875" xml:space="preserve" y="942.5456348765983" zvalue="1477">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="570" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1703.46,739.3) scale(1,1) translate(0,0)" writing-mode="lr" x="1703.46" xml:space="preserve" y="743.8" zvalue="1480">068</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="569" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1722.73,821.987) scale(1,1) translate(0,0)" writing-mode="lr" x="1722.73" xml:space="preserve" y="826.49" zvalue="1482">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="568" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1679.55,929.055) scale(1,1) translate(-2.55614e-12,1.00735e-13)" writing-mode="lr" x="1679.545454545455" xml:space="preserve" y="933.5552066208475" zvalue="1487">Ⅱ-7S备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="578" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1775.64,738.599) scale(1,1) translate(0,0)" writing-mode="lr" x="1775.64" xml:space="preserve" y="743.1" zvalue="1490">069</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="595" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1794.03,821.591) scale(1,1) translate(0,0)" writing-mode="lr" x="1794.03" xml:space="preserve" y="826.09" zvalue="1492">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="577" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1751.73,929.055) scale(1,1) translate(-2.66833e-12,1.00735e-13)" writing-mode="lr" x="1751.727272727273" xml:space="preserve" y="933.5552056300169" zvalue="1496">Ⅱ-8S备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="587" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1845.16,739.83) scale(1,1) translate(0,0)" writing-mode="lr" x="1845.16" xml:space="preserve" y="744.33" zvalue="1500">071</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="594" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1863.56,822.821) scale(1,1) translate(0,0)" writing-mode="lr" x="1863.56" xml:space="preserve" y="827.3200000000001" zvalue="1502">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="586" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1821.25,929.055) scale(1,1) translate(-2.77639e-12,1.00735e-13)" writing-mode="lr" x="1821.25" xml:space="preserve" y="933.5552067199303" zvalue="1506">Ⅱ-9S备用线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="596" stroke="rgb(255,255,255)" text-anchor="middle" x="310" xml:space="preserve" y="458.25" zvalue="1508">10kVII段     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="596" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="310" xml:space="preserve" y="474.25" zvalue="1508">母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1112.32,18.8545) scale(1,1) translate(0,0)" writing-mode="lr" x="1112.32" xml:space="preserve" y="23.35" zvalue="1511">110kV景琪线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6667,250.778) scale(1,1) translate(0,0)" writing-mode="lr" x="55.67" xml:space="preserve" y="255.28" zvalue="1542">10kVⅡ段频率</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="64.76000000000001" y="301.96" zvalue="936"/></g>
 </g>
 <g id="StateClass">
  <g id="160">
   <use height="30" transform="rotate(0,336.696,316.607) scale(0.708333,0.665547) translate(134.265,154.086)" width="30" x="326.07" xlink:href="#State:红绿圆(方形)_0" y="306.62" zvalue="538"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374897840132" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,336.696,316.607) scale(0.708333,0.665547) translate(134.265,154.086)" width="30" x="326.07" y="306.62"/></g>
  <g id="54">
   <use height="30" transform="rotate(0,239.821,317.857) scale(0.708333,0.665547) translate(94.375,154.714)" width="30" x="229.2" xlink:href="#State:红绿圆(方形)_0" y="307.87" zvalue="539"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562959828385798" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.821,317.857) scale(0.708333,0.665547) translate(94.375,154.714)" width="30" x="229.2" y="307.87"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="560">
   <path class="kv110" d="M 626.24 305.31 L 1630.57 305.31" stroke-width="6" zvalue="902"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674263629829" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674263629829"/></metadata>
  <path d="M 626.24 305.31 L 1630.57 305.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="559">
   <path class="kv10" d="M 426.57 655.02 L 1014.55 655.02" stroke-width="6" zvalue="904"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674263564293" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674263564293"/></metadata>
  <path d="M 426.57 655.02 L 1014.55 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="505">
   <path class="kv10" d="M 1075.45 655.02 L 1883.48 655.02" stroke-width="6" zvalue="969"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674263498757" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674263498757"/></metadata>
  <path d="M 1075.45 655.02 L 1883.48 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="DisconnectorClass">
  <g id="513">
   <use class="kv10" height="36" transform="rotate(0,556.321,611.214) scale(1,-1) translate(0,-1222.43)" width="14" x="549.3214285714289" xlink:href="#Disconnector:手车刀闸_0" y="593.2142857142857" zvalue="957"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450347335685" ObjectName="10kVⅠ段母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450347335685"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,556.321,611.214) scale(1,-1) translate(0,-1222.43)" width="14" x="549.3214285714289" y="593.2142857142857"/></g>
  <g id="494">
   <use class="kv110" height="36" transform="rotate(0,1219.57,359.214) scale(1,1) translate(0,0)" width="14" x="1212.571428571429" xlink:href="#Disconnector:手车刀闸_0" y="341.2142857142857" zvalue="985"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450347204613" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450347204613"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1219.57,359.214) scale(1,1) translate(0,0)" width="14" x="1212.571428571429" y="341.2142857142857"/></g>
  <g id="490">
   <use class="kv10" height="36" transform="rotate(0,1778.32,612.214) scale(1,-1) translate(0,-1224.43)" width="14" x="1771.321428571429" xlink:href="#Disconnector:手车刀闸_0" y="594.2142857142857" zvalue="991"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450347073541" ObjectName="10kVⅡ段母线电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450347073541"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1778.32,612.214) scale(1,-1) translate(0,-1224.43)" width="14" x="1771.321428571429" y="594.2142857142857"/></g>
  <g id="418">
   <use class="kv10" height="36" transform="rotate(0,1104.57,603.214) scale(0.571442,1) translate(825.384,0)" width="14" x="1100.571382795062" xlink:href="#Disconnector:手车刀闸_0" y="585.2142857142857" zvalue="1099"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450346942469" ObjectName="10kV分段0122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450346942469"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1104.57,603.214) scale(0.571442,1) translate(825.384,0)" width="14" x="1100.571382795062" y="585.2142857142857"/></g>
  <g id="680">
   <use class="kv110" height="30" transform="rotate(0,1115.25,143.797) scale(-0.947693,0.6712) translate(-2292.45,65.5094)" width="15" x="1108.142303113418" xlink:href="#Disconnector:刀闸_0" y="133.7287051331838" zvalue="1173"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450347859973" ObjectName="110kV景琪线1516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450347859973"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1115.25,143.797) scale(-0.947693,0.6712) translate(-2292.45,65.5094)" width="15" x="1108.142303113418" y="133.7287051331838"/></g>
  <g id="677">
   <use class="kv110" height="30" transform="rotate(180,1115.24,275.065) scale(0.947693,-0.6712) translate(61.1623,-689.809)" width="15" x="1108.130706458656" xlink:href="#Disconnector:刀闸_0" y="264.9974429261526" zvalue="1178"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450347663365" ObjectName="110kV景琪线1511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450347663365"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1115.24,275.065) scale(0.947693,-0.6712) translate(61.1623,-689.809)" width="15" x="1108.130706458656" y="264.9974429261526"/></g>
  <g id="766">
   <use class="kv110" height="30" transform="rotate(90,1078.97,102.722) scale(-1.11111,-0.814815) translate(-2049.21,-231.568)" width="15" x="1070.638888888889" xlink:href="#Disconnector:刀闸_0" y="90.49999999999994" zvalue="1192"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450347925509" ObjectName="110kV景琪线1519隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450347925509"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1078.97,102.722) scale(-1.11111,-0.814815) translate(-2049.21,-231.568)" width="15" x="1070.638888888889" y="90.49999999999994"/></g>
  <g id="42">
   <use class="kv110" height="30" transform="rotate(180,1500.68,322.139) scale(0.947693,-0.6712) translate(82.4364,-807.017)" width="15" x="1493.571428571428" xlink:href="#Disconnector:刀闸_0" y="312.0714285714286" zvalue="1220"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450348449797" ObjectName="#2主变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450348449797"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1500.68,322.139) scale(0.947693,-0.6712) translate(82.4364,-807.017)" width="15" x="1493.571428571428" y="312.0714285714286"/></g>
  <g id="53">
   <use class="kv110" height="30" transform="rotate(180,1501.39,415.425) scale(0.947693,-0.6712) translate(82.4758,-1039.29)" width="15" x="1494.285714285714" xlink:href="#Disconnector:刀闸_0" y="405.3571428571429" zvalue="1230"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450348777477" ObjectName="#2主变110kV侧1026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450348777477"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1501.39,415.425) scale(0.947693,-0.6712) translate(82.4758,-1039.29)" width="15" x="1494.285714285714" y="405.3571428571429"/></g>
  <g id="113">
   <use class="kv110" height="30" transform="rotate(180,903.536,322.191) scale(0.947693,-0.6712) translate(49.4776,-807.146)" width="15" x="896.4285714285717" xlink:href="#Disconnector:刀闸_0" y="312.1233766233767" zvalue="1279"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450349432837" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450349432837"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,903.536,322.191) scale(0.947693,-0.6712) translate(49.4776,-807.146)" width="15" x="896.4285714285717" y="312.1233766233767"/></g>
  <g id="106">
   <use class="kv110" height="30" transform="rotate(180,904.251,415.477) scale(0.947693,-0.6712) translate(49.517,-1039.42)" width="15" x="897.1428571428572" xlink:href="#Disconnector:刀闸_0" y="405.409090909091" zvalue="1289"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450349105157" ObjectName="#1主变110kV侧1016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450349105157"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,904.251,415.477) scale(0.947693,-0.6712) translate(49.517,-1039.42)" width="15" x="897.1428571428572" y="405.409090909091"/></g>
  <g id="175">
   <use class="kv10" height="26" transform="rotate(0,750,696.5) scale(1,1) translate(0,0)" width="14" x="743" xlink:href="#Disconnector:联体手车刀闸_0" y="683.5" zvalue="1342"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450350481413" ObjectName="#1站用变0551隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450350481413"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,750,696.5) scale(1,1) translate(0,0)" width="14" x="743" y="683.5"/></g>
  <g id="995">
   <use class="kv10" height="30" transform="rotate(180,843.197,797.086) scale(-0.947693,0.6712) translate(-1733.33,385.535)" width="15" x="836.0896908100234" xlink:href="#Disconnector:刀闸_0" y="787.0178751996992" zvalue="1350"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450350678021" ObjectName="#1电容器0568隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450350678021"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,843.197,797.086) scale(-0.947693,0.6712) translate(-1733.33,385.535)" width="15" x="836.0896908100234" y="787.0178751996992"/></g>
  <g id="301">
   <use class="kv10" height="26" transform="rotate(0,1369.45,699.435) scale(1,1) translate(0,0)" width="14" x="1362.454545454545" xlink:href="#Disconnector:联体手车刀闸_0" y="686.4350687002207" zvalue="1432"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450352185349" ObjectName="#2站用变0641隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450352185349"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1369.45,699.435) scale(1,1) translate(0,0)" width="14" x="1362.454545454545" y="686.4350687002207"/></g>
  <g id="320">
   <use class="kv10" height="30" transform="rotate(180,1451.22,798.203) scale(-0.947693,0.6712) translate(-2982.93,386.082)" width="15" x="1444.112418082751" xlink:href="#Disconnector:刀闸_0" y="788.134762081738" zvalue="1440"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450352644101" ObjectName="#2电容器0658隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450352644101"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1451.22,798.203) scale(-0.947693,0.6712) translate(-2982.93,386.082)" width="15" x="1444.112418082751" y="788.134762081738"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="512">
   <use class="kv10" height="51" transform="rotate(0,555.087,556.508) scale(1.0116,1.0473) translate(-6.10955,-23.9291)" width="44" x="532.8321337218041" xlink:href="#Accessory:母线PT带保险_0" y="529.8018490065208" zvalue="958"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450347270149" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="51" opacity="0" stroke="white" transform="rotate(0,555.087,556.508) scale(1.0116,1.0473) translate(-6.10955,-23.9291)" width="44" x="532.8321337218041" y="529.8018490065208"/></g>
  <g id="493">
   <use class="kv110" height="35" transform="rotate(0,1208.44,427.714) scale(1.08333,-1.11429) translate(-91.7069,-809.56)" width="30" x="1192.189394979624" xlink:href="#Accessory:5卷PT带壁雷器_0" y="408.2142857142857" zvalue="987"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450360180741" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1208.44,427.714) scale(1.08333,-1.11429) translate(-91.7069,-809.56)" width="30" x="1192.189394979624" y="408.2142857142857"/></g>
  <g id="489">
   <use class="kv10" height="51" transform="rotate(0,1777.09,560.214) scale(1.0116,1.0473) translate(-20.1212,-24.0965)" width="44" x="1754.832133721804" xlink:href="#Accessory:母线PT带保险_0" y="533.5080673604032" zvalue="992"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450347008005" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="51" opacity="0" stroke="white" transform="rotate(0,1777.09,560.214) scale(1.0116,1.0473) translate(-20.1212,-24.0965)" width="44" x="1754.832133721804" y="533.5080673604032"/></g>
  <g id="414">
   <use class="kv10" height="20" transform="rotate(0,1065.57,595.214) scale(1.25,1.25) translate(-210.614,-116.543)" width="20" x="1053.071428571429" xlink:href="#Accessory:线路PT3_0" y="582.7142857142857" zvalue="1104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450346876933" ObjectName="10kV分段避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1065.57,595.214) scale(1.25,1.25) translate(-210.614,-116.543)" width="20" x="1053.071428571429" y="582.7142857142857"/></g>
  <g id="765">
   <use class="kv110" height="30" transform="rotate(0,1052.49,73.0355) scale(1.33333,-1.33333) translate(-258.121,-122.812)" width="30" x="1032.485447210151" xlink:href="#Accessory:PT789_0" y="53.03546420882998" zvalue="1191"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450347991045" ObjectName="110kV景琪线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1052.49,73.0355) scale(1.33333,-1.33333) translate(-258.121,-122.812)" width="30" x="1032.485447210151" y="53.03546420882998"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="511">
   <path class="kv10" d="M 556.32 582.74 L 556.32 594.21" stroke-width="1" zvalue="960"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="512@0" LinkObjectIDznd="513@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 556.32 582.74 L 556.32 594.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="510">
   <path class="kv10" d="M 556.32 628.21 L 556.32 655.02" stroke-width="1" zvalue="962"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="513@0" LinkObjectIDznd="559@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 556.32 628.21 L 556.32 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="497">
   <path class="kv10" d="M 1501.57 609.92 L 1501.57 655.02" stroke-width="1" zvalue="981"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="503@1" LinkObjectIDznd="505@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1501.57 609.92 L 1501.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="492">
   <path class="kv110" d="M 1219.57 342.21 L 1219.57 305.31" stroke-width="1" zvalue="989"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="494@0" LinkObjectIDznd="560@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1219.57 342.21 L 1219.57 305.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="491">
   <path class="kv110" d="M 1219.57 376.21 L 1219.57 409.22" stroke-width="1" zvalue="990"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="494@1" LinkObjectIDznd="493@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1219.57 376.21 L 1219.57 409.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="488">
   <path class="kv10" d="M 1778.32 586.44 L 1778.32 595.21" stroke-width="1" zvalue="994"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="489@0" LinkObjectIDznd="490@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1778.32 586.44 L 1778.32 595.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="487">
   <path class="kv10" d="M 1778.32 629.21 L 1778.32 655.02" stroke-width="1" zvalue="996"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="490@0" LinkObjectIDznd="505@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1778.32 629.21 L 1778.32 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="416">
   <path class="kv10" d="M 979.57 583.86 L 979.57 558.21 L 1104.57 558.21 L 1104.57 586.21" stroke-width="1" zvalue="1102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="419@0" LinkObjectIDznd="418@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 979.57 583.86 L 979.57 558.21 L 1104.57 558.21 L 1104.57 586.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="415">
   <path class="kv10" d="M 1104.57 620.21 L 1104.57 655.02" stroke-width="1" zvalue="1103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="418@1" LinkObjectIDznd="505@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1104.57 620.21 L 1104.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="413">
   <path class="kv10" d="M 1065.57 584.28 L 1065.57 558.21" stroke-width="1" zvalue="1105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@0" LinkObjectIDznd="416" MaxPinNum="2"/>
   </metadata>
  <path d="M 1065.57 584.28 L 1065.57 558.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv110" d="M 1115.19 194.27 L 1115.19 153.69" stroke-width="1" zvalue="1177"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="681@0" LinkObjectIDznd="680@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1115.19 194.27 L 1115.19 153.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv110" d="M 1132.89 172.83 L 1115.19 172.83" stroke-width="1" zvalue="1182"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="676@0" LinkObjectIDznd="16" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.89 172.83 L 1115.19 172.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv110" d="M 1115.18 284.96 L 1115.18 305.31" stroke-width="1" zvalue="1185"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="677@1" LinkObjectIDznd="560@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1115.18 284.96 L 1115.18 305.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv110" d="M 1115.35 220.16 L 1115.35 265.33" stroke-width="1" zvalue="1186"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="681@1" LinkObjectIDznd="677@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1115.35 220.16 L 1115.35 265.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv110" d="M 1132.89 244.87 L 1115.35 244.87" stroke-width="1" zvalue="1187"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="674@0" LinkObjectIDznd="12" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.89 244.87 L 1115.35 244.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv110" d="M 1132.89 117.59 L 1115.17 117.59" stroke-width="1" zvalue="1188"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="679@0" LinkObjectIDznd="9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.89 117.59 L 1115.17 117.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="kv110" d="M 1115.17 75.18 L 1115.17 134.06" stroke-width="1" zvalue="1189"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="682@0" LinkObjectIDznd="680@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1115.17 75.18 L 1115.17 134.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv110" d="M 1052.42 92.57 L 1052.49 102.62 L 1067.15 102.62" stroke-width="1" zvalue="1195"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="765@0" LinkObjectIDznd="766@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1052.42 92.57 L 1052.49 102.62 L 1067.15 102.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv110" d="M 1090.99 102.65 L 1115.17 102.65" stroke-width="1" zvalue="1196"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="766@1" LinkObjectIDznd="9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1090.99 102.65 L 1115.17 102.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv110" d="M 1235.25 326.54 L 1219.57 326.54" stroke-width="1" zvalue="1199"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="23@0" LinkObjectIDznd="492" MaxPinNum="2"/>
   </metadata>
  <path d="M 1235.25 326.54 L 1219.57 326.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv110" d="M 1200.38 387.42 L 1219.57 387.42" stroke-width="1" zvalue="1202"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@0" LinkObjectIDznd="491" MaxPinNum="2"/>
   </metadata>
  <path d="M 1200.38 387.42 L 1219.57 387.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv110" d="M 1501.52 466.07 L 1586.91 466.07 L 1586.91 499.37" stroke-width="1" zvalue="1208"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@2" LinkObjectIDznd="31@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1501.52 466.07 L 1586.91 466.07 L 1586.91 499.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv10" d="M 1501.52 514.78 L 1501.57 569.77" stroke-width="1" zvalue="1213"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@1" LinkObjectIDznd="503@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1501.52 514.78 L 1501.57 569.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv110" d="M 1501.42 351.66 L 1501.42 332.04" stroke-width="1" zvalue="1221"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="42@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1501.42 351.66 L 1501.42 332.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv110" d="M 1500.6 312.4 L 1500.6 305.31" stroke-width="1" zvalue="1222"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@0" LinkObjectIDznd="560@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1500.6 312.4 L 1500.6 305.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv110" d="M 1520.39 339.79 L 1501.42 339.79" stroke-width="1" zvalue="1225"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="43" MaxPinNum="2"/>
   </metadata>
  <path d="M 1520.39 339.79 L 1501.42 339.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv110" d="M 1521.68 396.65 L 1501.31 396.65" stroke-width="1" zvalue="1228"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="58" MaxPinNum="2"/>
   </metadata>
  <path d="M 1521.68 396.65 L 1501.31 396.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv110" d="M 1501.53 450.98 L 1501.53 425.32" stroke-width="1" zvalue="1231"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@0" LinkObjectIDznd="53@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1501.53 450.98 L 1501.53 425.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv110" d="M 1501.31 405.69 L 1501.31 377.55" stroke-width="1" zvalue="1232"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="38@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1501.31 405.69 L 1501.31 377.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv110" d="M 1522.97 435.79 L 1501.57 435.79" stroke-width="1" zvalue="1235"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="57" MaxPinNum="2"/>
   </metadata>
  <path d="M 1522.97 435.79 L 1501.57 435.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv10" d="M 904.43 609.26 L 904.43 655.02" stroke-width="1" zvalue="1270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@1" LinkObjectIDznd="559@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 904.43 609.26 L 904.43 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="kv110" d="M 904.38 466.12 L 989.77 466.12 L 989.77 499.42" stroke-width="1" zvalue="1275"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@2" LinkObjectIDznd="117@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 904.38 466.12 L 989.77 466.12 L 989.77 499.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv10" d="M 904.38 514.83 L 904.43 569.11" stroke-width="1" zvalue="1276"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@1" LinkObjectIDznd="120@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 904.38 514.83 L 904.43 569.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv110" d="M 904.27 351.71 L 904.27 332.09" stroke-width="1" zvalue="1280"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="113@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 904.27 351.71 L 904.27 332.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv110" d="M 903.45 312.46 L 903.45 305.31" stroke-width="1" zvalue="1282"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="560@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 903.45 312.46 L 903.45 305.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv110" d="M 923.25 339.85 L 904.27 339.85" stroke-width="1" zvalue="1284"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="112" MaxPinNum="2"/>
   </metadata>
  <path d="M 923.25 339.85 L 904.27 339.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="kv110" d="M 924.54 396.7 L 904.17 396.7" stroke-width="1" zvalue="1287"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="104" MaxPinNum="2"/>
   </metadata>
  <path d="M 924.54 396.7 L 904.17 396.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv110" d="M 904.39 451.03 L 904.39 425.37" stroke-width="1" zvalue="1290"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@0" LinkObjectIDznd="106@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 904.39 451.03 L 904.39 425.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv110" d="M 904.17 405.74 L 904.17 377.6" stroke-width="1" zvalue="1292"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@0" LinkObjectIDznd="114@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 904.17 405.74 L 904.17 377.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv110" d="M 925.82 435.85 L 904.39 435.85" stroke-width="1" zvalue="1295"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="105" MaxPinNum="2"/>
   </metadata>
  <path d="M 925.82 435.85 L 904.39 435.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv10" d="M 457 715.32 L 457 655.02" stroke-width="1" zvalue="1300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="559@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 457 715.32 L 457 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv10" d="M 457 856.22 L 457 755.47" stroke-width="1" zvalue="1301"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@0" LinkObjectIDznd="129@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 457 856.22 L 457 755.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv10" d="M 484.05 794.17 L 484.05 786.47 L 457 786.47" stroke-width="1" zvalue="1302"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@0" LinkObjectIDznd="126" MaxPinNum="2"/>
   </metadata>
  <path d="M 484.05 794.17 L 484.05 786.47 L 457 786.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv10" d="M 526 715.8 L 526 655.02" stroke-width="1" zvalue="1309"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="559@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 526 715.8 L 526 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv10" d="M 526 856.22 L 526 755.95" stroke-width="1" zvalue="1311"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@0" LinkObjectIDznd="146@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 526 856.22 L 526 755.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv10" d="M 553.05 794.65 L 553.05 786.95 L 526 786.95" stroke-width="1" zvalue="1312"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="136" MaxPinNum="2"/>
   </metadata>
  <path d="M 553.05 794.65 L 553.05 786.95 L 526 786.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="kv10" d="M 603 718.28 L 603 655.02" stroke-width="1" zvalue="1319"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="156@0" LinkObjectIDznd="559@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 603 718.28 L 603 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv10" d="M 603 856.22 L 603 758.43" stroke-width="1" zvalue="1321"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@0" LinkObjectIDznd="156@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 603 856.22 L 603 758.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv10" d="M 630.05 797.13 L 630.05 789.44 L 603 789.44" stroke-width="1" zvalue="1322"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="153" MaxPinNum="2"/>
   </metadata>
  <path d="M 630.05 797.13 L 630.05 789.44 L 603 789.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv10" d="M 669 720.76 L 669 655.02" stroke-width="1" zvalue="1329"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="559@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 669 720.76 L 669 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv10" d="M 669 856.22 L 669 760.91" stroke-width="1" zvalue="1330"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 669 856.22 L 669 760.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="kv10" d="M 696.05 799.61 L 696.05 791.92 L 669 791.92" stroke-width="1" zvalue="1331"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@0" LinkObjectIDznd="162" MaxPinNum="2"/>
   </metadata>
  <path d="M 696.05 799.61 L 696.05 791.92 L 669 791.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv10" d="M 749.5 803.68 L 749.5 707.82" stroke-width="1" zvalue="1344"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="175@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 749.5 803.68 L 749.5 707.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="184">
   <path class="kv10" d="M 749.99 685.2 L 749.99 655.02" stroke-width="1" zvalue="1345"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@0" LinkObjectIDznd="559@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 749.99 685.2 L 749.99 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv10" d="M 770.03 762 L 770.03 748.5 L 749.5 748.5" stroke-width="1" zvalue="1348"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="177" MaxPinNum="2"/>
   </metadata>
  <path d="M 770.03 762 L 770.03 748.5 L 749.5 748.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="205">
   <path class="kv10" d="M 844.24 683.81 L 844.24 655.02" stroke-width="1" zvalue="1359"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="992@1" LinkObjectIDznd="559@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 844.24 683.81 L 844.24 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv10" d="M 844.24 715.75 L 844.24 787.19" stroke-width="1" zvalue="1360"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="992@0" LinkObjectIDznd="995@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 844.24 715.75 L 844.24 787.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv10" d="M 843.28 806.82 L 843.28 841.99" stroke-width="1" zvalue="1365"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="995@0" LinkObjectIDznd="224@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 843.28 806.82 L 843.28 841.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="kv10" d="M 878.23 730.27 L 878.23 722.5 L 844.24 722.5" stroke-width="1" zvalue="1372"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@0" LinkObjectIDznd="201" MaxPinNum="2"/>
   </metadata>
  <path d="M 878.23 730.27 L 878.23 722.5 L 844.24 722.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="kv10" d="M 832.25 827.62 L 843.28 827.62" stroke-width="1" zvalue="1377"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="227@0" LinkObjectIDznd="195" MaxPinNum="2"/>
   </metadata>
  <path d="M 832.25 827.62 L 843.28 827.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="237">
   <path class="kv10" d="M 929 705.51 L 929 655.02" stroke-width="1" zvalue="1383"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="239@0" LinkObjectIDznd="559@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 929 705.51 L 929 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="235">
   <path class="kv10" d="M 928.82 856.22 L 928.82 745.66" stroke-width="1" zvalue="1384"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@0" LinkObjectIDznd="239@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 928.82 856.22 L 928.82 745.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="234">
   <path class="kv10" d="M 955.87 805.27 L 955.87 797.86 L 928.82 797.86" stroke-width="1" zvalue="1385"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="238@0" LinkObjectIDznd="235" MaxPinNum="2"/>
   </metadata>
  <path d="M 955.87 805.27 L 955.87 797.86 L 928.82 797.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="kv10" d="M 979.57 624.01 L 979.57 655.02" stroke-width="1" zvalue="1388"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="419@1" LinkObjectIDznd="559@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 979.57 624.01 L 979.57 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="256">
   <path class="kv10" d="M 1091.36 700.06 L 1091.36 655.02" stroke-width="1" zvalue="1394"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="258@0" LinkObjectIDznd="505@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1091.36 700.06 L 1091.36 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="254">
   <path class="kv10" d="M 1093.18 856.22 L 1093.18 740.21" stroke-width="1" zvalue="1395"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@0" LinkObjectIDznd="258@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1093.18 856.22 L 1093.18 740.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv10" d="M 1120.23 799.82 L 1120.23 792.4 L 1093.18 792.4" stroke-width="1" zvalue="1396"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="257@0" LinkObjectIDznd="254" MaxPinNum="2"/>
   </metadata>
  <path d="M 1120.23 799.82 L 1120.23 792.4 L 1093.18 792.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="265">
   <path class="kv10" d="M 1154.82 702.99 L 1154.82 655.02" stroke-width="1" zvalue="1404"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@0" LinkObjectIDznd="505@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1154.82 702.99 L 1154.82 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="264">
   <path class="kv10" d="M 1156.64 856.22 L 1156.64 743.14" stroke-width="1" zvalue="1405"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="262@0" LinkObjectIDznd="267@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1156.64 856.22 L 1156.64 743.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="263">
   <path class="kv10" d="M 1183.69 802.75 L 1183.69 795.34 L 1156.64 795.34" stroke-width="1" zvalue="1406"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="266@0" LinkObjectIDznd="264" MaxPinNum="2"/>
   </metadata>
  <path d="M 1183.69 802.75 L 1183.69 795.34 L 1156.64 795.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="274">
   <path class="kv10" d="M 1219.18 705.93 L 1219.18 655.02" stroke-width="1" zvalue="1414"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="277@0" LinkObjectIDznd="505@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1219.18 705.93 L 1219.18 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="273">
   <path class="kv10" d="M 1221 856.22 L 1221 746.08" stroke-width="1" zvalue="1415"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@0" LinkObjectIDznd="277@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1221 856.22 L 1221 746.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="272">
   <path class="kv10" d="M 1248.05 805.69 L 1248.05 798.28 L 1221 798.28" stroke-width="1" zvalue="1416"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="273" MaxPinNum="2"/>
   </metadata>
  <path d="M 1248.05 805.69 L 1248.05 798.28 L 1221 798.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="289">
   <path class="kv10" d="M 1289.18 707.05 L 1289.18 655.02" stroke-width="1" zvalue="1424"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="293@0" LinkObjectIDznd="505@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1289.18 707.05 L 1289.18 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="288">
   <path class="kv10" d="M 1291 856.22 L 1291 747.2" stroke-width="1" zvalue="1425"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="285@0" LinkObjectIDznd="293@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1291 856.22 L 1291 747.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="287">
   <path class="kv10" d="M 1318.05 806.81 L 1318.05 799.39 L 1291 799.39" stroke-width="1" zvalue="1426"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="290@0" LinkObjectIDznd="288" MaxPinNum="2"/>
   </metadata>
  <path d="M 1318.05 806.81 L 1318.05 799.39 L 1291 799.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="300">
   <path class="kv10" d="M 1368.95 806.61 L 1368.95 710.75" stroke-width="1" zvalue="1434"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="302@0" LinkObjectIDznd="301@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1368.95 806.61 L 1368.95 710.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="297">
   <path class="kv10" d="M 1389.48 764.94 L 1389.48 751.44 L 1368.95 751.44" stroke-width="1" zvalue="1437"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="298@0" LinkObjectIDznd="300" MaxPinNum="2"/>
   </metadata>
  <path d="M 1389.48 764.94 L 1389.48 751.44 L 1368.95 751.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="316">
   <path class="kv10" d="M 1451.36 684.02 L 1451.36 655.02" stroke-width="1" zvalue="1447"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="318@1" LinkObjectIDznd="505@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1451.36 684.02 L 1451.36 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="315">
   <path class="kv10" d="M 1451.36 715.96 L 1451.28 788.31" stroke-width="1" zvalue="1448"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="318@0" LinkObjectIDznd="320@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1451.36 715.96 L 1451.28 788.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="314">
   <path class="kv10" d="M 1451.3 807.94 L 1451.3 843.11" stroke-width="1" zvalue="1449"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="320@0" LinkObjectIDznd="311@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1451.3 807.94 L 1451.3 843.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="312">
   <path class="kv10" d="M 1486.25 731.39 L 1486.25 723.62 L 1451.35 723.62" stroke-width="1" zvalue="1451"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="313@0" LinkObjectIDznd="315" MaxPinNum="2"/>
   </metadata>
  <path d="M 1486.25 731.39 L 1486.25 723.62 L 1451.35 723.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="309">
   <path class="kv10" d="M 1440.27 828.74 L 1451.3 828.74" stroke-width="1" zvalue="1456"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="310@0" LinkObjectIDznd="314" MaxPinNum="2"/>
   </metadata>
  <path d="M 1440.27 828.74 L 1451.3 828.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="327">
   <path class="kv10" d="M 1526.27 709.98 L 1526.27 655.02" stroke-width="1" zvalue="1463"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="329@0" LinkObjectIDznd="505@10" MaxPinNum="2"/>
   </metadata>
  <path d="M 1526.27 709.98 L 1526.27 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="326">
   <path class="kv10" d="M 1528.09 856.22 L 1528.09 750.13" stroke-width="1" zvalue="1464"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="324@0" LinkObjectIDznd="329@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1528.09 856.22 L 1528.09 750.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="325">
   <path class="kv10" d="M 1555.14 809.74 L 1555.14 802.33 L 1528.09 802.33" stroke-width="1" zvalue="1465"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="328@0" LinkObjectIDznd="326" MaxPinNum="2"/>
   </metadata>
  <path d="M 1555.14 809.74 L 1555.14 802.33 L 1528.09 802.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="565">
   <path class="kv10" d="M 1603.55 712.01 L 1603.55 655.02" stroke-width="1" zvalue="1473"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="567@0" LinkObjectIDznd="505@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1603.55 712.01 L 1603.55 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="564">
   <path class="kv10" d="M 1605.36 856.22 L 1605.36 752.16" stroke-width="1" zvalue="1474"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="562@0" LinkObjectIDznd="567@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1605.36 856.22 L 1605.36 752.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="563">
   <path class="kv10" d="M 1632.41 811.77 L 1632.41 804.35 L 1605.36 804.35" stroke-width="1" zvalue="1475"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="566@0" LinkObjectIDznd="564" MaxPinNum="2"/>
   </metadata>
  <path d="M 1632.41 811.77 L 1632.41 804.35 L 1605.36 804.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="574">
   <path class="kv10" d="M 1677.91 714.03 L 1677.91 655.02" stroke-width="1" zvalue="1483"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="576@0" LinkObjectIDznd="505@11" MaxPinNum="2"/>
   </metadata>
  <path d="M 1677.91 714.03 L 1677.91 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="573">
   <path class="kv10" d="M 1679.73 856.22 L 1679.73 754.18" stroke-width="1" zvalue="1484"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="571@0" LinkObjectIDznd="576@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1679.73 856.22 L 1679.73 754.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="572">
   <path class="kv10" d="M 1706.78 813.79 L 1706.78 806.38 L 1679.73 806.38" stroke-width="1" zvalue="1485"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="575@0" LinkObjectIDznd="573" MaxPinNum="2"/>
   </metadata>
  <path d="M 1706.78 813.79 L 1706.78 806.38 L 1679.73 806.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="582">
   <path class="kv10" d="M 1750.09 713.33 L 1750.09 655.02" stroke-width="1" zvalue="1492"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="584@0" LinkObjectIDznd="505@12" MaxPinNum="2"/>
   </metadata>
  <path d="M 1750.09 713.33 L 1750.09 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="581">
   <path class="kv10" d="M 1751.91 856.22 L 1751.91 753.48" stroke-width="1" zvalue="1493"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="579@0" LinkObjectIDznd="584@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1751.91 856.22 L 1751.91 753.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="580">
   <path class="kv10" d="M 1778.96 813.09 L 1778.96 805.68 L 1751.91 805.68" stroke-width="1" zvalue="1494"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="583@0" LinkObjectIDznd="581" MaxPinNum="2"/>
   </metadata>
  <path d="M 1778.96 813.09 L 1778.96 805.68 L 1751.91 805.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="585">
   <path class="kv10" d="M 1369.44 688.14 L 1369.44 655.02" stroke-width="1" zvalue="1497"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="301@0" LinkObjectIDznd="505@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1369.44 688.14 L 1369.44 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="591">
   <path class="kv10" d="M 1819.61 714.56 L 1819.61 655.02" stroke-width="1" zvalue="1502"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="593@0" LinkObjectIDznd="505@13" MaxPinNum="2"/>
   </metadata>
  <path d="M 1819.61 714.56 L 1819.61 655.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="590">
   <path class="kv10" d="M 1821.43 856.22 L 1821.43 754.71" stroke-width="1" zvalue="1503"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="588@0" LinkObjectIDznd="593@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1821.43 856.22 L 1821.43 754.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="589">
   <path class="kv10" d="M 1848.48 814.32 L 1848.48 806.91 L 1821.43 806.91" stroke-width="1" zvalue="1504"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="592@0" LinkObjectIDznd="590" MaxPinNum="2"/>
   </metadata>
  <path d="M 1848.48 814.32 L 1848.48 806.91 L 1821.43 806.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="503">
   <use class="kv10" height="20" transform="rotate(0,1501.57,590.123) scale(2.2,2.2) translate(-813.039,-309.885)" width="10" x="1490.571428571429" xlink:href="#Breaker:小车断路器_0" y="568.1233766233767" zvalue="973"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924598169604" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473924598169604"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1501.57,590.123) scale(2.2,2.2) translate(-813.039,-309.885)" width="10" x="1490.571428571429" y="568.1233766233767"/></g>
  <g id="419">
   <use class="kv10" height="20" transform="rotate(0,979.571,604.214) scale(2.2,2.2) translate(-528.312,-317.571)" width="10" x="968.5714285714289" xlink:href="#Breaker:小车断路器_0" y="582.2142857142857" zvalue="1097"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924598104068" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473924598104068"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,979.571,604.214) scale(2.2,2.2) translate(-528.312,-317.571)" width="10" x="968.5714285714289" y="582.2142857142857"/></g>
  <g id="681">
   <use class="kv110" height="20" transform="rotate(0,1115.24,207.229) scale(1.5542,1.35421) translate(-394.905,-50.6612)" width="10" x="1107.472844755827" xlink:href="#Breaker:开关_0" y="193.6868882669556" zvalue="1170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924598235140" ObjectName="110kV景琪线151断路器"/>
   <cge:TPSR_Ref TObjectID="6473924598235140"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1115.24,207.229) scale(1.5542,1.35421) translate(-394.905,-50.6612)" width="10" x="1107.472844755827" y="193.6868882669556"/></g>
  <g id="38">
   <use class="kv110" height="20" transform="rotate(0,1501.47,364.614) scale(1.5542,1.35421) translate(-532.625,-91.8271)" width="10" x="1493.696818484176" xlink:href="#Breaker:开关_0" y="351.0714285714286" zvalue="1216"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924598300676" ObjectName="#2主变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473924598300676"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1501.47,364.614) scale(1.5542,1.35421) translate(-532.625,-91.8271)" width="10" x="1493.696818484176" y="351.0714285714286"/></g>
  <g id="120">
   <use class="kv10" height="20" transform="rotate(0,904.429,589.461) scale(2.2,2.2) translate(-487.325,-309.524)" width="10" x="893.4285714285718" xlink:href="#Breaker:小车断路器_0" y="567.4610389610391" zvalue="1267"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924598431748" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924598431748"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,904.429,589.461) scale(2.2,2.2) translate(-487.325,-309.524)" width="10" x="893.4285714285718" y="567.4610389610391"/></g>
  <g id="114">
   <use class="kv110" height="20" transform="rotate(0,904.325,364.665) scale(1.5542,1.35421) translate(-319.695,-91.8407)" width="10" x="896.5539613413189" xlink:href="#Breaker:开关_0" y="351.1233766233767" zvalue="1277"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924598366212" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473924598366212"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,904.325,364.665) scale(1.5542,1.35421) translate(-319.695,-91.8407)" width="10" x="896.5539613413189" y="351.1233766233767"/></g>
  <g id="129">
   <use class="kv10" height="20" transform="rotate(0,457,735.669) scale(2.2,2.2) translate(-243.273,-389.274)" width="10" x="446" xlink:href="#Breaker:小车断路器_0" y="713.66884999461" zvalue="1297"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924598497284" ObjectName="Ⅰ-1S循环水线051断路器"/>
   <cge:TPSR_Ref TObjectID="6473924598497284"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,457,735.669) scale(2.2,2.2) translate(-243.273,-389.274)" width="10" x="446" y="713.66884999461"/></g>
  <g id="146">
   <use class="kv10" height="20" transform="rotate(0,526,736.149) scale(2.2,2.2) translate(-280.909,-389.536)" width="10" x="515" xlink:href="#Breaker:小车断路器_0" y="714.1493732402851" zvalue="1306"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924598562820" ObjectName="Ⅰ-2S老厂#1出线052断路器"/>
   <cge:TPSR_Ref TObjectID="6473924598562820"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,526,736.149) scale(2.2,2.2) translate(-280.909,-389.536)" width="10" x="515" y="714.1493732402851"/></g>
  <g id="156">
   <use class="kv10" height="20" transform="rotate(0,603,738.63) scale(2.2,2.2) translate(-322.909,-390.889)" width="10" x="592" xlink:href="#Breaker:小车断路器_0" y="716.6298964859602" zvalue="1316"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924598628356" ObjectName="Ⅰ-3S老厂#2出线053断路器"/>
   <cge:TPSR_Ref TObjectID="6473924598628356"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,603,738.63) scale(2.2,2.2) translate(-322.909,-390.889)" width="10" x="592" y="716.6298964859602"/></g>
  <g id="166">
   <use class="kv10" height="20" transform="rotate(0,669,741.11) scale(2.2,2.2) translate(-358.909,-392.242)" width="10" x="658" xlink:href="#Breaker:小车断路器_0" y="719.1104197316353" zvalue="1326"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924598693892" ObjectName="Ⅰ-4S备用线054断路器"/>
   <cge:TPSR_Ref TObjectID="6473924598693892"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,669,741.11) scale(2.2,2.2) translate(-358.909,-392.242)" width="10" x="658" y="719.1104197316353"/></g>
  <g id="992">
   <use class="kv10" height="20" transform="rotate(180,844.244,699.563) scale(2.00844,1.75) translate(-418.854,-292.313)" width="10" x="834.202007567329" xlink:href="#Breaker:小车断路器_0" y="682.0628051757811" zvalue="1354"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924598759428" ObjectName="#1电容器056断路器"/>
   <cge:TPSR_Ref TObjectID="6473924598759428"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,844.244,699.563) scale(2.00844,1.75) translate(-418.854,-292.313)" width="10" x="834.202007567329" y="682.0628051757811"/></g>
  <g id="239">
   <use class="kv10" height="20" transform="rotate(0,929,725.864) scale(2.2,2.2) translate(-500.727,-383.926)" width="10" x="918" xlink:href="#Breaker:小车断路器_0" y="703.8636702500376" zvalue="1379"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924598824964" ObjectName="Ⅰ-5S备用线057断路器"/>
   <cge:TPSR_Ref TObjectID="6473924598824964"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,929,725.864) scale(2.2,2.2) translate(-500.727,-383.926)" width="10" x="918" y="703.8636702500376"/></g>
  <g id="258">
   <use class="kv10" height="20" transform="rotate(0,1091.36,720.409) scale(2.2,2.2) translate(-589.289,-380.95)" width="10" x="1080.363636363636" xlink:href="#Breaker:小车断路器_0" y="698.4091247954922" zvalue="1390"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924598890500" ObjectName="Ⅱ-1S前工段线059断路器"/>
   <cge:TPSR_Ref TObjectID="6473924598890500"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1091.36,720.409) scale(2.2,2.2) translate(-589.289,-380.95)" width="10" x="1080.363636363636" y="698.4091247954922"/></g>
  <g id="267">
   <use class="kv10" height="20" transform="rotate(0,1154.82,723.344) scale(2.2,2.2) translate(-623.901,-382.551)" width="10" x="1143.818181818182" xlink:href="#Breaker:小车断路器_0" y="701.3441934957127" zvalue="1400"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924598956036" ObjectName="Ⅱ-2S后工段线61断路器"/>
   <cge:TPSR_Ref TObjectID="6473924598956036"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1154.82,723.344) scale(2.2,2.2) translate(-623.901,-382.551)" width="10" x="1143.818181818182" y="701.3441934957127"/></g>
  <g id="277">
   <use class="kv10" height="20" transform="rotate(0,1219.18,726.279) scale(2.2,2.2) translate(-659.008,-384.152)" width="10" x="1208.181818181818" xlink:href="#Breaker:小车断路器_0" y="704.2792621959334" zvalue="1410"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924599021572" ObjectName="Ⅱ-3S干燥车间线062断路器"/>
   <cge:TPSR_Ref TObjectID="6473924599021572"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1219.18,726.279) scale(2.2,2.2) translate(-659.008,-384.152)" width="10" x="1208.181818181818" y="704.2792621959334"/></g>
  <g id="293">
   <use class="kv10" height="20" transform="rotate(0,1289.18,727.396) scale(2.2,2.2) translate(-697.19,-384.762)" width="10" x="1278.181818181818" xlink:href="#Breaker:小车断路器_0" y="705.396149077972" zvalue="1420"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924599087108" ObjectName="Ⅱ-4S环保车间MVR#1线063断路器"/>
   <cge:TPSR_Ref TObjectID="6473924599087108"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1289.18,727.396) scale(2.2,2.2) translate(-697.19,-384.762)" width="10" x="1278.181818181818" y="705.396149077972"/></g>
  <g id="318">
   <use class="kv10" height="20" transform="rotate(180,1451.36,699.771) scale(2.00844,1.75) translate(-723.686,-292.402)" width="10" x="1441.315643930966" xlink:href="#Breaker:小车断路器_0" y="682.270601148729" zvalue="1444"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924599152644" ObjectName="#2电容器065断路器"/>
   <cge:TPSR_Ref TObjectID="6473924599152644"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1451.36,699.771) scale(2.00844,1.75) translate(-723.686,-292.402)" width="10" x="1441.315643930966" y="682.270601148729"/></g>
  <g id="329">
   <use class="kv10" height="20" transform="rotate(0,1526.27,730.331) scale(2.2,2.2) translate(-826.512,-386.362)" width="10" x="1515.272727272727" xlink:href="#Breaker:小车断路器_0" y="708.3312177781925" zvalue="1459"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924599218180" ObjectName="Ⅱ-5S环保车间MVR#2线066断路器"/>
   <cge:TPSR_Ref TObjectID="6473924599218180"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1526.27,730.331) scale(2.2,2.2) translate(-826.512,-386.362)" width="10" x="1515.272727272727" y="708.3312177781925"/></g>
  <g id="567">
   <use class="kv10" height="20" transform="rotate(0,1603.55,732.357) scale(2.2,2.2) translate(-868.661,-387.468)" width="10" x="1592.545454545455" xlink:href="#Breaker:小车断路器_0" y="710.3571955693221" zvalue="1469"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924599283716" ObjectName="Ⅱ-6S环保生化线067断路器"/>
   <cge:TPSR_Ref TObjectID="6473924599283716"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1603.55,732.357) scale(2.2,2.2) translate(-868.661,-387.468)" width="10" x="1592.545454545455" y="710.3571955693221"/></g>
  <g id="576">
   <use class="kv10" height="20" transform="rotate(0,1677.91,734.383) scale(2.2,2.2) translate(-909.223,-388.573)" width="10" x="1666.909090909091" xlink:href="#Breaker:小车断路器_0" y="712.3831733604519" zvalue="1479"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924599349252" ObjectName="Ⅱ-7S备用线068断路器"/>
   <cge:TPSR_Ref TObjectID="6473924599349252"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1677.91,734.383) scale(2.2,2.2) translate(-909.223,-388.573)" width="10" x="1666.909090909091" y="712.3831733604519"/></g>
  <g id="584">
   <use class="kv10" height="20" transform="rotate(0,1750.09,733.682) scale(2.2,2.2) translate(-948.595,-388.19)" width="10" x="1739.090909090909" xlink:href="#Breaker:小车断路器_0" y="711.6818784243088" zvalue="1489"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924599414788" ObjectName="Ⅱ-8S备用线069断路器"/>
   <cge:TPSR_Ref TObjectID="6473924599414788"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1750.09,733.682) scale(2.2,2.2) translate(-948.595,-388.19)" width="10" x="1739.090909090909" y="711.6818784243088"/></g>
  <g id="593">
   <use class="kv10" height="20" transform="rotate(0,1819.61,734.912) scale(2.2,2.2) translate(-986.517,-388.861)" width="10" x="1808.613636363636" xlink:href="#Breaker:小车断路器_0" y="712.912401669984" zvalue="1499"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924599480324" ObjectName="Ⅱ-9S备用线071断路器"/>
   <cge:TPSR_Ref TObjectID="6473924599480324"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1819.61,734.912) scale(2.2,2.2) translate(-986.517,-388.861)" width="10" x="1808.613636363636" y="712.912401669984"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="679">
   <use class="kv110" height="20" transform="rotate(90,1142.71,117.523) scale(1.24619,-1.0068) translate(-224.513,-234.185)" width="10" x="1136.476524870565" xlink:href="#GroundDisconnector:地刀_0" y="107.4554202210742" zvalue="1175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450347794437" ObjectName="110kV景琪线15167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450347794437"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1142.71,117.523) scale(1.24619,-1.0068) translate(-224.513,-234.185)" width="10" x="1136.476524870565" y="107.4554202210742"/></g>
  <g id="676">
   <use class="kv110" height="20" transform="rotate(90,1142.71,172.769) scale(1.24619,-1.0068) translate(-224.513,-344.303)" width="10" x="1136.476524870565" xlink:href="#GroundDisconnector:地刀_0" y="162.7012087952933" zvalue="1180"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450347597829" ObjectName="110kV景琪线15160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450347597829"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1142.71,172.769) scale(1.24619,-1.0068) translate(-224.513,-344.303)" width="10" x="1136.476524870565" y="162.7012087952933"/></g>
  <g id="674">
   <use class="kv110" height="20" transform="rotate(90,1142.71,244.809) scale(1.24619,-1.0068) translate(-224.513,-487.897)" width="10" x="1136.476524870565" xlink:href="#GroundDisconnector:地刀_0" y="234.7411410462698" zvalue="1183"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450347466757" ObjectName="110kV景琪线15117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450347466757"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1142.71,244.809) scale(1.24619,-1.0068) translate(-224.513,-487.897)" width="10" x="1136.476524870565" y="234.7411410462698"/></g>
  <g id="23">
   <use class="kv110" height="20" transform="rotate(90,1245.07,326.481) scale(1.24619,-1.0068) translate(-244.735,-650.689)" width="10" x="1238.837072725879" xlink:href="#GroundDisconnector:地刀_0" y="316.412927274121" zvalue="1198"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450348122117" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450348122117"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1245.07,326.481) scale(1.24619,-1.0068) translate(-244.735,-650.689)" width="10" x="1238.837072725879" y="316.412927274121"/></g>
  <g id="26">
   <use class="kv110" height="20" transform="rotate(270,1190.57,387.481) scale(1.24619,-1.0068) translate(-233.968,-772.277)" width="10" x="1184.337072725879" xlink:href="#GroundDisconnector:地刀_0" y="377.412927274121" zvalue="1201"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450348253189" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450348253189"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1190.57,387.481) scale(1.24619,-1.0068) translate(-233.968,-772.277)" width="10" x="1184.337072725879" y="377.412927274121"/></g>
  <g id="31">
   <use class="kv110" height="40" transform="rotate(0,1596.29,510.048) scale(0.7,-0.875) translate(678.126,-1095.46)" width="40" x="1582.293741049734" xlink:href="#GroundDisconnector:中性点地刀12_0" y="492.5478286207355" zvalue="1206"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450348384261" ObjectName="#2主变110kV侧1020中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450348384261"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1596.29,510.048) scale(0.7,-0.875) translate(678.126,-1095.46)" width="40" x="1582.293741049734" y="492.5478286207355"/></g>
  <g id="46">
   <use class="kv110" height="20" transform="rotate(90,1530.21,339.731) scale(1.24619,-1.0068) translate(-301.065,-677.099)" width="10" x="1523.979929868736" xlink:href="#GroundDisconnector:地刀_0" y="329.662927274121" zvalue="1224"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450348580869" ObjectName="#2主变110kV侧10217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450348580869"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1530.21,339.731) scale(1.24619,-1.0068) translate(-301.065,-677.099)" width="10" x="1523.979929868736" y="329.662927274121"/></g>
  <g id="49">
   <use class="kv110" height="20" transform="rotate(90,1531.5,396.588) scale(1.24619,-1.0068) translate(-301.319,-790.429)" width="10" x="1525.26564415445" xlink:href="#GroundDisconnector:地刀_0" y="386.5200701312639" zvalue="1227"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450348711941" ObjectName="#2主变110kV侧10260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450348711941"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1531.5,396.588) scale(1.24619,-1.0068) translate(-301.319,-790.429)" width="10" x="1525.26564415445" y="386.5200701312639"/></g>
  <g id="60">
   <use class="kv110" height="20" transform="rotate(90,1532.78,435.731) scale(1.24619,-1.0068) translate(-301.573,-868.451)" width="10" x="1526.551358440165" xlink:href="#GroundDisconnector:地刀_0" y="425.662927274121" zvalue="1234"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450348908549" ObjectName="#2主变110kV侧10267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450348908549"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1532.78,435.731) scale(1.24619,-1.0068) translate(-301.573,-868.451)" width="10" x="1526.551358440165" y="425.662927274121"/></g>
  <g id="117">
   <use class="kv110" height="40" transform="rotate(0,999.151,510.1) scale(0.7,-0.875) translate(422.208,-1095.57)" width="40" x="985.1508839068769" xlink:href="#GroundDisconnector:中性点地刀12_0" y="492.5997766726835" zvalue="1272"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450349563909" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450349563909"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,999.151,510.1) scale(0.7,-0.875) translate(422.208,-1095.57)" width="40" x="985.1508839068769" y="492.5997766726835"/></g>
  <g id="110">
   <use class="kv110" height="20" transform="rotate(90,933.068,339.783) scale(1.24619,-1.0068) translate(-183.099,-677.203)" width="10" x="926.8370727258791" xlink:href="#GroundDisconnector:地刀_0" y="329.7148753260691" zvalue="1283"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450349367301" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450349367301"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,933.068,339.783) scale(1.24619,-1.0068) translate(-183.099,-677.203)" width="10" x="926.8370727258791" y="329.7148753260691"/></g>
  <g id="108">
   <use class="kv110" height="20" transform="rotate(90,934.354,396.64) scale(1.24619,-1.0068) translate(-183.353,-790.533)" width="10" x="928.1227870115933" xlink:href="#GroundDisconnector:地刀_0" y="386.572018183212" zvalue="1286"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450349236229" ObjectName="#1主变110kV侧10160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450349236229"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,934.354,396.64) scale(1.24619,-1.0068) translate(-183.353,-790.533)" width="10" x="928.1227870115933" y="386.572018183212"/></g>
  <g id="103">
   <use class="kv110" height="20" transform="rotate(90,935.639,435.783) scale(1.24619,-1.0068) translate(-183.607,-868.554)" width="10" x="929.4085012973077" xlink:href="#GroundDisconnector:地刀_0" y="425.7148753260691" zvalue="1293"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450349039621" ObjectName="#1主变110kV侧10167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450349039621"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,935.639,435.783) scale(1.24619,-1.0068) translate(-183.607,-868.554)" width="10" x="929.4085012973077" y="425.7148753260691"/></g>
  <g id="128">
   <use class="kv10" height="20" transform="rotate(0,484,803.669) scale(1,1) translate(0,0)" width="10" x="479" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="793.6688499946099" zvalue="1299"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450349760517" ObjectName="Ⅰ-1S循环水线05167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450349760517"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,484,803.669) scale(1,1) translate(0,0)" width="10" x="479" y="793.6688499946099"/></g>
  <g id="145">
   <use class="kv10" height="20" transform="rotate(0,553,804.149) scale(1,1) translate(0,0)" width="10" x="548" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="794.1493732402851" zvalue="1308"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450349957125" ObjectName="Ⅰ-2S老厂#1出线05267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450349957125"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,553,804.149) scale(1,1) translate(0,0)" width="10" x="548" y="794.1493732402851"/></g>
  <g id="155">
   <use class="kv10" height="20" transform="rotate(0,630,806.63) scale(1,1) translate(0,0)" width="10" x="625" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="796.6298964859602" zvalue="1318"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450350153733" ObjectName="Ⅰ-3S老厂#2出线05367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450350153733"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,630,806.63) scale(1,1) translate(0,0)" width="10" x="625" y="796.6298964859602"/></g>
  <g id="164">
   <use class="kv10" height="20" transform="rotate(0,696,809.11) scale(1,1) translate(0,0)" width="10" x="691" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="799.1104197316354" zvalue="1328"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450350350341" ObjectName="Ⅰ-4S备用线05467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450350350341"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,696,809.11) scale(1,1) translate(0,0)" width="10" x="691" y="799.1104197316354"/></g>
  <g id="186">
   <use class="kv10" height="20" transform="rotate(0,770,771.5) scale(0.55,1) translate(627.75,0)" width="10" x="767.25" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="761.5" zvalue="1347"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450350612485" ObjectName="#1站用变05517接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450350612485"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,770,771.5) scale(0.55,1) translate(627.75,0)" width="10" x="767.25" y="761.5"/></g>
  <g id="220">
   <use class="kv10" height="20" transform="rotate(0,878.205,739.773) scale(0.55,1) translate(716.281,0)" width="10" x="875.4545454545455" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="729.7727272727273" zvalue="1371"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450350809093" ObjectName="#1电容器05667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450350809093"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,878.205,739.773) scale(0.55,1) translate(716.281,0)" width="10" x="875.4545454545455" y="729.7727272727273"/></g>
  <g id="227">
   <use class="kv10" height="20" transform="rotate(90,822.75,827.591) scale(0.55,1) translate(670.909,0)" width="10" x="820" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="817.5909090909091" zvalue="1376"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450351005701" ObjectName="#1电容器05687接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450351005701"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,822.75,827.591) scale(0.55,1) translate(670.909,0)" width="10" x="820" y="817.5909090909091"/></g>
  <g id="238">
   <use class="kv10" height="20" transform="rotate(0,955.818,814.773) scale(1,1) translate(0,0)" width="10" x="950.8181818181819" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="804.7727611591285" zvalue="1381"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450351202309" ObjectName="Ⅰ-5S备用线05767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450351202309"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,955.818,814.773) scale(1,1) translate(0,0)" width="10" x="950.8181818181819" y="804.7727611591285"/></g>
  <g id="257">
   <use class="kv10" height="20" transform="rotate(0,1120.18,809.318) scale(1,1) translate(0,0)" width="10" x="1115.181818181818" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="799.3182157045833" zvalue="1392"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450351398917" ObjectName="Ⅱ-1S前工段线05967接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450351398917"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1120.18,809.318) scale(1,1) translate(0,0)" width="10" x="1115.181818181818" y="799.3182157045833"/></g>
  <g id="266">
   <use class="kv10" height="20" transform="rotate(0,1183.64,812.253) scale(1,1) translate(0,0)" width="10" x="1178.636363636364" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="802.2532844048037" zvalue="1402"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450351595525" ObjectName="Ⅱ-2S后工段线06167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450351595525"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1183.64,812.253) scale(1,1) translate(0,0)" width="10" x="1178.636363636364" y="802.2532844048037"/></g>
  <g id="276">
   <use class="kv10" height="20" transform="rotate(0,1248,815.188) scale(1,1) translate(0,0)" width="10" x="1243" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="805.1883531050241" zvalue="1412"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450351792133" ObjectName="Ⅱ-3S干燥车间线06267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450351792133"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1248,815.188) scale(1,1) translate(0,0)" width="10" x="1243" y="805.1883531050241"/></g>
  <g id="290">
   <use class="kv10" height="20" transform="rotate(0,1318,816.305) scale(1,1) translate(0,0)" width="10" x="1313" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="806.3052399870629" zvalue="1422"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450351988741" ObjectName="Ⅱ-4S环保车间MVR#1线06367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450351988741"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1318,816.305) scale(1,1) translate(0,0)" width="10" x="1313" y="806.3052399870629"/></g>
  <g id="298">
   <use class="kv10" height="20" transform="rotate(0,1389.45,774.435) scale(0.55,1) translate(1134.58,0)" width="10" x="1386.704545454545" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="764.4350687002207" zvalue="1436"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450352119813" ObjectName="#2站用变06417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450352119813"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1389.45,774.435) scale(0.55,1) translate(1134.58,0)" width="10" x="1386.704545454545" y="764.4350687002207"/></g>
  <g id="313">
   <use class="kv10" height="20" transform="rotate(0,1486.23,740.89) scale(0.55,1) translate(1213.75,0)" width="10" x="1483.477272727273" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="730.889614154766" zvalue="1450"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450352578565" ObjectName="#2电容器06567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450352578565"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1486.23,740.89) scale(0.55,1) translate(1213.75,0)" width="10" x="1483.477272727273" y="730.889614154766"/></g>
  <g id="310">
   <use class="kv10" height="20" transform="rotate(90,1430.77,828.708) scale(0.55,1) translate(1168.38,0)" width="10" x="1428.022727272727" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="818.7077959729479" zvalue="1455"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450352381957" ObjectName="#2电容器06587接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450352381957"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1430.77,828.708) scale(0.55,1) translate(1168.38,0)" width="10" x="1428.022727272727" y="818.7077959729479"/></g>
  <g id="328">
   <use class="kv10" height="20" transform="rotate(0,1555.09,819.24) scale(1,1) translate(0,0)" width="10" x="1550.090909090909" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="809.2403086872835" zvalue="1461"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450352840709" ObjectName="Ⅱ-5S环保车间MVR#2线06667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450352840709"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1555.09,819.24) scale(1,1) translate(0,0)" width="10" x="1550.090909090909" y="809.2403086872835"/></g>
  <g id="566">
   <use class="kv10" height="20" transform="rotate(0,1632.36,821.266) scale(1,1) translate(0,0)" width="10" x="1627.363636363636" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="811.2662864784131" zvalue="1471"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450353037317" ObjectName="Ⅱ-6S环保生化线06767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450353037317"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1632.36,821.266) scale(1,1) translate(0,0)" width="10" x="1627.363636363636" y="811.2662864784131"/></g>
  <g id="575">
   <use class="kv10" height="20" transform="rotate(0,1706.73,823.292) scale(1,1) translate(0,0)" width="10" x="1701.727272727273" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="813.2922642695428" zvalue="1481"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450353233925" ObjectName="Ⅱ-7S备用线06867接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450353233925"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1706.73,823.292) scale(1,1) translate(0,0)" width="10" x="1701.727272727273" y="813.2922642695428"/></g>
  <g id="583">
   <use class="kv10" height="20" transform="rotate(0,1778.91,822.591) scale(1,1) translate(0,0)" width="10" x="1773.909090909091" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="812.5909693333996" zvalue="1491"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450353430533" ObjectName="Ⅱ-8S备用线06967接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450353430533"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1778.91,822.591) scale(1,1) translate(0,0)" width="10" x="1773.909090909091" y="812.5909693333996"/></g>
  <g id="592">
   <use class="kv10" height="20" transform="rotate(0,1848.43,823.821) scale(1,1) translate(0,0)" width="10" x="1843.431818181818" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="813.8214925790749" zvalue="1501"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450353627141" ObjectName="Ⅱ-9S备用线07167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450353627141"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1848.43,823.821) scale(1,1) translate(0,0)" width="10" x="1843.431818181818" y="813.8214925790749"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="226">
   <g id="2260">
    <use class="kv110" height="30" transform="rotate(0,1501.52,482.721) scale(2.16513,2.28139) translate(-795.203,-251.91)" width="22" x="1477.7" xlink:href="#PowerTransformer2:接地可调两卷变_0" y="448.5" zvalue="1205"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874466852867" ObjectName="110"/>
    </metadata>
   </g>
   <g id="2261">
    <use class="kv10" height="30" transform="rotate(0,1501.52,482.721) scale(2.16513,2.28139) translate(-795.203,-251.91)" width="22" x="1477.7" xlink:href="#PowerTransformer2:接地可调两卷变_1" y="448.5" zvalue="1205"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874466918403" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399464976387" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399464976387"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1501.52,482.721) scale(2.16513,2.28139) translate(-795.203,-251.91)" width="22" x="1477.7" y="448.5"/></g>
  <g id="118">
   <g id="1180">
    <use class="kv110" height="30" transform="rotate(0,904.376,482.773) scale(2.16513,2.28139) translate(-473.86,-251.939)" width="22" x="880.5599999999999" xlink:href="#PowerTransformer2:接地可调两卷变_0" y="448.55" zvalue="1271"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874466983939" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1181">
    <use class="kv10" height="30" transform="rotate(0,904.376,482.773) scale(2.16513,2.28139) translate(-473.86,-251.939)" width="22" x="880.5599999999999" xlink:href="#PowerTransformer2:接地可调两卷变_1" y="448.55" zvalue="1271"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874467049475" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399465041923" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399465041923"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,904.376,482.773) scale(2.16513,2.28139) translate(-473.86,-251.939)" width="22" x="880.5599999999999" y="448.55"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="124">
   <use class="kv10" height="30" transform="rotate(0,457,876.474) scale(3.75,-1.5) translate(-318.633,-1453.29)" width="12" x="434.5" xlink:href="#EnergyConsumer:负荷_0" y="853.9740600585938" zvalue="1303"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450349629445" ObjectName="Ⅰ-1S循环水线"/>
   <cge:TPSR_Ref TObjectID="6192450349629445"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,457,876.474) scale(3.75,-1.5) translate(-318.633,-1453.29)" width="12" x="434.5" y="853.9740600585938"/></g>
  <g id="134">
   <use class="kv10" height="30" transform="rotate(0,526,876.474) scale(3.75,-1.5) translate(-369.233,-1453.29)" width="12" x="503.5" xlink:href="#EnergyConsumer:负荷_0" y="853.9740600458213" zvalue="1313"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450349826053" ObjectName="Ⅰ-2S老厂#1出线"/>
   <cge:TPSR_Ref TObjectID="6192450349826053"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,526,876.474) scale(3.75,-1.5) translate(-369.233,-1453.29)" width="12" x="503.5" y="853.9740600458213"/></g>
  <g id="151">
   <use class="kv10" height="30" transform="rotate(0,603,876.474) scale(3.75,-1.5) translate(-425.7,-1453.29)" width="12" x="580.5" xlink:href="#EnergyConsumer:负荷_0" y="853.9740600926534" zvalue="1323"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450350022661" ObjectName="Ⅰ-3S老厂#2出线"/>
   <cge:TPSR_Ref TObjectID="6192450350022661"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,603,876.474) scale(3.75,-1.5) translate(-425.7,-1453.29)" width="12" x="580.5" y="853.9740600926534"/></g>
  <g id="159">
   <use class="kv10" height="30" transform="rotate(0,669,876.474) scale(3.75,-1.5) translate(-474.1,-1453.29)" width="12" x="646.5" xlink:href="#EnergyConsumer:负荷_0" y="853.974059990474" zvalue="1332"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450350219269" ObjectName="Ⅰ-4S备用线"/>
   <cge:TPSR_Ref TObjectID="6192450350219269"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,669,876.474) scale(3.75,-1.5) translate(-474.1,-1453.29)" width="12" x="646.5" y="853.974059990474"/></g>
  <g id="171">
   <use class="kv10" height="35" transform="rotate(0,749.5,821.617) scale(1.28125,1.28125) translate(-160.024,-175.433)" width="32" x="729" xlink:href="#EnergyConsumer:站用变13_0" y="799.1948089599609" zvalue="1338"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450350415877" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,749.5,821.617) scale(1.28125,1.28125) translate(-160.024,-175.433)" width="32" x="729" y="799.1948089599609"/></g>
  <g id="233">
   <use class="kv10" height="30" transform="rotate(0,928.818,876.474) scale(3.75,-1.5) translate(-664.633,-1453.29)" width="12" x="906.3181818181819" xlink:href="#EnergyConsumer:负荷_0" y="853.9740599099688" zvalue="1386"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450351071237" ObjectName="Ⅰ-5S备用线"/>
   <cge:TPSR_Ref TObjectID="6192450351071237"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,928.818,876.474) scale(3.75,-1.5) translate(-664.633,-1453.29)" width="12" x="906.3181818181819" y="853.9740599099688"/></g>
  <g id="248">
   <use class="kv10" height="30" transform="rotate(0,1093.18,876.474) scale(3.75,-1.5) translate(-785.167,-1453.29)" width="12" x="1070.681818181818" xlink:href="#EnergyConsumer:负荷_0" y="853.9740599533177" zvalue="1397"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450351267845" ObjectName="Ⅱ-1S前工段线"/>
   <cge:TPSR_Ref TObjectID="6192450351267845"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1093.18,876.474) scale(3.75,-1.5) translate(-785.167,-1453.29)" width="12" x="1070.681818181818" y="853.9740599533177"/></g>
  <g id="262">
   <use class="kv10" height="30" transform="rotate(0,1156.64,876.474) scale(3.75,-1.5) translate(-831.7,-1453.29)" width="12" x="1134.136363636364" xlink:href="#EnergyConsumer:负荷_0" y="853.9740600462079" zvalue="1407"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450351464453" ObjectName="Ⅱ-2S后工段线"/>
   <cge:TPSR_Ref TObjectID="6192450351464453"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1156.64,876.474) scale(3.75,-1.5) translate(-831.7,-1453.29)" width="12" x="1134.136363636364" y="853.9740600462079"/></g>
  <g id="271">
   <use class="kv10" height="30" transform="rotate(0,1221,876.474) scale(3.75,-1.5) translate(-878.9,-1453.29)" width="12" x="1198.5" xlink:href="#EnergyConsumer:负荷_0" y="853.974059662261" zvalue="1417"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450351661061" ObjectName="Ⅱ-3S干燥车间线"/>
   <cge:TPSR_Ref TObjectID="6192450351661061"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1221,876.474) scale(3.75,-1.5) translate(-878.9,-1453.29)" width="12" x="1198.5" y="853.974059662261"/></g>
  <g id="285">
   <use class="kv10" height="30" transform="rotate(0,1291,876.474) scale(3.75,-1.5) translate(-930.233,-1453.29)" width="12" x="1268.5" xlink:href="#EnergyConsumer:负荷_0" y="853.9740604053836" zvalue="1427"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450351857669" ObjectName="Ⅱ-4S环保车间MVR#1线"/>
   <cge:TPSR_Ref TObjectID="6192450351857669"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1291,876.474) scale(3.75,-1.5) translate(-930.233,-1453.29)" width="12" x="1268.5" y="853.9740604053836"/></g>
  <g id="302">
   <use class="kv10" height="35" transform="rotate(0,1368.95,824.552) scale(1.28125,1.28125) translate(-296.002,-176.077)" width="32" x="1348.454545454545" xlink:href="#EnergyConsumer:站用变13_0" y="802.1298776601815" zvalue="1430"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450352250885" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1368.95,824.552) scale(1.28125,1.28125) translate(-296.002,-176.077)" width="32" x="1348.454545454545" y="802.1298776601815"/></g>
  <g id="324">
   <use class="kv10" height="30" transform="rotate(0,1528.09,876.474) scale(3.75,-1.5) translate(-1104.1,-1453.29)" width="12" x="1505.590909090909" xlink:href="#EnergyConsumer:负荷_0" y="853.9740600214367" zvalue="1466"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450352709637" ObjectName="Ⅱ-5S环保车间MVR#2线"/>
   <cge:TPSR_Ref TObjectID="6192450352709637"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1528.09,876.474) scale(3.75,-1.5) translate(-1104.1,-1453.29)" width="12" x="1505.590909090909" y="853.9740600214367"/></g>
  <g id="562">
   <use class="kv10" height="30" transform="rotate(0,1605.36,876.474) scale(3.75,-1.5) translate(-1160.77,-1453.29)" width="12" x="1582.863636363636" xlink:href="#EnergyConsumer:负荷_0" y="853.9740606778619" zvalue="1476"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450352906245" ObjectName="Ⅱ-6S环保生化线"/>
   <cge:TPSR_Ref TObjectID="6192450352906245"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1605.36,876.474) scale(3.75,-1.5) translate(-1160.77,-1453.29)" width="12" x="1582.863636363636" y="853.9740606778619"/></g>
  <g id="571">
   <use class="kv10" height="30" transform="rotate(0,1679.73,876.474) scale(3.75,-1.5) translate(-1215.3,-1453.29)" width="12" x="1657.227272727273" xlink:href="#EnergyConsumer:负荷_0" y="853.9740603806126" zvalue="1486"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450353102853" ObjectName="Ⅱ-7S备用线"/>
   <cge:TPSR_Ref TObjectID="6192450353102853"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1679.73,876.474) scale(3.75,-1.5) translate(-1215.3,-1453.29)" width="12" x="1657.227272727273" y="853.9740603806126"/></g>
  <g id="579">
   <use class="kv10" height="30" transform="rotate(0,1751.91,876.474) scale(3.75,-1.5) translate(-1268.23,-1453.29)" width="12" x="1729.409090909091" xlink:href="#EnergyConsumer:负荷_0" y="853.974059389782" zvalue="1495"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450353299461" ObjectName="Ⅱ-8S备用线"/>
   <cge:TPSR_Ref TObjectID="6192450353299461"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1751.91,876.474) scale(3.75,-1.5) translate(-1268.23,-1453.29)" width="12" x="1729.409090909091" y="853.974059389782"/></g>
  <g id="588">
   <use class="kv10" height="30" transform="rotate(0,1821.43,876.474) scale(3.75,-1.5) translate(-1319.22,-1453.29)" width="12" x="1798.931818181818" xlink:href="#EnergyConsumer:负荷_0" y="853.9740604796955" zvalue="1505"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450353496069" ObjectName="Ⅱ-9S备用线"/>
   <cge:TPSR_Ref TObjectID="6192450353496069"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1821.43,876.474) scale(3.75,-1.5) translate(-1319.22,-1453.29)" width="12" x="1798.931818181818" y="853.9740604796955"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="224">
   <use class="kv10" height="40" transform="rotate(0,844.545,859.773) scale(0.909091,0.909091) translate(83.3182,84.1591)" width="25" x="833.1818181818182" xlink:href="#Compensator:电容带电感_0" y="841.5909090909091" zvalue="1374"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450350874629" ObjectName="#1电容器"/>
   <cge:TPSR_Ref TObjectID="6192450350874629"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,844.545,859.773) scale(0.909091,0.909091) translate(83.3182,84.1591)" width="25" x="833.1818181818182" y="841.5909090909091"/></g>
  <g id="311">
   <use class="kv10" height="40" transform="rotate(0,1452.57,860.89) scale(0.909091,0.909091) translate(144.12,84.2708)" width="25" x="1441.204545454545" xlink:href="#Compensator:电容带电感_0" y="842.7077959729478" zvalue="1453"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450352447493" ObjectName="#2电容器"/>
   <cge:TPSR_Ref TObjectID="6192450352447493"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1452.57,860.89) scale(0.909091,0.909091) translate(144.12,84.2708)" width="25" x="1441.204545454545" y="842.7077959729478"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="682">
   <use class="kv110" height="40" transform="rotate(0,1115.32,53.9272) scale(1.23333,1.07136) translate(-207.507,-2.16477)" width="30" x="1096.820980975908" xlink:href="#ACLineSegment:线路带壁雷器_0" y="32.49999999999994" zvalue="1510"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249331138563" ObjectName="110kV景琪线"/>
   <cge:TPSR_Ref TObjectID="8444249331138563_5066549596848130"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1115.32,53.9272) scale(1.23333,1.07136) translate(-207.507,-2.16477)" width="30" x="1096.820980975908" y="32.49999999999994"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="8" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1189.89,40.4286) scale(1,1) translate(3.81326e-13,0)" writing-mode="lr" x="1146.77" xml:space="preserve" y="44.79" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128215511044" ObjectName="P"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="13" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1189.89,65.4286) scale(1,1) translate(3.81326e-13,0)" writing-mode="lr" x="1146.77" xml:space="preserve" y="69.79000000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128215576580" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="17" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1189.89,90.4286) scale(1,1) translate(3.81326e-13,0)" writing-mode="lr" x="1146.77" xml:space="preserve" y="94.79000000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128215642116" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="18" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,823.757,352.719) scale(1,1) translate(8.55712e-14,0)" writing-mode="lr" x="772.97" xml:space="preserve" y="358.58" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128189755396" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="19" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,823.757,381.719) scale(1,1) translate(8.55712e-14,0)" writing-mode="lr" x="772.97" xml:space="preserve" y="387.58" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128189820932" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="28">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="28" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,823.757,540.446) scale(1,1) translate(0,0)" writing-mode="lr" x="772.97" xml:space="preserve" y="546.3099999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128189886468" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="32" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,823.757,567.779) scale(1,1) translate(-1.71142e-13,0)" writing-mode="lr" x="772.97" xml:space="preserve" y="573.64" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128189952004" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="33" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,823.757,410.719) scale(1,1) translate(8.55712e-14,1.74624e-13)" writing-mode="lr" x="772.97" xml:space="preserve" y="416.58" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128190017540" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="34">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="34" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,823.757,596.779) scale(1,1) translate(-1.71142e-13,0)" writing-mode="lr" x="772.97" xml:space="preserve" y="602.64" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128190345220" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="35" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1429.52,346.714) scale(1,1) translate(-6.11297e-13,0)" writing-mode="lr" x="1378.73" xml:space="preserve" y="352.58" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128186675206" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="37" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1429.52,375.714) scale(1,1) translate(-6.11297e-13,0)" writing-mode="lr" x="1378.73" xml:space="preserve" y="381.58" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128186740740" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="39" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1433.8,550.156) scale(1,1) translate(0,0)" writing-mode="lr" x="1383.01" xml:space="preserve" y="556.02" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128186806276" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="40">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="40" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1433.8,579.156) scale(1,1) translate(0,1.24713e-13)" writing-mode="lr" x="1383.01" xml:space="preserve" y="585.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128186871812" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="51">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="51" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1429.52,404.714) scale(1,1) translate(-6.11297e-13,0)" writing-mode="lr" x="1378.73" xml:space="preserve" y="410.58" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128186937348" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="55" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1433.8,608.156) scale(1,1) translate(0,0)" writing-mode="lr" x="1383.01" xml:space="preserve" y="614.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128187265028" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,132.889,513.932) scale(1,1) translate(0,0)" writing-mode="lr" x="110.28" xml:space="preserve" y="518.34" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128185626628" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,134,538.869) scale(1,1) translate(0,0)" writing-mode="lr" x="111.39" xml:space="preserve" y="543.28" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128185692164" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,133.365,564.123) scale(1,1) translate(0,0)" writing-mode="lr" x="110.75" xml:space="preserve" y="568.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128185757700" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,135.294,490.915) scale(1,1) translate(0,5.32162e-14)" writing-mode="lr" x="112.68" xml:space="preserve" y="495.32" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128185888772" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="67">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="67" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1031.79,614.048) scale(1,1) translate(1.08668e-13,0)" writing-mode="lr" x="981" xml:space="preserve" y="618.41" zvalue="1">Ia:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128183529479" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="68">
   <text Format="f5.1" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="68" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1024.24,631.27) scale(1,1) translate(0,0)" writing-mode="lr" x="981.11" xml:space="preserve" y="635.63" zvalue="1">P:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128183726084" ObjectName="P"/>
   </metadata>
  </g>
  <g id="69">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,250.238,515.075) scale(1,1) translate(0,-1.11797e-13)" writing-mode="lr" x="227.63" xml:space="preserve" y="519.48" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128185102340" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="70">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,250.238,539.853) scale(1,1) translate(0,-1.17299e-13)" writing-mode="lr" x="227.63" xml:space="preserve" y="544.26" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128185167876" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="71">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,250.238,564.631) scale(1,1) translate(0,-1.22801e-13)" writing-mode="lr" x="227.63" xml:space="preserve" y="569.04" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128185233412" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="72">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,250.238,489.242) scale(1,1) translate(0,-1.06061e-13)" writing-mode="lr" x="227.63" xml:space="preserve" y="493.65" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128185364484" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,309.566,515.075) scale(1,1) translate(0,0)" writing-mode="lr" x="286.95" xml:space="preserve" y="519.48" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128184578052" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="74">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,309.566,538.742) scale(1,1) translate(0,5.85261e-14)" writing-mode="lr" x="286.95" xml:space="preserve" y="543.15" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128184643588" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="75">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,309.566,564.631) scale(1,1) translate(0,-1.22801e-13)" writing-mode="lr" x="286.95" xml:space="preserve" y="569.04" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128184709124" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="76">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,309.566,489.242) scale(1,1) translate(0,-1.06061e-13)" writing-mode="lr" x="286.95" xml:space="preserve" y="493.65" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128184840196" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="78" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,130.778,177.778) scale(1,1) translate(0,0)" writing-mode="lr" x="79.98999999999999" xml:space="preserve" y="183.64" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128216559620" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="79">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="79" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,308.556,177.889) scale(1,1) translate(0,0)" writing-mode="lr" x="257.76" xml:space="preserve" y="183.75" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128216625156" ObjectName="LOAD_QSUM"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="80" prefix="F:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,140.627,203.139) scale(1,1) translate(0,0)" writing-mode="lr" x="97.5" xml:space="preserve" y="207.5" zvalue="1">F:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128186019844" ObjectName="F"/>
   </metadata>
  </g>
  <g id="81">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="81" prefix="F:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,139.447,227.297) scale(1,1) translate(0,0)" writing-mode="lr" x="96.31999999999999" xml:space="preserve" y="231.66" zvalue="1">F:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128185495556" ObjectName="F"/>
   </metadata>
  </g>
  <g id="82">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="82" prefix="F:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,138.354,250.631) scale(1,1) translate(0,0)" writing-mode="lr" x="95.23" xml:space="preserve" y="254.99" zvalue="1">F:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128184971268" ObjectName="F"/>
   </metadata>
  </g>
  <g id="83">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="83" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,850.323,920.091) scale(1,1) translate(8.94088e-14,0)" writing-mode="lr" x="807.2" xml:space="preserve" y="924.45" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128199192580" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="84">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="84" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,848.101,942.313) scale(1,1) translate(-1.78324e-13,0)" writing-mode="lr" x="804.98" xml:space="preserve" y="946.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128199258116" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="85" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1457.23,915.652) scale(1,1) translate(0,0)" writing-mode="lr" x="1414.11" xml:space="preserve" y="920.01" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128207384582" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="86" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1457.23,936.763) scale(1,1) translate(0,0)" writing-mode="lr" x="1414.11" xml:space="preserve" y="941.12" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128207450116" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="87" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,465.222,965.141) scale(1,1) translate(0,0)" writing-mode="lr" x="422.1" xml:space="preserve" y="969.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128192311303" ObjectName="P"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="88" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,534.222,965.141) scale(1,1) translate(0,0)" writing-mode="lr" x="491.1" xml:space="preserve" y="969.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128193753092" ObjectName="P"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="89" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,611.222,965.141) scale(1,1) translate(0,0)" writing-mode="lr" x="568.1" xml:space="preserve" y="969.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128195194884" ObjectName="P"/>
   </metadata>
  </g>
  <g id="90">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="90" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,677.222,965.141) scale(1,1) translate(0,0)" writing-mode="lr" x="634.1" xml:space="preserve" y="969.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128196636678" ObjectName="P"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="91" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,937.04,965.141) scale(1,1) translate(0,0)" writing-mode="lr" x="893.92" xml:space="preserve" y="969.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128199585796" ObjectName="P"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="92" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1101.4,965.141) scale(1,1) translate(0,0)" writing-mode="lr" x="1058.28" xml:space="preserve" y="969.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128201027591" ObjectName="P"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="138" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1164.86,965.141) scale(1,1) translate(0,0)" writing-mode="lr" x="1121.73" xml:space="preserve" y="969.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128202469380" ObjectName="P"/>
   </metadata>
  </g>
  <g id="139">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="139" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1229.22,965.141) scale(1,1) translate(0,0)" writing-mode="lr" x="1186.1" xml:space="preserve" y="969.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128203911172" ObjectName="P"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="140" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1299.22,965.141) scale(1,1) translate(0,0)" writing-mode="lr" x="1256.1" xml:space="preserve" y="969.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128205352964" ObjectName="P"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="141" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1536.31,965.141) scale(1,1) translate(0,0)" writing-mode="lr" x="1493.19" xml:space="preserve" y="969.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128208302084" ObjectName="P"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="142" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1613.59,965.141) scale(1,1) translate(0,0)" writing-mode="lr" x="1570.46" xml:space="preserve" y="969.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128209743876" ObjectName="P"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="143" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1687.95,965.141) scale(1,1) translate(0,0)" writing-mode="lr" x="1644.82" xml:space="preserve" y="969.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128211185670" ObjectName="P"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="144" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1760.13,965.141) scale(1,1) translate(0,0)" writing-mode="lr" x="1717.01" xml:space="preserve" y="969.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128212627460" ObjectName="P"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="148" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1829.65,965.141) scale(1,1) translate(0,0)" writing-mode="lr" x="1786.53" xml:space="preserve" y="969.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128214069252" ObjectName="P"/>
   </metadata>
  </g>
  <g id="165">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="165" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,465.222,984.363) scale(1,1) translate(0,0)" writing-mode="lr" x="422.1" xml:space="preserve" y="988.72" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128192376838" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="169">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="169" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,534.222,984.363) scale(1,1) translate(0,0)" writing-mode="lr" x="491.1" xml:space="preserve" y="988.72" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128193818628" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="170">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="170" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,611.222,984.363) scale(1,1) translate(0,0)" writing-mode="lr" x="568.1" xml:space="preserve" y="988.72" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128195260423" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="172">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="172" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,677.222,984.363) scale(1,1) translate(0,0)" writing-mode="lr" x="634.1" xml:space="preserve" y="988.72" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128196702215" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="173">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="173" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,937.04,984.363) scale(1,1) translate(0,0)" writing-mode="lr" x="893.92" xml:space="preserve" y="988.72" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128199651332" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="174">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="174" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1101.4,984.363) scale(1,1) translate(0,0)" writing-mode="lr" x="1058.28" xml:space="preserve" y="988.72" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128201093126" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="176">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="176" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1164.86,984.363) scale(1,1) translate(0,0)" writing-mode="lr" x="1121.73" xml:space="preserve" y="988.72" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128202534918" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="178">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="178" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1229.22,984.363) scale(1,1) translate(0,0)" writing-mode="lr" x="1186.1" xml:space="preserve" y="988.72" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128203976708" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="179">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="179" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1299.22,984.363) scale(1,1) translate(0,0)" writing-mode="lr" x="1256.1" xml:space="preserve" y="988.72" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128205418500" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="180">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="180" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1536.31,984.363) scale(1,1) translate(0,0)" writing-mode="lr" x="1493.19" xml:space="preserve" y="988.72" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128208367620" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="181">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="181" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1613.59,984.363) scale(1,1) translate(0,0)" writing-mode="lr" x="1570.46" xml:space="preserve" y="988.72" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128209809412" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="182">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="182" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1687.95,984.363) scale(1,1) translate(0,0)" writing-mode="lr" x="1644.82" xml:space="preserve" y="988.72" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128211251204" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="183">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="183" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1760.13,984.363) scale(1,1) translate(0,0)" writing-mode="lr" x="1717.01" xml:space="preserve" y="988.72" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128212692996" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="189">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="189" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1829.65,984.363) scale(1,1) translate(0,0)" writing-mode="lr" x="1786.53" xml:space="preserve" y="988.72" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128214134788" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="190">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="190" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,466.333,1004.7) scale(1,1) translate(0,0)" writing-mode="lr" x="423.21" xml:space="preserve" y="1009.06" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128192442375" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="192">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="192" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,535.333,1004.7) scale(1,1) translate(0,0)" writing-mode="lr" x="492.21" xml:space="preserve" y="1009.06" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128193884164" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="194">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="194" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,612.333,1004.7) scale(1,1) translate(0,0)" writing-mode="lr" x="569.21" xml:space="preserve" y="1009.06" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128195325958" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="196">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="196" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,678.333,1004.7) scale(1,1) translate(0,0)" writing-mode="lr" x="635.21" xml:space="preserve" y="1009.06" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128196767750" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="197">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="197" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,938.152,1004.7) scale(1,1) translate(0,0)" writing-mode="lr" x="895.03" xml:space="preserve" y="1009.06" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128199716868" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="198">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="198" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1102.52,1004.7) scale(1,1) translate(0,0)" writing-mode="lr" x="1059.39" xml:space="preserve" y="1009.06" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128201158660" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="199">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="199" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1166.06,1005.33) scale(1,1) translate(0,0)" writing-mode="lr" x="1122.94" xml:space="preserve" y="1009.69" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128202600455" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="200">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="200" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1230.33,1004.7) scale(1,1) translate(0,0)" writing-mode="lr" x="1187.21" xml:space="preserve" y="1009.06" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128204042244" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="202">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="202" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1300.33,1004.7) scale(1,1) translate(0,0)" writing-mode="lr" x="1257.21" xml:space="preserve" y="1009.06" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128205484036" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="203">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="203" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1537.42,1004.7) scale(1,1) translate(0,0)" writing-mode="lr" x="1494.3" xml:space="preserve" y="1009.06" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128208433156" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="204">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="204" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1613.59,1004.7) scale(1,1) translate(0,0)" writing-mode="lr" x="1570.46" xml:space="preserve" y="1009.06" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128209874948" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="206">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="206" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1686.84,1004.7) scale(1,1) translate(0,0)" writing-mode="lr" x="1643.71" xml:space="preserve" y="1009.06" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128211316740" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="207">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="207" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1759.02,1004.7) scale(1,1) translate(0,0)" writing-mode="lr" x="1715.9" xml:space="preserve" y="1009.06" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128212758532" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="208">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="208" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1828.54,1004.7) scale(1,1) translate(0,0)" writing-mode="lr" x="1785.42" xml:space="preserve" y="1009.06" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128214200324" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
</svg>