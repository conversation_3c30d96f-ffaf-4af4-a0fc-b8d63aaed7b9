<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549592064002" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:PT带保险_0" viewBox="0,0,11,29">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="0.1666666666666661"/>
   <rect fill-opacity="0" height="6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.5,7.5) scale(1,1) translate(0,0)" width="4" x="3.5" y="4.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="13.16666666666667" y2="0.5"/>
   <ellipse cx="5.4" cy="18.1" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.4" cy="23.78" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="4.583333333333332" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Breaker:小车母联_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="17.58333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车母联_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="0.8333333333333304" y2="5.749999999999999"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车母联_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="14" y2="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.666666666666666" x2="7.25" y1="5.833333333333333" y2="14.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV弄璋糖厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="71.94" y="327" zvalue="31"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="1" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" width="72.88" x="71.94" y="327" zvalue="31"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" writing-mode="lr" x="108.38" xml:space="preserve" y="343.5" zvalue="31">信号一览</text>
  <image height="60" id="2" preserveAspectRatio="xMidYMid slice" width="269.25" x="66.95999999999999" xlink:href="logo.png" y="45.64"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.589,75.6429) scale(1,1) translate(0,0)" writing-mode="lr" x="201.59" xml:space="preserve" y="79.14" zvalue="138"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,203.298,75.3332) scale(1,1) translate(9.43293e-15,0)" writing-mode="lr" x="203.3" xml:space="preserve" y="84.33" zvalue="139">10kV弄璋糖厂</text>
  <line fill="none" id="26" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="388.75" x2="388.75" y1="11" y2="1039.25" zvalue="6"/>
  <line fill="none" id="24" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.22692454998003" x2="331.8381846035992" y1="168.8779458827686" y2="168.8779458827686" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="927.8127909390723" y2="927.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="979.1522909390724" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="361.3219285714284" y1="927.8127909390723" y2="927.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="361.3219285714284" y1="979.1522909390724" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3219285714284" x2="361.3219285714284" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5593285714284" x2="279.5593285714284" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="279.5592285714284" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3213285714285" x2="361.3213285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5593285714284" x2="279.5593285714284" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="279.5592285714284" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3213285714285" x2="361.3213285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,92.8383,957.386) scale(1,1) translate(0,1.05089e-13)" writing-mode="lr" x="60.68" xml:space="preserve" y="963.39" zvalue="10">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,132.899,994.755) scale(1,1) translate(-6.25312e-14,-1.52933e-12)" writing-mode="lr" x="70.40000000000001" xml:space="preserve" y="1000.75" zvalue="11">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,276.202,995.755) scale(1,1) translate(1.38222e-13,-1.53089e-12)" writing-mode="lr" x="207.5" xml:space="preserve" y="1001.75" zvalue="12">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.9923,1024.31) scale(1,1) translate(-6.10858e-14,-1.57527e-12)" writing-mode="lr" x="83.98999999999999" xml:space="preserve" y="1030.31" zvalue="13">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,280.836,1022.31) scale(1,1) translate(-4.58902e-14,1.12298e-13)" writing-mode="lr" x="206.67" xml:space="preserve" y="1028.31" zvalue="14">更新日期  </text>
  <line fill="none" id="17" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="54.60611355802337" x2="359.2173736116425" y1="623.6445306693694" y2="623.6445306693694" zvalue="15"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,97.6707,640.686) scale(1,1) translate(5.99964e-15,-1.38109e-13)" writing-mode="lr" x="97.67070806204492" xml:space="preserve" y="645.1863811688671" zvalue="17">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="172.1071428571429" y2="172.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="172.1071428571429" y2="172.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="290.6071428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="290.6071428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" x="139.5390625" xml:space="preserve" y="467.109375" zvalue="20">10kV母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="139.5390625" xml:space="preserve" y="483.109375" zvalue="20">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,502.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="507.1071428571429" zvalue="23">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,528.107) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="532.6071428571429" zvalue="24">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,553.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="558.1071428571429" zvalue="25">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,579.107) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="583.6071428571429" zvalue="26">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,604.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="609.1071428571429" zvalue="27">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.3214,186.107) scale(1,1) translate(0,0)" writing-mode="lr" x="54.32" xml:space="preserve" y="191.61" zvalue="28">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.571,186.107) scale(1,1) translate(0,0)" writing-mode="lr" x="240.57" xml:space="preserve" y="191.61" zvalue="29">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,65.0089,210.357) scale(1,1) translate(0,0)" writing-mode="lr" x="65.01000000000001" xml:space="preserve" y="214.86" zvalue="30">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,208.399,340.5) scale(1,1) translate(0,0)" writing-mode="lr" x="208.4" xml:space="preserve" y="345" zvalue="379">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,296.399,340.5) scale(1,1) translate(0,0)" writing-mode="lr" x="296.4" xml:space="preserve" y="345" zvalue="380">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="301" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1275.67,201.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1275.67" xml:space="preserve" y="206.17" zvalue="392">10kV弄璋糖厂线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="10" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,241.838,958.386) scale(1,1) translate(0,1.052e-13)" writing-mode="lr" x="132.68" xml:space="preserve" y="964.39" zvalue="418">NongZhang-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1661,484) scale(1,1) translate(0,0)" writing-mode="lr" x="1661" xml:space="preserve" y="488.5" zvalue="420">10.5kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1302.5,424) scale(1,1) translate(0,0)" writing-mode="lr" x="1302.5" xml:space="preserve" y="428.5" zvalue="422">061</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1355,312) scale(1,1) translate(0,0)" writing-mode="lr" x="1355" xml:space="preserve" y="316.5" zvalue="427">0619</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,954.5,484) scale(1,1) translate(0,0)" writing-mode="lr" x="954.5" xml:space="preserve" y="488.5" zvalue="431">10.5kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1177,556) scale(1,1) translate(0,0)" writing-mode="lr" x="1177" xml:space="preserve" y="560.5" zvalue="433">059</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1269.67,833) scale(1,1) translate(0,0)" writing-mode="lr" x="1269.67" xml:space="preserve" y="837.5" zvalue="437">污水处理</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1297.5,555) scale(1,1) translate(0,0)" writing-mode="lr" x="1297.5" xml:space="preserve" y="559.5" zvalue="440">058</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1309.5,683) scale(1,1) translate(0,0)" writing-mode="lr" x="1309.5" xml:space="preserve" y="687.5" zvalue="443">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1332.44,815) scale(1,1) translate(0,0)" writing-mode="lr" x="1332.44" xml:space="preserve" y="819.5" zvalue="446">Ⅰ段母线PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1358,555) scale(1,1) translate(0,0)" writing-mode="lr" x="1358" xml:space="preserve" y="559.5" zvalue="447">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1393.67,833) scale(1,1) translate(0,0)" writing-mode="lr" x="1393.67" xml:space="preserve" y="837.5" zvalue="455">生活区一</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1423,555) scale(1,1) translate(0,0)" writing-mode="lr" x="1423" xml:space="preserve" y="559.5" zvalue="457">057</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1434.5,683) scale(1,1) translate(0,0)" writing-mode="lr" x="1434.5" xml:space="preserve" y="687.5" zvalue="462">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1453.67,833) scale(1,1) translate(1.58503e-12,0)" writing-mode="lr" x="1453.67" xml:space="preserve" y="837.5" zvalue="465">泵站</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1481.5,555) scale(1,1) translate(0,0)" writing-mode="lr" x="1481.5" xml:space="preserve" y="559.5" zvalue="467">056</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1493.5,683) scale(1,1) translate(0,0)" writing-mode="lr" x="1493.5" xml:space="preserve" y="687.5" zvalue="472">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1513.67,833) scale(1,1) translate(0,0)" writing-mode="lr" x="1513.67" xml:space="preserve" y="837.5" zvalue="475">#1无功补偿</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1541.5,555) scale(1,1) translate(0,0)" writing-mode="lr" x="1541.5" xml:space="preserve" y="559.5" zvalue="477">055</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1553.5,683) scale(1,1) translate(0,0)" writing-mode="lr" x="1553.5" xml:space="preserve" y="687.5" zvalue="482">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1573.67,833) scale(1,1) translate(0,0)" writing-mode="lr" x="1573.67" xml:space="preserve" y="837.5" zvalue="485">热电Ⅰ回</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1601.5,555) scale(1,1) translate(0,0)" writing-mode="lr" x="1601.5" xml:space="preserve" y="559.5" zvalue="487">054</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1613.5,683) scale(1,1) translate(0,0)" writing-mode="lr" x="1613.5" xml:space="preserve" y="687.5" zvalue="492">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" x="1634.0703125" xml:space="preserve" y="836.75" zvalue="495">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1634.0703125" xml:space="preserve" y="852.75" zvalue="495">6000kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1661.5,555) scale(1,1) translate(0,0)" writing-mode="lr" x="1661.5" xml:space="preserve" y="559.5" zvalue="497">053</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1693.67,833) scale(1,1) translate(0,0)" writing-mode="lr" x="1693.67" xml:space="preserve" y="837.5" zvalue="505">备用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1721.5,555) scale(1,1) translate(0,0)" writing-mode="lr" x="1721.5" xml:space="preserve" y="559.5" zvalue="507">052</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1732,683) scale(1,1) translate(0,0)" writing-mode="lr" x="1732" xml:space="preserve" y="687.5" zvalue="511">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1753.67,833) scale(1,1) translate(0,0)" writing-mode="lr" x="1753.67" xml:space="preserve" y="837.5" zvalue="514">压榨车间Ⅰ回</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1781.5,555) scale(1,1) translate(0,0)" writing-mode="lr" x="1781.5" xml:space="preserve" y="559.5" zvalue="515">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1792,683) scale(1,1) translate(0,0)" writing-mode="lr" x="1792" xml:space="preserve" y="687.5" zvalue="520">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,980.442,815) scale(1,1) translate(0,0)" writing-mode="lr" x="980.4400000000001" xml:space="preserve" y="819.5" zvalue="524">Ⅱ段母线PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1006,555) scale(1,1) translate(0,0)" writing-mode="lr" x="1006" xml:space="preserve" y="559.5" zvalue="525">0902</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,552.083,839.25) scale(1,1) translate(0,-1.81299e-13)" writing-mode="lr" x="552.0833333333335" xml:space="preserve" y="843.75" zvalue="530">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,577,555) scale(1,1) translate(0,0)" writing-mode="lr" x="577" xml:space="preserve" y="559.5" zvalue="532">068</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="155" stroke="rgb(255,255,255)" text-anchor="middle" x="615.3359375" xml:space="preserve" y="836.75" zvalue="537">制炼循环</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="155" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="615.3359375" xml:space="preserve" y="852.75" zvalue="537">水站</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,645,555) scale(1,1) translate(0,0)" writing-mode="lr" x="645" xml:space="preserve" y="559.5" zvalue="539">067</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,656.5,683) scale(1,1) translate(0,0)" writing-mode="lr" x="656.5" xml:space="preserve" y="687.5" zvalue="544">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" x="675.3359375" xml:space="preserve" y="836.75" zvalue="547">制炼车间Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="675.3359375" xml:space="preserve" y="852.75" zvalue="547">回</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,705,555) scale(1,1) translate(0,0)" writing-mode="lr" x="705" xml:space="preserve" y="559.5" zvalue="549">066</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,716.5,683) scale(1,1) translate(0,0)" writing-mode="lr" x="716.5" xml:space="preserve" y="687.5" zvalue="554">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" x="735.3359375" xml:space="preserve" y="836.75" zvalue="557">制炼车间Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="735.3359375" xml:space="preserve" y="852.75" zvalue="557">回</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,765,555) scale(1,1) translate(0,0)" writing-mode="lr" x="765" xml:space="preserve" y="559.5" zvalue="559">065</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,776.5,683) scale(1,1) translate(0,0)" writing-mode="lr" x="776.5" xml:space="preserve" y="687.5" zvalue="564">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,795.333,839.25) scale(1,1) translate(0,-1.83076e-13)" writing-mode="lr" x="795.3333333333334" xml:space="preserve" y="843.75" zvalue="567">备用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="182" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,825,555) scale(1,1) translate(0,0)" writing-mode="lr" x="825" xml:space="preserve" y="559.5" zvalue="569">064</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,836.5,683) scale(1,1) translate(0,0)" writing-mode="lr" x="836.5" xml:space="preserve" y="687.5" zvalue="574">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="192" stroke="rgb(255,255,255)" text-anchor="middle" x="855.3359375" xml:space="preserve" y="836.75" zvalue="577">锅炉高压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="192" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="855.3359375" xml:space="preserve" y="852.75" zvalue="577">机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="191" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,885,555) scale(1,1) translate(0,0)" writing-mode="lr" x="885" xml:space="preserve" y="559.5" zvalue="579">063</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,896.5,683) scale(1,1) translate(0,0)" writing-mode="lr" x="896.5" xml:space="preserve" y="687.5" zvalue="584">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,915.333,839.25) scale(1,1) translate(0,-1.83076e-13)" writing-mode="lr" x="915.3333333333334" xml:space="preserve" y="843.75" zvalue="587">热电Ⅱ回</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,945,555) scale(1,1) translate(0,0)" writing-mode="lr" x="945" xml:space="preserve" y="559.5" zvalue="589">062</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,956.5,683) scale(1,1) translate(0,0)" writing-mode="lr" x="956.5" xml:space="preserve" y="687.5" zvalue="594">67</text>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="34">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="34" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,161.571,210.968) scale(1,1) translate(0,0)" writing-mode="lr" x="161.72" xml:space="preserve" y="217.4" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="33" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,161.571,186) scale(1,1) translate(0,0)" writing-mode="lr" x="161.72" xml:space="preserve" y="192.43" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126555811844" ObjectName=""/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="35" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,343.571,185.857) scale(1,1) translate(0,0)" writing-mode="lr" x="343.72" xml:space="preserve" y="192.29" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126555877380" ObjectName=""/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,146.071,528.593) scale(1,1) translate(0,0)" writing-mode="lr" x="146.2" xml:space="preserve" y="533.5" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="143" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,146.071,553.718) scale(1,1) translate(0,-1.20619e-13)" writing-mode="lr" x="146.2" xml:space="preserve" y="558.63" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,147.071,577.843) scale(1,1) translate(0,0)" writing-mode="lr" x="147.2" xml:space="preserve" y="582.75" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,147.5,503.468) scale(1,1) translate(0,1.09461e-13)" writing-mode="lr" x="147.63" xml:space="preserve" y="508.38" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="148" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,146.321,602.968) scale(1,1) translate(0,0)" writing-mode="lr" x="146.45" xml:space="preserve" y="607.88" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="132">
   <use height="30" transform="rotate(0,329.768,340.5) scale(0.708333,0.665547) translate(131.412,166.093)" width="30" x="319.14" xlink:href="#State:红绿圆(方形)_0" y="330.52" zvalue="381"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.768,340.5) scale(0.708333,0.665547) translate(131.412,166.093)" width="30" x="319.14" y="330.52"/></g>
  <g id="94">
   <use height="30" transform="rotate(0,247.143,340.5) scale(0.708333,0.665547) translate(97.3897,166.093)" width="30" x="236.52" xlink:href="#State:红绿圆(方形)_0" y="330.52" zvalue="382"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,247.143,340.5) scale(0.708333,0.665547) translate(97.3897,166.093)" width="30" x="236.52" y="330.52"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="11">
   <path class="kv10" d="M 1170.65 503 L 1786 503" stroke-width="6" zvalue="419"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674253144068" ObjectName="10.5kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674253144068"/></metadata>
  <path d="M 1170.65 503 L 1786 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv10" d="M 508 503 L 1075 503" stroke-width="6" zvalue="430"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674253209604" ObjectName="10.5kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674253209604"/></metadata>
  <path d="M 508 503 L 1075 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="29">
   <use class="kv10" height="20" transform="rotate(0,1276,425) scale(2.2,2.2) translate(-690,-219.818)" width="10" x="1265" xlink:href="#Breaker:小车断路器_0" y="403" zvalue="421"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924553342980" ObjectName="10kV弄璋糖厂线061断路器"/>
   <cge:TPSR_Ref TObjectID="6473924553342980"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1276,425) scale(2.2,2.2) translate(-690,-219.818)" width="10" x="1265" y="403"/></g>
  <g id="45">
   <use class="kv10" height="20" transform="rotate(0,1205,556) scale(2.2,2.2) translate(-651.273,-291.273)" width="10" x="1194" xlink:href="#Breaker:小车母联_0" y="534" zvalue="432"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924553408516" ObjectName="10kV分段059断路器"/>
   <cge:TPSR_Ref TObjectID="6473924553408516"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1205,556) scale(2.2,2.2) translate(-651.273,-291.273)" width="10" x="1194" y="534"/></g>
  <g id="51">
   <use class="kv10" height="20" transform="rotate(0,1269,556) scale(2.2,2.2) translate(-686.182,-291.273)" width="10" x="1258" xlink:href="#Breaker:小车断路器_0" y="534" zvalue="439"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924553474052" ObjectName="污水处理058断路器"/>
   <cge:TPSR_Ref TObjectID="6473924553474052"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1269,556) scale(2.2,2.2) translate(-686.182,-291.273)" width="10" x="1258" y="534"/></g>
  <g id="76">
   <use class="kv10" height="20" transform="rotate(0,1393,556) scale(2.2,2.2) translate(-753.818,-291.273)" width="10" x="1382" xlink:href="#Breaker:小车断路器_0" y="534" zvalue="456"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924553539588" ObjectName="生活区一057断路器"/>
   <cge:TPSR_Ref TObjectID="6473924553539588"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1393,556) scale(2.2,2.2) translate(-753.818,-291.273)" width="10" x="1382" y="534"/></g>
  <g id="85">
   <use class="kv10" height="20" transform="rotate(0,1453,556) scale(2.2,2.2) translate(-786.545,-291.273)" width="10" x="1442" xlink:href="#Breaker:小车断路器_0" y="534" zvalue="466"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924553605124" ObjectName="泵站056断路器"/>
   <cge:TPSR_Ref TObjectID="6473924553605124"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1453,556) scale(2.2,2.2) translate(-786.545,-291.273)" width="10" x="1442" y="534"/></g>
  <g id="95">
   <use class="kv10" height="20" transform="rotate(0,1513,556) scale(2.2,2.2) translate(-819.273,-291.273)" width="10" x="1502" xlink:href="#Breaker:小车断路器_0" y="534" zvalue="476"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924553670660" ObjectName="#1无功补偿055断路器"/>
   <cge:TPSR_Ref TObjectID="6473924553670660"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1513,556) scale(2.2,2.2) translate(-819.273,-291.273)" width="10" x="1502" y="534"/></g>
  <g id="104">
   <use class="kv10" height="20" transform="rotate(0,1573,556) scale(2.2,2.2) translate(-852,-291.273)" width="10" x="1562" xlink:href="#Breaker:小车断路器_0" y="534" zvalue="486"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924553736197" ObjectName="热电Ⅰ回054断路器"/>
   <cge:TPSR_Ref TObjectID="6473924553736197"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1573,556) scale(2.2,2.2) translate(-852,-291.273)" width="10" x="1562" y="534"/></g>
  <g id="113">
   <use class="kv10" height="20" transform="rotate(0,1633,556) scale(2.2,2.2) translate(-884.727,-291.273)" width="10" x="1622" xlink:href="#Breaker:小车断路器_0" y="534" zvalue="496"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924553932804" ObjectName="#1发电机053断路器"/>
   <cge:TPSR_Ref TObjectID="6473924553932804"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1633,556) scale(2.2,2.2) translate(-884.727,-291.273)" width="10" x="1622" y="534"/></g>
  <g id="121">
   <use class="kv10" height="20" transform="rotate(0,1693,556) scale(2.2,2.2) translate(-917.455,-291.273)" width="10" x="1682" xlink:href="#Breaker:小车断路器_0" y="534" zvalue="506"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924553801733" ObjectName="备用052断路器"/>
   <cge:TPSR_Ref TObjectID="6473924553801733"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1693,556) scale(2.2,2.2) translate(-917.455,-291.273)" width="10" x="1682" y="534"/></g>
  <g id="130">
   <use class="kv10" height="20" transform="rotate(0,1753,556) scale(2.2,2.2) translate(-950.182,-291.273)" width="10" x="1742" xlink:href="#Breaker:小车断路器_0" y="534" zvalue="514"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924553867268" ObjectName="压榨车间Ⅰ回051断路器"/>
   <cge:TPSR_Ref TObjectID="6473924553867268"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1753,556) scale(2.2,2.2) translate(-950.182,-291.273)" width="10" x="1742" y="534"/></g>
  <g id="150">
   <use class="kv10" height="20" transform="rotate(0,551,556) scale(2.2,2.2) translate(-294.545,-291.273)" width="10" x="540" xlink:href="#Breaker:小车断路器_0" y="534" zvalue="531"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924553998340" ObjectName="#2发电机068断路器"/>
   <cge:TPSR_Ref TObjectID="6473924553998340"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,551,556) scale(2.2,2.2) translate(-294.545,-291.273)" width="10" x="540" y="534"/></g>
  <g id="160">
   <use class="kv10" height="20" transform="rotate(0,615,556) scale(2.2,2.2) translate(-329.455,-291.273)" width="10" x="604" xlink:href="#Breaker:小车断路器_0" y="534" zvalue="538"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924554063876" ObjectName="制炼循环水站067断路器"/>
   <cge:TPSR_Ref TObjectID="6473924554063876"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,615,556) scale(2.2,2.2) translate(-329.455,-291.273)" width="10" x="604" y="534"/></g>
  <g id="170">
   <use class="kv10" height="20" transform="rotate(0,675,556) scale(2.2,2.2) translate(-362.182,-291.273)" width="10" x="664" xlink:href="#Breaker:小车断路器_0" y="534" zvalue="548"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924554129412" ObjectName="制炼车间Ⅱ回066断路器"/>
   <cge:TPSR_Ref TObjectID="6473924554129412"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,675,556) scale(2.2,2.2) translate(-362.182,-291.273)" width="10" x="664" y="534"/></g>
  <g id="179">
   <use class="kv10" height="20" transform="rotate(0,735,556) scale(2.2,2.2) translate(-394.909,-291.273)" width="10" x="724" xlink:href="#Breaker:小车断路器_0" y="534" zvalue="558"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924554194948" ObjectName="制炼车间Ⅰ回065断路器"/>
   <cge:TPSR_Ref TObjectID="6473924554194948"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,735,556) scale(2.2,2.2) translate(-394.909,-291.273)" width="10" x="724" y="534"/></g>
  <g id="188">
   <use class="kv10" height="20" transform="rotate(0,795,556) scale(2.2,2.2) translate(-427.636,-291.273)" width="10" x="784" xlink:href="#Breaker:小车断路器_0" y="534" zvalue="568"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924554260484" ObjectName="备用064断路器"/>
   <cge:TPSR_Ref TObjectID="6473924554260484"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,795,556) scale(2.2,2.2) translate(-427.636,-291.273)" width="10" x="784" y="534"/></g>
  <g id="197">
   <use class="kv10" height="20" transform="rotate(0,855,556) scale(2.2,2.2) translate(-460.364,-291.273)" width="10" x="844" xlink:href="#Breaker:小车断路器_0" y="534" zvalue="578"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924554326020" ObjectName="锅炉高压机063断路器"/>
   <cge:TPSR_Ref TObjectID="6473924554326020"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,855,556) scale(2.2,2.2) translate(-460.364,-291.273)" width="10" x="844" y="534"/></g>
  <g id="206">
   <use class="kv10" height="20" transform="rotate(0,915,556) scale(2.2,2.2) translate(-493.091,-291.273)" width="10" x="904" xlink:href="#Breaker:小车断路器_0" y="534" zvalue="588"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924554391556" ObjectName="热电Ⅱ回062断路器"/>
   <cge:TPSR_Ref TObjectID="6473924554391556"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,915,556) scale(2.2,2.2) translate(-493.091,-291.273)" width="10" x="904" y="534"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="31">
   <path class="kv10" d="M 1275.67 246.38 L 1275.67 404.65" stroke-width="1" zvalue="422"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="300@0" LinkObjectIDznd="29@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1275.67 246.38 L 1275.67 404.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="kv10" d="M 1276 444.8 L 1276 503" stroke-width="1" zvalue="423"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="29@1" LinkObjectIDznd="11@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1276 444.8 L 1276 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv10" d="M 1321.25 278.37 L 1321.25 297.03" stroke-width="1" zvalue="427"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="38@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1321.25 278.37 L 1321.25 297.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv10" d="M 1321 322.92 L 1321 347 L 1275.67 347" stroke-width="1" zvalue="428"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@1" LinkObjectIDznd="31" MaxPinNum="2"/>
   </metadata>
  <path d="M 1321 322.92 L 1321 347 L 1275.67 347" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv10" d="M 1205 503 L 1205 536.2" stroke-width="1" zvalue="433"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="11@1" LinkObjectIDznd="45@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1205 503 L 1205 536.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv10" d="M 1205 575.25 L 1205 693 L 1043 693 L 1043 503" stroke-width="1" zvalue="434"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@1" LinkObjectIDznd="42@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1205 575.25 L 1205 693 L 1043 693 L 1043 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv10" d="M 1269.67 786.63 L 1269.67 575.8" stroke-width="1" zvalue="440"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="51@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1269.67 786.63 L 1269.67 575.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv10" d="M 1269 535.65 L 1269 503" stroke-width="1" zvalue="441"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="11@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1269 535.65 L 1269 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv10" d="M 1289.58 669.38 L 1289.58 646 L 1269.67 646" stroke-width="1" zvalue="443"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="52" MaxPinNum="2"/>
   </metadata>
  <path d="M 1289.58 669.38 L 1289.58 646 L 1269.67 646" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv10" d="M 1329.11 755.04 L 1329.11 570.96" stroke-width="1" zvalue="448"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="65@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1329.11 755.04 L 1329.11 570.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv10" d="M 1329 541.09 L 1329 503" stroke-width="1" zvalue="449"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@1" LinkObjectIDznd="11@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1329 541.09 L 1329 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv10" d="M 1393.67 786.63 L 1393.67 575.8" stroke-width="1" zvalue="458"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@0" LinkObjectIDznd="76@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1393.67 786.63 L 1393.67 575.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 1393 535.65 L 1393 503" stroke-width="1" zvalue="459"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@0" LinkObjectIDznd="11@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1393 535.65 L 1393 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv10" d="M 1413.58 669.38 L 1413.58 646 L 1393.67 646" stroke-width="1" zvalue="461"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@0" LinkObjectIDznd="75" MaxPinNum="2"/>
   </metadata>
  <path d="M 1413.58 669.38 L 1413.58 646 L 1393.67 646" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv10" d="M 1453.67 786.63 L 1453.67 575.8" stroke-width="1" zvalue="468"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="85@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1453.67 786.63 L 1453.67 575.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv10" d="M 1453 535.65 L 1453 503" stroke-width="1" zvalue="469"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@0" LinkObjectIDznd="11@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1453 535.65 L 1453 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv10" d="M 1473.58 669.38 L 1473.58 646 L 1453.67 646" stroke-width="1" zvalue="471"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="84" MaxPinNum="2"/>
   </metadata>
  <path d="M 1473.58 669.38 L 1473.58 646 L 1453.67 646" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv10" d="M 1513.67 786.63 L 1513.67 575.8" stroke-width="1" zvalue="478"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="95@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1513.67 786.63 L 1513.67 575.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv10" d="M 1513 535.65 L 1513 503" stroke-width="1" zvalue="479"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@0" LinkObjectIDznd="11@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1513 535.65 L 1513 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv10" d="M 1533.58 669.38 L 1533.58 646 L 1513.67 646" stroke-width="1" zvalue="481"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="93" MaxPinNum="2"/>
   </metadata>
  <path d="M 1533.58 669.38 L 1533.58 646 L 1513.67 646" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv10" d="M 1573.67 786.63 L 1573.67 575.8" stroke-width="1" zvalue="488"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="104@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1573.67 786.63 L 1573.67 575.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv10" d="M 1573 535.65 L 1573 503" stroke-width="1" zvalue="489"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="11@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1573 535.65 L 1573 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv10" d="M 1593.58 669.38 L 1593.58 646 L 1573.67 646" stroke-width="1" zvalue="491"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@0" LinkObjectIDznd="103" MaxPinNum="2"/>
   </metadata>
  <path d="M 1593.58 669.38 L 1593.58 646 L 1573.67 646" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv10" d="M 1633.67 786.63 L 1633.67 575.8" stroke-width="1" zvalue="498"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="113@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1633.67 786.63 L 1633.67 575.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv10" d="M 1633 535.65 L 1633 503" stroke-width="1" zvalue="499"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="11@10" MaxPinNum="2"/>
   </metadata>
  <path d="M 1633 535.65 L 1633 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv10" d="M 1693.67 786.63 L 1693.67 575.8" stroke-width="1" zvalue="508"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@0" LinkObjectIDznd="121@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1693.67 786.63 L 1693.67 575.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv10" d="M 1693 535.65 L 1693 503" stroke-width="1" zvalue="509"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@0" LinkObjectIDznd="11@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1693 535.65 L 1693 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv10" d="M 1713.58 669.38 L 1713.58 646 L 1693.67 646" stroke-width="1" zvalue="511"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@0" LinkObjectIDznd="120" MaxPinNum="2"/>
   </metadata>
  <path d="M 1713.58 669.38 L 1713.58 646 L 1693.67 646" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv10" d="M 1753.67 786.63 L 1753.67 575.8" stroke-width="1" zvalue="516"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="130@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1753.67 786.63 L 1753.67 575.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv10" d="M 1753 535.65 L 1753 503" stroke-width="1" zvalue="517"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@0" LinkObjectIDznd="11@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1753 535.65 L 1753 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv10" d="M 1773.58 669.38 L 1773.58 646 L 1753.67 646" stroke-width="1" zvalue="519"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@0" LinkObjectIDznd="129" MaxPinNum="2"/>
   </metadata>
  <path d="M 1773.58 669.38 L 1773.58 646 L 1753.67 646" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="kv10" d="M 977.11 755.04 L 977.11 570.96" stroke-width="1" zvalue="526"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="139@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 977.11 755.04 L 977.11 570.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv10" d="M 977 541.09 L 977 503" stroke-width="1" zvalue="527"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@1" LinkObjectIDznd="42@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 977 541.09 L 977 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv10" d="M 551.67 786.63 L 551.67 575.8" stroke-width="1" zvalue="533"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@0" LinkObjectIDznd="150@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 551.67 786.63 L 551.67 575.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv10" d="M 551 535.65 L 551 503" stroke-width="1" zvalue="534"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="42@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 551 535.65 L 551 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="kv10" d="M 615.67 786.63 L 615.67 575.8" stroke-width="1" zvalue="540"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="160@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 615.67 786.63 L 615.67 575.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv10" d="M 615 535.65 L 615 503" stroke-width="1" zvalue="541"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160@0" LinkObjectIDznd="42@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 615 535.65 L 615 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv10" d="M 635.58 669.38 L 635.58 646 L 615.67 646" stroke-width="1" zvalue="543"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="159" MaxPinNum="2"/>
   </metadata>
  <path d="M 635.58 669.38 L 635.58 646 L 615.67 646" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv10" d="M 675.67 786.63 L 675.67 575.8" stroke-width="1" zvalue="550"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="170@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 675.67 786.63 L 675.67 575.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv10" d="M 675 535.65 L 675 503" stroke-width="1" zvalue="551"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="42@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 675 535.65 L 675 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="165">
   <path class="kv10" d="M 695.58 669.38 L 695.58 646 L 675.67 646" stroke-width="1" zvalue="553"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="169" MaxPinNum="2"/>
   </metadata>
  <path d="M 695.58 669.38 L 695.58 646 L 675.67 646" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv10" d="M 735.67 786.63 L 735.67 575.8" stroke-width="1" zvalue="560"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@0" LinkObjectIDznd="179@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 735.67 786.63 L 735.67 575.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv10" d="M 735 535.65 L 735 503" stroke-width="1" zvalue="561"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@0" LinkObjectIDznd="42@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 735 535.65 L 735 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv10" d="M 755.58 669.38 L 755.58 646 L 735.67 646" stroke-width="1" zvalue="563"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@0" LinkObjectIDznd="178" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.58 669.38 L 755.58 646 L 735.67 646" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv10" d="M 795.67 786.63 L 795.67 575.8" stroke-width="1" zvalue="570"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189@0" LinkObjectIDznd="188@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 795.67 786.63 L 795.67 575.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv10" d="M 795 535.65 L 795 503" stroke-width="1" zvalue="571"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@0" LinkObjectIDznd="42@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 795 535.65 L 795 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="184">
   <path class="kv10" d="M 815.58 669.38 L 815.58 646 L 795.67 646" stroke-width="1" zvalue="573"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@0" LinkObjectIDznd="187" MaxPinNum="2"/>
   </metadata>
  <path d="M 815.58 669.38 L 815.58 646 L 795.67 646" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv10" d="M 855.67 786.63 L 855.67 575.8" stroke-width="1" zvalue="580"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@0" LinkObjectIDznd="197@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 855.67 786.63 L 855.67 575.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv10" d="M 855 535.65 L 855 503" stroke-width="1" zvalue="581"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="42@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 855 535.65 L 855 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv10" d="M 875.58 669.38 L 875.58 646 L 855.67 646" stroke-width="1" zvalue="583"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@0" LinkObjectIDznd="196" MaxPinNum="2"/>
   </metadata>
  <path d="M 875.58 669.38 L 875.58 646 L 855.67 646" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="205">
   <path class="kv10" d="M 915.67 786.63 L 915.67 575.8" stroke-width="1" zvalue="590"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="206@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 915.67 786.63 L 915.67 575.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="kv10" d="M 915 535.65 L 915 503" stroke-width="1" zvalue="591"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@0" LinkObjectIDznd="42@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 915 535.65 L 915 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="kv10" d="M 935.58 669.38 L 935.58 646 L 915.67 646" stroke-width="1" zvalue="593"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@0" LinkObjectIDznd="205" MaxPinNum="2"/>
   </metadata>
  <path d="M 935.58 669.38 L 935.58 646 L 915.67 646" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="36">
   <use class="kv10" height="29" transform="rotate(0,1321.25,255.568) scale(1.59091,-1.59091) translate(-487.5,-407.643)" width="11" x="1312.5" xlink:href="#Accessory:PT带保险_0" y="232.5" zvalue="424"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450000977925" ObjectName="线路PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1321.25,255.568) scale(1.59091,-1.59091) translate(-487.5,-407.643)" width="11" x="1312.5" y="232.5"/></g>
  <g id="66">
   <use class="kv10" height="18" transform="rotate(0,1331.44,775.25) scale(2.51333,2.41667) translate(-790.341,-441.707)" width="15" x="1312.592274981432" xlink:href="#Accessory:PT8_0" y="753.5000000000002" zvalue="445"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450001371141" ObjectName="Ⅰ段母线PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1331.44,775.25) scale(2.51333,2.41667) translate(-790.341,-441.707)" width="15" x="1312.592274981432" y="753.5000000000002"/></g>
  <g id="140">
   <use class="kv10" height="18" transform="rotate(0,979.442,775.25) scale(2.51333,2.41667) translate(-578.394,-441.707)" width="15" x="960.5922749814318" xlink:href="#Accessory:PT8_0" y="753.5000000000002" zvalue="522"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450002681861" ObjectName="Ⅱ段母线PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,979.442,775.25) scale(2.51333,2.41667) translate(-578.394,-441.707)" width="15" x="960.5922749814318" y="753.5000000000002"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="38">
   <use class="kv10" height="26" transform="rotate(0,1321,310) scale(1,-1) translate(0,-620)" width="12" x="1315" xlink:href="#Disconnector:小车隔刀熔断器_0" y="297" zvalue="426"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450001043461" ObjectName="10kV弄璋糖厂线0619隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450001043461"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1321,310) scale(1,-1) translate(0,-620)" width="12" x="1315" y="297"/></g>
  <g id="65">
   <use class="kv10" height="26" transform="rotate(0,1329,556) scale(1.25,1.15385) translate(-264.3,-72.1333)" width="12" x="1321.5" xlink:href="#Disconnector:小车隔刀熔断器_0" y="541" zvalue="446"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450001305605" ObjectName="Ⅰ段母线PT0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450001305605"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1329,556) scale(1.25,1.15385) translate(-264.3,-72.1333)" width="12" x="1321.5" y="541"/></g>
  <g id="139">
   <use class="kv10" height="26" transform="rotate(0,977,556) scale(1.25,1.15385) translate(-193.9,-72.1333)" width="12" x="969.5" xlink:href="#Disconnector:小车隔刀熔断器_0" y="541" zvalue="523"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450002616325" ObjectName="Ⅱ段母线PT0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450002616325"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,977,556) scale(1.25,1.15385) translate(-193.9,-72.1333)" width="12" x="969.5" y="541"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="49">
   <use class="kv10" height="30" transform="rotate(0,1269.67,799) scale(3.09524,-0.833333) translate(-852.133,-1760.3)" width="7" x="1258.833333333333" xlink:href="#ACLineSegment:线路_0" y="786.5000000000001" zvalue="436"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450001108997" ObjectName="污水处理"/>
   <cge:TPSR_Ref TObjectID="6192450001108997_5066549592064002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1269.67,799) scale(3.09524,-0.833333) translate(-852.133,-1760.3)" width="7" x="1258.833333333333" y="786.5000000000001"/></g>
  <g id="77">
   <use class="kv10" height="30" transform="rotate(0,1393.67,799) scale(3.09524,-0.833333) translate(-936.072,-1760.3)" width="7" x="1382.833333333333" xlink:href="#ACLineSegment:线路_0" y="786.5000000000001" zvalue="454"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450001567749" ObjectName="生活区一"/>
   <cge:TPSR_Ref TObjectID="6192450001567749_5066549592064002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1393.67,799) scale(3.09524,-0.833333) translate(-936.072,-1760.3)" width="7" x="1382.833333333333" y="786.5000000000001"/></g>
  <g id="86">
   <use class="kv10" height="30" transform="rotate(0,1453.67,799) scale(3.09524,-0.833333) translate(-976.687,-1760.3)" width="7" x="1442.833333333333" xlink:href="#ACLineSegment:线路_0" y="786.5000000000001" zvalue="464"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450001764357" ObjectName="泵站"/>
   <cge:TPSR_Ref TObjectID="6192450001764357_5066549592064002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1453.67,799) scale(3.09524,-0.833333) translate(-976.687,-1760.3)" width="7" x="1442.833333333333" y="786.5000000000001"/></g>
  <g id="96">
   <use class="kv10" height="30" transform="rotate(0,1513.67,799) scale(3.09524,-0.833333) translate(-1017.3,-1760.3)" width="7" x="1502.833333333333" xlink:href="#ACLineSegment:线路_0" y="786.5000000000001" zvalue="474"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450001960965" ObjectName="#1无功补偿"/>
   <cge:TPSR_Ref TObjectID="6192450001960965_5066549592064002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1513.67,799) scale(3.09524,-0.833333) translate(-1017.3,-1760.3)" width="7" x="1502.833333333333" y="786.5000000000001"/></g>
  <g id="105">
   <use class="kv10" height="30" transform="rotate(0,1573.67,799) scale(3.09524,-0.833333) translate(-1057.92,-1760.3)" width="7" x="1562.833333333333" xlink:href="#ACLineSegment:线路_0" y="786.5000000000001" zvalue="484"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450002157573" ObjectName="热电Ⅰ回"/>
   <cge:TPSR_Ref TObjectID="6192450002157573_5066549592064002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1573.67,799) scale(3.09524,-0.833333) translate(-1057.92,-1760.3)" width="7" x="1562.833333333333" y="786.5000000000001"/></g>
  <g id="114">
   <use class="kv10" height="30" transform="rotate(0,1633.67,799) scale(3.09524,-0.833333) translate(-1098.53,-1760.3)" width="7" x="1622.833333333333" xlink:href="#ACLineSegment:线路_0" y="786.5000000000001" zvalue="494"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450002747397" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192450002747397_5066549592064002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1633.67,799) scale(3.09524,-0.833333) translate(-1098.53,-1760.3)" width="7" x="1622.833333333333" y="786.5000000000001"/></g>
  <g id="122">
   <use class="kv10" height="30" transform="rotate(0,1693.67,799) scale(3.09524,-0.833333) translate(-1139.15,-1760.3)" width="7" x="1682.833333333333" xlink:href="#ACLineSegment:线路_0" y="786.5000000000001" zvalue="504"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450002354181" ObjectName="备用1"/>
   <cge:TPSR_Ref TObjectID="6192450002354181_5066549592064002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1693.67,799) scale(3.09524,-0.833333) translate(-1139.15,-1760.3)" width="7" x="1682.833333333333" y="786.5000000000001"/></g>
  <g id="131">
   <use class="kv10" height="30" transform="rotate(0,1753.67,799) scale(3.09524,-0.833333) translate(-1179.76,-1760.3)" width="7" x="1742.833333333333" xlink:href="#ACLineSegment:线路_0" y="786.5000000000001" zvalue="513"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450002550789" ObjectName="压榨车间Ⅰ回"/>
   <cge:TPSR_Ref TObjectID="6192450002550789_5066549592064002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1753.67,799) scale(3.09524,-0.833333) translate(-1179.76,-1760.3)" width="7" x="1742.833333333333" y="786.5000000000001"/></g>
  <g id="151">
   <use class="kv10" height="30" transform="rotate(0,551.667,799) scale(3.09524,-0.833333) translate(-366.103,-1760.3)" width="7" x="540.8333333333335" xlink:href="#ACLineSegment:线路_0" y="786.5000000000001" zvalue="529"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450002812933" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192450002812933_5066549592064002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,551.667,799) scale(3.09524,-0.833333) translate(-366.103,-1760.3)" width="7" x="540.8333333333335" y="786.5000000000001"/></g>
  <g id="161">
   <use class="kv10" height="30" transform="rotate(0,615.667,799) scale(3.09524,-0.833333) translate(-409.426,-1760.3)" width="7" x="604.8333333333335" xlink:href="#ACLineSegment:线路_0" y="786.5000000000001" zvalue="536"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450003009541" ObjectName="制炼循环水站"/>
   <cge:TPSR_Ref TObjectID="6192450003009541_5066549592064002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,615.667,799) scale(3.09524,-0.833333) translate(-409.426,-1760.3)" width="7" x="604.8333333333335" y="786.5000000000001"/></g>
  <g id="171">
   <use class="kv10" height="30" transform="rotate(0,675.667,799) scale(3.09524,-0.833333) translate(-450.041,-1760.3)" width="7" x="664.8333333333335" xlink:href="#ACLineSegment:线路_0" y="786.5000000000001" zvalue="546"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450003206149" ObjectName="制炼车间Ⅱ回"/>
   <cge:TPSR_Ref TObjectID="6192450003206149_5066549592064002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,675.667,799) scale(3.09524,-0.833333) translate(-450.041,-1760.3)" width="7" x="664.8333333333335" y="786.5000000000001"/></g>
  <g id="180">
   <use class="kv10" height="30" transform="rotate(0,735.667,799) scale(3.09524,-0.833333) translate(-490.656,-1760.3)" width="7" x="724.8333333333335" xlink:href="#ACLineSegment:线路_0" y="786.5000000000001" zvalue="556"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450003402757" ObjectName="制炼车间Ⅰ回"/>
   <cge:TPSR_Ref TObjectID="6192450003402757_5066549592064002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,735.667,799) scale(3.09524,-0.833333) translate(-490.656,-1760.3)" width="7" x="724.8333333333335" y="786.5000000000001"/></g>
  <g id="189">
   <use class="kv10" height="30" transform="rotate(0,795.667,799) scale(3.09524,-0.833333) translate(-531.272,-1760.3)" width="7" x="784.8333333333335" xlink:href="#ACLineSegment:线路_0" y="786.5000000000001" zvalue="566"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450003599365" ObjectName="备用2"/>
   <cge:TPSR_Ref TObjectID="6192450003599365_5066549592064002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,795.667,799) scale(3.09524,-0.833333) translate(-531.272,-1760.3)" width="7" x="784.8333333333335" y="786.5000000000001"/></g>
  <g id="198">
   <use class="kv10" height="30" transform="rotate(0,855.667,799) scale(3.09524,-0.833333) translate(-571.887,-1760.3)" width="7" x="844.8333333333335" xlink:href="#ACLineSegment:线路_0" y="786.5000000000001" zvalue="576"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450003795973" ObjectName="锅炉高压机"/>
   <cge:TPSR_Ref TObjectID="6192450003795973_5066549592064002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,855.667,799) scale(3.09524,-0.833333) translate(-571.887,-1760.3)" width="7" x="844.8333333333335" y="786.5000000000001"/></g>
  <g id="207">
   <use class="kv10" height="30" transform="rotate(0,915.667,799) scale(3.09524,-0.833333) translate(-612.503,-1760.3)" width="7" x="904.8333333333335" xlink:href="#ACLineSegment:线路_0" y="786.5000000000001" zvalue="586"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450003992581" ObjectName="热电Ⅱ回"/>
   <cge:TPSR_Ref TObjectID="6192450003992581_5066549592064002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,915.667,799) scale(3.09524,-0.833333) translate(-612.503,-1760.3)" width="7" x="904.8333333333335" y="786.5000000000001"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="55">
   <use class="kv10" height="20" transform="rotate(0,1289.5,684) scale(1.5,1.5) translate(-427.333,-223)" width="10" x="1282" xlink:href="#GroundDisconnector:地刀_0" y="669" zvalue="442"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450001240069" ObjectName="污水处理05867接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450001240069"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1289.5,684) scale(1.5,1.5) translate(-427.333,-223)" width="10" x="1282" y="669"/></g>
  <g id="73">
   <use class="kv10" height="20" transform="rotate(0,1413.5,684) scale(1.5,1.5) translate(-468.667,-223)" width="10" x="1406" xlink:href="#GroundDisconnector:地刀_0" y="669" zvalue="460"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450001502213" ObjectName="生活区一05767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450001502213"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1413.5,684) scale(1.5,1.5) translate(-468.667,-223)" width="10" x="1406" y="669"/></g>
  <g id="82">
   <use class="kv10" height="20" transform="rotate(0,1473.5,684) scale(1.5,1.5) translate(-488.667,-223)" width="10" x="1466" xlink:href="#GroundDisconnector:地刀_0" y="669" zvalue="470"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450001698821" ObjectName="泵站05667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450001698821"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1473.5,684) scale(1.5,1.5) translate(-488.667,-223)" width="10" x="1466" y="669"/></g>
  <g id="91">
   <use class="kv10" height="20" transform="rotate(0,1533.5,684) scale(1.5,1.5) translate(-508.667,-223)" width="10" x="1526" xlink:href="#GroundDisconnector:地刀_0" y="669" zvalue="480"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450001895429" ObjectName="#1无功补偿05567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450001895429"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1533.5,684) scale(1.5,1.5) translate(-508.667,-223)" width="10" x="1526" y="669"/></g>
  <g id="101">
   <use class="kv10" height="20" transform="rotate(0,1593.5,684) scale(1.5,1.5) translate(-528.667,-223)" width="10" x="1586" xlink:href="#GroundDisconnector:地刀_0" y="669" zvalue="490"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450002092037" ObjectName="热电Ⅰ回05467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450002092037"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1593.5,684) scale(1.5,1.5) translate(-528.667,-223)" width="10" x="1586" y="669"/></g>
  <g id="118">
   <use class="kv10" height="20" transform="rotate(0,1713.5,684) scale(1.5,1.5) translate(-568.667,-223)" width="10" x="1706" xlink:href="#GroundDisconnector:地刀_0" y="669" zvalue="510"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450002288645" ObjectName="备用05267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450002288645"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1713.5,684) scale(1.5,1.5) translate(-568.667,-223)" width="10" x="1706" y="669"/></g>
  <g id="127">
   <use class="kv10" height="20" transform="rotate(0,1773.5,684) scale(1.5,1.5) translate(-588.667,-223)" width="10" x="1766" xlink:href="#GroundDisconnector:地刀_0" y="669" zvalue="518"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450002485253" ObjectName="压榨车间Ⅰ回05167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450002485253"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1773.5,684) scale(1.5,1.5) translate(-588.667,-223)" width="10" x="1766" y="669"/></g>
  <g id="157">
   <use class="kv10" height="20" transform="rotate(0,635.5,684) scale(1.5,1.5) translate(-209.333,-223)" width="10" x="628" xlink:href="#GroundDisconnector:地刀_0" y="669" zvalue="542"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450002944005" ObjectName="制炼循环水站06767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450002944005"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,635.5,684) scale(1.5,1.5) translate(-209.333,-223)" width="10" x="628" y="669"/></g>
  <g id="166">
   <use class="kv10" height="20" transform="rotate(0,695.5,684) scale(1.5,1.5) translate(-229.333,-223)" width="10" x="688" xlink:href="#GroundDisconnector:地刀_0" y="669" zvalue="552"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450003140613" ObjectName="制炼车间Ⅱ回06667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450003140613"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,695.5,684) scale(1.5,1.5) translate(-229.333,-223)" width="10" x="688" y="669"/></g>
  <g id="176">
   <use class="kv10" height="20" transform="rotate(0,755.5,684) scale(1.5,1.5) translate(-249.333,-223)" width="10" x="748" xlink:href="#GroundDisconnector:地刀_0" y="669" zvalue="562"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450003337221" ObjectName="制炼车间Ⅰ回06567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450003337221"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,755.5,684) scale(1.5,1.5) translate(-249.333,-223)" width="10" x="748" y="669"/></g>
  <g id="185">
   <use class="kv10" height="20" transform="rotate(0,815.5,684) scale(1.5,1.5) translate(-269.333,-223)" width="10" x="808" xlink:href="#GroundDisconnector:地刀_0" y="669" zvalue="572"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450003533829" ObjectName="备用06467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450003533829"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,815.5,684) scale(1.5,1.5) translate(-269.333,-223)" width="10" x="808" y="669"/></g>
  <g id="194">
   <use class="kv10" height="20" transform="rotate(0,875.5,684) scale(1.5,1.5) translate(-289.333,-223)" width="10" x="868" xlink:href="#GroundDisconnector:地刀_0" y="669" zvalue="582"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450003730437" ObjectName="锅炉高压机06367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450003730437"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,875.5,684) scale(1.5,1.5) translate(-289.333,-223)" width="10" x="868" y="669"/></g>
  <g id="203">
   <use class="kv10" height="20" transform="rotate(0,935.5,684) scale(1.5,1.5) translate(-309.333,-223)" width="10" x="928" xlink:href="#GroundDisconnector:地刀_0" y="669" zvalue="592"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450003927045" ObjectName="热电Ⅱ回06267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450003927045"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,935.5,684) scale(1.5,1.5) translate(-309.333,-223)" width="10" x="928" y="669"/></g>
 </g>
</svg>