<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549596782594" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="ACLineSegment:带避雷器线路PT的线路_0" viewBox="0,0,28,25">
   <use terminal-index="0" type="0" x="7.889690721649489" xlink:href="#terminal" y="23.4778860569715"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.705760715003304" x2="3.705760715003304" y1="17.98165807560139" y2="20.28165807560139"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.1" x2="7.9" y1="20.35" y2="20.35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.99493209648661" x2="15.73801133176259" y1="13.42856966348774" y2="13.42856966348774"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.05708746256016" x2="12.05708746256016" y1="18.21462834614074" y2="20.31394252867895"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.05708746256016" x2="12.05708746256016" y1="15.83326413498526" y2="10.847392951457"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.881013745704481" x2="7.881013745704481" y1="23.33200899550225" y2="0.6833333333333229"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.0715036948084" x2="12.0715036948084" y1="8.626739580957119" y2="6.39621826201027"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.520172840926069" x2="14.92116229358282" y1="6.262853550392695" y2="6.262853550392695"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.11740027094565" x2="12.11740027094565" y1="10.74142612107711" y2="10.74142612107711"/>
   <ellipse cx="23.44" cy="13.33" fill-opacity="0" rx="3.84" ry="3.87" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.45" cy="8.369999999999999" fill-opacity="0" rx="3.84" ry="3.87" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.43" cy="13.38" fill-opacity="0" rx="3.84" ry="3.87" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.49620888348457" x2="14.79890446679221" y1="10.7414261210771" y2="10.7414261210771"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.39664267427136" x2="14.13972190954734" y1="5.17713898780255" y2="5.17713898780255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.18422026904639" x2="12.89979825188121" y1="4.36285306585993" y2="4.36285306585993"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.11740027094565" x2="12.11740027094565" y1="18.06999941856068" y2="18.06999941856068"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.49620888348457" x2="14.79890446679221" y1="18.06999941856068" y2="18.06999941856068"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.496208883484574" x2="14.79890446679222" y1="8.705711316220558" y2="8.705711316220558"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.49620888348457" x2="14.79890446679221" y1="16.03428461370413" y2="16.03428461370413"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(90,3.74,12.62) scale(1,1) translate(0,0)" width="10.7" x="-1.61" y="10.12"/>
   <path d="M 5.09221 10.9572 L 2.32976 10.9572 L 3.734 14.8326 L 5.09221 10.9572" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.786782704008788" x2="1.643104543089253" y1="5.14716501266186" y2="5.14716501266186"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.71494362354902" x2="3.71494362354902" y1="14.69035020469595" y2="5.122943730448588"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.981067506052216" x2="2.218615398772524" y1="4.178313724130515" y2="4.178313724130515"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.405556650368943" x2="3.139432767865758" y1="3.451675257731946" y2="3.451675257731946"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.85" x2="3.65" y1="20.35" y2="20.35"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="Accessory:接地变接地设备_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.75"/>
   <rect fill-opacity="0" height="13.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.94,7.54) scale(1,1) translate(0,0)" width="6.08" x="2.9" y="0.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.2499999999999956" x2="11.58333333333334" y1="20.49453511141348" y2="20.49453511141348"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.833333333333332" x2="10" y1="22.90451817731685" y2="22.90451817731685"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6" x2="6" y1="20.66666666666667" y2="14.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.934588934424228" x2="7.898744398909104" y1="25.38116790988687" y2="25.38116790988687"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:四绕组PT带熔断器_0" viewBox="0,0,35,35">
   <use terminal-index="0" type="0" x="17.56245852479325" xlink:href="#terminal" y="34.62373692455962"/>
   <rect fill-opacity="0" height="3.55" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,30.81,10.63) scale(1,1) translate(0,0)" width="8.92" x="26.34" y="8.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.61245852479332" x2="34.61245852479332" y1="14.09040359122622" y2="7.090403591226224"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666667" x2="31" y1="17.94225544307809" y2="17.94225544307809"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.86245852479331" x2="30.86245852479331" y1="17.93299618381883" y2="15.09040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.6124585247933" x2="26.6124585247933" y1="16.09040359122623" y2="14.09040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.61245852479333" x2="34.61245852479333" y1="7.09040359122622" y2="5.09040359122622"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,17.58,28.25) scale(1,1) translate(0,0)" width="5" x="15.08" y="23.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.647721108666" x2="30.647721108666" y1="6.086748218596288" y2="2.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.71438777533266" x2="28.71438777533266" y1="2.766127685651949" y2="2.766127685651949"/>
   <ellipse cx="14.04" cy="17.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.05048569403981" x2="22.05048569403981" y1="29.78084700683307" y2="29.78084700683307"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.7608382776216" x2="28.48402243293775" y1="3.004208141873306" y2="3.004208141873306"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.01083827762157" x2="29.40068909960439" y1="1.75420814187332" y2="1.75420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.51083827762159" x2="30.06735576627107" y1="0.2542081418732955" y2="0.2542081418732955"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51457106407813" x2="19.32090482830307" y1="19.30654513693459" y2="16.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58713830216066" x2="22.81883560169719" y1="16.79040359122622" y2="16.79040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.89410730945214" x2="23.0877735452272" y1="19.40299989806297" y2="16.78208583485582"/>
   <ellipse cx="14.04" cy="10.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.04" cy="17.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.3644003886642" x2="22.3644003886642" y1="25.44213090500034" y2="25.44213090500034"/>
   <ellipse cx="21.04" cy="10.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.4422038255927" x2="16.4422038255927" y1="12.07076893362115" y2="12.07076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.19220382559271" x2="17.19220382559271" y1="18.32076893362116" y2="18.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.87303257583199" x2="23.32581493230132" y1="10.72611741162254" y2="11.85543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.92264294445647" x2="20.87303257583197" y1="12.24992879670395" y2="10.72611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.873032575832" x2="20.37063985073817" y1="10.72611741162252" y2="8.072985910425688"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.94220382559271" x2="16.94220382559271" y1="11.57076893362115" y2="11.57076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="13.12063985073816" y1="17.97611741162255" y2="15.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583197" y1="19.49992879670398" y2="17.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="16.07581493230131" y1="17.97611741162255" y2="19.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="13.12063985073816" y1="10.72611741162253" y2="8.072985910425691"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583196" y1="12.24992879670396" y2="10.72611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="16.07581493230131" y1="10.72611741162254" y2="11.85543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.59103363843511" x2="17.59103363843511" y1="20.40822158129308" y2="34.83333333333334"/>
  </symbol>
  <symbol id="Accessory:放电间隙4_0" viewBox="0,0,15,15">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.517857142857143" x2="7.517857142857143" y1="0.4377686487201995" y2="11.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.517857142857143" x2="7.517857142857143" y1="13.5" y2="11.35783935656103"/>
   <path d="M 7.53329 8.11336 L 4.01543 4.05453 L 10.9321 4.05453 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id=":光伏发电2_0" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="29.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.000411522633746" x2="5.000411522633746" y1="29.91666666666667" y2="1.116666666666665"/>
   <path d="M 4.91667 14.5224 L 3.5 7.60569 L 5 11.1057 L 6.5 7.43903 L 4.95238 14.739" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:110kV东坡光伏主变_0" viewBox="0,0,43,30">
   <use terminal-index="0" type="1" x="32.09064929126657" xlink:href="#terminal" y="0.83736282578875"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.10528120713306" x2="28.03920465710243" y1="7.390146319158664" y2="3.694602328551218"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="36.31666666666666" x2="32.09796524919982" y1="3.444602328551216" y2="7.390146319158664"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.09268404206676" x2="32.09268404206676" y1="7.404778235025146" y2="11.43728637061797"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="42.62117055326932" x2="41.62117055326932" y1="1.925582990397805" y2="4.925582990397805"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="21.62277091906721" x2="42.62277091906721" y1="12.84064929126657" y2="1.923982624599901"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="39.61385459533608" x2="42.61385459533608" y1="0.925582990397805" y2="1.925582990397805"/>
   <ellipse cx="32.18" cy="9.19" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="32.10285779606767" xlink:href="#terminal" y="7.39147233653406"/>
  </symbol>
  <symbol id="PowerTransformer2:110kV东坡光伏主变_1" viewBox="0,0,43,30">
   <use terminal-index="1" type="1" x="32.08333333333334" xlink:href="#terminal" y="28.80121170553269"/>
   <ellipse cx="32.18" cy="20.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.27194787379972" x2="28.2058713237691" y1="21.05681298582533" y2="17.36126899521788"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="36.48333333333333" x2="32.26463191586649" y1="17.11126899521788" y2="21.05681298582533"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.25935070873342" x2="32.25935070873342" y1="21.07144490169181" y2="25.10395303728464"/>
   <use terminal-index="3" type="2" x="32.08333333333334" xlink:href="#terminal" y="21"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.750000000000004" y2="4.166666666666668"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="32"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="1" y1="27" y2="11"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="27" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Accessory:厂用变2020_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <path d="M 13 13.5 L 17 13.5 L 15 10.5 L 13 13.5 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="20.66666666666666" y2="22.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="17.91666666666666" y2="20.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="25.41666666666667" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="20.66666666666666" y2="22.66666666666666"/>
   <ellipse cx="14.95" cy="20.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.95" cy="12.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0833 3.5 L 14.0833 4.5 L 16.0833 4.5 L 15.0833 3.5 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="1" y2="7.333333333333331"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变20231222_0" viewBox="0,0,18,25">
   <use terminal-index="0" type="0" x="9" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="0.25" y2="24.25"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷SVG_0" viewBox="0,0,19,38">
   <use terminal-index="0" type="0" x="9.5" xlink:href="#terminal" y="1"/>
   <text fill="rgb(0,0,0)" font-family="宋体" font-size="3" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" text-anchor="middle" x="9.598524611034447" xml:space="preserve" y="27.83333333333334">IGBT</text>
   <rect fill-opacity="0" height="11.97" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,9.34,27.32) scale(1,1) translate(0,0)" width="6.24" x="6.22" y="21.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.418412848215781" x2="9.654606963472311" y1="7.405540230476685" y2="7.405540230476685"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.654606963472311" x2="9.654606963472311" y1="7.405540230476694" y2="15.00091408531136"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.072888846454279" x2="13.34180025300773" y1="37.78754286994457" y2="37.78754286994457"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.381314666673847" x2="9.381314666673847" y1="33.16666666666667" y2="37.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.28748845890916" x2="13.28748845890916" y1="35.14710565745744" y2="37.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.089989994732893" x2="6.089989994732893" y1="35.0999590698429" y2="37.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.632742212616975" x2="9.632742212616975" y1="21.40056988126481" y2="14.86865633162021"/>
   <path d="M 1.99207 7.50181 A 6.04954 7.86213 -90 1 0 9.30345 1.46688" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV东坡光伏电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="62.32" xlink:href="logo.png" y="55.5"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,190.321,85.5) scale(1,1) translate(0,0)" writing-mode="lr" x="190.32" xml:space="preserve" y="89" zvalue="8"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,190.821,85.1903) scale(1,1) translate(0,0)" writing-mode="lr" x="190.82" xml:space="preserve" y="94.19" zvalue="9">110kV东坡光伏电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="1" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,83.9464,238.25) scale(1,1) translate(0,0)" width="72.88" x="47.51" y="226.25" zvalue="42"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.9464,238.25) scale(1,1) translate(0,0)" writing-mode="lr" x="83.95" xml:space="preserve" y="242.75" zvalue="42">信号一览</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="99" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,85.2946,282.286) scale(1,1) translate(0,0)" width="72.88" x="48.86" y="270.29" zvalue="589"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.2946,282.286) scale(1,1) translate(0,0)" writing-mode="lr" x="85.29000000000001" xml:space="preserve" y="286.79" zvalue="589">AVC / AGC</text>
  <line fill="none" id="31" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="396.3214285714287" x2="396.3214285714287" y1="23.5" y2="1053.5" zvalue="10"/>
  <line fill="none" id="29" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.32142857142912" x2="389.3214285714287" y1="159.3704926140824" y2="159.3704926140824" zvalue="12"/>
  <line fill="none" id="28" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.32142857142912" x2="389.3214285714287" y1="629.3704926140824" y2="629.3704926140824" zvalue="13"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="22.32142857142867" x2="112.3214285714287" y1="944.5" y2="944.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="22.32142857142867" x2="112.3214285714287" y1="983.6632999999999" y2="983.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="22.32142857142867" x2="22.32142857142867" y1="944.5" y2="983.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="112.3214285714287" y1="944.5" y2="983.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="382.3214285714287" y1="944.5" y2="944.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="382.3214285714287" y1="983.6632999999999" y2="983.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="112.3214285714287" y1="944.5" y2="983.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="382.3214285714287" x2="382.3214285714287" y1="944.5" y2="983.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="22.32142857142867" x2="112.3214285714287" y1="983.66327" y2="983.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="22.32142857142867" x2="112.3214285714287" y1="1011.58167" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="22.32142857142867" x2="22.32142857142867" y1="983.66327" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="112.3214285714287" y1="983.66327" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="202.3214285714287" y1="983.66327" y2="983.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="202.3214285714287" y1="1011.58167" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="112.3214285714287" y1="983.66327" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="202.3214285714287" x2="202.3214285714287" y1="983.66327" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="202.3214285714288" x2="292.3214285714288" y1="983.66327" y2="983.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="202.3214285714288" x2="292.3214285714288" y1="1011.58167" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="202.3214285714288" x2="202.3214285714288" y1="983.66327" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="292.3214285714288" x2="292.3214285714288" y1="983.66327" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="292.3214285714287" x2="382.3214285714287" y1="983.66327" y2="983.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="292.3214285714287" x2="382.3214285714287" y1="1011.58167" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="292.3214285714287" x2="292.3214285714287" y1="983.66327" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="382.3214285714287" x2="382.3214285714287" y1="983.66327" y2="1011.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="22.32142857142867" x2="112.3214285714287" y1="1011.5816" y2="1011.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="22.32142857142867" x2="112.3214285714287" y1="1039.5" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="22.32142857142867" x2="22.32142857142867" y1="1011.5816" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="112.3214285714287" y1="1011.5816" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="202.3214285714287" y1="1011.5816" y2="1011.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="202.3214285714287" y1="1039.5" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.3214285714287" x2="112.3214285714287" y1="1011.5816" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="202.3214285714287" x2="202.3214285714287" y1="1011.5816" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="202.3214285714288" x2="292.3214285714288" y1="1011.5816" y2="1011.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="202.3214285714288" x2="292.3214285714288" y1="1039.5" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="202.3214285714288" x2="202.3214285714288" y1="1011.5816" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="292.3214285714288" x2="292.3214285714288" y1="1011.5816" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="292.3214285714287" x2="382.3214285714287" y1="1011.5816" y2="1011.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="292.3214285714287" x2="382.3214285714287" y1="1039.5" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="292.3214285714287" x2="292.3214285714287" y1="1011.5816" y2="1039.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="382.3214285714287" x2="382.3214285714287" y1="1011.5816" y2="1039.5"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.3214,964.5) scale(1,1) translate(0,0)" writing-mode="lr" x="67.31999999999999" xml:space="preserve" y="970.5" zvalue="15">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,64.3214,998.5) scale(1,1) translate(0,0)" writing-mode="lr" x="64.31999999999999" xml:space="preserve" y="1004.5" zvalue="16">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,246.321,998.5) scale(1,1) translate(0,0)" writing-mode="lr" x="246.32" xml:space="preserve" y="1004.5" zvalue="17">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63.3214,1026.5) scale(1,1) translate(0,0)" writing-mode="lr" x="63.32" xml:space="preserve" y="1032.5" zvalue="18">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,245.321,1026.5) scale(1,1) translate(0,0)" writing-mode="lr" x="245.32" xml:space="preserve" y="1032.5" zvalue="19">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,87.8214,659) scale(1,1) translate(0,0)" writing-mode="lr" x="87.82142857142867" xml:space="preserve" y="663.5" zvalue="21">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,247.376,966.5) scale(1,1) translate(0,0)" writing-mode="lr" x="247.38" xml:space="preserve" y="972.5" zvalue="22">DongPo-01-2024</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,157.376,998.5) scale(1,1) translate(0,0)" writing-mode="lr" x="157.38" xml:space="preserve" y="1004.5" zvalue="23">唐涛</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,337.375,998.5) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="1004.5" zvalue="24">20231106</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.25" x2="201.25" y1="163.5" y2="163.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.25" x2="201.25" y1="189.5" y2="189.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.25" x2="20.25" y1="163.5" y2="189.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.25" x2="201.25" y1="163.5" y2="189.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.25" x2="382.25" y1="163.5" y2="163.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.25" x2="382.25" y1="189.5" y2="189.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.25" x2="201.25" y1="163.5" y2="189.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="382.25" x2="382.25" y1="163.5" y2="189.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.25" x2="201.25" y1="189.5" y2="189.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.25" x2="201.25" y1="213" y2="213"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="20.25" x2="20.25" y1="189.5" y2="213"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.25" x2="201.25" y1="189.5" y2="213"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.25" x2="382.25" y1="189.5" y2="189.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.25" x2="382.25" y1="213" y2="213"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="201.25" x2="201.25" y1="189.5" y2="213"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="382.25" x2="382.25" y1="189.5" y2="213"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="456.6666435058594" y2="456.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="494.1566435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="50.72202829616253" y1="456.6666435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="456.6666435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="456.6666435058594" y2="456.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="494.1566435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="456.6666435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5161282961625" x2="161.5161282961625" y1="456.6666435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="456.6666435058594" y2="456.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="494.1566435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="161.5160282961625" y1="456.6666435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1818282961625" x2="234.1818282961625" y1="456.6666435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="456.6666435058594" y2="456.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="494.1566435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="234.1817282961625" y1="456.6666435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4818282961625" x2="296.4818282961625" y1="456.6666435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="456.6666435058594" y2="456.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="494.1566435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="296.4817282961625" y1="456.6666435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="358.7818282961625" x2="358.7818282961625" y1="456.6666435058594" y2="494.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="494.1567435058594" y2="494.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="518.3253435058593" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="50.72202829616253" y1="494.1567435058594" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="494.1567435058594" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="494.1567435058594" y2="494.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="518.3253435058593" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="494.1567435058594" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5161282961625" x2="161.5161282961625" y1="494.1567435058594" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="494.1567435058594" y2="494.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="518.3253435058593" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="161.5160282961625" y1="494.1567435058594" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1818282961625" x2="234.1818282961625" y1="494.1567435058594" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="494.1567435058594" y2="494.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="518.3253435058593" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="234.1817282961625" y1="494.1567435058594" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4818282961625" x2="296.4818282961625" y1="494.1567435058594" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="494.1567435058594" y2="494.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="518.3253435058593" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="296.4817282961625" y1="494.1567435058594" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="358.7818282961625" x2="358.7818282961625" y1="494.1567435058594" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="518.3253435058593" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="542.4939435058594" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="50.72202829616253" y1="518.3253435058593" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="518.3253435058593" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="518.3253435058593" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="542.4939435058594" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="518.3253435058593" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5161282961625" x2="161.5161282961625" y1="518.3253435058593" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="518.3253435058593" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="542.4939435058594" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="161.5160282961625" y1="518.3253435058593" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1818282961625" x2="234.1818282961625" y1="518.3253435058593" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="518.3253435058593" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="542.4939435058594" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="234.1817282961625" y1="518.3253435058593" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4818282961625" x2="296.4818282961625" y1="518.3253435058593" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="518.3253435058593" y2="518.3253435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="542.4939435058594" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="296.4817282961625" y1="518.3253435058593" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="358.7818282961625" x2="358.7818282961625" y1="518.3253435058593" y2="542.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="542.4940335058594" y2="542.4940335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="566.6626335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="50.72202829616253" y1="542.4940335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="542.4940335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="542.4940335058594" y2="542.4940335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="566.6626335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="542.4940335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5161282961625" x2="161.5161282961625" y1="542.4940335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="542.4940335058594" y2="542.4940335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="566.6626335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="161.5160282961625" y1="542.4940335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1818282961625" x2="234.1818282961625" y1="542.4940335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="542.4940335058594" y2="542.4940335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="566.6626335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="234.1817282961625" y1="542.4940335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4818282961625" x2="296.4818282961625" y1="542.4940335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="542.4940335058594" y2="542.4940335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="566.6626335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="296.4817282961625" y1="542.4940335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="358.7818282961625" x2="358.7818282961625" y1="542.4940335058594" y2="566.6626335058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="566.6628435058594" y2="566.6628435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="590.8314435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="50.72202829616253" y1="566.6628435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="566.6628435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="566.6628435058594" y2="566.6628435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="590.8314435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="566.6628435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5161282961625" x2="161.5161282961625" y1="566.6628435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="566.6628435058594" y2="566.6628435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="590.8314435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="161.5160282961625" y1="566.6628435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1818282961625" x2="234.1818282961625" y1="566.6628435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="566.6628435058594" y2="566.6628435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="590.8314435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="234.1817282961625" y1="566.6628435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4818282961625" x2="296.4818282961625" y1="566.6628435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="566.6628435058594" y2="566.6628435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="590.8314435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="296.4817282961625" y1="566.6628435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="358.7818282961625" x2="358.7818282961625" y1="566.6628435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="590.8314435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="99.21602829616245" y1="615.0000435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="50.72202829616253" x2="50.72202829616253" y1="590.8314435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="590.8314435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="590.8314435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="161.5161282961625" y1="615.0000435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.21602829616245" x2="99.21602829616245" y1="590.8314435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5161282961625" x2="161.5161282961625" y1="590.8314435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="590.8314435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="234.1818282961625" y1="615.0000435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="161.5160282961625" x2="161.5160282961625" y1="590.8314435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1818282961625" x2="234.1818282961625" y1="590.8314435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="590.8314435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="296.4818282961625" y1="615.0000435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.1817282961625" x2="234.1817282961625" y1="590.8314435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4818282961625" x2="296.4818282961625" y1="590.8314435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="590.8314435058594" y2="590.8314435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="358.7818282961625" y1="615.0000435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="296.4817282961625" x2="296.4817282961625" y1="590.8314435058594" y2="615.0000435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="358.7818282961625" x2="358.7818282961625" y1="590.8314435058594" y2="615.0000435058594"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,172.149,236.341) scale(1,1) translate(0,0)" writing-mode="lr" x="172.15" xml:space="preserve" y="240.84" zvalue="31">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,277.149,236.341) scale(1,1) translate(0,0)" writing-mode="lr" x="277.15" xml:space="preserve" y="240.84" zvalue="32">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,188.917,476.25) scale(1,1) translate(0,0)" writing-mode="lr" x="188.9166666666665" xml:space="preserve" y="480.7500000000001" zvalue="33">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.75,504) scale(1,1) translate(0,5.43454e-14)" writing-mode="lr" x="70.75" xml:space="preserve" y="508.5000000000001" zvalue="34">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.75,529.5) scale(1,1) translate(0,0)" writing-mode="lr" x="70.75" xml:space="preserve" y="534" zvalue="35">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.75,555) scale(1,1) translate(0,0)" writing-mode="lr" x="70.75" xml:space="preserve" y="559.5" zvalue="36">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.75,579.5) scale(1,1) translate(0,0)" writing-mode="lr" x="69.75" xml:space="preserve" y="584" zvalue="37">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.75,606) scale(1,1) translate(0,0)" writing-mode="lr" x="70.75" xml:space="preserve" y="610.5" zvalue="38">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.75,175.5) scale(1,1) translate(0,0)" writing-mode="lr" x="52.75" xml:space="preserve" y="181" zvalue="39">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.75,175.5) scale(1,1) translate(0,0)" writing-mode="lr" x="232.75" xml:space="preserve" y="181" zvalue="40">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" x="125.921875" xml:space="preserve" y="473.265625" zvalue="49">110kV母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="125.921875" xml:space="preserve" y="490.265625" zvalue="49">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.0972,199.444) scale(1,1) translate(0,0)" writing-mode="lr" x="67.09999999999999" xml:space="preserve" y="203.94" zvalue="61">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,699.173,680.202) scale(1,1) translate(0,0)" writing-mode="lr" x="699.17" xml:space="preserve" y="684.7" zvalue="65">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,979,95.644) scale(1,1) translate(0,0)" writing-mode="lr" x="979" xml:space="preserve" y="100.14" zvalue="66">110kV东坡光伏电站线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,957.738,287.157) scale(1,1) translate(0,0)" writing-mode="lr" x="957.74" xml:space="preserve" y="291.66" zvalue="68">171</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,999.371,363.257) scale(1,1) translate(0,0)" writing-mode="lr" x="999.37" xml:space="preserve" y="367.76" zvalue="79">3</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1042.97,253.389) scale(1,1) translate(5.73878e-13,0)" writing-mode="lr" x="1042.97" xml:space="preserve" y="257.89" zvalue="84">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,997.63,216.727) scale(1,1) translate(-4.3993e-13,0)" writing-mode="lr" x="997.63" xml:space="preserve" y="221.23" zvalue="86">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1138.95,497.72) scale(1,1) translate(0,-1.08629e-13)" writing-mode="lr" x="1138.95" xml:space="preserve" y="502.22" zvalue="125">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,945.82,658.543) scale(1,1) translate(0,0)" writing-mode="lr" x="945.8200000000001" xml:space="preserve" y="663.04" zvalue="150">301</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" x="900.0859375" xml:space="preserve" y="515.5508130183639" zvalue="156">110kV1号主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="900.0859375" xml:space="preserve" y="531.5508130183639" zvalue="156">(SZ18-100000/110)</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="166" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1059.2,533.329) scale(1,1) translate(0,0)" writing-mode="lr" x="1059.2" xml:space="preserve" y="537.83" zvalue="161">3010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,980.693,949.614) scale(1,1) translate(0,0)" writing-mode="lr" x="980.6900000000001" xml:space="preserve" y="954.11" zvalue="164">35kVSVG</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1004.66,736.758) scale(1,1) translate(0,0)" writing-mode="lr" x="1004.66" xml:space="preserve" y="741.26" zvalue="174">372</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,940.981,792.755) scale(1,1) translate(0,0)" writing-mode="lr" x="940.98" xml:space="preserve" y="797.26" zvalue="178">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,995.518,823.372) scale(1,1) translate(5.49389e-13,0)" writing-mode="lr" x="995.52" xml:space="preserve" y="827.87" zvalue="181">8</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="170" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,924.485,842.543) scale(1,1) translate(2.035e-13,0)" writing-mode="lr" x="924.48" xml:space="preserve" y="847.04" zvalue="189">87</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="175" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,833.68,933.719) scale(1,1) translate(0,-2.02462e-13)" writing-mode="lr" x="833.6795306038673" xml:space="preserve" y="938.2191550067216" zvalue="196">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,858.259,734.861) scale(1,1) translate(0,0)" writing-mode="lr" x="858.26" xml:space="preserve" y="739.36" zvalue="201">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,730.859,736.758) scale(1,1) translate(0,0)" writing-mode="lr" x="730.86" xml:space="preserve" y="741.26" zvalue="209">371</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,658.266,802.957) scale(1,1) translate(0,0)" writing-mode="lr" x="658.27" xml:space="preserve" y="807.46" zvalue="214">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="214" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1121.3,738.144) scale(1,1) translate(0,0)" writing-mode="lr" x="1121.3" xml:space="preserve" y="742.64" zvalue="222">373</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="215" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1084.38,796.098) scale(1,1) translate(2.38643e-13,0)" writing-mode="lr" x="1084.38" xml:space="preserve" y="800.6" zvalue="224">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1096.43,884.065) scale(1,1) translate(0,0)" writing-mode="lr" x="1096.43" xml:space="preserve" y="888.5599999999999" zvalue="226">35kV#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="224" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1238.04,738.144) scale(1,1) translate(0,0)" writing-mode="lr" x="1238.04" xml:space="preserve" y="742.64" zvalue="243">374</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="223" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1197.99,796.098) scale(1,1) translate(0,0)" writing-mode="lr" x="1197.99" xml:space="preserve" y="800.6" zvalue="246">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="222" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1213.17,880.629) scale(1,1) translate(0,0)" writing-mode="lr" x="1213.17" xml:space="preserve" y="885.13" zvalue="248">35kV#1集电线路</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="239" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1350.93,738.144) scale(1,1) translate(0,0)" writing-mode="lr" x="1350.93" xml:space="preserve" y="742.64" zvalue="259">375</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1310.88,796.098) scale(1,1) translate(0,0)" writing-mode="lr" x="1310.88" xml:space="preserve" y="800.6" zvalue="262">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="237" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1326.57,884.716) scale(1,1) translate(0,1.64612e-12)" writing-mode="lr" x="1326.573569041248" xml:space="preserve" y="889.2163754006169" zvalue="264">35kV#2集电线路</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1599.75,738.144) scale(1,1) translate(0,0)" writing-mode="lr" x="1599.75" xml:space="preserve" y="742.64" zvalue="275">377</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1559.22,795.326) scale(1,1) translate(0,0)" writing-mode="lr" x="1559.22" xml:space="preserve" y="799.83" zvalue="278">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1586.09,882.124) scale(1,1) translate(0,0)" writing-mode="lr" x="1586.08845309004" xml:space="preserve" y="886.6244499653164" zvalue="280">35kV芒东集电线路</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1476.35,738.144) scale(1,1) translate(0,0)" writing-mode="lr" x="1476.35" xml:space="preserve" y="742.64" zvalue="289">376</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="265" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1436.3,796.098) scale(1,1) translate(0,0)" writing-mode="lr" x="1436.3" xml:space="preserve" y="800.6" zvalue="292">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1465.17,884.896) scale(1,1) translate(0,0)" writing-mode="lr" x="1465.172529403761" xml:space="preserve" y="889.3962925002381" zvalue="294">35kV#3集电线路</text>
  <path d="M 951.344 837.398 L 951.344 821.507 L 980.289 821.507" fill="none" id="173" stroke="rgb(255,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="327"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="297" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,983.089,964.753) scale(1,1) translate(0,-2.11831e-13)" writing-mode="lr" x="983.0890170526698" xml:space="preserve" y="969.253274727379" zvalue="527">(+-25Mvar)</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="301" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,698.146,876.955) scale(1,1) translate(0,0)" writing-mode="lr" x="698.1460729377977" xml:space="preserve" y="881.4554856785794" zvalue="530">35kV#1-1储能装置</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1040.31,404.648) scale(1,1) translate(0,0)" writing-mode="lr" x="1040.31" xml:space="preserve" y="409.15" zvalue="535">37</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="306" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1040.4,325.648) scale(1,1) translate(0,0)" writing-mode="lr" x="1040.4" xml:space="preserve" y="330.15" zvalue="541">30</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="310" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1041.94,178.648) scale(1,1) translate(0,0)" writing-mode="lr" x="1041.94" xml:space="preserve" y="183.15" zvalue="546">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1561.11,535.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1561.11" xml:space="preserve" y="539.83" zvalue="551">10kV#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1495.89,473.833) scale(1,1) translate(-6.39883e-13,0)" writing-mode="lr" x="1495.888888888889" xml:space="preserve" y="478.3333333333334" zvalue="552">外部站用电源10kV</text>
  <rect fill="none" height="38" id="61" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1213,293) scale(1,1) translate(0,0)" width="132" x="1147" y="274" zvalue="571"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1213,293) scale(1,1) translate(0,0)" writing-mode="lr" x="1213" xml:space="preserve" y="299" zvalue="571">设计容量93MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,337.375,1024.67) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="1030.67" zvalue="591">20240521</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="47.51" y="226.25" zvalue="42"/></g>
  <g href="新能源AVC AGC 控制.svg"><rect fill-opacity="0" height="24" width="72.88" x="48.86" y="270.29" zvalue="589"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="22" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,158.321,175.25) scale(1,1) translate(0,0)" writing-mode="lr" x="158.47" xml:space="preserve" y="181.57" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128171143172" ObjectName="全站有功总加"/>
   </metadata>
  </g>
  <g id="1035">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="1035" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,340.321,174.25) scale(1,1) translate(0,0)" writing-mode="lr" x="340.47" xml:space="preserve" y="180.57" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128171208708" ObjectName="全站无功总加"/>
   </metadata>
  </g>
  <g id="36">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,193.321,509.167) scale(1,1) translate(0,0)" writing-mode="lr" x="193.04" xml:space="preserve" y="513.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128157773828" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,193.321,530.667) scale(1,1) translate(0,0)" writing-mode="lr" x="193.04" xml:space="preserve" y="535.33" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128157511684" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,193.321,553.167) scale(1,1) translate(0,-1.20496e-13)" writing-mode="lr" x="193.04" xml:space="preserve" y="557.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128157577220" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,193.321,577.667) scale(1,1) translate(0,0)" writing-mode="lr" x="193.04" xml:space="preserve" y="582.33" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128157642756" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="41">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,158.321,200.758) scale(1,1) translate(0,-4.25251e-14)" writing-mode="lr" x="157.96" xml:space="preserve" y="205.4" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128158494724" ObjectName="F"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,130.889,507.167) scale(1,1) translate(-2.30926e-14,0)" writing-mode="lr" x="131.01" xml:space="preserve" y="512.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128275935236" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="98" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,193.321,603.5) scale(1,1) translate(0,0)" writing-mode="lr" x="193.04" xml:space="preserve" y="608.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128157970436" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="131">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="131" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,130.889,531.25) scale(1,1) translate(-2.30926e-14,0)" writing-mode="lr" x="131.01" xml:space="preserve" y="536.16" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128158560260" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="132">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="132" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,131.889,554.333) scale(1,1) translate(-2.33147e-14,-1.20755e-13)" writing-mode="lr" x="132.01" xml:space="preserve" y="559.24" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128158625796" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="150" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,130.889,579.417) scale(1,1) translate(-2.30926e-14,0)" writing-mode="lr" x="131.01" xml:space="preserve" y="584.33" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128158691332" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="157">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="157" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,130.889,603.5) scale(1,1) translate(-2.30926e-14,0)" writing-mode="lr" x="131.01" xml:space="preserve" y="608.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128158756868" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="54" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,978.966,27.4844) scale(1,1) translate(0,-9.41498e-15)" writing-mode="lr" x="937.36" xml:space="preserve" y="31.85" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128158035972" ObjectName="P"/>
   </metadata>
  </g>
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="68" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,978.966,48.7101) scale(1,1) translate(0,2.82672e-14)" writing-mode="lr" x="937.36" xml:space="preserve" y="53.08" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128158101508" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="69">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="69" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,978.966,69.9357) scale(1,1) translate(0,4.71193e-14)" writing-mode="lr" x="937.36" xml:space="preserve" y="74.3" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128158167044" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="177">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="177" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,717.686,651.399) scale(1,1) translate(-1.49718e-13,0)" writing-mode="lr" x="676.08" xml:space="preserve" y="655.77" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128157773828" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="190">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="190" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1046.71,613.839) scale(1,1) translate(-2.21061e-13,0)" writing-mode="lr" x="997.7" xml:space="preserve" y="619.7" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128160264196" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="202">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="202" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1046.71,642.568) scale(1,1) translate(-2.21061e-13,0)" writing-mode="lr" x="997.7" xml:space="preserve" y="648.4299999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128160329732" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="206">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="206" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1046.71,671.298) scale(1,1) translate(-2.21061e-13,5.81236e-13)" writing-mode="lr" x="997.7" xml:space="preserve" y="677.16" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128160722948" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="207">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="207" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,979.098,983.358) scale(1,1) translate(0,-2.15827e-13)" writing-mode="lr" x="937.49" xml:space="preserve" y="987.77" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128162164740" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="208">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="208" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,980.82,1001.13) scale(1,1) translate(0,8.74182e-13)" writing-mode="lr" x="939.21" xml:space="preserve" y="1005.49" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128162230276" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="209">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="209" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1094.73,917.055) scale(1,1) translate(1.16719e-13,7.99512e-13)" writing-mode="lr" x="1053.12" xml:space="preserve" y="921.42" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128164458502" ObjectName="P"/>
   </metadata>
  </g>
  <g id="210">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="210" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1096.48,941.914) scale(1,1) translate(1.16914e-13,8.21591e-13)" writing-mode="lr" x="1054.88" xml:space="preserve" y="946.28" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128164524039" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="213">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="213" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1096.11,966.773) scale(1,1) translate(1.16872e-13,8.43671e-13)" writing-mode="lr" x="1054.5" xml:space="preserve" y="971.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128164589574" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="216">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="216" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1212.48,917.055) scale(1,1) translate(-2.59585e-13,7.99512e-13)" writing-mode="lr" x="1170.88" xml:space="preserve" y="921.42" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128164982788" ObjectName="P"/>
   </metadata>
  </g>
  <g id="220">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="220" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1217.62,941.914) scale(1,1) translate(-2.60726e-13,8.21591e-13)" writing-mode="lr" x="1176.01" xml:space="preserve" y="946.28" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128165048327" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="232">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="232" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1217.52,966.773) scale(1,1) translate(-2.60703e-13,8.43671e-13)" writing-mode="lr" x="1175.91" xml:space="preserve" y="971.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128165113862" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="241">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="241" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1325.63,918.111) scale(1,1) translate(-2.8471e-13,8.00451e-13)" writing-mode="lr" x="1284.03" xml:space="preserve" y="922.48" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128166031364" ObjectName="P"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="245" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1326.14,942.442) scale(1,1) translate(-2.84821e-13,8.22061e-13)" writing-mode="lr" x="1284.53" xml:space="preserve" y="946.8099999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128166096902" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="255">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="255" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1329.39,966.773) scale(1,1) translate(-2.85544e-13,8.43671e-13)" writing-mode="lr" x="1287.79" xml:space="preserve" y="971.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128166162439" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="75">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="75" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,699.292,922.5) scale(1,1) translate(0,0)" writing-mode="lr" x="657.13" xml:space="preserve" y="926.86" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128170225668" ObjectName="P"/>
   </metadata>
  </g>
  <g id="81">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="81" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,701.292,945.5) scale(1,1) translate(2.18922e-13,0)" writing-mode="lr" x="659.13" xml:space="preserve" y="949.86" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128170291204" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="82">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="82" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,699.292,974.5) scale(1,1) translate(0,0)" writing-mode="lr" x="657.13" xml:space="preserve" y="978.86" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128170356740" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="84">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="84" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1450.32,918.111) scale(1,1) translate(6.24789e-13,0)" writing-mode="lr" x="1408.71" xml:space="preserve" y="922.48" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128167079942" ObjectName="P"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="85" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1450.32,942.442) scale(1,1) translate(6.24789e-13,0)" writing-mode="lr" x="1408.71" xml:space="preserve" y="946.8099999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128167145479" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="86" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1450.32,966.773) scale(1,1) translate(6.24789e-13,0)" writing-mode="lr" x="1408.71" xml:space="preserve" y="971.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128167211012" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="87" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1573.23,918.111) scale(1,1) translate(-3.39687e-13,0)" writing-mode="lr" x="1531.63" xml:space="preserve" y="922.48" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128168652804" ObjectName="P"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="88" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1573.23,941.442) scale(1,1) translate(-3.39687e-13,0)" writing-mode="lr" x="1531.63" xml:space="preserve" y="945.8099999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128168718340" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="89" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1573.23,966.773) scale(1,1) translate(-3.39687e-13,0)" writing-mode="lr" x="1531.63" xml:space="preserve" y="971.14" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128168783876" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="103" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1100.89,364.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1045.79" xml:space="preserve" y="370.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128160133124" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="104" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1100.89,401.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1045.79" xml:space="preserve" y="407.36" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128160198660" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="106">
   <text Format="f5.1" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="106" prefix="Pmx:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,193.5,280.484) scale(1,1) translate(0,0)" writing-mode="lr" x="136.48" xml:space="preserve" y="284.85" zvalue="1">Pmx:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128275542020" ObjectName="全厂可调有功上限"/>
   </metadata>
  </g>
  <g id="108">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="108" prefix="Pmn:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,322.775,281.484) scale(1,1) translate(8.8446e-14,0)" writing-mode="lr" x="267.93" xml:space="preserve" y="285.85" zvalue="1">Pmn:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128275607556" ObjectName="全厂可调有功下限"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,308.661,235.143) scale(0.708333,0.665547) translate(122.721,113.148)" width="30" x="298.04" xlink:href="#State:红绿圆(方形)_0" y="225.16" zvalue="46"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374897709060" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,308.661,235.143) scale(0.708333,0.665547) translate(122.721,113.148)" width="30" x="298.04" y="225.16"/></g>
  <g id="21">
   <use height="30" transform="rotate(0,213.036,235.143) scale(0.708333,0.665547) translate(83.3456,113.148)" width="30" x="202.41" xlink:href="#State:红绿圆(方形)_0" y="225.16" zvalue="47"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562959765274630" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,213.036,235.143) scale(0.708333,0.665547) translate(83.3456,113.148)" width="30" x="202.41" y="225.16"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="43">
   <path class="kv35" d="M 672.03 699.78 L 1628.76 699.78" stroke-width="4" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674263367685" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674263367685"/></metadata>
  <path d="M 672.03 699.78 L 1628.76 699.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv110" d="M 980 426 L 984 426" stroke-width="6" zvalue="584"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674264285189" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674264285189"/></metadata>
  <path d="M 980 426 L 984 426" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="45">
   <use class="kv110" height="25" transform="rotate(0,988.407,124.913) scale(1.39552,1.3698) translate(-274.598,-29.1001)" width="28" x="968.8697602346255" xlink:href="#ACLineSegment:带避雷器线路PT的线路_0" y="107.7907995893444" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249330679811" ObjectName="110kV东坡光伏电站线"/>
   <cge:TPSR_Ref TObjectID="8444249330679811_5066549596782594"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,988.407,124.913) scale(1.39552,1.3698) translate(-274.598,-29.1001)" width="28" x="968.8697602346255" y="107.7907995893444"/></g>
 </g>
 <g id="BreakerClass">
  <g id="48">
   <use class="kv110" height="20" transform="rotate(0,981.858,285.228) scale(1.4472,1.30248) translate(-301.171,-63.2155)" width="10" x="974.6217952376595" xlink:href="#Breaker:开关_0" y="272.2030159642017" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924597121028" ObjectName="110kV东坡光伏电站线171断路器"/>
   <cge:TPSR_Ref TObjectID="6473924597121028"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,981.858,285.228) scale(1.4472,1.30248) translate(-301.171,-63.2155)" width="10" x="974.6217952376595" y="272.2030159642017"/></g>
  <g id="151">
   <use class="kv35" height="20" transform="rotate(0,981.506,658.43) scale(1.75856,1.54419) translate(-419.581,-226.596)" width="10" x="972.7128166761785" xlink:href="#Breaker:小车断路器_0" y="642.9878793940439" zvalue="149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924597186564" ObjectName="110kV1号主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924597186564"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,981.506,658.43) scale(1.75856,1.54419) translate(-419.581,-226.596)" width="10" x="972.7128166761785" y="642.9878793940439"/></g>
  <g id="64">
   <use class="kv35" height="20" transform="rotate(0,981.506,737.723) scale(2.12257,2.12257) translate(-513.478,-378.936)" width="10" x="970.892761666988" xlink:href="#Breaker:小车断路器_0" y="716.4975285424615" zvalue="173"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924597252100" ObjectName="35kVSVG372断路器"/>
   <cge:TPSR_Ref TObjectID="6473924597252100"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,981.506,737.723) scale(2.12257,2.12257) translate(-513.478,-378.936)" width="10" x="970.892761666988" y="716.4975285424615"/></g>
  <g id="187">
   <use class="kv35" height="20" transform="rotate(0,705.292,737.723) scale(2.12257,2.12257) translate(-367.397,-378.936)" width="10" x="694.6793110204225" xlink:href="#Breaker:小车断路器_0" y="716.4975285424614" zvalue="208"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924597317636" ObjectName="35kV#1-1储能装置371断路器"/>
   <cge:TPSR_Ref TObjectID="6473924597317636"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,705.292,737.723) scale(2.12257,2.12257) translate(-367.397,-378.936)" width="10" x="694.6793110204225" y="716.4975285424614"/></g>
  <g id="199">
   <use class="kv35" height="20" transform="rotate(0,1094.79,734.723) scale(2.12257,2.12257) translate(-573.389,-377.349)" width="10" x="1084.172681070995" xlink:href="#Breaker:小车断路器_0" y="713.4975283971812" zvalue="221"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924597383172" ObjectName="35kV#1站用变373断路器"/>
   <cge:TPSR_Ref TObjectID="6473924597383172"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1094.79,734.723) scale(2.12257,2.12257) translate(-573.389,-377.349)" width="10" x="1084.172681070995" y="713.4975283971812"/></g>
  <g id="236">
   <use class="kv35" height="20" transform="rotate(0,1211.53,734.723) scale(2.12257,2.12257) translate(-635.13,-377.349)" width="10" x="1200.913864477901" xlink:href="#Breaker:小车断路器_0" y="713.4975283971812" zvalue="242"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924597448708" ObjectName="35kV#1集电线路374断路器"/>
   <cge:TPSR_Ref TObjectID="6473924597448708"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1211.53,734.723) scale(2.12257,2.12257) translate(-635.13,-377.349)" width="10" x="1200.913864477901" y="713.4975283971812"/></g>
  <g id="249">
   <use class="kv35" height="20" transform="rotate(0,1324.41,734.723) scale(2.12257,2.12257) translate(-694.83,-377.349)" width="10" x="1313.795835210198" xlink:href="#Breaker:小车断路器_0" y="713.4975283971812" zvalue="258"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924597514244" ObjectName="35kV#2集电线路375断路器"/>
   <cge:TPSR_Ref TObjectID="6473924597514244"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1324.41,734.723) scale(2.12257,2.12257) translate(-694.83,-377.349)" width="10" x="1313.795835210198" y="713.4975283971812"/></g>
  <g id="263">
   <use class="kv35" height="20" transform="rotate(0,1573.23,734.723) scale(2.12257,2.12257) translate(-826.426,-377.349)" width="10" x="1562.619616012578" xlink:href="#Breaker:小车断路器_0" y="713.4975285812028" zvalue="274"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924597645316" ObjectName="35kV芒东集电线路377断路器"/>
   <cge:TPSR_Ref TObjectID="6473924597645316"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1573.23,734.723) scale(2.12257,2.12257) translate(-826.426,-377.349)" width="10" x="1562.619616012578" y="713.4975285812028"/></g>
  <g id="276">
   <use class="kv35" height="20" transform="rotate(0,1449.83,734.723) scale(2.12257,2.12257) translate(-761.164,-377.349)" width="10" x="1439.220247134973" xlink:href="#Breaker:小车断路器_0" y="713.4975283971812" zvalue="288"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924597579780" ObjectName="35kV#3集电线路376断路器"/>
   <cge:TPSR_Ref TObjectID="6473924597579780"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1449.83,734.723) scale(2.12257,2.12257) translate(-761.164,-377.349)" width="10" x="1439.220247134973" y="713.4975283971812"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="65">
   <use class="kv110" height="30" transform="rotate(0,981.872,359.751) scale(0.932643,0.683938) translate(70.4072,161.507)" width="15" x="974.8776116891091" xlink:href="#Disconnector:刀闸_0" y="349.49231097942" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450338816005" ObjectName="110kV东坡光伏电站线1713隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450338816005"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,981.872,359.751) scale(0.932643,0.683938) translate(70.4072,161.507)" width="15" x="974.8776116891091" y="349.49231097942"/></g>
  <g id="76">
   <use class="kv110" height="30" transform="rotate(0,979.781,214.636) scale(1.1256,0.825443) translate(-108.39,42.771)" width="15" x="971.3391426081679" xlink:href="#Disconnector:刀闸_0" y="202.2547862369235" zvalue="85"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450339012613" ObjectName="110kV东坡光伏电站线1716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450339012613"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,979.781,214.636) scale(1.1256,0.825443) translate(-108.39,42.771)" width="15" x="971.3391426081679" y="202.2547862369235"/></g>
  <g id="165">
   <use class="kv35" height="30" transform="rotate(0,1033.15,530.865) scale(1.17352,0.86058) translate(-151.462,83.9125)" width="15" x="1024.347620135413" xlink:href="#Disconnector:刀闸_0" y="517.956148629969" zvalue="160"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450356707333" ObjectName="110kV1号主变35kV侧3010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450356707333"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1033.15,530.865) scale(1.17352,0.86058) translate(-151.462,83.9125)" width="15" x="1024.347620135413" y="517.956148629969"/></g>
  <g id="124">
   <use class="kv35" height="30" transform="rotate(0,982.951,821.307) scale(0.699651,0.582507) translate(419.713,582.383)" width="15" x="977.7039090007128" xlink:href="#Disconnector:刀闸_0" y="812.5694652171675" zvalue="180"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450339667973" ObjectName="35kVSVG3728隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450339667973"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,982.951,821.307) scale(0.699651,0.582507) translate(419.713,582.383)" width="15" x="977.7039090007128" y="812.5694652171675"/></g>
  <g id="178">
   <use class="kv35" height="36" transform="rotate(0,829.249,734.723) scale(1.24046,1.24046) translate(-159.065,-138.096)" width="14" x="820.5657575521026" xlink:href="#Disconnector:手车刀闸_0" y="712.3948958239403" zvalue="200"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450339995653" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450339995653"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,829.249,734.723) scale(1.24046,1.24046) translate(-159.065,-138.096)" width="14" x="820.5657575521026" y="712.3948958239403"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="67">
   <path class="kv110" d="M 981.95 297.67 L 981.95 349.83" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@1" LinkObjectIDznd="65@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 981.95 297.67 L 981.95 349.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv110" d="M 979.88 139.95 L 979.88 202.66" stroke-width="1" zvalue="86"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="76@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 979.88 139.95 L 979.88 202.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv110" d="M 979.85 226.81 L 979.85 272.77" stroke-width="1" zvalue="87"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 979.85 226.81 L 979.85 272.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv110" d="M 1002.54 248.52 L 979.85 248.52" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="79" MaxPinNum="2"/>
   </metadata>
  <path d="M 1002.54 248.52 L 979.85 248.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv35" d="M 981.91 516.82 L 981.91 644.15" stroke-width="1" zvalue="150"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@1" LinkObjectIDznd="151@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 981.91 516.82 L 981.91 644.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="kv35" d="M 981.51 672.33 L 981.51 699.78" stroke-width="1" zvalue="151"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@1" LinkObjectIDznd="43@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 981.51 672.33 L 981.51 699.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv35" d="M 967.86 629.57 L 981.91 629.57" stroke-width="1" zvalue="153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="153" MaxPinNum="2"/>
   </metadata>
  <path d="M 967.86 629.57 L 981.91 629.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv110" d="M 981.96 465.66 L 1101.16 465.66 L 1101.16 484.46" stroke-width="1" zvalue="156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@2" LinkObjectIDznd="121@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 981.96 465.66 L 1101.16 465.66 L 1101.16 484.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv35" d="M 981.91 498.18 L 1033.25 498.18 L 1033.25 518.38" stroke-width="1" zvalue="161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@3" LinkObjectIDznd="165@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 981.91 498.18 L 1033.25 498.18 L 1033.25 518.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv35" d="M 1033.15 556.65 L 1033.22 543.55" stroke-width="1" zvalue="162"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="165@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1033.15 556.65 L 1033.22 543.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv35" d="M 981.51 718.09 L 981.51 699.78" stroke-width="1" zvalue="175"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@0" LinkObjectIDznd="43@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 981.51 718.09 L 981.51 699.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv35" d="M 958.9 777.43 L 958.9 766.77 L 983.01 766.77" stroke-width="1" zvalue="178"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="127" MaxPinNum="2"/>
   </metadata>
  <path d="M 958.9 777.43 L 958.9 766.77 L 983.01 766.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv35" d="M 1010.38 780.4 L 1010.38 766.03 L 983.01 766.03" stroke-width="1" zvalue="179"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="127" MaxPinNum="2"/>
   </metadata>
  <path d="M 1010.38 780.4 L 1010.38 766.03 L 983.01 766.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv35" d="M 983.01 812.86 L 983.01 756.83" stroke-width="1" zvalue="182"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@0" LinkObjectIDznd="64@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 983.01 812.86 L 983.01 756.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv35" d="M 965.93 839.96 L 982.79 839.96" stroke-width="1" zvalue="189"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162@0" LinkObjectIDznd="293" MaxPinNum="2"/>
   </metadata>
  <path d="M 965.93 839.96 L 982.79 839.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="181">
   <path class="kv35" d="M 829.25 846.99 L 829.25 755.81" stroke-width="1" zvalue="202"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="178@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 829.25 846.99 L 829.25 755.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv35" d="M 829.25 713.64 L 829.25 699.78" stroke-width="1" zvalue="203"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="43@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 829.25 713.64 L 829.25 699.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv35" d="M 804.06 826.67 L 804.06 800.52 L 829.25 800.52" stroke-width="1" zvalue="204"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@0" LinkObjectIDznd="181" MaxPinNum="2"/>
   </metadata>
  <path d="M 804.06 826.67 L 804.06 800.52 L 829.25 800.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv35" d="M 705.29 849.58 L 705.29 756.83" stroke-width="1" zvalue="209"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="298@0" LinkObjectIDznd="187@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.29 849.58 L 705.29 756.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv35" d="M 677.64 785.15 L 677.64 765.01 L 705.29 765.01" stroke-width="1" zvalue="214"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="189" MaxPinNum="2"/>
   </metadata>
  <path d="M 677.64 785.15 L 677.64 765.01 L 705.29 765.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv35" d="M 705.29 764.04 L 732.98 764.04 L 732.98 788.12" stroke-width="1" zvalue="215"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189" LinkObjectIDznd="192@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.29 764.04 L 732.98 764.04 L 732.98 788.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="kv35" d="M 705.29 718.09 L 705.29 699.78" stroke-width="1" zvalue="219"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@0" LinkObjectIDznd="43@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.29 718.09 L 705.29 699.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv35" d="M 1068.45 782.15 L 1068.45 770.74 L 1095.27 770.7" stroke-width="1" zvalue="236"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="200@0" LinkObjectIDznd="219" MaxPinNum="2"/>
   </metadata>
  <path d="M 1068.45 782.15 L 1068.45 770.74 L 1095.27 770.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="218">
   <path class="kv35" d="M 1123.8 785.12 L 1123.8 771.27 L 1095.27 771.23" stroke-width="1" zvalue="237"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201@0" LinkObjectIDznd="219" MaxPinNum="2"/>
   </metadata>
  <path d="M 1123.8 785.12 L 1123.8 771.27 L 1095.27 771.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="219">
   <path class="kv35" d="M 1095.27 852.88 L 1095.27 753.83" stroke-width="1" zvalue="238"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@0" LinkObjectIDznd="199@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1095.27 852.88 L 1095.27 753.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="kv35" d="M 1095.06 815.67 L 1095.06 815.36" stroke-width="1" zvalue="240"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@0" LinkObjectIDznd="219" MaxPinNum="2"/>
   </metadata>
  <path d="M 1095.06 815.67 L 1095.06 815.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="229">
   <path class="kv35" d="M 1185.19 782.15 L 1185.19 770.74 L 1212.01 770.7" stroke-width="1" zvalue="252"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="234@0" LinkObjectIDznd="227" MaxPinNum="2"/>
   </metadata>
  <path d="M 1185.19 782.15 L 1185.19 770.74 L 1212.01 770.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="kv35" d="M 1240.54 785.12 L 1240.54 771.27 L 1212.01 771.23" stroke-width="1" zvalue="253"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@0" LinkObjectIDznd="227" MaxPinNum="2"/>
   </metadata>
  <path d="M 1240.54 785.12 L 1240.54 771.27 L 1212.01 771.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="227">
   <path class="kv35" d="M 1212.01 852.75 L 1212.01 753.83" stroke-width="1" zvalue="254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@0" LinkObjectIDznd="236@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1212.01 852.75 L 1212.01 753.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv35" d="M 1211.53 715.09 L 1211.53 699.78" stroke-width="1" zvalue="255"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="236@0" LinkObjectIDznd="43@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1211.53 715.09 L 1211.53 699.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv35" d="M 1211.8 815.67 L 1211.8 815.27" stroke-width="1" zvalue="256"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="227" MaxPinNum="2"/>
   </metadata>
  <path d="M 1211.8 815.67 L 1211.8 815.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="244">
   <path class="kv35" d="M 1298.08 782.15 L 1298.08 770.74 L 1324.89 770.7" stroke-width="1" zvalue="266"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="247@0" LinkObjectIDznd="242" MaxPinNum="2"/>
   </metadata>
  <path d="M 1298.08 782.15 L 1298.08 770.74 L 1324.89 770.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="243">
   <path class="kv35" d="M 1353.42 785.12 L 1353.42 770.74 L 1298.22 770.74" stroke-width="1" zvalue="267"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@0" LinkObjectIDznd="244" MaxPinNum="2"/>
   </metadata>
  <path d="M 1353.42 785.12 L 1353.42 770.74 L 1298.22 770.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="kv35" d="M 1324.89 852.75 L 1324.89 753.83" stroke-width="1" zvalue="268"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@0" LinkObjectIDznd="249@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1324.89 852.75 L 1324.89 753.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv35" d="M 1324.95 816.92 L 1324.89 781.73" stroke-width="1" zvalue="270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="287@0" LinkObjectIDznd="242" MaxPinNum="2"/>
   </metadata>
  <path d="M 1324.95 816.92 L 1324.89 781.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="258">
   <path class="kv35" d="M 1546.42 781.38 L 1546.42 769.96 L 1573.23 769.92" stroke-width="1" zvalue="282"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@0" LinkObjectIDznd="256" MaxPinNum="2"/>
   </metadata>
  <path d="M 1546.42 781.38 L 1546.42 769.96 L 1573.23 769.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="257">
   <path class="kv35" d="M 1601.76 784.34 L 1601.76 769.96 L 1546.56 769.96" stroke-width="1" zvalue="283"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="262@0" LinkObjectIDznd="258" MaxPinNum="2"/>
   </metadata>
  <path d="M 1601.76 784.34 L 1601.76 769.96 L 1546.56 769.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="256">
   <path class="kv35" d="M 1573.23 851.97 L 1573.23 753.83" stroke-width="1" zvalue="284"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="260@0" LinkObjectIDznd="263@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1573.23 851.97 L 1573.23 753.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="254">
   <path class="kv35" d="M 1573.02 814.9 L 1573.02 814.52" stroke-width="1" zvalue="286"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="259@0" LinkObjectIDznd="256" MaxPinNum="2"/>
   </metadata>
  <path d="M 1573.02 814.9 L 1573.02 814.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="271">
   <path class="kv35" d="M 1423.5 782.15 L 1423.5 770.74 L 1450.32 770.7" stroke-width="1" zvalue="296"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="269" MaxPinNum="2"/>
   </metadata>
  <path d="M 1423.5 782.15 L 1423.5 770.74 L 1450.32 770.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="kv35" d="M 1478.85 785.12 L 1478.85 771.27 L 1450.32 771.23" stroke-width="1" zvalue="297"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="275@0" LinkObjectIDznd="269" MaxPinNum="2"/>
   </metadata>
  <path d="M 1478.85 785.12 L 1478.85 771.27 L 1450.32 771.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv35" d="M 1450.32 852.75 L 1450.32 753.83" stroke-width="1" zvalue="298"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="273@0" LinkObjectIDznd="276@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1450.32 852.75 L 1450.32 753.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="267">
   <path class="kv35" d="M 1450.11 815.67 L 1450.11 815.3" stroke-width="1" zvalue="300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="269" MaxPinNum="2"/>
   </metadata>
  <path d="M 1450.11 815.67 L 1450.11 815.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="280">
   <path class="kv35" d="M 1573.23 715.09 L 1573.23 699.78" stroke-width="1" zvalue="306"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@0" LinkObjectIDznd="43@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1573.23 715.09 L 1573.23 699.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="285">
   <path class="kv35" d="M 1094.79 715.09 L 1094.79 699.78" stroke-width="1" zvalue="313"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@0" LinkObjectIDznd="43@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1094.79 715.09 L 1094.79 699.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv35" d="M 1324.41 715.09 L 1324.41 699.78" stroke-width="1" zvalue="316"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@0" LinkObjectIDznd="43@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1324.41 715.09 L 1324.41 699.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv35" d="M 1449.83 715.09 L 1449.83 699.78" stroke-width="1" zvalue="317"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="43@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1449.83 715.09 L 1449.83 699.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="293">
   <path class="kv35" d="M 982.79 854.45 L 982.79 829.9" stroke-width="1" zvalue="522"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="124@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 982.79 854.45 L 982.79 829.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="295">
   <path class="kv35" d="M 983.01 781.57 L 982.59 781.57" stroke-width="1" zvalue="525"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127" LinkObjectIDznd="296@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 983.01 781.57 L 982.59 781.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="299">
   <path class="kv35" d="M 705.29 818.57 L 704.59 818.57" stroke-width="1" zvalue="532"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189" LinkObjectIDznd="300@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 705.29 818.57 L 704.59 818.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="307">
   <path class="kv110" d="M 1001.37 320.78 L 981.95 320.78" stroke-width="1" zvalue="542"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="308@0" LinkObjectIDznd="67" MaxPinNum="2"/>
   </metadata>
  <path d="M 1001.37 320.78 L 981.95 320.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv110" d="M 999.3 173.78 L 979.88 173.78" stroke-width="1" zvalue="547"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="312@0" LinkObjectIDznd="78" MaxPinNum="2"/>
   </metadata>
  <path d="M 999.3 173.78 L 979.88 173.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv110" d="M 981.93 369.84 L 981.93 426" stroke-width="1" zvalue="585"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@1" LinkObjectIDznd="97@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 981.93 369.84 L 981.93 426" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv110" d="M 981.93 450 L 981.93 426" stroke-width="1" zvalue="586"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="97@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 981.93 450 L 981.93 426" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv110" d="M 1000.3 399.78 L 981.93 399.78" stroke-width="1" zvalue="587"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="304@0" LinkObjectIDznd="100" MaxPinNum="2"/>
   </metadata>
  <path d="M 1000.3 399.78 L 981.93 399.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="74">
   <use class="kv110" height="20" transform="rotate(270,1011.94,248.565) scale(0.964803,0.964803) translate(36.7406,8.7159)" width="10" x="1007.120272721775" xlink:href="#GroundDisconnector:地刀_0" y="238.9173066457038" zvalue="83"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450338947077" ObjectName="110kV东坡光伏电站线17160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450338947077"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1011.94,248.565) scale(0.964803,0.964803) translate(36.7406,8.7159)" width="10" x="1007.120272721775" y="238.9173066457038"/></g>
  <g id="121">
   <use class="kv110" height="40" transform="rotate(0,1098.11,498.776) scale(1.17352,-1.17352) translate(-158.898,-920.332)" width="40" x="1074.640402670328" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="475.305871796878" zvalue="124"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450339143685" ObjectName="110kV1号主变1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450339143685"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1098.11,498.776) scale(1.17352,-1.17352) translate(-158.898,-920.332)" width="40" x="1074.640402670328" y="475.305871796878"/></g>
  <g id="60">
   <use class="kv35" height="20" transform="rotate(0,958.821,792.344) scale(1.52939,1.52939) translate(-329.244,-268.972)" width="10" x="951.1741972546412" xlink:href="#GroundDisconnector:地刀_0" y="777.0504152304929" zvalue="177"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450339602437" ObjectName="35kVSVG37267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450339602437"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,958.821,792.344) scale(1.52939,1.52939) translate(-329.244,-268.972)" width="10" x="951.1741972546412" y="777.0504152304929"/></g>
  <g id="162">
   <use class="kv35" height="20" transform="rotate(90,952.453,839.891) scale(1.38209,1.38209) translate(-261.4,-228.372)" width="10" x="945.5421822664836" xlink:href="#GroundDisconnector:地刀_0" y="826.0706074987597" zvalue="188"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450339799045" ObjectName="35kVSVG37287接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450339799045"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,952.453,839.891) scale(1.38209,1.38209) translate(-261.4,-228.372)" width="10" x="945.5421822664836" y="826.0706074987597"/></g>
  <g id="191">
   <use class="kv35" height="20" transform="rotate(0,677.562,800.063) scale(1.52939,1.52939) translate(-231.888,-271.644)" width="10" x="669.9153806615467" xlink:href="#GroundDisconnector:地刀_0" y="784.7688405797098" zvalue="213"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450340126725" ObjectName="35kV#1-1储能装置37167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450340126725"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,677.562,800.063) scale(1.52939,1.52939) translate(-231.888,-271.644)" width="10" x="669.9153806615467" y="784.7688405797098"/></g>
  <g id="200">
   <use class="kv35" height="20" transform="rotate(0,1068.38,797.063) scale(1.52939,1.52939) translate(-367.166,-270.606)" width="10" x="1060.729007679749" xlink:href="#GroundDisconnector:地刀_0" y="781.7688405797099" zvalue="223"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450340323333" ObjectName="35kV#1站用变37367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450340323333"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1068.38,797.063) scale(1.52939,1.52939) translate(-367.166,-270.606)" width="10" x="1060.729007679749" y="781.7688405797099"/></g>
  <g id="234">
   <use class="kv35" height="20" transform="rotate(0,1185.12,797.063) scale(1.52939,1.52939) translate(-407.576,-270.606)" width="10" x="1177.470191086654" xlink:href="#GroundDisconnector:地刀_0" y="781.7688405797099" zvalue="245"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450340782085" ObjectName="35kV#1集电线路37467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450340782085"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1185.12,797.063) scale(1.52939,1.52939) translate(-407.576,-270.606)" width="10" x="1177.470191086654" y="781.7688405797099"/></g>
  <g id="247">
   <use class="kv35" height="20" transform="rotate(0,1298,797.063) scale(1.52939,1.52939) translate(-446.649,-270.606)" width="10" x="1290.352161818951" xlink:href="#GroundDisconnector:地刀_0" y="781.7688405797099" zvalue="261"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450341044229" ObjectName="35kV#2集电线路37567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450341044229"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1298,797.063) scale(1.52939,1.52939) translate(-446.649,-270.606)" width="10" x="1290.352161818951" y="781.7688405797099"/></g>
  <g id="261">
   <use class="kv35" height="20" transform="rotate(0,1546.34,796.291) scale(1.52939,1.52939) translate(-532.611,-270.338)" width="10" x="1538.692497430005" xlink:href="#GroundDisconnector:地刀_0" y="780.9969980447885" zvalue="277"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450341699589" ObjectName="35kV芒东集电线路37767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450341699589"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1546.34,796.291) scale(1.52939,1.52939) translate(-532.611,-270.338)" width="10" x="1538.692497430005" y="780.9969980447885"/></g>
  <g id="274">
   <use class="kv35" height="20" transform="rotate(0,1423.42,797.063) scale(1.52939,1.52939) translate(-490.064,-270.606)" width="10" x="1415.776573743726" xlink:href="#GroundDisconnector:地刀_0" y="781.7688405797099" zvalue="291"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450341371909" ObjectName="35kV#3集电线路37667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450341371909"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1423.42,797.063) scale(1.52939,1.52939) translate(-490.064,-270.606)" width="10" x="1415.776573743726" y="781.7688405797099"/></g>
  <g id="304">
   <use class="kv110" height="20" transform="rotate(270,1009.7,399.824) scale(0.964803,0.964803) translate(36.6589,14.2339)" width="10" x="1004.880425184952" xlink:href="#GroundDisconnector:地刀_0" y="390.1759841567394" zvalue="534"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450366930949" ObjectName="110kV东坡光伏电站线17137接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450366930949"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1009.7,399.824) scale(0.964803,0.964803) translate(36.6589,14.2339)" width="10" x="1004.880425184952" y="390.1759841567394"/></g>
  <g id="308">
   <use class="kv110" height="20" transform="rotate(270,1010.77,320.824) scale(0.964803,0.964803) translate(36.6979,11.352)" width="10" x="1005.950945947268" xlink:href="#GroundDisconnector:地刀_0" y="311.1759841567394" zvalue="540"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450342289413" ObjectName="110kV东坡光伏电站线17130接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450342289413"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1010.77,320.824) scale(0.964803,0.964803) translate(36.6979,11.352)" width="10" x="1005.950945947268" y="311.1759841567394"/></g>
  <g id="312">
   <use class="kv110" height="20" transform="rotate(270,1008.7,173.824) scale(0.964803,0.964803) translate(36.6224,5.98928)" width="10" x="1003.880425184952" xlink:href="#GroundDisconnector:地刀_0" y="164.1759841567395" zvalue="545"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450342420485" ObjectName="110kV东坡光伏电站线17167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450342420485"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1008.7,173.824) scale(0.964803,0.964803) translate(36.6224,5.98928)" width="10" x="1003.880425184952" y="164.1759841567395"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="155">
   <use class="kv35" height="26" transform="rotate(90,953.35,629.53) scale(1.17352,1.17352) translate(-139.923,-90.8276)" width="12" x="946.3086558163426" xlink:href="#Accessory:避雷器_0" y="614.2747306942387" zvalue="152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450339209221" ObjectName="110kV1号主变35kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,953.35,629.53) scale(1.17352,1.17352) translate(-139.923,-90.8276)" width="12" x="946.3086558163426" y="614.2747306942387"/></g>
  <g id="161">
   <use class="kv35" height="26" transform="rotate(0,1033.15,571.026) scale(1.17352,1.17352) translate(-151.722,-82.1771)" width="12" x="1026.107897526069" xlink:href="#Accessory:接地变接地设备_0" y="555.7702317857138" zvalue="157"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450339274757" ObjectName="110kV1号主变接地设备"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1033.15,571.026) scale(1.17352,1.17352) translate(-151.722,-82.1771)" width="12" x="1026.107897526069" y="555.7702317857138"/></g>
  <g id="51">
   <use class="kv35" height="20" transform="rotate(0,1010.38,795.345) scale(1.50795,1.7082) translate(-335.267,-322.659)" width="20" x="995.3050088578028" xlink:href="#Accessory:线路PT3_0" y="778.262525982198" zvalue="176"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450339471365" ObjectName="35kVSVGPT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1010.38,795.345) scale(1.50795,1.7082) translate(-335.267,-322.659)" width="20" x="995.3050088578028" y="778.262525982198"/></g>
  <g id="174">
   <use class="kv35" height="35" transform="rotate(0,829.143,878.992) scale(1.6953,-1.8688) translate(-327.891,-1334.14)" width="35" x="799.4754028844409" xlink:href="#Accessory:四绕组PT带熔断器_0" y="846.2876877696718" zvalue="195"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450339864581" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,829.143,878.992) scale(1.6953,-1.8688) translate(-327.891,-1334.14)" width="35" x="799.4754028844409" y="846.2876877696718"/></g>
  <g id="176">
   <use class="kv35" height="20" transform="rotate(0,804.058,846.089) scale(-1.4472,2.21905) translate(-1355.18,-452.614)" width="20" x="789.5861704057568" xlink:href="#Accessory:线路PT3_0" y="823.8985789441858" zvalue="197"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450339930117" ObjectName="35kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,804.058,846.089) scale(-1.4472,2.21905) translate(-1355.18,-452.614)" width="20" x="789.5861704057568" y="823.8985789441858"/></g>
  <g id="192">
   <use class="kv35" height="20" transform="rotate(0,732.985,803.063) scale(1.50795,1.7082) translate(-241.826,-325.859)" width="20" x="717.9054049393167" xlink:href="#Accessory:线路PT3_0" y="785.980951331415" zvalue="212"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450340192261" ObjectName="35kV#1-1储能装置PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,732.985,803.063) scale(1.50795,1.7082) translate(-241.826,-325.859)" width="20" x="717.9054049393167" y="785.980951331415"/></g>
  <g id="201">
   <use class="kv35" height="20" transform="rotate(0,1123.8,800.063) scale(1.50795,1.7082) translate(-373.471,-324.615)" width="20" x="1108.719031957518" xlink:href="#Accessory:线路PT3_0" y="782.980951331415" zvalue="222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450340388869" ObjectName="35kV#1站用变PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1123.8,800.063) scale(1.50795,1.7082) translate(-373.471,-324.615)" width="20" x="1108.719031957518" y="782.980951331415"/></g>
  <g id="212">
   <use class="kv35" height="15" transform="rotate(0,1095.06,823.687) scale(1.14529,1.14529) translate(-137.825,-103.4)" width="15" x="1086.46966605302" xlink:href="#Accessory:放电间隙4_0" y="815.0978330478795" zvalue="233"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450340519941" ObjectName="35kV#1站用变电缆"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1095.06,823.687) scale(1.14529,1.14529) translate(-137.825,-103.4)" width="15" x="1086.46966605302" y="815.0978330478795"/></g>
  <g id="235">
   <use class="kv35" height="20" transform="rotate(0,1240.54,800.063) scale(1.50795,1.7082) translate(-412.795,-324.615)" width="20" x="1225.460215364424" xlink:href="#Accessory:线路PT3_0" y="782.980951331415" zvalue="244"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450340847621" ObjectName="35kV#1集电线路PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1240.54,800.063) scale(1.50795,1.7082) translate(-412.795,-324.615)" width="20" x="1225.460215364424" y="782.980951331415"/></g>
  <g id="230">
   <use class="kv35" height="15" transform="rotate(0,1211.8,823.687) scale(1.14529,1.14529) translate(-152.635,-103.4)" width="15" x="1203.210849459926" xlink:href="#Accessory:放电间隙4_0" y="815.0978330478795" zvalue="251"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450340585477" ObjectName="35kV#1集电线路电缆"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1211.8,823.687) scale(1.14529,1.14529) translate(-152.635,-103.4)" width="15" x="1203.210849459926" y="815.0978330478795"/></g>
  <g id="248">
   <use class="kv35" height="20" transform="rotate(0,1353.42,800.063) scale(1.50795,1.7082) translate(-450.819,-324.615)" width="20" x="1338.342186096721" xlink:href="#Accessory:线路PT3_0" y="782.980951331415" zvalue="260"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450341109765" ObjectName="35kV#2集电线路PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1353.42,800.063) scale(1.50795,1.7082) translate(-450.819,-324.615)" width="20" x="1338.342186096721" y="782.980951331415"/></g>
  <g id="262">
   <use class="kv35" height="20" transform="rotate(0,1601.76,799.291) scale(1.50795,1.7082) translate(-534.472,-324.295)" width="20" x="1586.682521707775" xlink:href="#Accessory:线路PT3_0" y="782.2091087964932" zvalue="276"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450341765125" ObjectName="35kV芒东集电线路PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1601.76,799.291) scale(1.50795,1.7082) translate(-534.472,-324.295)" width="20" x="1586.682521707775" y="782.2091087964932"/></g>
  <g id="259">
   <use class="kv35" height="15" transform="rotate(0,1573.02,822.916) scale(1.14529,1.14529) translate(-198.458,-103.302)" width="15" x="1564.433155803277" xlink:href="#Accessory:放电间隙4_0" y="814.3259905129578" zvalue="281"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450341502981" ObjectName="35kV芒东集电线路电缆"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1573.02,822.916) scale(1.14529,1.14529) translate(-198.458,-103.302)" width="15" x="1564.433155803277" y="814.3259905129578"/></g>
  <g id="275">
   <use class="kv35" height="20" transform="rotate(0,1478.85,800.063) scale(1.50795,1.7082) translate(-493.068,-324.615)" width="20" x="1463.766598021496" xlink:href="#Accessory:线路PT3_0" y="782.980951331415" zvalue="290"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450341437445" ObjectName="35kV#3集电线路PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1478.85,800.063) scale(1.50795,1.7082) translate(-493.068,-324.615)" width="20" x="1463.766598021496" y="782.980951331415"/></g>
  <g id="272">
   <use class="kv35" height="15" transform="rotate(0,1450.11,823.687) scale(1.14529,1.14529) translate(-182.865,-103.4)" width="15" x="1441.517232116998" xlink:href="#Accessory:放电间隙4_0" y="815.0978330478795" zvalue="295"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450341175301" ObjectName="35kV#3集电线路电缆"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1450.11,823.687) scale(1.14529,1.14529) translate(-182.865,-103.4)" width="15" x="1441.517232116998" y="815.0978330478795"/></g>
  <g id="287">
   <use class="kv35" height="15" transform="rotate(0,1324.95,823.669) scale(0.964803,0.964803) translate(48.0712,29.7842)" width="15" x="1317.711223101443" xlink:href="#Accessory:放电间隙4_0" y="816.4328401391399" zvalue="315"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450341830661" ObjectName="35kV#2集电线路电缆"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1324.95,823.669) scale(0.964803,0.964803) translate(48.0712,29.7842)" width="15" x="1317.711223101443" y="816.4328401391399"/></g>
  <g id="296">
   <use class="kv35" height="15" transform="rotate(0,982.59,789.59) scale(1.14529,1.14529) translate(-123.558,-99.0746)" width="15" x="974" xlink:href="#Accessory:放电间隙4_0" y="781" zvalue="524"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450341896197" ObjectName="35kVSVG电缆"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,982.59,789.59) scale(1.14529,1.14529) translate(-123.558,-99.0746)" width="15" x="974" y="781"/></g>
  <g id="300">
   <use class="kv35" height="15" transform="rotate(0,704.59,826.59) scale(1.14529,1.14529) translate(-88.2919,-103.768)" width="15" x="696" xlink:href="#Accessory:放电间隙4_0" y="818" zvalue="531"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450342027269" ObjectName="35kV储能进线柜电缆"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,704.59,826.59) scale(1.14529,1.14529) translate(-88.2919,-103.768)" width="15" x="696" y="818"/></g>
  <g id="53">
   <use class="kv10" height="29" transform="rotate(0,1492.94,531.806) scale(2.95185,3.159) translate(-957.901,-332.154)" width="30" x="1448.666666666667" xlink:href="#Accessory:厂用变2020_0" y="486" zvalue="550"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450356772869" ObjectName="10kV#2站用变"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1492.94,531.806) scale(2.95185,3.159) translate(-957.901,-332.154)" width="30" x="1448.666666666667" y="486"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="158">
   <g id="1580">
    <use class="kv110" height="30" transform="rotate(0,956.39,483.842) scale(2.41152,2.38948) translate(-529.45,-260.511)" width="43" x="904.54" xlink:href="#PowerTransformer2:110kV东坡光伏主变_0" y="448" zvalue="155"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874466721795" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1581">
    <use class="kv35" height="30" transform="rotate(0,956.39,483.842) scale(2.41152,2.38948) translate(-529.45,-260.511)" width="43" x="904.54" xlink:href="#PowerTransformer2:110kV东坡光伏主变_1" y="448" zvalue="155"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874466787331" ObjectName="35"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399464910851" ObjectName="110kV1号主变"/>
   <cge:TPSR_Ref TObjectID="6755399464910851"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,956.39,483.842) scale(2.41152,2.38948) translate(-529.45,-260.511)" width="43" x="904.54" y="448"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="47">
   <use class="kv35" height="38" transform="rotate(0,982.79,895.584) scale(2.89441,2.28506) translate(-625.245,-479.238)" width="19" x="955.2930610840465" xlink:href="#EnergyConsumer:负荷SVG_0" y="852.16771482131" zvalue="163"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450339405829" ObjectName="35kVSVG"/>
   <cge:TPSR_Ref TObjectID="6192450339405829"/></metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,982.79,895.584) scale(2.89441,2.28506) translate(-625.245,-479.238)" width="19" x="955.2930610840465" y="852.16771482131"/></g>
  <g id="203">
   <use class="kv35" height="25" transform="rotate(0,1095.27,858.439) scale(0.15008,0.463106) translate(6194.96,988.507)" width="18" x="1093.918236681381" xlink:href="#EnergyConsumer:站用变20231222_0" y="852.6497133700188" zvalue="225"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450340454405" ObjectName="35kV#1站用变"/>
   </metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1095.27,858.439) scale(0.15008,0.463106) translate(6194.96,988.507)" width="18" x="1093.918236681381" y="852.6497133700188"/></g>
  <g id="298">
   <use class="kv35" height="30" transform="rotate(0,705.292,854.789) scale(0.225121,-0.385921) translate(2423.01,-3078.93)" width="12" x="703.9414214394828" xlink:href="#EnergyConsumer:负荷_0" y="849" zvalue="529"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450341961733" ObjectName="35kV#1-1储能装置"/>
   <cge:TPSR_Ref TObjectID="6192450341961733"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,705.292,854.789) scale(0.225121,-0.385921) translate(2423.01,-3078.93)" width="12" x="703.9414214394828" y="849"/></g>
 </g>
</svg>