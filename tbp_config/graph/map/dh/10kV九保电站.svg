<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549592915970" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:D-Y_0" viewBox="0,0,30,50">
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01666666666667" x2="15.01666666666667" y1="37.50000000000001" y2="32.91666666666667"/>
   <use terminal-index="1" type="1" x="15.00325153685922" xlink:href="#terminal" y="49.86055225321343"/>
   <use terminal-index="2" type="2" x="15" xlink:href="#terminal" y="37.75085860895189"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.25" x2="15" y1="43.08333333333334" y2="37.75000000000001"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15" x2="20" y1="37.74761215261902" y2="42.91666666666667"/>
   <ellipse cx="15" cy="35.25" fill-opacity="0" rx="14.75" ry="14.75" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:D-Y_1" viewBox="0,0,30,50">
   <path d="M 10 16 L 20.1667 16 L 15 7.83333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.75" ry="14.75" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="0" type="1" x="15.00731595793324" xlink:href="#terminal" y="0.3902606310013752"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV九保电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="43.5" xlink:href="logo.png" y="35"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,178.125,65) scale(1,1) translate(0,0)" writing-mode="lr" x="178.13" xml:space="preserve" y="68.5" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,179.833,64.6903) scale(1,1) translate(6.82787e-15,0)" writing-mode="lr" x="179.83" xml:space="preserve" y="73.69" zvalue="3">10kV九保电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="25" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,84,306) scale(1,1) translate(0,0)" width="97" x="35.5" y="294" zvalue="9"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84,306) scale(1,1) translate(0,0)" writing-mode="lr" x="84" xml:space="preserve" y="310.5" zvalue="9">全站公用</text>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.5" x2="377.5" y1="3" y2="1033" zvalue="4"/>
  <line fill="none" id="28" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.500000000000227" x2="370.4999999999998" y1="138.8704926140824" y2="138.8704926140824" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="151.0000000000001" y2="151.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="177.0000000000001" y2="177.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="4.5" y1="151.0000000000001" y2="177.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="151.0000000000001" y2="177.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="151.0000000000001" y2="151.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="177.0000000000001" y2="177.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="151.0000000000001" y2="177.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.5" x2="366.5" y1="151.0000000000001" y2="177.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="177.0000000000001" y2="177.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="201.2500000000001" y2="201.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="4.5" y1="177.0000000000001" y2="201.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="177.0000000000001" y2="201.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="177.0000000000001" y2="177.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="201.2500000000001" y2="201.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="177.0000000000001" y2="201.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.5" x2="366.5" y1="177.0000000000001" y2="201.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="201.2500000000001" y2="201.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="224.0000000000001" y2="224.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="4.5" y1="201.2500000000001" y2="224.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="201.2500000000001" y2="224.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="201.2500000000001" y2="201.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="224.0000000000001" y2="224.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="201.2500000000001" y2="224.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.5" x2="366.5" y1="201.2500000000001" y2="224.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="224.0000000000001" y2="224.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="246.7500000000001" y2="246.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="4.5" y1="224.0000000000001" y2="246.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="224.0000000000001" y2="246.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="224.0000000000001" y2="224.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="246.7500000000001" y2="246.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="224.0000000000001" y2="246.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.5" x2="366.5" y1="224.0000000000001" y2="246.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="246.7500000000001" y2="246.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="185.5" y1="269.5000000000001" y2="269.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.5" x2="4.5" y1="246.7500000000001" y2="269.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="246.7500000000001" y2="269.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="246.7500000000001" y2="246.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="366.5" y1="269.5000000000001" y2="269.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.5" x2="185.5" y1="246.7500000000001" y2="269.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.5" x2="366.5" y1="246.7500000000001" y2="269.5000000000001"/>
  <line fill="none" id="26" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.500000000000227" x2="370.4999999999998" y1="608.8704926140824" y2="608.8704926140824" zvalue="8"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="431.6666435058594" y2="431.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="469.1566435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="56.72236173734677" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0670617373469" x2="105.0670617373469" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="431.6666435058594" y2="431.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="469.1566435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="105.0673617373468" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1756617373469" x2="167.1756617373469" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="431.6666435058594" y2="431.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="469.1566435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="167.1751617373468" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4999617373469" x2="230.4999617373469" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="431.6666435058594" y2="431.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="469.1566435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="230.4998617373468" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="431.6666435058594" y2="431.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="469.1566435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.7164617373469" x2="354.7164617373469" y1="431.6666435058594" y2="469.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="469.1567435058594" y2="469.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="493.3253435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="56.72236173734677" y1="469.1567435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0670617373469" x2="105.0670617373469" y1="469.1567435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="469.1567435058594" y2="469.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="493.3253435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="105.0673617373468" y1="469.1567435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1756617373469" x2="167.1756617373469" y1="469.1567435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="469.1567435058594" y2="469.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="493.3253435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="167.1751617373468" y1="469.1567435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4999617373469" x2="230.4999617373469" y1="469.1567435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="469.1567435058594" y2="469.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="493.3253435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="230.4998617373468" y1="469.1567435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="469.1567435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="469.1567435058594" y2="469.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="493.3253435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="469.1567435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.7164617373469" x2="354.7164617373469" y1="469.1567435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="493.3253435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="517.4939435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="56.72236173734677" y1="493.3253435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0670617373469" x2="105.0670617373469" y1="493.3253435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="493.3253435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="517.4939435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="105.0673617373468" y1="493.3253435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1756617373469" x2="167.1756617373469" y1="493.3253435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="493.3253435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="517.4939435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="167.1751617373468" y1="493.3253435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4999617373469" x2="230.4999617373469" y1="493.3253435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="493.3253435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="517.4939435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="230.4998617373468" y1="493.3253435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="493.3253435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="493.3253435058594" y2="493.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="517.4939435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="493.3253435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.7164617373469" x2="354.7164617373469" y1="493.3253435058594" y2="517.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="517.4939835058594" y2="517.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="541.6625835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="56.72236173734677" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0670617373469" x2="105.0670617373469" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="517.4939835058594" y2="517.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="541.6625835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="105.0673617373468" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1756617373469" x2="167.1756617373469" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="517.4939835058594" y2="517.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="541.6625835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="167.1751617373468" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4999617373469" x2="230.4999617373469" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="517.4939835058594" y2="517.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="541.6625835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="230.4998617373468" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="517.4939835058594" y2="517.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="541.6625835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.7164617373469" x2="354.7164617373469" y1="517.4939835058594" y2="541.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="541.6627435058593" y2="541.6627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="56.72236173734677" y1="541.6627435058593" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0670617373469" x2="105.0670617373469" y1="541.6627435058593" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="541.6627435058593" y2="541.6627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="105.0673617373468" y1="541.6627435058593" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1756617373469" x2="167.1756617373469" y1="541.6627435058593" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="541.6627435058593" y2="541.6627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="167.1751617373468" y1="541.6627435058593" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4999617373469" x2="230.4999617373469" y1="541.6627435058593" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="541.6627435058593" y2="541.6627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="230.4998617373468" y1="541.6627435058593" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="541.6627435058593" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="541.6627435058593" y2="541.6627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="541.6627435058593" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.7164617373469" x2="354.7164617373469" y1="541.6627435058593" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="105.0670617373469" y1="589.9999435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="56.72236173734677" x2="56.72236173734677" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0670617373469" x2="105.0670617373469" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="167.1756617373469" y1="589.9999435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="105.0673617373468" x2="105.0673617373468" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1756617373469" x2="167.1756617373469" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="230.4999617373469" y1="589.9999435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="167.1751617373468" x2="167.1751617373468" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4999617373469" x2="230.4999617373469" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="292.6081617373468" y1="589.9999435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.4998617373468" x2="230.4998617373468" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="565.8313435058594" y2="565.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="354.7164617373469" y1="589.9999435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="292.6081617373468" x2="292.6081617373468" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354.7164617373469" x2="354.7164617373469" y1="565.8313435058594" y2="589.9999435058594"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="93.5" y1="924.0000000000002" y2="924.0000000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="93.5" y1="963.1633000000002" y2="963.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="3.5" y1="924.0000000000002" y2="963.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="93.5" y1="924.0000000000002" y2="963.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="363.5" y1="924.0000000000002" y2="924.0000000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="363.5" y1="963.1633000000002" y2="963.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="93.5" y1="924.0000000000002" y2="963.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.5" x2="363.5" y1="924.0000000000002" y2="963.1633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="93.5" y1="963.1632700000002" y2="963.1632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="93.5" y1="991.0816700000003" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="3.5" y1="963.1632700000002" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="93.5" y1="963.1632700000002" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="183.5" y1="963.1632700000002" y2="963.1632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="183.5" y1="991.0816700000003" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="93.5" y1="963.1632700000002" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5" x2="183.5" y1="963.1632700000002" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5000000000001" x2="273.5000000000001" y1="963.1632700000002" y2="963.1632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5000000000001" x2="273.5000000000001" y1="991.0816700000003" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5000000000001" x2="183.5000000000001" y1="963.1632700000002" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5000000000001" x2="273.5000000000001" y1="963.1632700000002" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5" x2="363.5" y1="963.1632700000002" y2="963.1632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5" x2="363.5" y1="991.0816700000003" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5" x2="273.5" y1="963.1632700000002" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.5" x2="363.5" y1="963.1632700000002" y2="991.0816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="93.5" y1="991.0816000000002" y2="991.0816000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="93.5" y1="1019" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.5" x2="3.5" y1="991.0816000000002" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="93.5" y1="991.0816000000002" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="183.5" y1="991.0816000000002" y2="991.0816000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="183.5" y1="1019" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.5" x2="93.5" y1="991.0816000000002" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5" x2="183.5" y1="991.0816000000002" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5000000000001" x2="273.5000000000001" y1="991.0816000000002" y2="991.0816000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5000000000001" x2="273.5000000000001" y1="1019" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.5000000000001" x2="183.5000000000001" y1="991.0816000000002" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5000000000001" x2="273.5000000000001" y1="991.0816000000002" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5" x2="363.5" y1="991.0816000000002" y2="991.0816000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5" x2="363.5" y1="1019" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.5" x2="273.5" y1="991.0816000000002" y2="1019"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.5" x2="363.5" y1="991.0816000000002" y2="1019"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.5,944) scale(1,1) translate(0,0)" writing-mode="lr" x="48.5" xml:space="preserve" y="950" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.5,978) scale(1,1) translate(0,0)" writing-mode="lr" x="45.5" xml:space="preserve" y="984" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.5,978) scale(1,1) translate(0,0)" writing-mode="lr" x="227.5" xml:space="preserve" y="984" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.5,1006) scale(1,1) translate(0,0)" writing-mode="lr" x="44.5" xml:space="preserve" y="1012" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.5,1006) scale(1,1) translate(0,0)" writing-mode="lr" x="226.5" xml:space="preserve" y="1012" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69,638.5) scale(1,1) translate(0,2.06335e-13)" writing-mode="lr" x="69" xml:space="preserve" y="643.0000000000001" zvalue="18">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.899,305.841) scale(1,1) translate(0,0)" writing-mode="lr" x="201.9" xml:space="preserve" y="310.34" zvalue="19">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,306.899,305.841) scale(1,1) translate(0,0)" writing-mode="lr" x="306.9" xml:space="preserve" y="310.34" zvalue="20">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.5,481.5) scale(1,1) translate(0,0)" writing-mode="lr" x="80.5" xml:space="preserve" y="486" zvalue="21">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.5,507) scale(1,1) translate(0,0)" writing-mode="lr" x="80.5" xml:space="preserve" y="511.5" zvalue="22">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.5,532.5) scale(1,1) translate(0,0)" writing-mode="lr" x="80.5" xml:space="preserve" y="537" zvalue="23">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.5,557) scale(1,1) translate(0,-1.20459e-13)" writing-mode="lr" x="79.5" xml:space="preserve" y="561.5000000000001" zvalue="24">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.5,583.5) scale(1,1) translate(0,0)" writing-mode="lr" x="80.5" xml:space="preserve" y="588" zvalue="25">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.554,946) scale(1,1) translate(0,0)" writing-mode="lr" x="228.55" xml:space="preserve" y="952" zvalue="26">MGS4-01-2008</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,138.554,978) scale(1,1) translate(0,0)" writing-mode="lr" x="138.55" xml:space="preserve" y="984" zvalue="27">何成飞</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,318.554,978) scale(1,1) translate(0,0)" writing-mode="lr" x="318.55" xml:space="preserve" y="984" zvalue="28">20210510</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,42.5,165) scale(1,1) translate(0,0)" writing-mode="lr" x="42.5" xml:space="preserve" y="170.5" zvalue="29">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.5,165) scale(1,1) translate(0,0)" writing-mode="lr" x="222.5" xml:space="preserve" y="170.5" zvalue="30">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.6875,237) scale(1,1) translate(0,0)" writing-mode="lr" x="49.69" xml:space="preserve" y="241.5" zvalue="31">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.583,191.694) scale(1,1) translate(0,0)" writing-mode="lr" x="234.58" xml:space="preserve" y="196.19" zvalue="32">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" x="255.875" xml:space="preserve" y="449.75" zvalue="33">0.4kV  母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="255.875" xml:space="preserve" y="465.75" zvalue="33">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1104.54,951.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1104.54" xml:space="preserve" y="956" zvalue="35">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1160.38,495.688) scale(1,1) translate(0,0)" writing-mode="lr" x="1160.38" xml:space="preserve" y="500.19" zvalue="36">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1105,78.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1105" xml:space="preserve" y="83" zvalue="38">10kV沙九线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1073.62,315.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1073.63" xml:space="preserve" y="320.25" zvalue="40">0516</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1074.21,655) scale(1,1) translate(0,0)" writing-mode="lr" x="1074.21" xml:space="preserve" y="659.5" zvalue="44">4016</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1127.62,764.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1127.63" xml:space="preserve" y="769" zvalue="47">401</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1162,519) scale(1,1) translate(0,0)" writing-mode="lr" x="1162" xml:space="preserve" y="523.5" zvalue="49">320kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1106,978.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1106" xml:space="preserve" y="983" zvalue="50">212kW</text>
 </g>
 <g id="ButtonClass">
  <g href="500kV德宏变_全站公用.svg"><rect fill-opacity="0" height="24" width="97" x="35.5" y="294" zvalue="9"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="GeneratorClass">
  <g id="33">
   <use class="v400" height="30" transform="rotate(0,1103.79,900.312) scale(2.39583,2.39583) translate(-622.138,-503.592)" width="30" x="1067.848526204546" xlink:href="#Generator:发电机_0" y="864.375" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450022277125" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192450022277125"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1103.79,900.312) scale(2.39583,2.39583) translate(-622.138,-503.592)" width="30" x="1067.848526204546" y="864.375"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="34">
   <g id="340">
    <use class="v400" height="50" transform="rotate(0,1103.81,502.688) scale(1.7625,1.7625) translate(-466.099,-198.412)" width="30" x="1077.38" xlink:href="#PowerTransformer2:D-Y_0" y="458.63" zvalue="35"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874455646211" ObjectName="0.4"/>
    </metadata>
   </g>
   <g id="341">
    <use class="kv10" height="50" transform="rotate(0,1103.81,502.688) scale(1.7625,1.7625) translate(-466.099,-198.412)" width="30" x="1077.38" xlink:href="#PowerTransformer2:D-Y_1" y="458.63" zvalue="35"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874455580675" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399459667971" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399459667971"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1103.81,502.688) scale(1.7625,1.7625) translate(-466.099,-198.412)" width="30" x="1077.38" y="458.63"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="39">
   <use class="kv10" height="30" transform="rotate(0,1103.75,316.75) scale(1.25,0.916667) translate(-218.875,27.5455)" width="15" x="1094.375" xlink:href="#Disconnector:刀闸_0" y="303" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450022408197" ObjectName="10kV沙九线0516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450022408197"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1103.75,316.75) scale(1.25,0.916667) translate(-218.875,27.5455)" width="15" x="1094.375" y="303"/></g>
  <g id="42">
   <use class="v400" height="30" transform="rotate(0,1103.71,656) scale(1.25,0.916667) translate(-218.867,58.3864)" width="15" x="1094.334570674413" xlink:href="#Disconnector:刀闸_0" y="642.25" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450022473733" ObjectName="#1发电机4016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450022473733"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1103.71,656) scale(1.25,0.916667) translate(-218.867,58.3864)" width="15" x="1094.334570674413" y="642.25"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="40">
   <path class="kv10" d="M 1105 147.89 L 1105 303.45" stroke-width="1" zvalue="40"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="39@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1105 147.89 L 1105 303.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv10" d="M 1103.83 330.26 L 1103.83 459.31" stroke-width="1" zvalue="41"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@1" LinkObjectIDznd="34@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1103.83 330.26 L 1103.83 459.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="v400" d="M 1103.82 642.7 L 1103.82 546.5" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@0" LinkObjectIDznd="34@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1103.82 642.7 L 1103.82 546.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="v400" d="M 1103.79 864.97 L 1103.88 781.62" stroke-width="1" zvalue="47"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="45@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1103.79 864.97 L 1103.88 781.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="v400" d="M 1103.69 749.36 L 1103.79 669.51" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="42@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1103.69 749.36 L 1103.79 669.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="45">
   <use class="v400" height="20" transform="rotate(0,1103.75,765.5) scale(1.875,1.6875) translate(-510.708,-304.995)" width="10" x="1094.375" xlink:href="#Breaker:开关_0" y="748.625" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924558192645" ObjectName="#1发电机401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924558192645"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1103.75,765.5) scale(1.875,1.6875) translate(-510.708,-304.995)" width="10" x="1094.375" y="748.625"/></g>
 </g>
</svg>