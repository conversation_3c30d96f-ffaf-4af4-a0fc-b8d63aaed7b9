<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549682569217" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:带熔断器35kVPT11_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="1.1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="26.25" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="18.06481481481482" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="20.69444444444444" y1="24.96612466124661" y2="24.96612466124661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69444444444444" x2="19.37962962962963" y1="24.96612466124661" y2="22.5"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,5.71) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="13" y2="1"/>
   <ellipse cx="15.03" cy="18.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.67" cy="23.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="18.5" cy="23.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="20.5" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
  </symbol>
  <symbol id="Accessory:线路PT99_0" viewBox="0,0,35,30">
   <use terminal-index="0" type="0" x="35" xlink:href="#terminal" y="6.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.5" x2="1.5" y1="4" y2="9"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.27377700368697" x2="20.43246971500333" y1="6.765818666751574" y2="6.765818666751574"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.6817302644139" x2="34.98333333333333" y1="6.765818666751574" y2="6.765818666751574"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.55947355115261" x2="24.55947355115261" y1="6.895129129519741" y2="14.09675167759026"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.46102695297582" x2="5.583333333333334" y1="6.846289710456816" y2="6.846289710456816"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="20.47373975336484" x2="20.47373975336484" y1="6.846289710456816" y2="6.846289710456816"/>
   <ellipse cx="24.54" cy="18.35" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="20.47373975336483" x2="20.47373975336483" y1="3.466505874834564" y2="10.30654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.61665011096792" x2="31.61665011096792" y1="6.846289710456816" y2="6.846289710456816"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="17.37848687625285" x2="17.37848687625285" y1="3.466505874834558" y2="10.30654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="31.6166501109679" x2="31.6166501109679" y1="3.466505874834564" y2="10.30654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="28.52139723385593" x2="28.52139723385593" y1="3.466505874834564" y2="10.30654458978453"/>
   <ellipse cx="27.04" cy="22.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="22.04" cy="22.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:四卷带壁雷器母线PT_0" viewBox="0,0,40,35">
   <use terminal-index="0" type="0" x="22.10905664884498" xlink:href="#terminal" y="34.51612485684674"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="28.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <rect fill-opacity="0" height="15.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,30,14.36) scale(1,-1) translate(0,-926.43)" width="7" x="26.5" y="6.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="16.27740325661302" y2="15.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="7.166666666666671" y1="16.27740325661302" y2="12.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="12.27740325661302" y2="13.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="30.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="22" y1="28.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="14" y1="21.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="30" y1="28.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="30" y1="28.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.92943360505483" x2="29.92943360505483" y1="6.610736589946352" y2="3.462520268614096"/>
   <ellipse cx="13.81" cy="17.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.95921744067709" x2="27.68240159599324" y1="3.3946117330996" y2="3.3946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.12588410734375" x2="28.51573492932657" y1="2.144611733099605" y2="2.144611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="30.54255077401042" x2="29.09906826265991" y1="0.8946117330996053" y2="0.8946117330996053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599325" x2="16.18240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="13.78240159599324" y1="15.23154965466559" y2="17.7066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="11.38240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <ellipse cx="20.23" cy="13.99" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.48" cy="10.41" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="17.79906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="20.19906826265991" y1="11.73154965466559" y2="14.2066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="22.59906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="8.148216321332258" y2="10.62329629003646"/>
   <ellipse cx="6.64" cy="14.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀11_0" viewBox="0,0,20,25">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="16" y1="1.75" y2="1.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="16" y1="1.75" y2="6.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="5" y1="17.75" y2="17.75"/>
   <path d="M 16 8.75 L 15.5 6.75 L 16.5 6.75 L 16 8.75 z" fill="rgb(0,0,0)" fill-opacity="1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="20.58333333333334" y2="13.36429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666668" x2="8.000000000000002" y1="20.59694619969018" y2="20.59694619969018"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333334" x2="7" y1="22.43157590710537" y2="22.43157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="24.18348701738202" y2="24.18348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.9166666666666661" x2="5.081410256410254" y1="3.516666666666666" y2="13.3642916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.39249548976499" y2="3.39249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.350000000000005" y2="0.6666666666666679"/>
   <path d="M 16.0833 10.9167 L 15.5833 12.9167 L 16.5833 12.9167 L 16.0833 10.9167 z" fill="rgb(0,0,0)" fill-opacity="1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.08333333333334" x2="16.08333333333334" y1="17.91666666666667" y2="12.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀11_1" viewBox="0,0,20,25">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="16" y1="1.75" y2="6.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="16" y1="1.75" y2="1.75"/>
   <path d="M 16 8.75 L 15.5 6.75 L 16.5 6.75 L 16 8.75 z" fill="rgb(0,0,0)" fill-opacity="1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="5" y1="17.75" y2="17.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="20.58333333333333" y2="3.333333333333332"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666668" x2="8.000000000000002" y1="20.59694619969018" y2="20.59694619969018"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333334" x2="7" y1="22.43157590710537" y2="22.43157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="24.18348701738202" y2="24.18348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.350000000000005" y2="0.6666666666666679"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.39249548976499" y2="3.39249548976499"/>
   <path d="M 16.0833 10.9167 L 15.5833 12.9167 L 16.5833 12.9167 L 16.0833 10.9167 z" fill="rgb(0,0,0)" fill-opacity="1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.08333333333334" x2="16.08333333333334" y1="17.91666666666667" y2="12.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀11_2" viewBox="0,0,20,25">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="8" y1="4.5" y2="12.5"/>
   <path d="M 16 8.75 L 15.5 6.75 L 16.5 6.75 L 16 8.75 z" fill="rgb(0,0,0)" fill-opacity="1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="16" y1="1.75" y2="6.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="5" y1="17.75" y2="17.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="16" y1="1.75" y2="1.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="2" y1="4.5" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="20.58333333333334" y2="13.36429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666668" x2="8.000000000000002" y1="20.59694619969018" y2="20.59694619969018"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333334" x2="7" y1="22.43157590710537" y2="22.43157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="24.18348701738202" y2="24.18348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.39249548976499" y2="3.39249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.350000000000005" y2="0.6666666666666679"/>
   <path d="M 16.0833 10.9167 L 15.5833 12.9167 L 16.5833 12.9167 L 16.0833 10.9167 z" fill="rgb(0,0,0)" fill-opacity="1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.08333333333334" x2="16.08333333333334" y1="17.91666666666667" y2="12.91666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变13_0" viewBox="0,0,32,35">
   <use terminal-index="0" type="0" x="16" xlink:href="#terminal" y="3.5"/>
   <ellipse cx="16.04" cy="11.97" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.97" cy="24.14" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067018" x2="16.03525619067018" y1="20.85" y2="24.95894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067017" x2="10.66666666666667" y1="24.98394833233988" y2="28.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.06302903072498" x2="21.16666666666667" y1="24.93990500916851" y2="28.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067018" x2="16.03525619067018" y1="7.6" y2="11.70894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067017" x2="12.05" y1="11.73394833233987" y2="14.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.06302903072498" x2="20.15" y1="11.68990500916851" y2="14.5"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_0" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="3.333333333333332" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="24.08333333333333" y2="29.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="7" y1="19.5" y2="24.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_1" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="18.25" y2="32.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_2" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="4" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="10" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="25.83333333333333" y2="32.41666666666667"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="PowerTransformer3:主变高压侧有中性点_0" viewBox="0,0,50,50">
   <use terminal-index="0" type="1" x="14.75" xlink:href="#terminal" y="0.2499999999999929"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.33333333333333" x2="15.15740740740741" y1="10.58333333333333" y2="14.1754686785551"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.08333333333333" x2="15.17203932327389" y1="10.66666666666667" y2="14.1754686785551"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.16666666666666" x2="15.16666666666666" y1="14.16666666666666" y2="18.91666666666666"/>
   <ellipse cx="14.93" cy="15" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="2" x="15.08333333333334" xlink:href="#terminal" y="14.16666666666666"/>
  </symbol>
  <symbol id="PowerTransformer3:主变高压侧有中性点_1" viewBox="0,0,50,50">
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.66666666666666" x2="34.66666666666666" y1="29.41666666666667" y2="25.11796982167353"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.66598079561043" x2="29.75" y1="25.11796982167353" y2="20.66666666666667"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.67329675354367" x2="39.25" y1="25.11065386374029" y2="20.58333333333334"/>
   <ellipse cx="34.78" cy="25.08" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="1" x="49.50000000000001" xlink:href="#terminal" y="25.08333333333333"/>
  </symbol>
  <symbol id="PowerTransformer3:主变高压侧有中性点_2" viewBox="0,0,50,50">
   <path d="M 15.1667 32 L 10.0833 40 L 20.1667 40 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.12" cy="35.17" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="3" type="1" x="15.08333333333334" xlink:href="#terminal" y="49.91666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="Accessory:PT象达_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15.5" xlink:href="#terminal" y="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="18.25" y1="12.08333333333333" y2="12.08333333333333"/>
   <ellipse cx="15.65" cy="12.68" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.9" cy="18.28" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="18.25" y1="18.33333333333333" y2="18.33333333333333"/>
  </symbol>
  <symbol id="Accessory:避雷器带指示灯_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="5" y2="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.83333333333333" x2="10.83333333333333" y1="17.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.83333333333334" x2="18.83333333333334" y1="20.75" y2="18.33333333333334"/>
   <ellipse cx="18.83" cy="16" fill-opacity="0" rx="2.24" ry="2.24" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.83333333333333" x2="18.83333333333334" y1="4.75" y2="4.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.83333333333333" x2="18.83333333333334" y1="20.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.83333333333333" x2="14.83333333333333" y1="23.08333333333334" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.08333333333333" x2="16.08333333333334" y1="25.25" y2="25.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.08333333333333" x2="17.08333333333334" y1="23.25" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.58333333333333" x2="16.58333333333334" y1="24.25" y2="24.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.16666666666667" x2="17.5" y1="14.33333333333333" y2="17.75"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.83,13.25) scale(1,1) translate(0,0)" width="6" x="7.83" y="8.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.83333333333333" x2="20.83333333333334" y1="11.75" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.83333333333334" x2="18.83333333333334" y1="4.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.83333333333333" x2="20.83333333333334" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.83333333333334" x2="18.83333333333334" y1="11.75" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.833333333333334" x2="10.83333333333333" y1="13.75" y2="14.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.41666666666667" x2="20.41666666666667" y1="14.33333333333333" y2="17.33333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.83333333333333" x2="11.83333333333333" y1="14.75" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.83333333333333" x2="10.83333333333333" y1="4.75" y2="14.75"/>
  </symbol>
  <symbol id="Accessory:10kV母线PT带消谐装置_0" viewBox="0,0,35,30">
   <use terminal-index="0" type="0" x="17.56245852479325" xlink:href="#terminal" y="28.12373692455963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.61245852479333" x2="34.61245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.86245852479331" x2="30.86245852479331" y1="22.68299618381883" y2="19.84040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.6124585247933" x2="26.6124585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.61245852479332" x2="34.61245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666667" x2="31" y1="22.69225544307809" y2="22.69225544307809"/>
   <rect fill-opacity="0" height="3.55" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,30.81,15.38) scale(1,1) translate(0,0)" width="8.92" x="26.34" y="13.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.647721108666" x2="30.647721108666" y1="10.83674821859629" y2="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.71438777533266" x2="28.71438777533266" y1="7.51612768565195" y2="7.51612768565195"/>
   <ellipse cx="14.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.05048569403981" x2="22.05048569403981" y1="34.53084700683308" y2="34.53084700683308"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.7608382776216" x2="28.48402243293775" y1="7.754208141873304" y2="7.754208141873304"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.01083827762157" x2="29.40068909960439" y1="6.50420814187332" y2="6.50420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.51083827762159" x2="30.06735576627107" y1="5.004208141873296" y2="5.004208141873296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.89410730945214" x2="23.0877735452272" y1="24.15299989806297" y2="21.53208583485582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58713830216066" x2="22.81883560169719" y1="21.54040359122622" y2="21.54040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51457106407813" x2="19.32090482830307" y1="24.05654513693459" y2="21.43563107372743"/>
   <ellipse cx="14.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.3644003886642" x2="22.3644003886642" y1="30.19213090500035" y2="30.19213090500035"/>
   <ellipse cx="21.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.19220382559271" x2="17.19220382559271" y1="23.07076893362116" y2="23.07076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.4422038255927" x2="16.4422038255927" y1="16.82076893362115" y2="16.82076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.92264294445647" x2="20.87303257583197" y1="16.99992879670395" y2="15.47611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.873032575832" x2="20.37063985073817" y1="15.47611741162252" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.87303257583199" x2="23.32581493230132" y1="15.47611741162254" y2="16.60543752773795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.94220382559271" x2="16.94220382559271" y1="16.32076893362115" y2="16.32076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="16.07581493230131" y1="22.72611741162255" y2="23.85543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="13.12063985073816" y1="22.72611741162255" y2="20.07298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583197" y1="24.24992879670398" y2="22.72611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="16.07581493230131" y1="15.47611741162254" y2="16.60543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="13.12063985073816" y1="15.47611741162253" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583196" y1="16.99992879670396" y2="15.47611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.59103363843511" x2="17.59103363843511" y1="25.15822158129307" y2="28.16666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变11_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="9.25" y2="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="26.83333333333333" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0928507596068" x2="10.0928507596068" y1="10.97013412501683" y2="12.64189293057399"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.77292225201073" x2="10.0928507596068" y1="14.31365173613114" y2="12.64189293057398"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id=":线路发电机1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="30"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.08333333333333215" y2="29.74999999999999"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV户宋河电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="48" xlink:href="logo.png" y="43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,176,73) scale(1,1) translate(0,0)" writing-mode="lr" x="176" xml:space="preserve" y="76.5" zvalue="54"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,176.5,72.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="176.5" xml:space="preserve" y="81.69" zvalue="55">110kV户宋河电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="372" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,82.625,185.25) scale(1,1) translate(0,0)" width="72.88" x="46.19" y="173.25" zvalue="1811"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.625,185.25) scale(1,1) translate(0,0)" writing-mode="lr" x="82.63" xml:space="preserve" y="189.75" zvalue="1811">信号一览</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="225" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,82.625,225.25) scale(1,1) translate(0,0)" width="72.88" x="46.19" y="213.25" zvalue="1989"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.625,225.25) scale(1,1) translate(0,0)" writing-mode="lr" x="82.63" xml:space="preserve" y="229.75" zvalue="1989">AVC / AGC</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,434.222,284.889) scale(1,1) translate(0,0)" writing-mode="lr" x="434.22" xml:space="preserve" y="289.39" zvalue="7">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1802.44,317.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1802.44" xml:space="preserve" y="321.72" zvalue="12">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,786.883,635.556) scale(1,1) translate(0,0)" writing-mode="lr" x="786.88" xml:space="preserve" y="640.0599999999999" zvalue="16">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,521.333,220.111) scale(1,1) translate(0,0)" writing-mode="lr" x="521.33" xml:space="preserve" y="224.61" zvalue="17">171</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,512.556,267.667) scale(1,1) translate(0,0)" writing-mode="lr" x="512.5599999999999" xml:space="preserve" y="272.17" zvalue="18">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,514.222,159.667) scale(1,1) translate(0,0)" writing-mode="lr" x="514.22" xml:space="preserve" y="164.17" zvalue="20">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,494.932,68.4444) scale(1,1) translate(0,0)" writing-mode="lr" x="494.93" xml:space="preserve" y="72.94" zvalue="25">110kV户南线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,549.056,258.778) scale(1,1) translate(0,0)" writing-mode="lr" x="549.0599999999999" xml:space="preserve" y="263.28" zvalue="27">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,549.056,206.333) scale(1,1) translate(0,0)" writing-mode="lr" x="549.0599999999999" xml:space="preserve" y="210.83" zvalue="29">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,549.056,151.889) scale(1,1) translate(0,0)" writing-mode="lr" x="549.0599999999999" xml:space="preserve" y="156.39" zvalue="32">67</text>
  <line fill="none" id="67" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382" x2="382" y1="11" y2="1041" zvalue="58"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,840.556,255.667) scale(1,1) translate(0,0)" writing-mode="lr" x="840.5599999999999" xml:space="preserve" y="260.17" zvalue="94">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,869.333,242.944) scale(1,1) translate(0,0)" writing-mode="lr" x="869.33" xml:space="preserve" y="247.44" zvalue="96">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,692.778,337.222) scale(1,1) translate(0,0)" writing-mode="lr" x="692.78" xml:space="preserve" y="341.72" zvalue="102">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,702.111,393.222) scale(1,1) translate(0,0)" writing-mode="lr" x="702.11" xml:space="preserve" y="397.72" zvalue="103">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,724.444,370.222) scale(1,1) translate(0,0)" writing-mode="lr" x="724.4400000000001" xml:space="preserve" y="374.72" zvalue="115">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="309" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,558.515,701.111) scale(1,1) translate(6.06215e-14,0)" writing-mode="lr" x="558.51" xml:space="preserve" y="705.61" zvalue="613">071</text>
  <line fill="none" id="666" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="146.8704926140824" y2="146.8704926140824" zvalue="1056"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,211.804,952.75) scale(1,1) translate(0,0)" writing-mode="lr" x="211.8" xml:space="preserve" y="958.75" zvalue="1080"> HuSongHe-01-2015</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,145.554,987.25) scale(1,1) translate(0,0)" writing-mode="lr" x="145.55" xml:space="preserve" y="993.25" zvalue="1081">李文杰</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,325.554,986) scale(1,1) translate(0,0)" writing-mode="lr" x="325.55" xml:space="preserve" y="992" zvalue="1082">20200911</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,813.956,144.75) scale(1,1) translate(0,0)" writing-mode="lr" x="813.9562440537476" xml:space="preserve" y="149.25" zvalue="1094">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,869.333,284.111) scale(1,1) translate(0,0)" writing-mode="lr" x="869.33" xml:space="preserve" y="288.61" zvalue="1096">10</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,766.5,520.25) scale(1,1) translate(0,0)" writing-mode="lr" x="766.5000000000001" xml:space="preserve" y="524.75" zvalue="1098">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="729" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1485.83,216.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1485.83" xml:space="preserve" y="220.67" zvalue="1150">371</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="728" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1477.06,263.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1477.06" xml:space="preserve" y="268.22" zvalue="1151">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="727" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1478.72,163.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1478.72" xml:space="preserve" y="168.22" zvalue="1154">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="726" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1463.53,77.5) scale(1,1) translate(7.8411e-13,0)" writing-mode="lr" x="1463.53" xml:space="preserve" y="82" zvalue="1158">35kV宏利硅厂线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="723" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1501.56,143.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1501.56" xml:space="preserve" y="148.12" zvalue="1164">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="839" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1795.64,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1795.64" xml:space="preserve" y="266.67" zvalue="1247">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="851" stroke="rgb(255,255,255)" text-anchor="middle" x="530" xml:space="preserve" y="927.75" zvalue="1261">#1发电机 </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="851" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="530" xml:space="preserve" y="943.75" zvalue="1261">21MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,489,847) scale(1,1) translate(0,0)" writing-mode="lr" x="489" xml:space="preserve" y="851.5" zvalue="1264">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,632,851) scale(1,1) translate(0,0)" writing-mode="lr" x="632" xml:space="preserve" y="855.5" zvalue="1268">0912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,494,609.222) scale(1,1) translate(0,0)" writing-mode="lr" x="494" xml:space="preserve" y="613.72" zvalue="1306">078</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,706,702.333) scale(1,1) translate(0,0)" writing-mode="lr" x="706" xml:space="preserve" y="706.83" zvalue="1320">072</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" x="763.328125" xml:space="preserve" y="789.5" zvalue="1339">10kVⅠ母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="763.328125" xml:space="preserve" y="805.5" zvalue="1339">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,694.833,219.167) scale(1,1) translate(0,0)" writing-mode="lr" x="694.83" xml:space="preserve" y="223.67" zvalue="1438">172</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="256" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,686.056,266.722) scale(1,1) translate(0,0)" writing-mode="lr" x="686.0599999999999" xml:space="preserve" y="271.22" zvalue="1439">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,687.722,158.722) scale(1,1) translate(0,0)" writing-mode="lr" x="687.72" xml:space="preserve" y="163.22" zvalue="1442">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="254" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,669.432,68.5) scale(1,1) translate(0,0)" writing-mode="lr" x="669.4299999999999" xml:space="preserve" y="73" zvalue="1446">110kV宋勐佳线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,722.556,254.167) scale(1,1) translate(0,0)" writing-mode="lr" x="722.5599999999999" xml:space="preserve" y="258.67" zvalue="1449">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,722.556,205.389) scale(1,1) translate(0,0)" writing-mode="lr" x="722.5599999999999" xml:space="preserve" y="209.89" zvalue="1451">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,722.556,150.944) scale(1,1) translate(0,0)" writing-mode="lr" x="722.5599999999999" xml:space="preserve" y="155.44" zvalue="1453">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,981.833,218.167) scale(1,1) translate(1.07673e-13,0)" writing-mode="lr" x="981.83" xml:space="preserve" y="222.67" zvalue="1464">173</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="282" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,973.056,265.722) scale(1,1) translate(0,0)" writing-mode="lr" x="973.0599999999999" xml:space="preserve" y="270.22" zvalue="1465">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="280" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,974.722,157.722) scale(1,1) translate(0,0)" writing-mode="lr" x="974.72" xml:space="preserve" y="162.22" zvalue="1468">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="279" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,956.432,67.5) scale(1,1) translate(0,0)" writing-mode="lr" x="956.4299999999999" xml:space="preserve" y="72" zvalue="1472">110kV允宋线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="277" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1009.56,202.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1009.56" xml:space="preserve" y="206.67" zvalue="1477">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="276" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1009.56,149.944) scale(1,1) translate(0,0)" writing-mode="lr" x="1009.56" xml:space="preserve" y="154.44" zvalue="1479">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="307" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1157.83,217.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.83" xml:space="preserve" y="221.67" zvalue="1488">174</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="306" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1149.06,264.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1149.06" xml:space="preserve" y="269.22" zvalue="1489">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="305" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1150.72,156.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1150.72" xml:space="preserve" y="161.22" zvalue="1492">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="304" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1132.43,66.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1132.43" xml:space="preserve" y="71" zvalue="1496">110kV宋腊线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="303" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1185.56,198.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1185.56" xml:space="preserve" y="202.67" zvalue="1499">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1185.56,148.944) scale(1,1) translate(0,0)" writing-mode="lr" x="1185.56" xml:space="preserve" y="153.44" zvalue="1501">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,575,531) scale(1,1) translate(0,0)" writing-mode="lr" x="575" xml:space="preserve" y="535.5" zvalue="1511">1010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,467.5,508) scale(1,1) translate(0,0)" writing-mode="lr" x="467.5" xml:space="preserve" y="512.5" zvalue="1514">生活区</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,679,811) scale(1,1) translate(0,0)" writing-mode="lr" x="679" xml:space="preserve" y="815.5" zvalue="1516">#1厂用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,764.25,547.75) scale(1,1) translate(0,0)" writing-mode="lr" x="764.2499999999999" xml:space="preserve" y="552.25" zvalue="1521">25MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,788,687) scale(1,1) translate(0,0)" writing-mode="lr" x="788" xml:space="preserve" y="691.5" zvalue="1525">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1193,635.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1193" xml:space="preserve" y="640.0599999999999" zvalue="1529">10kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1014.89,337.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1014.89" xml:space="preserve" y="341.72" zvalue="1532">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1024.23,393.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1024.23" xml:space="preserve" y="397.72" zvalue="1533">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1046.56,370.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1046.56" xml:space="preserve" y="374.72" zvalue="1536">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,960.147,700.111) scale(1,1) translate(0,0)" writing-mode="lr" x="960.15" xml:space="preserve" y="704.61" zvalue="1538">073</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1088.62,520.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1088.617063355078" xml:space="preserve" y="524.75" zvalue="1541">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" x="936.125" xml:space="preserve" y="927.75" zvalue="1544">#2发电机  </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="936.125" xml:space="preserve" y="943.75" zvalue="1544">21MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,895.117,847) scale(1,1) translate(0,0)" writing-mode="lr" x="895.12" xml:space="preserve" y="851.5" zvalue="1546">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1038.12,851) scale(1,1) translate(0,0)" writing-mode="lr" x="1038.12" xml:space="preserve" y="855.5" zvalue="1550">0922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1112.12,702.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1112.12" xml:space="preserve" y="706.83" zvalue="1556">074</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" x="1189.4140625" xml:space="preserve" y="789.5" zvalue="1560">10kVⅡ母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1189.4140625" xml:space="preserve" y="805.5" zvalue="1560">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,897.117,531) scale(1,1) translate(0,0)" writing-mode="lr" x="897.12" xml:space="preserve" y="535.5" zvalue="1566">1020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1085.12,811) scale(1,1) translate(0,0)" writing-mode="lr" x="1085.12" xml:space="preserve" y="815.5" zvalue="1572">#2厂用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1086.37,547.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1086.367063355077" xml:space="preserve" y="552.25" zvalue="1577">25MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1219.28,683) scale(1,1) translate(0,0)" writing-mode="lr" x="1219.28" xml:space="preserve" y="687.5" zvalue="1579">0902</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="210" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1549,641.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1549" xml:space="preserve" y="646.0599999999999" zvalue="1633">10kVⅢ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="209" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1376.89,337.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1376.89" xml:space="preserve" y="341.72" zvalue="1636">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1386.23,393.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1386.23" xml:space="preserve" y="397.72" zvalue="1637">103</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1408.56,370.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1408.56" xml:space="preserve" y="374.72" zvalue="1640">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="206" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1366.15,700.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1366.15" xml:space="preserve" y="704.61" zvalue="1642">075</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" x="1342.109375" xml:space="preserve" y="927.75" zvalue="1648">#3发电机  </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1342.109375" xml:space="preserve" y="943.75" zvalue="1648">21MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1301.12,847) scale(1,1) translate(0,0)" writing-mode="lr" x="1301.12" xml:space="preserve" y="851.5" zvalue="1650">0931</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1444.12,851) scale(1,1) translate(0,0)" writing-mode="lr" x="1444.12" xml:space="preserve" y="855.5" zvalue="1654">0932</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1518.12,702.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1518.12" xml:space="preserve" y="706.83" zvalue="1658">076</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" x="1595.421875" xml:space="preserve" y="540.5" zvalue="1662">10kVⅢ母线电</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1595.421875" xml:space="preserve" y="556.5" zvalue="1662">压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1251.12,531) scale(1,1) translate(0,0)" writing-mode="lr" x="1251.12" xml:space="preserve" y="535.5" zvalue="1668">1030</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1491.12,811) scale(1,1) translate(0,0)" writing-mode="lr" x="1491.12" xml:space="preserve" y="815.5" zvalue="1670">#3厂用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="214" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1456.37,499.75) scale(1,1) translate(1.10365e-12,0)" writing-mode="lr" x="1456.367063355077" xml:space="preserve" y="504.25" zvalue="1675">40MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1628.06,629) scale(1,1) translate(0,0)" writing-mode="lr" x="1628.06" xml:space="preserve" y="633.5" zvalue="1677">0903</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="348" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1458,483.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1458" xml:space="preserve" y="488" zvalue="1683">#3主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="351" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1389,600.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1389" xml:space="preserve" y="604.72" zvalue="1686">0036</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1645.83,217.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1645.83" xml:space="preserve" y="221.67" zvalue="1696">372</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1637.06,264.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1637.06" xml:space="preserve" y="269.22" zvalue="1697">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1638.72,164.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1638.72" xml:space="preserve" y="169.22" zvalue="1700">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1620.04,78.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1620.04" xml:space="preserve" y="83" zvalue="1704">35kV金源硅厂线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1661.56,144.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1661.56" xml:space="preserve" y="149.12" zvalue="1706">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1774.5,158) scale(1,1) translate(0,0)" writing-mode="lr" x="1774.5" xml:space="preserve" y="162.5" zvalue="1714">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1638.67,337.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1638.67" xml:space="preserve" y="341.72" zvalue="1721">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="178" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1648.11,393.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1648.11" xml:space="preserve" y="397.72" zvalue="1722">303</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1641.48,443.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1641.48" xml:space="preserve" y="447.72" zvalue="1727">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="196" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1628,701) scale(1,1) translate(0,0)" writing-mode="lr" x="1628" xml:space="preserve" y="705.5" zvalue="1738">077</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="177" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1629.5,798) scale(1,1) translate(0,0)" writing-mode="lr" x="1629.5" xml:space="preserve" y="802.5" zvalue="1741">0779</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="212" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1631,757) scale(1,1) translate(0,0)" writing-mode="lr" x="1631" xml:space="preserve" y="761.5" zvalue="1746">坝区变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="354" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1792,758.997) scale(1,1) translate(0,0)" writing-mode="lr" x="1792" xml:space="preserve" y="763.5" zvalue="1750">10kV户铜线</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="246.9999566202091" y2="246.9999566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="273.3396566202091" y2="273.3396566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="246.9999566202091" y2="273.3396566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="246.9999566202091" y2="273.3396566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="246.9999566202091" y2="246.9999566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="273.3396566202091" y2="273.3396566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="246.9999566202091" y2="273.3396566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="246.9999566202091" y2="273.3396566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="273.3397566202091" y2="273.3397566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="297.9066566202091" y2="297.9066566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="273.3397566202091" y2="297.9066566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="273.3397566202091" y2="297.9066566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="273.3397566202091" y2="273.3397566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="297.9066566202091" y2="297.9066566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="273.3397566202091" y2="297.9066566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="273.3397566202091" y2="297.9066566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="297.9065566202091" y2="297.9065566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="320.9538566202091" y2="320.9538566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="297.9065566202091" y2="320.9538566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="297.9065566202091" y2="320.9538566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="297.9065566202091" y2="297.9065566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="320.9538566202091" y2="320.9538566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="297.9065566202091" y2="320.9538566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="297.9065566202091" y2="320.9538566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="320.9538566202091" y2="320.9538566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="344.0011566202091" y2="344.0011566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="320.9538566202091" y2="344.0011566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="320.9538566202091" y2="344.0011566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="320.9538566202091" y2="320.9538566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="344.0011566202091" y2="344.0011566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="320.9538566202091" y2="344.0011566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="320.9538566202091" y2="344.0011566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="344.0010866202091" y2="344.0010866202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="367.0483866202091" y2="367.0483866202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="344.0010866202091" y2="367.0483866202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="344.0010866202091" y2="367.0483866202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="344.0010866202091" y2="344.0010866202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="367.0483866202091" y2="367.0483866202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="344.0010866202091" y2="367.0483866202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="344.0010866202091" y2="367.0483866202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="367.0483566202091" y2="367.0483566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="390.0956566202091" y2="390.0956566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="367.0483566202091" y2="390.0956566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="367.0483566202091" y2="390.0956566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="367.0483566202091" y2="367.0483566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="390.0956566202091" y2="390.0956566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="367.0483566202091" y2="390.0956566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="367.0483566202091" y2="390.0956566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="390.0955566202091" y2="390.0955566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="413.1428566202091" y2="413.1428566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="390.0955566202091" y2="413.1428566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="390.0955566202091" y2="413.1428566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="390.0955566202091" y2="390.0955566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="413.1428566202091" y2="413.1428566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="390.0955566202091" y2="413.1428566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="390.0955566202091" y2="413.1428566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="413.1428566202091" y2="413.1428566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="436.1901566202091" y2="436.1901566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="413.1428566202091" y2="436.1901566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="413.1428566202091" y2="436.1901566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="413.1428566202091" y2="413.1428566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="436.1901566202091" y2="436.1901566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="413.1428566202091" y2="436.1901566202091"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="413.1428566202091" y2="436.1901566202091"/>
  <line fill="none" id="397" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="492.8704926140824" y2="492.8704926140824" zvalue="1790"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="999.0816" y2="1027"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="395" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,952) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="958" zvalue="1792">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="394" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50,986) scale(1,1) translate(0,0)" writing-mode="lr" x="50" xml:space="preserve" y="992" zvalue="1793">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="393" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232,986) scale(1,1) translate(0,0)" writing-mode="lr" x="232" xml:space="preserve" y="992" zvalue="1794">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="392" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="49" xml:space="preserve" y="1020" zvalue="1795">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="391" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="231" xml:space="preserve" y="1020" zvalue="1796">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="388" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.5,566.5) scale(1,1) translate(0,0)" writing-mode="lr" x="73.5" xml:space="preserve" y="571" zvalue="1798">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="387" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,198.399,185.841) scale(1,1) translate(0,0)" writing-mode="lr" x="198.4" xml:space="preserve" y="190.34" zvalue="1799">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="386" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,303.399,185.841) scale(1,1) translate(0,0)" writing-mode="lr" x="303.4" xml:space="preserve" y="190.34" zvalue="1800">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="385" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,55.5,260) scale(1,1) translate(0,0)" writing-mode="lr" x="13" xml:space="preserve" y="264.5" zvalue="1801">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="384" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,236,260) scale(1,1) translate(0,0)" writing-mode="lr" x="193.5" xml:space="preserve" y="264.5" zvalue="1802">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="382" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.6875,333.25) scale(1,1) translate(0,0)" writing-mode="lr" x="58.69" xml:space="preserve" y="337.75" zvalue="1803">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="381" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.625,332) scale(1,1) translate(0,0)" writing-mode="lr" x="237.63" xml:space="preserve" y="336.5" zvalue="1804">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="380" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6875,355.25) scale(1,1) translate(0,0)" writing-mode="lr" x="55.69" xml:space="preserve" y="359.75" zvalue="1805">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="371" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,54.5,286) scale(1,1) translate(0,0)" writing-mode="lr" x="12" xml:space="preserve" y="290.5" zvalue="1812">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="370" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235,286) scale(1,1) translate(0,0)" writing-mode="lr" x="192.5" xml:space="preserve" y="290.5" zvalue="1813">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="367" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6875,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="55.69" xml:space="preserve" y="407.75" zvalue="1816">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="366" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,223.688,402.25) scale(1,1) translate(0,0)" writing-mode="lr" x="223.69" xml:space="preserve" y="406.75" zvalue="1817">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="365" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6875,426.25) scale(1,1) translate(0,0)" writing-mode="lr" x="55.69" xml:space="preserve" y="430.75" zvalue="1818">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="364" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,223.688,425.25) scale(1,1) translate(0,0)" writing-mode="lr" x="223.69" xml:space="preserve" y="429.75" zvalue="1819">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="363" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,54.5,309) scale(1,1) translate(0,0)" writing-mode="lr" x="12" xml:space="preserve" y="313.5" zvalue="1820">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="361" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,234.5,308) scale(1,1) translate(0,0)" writing-mode="lr" x="192" xml:space="preserve" y="312.5" zvalue="1822">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="358" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.688,355.25) scale(1,1) translate(0,0)" writing-mode="lr" x="235.69" xml:space="preserve" y="359.75" zvalue="1825">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="353" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.2589,377.821) scale(1,1) translate(0,0)" writing-mode="lr" x="54.26" xml:space="preserve" y="382.32" zvalue="1828">10kVⅢ母频率</text>
 </g>
 <g id="ButtonClass">
  <g href="户撒四级电站_110kV母线.svg"><rect fill-opacity="0" height="40" width="12" x="809" y="230" zvalue="1053"/></g>
  <g href="户撒四级电站_110kV母线.svg"><rect fill-opacity="0" height="40" width="12" x="1764.09" y="228.5" zvalue="1253"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="46.19" y="173.25" zvalue="1811"/></g>
  <g href="新能源AVC AGC 控制.svg"><rect fill-opacity="0" height="24" width="72.88" x="46.19" y="213.25" zvalue="1989"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="89">
   <use class="kv110" height="30" transform="rotate(0,815.222,256.667) scale(-1.11111,-0.814815) translate(-1548.09,-574.444)" width="15" x="806.8888888888888" xlink:href="#Disconnector:刀闸_0" y="244.4444581137762" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454487834627" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454487834627"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,815.222,256.667) scale(-1.11111,-0.814815) translate(-1548.09,-574.444)" width="15" x="806.8888888888888" y="244.4444581137762"/></g>
  <g id="848">
   <use class="kv35" height="30" transform="rotate(0,1770.31,263.167) scale(-1.11111,-0.814815) translate(-3362.76,-588.922)" width="15" x="1761.976400781394" xlink:href="#Disconnector:刀闸_0" y="250.9444581137762" zvalue="1246"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454488752131" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454488752131"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1770.31,263.167) scale(-1.11111,-0.814815) translate(-3362.76,-588.922)" width="15" x="1761.976400781394" y="250.9444581137762"/></g>
  <g id="21">
   <use class="kv110" height="30" transform="rotate(0,500,268.667) scale(-1.11111,-0.814815) translate(-949.167,-601.172)" width="15" x="491.6666666666666" xlink:href="#Disconnector:刀闸_0" y="256.4444580078124" zvalue="17"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454487244803" ObjectName="110kV户南线1711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454487244803"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,500,268.667) scale(-1.11111,-0.814815) translate(-949.167,-601.172)" width="15" x="491.6666666666666" y="256.4444580078124"/></g>
  <g id="22">
   <use class="kv110" height="30" transform="rotate(0,500,160.667) scale(-1.11111,-0.814815) translate(-949.167,-360.626)" width="15" x="491.6666666931577" xlink:href="#Disconnector:刀闸_0" y="148.4444444444445" zvalue="19"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454487310339" ObjectName="110kV户南线1716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454487310339"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,500,160.667) scale(-1.11111,-0.814815) translate(-949.167,-360.626)" width="15" x="491.6666666931577" y="148.4444444444445"/></g>
  <g id="99">
   <use class="kv110" height="30" transform="rotate(0,681.333,338.222) scale(1.11111,0.814815) translate(-67.3,74.0909)" width="15" x="673.0000101725263" xlink:href="#Disconnector:刀闸_0" y="326" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454488031235" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454488031235"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,681.333,338.222) scale(1.11111,0.814815) translate(-67.3,74.0909)" width="15" x="673.0000101725263" y="326"/></g>
  <g id="763">
   <use class="kv35" height="30" transform="rotate(0,1464.5,264.722) scale(-1.11111,-0.814815) translate(-2781.72,-592.386)" width="15" x="1456.166666666667" xlink:href="#Disconnector:刀闸_0" y="252.4999862247043" zvalue="1149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454488686595" ObjectName="35kV宏利硅厂线3711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454488686595"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1464.5,264.722) scale(-1.11111,-0.814815) translate(-2781.72,-592.386)" width="15" x="1456.166666666667" y="252.4999862247043"/></g>
  <g id="762">
   <use class="kv35" height="30" transform="rotate(0,1464.5,164.722) scale(-1.11111,-0.814815) translate(-2781.72,-369.659)" width="15" x="1456.166666693158" xlink:href="#Disconnector:刀闸_0" y="152.4999862247043" zvalue="1152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454488621059" ObjectName="35kV宏利硅厂线3716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454488621059"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1464.5,164.722) scale(-1.11111,-0.814815) translate(-2781.72,-369.659)" width="15" x="1456.166666693158" y="152.4999862247043"/></g>
  <g id="9">
   <use class="kv10" height="26" transform="rotate(0,467,848) scale(1,1) translate(0,0)" width="12" x="461" xlink:href="#Disconnector:单手车刀闸1212_0" y="835" zvalue="1263"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454488883203" ObjectName="#1发电机0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454488883203"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,467,848) scale(1,1) translate(0,0)" width="12" x="461" y="835"/></g>
  <g id="18">
   <use class="kv10" height="26" transform="rotate(0,605,848) scale(1,1) translate(0,0)" width="12" x="599" xlink:href="#Disconnector:单手车刀闸1212_0" y="835" zvalue="1267"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454488948739" ObjectName="#1发电机0912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454488948739"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,605,848) scale(1,1) translate(0,0)" width="12" x="599" y="835"/></g>
  <g id="272">
   <use class="kv110" height="30" transform="rotate(0,673.5,267.722) scale(-1.11111,-0.814815) translate(-1278.82,-599.068)" width="15" x="665.1666666666666" xlink:href="#Disconnector:刀闸_0" y="255.5000133514405" zvalue="1437"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454489735171" ObjectName="110kV宋勐佳线1721隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454489735171"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,673.5,267.722) scale(-1.11111,-0.814815) translate(-1278.82,-599.068)" width="15" x="665.1666666666666" y="255.5000133514405"/></g>
  <g id="271">
   <use class="kv110" height="30" transform="rotate(0,673.5,159.722) scale(-1.11111,-0.814815) translate(-1278.82,-358.523)" width="15" x="665.1666666931577" xlink:href="#Disconnector:刀闸_0" y="147.4999997880724" zvalue="1440"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454489669635" ObjectName="110kV宋勐佳线1726隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454489669635"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,673.5,159.722) scale(-1.11111,-0.814815) translate(-1278.82,-358.523)" width="15" x="665.1666666931577" y="147.4999997880724"/></g>
  <g id="300">
   <use class="kv110" height="30" transform="rotate(0,960.5,266.722) scale(-1.11111,-0.814815) translate(-1824.12,-596.841)" width="15" x="952.1666666666666" xlink:href="#Disconnector:刀闸_0" y="254.5000133514405" zvalue="1463"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454490259459" ObjectName="110kV允宋线1731隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454490259459"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,960.5,266.722) scale(-1.11111,-0.814815) translate(-1824.12,-596.841)" width="15" x="952.1666666666666" y="254.5000133514405"/></g>
  <g id="299">
   <use class="kv110" height="30" transform="rotate(0,960.5,158.722) scale(-1.11111,-0.814815) translate(-1824.12,-356.295)" width="15" x="952.1666666931577" xlink:href="#Disconnector:刀闸_0" y="146.4999997880724" zvalue="1466"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454490193923" ObjectName="110kV允宋线1736隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454490193923"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,960.5,158.722) scale(-1.11111,-0.814815) translate(-1824.12,-356.295)" width="15" x="952.1666666931577" y="146.4999997880724"/></g>
  <g id="322">
   <use class="kv110" height="30" transform="rotate(0,1136.5,265.722) scale(-1.11111,-0.814815) translate(-2158.52,-594.614)" width="15" x="1128.166666666667" xlink:href="#Disconnector:刀闸_0" y="253.5000133514405" zvalue="1487"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454490783747" ObjectName="110kV宋腊线1741隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454490783747"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1136.5,265.722) scale(-1.11111,-0.814815) translate(-2158.52,-594.614)" width="15" x="1128.166666666667" y="253.5000133514405"/></g>
  <g id="321">
   <use class="kv110" height="30" transform="rotate(0,1136.5,157.722) scale(-1.11111,-0.814815) translate(-2158.52,-354.068)" width="15" x="1128.166666693158" xlink:href="#Disconnector:刀闸_0" y="145.4999997880724" zvalue="1490"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454490718211" ObjectName="110kV宋腊线1746隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454490718211"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1136.5,157.722) scale(-1.11111,-0.814815) translate(-2158.52,-354.068)" width="15" x="1128.166666693158" y="145.4999997880724"/></g>
  <g id="41">
   <use class="kv10" height="33" transform="rotate(0,762,681) scale(1,-1) translate(0,-1362)" width="14" x="755" xlink:href="#Disconnector:手车隔离开关13_0" y="664.5" zvalue="1524"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454491308035" ObjectName="10kVⅠ母电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454491308035"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,762,681) scale(1,-1) translate(0,-1362)" width="14" x="755" y="664.5"/></g>
  <g id="164">
   <use class="kv110" height="30" transform="rotate(0,1003.45,338.222) scale(1.11111,0.814815) translate(-99.5117,74.0909)" width="15" x="995.1170735276038" xlink:href="#Disconnector:刀闸_0" y="326" zvalue="1530"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454492225539" ObjectName="#2主变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454492225539"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1003.45,338.222) scale(1.11111,0.814815) translate(-99.5117,74.0909)" width="15" x="995.1170735276038" y="326"/></g>
  <g id="150">
   <use class="kv10" height="26" transform="rotate(0,873.117,848) scale(1,1) translate(0,0)" width="12" x="867.1170633550774" xlink:href="#Disconnector:单手车刀闸1212_0" y="835" zvalue="1545"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454491963395" ObjectName="#2发电机0921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454491963395"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,873.117,848) scale(1,1) translate(0,0)" width="12" x="867.1170633550774" y="835"/></g>
  <g id="147">
   <use class="kv10" height="26" transform="rotate(0,1011.12,848) scale(1,1) translate(0,0)" width="12" x="1005.117063355077" xlink:href="#Disconnector:单手车刀闸1212_0" y="835" zvalue="1549"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454491897859" ObjectName="#2发电机0922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454491897859"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1011.12,848) scale(1,1) translate(0,0)" width="12" x="1005.117063355077" y="835"/></g>
  <g id="119">
   <use class="kv10" height="33" transform="rotate(0,1188.12,681) scale(1,-1) translate(0,-1362)" width="14" x="1181.117063355077" xlink:href="#Disconnector:手车隔离开关13_0" y="664.5" zvalue="1578"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454491373571" ObjectName="10kVⅡ母电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454491373571"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,1188.12,681) scale(1,-1) translate(0,-1362)" width="14" x="1181.117063355077" y="664.5"/></g>
  <g id="342">
   <use class="kv110" height="30" transform="rotate(0,1365.45,338.222) scale(1.11111,0.814815) translate(-135.712,74.0909)" width="15" x="1357.117073527604" xlink:href="#Disconnector:刀闸_0" y="326" zvalue="1634"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454493143043" ObjectName="#3主变110kV侧1031隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454493143043"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1365.45,338.222) scale(1.11111,0.814815) translate(-135.712,74.0909)" width="15" x="1357.117073527604" y="326"/></g>
  <g id="331">
   <use class="kv10" height="26" transform="rotate(0,1279.12,848) scale(1,1) translate(0,0)" width="12" x="1273.117063355077" xlink:href="#Disconnector:单手车刀闸1212_0" y="835" zvalue="1649"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454492880899" ObjectName="#3发电机0931隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454492880899"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1279.12,848) scale(1,1) translate(0,0)" width="12" x="1273.117063355077" y="835"/></g>
  <g id="328">
   <use class="kv10" height="26" transform="rotate(0,1417.12,848) scale(1,1) translate(0,0)" width="12" x="1411.117063355077" xlink:href="#Disconnector:单手车刀闸1212_0" y="835" zvalue="1653"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454492815363" ObjectName="#3发电机0932隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454492815363"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1417.12,848) scale(1,1) translate(0,0)" width="12" x="1411.117063355077" y="835"/></g>
  <g id="213">
   <use class="kv10" height="33" transform="rotate(0,1594.12,627) scale(1,1) translate(0,0)" width="14" x="1587.117063355077" xlink:href="#Disconnector:手车隔离开关13_0" y="610.5" zvalue="1676"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454492291075" ObjectName="10kVⅢ母电压互感器0903隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454492291075"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,1594.12,627) scale(1,1) translate(0,0)" width="14" x="1587.117063355077" y="610.5"/></g>
  <g id="350">
   <use class="kv10" height="30" transform="rotate(0,1365.33,598.222) scale(1.11111,0.814815) translate(-135.7,133.182)" width="15" x="1357" xlink:href="#Disconnector:刀闸_0" y="586" zvalue="1685"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454493208579" ObjectName="#3主变10kV侧0036隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454493208579"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1365.33,598.222) scale(1.11111,0.814815) translate(-135.7,133.182)" width="15" x="1357" y="586"/></g>
  <g id="169">
   <use class="kv35" height="30" transform="rotate(0,1624.5,265.722) scale(-1.11111,-0.814815) translate(-3085.72,-594.614)" width="15" x="1616.166666666667" xlink:href="#Disconnector:刀闸_0" y="253.4999862247043" zvalue="1695"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454493798403" ObjectName="35kV金源硅厂线3721隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454493798403"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1624.5,265.722) scale(-1.11111,-0.814815) translate(-3085.72,-594.614)" width="15" x="1616.166666666667" y="253.4999862247043"/></g>
  <g id="142">
   <use class="kv35" height="30" transform="rotate(0,1624.5,165.722) scale(-1.11111,-0.814815) translate(-3085.72,-371.886)" width="15" x="1616.166666693158" xlink:href="#Disconnector:刀闸_0" y="153.4999862247043" zvalue="1698"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454493732867" ObjectName="35kV金源硅厂线3726隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454493732867"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1624.5,165.722) scale(-1.11111,-0.814815) translate(-3085.72,-371.886)" width="15" x="1616.166666693158" y="153.4999862247043"/></g>
  <g id="183">
   <use class="kv35" height="30" transform="rotate(0,1627.22,338.222) scale(1.11111,0.814815) translate(-161.889,74.0909)" width="15" x="1618.891298801276" xlink:href="#Disconnector:刀闸_0" y="326" zvalue="1719"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454493995011" ObjectName="#3主变35kV侧3031隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454493995011"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1627.22,338.222) scale(1.11111,0.814815) translate(-161.889,74.0909)" width="15" x="1618.891298801276" y="326"/></g>
  <g id="184">
   <use class="kv35" height="30" transform="rotate(0,1627.32,444.222) scale(1.11111,0.814815) translate(-161.898,98.1818)" width="15" x="1618.983951438215" xlink:href="#Disconnector:刀闸_0" y="432" zvalue="1726"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454494060547" ObjectName="#3主变35kV侧3036隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454494060547"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1627.32,444.222) scale(1.11111,0.814815) translate(-161.898,98.1818)" width="15" x="1618.983951438215" y="432"/></g>
  <g id="174">
   <use class="kv10" height="26" transform="rotate(0,1599,797) scale(1,1) translate(0,0)" width="12" x="1593" xlink:href="#Disconnector:单手车刀闸1212_0" y="784" zvalue="1740"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454494126083" ObjectName="0779"/>
   <cge:TPSR_Ref TObjectID="6192454494126083"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1599,797) scale(1,1) translate(0,0)" width="12" x="1593" y="784"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="6">
   <path class="kv110" d="M 434.44 299.67 L 1376 299.67" stroke-width="4" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674414493699" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674414493699"/></metadata>
  <path d="M 434.44 299.67 L 1376 299.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv35" d="M 1446 300.67 L 1820.44 300.67" stroke-width="4" zvalue="11"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674414559235" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674414559235"/></metadata>
  <path d="M 1446 300.67 L 1820.44 300.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv10" d="M 435.88 652.89 L 823 652.89" stroke-width="4" zvalue="15"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674414624771" ObjectName="10kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674414624771"/></metadata>
  <path d="M 435.88 652.89 L 823 652.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv10" d="M 842 652.89 L 1229.12 652.89" stroke-width="4" zvalue="1528"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674414690307" ObjectName="10kVⅡ母"/>
   <cge:TPSR_Ref TObjectID="9288674414690307"/></metadata>
  <path d="M 842 652.89 L 1229.12 652.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="343">
   <path class="kv10" d="M 1248 652.89 L 1635.12 652.89" stroke-width="4" zvalue="1632"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674414755843" ObjectName="10kVⅢ母"/>
   <cge:TPSR_Ref TObjectID="9288674414755843"/></metadata>
  <path d="M 1248 652.89 L 1635.12 652.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="19">
   <use class="kv110" height="20" transform="rotate(0,500,221.111) scale(1.22222,1.11111) translate(-89.798,-21)" width="10" x="493.8888888888889" xlink:href="#Breaker:开关_0" y="210" zvalue="16"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925177442307" ObjectName="110kV户南线171断路器"/>
   <cge:TPSR_Ref TObjectID="6473925177442307"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,500,221.111) scale(1.22222,1.11111) translate(-89.798,-21)" width="10" x="493.8888888888889" y="210"/></g>
  <g id="101">
   <use class="kv110" height="20" transform="rotate(0,681.333,394.222) scale(1.22222,1.11111) translate(-122.768,-38.3111)" width="10" x="675.2222324079938" xlink:href="#Breaker:开关_0" y="383.1111111111111" zvalue="102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925177507843" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925177507843"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,681.333,394.222) scale(1.22222,1.11111) translate(-122.768,-38.3111)" width="10" x="675.2222324079938" y="383.1111111111111"/></g>
  <g id="341">
   <use class="kv10" height="20" transform="rotate(0,529.03,701.111) scale(2,2) translate(-259.515,-340.556)" width="10" x="519.0295695852833" xlink:href="#Breaker:小车断路器_0" y="681.1111128065321" zvalue="612"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925177573379" ObjectName="#1发电机071断路器"/>
   <cge:TPSR_Ref TObjectID="6473925177573379"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,529.03,701.111) scale(2,2) translate(-259.515,-340.556)" width="10" x="519.0295695852833" y="681.1111128065321"/></g>
  <g id="764">
   <use class="kv35" height="20" transform="rotate(0,1464.5,217.167) scale(1.22222,1.11111) translate(-265.162,-20.6056)" width="10" x="1458.388888888889" xlink:href="#Breaker:开关_0" y="206.05554178026" zvalue="1148"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925177638915" ObjectName="35kV宏利硅厂线371断路器"/>
   <cge:TPSR_Ref TObjectID="6473925177638915"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1464.5,217.167) scale(1.22222,1.11111) translate(-265.162,-20.6056)" width="10" x="1458.388888888889" y="206.05554178026"/></g>
  <g id="152">
   <use class="kv10" height="20" transform="rotate(0,469,610.222) scale(2,2) translate(-229.5,-295.111)" width="10" x="459.0000000000002" xlink:href="#Breaker:小车断路器_0" y="590.2222256130642" zvalue="1305"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925177704451" ObjectName="生活区078断路器"/>
   <cge:TPSR_Ref TObjectID="6473925177704451"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,469,610.222) scale(2,2) translate(-229.5,-295.111)" width="10" x="459.0000000000002" y="590.2222256130642"/></g>
  <g id="168">
   <use class="kv10" height="20" transform="rotate(0,679.5,703.333) scale(2,2) translate(-334.75,-341.667)" width="10" x="669.5000000000002" xlink:href="#Breaker:小车断路器_0" y="683.3333384195963" zvalue="1319"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925177769987" ObjectName="#1厂用变072断路器"/>
   <cge:TPSR_Ref TObjectID="6473925177769987"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,679.5,703.333) scale(2,2) translate(-334.75,-341.667)" width="10" x="669.5000000000002" y="683.3333384195963"/></g>
  <g id="273">
   <use class="kv110" height="20" transform="rotate(0,673.5,220.167) scale(1.22222,1.11111) translate(-121.343,-20.9056)" width="10" x="667.3888888888889" xlink:href="#Breaker:开关_0" y="209.055555343628" zvalue="1436"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925177835523" ObjectName="110kV宋勐佳线172断路器"/>
   <cge:TPSR_Ref TObjectID="6473925177835523"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,673.5,220.167) scale(1.22222,1.11111) translate(-121.343,-20.9056)" width="10" x="667.3888888888889" y="209.055555343628"/></g>
  <g id="301">
   <use class="kv110" height="20" transform="rotate(0,960.5,219.167) scale(1.22222,1.11111) translate(-173.525,-20.8056)" width="10" x="954.3888888888889" xlink:href="#Breaker:开关_0" y="208.055555343628" zvalue="1462"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925177901059" ObjectName="110kV允宋线173断路器"/>
   <cge:TPSR_Ref TObjectID="6473925177901059"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,960.5,219.167) scale(1.22222,1.11111) translate(-173.525,-20.8056)" width="10" x="954.3888888888889" y="208.055555343628"/></g>
  <g id="323">
   <use class="kv110" height="20" transform="rotate(0,1136.5,218.167) scale(1.22222,1.11111) translate(-205.525,-20.7056)" width="10" x="1130.388888888889" xlink:href="#Breaker:开关_0" y="207.055555343628" zvalue="1486"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925177966595" ObjectName="110kV宋腊线174断路器"/>
   <cge:TPSR_Ref TObjectID="6473925177966595"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1136.5,218.167) scale(1.22222,1.11111) translate(-205.525,-20.7056)" width="10" x="1130.388888888889" y="207.055555343628"/></g>
  <g id="163">
   <use class="kv110" height="20" transform="rotate(0,1003.45,394.222) scale(1.22222,1.11111) translate(-181.334,-38.3111)" width="10" x="997.3392957630713" xlink:href="#Breaker:开关_0" y="383.1111111111111" zvalue="1531"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925178163203" ObjectName="#2主变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473925178163203"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1003.45,394.222) scale(1.22222,1.11111) translate(-181.334,-38.3111)" width="10" x="997.3392957630713" y="383.1111111111111"/></g>
  <g id="157">
   <use class="kv10" height="20" transform="rotate(0,935.147,701.111) scale(2,2) translate(-462.573,-340.556)" width="10" x="925.1466329403607" xlink:href="#Breaker:小车断路器_0" y="681.1111128065321" zvalue="1537"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925178097667" ObjectName="#2发电机073断路器"/>
   <cge:TPSR_Ref TObjectID="6473925178097667"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,935.147,701.111) scale(2,2) translate(-462.573,-340.556)" width="10" x="925.1466329403607" y="681.1111128065321"/></g>
  <g id="141">
   <use class="kv10" height="20" transform="rotate(0,1085.62,703.333) scale(2,2) translate(-537.809,-341.667)" width="10" x="1075.617063355078" xlink:href="#Breaker:小车断路器_0" y="683.3333384195963" zvalue="1555"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925178032131" ObjectName="#2厂用变074断路器"/>
   <cge:TPSR_Ref TObjectID="6473925178032131"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1085.62,703.333) scale(2,2) translate(-537.809,-341.667)" width="10" x="1075.617063355078" y="683.3333384195963"/></g>
  <g id="340">
   <use class="kv110" height="20" transform="rotate(0,1365.45,394.222) scale(1.22222,1.11111) translate(-247.153,-38.3111)" width="10" x="1359.339295763071" xlink:href="#Breaker:开关_0" y="383.1111111111111" zvalue="1635"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925178359811" ObjectName="#3主变110kV侧103断路器"/>
   <cge:TPSR_Ref TObjectID="6473925178359811"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1365.45,394.222) scale(1.22222,1.11111) translate(-247.153,-38.3111)" width="10" x="1359.339295763071" y="383.1111111111111"/></g>
  <g id="337">
   <use class="kv10" height="20" transform="rotate(0,1341.15,701.111) scale(2,2) translate(-665.573,-340.556)" width="10" x="1331.146632940361" xlink:href="#Breaker:小车断路器_0" y="681.1111128065321" zvalue="1641"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925178294275" ObjectName="#3发电机075断路器"/>
   <cge:TPSR_Ref TObjectID="6473925178294275"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1341.15,701.111) scale(2,2) translate(-665.573,-340.556)" width="10" x="1331.146632940361" y="681.1111128065321"/></g>
  <g id="308">
   <use class="kv10" height="20" transform="rotate(0,1491.62,703.333) scale(2,2) translate(-740.809,-341.667)" width="10" x="1481.617063355078" xlink:href="#Breaker:小车断路器_0" y="683.3333384195963" zvalue="1657"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925178228739" ObjectName="#3厂用变076断路器"/>
   <cge:TPSR_Ref TObjectID="6473925178228739"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1491.62,703.333) scale(2,2) translate(-740.809,-341.667)" width="10" x="1481.617063355078" y="683.3333384195963"/></g>
  <g id="170">
   <use class="kv35" height="20" transform="rotate(0,1624.5,218.167) scale(1.22222,1.11111) translate(-294.253,-20.7056)" width="10" x="1618.388888888889" xlink:href="#Breaker:开关_0" y="207.05554178026" zvalue="1694"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925178425347" ObjectName="35kV金源硅厂线372断路器"/>
   <cge:TPSR_Ref TObjectID="6473925178425347"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1624.5,218.167) scale(1.22222,1.11111) translate(-294.253,-20.7056)" width="10" x="1618.388888888889" y="207.05554178026"/></g>
  <g id="182">
   <use class="kv35" height="20" transform="rotate(0,1627.33,394.222) scale(1.22222,1.11111) translate(-294.768,-38.3111)" width="10" x="1621.222222235468" xlink:href="#Breaker:开关_0" y="383.1111111111111" zvalue="1720"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925178490883" ObjectName="#3主变35kV侧303断路器"/>
   <cge:TPSR_Ref TObjectID="6473925178490883"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1627.33,394.222) scale(1.22222,1.11111) translate(-294.768,-38.3111)" width="10" x="1621.222222235468" y="383.1111111111111"/></g>
  <g id="205">
   <use class="kv10" height="20" transform="rotate(0,1598,702) scale(2,2) translate(-794,-341)" width="10" x="1588" xlink:href="#Breaker:小车断路器_0" y="682" zvalue="1737"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925178556419" ObjectName="坝区变077断路器"/>
   <cge:TPSR_Ref TObjectID="6473925178556419"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1598,702) scale(2,2) translate(-794,-341)" width="10" x="1588" y="682"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="23">
   <path class="kv110" d="M 499.9 280.48 L 499.9 299.67" stroke-width="1" zvalue="20"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@0" LinkObjectIDznd="6@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 499.9 280.48 L 499.9 299.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv110" d="M 499.93 256.65 L 499.93 231.72" stroke-width="1" zvalue="21"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@1" LinkObjectIDznd="19@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 499.93 256.65 L 499.93 231.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv110" d="M 499.96 210.48 L 499.9 172.48" stroke-width="1" zvalue="22"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="19@0" LinkObjectIDznd="22@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 499.96 210.48 L 499.9 172.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv110" d="M 499.93 148.65 L 499.93 127.44" stroke-width="1" zvalue="25"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@1" LinkObjectIDznd="27@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 499.93 148.65 L 499.93 127.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv110" d="M 815.12 268.48 L 815.12 299.67" stroke-width="1" zvalue="98"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="6@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 815.12 268.48 L 815.12 299.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv110" d="M 815.15 244.65 L 815.15 217.37" stroke-width="1" zvalue="99"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@1" LinkObjectIDznd="68@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 815.15 244.65 L 815.15 217.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv110" d="M 681.4 350.24 L 681.4 383.59" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@1" LinkObjectIDznd="101@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 681.4 350.24 L 681.4 383.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="296">
   <path class="kv110" d="M 681.43 326.4 L 681.43 299.67" stroke-width="1" zvalue="610"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="6@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 681.43 326.4 L 681.43 299.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="334">
   <path class="kv10" d="M 529.03 682.61 L 529.03 652.89" stroke-width="1" zvalue="623"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@0" LinkObjectIDznd="15@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 529.03 682.61 L 529.03 652.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="761">
   <path class="kv35" d="M 1464.4 276.54 L 1464.4 300.67" stroke-width="1" zvalue="1153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="763@0" LinkObjectIDznd="10@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1464.4 276.54 L 1464.4 300.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="760">
   <path class="kv35" d="M 1464.43 252.71 L 1464.43 227.78" stroke-width="1" zvalue="1155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="763@1" LinkObjectIDznd="764@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1464.43 252.71 L 1464.43 227.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="759">
   <path class="kv35" d="M 1464.46 206.54 L 1464.4 176.54" stroke-width="1" zvalue="1156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="764@0" LinkObjectIDznd="762@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1464.46 206.54 L 1464.4 176.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="734">
   <path class="kv35" d="M 1464.43 152.71 L 1464.43 115.88" stroke-width="1" zvalue="1168"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="762@1" LinkObjectIDznd="758@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1464.43 152.71 L 1464.43 115.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="846">
   <path class="kv35" d="M 1770.21 274.98 L 1770.21 300.67" stroke-width="1" zvalue="1250"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="848@0" LinkObjectIDznd="10@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1770.21 274.98 L 1770.21 300.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="849">
   <path class="kv10" d="M 529 861.88 L 529.03 719.11" stroke-width="1" zvalue="1259"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="341@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 529 861.88 L 529.03 719.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv10" d="M 467 835.08 L 467 826 L 529.01 826" stroke-width="1" zvalue="1264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@1" LinkObjectIDznd="849" MaxPinNum="2"/>
   </metadata>
  <path d="M 467 835.08 L 467 826 L 529.01 826" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv10" d="M 467.08 860.97 L 466.95 873.1" stroke-width="1" zvalue="1265"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="54@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 467.08 860.97 L 466.95 873.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv10" d="M 529.01 827 L 605 827 L 605 835.08" stroke-width="1" zvalue="1269"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="849" LinkObjectIDznd="18@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 529.01 827 L 605 827 L 605 835.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv10" d="M 605.08 860.97 L 605 874.5" stroke-width="1" zvalue="1270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@0" LinkObjectIDznd="72@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 605.08 860.97 L 605 874.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="165">
   <path class="kv10" d="M 679.5 684.83 L 679.5 652.89" stroke-width="1" zvalue="1324"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@0" LinkObjectIDznd="15@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 679.5 684.83 L 679.5 652.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv10" d="M 679.5 740.79 L 679.5 721.33" stroke-width="1" zvalue="1329"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="168@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 679.5 740.79 L 679.5 721.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv110" d="M 481.5 133.65 L 499.93 133.65" stroke-width="1" zvalue="1431"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 481.5 133.65 L 499.93 133.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv110" d="M 536.78 137.39 L 499.93 137.39" stroke-width="1" zvalue="1432"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 536.78 137.39 L 499.93 137.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv110" d="M 536.78 191.83 L 499.93 191.83" stroke-width="1" zvalue="1433"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="8@0" LinkObjectIDznd="25" MaxPinNum="2"/>
   </metadata>
  <path d="M 536.78 191.83 L 499.93 191.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv110" d="M 536.78 244.28 L 499.93 244.28" stroke-width="1" zvalue="1434"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="24" MaxPinNum="2"/>
   </metadata>
  <path d="M 536.78 244.28 L 499.93 244.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="kv110" d="M 673.4 279.54 L 673.4 299.67" stroke-width="1" zvalue="1441"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="6@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 673.4 279.54 L 673.4 299.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv110" d="M 673.43 255.71 L 673.43 230.78" stroke-width="1" zvalue="1443"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@1" LinkObjectIDznd="273@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 673.43 255.71 L 673.43 230.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="268">
   <path class="kv110" d="M 673.46 209.54 L 673.4 171.54" stroke-width="1" zvalue="1444"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="273@0" LinkObjectIDznd="271@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 673.46 209.54 L 673.4 171.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="kv110" d="M 673.43 147.71 L 673.43 127.28" stroke-width="1" zvalue="1447"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@1" LinkObjectIDznd="267@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 673.43 147.71 L 673.43 127.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="261">
   <path class="kv110" d="M 655 132.71 L 673.43 132.71" stroke-width="1" zvalue="1455"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="262@0" LinkObjectIDznd="266" MaxPinNum="2"/>
   </metadata>
  <path d="M 655 132.71 L 673.43 132.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="260">
   <path class="kv110" d="M 710.28 136.44 L 673.43 136.44" stroke-width="1" zvalue="1456"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@0" LinkObjectIDznd="266" MaxPinNum="2"/>
   </metadata>
  <path d="M 710.28 136.44 L 673.43 136.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="259">
   <path class="kv110" d="M 710.28 190.89 L 673.43 190.89" stroke-width="1" zvalue="1457"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="264@0" LinkObjectIDznd="268" MaxPinNum="2"/>
   </metadata>
  <path d="M 710.28 190.89 L 673.43 190.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="258">
   <path class="kv110" d="M 710.28 239.67 L 673.43 239.67" stroke-width="1" zvalue="1458"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265@0" LinkObjectIDznd="269" MaxPinNum="2"/>
   </metadata>
  <path d="M 710.28 239.67 L 673.43 239.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="274">
   <path class="kv110" d="M 858.5 226.78 L 815.15 226.78" stroke-width="1" zvalue="1459"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="97" MaxPinNum="2"/>
   </metadata>
  <path d="M 858.5 226.78 L 815.15 226.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="275">
   <path class="kv110" d="M 858.5 270.61 L 815.12 270.61" stroke-width="1" zvalue="1460"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 858.5 270.61 L 815.12 270.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="298">
   <path class="kv110" d="M 960.4 278.54 L 960.4 299.67" stroke-width="1" zvalue="1467"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="300@0" LinkObjectIDznd="6@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.4 278.54 L 960.4 299.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="297">
   <path class="kv110" d="M 960.43 254.71 L 960.43 229.78" stroke-width="1" zvalue="1469"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="300@1" LinkObjectIDznd="301@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.43 254.71 L 960.43 229.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="294">
   <path class="kv110" d="M 960.46 208.54 L 960.4 170.54" stroke-width="1" zvalue="1470"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="301@0" LinkObjectIDznd="299@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.46 208.54 L 960.4 170.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="292">
   <path class="kv110" d="M 960.43 146.71 L 960.43 126.28" stroke-width="1" zvalue="1473"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="299@1" LinkObjectIDznd="293@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.43 146.71 L 960.43 126.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="287">
   <path class="kv110" d="M 942 131.71 L 960.43 131.71" stroke-width="1" zvalue="1481"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="288@0" LinkObjectIDznd="292" MaxPinNum="2"/>
   </metadata>
  <path d="M 942 131.71 L 960.43 131.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="286">
   <path class="kv110" d="M 997.28 135.44 L 960.43 135.44" stroke-width="1" zvalue="1482"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="289@0" LinkObjectIDznd="292" MaxPinNum="2"/>
   </metadata>
  <path d="M 997.28 135.44 L 960.43 135.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="285">
   <path class="kv110" d="M 997.28 187.67 L 960.43 187.67" stroke-width="1" zvalue="1483"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="290@0" LinkObjectIDznd="294" MaxPinNum="2"/>
   </metadata>
  <path d="M 997.28 187.67 L 960.43 187.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="320">
   <path class="kv110" d="M 1136.4 277.54 L 1136.4 299.67" stroke-width="1" zvalue="1491"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="322@0" LinkObjectIDznd="6@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1136.4 277.54 L 1136.4 299.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="319">
   <path class="kv110" d="M 1136.43 253.71 L 1136.43 228.78" stroke-width="1" zvalue="1493"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="322@1" LinkObjectIDznd="323@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1136.43 253.71 L 1136.43 228.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="318">
   <path class="kv110" d="M 1136.46 207.54 L 1136.4 169.54" stroke-width="1" zvalue="1494"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="323@0" LinkObjectIDznd="321@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1136.46 207.54 L 1136.4 169.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="316">
   <path class="kv110" d="M 1136.43 145.71 L 1136.43 125.28" stroke-width="1" zvalue="1497"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="321@1" LinkObjectIDznd="317@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1136.43 145.71 L 1136.43 125.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="312">
   <path class="kv110" d="M 1118 130.71 L 1136.43 130.71" stroke-width="1" zvalue="1503"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="313@0" LinkObjectIDznd="316" MaxPinNum="2"/>
   </metadata>
  <path d="M 1118 130.71 L 1136.43 130.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv110" d="M 1173.28 134.44 L 1136.43 134.44" stroke-width="1" zvalue="1504"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="314@0" LinkObjectIDznd="316" MaxPinNum="2"/>
   </metadata>
  <path d="M 1173.28 134.44 L 1136.43 134.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="310">
   <path class="kv110" d="M 1173.28 183.67 L 1136.42 183.67" stroke-width="1" zvalue="1505"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="315@0" LinkObjectIDznd="318" MaxPinNum="2"/>
   </metadata>
  <path d="M 1173.28 183.67 L 1136.42 183.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="324">
   <path class="kv110" d="M 681.41 404.83 L 681.41 474.84" stroke-width="1" zvalue="1506"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@1" LinkObjectIDznd="73@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 681.41 404.83 L 681.41 474.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="325">
   <path class="kv110" d="M 713.61 358.06 L 681.4 358.06" stroke-width="1" zvalue="1507"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="108" MaxPinNum="2"/>
   </metadata>
  <path d="M 713.61 358.06 L 681.4 358.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv10" d="M 679.5 566.02 L 679.5 652.89" stroke-width="1" zvalue="1508"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@1" LinkObjectIDznd="15@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 679.5 566.02 L 679.5 652.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv110" d="M 679.54 501.12 L 538.07 501.12 L 538.07 515.85" stroke-width="1" zvalue="1511"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@2" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 679.54 501.12 L 538.07 501.12 L 538.07 515.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv10" d="M 469 628.22 L 469 652.89" stroke-width="1" zvalue="1512"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@1" LinkObjectIDznd="15@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 469 628.22 L 469 652.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv10" d="M 468.5 558.89 L 468.5 591.72" stroke-width="1" zvalue="1514"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@0" LinkObjectIDznd="152@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 468.5 558.89 L 468.5 591.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv10" d="M 579 881.75 L 579 827" stroke-width="1" zvalue="1519"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75@0" LinkObjectIDznd="43" MaxPinNum="2"/>
   </metadata>
  <path d="M 579 881.75 L 579 827" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv10" d="M 761.99 697.3 L 761.99 719.76" stroke-width="1" zvalue="1526"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="175@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 761.99 697.3 L 761.99 719.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv110" d="M 1003.52 350.24 L 1003.52 383.59" stroke-width="1" zvalue="1534"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@1" LinkObjectIDznd="163@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1003.52 350.24 L 1003.52 383.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv10" d="M 935.15 682.61 L 935.15 652.89" stroke-width="1" zvalue="1539"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="166@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 935.15 682.61 L 935.15 652.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv10" d="M 935.12 861.88 L 935.15 719.11" stroke-width="1" zvalue="1542"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@0" LinkObjectIDznd="157@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 935.12 861.88 L 935.15 719.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv10" d="M 873.12 835.08 L 873.12 826 L 935.12 826" stroke-width="1" zvalue="1547"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@1" LinkObjectIDznd="153" MaxPinNum="2"/>
   </metadata>
  <path d="M 873.12 835.08 L 873.12 826 L 935.12 826" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="kv10" d="M 873.2 860.97 L 873.07 873.1" stroke-width="1" zvalue="1548"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="124@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 873.2 860.97 L 873.07 873.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv10" d="M 935.12 827 L 1011.12 827 L 1011.12 835.08" stroke-width="1" zvalue="1551"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153" LinkObjectIDznd="147@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 935.12 827 L 1011.12 827 L 1011.12 835.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv10" d="M 1011.2 860.97 L 1011.12 874.5" stroke-width="1" zvalue="1552"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="147@0" LinkObjectIDznd="123@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1011.2 860.97 L 1011.12 874.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="kv10" d="M 1085.62 684.83 L 1085.62 652.89" stroke-width="1" zvalue="1557"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@0" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1085.62 684.83 L 1085.62 652.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv10" d="M 1085.62 740.79 L 1085.62 721.33" stroke-width="1" zvalue="1558"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="141@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1085.62 740.79 L 1085.62 721.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv110" d="M 1003.53 404.83 L 1003.53 474.84" stroke-width="1" zvalue="1561"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@1" LinkObjectIDznd="154@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1003.53 404.83 L 1003.53 474.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="kv110" d="M 1035.73 358.06 L 1003.52 358.06" stroke-width="1" zvalue="1562"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="162" MaxPinNum="2"/>
   </metadata>
  <path d="M 1035.73 358.06 L 1003.52 358.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="kv10" d="M 1001.62 566.02 L 1001.62 652.89" stroke-width="1" zvalue="1563"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@1" LinkObjectIDznd="166@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1001.62 566.02 L 1001.62 652.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv110" d="M 1001.65 501.12 L 860.19 501.12 L 860.19 515.85" stroke-width="1" zvalue="1565"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@2" LinkObjectIDznd="130@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1001.65 501.12 L 860.19 501.12 L 860.19 515.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv10" d="M 985.12 881.75 L 985.12 827" stroke-width="1" zvalue="1576"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@0" LinkObjectIDznd="146" MaxPinNum="2"/>
   </metadata>
  <path d="M 985.12 881.75 L 985.12 827" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv10" d="M 1188.12 665 L 1188.12 652.89" stroke-width="1" zvalue="1580"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@1" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1188.12 665 L 1188.12 652.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv10" d="M 1188.11 697.3 L 1188.11 719.76" stroke-width="1" zvalue="1581"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="134@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1188.11 697.3 L 1188.11 719.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv110" d="M 1003.55 326.4 L 1003.55 299.67" stroke-width="1" zvalue="1582"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@0" LinkObjectIDznd="6@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1003.55 326.4 L 1003.55 299.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="339">
   <path class="kv110" d="M 1365.52 350.24 L 1365.52 383.59" stroke-width="1" zvalue="1638"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="342@1" LinkObjectIDznd="340@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1365.52 350.24 L 1365.52 383.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="336">
   <path class="kv10" d="M 1341.15 682.61 L 1341.15 652.89" stroke-width="1" zvalue="1643"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="337@0" LinkObjectIDznd="343@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1341.15 682.61 L 1341.15 652.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="333">
   <path class="kv10" d="M 1341.12 861.88 L 1341.15 719.11" stroke-width="1" zvalue="1646"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="332@0" LinkObjectIDznd="337@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1341.12 861.88 L 1341.15 719.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="330">
   <path class="kv10" d="M 1279.12 835.08 L 1279.12 826 L 1341.12 826" stroke-width="1" zvalue="1651"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="331@1" LinkObjectIDznd="333" MaxPinNum="2"/>
   </metadata>
  <path d="M 1279.12 835.08 L 1279.12 826 L 1341.12 826" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="329">
   <path class="kv10" d="M 1279.2 860.97 L 1279.07 873.1" stroke-width="1" zvalue="1652"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="331@0" LinkObjectIDznd="221@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1279.2 860.97 L 1279.07 873.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="327">
   <path class="kv10" d="M 1341.12 827 L 1417.12 827 L 1417.12 835.08" stroke-width="1" zvalue="1655"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="333" LinkObjectIDznd="328@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1341.12 827 L 1417.12 827 L 1417.12 835.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="326">
   <path class="kv10" d="M 1417.2 860.97 L 1417.12 874.5" stroke-width="1" zvalue="1656"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="328@0" LinkObjectIDznd="219@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1417.2 860.97 L 1417.12 874.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="295">
   <path class="kv10" d="M 1491.62 684.83 L 1491.62 652.89" stroke-width="1" zvalue="1659"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="308@0" LinkObjectIDznd="343@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1491.62 684.83 L 1491.62 652.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="291">
   <path class="kv10" d="M 1491.62 740.79 L 1491.62 721.33" stroke-width="1" zvalue="1660"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="228@0" LinkObjectIDznd="308@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1491.62 740.79 L 1491.62 721.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="281">
   <path class="kv110" d="M 1365.53 404.83 L 1365.53 475.45" stroke-width="1" zvalue="1663"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="340@1" LinkObjectIDznd="347@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1365.53 404.83 L 1365.53 475.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="kv110" d="M 1397.73 358.06 L 1365.52 358.06" stroke-width="1" zvalue="1664"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="338@0" LinkObjectIDznd="339" MaxPinNum="2"/>
   </metadata>
  <path d="M 1397.73 358.06 L 1365.52 358.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="229">
   <path class="kv110" d="M 1365.15 500.5 L 1218.94 500.5 L 1218.94 518.33" stroke-width="1" zvalue="1667"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="347@1" LinkObjectIDznd="230@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1365.15 500.5 L 1218.94 500.5 L 1218.94 518.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv10" d="M 1391.12 881.75 L 1391.12 827" stroke-width="1" zvalue="1674"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217@0" LinkObjectIDznd="327" MaxPinNum="2"/>
   </metadata>
  <path d="M 1391.12 881.75 L 1391.12 827" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="349">
   <path class="kv110" d="M 1365.55 326.4 L 1365.55 299.67" stroke-width="1" zvalue="1683"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="342@0" LinkObjectIDznd="6@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1365.55 326.4 L 1365.55 299.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv35" d="M 1489.28 129.12 L 1464.43 129.12" stroke-width="1" zvalue="1688"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="755@0" LinkObjectIDznd="734" MaxPinNum="2"/>
   </metadata>
  <path d="M 1489.28 129.12 L 1464.43 129.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv35" d="M 1466 129.12 L 1429.5 129.12 L 1429.5 117.33" stroke-width="1" zvalue="1690"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="4" LinkObjectIDznd="17@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1466 129.12 L 1429.5 129.12 L 1429.5 117.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv35" d="M 1430 144 L 1430 129.12" stroke-width="1" zvalue="1692"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@0" LinkObjectIDznd="77" MaxPinNum="2"/>
   </metadata>
  <path d="M 1430 144 L 1430 129.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv35" d="M 1624.4 277.54 L 1624.4 300.67" stroke-width="1" zvalue="1699"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@0" LinkObjectIDznd="10@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1624.4 277.54 L 1624.4 300.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv35" d="M 1624.43 253.71 L 1624.43 228.78" stroke-width="1" zvalue="1701"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@1" LinkObjectIDznd="170@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1624.43 253.71 L 1624.43 228.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv35" d="M 1624.46 207.54 L 1624.4 177.54" stroke-width="1" zvalue="1702"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="142@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1624.46 207.54 L 1624.4 177.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv35" d="M 1624.43 153.71 L 1624.43 116.88" stroke-width="1" zvalue="1707"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="142@1" LinkObjectIDznd="128@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1624.43 153.71 L 1624.43 116.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv35" d="M 1649.28 130.12 L 1624.43 130.12" stroke-width="1" zvalue="1708"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@0" LinkObjectIDznd="126" MaxPinNum="2"/>
   </metadata>
  <path d="M 1649.28 130.12 L 1624.43 130.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv35" d="M 1626 130.12 L 1589.5 130.12 L 1589.5 118.33" stroke-width="1" zvalue="1710"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104" LinkObjectIDznd="95@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1626 130.12 L 1589.5 130.12 L 1589.5 118.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv35" d="M 1590 145 L 1590 130.12" stroke-width="1" zvalue="1712"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@0" LinkObjectIDznd="92" MaxPinNum="2"/>
   </metadata>
  <path d="M 1590 145 L 1590 130.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="181">
   <path class="kv35" d="M 1627.29 350.24 L 1627.29 383.59" stroke-width="1" zvalue="1723"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@1" LinkObjectIDznd="182@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1627.29 350.24 L 1627.29 383.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv35" d="M 1627.32 326.4 L 1627.32 300.67" stroke-width="1" zvalue="1727"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="183@0" LinkObjectIDznd="10@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1627.32 326.4 L 1627.32 300.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv35" d="M 1627.41 404.83 L 1627.41 432.4" stroke-width="1" zvalue="1728"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@1" LinkObjectIDznd="184@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1627.41 404.83 L 1627.41 432.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="kv35" d="M 1755 215 L 1755 222 L 1786.06 222 L 1786.06 218.29" stroke-width="1" zvalue="1731"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="173@0" LinkObjectIDznd="171@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1755 215 L 1755 222 L 1786.06 222 L 1786.06 218.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="kv35" d="M 1770.24 251.15 L 1770.24 222" stroke-width="1" zvalue="1733"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="848@1" LinkObjectIDznd="190" MaxPinNum="2"/>
   </metadata>
  <path d="M 1770.24 251.15 L 1770.24 222" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv10" d="M 1594.12 643 L 1594.12 652.89" stroke-width="1" zvalue="1734"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="213@1" LinkObjectIDznd="343@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1594.12 643 L 1594.12 652.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv10" d="M 1594.11 610.7 L 1594.11 604.24" stroke-width="1" zvalue="1735"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="213@0" LinkObjectIDznd="284@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1594.11 610.7 L 1594.11 604.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="249">
   <path class="kv10" d="M 1598 683.5 L 1598 652.89" stroke-width="1" zvalue="1739"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="343@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1598 683.5 L 1598 652.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv10" d="M 1599.08 824.76 L 1599.08 809.97" stroke-width="1" zvalue="1743"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@0" LinkObjectIDznd="174@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1599.08 824.76 L 1599.08 809.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="kv10" d="M 1599 784.08 L 1599 720" stroke-width="1" zvalue="1744"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@1" LinkObjectIDznd="205@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1599 784.08 L 1599 720" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="335">
   <path class="kv10" d="M 1599 727.88 L 1599 727.88" stroke-width="1" zvalue="1746"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@0" LinkObjectIDznd="191" MaxPinNum="2"/>
   </metadata>
  <path d="M 1599 727.88 L 1599 727.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="357">
   <path class="kv10" d="M 1784.5 777 L 1599 777" stroke-width="1" zvalue="1751"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="346@0" LinkObjectIDznd="191" MaxPinNum="2"/>
   </metadata>
  <path d="M 1784.5 777 L 1599 777" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="376">
   <path class="kv10" d="M 1680.03 807.63 L 1680.03 778" stroke-width="1" zvalue="1769"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="375@0" LinkObjectIDznd="357" MaxPinNum="2"/>
   </metadata>
  <path d="M 1680.03 807.63 L 1680.03 778" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="389">
   <path class="kv10" d="M 762 665 L 762 652.89" stroke-width="1" zvalue="1784"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@1" LinkObjectIDznd="15@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 762 665 L 762 652.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv35" d="M 1427.1 520.15 L 1627.39 520.15 L 1627.39 456.24" stroke-width="1" zvalue="1785"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="347@2" LinkObjectIDznd="184@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1427.1 520.15 L 1627.39 520.15 L 1627.39 456.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="383">
   <path class="kv10" d="M 1365.43 586.4 L 1365.43 564.85" stroke-width="1" zvalue="1786"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="350@0" LinkObjectIDznd="347@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1365.43 586.4 L 1365.43 564.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv10" d="M 1365.4 610.24 L 1365.4 652.89" stroke-width="1" zvalue="1787"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="350@1" LinkObjectIDznd="343@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1365.4 610.24 L 1365.4 652.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="1">
   <use class="kv110" height="20" transform="rotate(270,547.611,244.222) scale(-1.11111,1.11111) translate(-1039.91,-23.3111)" width="10" x="542.0555555555555" xlink:href="#GroundDisconnector:地刀_0" y="233.1111111111111" zvalue="26"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454487506947" ObjectName="110kV户南线17117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454487506947"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,547.611,244.222) scale(-1.11111,1.11111) translate(-1039.91,-23.3111)" width="10" x="542.0555555555555" y="233.1111111111111"/></g>
  <g id="8">
   <use class="kv110" height="20" transform="rotate(270,547.611,191.778) scale(-1.11111,1.11111) translate(-1039.91,-18.0667)" width="10" x="542.0555555555557" xlink:href="#GroundDisconnector:地刀_0" y="180.6666666666667" zvalue="28"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454487638019" ObjectName="110kV户南线17160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454487638019"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,547.611,191.778) scale(-1.11111,1.11111) translate(-1039.91,-18.0667)" width="10" x="542.0555555555557" y="180.6666666666667"/></g>
  <g id="32">
   <use class="kv110" height="20" transform="rotate(270,547.611,137.333) scale(-1.11111,1.11111) translate(-1039.91,-12.6222)" width="10" x="542.0555556615193" xlink:href="#GroundDisconnector:地刀_0" y="126.2222222222221" zvalue="31"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454487769091" ObjectName="110kV户南线17167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454487769091"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,547.611,137.333) scale(-1.11111,1.11111) translate(-1039.91,-12.6222)" width="10" x="542.0555556615193" y="126.2222222222221"/></g>
  <g id="91">
   <use class="kv110" height="20" transform="rotate(270,869.333,226.722) scale(-1.11111,1.11111) translate(-1651.18,-21.5611)" width="10" x="863.7777845594618" xlink:href="#GroundDisconnector:地刀_0" y="215.6111008326212" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454487965699" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454487965699"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,869.333,226.722) scale(-1.11111,1.11111) translate(-1651.18,-21.5611)" width="10" x="863.7777845594618" y="215.6111008326212"/></g>
  <g id="114">
   <use class="kv110" height="20" transform="rotate(270,724.444,358) scale(-1.11111,1.11111) translate(-1375.89,-34.6889)" width="10" x="718.8888990614149" xlink:href="#GroundDisconnector:地刀_0" y="346.8888888888889" zvalue="114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454488162307" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454488162307"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,724.444,358) scale(-1.11111,1.11111) translate(-1375.89,-34.6889)" width="10" x="718.8888990614149" y="346.8888888888889"/></g>
  <g id="70">
   <use class="kv110" height="20" transform="rotate(270,869.333,270.556) scale(-1.11111,1.11111) translate(-1651.18,-25.9444)" width="10" x="863.7777845594618" xlink:href="#GroundDisconnector:地刀_0" y="259.4444444444444" zvalue="1095"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454488358915" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454488358915"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,869.333,270.556) scale(-1.11111,1.11111) translate(-1651.18,-25.9444)" width="10" x="863.7777845594618" y="259.4444444444444"/></g>
  <g id="755">
   <use class="kv35" height="20" transform="rotate(270,1500.11,129.069) scale(-1.11111,1.11111) translate(-2849.66,-11.7958)" width="10" x="1494.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="117.9580254990436" zvalue="1163"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454488489987" ObjectName="35kV宏利硅厂线37167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454488489987"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1500.11,129.069) scale(-1.11111,1.11111) translate(-2849.66,-11.7958)" width="10" x="1494.555555555556" y="117.9580254990436"/></g>
  <g id="265">
   <use class="kv110" height="20" transform="rotate(270,721.111,239.611) scale(-1.11111,1.11111) translate(-1369.56,-22.85)" width="10" x="715.5555555555555" xlink:href="#GroundDisconnector:地刀_0" y="228.500005086263" zvalue="1448"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454489538563" ObjectName="110kV宋勐佳线17217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454489538563"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,721.111,239.611) scale(-1.11111,1.11111) translate(-1369.56,-22.85)" width="10" x="715.5555555555555" y="228.500005086263"/></g>
  <g id="264">
   <use class="kv110" height="20" transform="rotate(270,721.111,190.833) scale(-1.11111,1.11111) translate(-1369.56,-17.9722)" width="10" x="715.5555555555557" xlink:href="#GroundDisconnector:地刀_0" y="179.7222220102947" zvalue="1450"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454489407491" ObjectName="110kV宋勐佳线17260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454489407491"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,721.111,190.833) scale(-1.11111,1.11111) translate(-1369.56,-17.9722)" width="10" x="715.5555555555557" y="179.7222220102947"/></g>
  <g id="263">
   <use class="kv110" height="20" transform="rotate(270,721.111,136.389) scale(-1.11111,1.11111) translate(-1369.56,-12.5278)" width="10" x="715.5555556615193" xlink:href="#GroundDisconnector:地刀_0" y="125.2777775658501" zvalue="1452"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454489276419" ObjectName="110kV宋勐佳线17267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454489276419"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,721.111,136.389) scale(-1.11111,1.11111) translate(-1369.56,-12.5278)" width="10" x="715.5555556615193" y="125.2777775658501"/></g>
  <g id="290">
   <use class="kv110" height="20" transform="rotate(270,1008.11,187.611) scale(-1.11111,1.11111) translate(-1914.86,-17.65)" width="10" x="1002.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="176.500005086263" zvalue="1476"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454490062851" ObjectName="110kV允宋线17360接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454490062851"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1008.11,187.611) scale(-1.11111,1.11111) translate(-1914.86,-17.65)" width="10" x="1002.555555555556" y="176.500005086263"/></g>
  <g id="289">
   <use class="kv110" height="20" transform="rotate(270,1008.11,135.389) scale(-1.11111,1.11111) translate(-1914.86,-12.4278)" width="10" x="1002.555555661519" xlink:href="#GroundDisconnector:地刀_0" y="124.2777775658501" zvalue="1478"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454489931779" ObjectName="110kV允宋线17367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454489931779"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1008.11,135.389) scale(-1.11111,1.11111) translate(-1914.86,-12.4278)" width="10" x="1002.555555661519" y="124.2777775658501"/></g>
  <g id="315">
   <use class="kv110" height="20" transform="rotate(270,1184.11,183.611) scale(-1.11111,1.11111) translate(-2249.26,-17.25)" width="10" x="1178.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="172.500005086263" zvalue="1498"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454490587139" ObjectName="110kV宋腊线17460接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454490587139"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1184.11,183.611) scale(-1.11111,1.11111) translate(-2249.26,-17.25)" width="10" x="1178.555555555556" y="172.500005086263"/></g>
  <g id="314">
   <use class="kv110" height="20" transform="rotate(270,1184.11,134.389) scale(-1.11111,1.11111) translate(-2249.26,-12.3278)" width="10" x="1178.555555661519" xlink:href="#GroundDisconnector:地刀_0" y="123.2777775658501" zvalue="1500"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454490456067" ObjectName="110kV宋腊线17467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454490456067"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1184.11,134.389) scale(-1.11111,1.11111) translate(-2249.26,-12.3278)" width="10" x="1178.555555661519" y="123.2777775658501"/></g>
  <g id="36">
   <use class="kv110" height="25" transform="rotate(0,545,533) scale(1.4,1.4) translate(-151.714,-147.286)" width="20" x="531" xlink:href="#GroundDisconnector:中性点地刀11_0" y="515.5" zvalue="1510"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454490914819" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454490914819"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,545,533) scale(1.4,1.4) translate(-151.714,-147.286)" width="20" x="531" y="515.5"/></g>
  <g id="161">
   <use class="kv110" height="20" transform="rotate(270,1046.56,358) scale(-1.11111,1.11111) translate(-1987.91,-34.6889)" width="10" x="1041.005962416492" xlink:href="#GroundDisconnector:地刀_0" y="346.8888888888889" zvalue="1535"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454492160003" ObjectName="#2主变110kV侧10217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454492160003"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1046.56,358) scale(-1.11111,1.11111) translate(-1987.91,-34.6889)" width="10" x="1041.005962416492" y="346.8888888888889"/></g>
  <g id="130">
   <use class="kv110" height="25" transform="rotate(0,867.117,533) scale(1.4,1.4) translate(-243.748,-147.286)" width="20" x="853.1170633550774" xlink:href="#GroundDisconnector:中性点地刀11_0" y="515.5" zvalue="1564"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454491766787" ObjectName="#2主变110kV侧1020中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454491766787"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,867.117,533) scale(1.4,1.4) translate(-243.748,-147.286)" width="20" x="853.1170633550774" y="515.5"/></g>
  <g id="338">
   <use class="kv110" height="20" transform="rotate(270,1408.56,358) scale(-1.11111,1.11111) translate(-2675.71,-34.6889)" width="10" x="1403.005962416492" xlink:href="#GroundDisconnector:地刀_0" y="346.8888888888889" zvalue="1639"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454493077507" ObjectName="#3主变110kV侧10317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454493077507"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1408.56,358) scale(-1.11111,1.11111) translate(-2675.71,-34.6889)" width="10" x="1403.005962416492" y="346.8888888888889"/></g>
  <g id="230">
   <use class="kv110" height="40" transform="rotate(0,1217.12,529) scale(0.7,-0.875) translate(515.622,-1136.07)" width="40" x="1203.117063355077" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="511.5" zvalue="1666"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454492684291" ObjectName="#3主变110kV侧1030中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454492684291"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1217.12,529) scale(0.7,-0.875) translate(515.622,-1136.07)" width="40" x="1203.117063355077" y="511.5"/></g>
  <g id="127">
   <use class="kv35" height="20" transform="rotate(270,1660.11,130.069) scale(-1.11111,1.11111) translate(-3153.66,-11.8958)" width="10" x="1654.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="118.9580254990436" zvalue="1705"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454493601795" ObjectName="35kV金源硅厂线37267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454493601795"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1660.11,130.069) scale(-1.11111,1.11111) translate(-3153.66,-11.8958)" width="10" x="1654.555555555556" y="118.9580254990436"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="657">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="657" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,499.841,8.09088) scale(1,1) translate(0,0)" writing-mode="lr" x="500.04" xml:space="preserve" y="12.94" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135417065474" ObjectName="P"/>
   </metadata>
  </g>
  <g id="659">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="659" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,499.841,25.5909) scale(1,1) translate(0,-4.78909e-14)" writing-mode="lr" x="500.04" xml:space="preserve" y="30.44" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135417131010" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="661">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="661" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,499.841,44.0909) scale(1,1) translate(0,0)" writing-mode="lr" x="500.04" xml:space="preserve" y="48.94" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135417196546" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="690">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="690" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,820.222,47.1667) scale(1,1) translate(0,0)" writing-mode="lr" x="820.42" xml:space="preserve" y="52.08" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135414968322" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="692">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="692" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1767.06,59.1667) scale(1,1) translate(0,0)" writing-mode="lr" x="1767.25" xml:space="preserve" y="64.08" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135415492610" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="694">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="694" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,755.883,833.389) scale(1,1) translate(0,0)" writing-mode="lr" x="756.08" xml:space="preserve" y="838.3" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135416016898" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="695">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="695" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,820.222,64.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="820.42" xml:space="preserve" y="69.58" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135415033858" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="697">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="697" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1767.06,76.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="1767.25" xml:space="preserve" y="81.58" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135415558146" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="699">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="699" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,755.883,850.389) scale(1,1) translate(0,0)" writing-mode="lr" x="756.08" xml:space="preserve" y="855.3" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135416082434" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="700">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="700" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,820.222,82.1667) scale(1,1) translate(0,0)" writing-mode="lr" x="820.42" xml:space="preserve" y="87.08" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135415099394" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="702">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="702" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1767.06,94.1667) scale(1,1) translate(0,0)" writing-mode="lr" x="1767.25" xml:space="preserve" y="99.08" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135415623682" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="704">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="704" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,755.883,867.389) scale(1,1) translate(0,0)" writing-mode="lr" x="756.08" xml:space="preserve" y="872.3" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135416147970" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="705">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="705" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,820.222,99.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="820.42" xml:space="preserve" y="104.58" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135415230466" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="707">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="707" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1767.06,111.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1767.25" xml:space="preserve" y="116.58" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135415754754" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="709">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="709" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,756.883,883.389) scale(1,1) translate(0,0)" writing-mode="lr" x="757.08" xml:space="preserve" y="888.3" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135416279042" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="710">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="710" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,820.222,117.167) scale(1,1) translate(0,0)" writing-mode="lr" x="820.42" xml:space="preserve" y="122.08" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135415427074" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="712">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="712" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1767.06,129.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1767.25" xml:space="preserve" y="134.08" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135415951362" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="714">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="714" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,755.883,901.389) scale(1,1) translate(0,0)" writing-mode="lr" x="756.08" xml:space="preserve" y="906.3" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135416475650" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="216">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="216" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,529.5,953) scale(1,1) translate(0,0)" writing-mode="lr" x="529.74" xml:space="preserve" y="957.49" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135422767106" ObjectName="P"/>
   </metadata>
  </g>
  <g id="218">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="218" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,529.5,968) scale(1,1) translate(0,0)" writing-mode="lr" x="529.74" xml:space="preserve" y="972.49" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135422832642" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="220">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="220" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,529.5,983) scale(1,1) translate(0,0)" writing-mode="lr" x="529.74" xml:space="preserve" y="987.49" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135422898178" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="222">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="222" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1463.53,9.09088) scale(1,1) translate(-9.44929e-13,0)" writing-mode="lr" x="1463.72" xml:space="preserve" y="13.94" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135421194242" ObjectName="P"/>
   </metadata>
  </g>
  <g id="223">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="223" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1463.53,26.5909) scale(1,1) translate(-9.44929e-13,0)" writing-mode="lr" x="1463.72" xml:space="preserve" y="31.44" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135421259778" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="224">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="224" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1463.53,44.0909) scale(1,1) translate(-9.44929e-13,0)" writing-mode="lr" x="1463.72" xml:space="preserve" y="48.94" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135421325314" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="143" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,569.721,393.5) scale(1,1) translate(0,0)" writing-mode="lr" x="569.96" xml:space="preserve" y="397.99" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135419162626" ObjectName="P"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="145" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,570.721,419.5) scale(1,1) translate(0,0)" writing-mode="lr" x="570.96" xml:space="preserve" y="423.99" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135419228162" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="156">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="156" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,575.5,448.5) scale(1,1) translate(0,0)" writing-mode="lr" x="575.74" xml:space="preserve" y="452.99" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135419424770" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="159">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="159" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,579.721,476.5) scale(1,1) translate(0,0)" writing-mode="lr" x="579.9400000000001" xml:space="preserve" y="482.95" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135420342274" ObjectName="HCos"/>
   </metadata>
  </g>
  <g id="379">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="379" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.611,332.167) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="337.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135415361538" ObjectName="F"/>
   </metadata>
  </g>
  <g id="378">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="378" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,155.611,260.167) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="265.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127232208900" ObjectName="户宋河电站上网有功"/>
   </metadata>
  </g>
  <g id="377">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="377" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,333.222,261.167) scale(1,1) translate(0,0)" writing-mode="lr" x="333.38" xml:space="preserve" y="266.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135447801858" ObjectName="F"/>
   </metadata>
  </g>
  <g id="374">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="374" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,331.972,330.806) scale(1,1) translate(0,0)" writing-mode="lr" x="332.13" xml:space="preserve" y="335.72" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135415885826" ObjectName="F"/>
   </metadata>
  </g>
  <g id="369">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="369" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,155.611,285.167) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="290.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135447605250" ObjectName="F"/>
   </metadata>
  </g>
  <g id="368">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="368" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,333.222,286.167) scale(1,1) translate(0,0)" writing-mode="lr" x="333.38" xml:space="preserve" y="291.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135447670786" ObjectName="F"/>
   </metadata>
  </g>
  <g id="362">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="362" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,155.611,309.167) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="314.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127179321349" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="360">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="360" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,333.222,308.167) scale(1,1) translate(0,0)" writing-mode="lr" x="333.38" xml:space="preserve" y="313.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127179255813" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="359">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="359" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.611,354.917) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="359.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135416410114" ObjectName="F"/>
   </metadata>
  </g>
  <g id="356">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="356" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,333.222,351.917) scale(1,1) translate(0,0)" writing-mode="lr" x="333.38" xml:space="preserve" y="356.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135435612162" ObjectName="F"/>
   </metadata>
  </g>
  <g id="352">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="352" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.611,378.917) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="383.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135438954498" ObjectName="F"/>
   </metadata>
  </g>
  <g id="247">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="247" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,156.861,399.667) scale(1,1) translate(0,-2.59237e-13)" writing-mode="lr" x="157.02" xml:space="preserve" y="404.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124632723461" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="227">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="227" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.611,423.917) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="428.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124632788997" ObjectName="坝下水位"/>
   </metadata>
  </g>
  <g id="226">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="226" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,333.222,400.917) scale(1,1) translate(0,-2.6007e-13)" writing-mode="lr" x="333.38" xml:space="preserve" y="405.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124632854533" ObjectName="降雨量"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="53" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1345.12,948) scale(1,1) translate(0,0)" writing-mode="lr" x="1344.54" xml:space="preserve" y="952.1900000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135436857346" ObjectName="P"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="56" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1346.12,972.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1345.54" xml:space="preserve" y="977.03" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135436922882" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="57" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1346.12,995.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1345.54" xml:space="preserve" y="1000.11" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135436988418" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="58">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="58" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1348.12,1018) scale(1,1) translate(0,0)" writing-mode="lr" x="1347.54" xml:space="preserve" y="1022.19" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135437447170" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="59">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="59" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,945.117,948) scale(1,1) translate(0,0)" writing-mode="lr" x="944.54" xml:space="preserve" y="952.1900000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135431483394" ObjectName="P"/>
   </metadata>
  </g>
  <g id="60">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="60" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,946.117,971.333) scale(1,1) translate(0,0)" writing-mode="lr" x="945.54" xml:space="preserve" y="975.53" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135431548930" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="61">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="61" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,946.117,994.667) scale(1,1) translate(0,0)" writing-mode="lr" x="945.54" xml:space="preserve" y="998.86" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135431614466" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="195">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="195" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,948.117,1018) scale(1,1) translate(0,0)" writing-mode="lr" x="947.54" xml:space="preserve" y="1022.19" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135431680002" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="427">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="427" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,961.341,9.09088) scale(1,1) translate(0,0)" writing-mode="lr" x="960.87" xml:space="preserve" y="13.73" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135426043906" ObjectName="P"/>
   </metadata>
  </g>
  <g id="428">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="428" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,961.341,27.7954) scale(1,1) translate(0,0)" writing-mode="lr" x="960.87" xml:space="preserve" y="32.46" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135426109442" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="429">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="429" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,961.341,44.0909) scale(1,1) translate(0,0)" writing-mode="lr" x="960.87" xml:space="preserve" y="48.73" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135426174978" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="431">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="431" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1136.93,9.09088) scale(1,1) translate(0,0)" writing-mode="lr" x="1136.46" xml:space="preserve" y="13.73" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135427616770" ObjectName="P"/>
   </metadata>
  </g>
  <g id="432">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="432" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1136.93,26.5909) scale(1,1) translate(0,4.36619e-14)" writing-mode="lr" x="1136.46" xml:space="preserve" y="31.25" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135427682306" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="433">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="433" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1136.93,44.0909) scale(1,1) translate(0,0)" writing-mode="lr" x="1136.46" xml:space="preserve" y="48.73" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135427747842" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="435">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="435" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,673.432,9.09088) scale(1,1) translate(0,0)" writing-mode="lr" x="672.96" xml:space="preserve" y="13.73" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135424471042" ObjectName="P"/>
   </metadata>
  </g>
  <g id="436">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="436" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,673.432,26.5909) scale(1,1) translate(0,0)" writing-mode="lr" x="672.96" xml:space="preserve" y="31.25" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135424536578" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="437">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="437" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,672.432,44.0909) scale(1,1) translate(0,0)" writing-mode="lr" x="671.96" xml:space="preserve" y="48.73" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135424602114" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="439">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="439" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,601,576.177) scale(1,1) translate(0,0)" writing-mode="lr" x="600.42" xml:space="preserve" y="580.38" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135419293698" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="440">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="440" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,601,605.177) scale(1,1) translate(0,0)" writing-mode="lr" x="600.42" xml:space="preserve" y="609.38" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135419359234" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="441">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="441" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,601,630.177) scale(1,1) translate(0,-2.74304e-13)" writing-mode="lr" x="600.42" xml:space="preserve" y="634.38" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135419752450" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="442">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="442" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,916.117,408.5) scale(1,1) translate(0,0)" writing-mode="lr" x="915.54" xml:space="preserve" y="412.7" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135432138754" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="443">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="443" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,917.117,435.5) scale(1,1) translate(0,0)" writing-mode="lr" x="916.54" xml:space="preserve" y="439.7" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135432204290" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="444">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="444" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,918.117,461.5) scale(1,1) translate(0,0)" writing-mode="lr" x="917.54" xml:space="preserve" y="465.7" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135432400898" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="445">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="445" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,924.117,576.177) scale(1,1) translate(0,6.25808e-14)" writing-mode="lr" x="923.54" xml:space="preserve" y="580.38" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135432269826" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="446">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="446" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,924.117,605.177) scale(1,1) translate(0,0)" writing-mode="lr" x="923.54" xml:space="preserve" y="609.38" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135432335362" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="447">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="447" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,924.117,630.177) scale(1,1) translate(0,0)" writing-mode="lr" x="923.54" xml:space="preserve" y="634.38" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135432728578" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="448">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="448" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1624.43,9.09088) scale(1,1) translate(0,0)" writing-mode="lr" x="1623.96" xml:space="preserve" y="13.73" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135441707010" ObjectName="P"/>
   </metadata>
  </g>
  <g id="449">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="449" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1624.43,26.5909) scale(1,1) translate(0,0)" writing-mode="lr" x="1623.96" xml:space="preserve" y="31.23" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135441772546" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="450">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="450" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1624.43,44.0909) scale(1,1) translate(0,0)" writing-mode="lr" x="1623.96" xml:space="preserve" y="48.73" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135441838082" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="451">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="451" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1269,407.5) scale(1,1) translate(1.34726e-13,0)" writing-mode="lr" x="1268.42" xml:space="preserve" y="411.7" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135439085570" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="452">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="452" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1269,436.5) scale(1,1) translate(1.34726e-13,0)" writing-mode="lr" x="1268.42" xml:space="preserve" y="440.7" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135439151106" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="453">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="453" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1269,461.5) scale(1,1) translate(1.34726e-13,0)" writing-mode="lr" x="1268.42" xml:space="preserve" y="465.7" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135439216642" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="454">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="454" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1539,407.5) scale(1,1) translate(-1.31761e-12,0)" writing-mode="lr" x="1538.42" xml:space="preserve" y="411.7" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135439544322" ObjectName="MP"/>
   </metadata>
  </g>
  <g id="455">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="455" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1539,436.5) scale(1,1) translate(-1.31761e-12,0)" writing-mode="lr" x="1538.42" xml:space="preserve" y="440.7" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135439609858" ObjectName="MQ"/>
   </metadata>
  </g>
  <g id="456">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="456" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1539,461.5) scale(1,1) translate(-1.31761e-12,0)" writing-mode="lr" x="1538.42" xml:space="preserve" y="465.7" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135439872002" ObjectName="MIa"/>
   </metadata>
  </g>
  <g id="457">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="457" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1463,618.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1462.42" xml:space="preserve" y="622.7" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135439347714" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="458">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="458" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1463,556.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1462.42" xml:space="preserve" y="560.7" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135439675394" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="459">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="459" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1463,588.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1462.42" xml:space="preserve" y="592.7" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135439740930" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="472">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="472" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1706,654.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1662.88" xml:space="preserve" y="659.25" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135438823426" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="473">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="473" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1181,830.444) scale(1,1) translate(0,-1.46194e-12)" writing-mode="lr" x="1137.88" xml:space="preserve" y="834.89" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135435218946" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="474">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="474" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1182,854.389) scale(1,1) translate(0,-1.50448e-12)" writing-mode="lr" x="1138.88" xml:space="preserve" y="858.83" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135435284482" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="475">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="475" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1182,875.389) scale(1,1) translate(0,-1.54178e-12)" writing-mode="lr" x="1138.88" xml:space="preserve" y="879.83" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135435350018" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="476">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="476" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1182,895.889) scale(1,1) translate(0,-1.57819e-12)" writing-mode="lr" x="1138.88" xml:space="preserve" y="900.33" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135435481090" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="AccessoryClass">
  <g id="68">
   <use class="kv110" height="35" transform="rotate(0,812.412,195.25) scale(1.3,1.3) translate(-181.48,-39.8077)" width="40" x="786.4124881074952" xlink:href="#Accessory:四卷带壁雷器母线PT_0" y="172.5" zvalue="1093"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454488227843" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,812.412,195.25) scale(1.3,1.3) translate(-181.48,-39.8077)" width="40" x="786.4124881074952" y="172.5"/></g>
  <g id="175">
   <use class="kv10" height="30" transform="rotate(0,762.068,742) scale(1.6,1.6) translate(-276.776,-269.25)" width="30" x="738.068344027507" xlink:href="#Accessory:带熔断器35kVPT11_0" y="718" zvalue="1338"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454489014275" ObjectName="10kVⅠ母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,762.068,742) scale(1.6,1.6) translate(-276.776,-269.25)" width="30" x="738.068344027507" y="718"/></g>
  <g id="33">
   <use class="kv110" height="30" transform="rotate(0,464,142) scale(1,1) translate(0,0)" width="35" x="446.5" xlink:href="#Accessory:线路PT99_0" y="127" zvalue="1430"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454489079811" ObjectName="110kV户南线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,464,142) scale(1,1) translate(0,0)" width="35" x="446.5" y="127"/></g>
  <g id="262">
   <use class="kv110" height="30" transform="rotate(0,637.5,141.056) scale(1,1) translate(0,0)" width="35" x="620" xlink:href="#Accessory:线路PT99_0" y="126.055555343628" zvalue="1454"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454489145347" ObjectName="110kV宋勐佳线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,637.5,141.056) scale(1,1) translate(0,0)" width="35" x="620" y="126.055555343628"/></g>
  <g id="288">
   <use class="kv110" height="30" transform="rotate(0,924.5,140.056) scale(1,1) translate(0,0)" width="35" x="907" xlink:href="#Accessory:线路PT99_0" y="125.055555343628" zvalue="1480"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454489800707" ObjectName="110kV允宋线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,924.5,140.056) scale(1,1) translate(0,0)" width="35" x="907" y="125.055555343628"/></g>
  <g id="313">
   <use class="kv110" height="30" transform="rotate(0,1100.5,139.056) scale(1,1) translate(0,0)" width="35" x="1083" xlink:href="#Accessory:线路PT99_0" y="124.055555343628" zvalue="1502"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454490324995" ObjectName="110kV宋腊线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1100.5,139.056) scale(1,1) translate(0,0)" width="35" x="1083" y="124.055555343628"/></g>
  <g id="54">
   <use class="kv10" height="30" transform="rotate(0,467,887) scale(1,1) translate(0,0)" width="30" x="452" xlink:href="#Accessory:带熔断器35kVPT11_0" y="872" zvalue="1516"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454491111427" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,467,887) scale(1,1) translate(0,0)" width="30" x="452" y="872"/></g>
  <g id="72">
   <use class="kv10" height="40" transform="rotate(0,605,893) scale(1,-1) translate(0,-1786)" width="30" x="590" xlink:href="#Accessory:带熔断器的线路PT1_0" y="873" zvalue="1517"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454491176963" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,605,893) scale(1,-1) translate(0,-1786)" width="30" x="590" y="873"/></g>
  <g id="75">
   <use class="kv10" height="29" transform="rotate(0,579,896) scale(1,-1) translate(0,-1792)" width="30" x="564" xlink:href="#Accessory:PT12321_0" y="881.5" zvalue="1518"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454491242499" ObjectName="#1发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,579,896) scale(1,-1) translate(0,-1792)" width="30" x="564" y="881.5"/></g>
  <g id="134">
   <use class="kv10" height="30" transform="rotate(0,1188.19,742) scale(1.6,1.6) translate(-436.57,-269.25)" width="30" x="1164.185407382585" xlink:href="#Accessory:带熔断器35kVPT11_0" y="718" zvalue="1559"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454491832323" ObjectName="10kVⅡ母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1188.19,742) scale(1.6,1.6) translate(-436.57,-269.25)" width="30" x="1164.185407382585" y="718"/></g>
  <g id="124">
   <use class="kv10" height="30" transform="rotate(0,873.117,887) scale(1,1) translate(0,0)" width="30" x="858.1170633550774" xlink:href="#Accessory:带熔断器35kVPT11_0" y="872" zvalue="1573"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454491570179" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,873.117,887) scale(1,1) translate(0,0)" width="30" x="858.1170633550774" y="872"/></g>
  <g id="123">
   <use class="kv10" height="40" transform="rotate(0,1011.12,893) scale(1,-1) translate(0,-1786)" width="30" x="996.1170633550774" xlink:href="#Accessory:带熔断器的线路PT1_0" y="873" zvalue="1574"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454491504643" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1011.12,893) scale(1,-1) translate(0,-1786)" width="30" x="996.1170633550774" y="873"/></g>
  <g id="122">
   <use class="kv10" height="29" transform="rotate(0,985.117,896) scale(1,-1) translate(0,-1792)" width="30" x="970.1170633550774" xlink:href="#Accessory:PT12321_0" y="881.5" zvalue="1575"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454491439107" ObjectName="#2发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,985.117,896) scale(1,-1) translate(0,-1792)" width="30" x="970.1170633550774" y="881.5"/></g>
  <g id="284">
   <use class="kv10" height="30" transform="rotate(0,1594.19,582) scale(1.6,-1.6) translate(-588.82,-936.75)" width="30" x="1570.185407382584" xlink:href="#Accessory:带熔断器35kVPT11_0" y="558" zvalue="1661"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454492749827" ObjectName="10kVⅢ母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1594.19,582) scale(1.6,-1.6) translate(-588.82,-936.75)" width="30" x="1570.185407382584" y="558"/></g>
  <g id="221">
   <use class="kv10" height="30" transform="rotate(0,1279.12,887) scale(1,1) translate(0,0)" width="30" x="1264.117063355077" xlink:href="#Accessory:带熔断器35kVPT11_0" y="872" zvalue="1671"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454492487683" ObjectName="#3发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1279.12,887) scale(1,1) translate(0,0)" width="30" x="1264.117063355077" y="872"/></g>
  <g id="219">
   <use class="kv10" height="40" transform="rotate(0,1417.12,893) scale(1,-1) translate(0,-1786)" width="30" x="1402.117063355077" xlink:href="#Accessory:带熔断器的线路PT1_0" y="873" zvalue="1672"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454492422147" ObjectName="#3发电机PT2"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1417.12,893) scale(1,-1) translate(0,-1786)" width="30" x="1402.117063355077" y="873"/></g>
  <g id="217">
   <use class="kv10" height="29" transform="rotate(0,1391.12,896) scale(1,-1) translate(0,-1792)" width="30" x="1376.117063355077" xlink:href="#Accessory:PT12321_0" y="881.5" zvalue="1673"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454492356611" ObjectName="#3发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1391.12,896) scale(1,-1) translate(0,-1792)" width="30" x="1376.117063355077" y="881.5"/></g>
  <g id="17">
   <use class="kv35" height="30" transform="rotate(0,1429,110) scale(1,-1) translate(0,-220)" width="30" x="1414" xlink:href="#Accessory:PT象达_0" y="95" zvalue="1689"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454493274115" ObjectName="35kV宏利硅厂线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1429,110) scale(1,-1) translate(0,-220)" width="30" x="1414" y="95"/></g>
  <g id="78">
   <use class="kv35" height="30" transform="rotate(0,1430,156) scale(1,1) translate(0,0)" width="30" x="1415" xlink:href="#Accessory:避雷器带指示灯_0" y="141" zvalue="1691"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454493339651" ObjectName="35kV宏利硅厂线避雷器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1430,156) scale(1,1) translate(0,0)" width="30" x="1415" y="141"/></g>
  <g id="95">
   <use class="kv35" height="30" transform="rotate(0,1589,111) scale(1,-1) translate(0,-222)" width="30" x="1574" xlink:href="#Accessory:PT象达_0" y="96" zvalue="1709"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454493470723" ObjectName="35kV金源硅厂线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1589,111) scale(1,-1) translate(0,-222)" width="30" x="1574" y="96"/></g>
  <g id="88">
   <use class="kv35" height="30" transform="rotate(0,1590,157) scale(1,1) translate(0,0)" width="30" x="1575" xlink:href="#Accessory:避雷器带指示灯_0" y="142" zvalue="1711"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454493405187" ObjectName="35kV金源硅厂线避雷器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1590,157) scale(1,1) translate(0,0)" width="30" x="1575" y="142"/></g>
  <g id="171">
   <use class="kv35" height="30" transform="rotate(0,1792,198) scale(0.857143,1.4) translate(296.167,-50.5714)" width="35" x="1777" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="177" zvalue="1713"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454493863939" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1792,198) scale(0.857143,1.4) translate(296.167,-50.5714)" width="35" x="1777" y="177"/></g>
  <g id="173">
   <use class="kv35" height="30" transform="rotate(0,1755,203) scale(1,-1) translate(0,-406)" width="30" x="1740" xlink:href="#Accessory:避雷器带指示灯_0" y="188" zvalue="1715"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454493929475" ObjectName="35kV母线避雷器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1755,203) scale(1,-1) translate(0,-406)" width="30" x="1740" y="188"/></g>
  <g id="180">
   <use class="kv10" height="30" transform="rotate(0,1599.16,847) scale(1.6,1.6) translate(-590.687,-308.625)" width="30" x="1575.164067223915" xlink:href="#Accessory:带熔断器35kVPT11_0" y="823" zvalue="1742"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454494191619" ObjectName="坝区变电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1599.16,847) scale(1.6,1.6) translate(-590.687,-308.625)" width="30" x="1575.164067223915" y="823"/></g>
  <g id="211">
   <use class="kv10" height="29" transform="rotate(0,1599,749.733) scale(1.53333,-1.53333) translate(-548.174,-1230.96)" width="30" x="1576" xlink:href="#Accessory:PT12321_0" y="727.5" zvalue="1745"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454494257155" ObjectName="坝区变"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1599,749.733) scale(1.53333,-1.53333) translate(-548.174,-1230.96)" width="30" x="1576" y="727.5"/></g>
  <g id="375">
   <use class="kv10" height="26" transform="rotate(0,1680,820) scale(1,1) translate(0,0)" width="12" x="1674" xlink:href="#Accessory:避雷器1_0" y="807" zvalue="1768"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454494781443" ObjectName="避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1680,820) scale(1,1) translate(0,0)" width="12" x="1674" y="807"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="73">
   <g id="730">
    <use class="kv110" height="50" transform="rotate(0,679.5,520.339) scale(2.15,1.85355) translate(-346.203,-218.274)" width="30" x="647.25" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="474" zvalue="1097"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874583703554" ObjectName="110"/>
    </metadata>
   </g>
   <g id="731">
    <use class="kv10" height="50" transform="rotate(0,679.5,520.339) scale(2.15,1.85355) translate(-346.203,-218.274)" width="30" x="647.25" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="474" zvalue="1097"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874583769090" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399529857026" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399529857026"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,679.5,520.339) scale(2.15,1.85355) translate(-346.203,-218.274)" width="30" x="647.25" y="474"/></g>
  <g id="154">
   <g id="1540">
    <use class="kv110" height="50" transform="rotate(0,1001.62,520.339) scale(2.15,1.85355) translate(-518.499,-218.274)" width="30" x="969.37" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="474" zvalue="1540"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874583834626" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1541">
    <use class="kv10" height="50" transform="rotate(0,1001.62,520.339) scale(2.15,1.85355) translate(-518.499,-218.274)" width="30" x="969.37" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="474" zvalue="1540"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874583900162" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399529922562" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399529922562"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1001.62,520.339) scale(2.15,1.85355) translate(-518.499,-218.274)" width="30" x="969.37" y="474"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="kv10" height="30" transform="rotate(0,529,884) scale(1.5,1.5) translate(-168.833,-287.167)" width="30" x="506.5" xlink:href="#Generator:发电机_0" y="861.5" zvalue="1260"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454488817667" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192454488817667"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,529,884) scale(1.5,1.5) translate(-168.833,-287.167)" width="30" x="506.5" y="861.5"/></g>
  <g id="151">
   <use class="kv10" height="30" transform="rotate(0,935.117,884) scale(1.5,1.5) translate(-304.206,-287.167)" width="30" x="912.6170633550774" xlink:href="#Generator:发电机_0" y="861.5" zvalue="1543"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454492028931" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192454492028931"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,935.117,884) scale(1.5,1.5) translate(-304.206,-287.167)" width="30" x="912.6170633550774" y="861.5"/></g>
  <g id="332">
   <use class="kv10" height="30" transform="rotate(0,1341.12,884) scale(1.5,1.5) translate(-439.539,-287.167)" width="30" x="1318.617063355077" xlink:href="#Generator:发电机_0" y="861.5" zvalue="1647"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454492946435" ObjectName="#3发电机"/>
   <cge:TPSR_Ref TObjectID="6192454492946435"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1341.12,884) scale(1.5,1.5) translate(-439.539,-287.167)" width="30" x="1318.617063355077" y="861.5"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="293">
   <use class="kv110" height="30" transform="rotate(0,960.432,104.278) scale(6.34921,1.48148) translate(-790.442,-26.6681)" width="7" x="938.2098173330401" xlink:href="#ACLineSegment:线路_0" y="82.05555534362799" zvalue="1471"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249327403010" ObjectName="110kV允宋线"/>
   <cge:TPSR_Ref TObjectID="8444249327403010_5066549682569217"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,960.432,104.278) scale(6.34921,1.48148) translate(-790.442,-26.6681)" width="7" x="938.2098173330401" y="82.05555534362799"/></g>
  <g id="317">
   <use class="kv110" height="30" transform="rotate(0,1136.43,103.278) scale(6.34921,1.48148) translate(-938.722,-26.3431)" width="7" x="1114.20981733304" xlink:href="#ACLineSegment:线路_0" y="81.05555534362799" zvalue="1495"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249327206402" ObjectName="110kV宋腊线"/>
   <cge:TPSR_Ref TObjectID="8444249327206402_5066549682569217"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1136.43,103.278) scale(6.34921,1.48148) translate(-938.722,-26.3431)" width="7" x="1114.20981733304" y="81.05555534362799"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="40">
   <use class="kv10" height="35" transform="rotate(0,468.5,541.828) scale(1.21875,-1.21875) translate(-80.5897,-982.577)" width="32" x="449" xlink:href="#EnergyConsumer:站用变13_0" y="520.5" zvalue="1513"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454490980355" ObjectName="生活区"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,468.5,541.828) scale(1.21875,-1.21875) translate(-80.5897,-982.577)" width="32" x="449" y="520.5"/></g>
  <g id="51">
   <use class="kv10" height="30" transform="rotate(0,679.5,770.5) scale(2.1,2.1) translate(-344.929,-387.095)" width="20" x="658.5000000000001" xlink:href="#EnergyConsumer:站用变11_0" y="739" zvalue="1515"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454491045891" ObjectName="#1厂用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,679.5,770.5) scale(2.1,2.1) translate(-344.929,-387.095)" width="20" x="658.5000000000001" y="739"/></g>
  <g id="125">
   <use class="kv10" height="30" transform="rotate(0,1085.62,770.5) scale(2.1,2.1) translate(-557.657,-387.095)" width="20" x="1064.617063355078" xlink:href="#EnergyConsumer:站用变11_0" y="739" zvalue="1571"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454491635715" ObjectName="#2厂用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1085.62,770.5) scale(2.1,2.1) translate(-557.657,-387.095)" width="20" x="1064.617063355078" y="739"/></g>
  <g id="228">
   <use class="kv10" height="30" transform="rotate(0,1491.62,770.5) scale(2.1,2.1) translate(-770.323,-387.095)" width="20" x="1470.617063355078" xlink:href="#EnergyConsumer:站用变11_0" y="739" zvalue="1669"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454492553219" ObjectName="#3厂用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1491.62,770.5) scale(2.1,2.1) translate(-770.323,-387.095)" width="20" x="1470.617063355078" y="739"/></g>
  <g id="346">
   <use class="kv10" height="30" transform="rotate(90,1798,777) scale(0.416682,1) translate(2513.54,0)" width="12" x="1795.499908447266" xlink:href="#EnergyConsumer:负荷_0" y="762" zvalue="1749"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454494322691" ObjectName="10kV户铜线"/>
   <cge:TPSR_Ref TObjectID="6192454494322691"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1798,777) scale(0.416682,1) translate(2513.54,0)" width="12" x="1795.499908447266" y="762"/></g>
 </g>
 <g id="PowerTransformer3Class">
  <g id="347">
   <g id="3470">
    <use class="kv110" height="50" transform="rotate(0,1383,520) scale(1.8,1.8) translate(-594.667,-211.111)" width="50" x="1338" xlink:href="#PowerTransformer3:主变高压侧有中性点_0" y="475" zvalue="1682"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874583965698" ObjectName="110"/>
    </metadata>
   </g>
   <g id="3471">
    <use class="kv35" height="50" transform="rotate(0,1383,520) scale(1.8,1.8) translate(-594.667,-211.111)" width="50" x="1338" xlink:href="#PowerTransformer3:主变高压侧有中性点_1" y="475" zvalue="1682"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874584031234" ObjectName="35"/>
    </metadata>
   </g>
   <g id="3472">
    <use class="kv10" height="50" transform="rotate(0,1383,520) scale(1.8,1.8) translate(-594.667,-211.111)" width="50" x="1338" xlink:href="#PowerTransformer3:主变高压侧有中性点_2" y="475" zvalue="1682"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874584096770" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399529988098" ObjectName="#3主变"/>
   <cge:TPSR_Ref TObjectID="6755399529988098"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1383,520) scale(1.8,1.8) translate(-594.667,-211.111)" width="50" x="1338" y="475"/></g>
 </g>
 <g id="StateClass">
  <g id="373">
   <use height="30" transform="rotate(0,330.673,186.357) scale(0.708333,0.665547) translate(131.784,88.6322)" width="30" x="320.05" xlink:href="#State:红绿圆(方形)_0" y="176.37" zvalue="1810"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374885912580" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,330.673,186.357) scale(0.708333,0.665547) translate(131.784,88.6322)" width="30" x="320.05" y="176.37"/></g>
  <g id="355">
   <use height="30" transform="rotate(0,235.048,186.357) scale(0.708333,0.665547) translate(92.4093,88.6322)" width="30" x="224.42" xlink:href="#State:红绿圆(方形)_0" y="176.37" zvalue="1827"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950276841480" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,235.048,186.357) scale(0.708333,0.665547) translate(92.4093,88.6322)" width="30" x="224.42" y="176.37"/></g>
 </g>
</svg>