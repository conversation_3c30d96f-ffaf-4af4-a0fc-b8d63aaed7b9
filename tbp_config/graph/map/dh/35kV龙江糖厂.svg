<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549589508098" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Breaker:小车开关带避雷器_0" viewBox="0,0,20,26">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="10" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982326301579349" x2="9.982326301579349" y1="18.5" y2="24.08333333333334"/>
   <rect fill-opacity="0" height="12.42" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.08,12.13) scale(1,1) translate(0,0)" width="6.67" x="6.75" y="5.92"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.06661748931881" x2="10.06661748931881" y1="2.083333333333329" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.75" x2="9.916666666666666" y1="21.25" y2="21.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.7" x2="17.7" y1="12.75" y2="21.08333333333334"/>
   <path d="M 13.6321 23.6556 L 9.98274 25.9797 L 6.33333 23.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.75 2.40358 L 10.0648 0.181096 L 6.45119 2.40358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.6321 21.8223 L 9.98274 24.1463 L 6.33333 21.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.75 4.15358 L 10.0648 1.9311 L 6.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438356" x2="17.20890410958904" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.44863013698632" x2="17.92808219178084" y1="8.036096718478305" y2="8.036096718478305"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438357" x2="18.1678082191781" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.9691780821918" x2="18.40753424657536" y1="8.42469775474782" y2="8.42469775474782"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.48972602739728" x2="18.88698630136988" y1="8.813298791017345" y2="8.813298791017345"/>
   <rect fill-opacity="0" height="5.57" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,17.71,12.73) scale(1,-1) translate(0,-1045.27)" width="2.42" x="16.5" y="9.949999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438358" x2="17.68835616438358" y1="9.979101899825864" y2="8.813298791017306"/>
  </symbol>
  <symbol id="Breaker:小车开关带避雷器_1" viewBox="0,0,20,26">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="10" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.75" x2="9.916666666666666" y1="21.25" y2="21.25"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="12.42" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.08,12.13) scale(1,1) translate(0,0)" width="6.67" x="6.75" y="5.92"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.7" x2="17.7" y1="12.75" y2="21.08333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.06661748931881" x2="10.06661748931881" y1="0.3333333333333321" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982326301579349" x2="9.982326301579349" y1="18.5" y2="26.08333333333333"/>
   <path d="M 13.6321 23.6556 L 9.98274 25.9797 L 6.33333 23.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.75 2.40358 L 10.0648 0.181096 L 6.45119 2.40358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.9691780821918" x2="18.40753424657536" y1="8.42469775474782" y2="8.42469775474782"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.48972602739728" x2="18.88698630136988" y1="8.813298791017345" y2="8.813298791017345"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438357" x2="18.1678082191781" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.44863013698632" x2="17.92808219178084" y1="8.036096718478305" y2="8.036096718478305"/>
   <rect fill-opacity="0" height="5.57" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,17.71,12.73) scale(1,-1) translate(0,-1045.27)" width="2.42" x="16.5" y="9.949999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438356" x2="17.20890410958904" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438358" x2="17.68835616438358" y1="9.979101899825864" y2="8.813298791017306"/>
  </symbol>
  <symbol id="Breaker:小车开关带避雷器_2" viewBox="0,0,20,26">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="10" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333334" x2="13.08333333333333" y1="6.083333333333334" y2="18.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="7" y1="6.166666666666669" y2="18"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.7" x2="17.7" y1="12.75" y2="21.08333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.75" x2="9.916666666666666" y1="21.25" y2="21.25"/>
   <rect fill-opacity="0" height="12.42" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.08,12.13) scale(1,1) translate(0,0)" width="6.67" x="6.75" y="5.92"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.06661748931881" x2="10.06661748931881" y1="2.083333333333329" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982326301579349" x2="9.982326301579349" y1="18.5" y2="24.08333333333334"/>
   <path d="M 13.6321 23.6556 L 9.98274 25.9797 L 6.33333 23.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.75 2.40358 L 10.0648 0.181096 L 6.45119 2.40358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.6321 21.8223 L 9.98274 24.1463 L 6.33333 21.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.75 4.15358 L 10.0648 1.9311 L 6.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.9691780821918" x2="18.40753424657536" y1="8.42469775474782" y2="8.42469775474782"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.48972602739728" x2="18.88698630136988" y1="8.813298791017345" y2="8.813298791017345"/>
   <rect fill-opacity="0" height="5.57" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,17.71,12.73) scale(1,-1) translate(0,-1045.27)" width="2.42" x="16.5" y="9.949999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.44863013698632" x2="17.92808219178084" y1="8.036096718478305" y2="8.036096718478305"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438357" x2="18.1678082191781" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438356" x2="17.20890410958904" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438358" x2="17.68835616438358" y1="9.979101899825864" y2="8.813298791017306"/>
  </symbol>
  <symbol id="Accessory:线路PT11带避雷器_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="23.75" y2="23.75"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,11) scale(1,1) translate(0,0)" width="6" x="17" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="20" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="32" y1="17" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.13040845230975" x2="32.13040845230975" y1="16.76788570496156" y2="23.53635804601289"/>
   <rect fill-opacity="0" height="6.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,32.04,24.56) scale(1,1) translate(0,0)" width="15.34" x="24.37" y="21.53"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.98821300514558" x2="19.98821300514558" y1="1.029523490692871" y2="18.75"/>
   <path d="M 30.0147 23.4481 L 34.2865 23.4481 L 32.115 28.6155 L 30.0147 23.4481" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <ellipse cx="19.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.22179295459144" x2="32.22179295459144" y1="32.22550978083666" y2="36.2625308385742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.01788570496155" x2="35.42570020422134" y1="36.23023467011234" y2="36.23023467011234"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.26384963537316" x2="34.53572596821302" y1="37.52208140858838" y2="37.52208140858838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.15382387138148" x2="33.11176719059974" y1="38.49096646244535" y2="38.49096646244535"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="32.91666666666667" y2="32.91666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变13_0" viewBox="0,0,32,35">
   <use terminal-index="0" type="0" x="16" xlink:href="#terminal" y="3.5"/>
   <ellipse cx="16.04" cy="11.97" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.97" cy="24.14" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067018" x2="16.03525619067018" y1="20.85" y2="24.95894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067017" x2="10.66666666666667" y1="24.98394833233988" y2="28.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.06302903072498" x2="21.16666666666667" y1="24.93990500916851" y2="28.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067018" x2="16.03525619067018" y1="7.6" y2="11.70894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067017" x2="12.05" y1="11.73394833233987" y2="14.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.06302903072498" x2="20.15" y1="11.68990500916851" y2="14.5"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000004" y2="0.9166666666666679"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.89694619969018" y2="15.89694619969018"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.73157590710537" y2="17.73157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.48348701738202" y2="19.48348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.05" x2="5.05" y1="3.566666666666666" y2="15.81666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="PowerTransformer2:586_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.75" x2="10.08333333333333" y1="17.11381925541057" y2="17.11381925541057"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.98518194534241" x2="9.999999999999998" y1="9.386991192770131" y2="16.99800064973551"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00299079104462" x2="19.66666666666667" y1="9.301854138598824" y2="16.8218426122152"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:586_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="30.58333333333333" y2="36.5"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="21" y1="36.5" y2="40.5"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="10" y1="36.5" y2="40.5"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV龙江糖厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="277.25" x="51.04" xlink:href="logo.png" y="52.86"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,189.661,82.8571) scale(1,1) translate(0,0)" writing-mode="lr" x="189.66" xml:space="preserve" y="86.36" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,184.286,80.0475) scale(1,1) translate(0,0)" writing-mode="lr" x="184.29" xml:space="preserve" y="89.05" zvalue="3">35kV龙江糖厂</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="11" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,80.2545,203.357) scale(1,1) translate(0,0)" width="73.56" x="43.47" y="191.36" zvalue="22"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.2545,203.357) scale(1,1) translate(0,0)" writing-mode="lr" x="80.25" xml:space="preserve" y="207.86" zvalue="22">信号一览</text>
  <line fill="none" id="29" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="385.0357142857144" x2="385.0357142857144" y1="20.85714285714295" y2="1050.857142857143" zvalue="4"/>
  <line fill="none" id="27" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.03571428571468" x2="378.0357142857142" y1="156.7276354712254" y2="156.7276354712254" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="187.2857142857144" y1="265.1071428571429" y2="265.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="187.2857142857144" y1="291.1071428571429" y2="291.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="6.285714285714448" y1="265.1071428571429" y2="291.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="187.2857142857144" y1="265.1071428571429" y2="291.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="368.2857142857144" y1="265.1071428571429" y2="265.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="368.2857142857144" y1="291.1071428571429" y2="291.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="187.2857142857144" y1="265.1071428571429" y2="291.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.2857142857144" x2="368.2857142857144" y1="265.1071428571429" y2="291.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="187.2857142857144" y1="291.1071428571429" y2="291.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="187.2857142857144" y1="315.3571428571429" y2="315.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="6.285714285714448" y1="291.1071428571429" y2="315.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="187.2857142857144" y1="291.1071428571429" y2="315.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="368.2857142857144" y1="291.1071428571429" y2="291.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="368.2857142857144" y1="315.3571428571429" y2="315.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="187.2857142857144" y1="291.1071428571429" y2="315.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.2857142857144" x2="368.2857142857144" y1="291.1071428571429" y2="315.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="187.2857142857144" y1="315.3571428571429" y2="315.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="187.2857142857144" y1="338.1071428571429" y2="338.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="6.285714285714448" y1="315.3571428571429" y2="338.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="187.2857142857144" y1="315.3571428571429" y2="338.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="368.2857142857144" y1="315.3571428571429" y2="315.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="368.2857142857144" y1="338.1071428571429" y2="338.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="187.2857142857144" y1="315.3571428571429" y2="338.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.2857142857144" x2="368.2857142857144" y1="315.3571428571429" y2="338.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="187.2857142857144" y1="338.1071428571429" y2="338.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="187.2857142857144" y1="360.8571428571429" y2="360.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="6.285714285714448" y1="338.1071428571429" y2="360.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="187.2857142857144" y1="338.1071428571429" y2="360.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="368.2857142857144" y1="338.1071428571429" y2="338.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="368.2857142857144" y1="360.8571428571429" y2="360.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="187.2857142857144" y1="338.1071428571429" y2="360.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.2857142857144" x2="368.2857142857144" y1="338.1071428571429" y2="360.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="187.2857142857144" y1="360.8571428571429" y2="360.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="187.2857142857144" y1="383.6071428571429" y2="383.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="6.285714285714448" y1="360.8571428571429" y2="383.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="187.2857142857144" y1="360.8571428571429" y2="383.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="368.2857142857144" y1="360.8571428571429" y2="360.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="368.2857142857144" y1="383.6071428571429" y2="383.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="187.2857142857144" y1="360.8571428571429" y2="383.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.2857142857144" x2="368.2857142857144" y1="360.8571428571429" y2="383.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="187.2857142857144" y1="383.6071428571429" y2="383.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="187.2857142857144" y1="406.3571428571429" y2="406.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="6.285714285714448" y1="383.6071428571429" y2="406.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="187.2857142857144" y1="383.6071428571429" y2="406.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="368.2857142857144" y1="383.6071428571429" y2="383.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="368.2857142857144" y1="406.3571428571429" y2="406.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="187.2857142857144" y1="383.6071428571429" y2="406.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.2857142857144" x2="368.2857142857144" y1="383.6071428571429" y2="406.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="187.2857142857144" y1="406.3571428571429" y2="406.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="187.2857142857144" y1="429.1071428571429" y2="429.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.285714285714448" x2="6.285714285714448" y1="406.3571428571429" y2="429.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="187.2857142857144" y1="406.3571428571429" y2="429.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="368.2857142857144" y1="406.3571428571429" y2="406.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="368.2857142857144" y1="429.1071428571429" y2="429.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.2857142857144" x2="187.2857142857144" y1="406.3571428571429" y2="429.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.2857142857144" x2="368.2857142857144" y1="406.3571428571429" y2="429.1071428571429"/>
  <line fill="none" id="25" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.285714285714675" x2="372.2857142857142" y1="510.9776354712254" y2="510.9776354712254" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.285714285714448" x2="95.28571428571445" y1="942.1071428571429" y2="942.1071428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.285714285714448" x2="95.28571428571445" y1="981.2704428571428" y2="981.2704428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.285714285714448" x2="5.285714285714448" y1="942.1071428571429" y2="981.2704428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.28571428571445" x2="95.28571428571445" y1="942.1071428571429" y2="981.2704428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.28571428571445" x2="365.2857142857144" y1="942.1071428571429" y2="942.1071428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.28571428571445" x2="365.2857142857144" y1="981.2704428571428" y2="981.2704428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.28571428571445" x2="95.28571428571445" y1="942.1071428571429" y2="981.2704428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="365.2857142857144" x2="365.2857142857144" y1="942.1071428571429" y2="981.2704428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.285714285714448" x2="95.28571428571445" y1="981.2704128571429" y2="981.2704128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.285714285714448" x2="95.28571428571445" y1="1009.188812857143" y2="1009.188812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.285714285714448" x2="5.285714285714448" y1="981.2704128571429" y2="1009.188812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.28571428571445" x2="95.28571428571445" y1="981.2704128571429" y2="1009.188812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.28571428571445" x2="185.2857142857144" y1="981.2704128571429" y2="981.2704128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.28571428571445" x2="185.2857142857144" y1="1009.188812857143" y2="1009.188812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.28571428571445" x2="95.28571428571445" y1="981.2704128571429" y2="1009.188812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="981.2704128571429" y2="1009.188812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.2857142857146" x2="275.2857142857146" y1="981.2704128571429" y2="981.2704128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.2857142857146" x2="275.2857142857146" y1="1009.188812857143" y2="1009.188812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.2857142857146" x2="185.2857142857146" y1="981.2704128571429" y2="1009.188812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.2857142857146" x2="275.2857142857146" y1="981.2704128571429" y2="1009.188812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.2857142857144" x2="365.2857142857144" y1="981.2704128571429" y2="981.2704128571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.2857142857144" x2="365.2857142857144" y1="1009.188812857143" y2="1009.188812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.2857142857144" x2="275.2857142857144" y1="981.2704128571429" y2="1009.188812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="365.2857142857144" x2="365.2857142857144" y1="981.2704128571429" y2="1009.188812857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.285714285714448" x2="95.28571428571445" y1="1009.188742857143" y2="1009.188742857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.285714285714448" x2="95.28571428571445" y1="1037.107142857143" y2="1037.107142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.285714285714448" x2="5.285714285714448" y1="1009.188742857143" y2="1037.107142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.28571428571445" x2="95.28571428571445" y1="1009.188742857143" y2="1037.107142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.28571428571445" x2="185.2857142857144" y1="1009.188742857143" y2="1009.188742857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.28571428571445" x2="185.2857142857144" y1="1037.107142857143" y2="1037.107142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.28571428571445" x2="95.28571428571445" y1="1009.188742857143" y2="1037.107142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="1009.188742857143" y2="1037.107142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.2857142857146" x2="275.2857142857146" y1="1009.188742857143" y2="1009.188742857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.2857142857146" x2="275.2857142857146" y1="1037.107142857143" y2="1037.107142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.2857142857146" x2="185.2857142857146" y1="1009.188742857143" y2="1037.107142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.2857142857146" x2="275.2857142857146" y1="1009.188742857143" y2="1037.107142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.2857142857144" x2="365.2857142857144" y1="1009.188742857143" y2="1009.188742857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.2857142857144" x2="365.2857142857144" y1="1037.107142857143" y2="1037.107142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.2857142857144" x2="275.2857142857144" y1="1009.188742857143" y2="1037.107142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="365.2857142857144" x2="365.2857142857144" y1="1009.188742857143" y2="1037.107142857143"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50.2857,962.107) scale(1,1) translate(0,0)" writing-mode="lr" x="50.29" xml:space="preserve" y="968.11" zvalue="10">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46.1746,994.996) scale(1,1) translate(0,0)" writing-mode="lr" x="46.17" xml:space="preserve" y="1001" zvalue="11">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.175,994.996) scale(1,1) translate(0,0)" writing-mode="lr" x="228.17" xml:space="preserve" y="1001" zvalue="12">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.1746,1023) scale(1,1) translate(0,0)" writing-mode="lr" x="45.17" xml:space="preserve" y="1029" zvalue="13">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.175,1023) scale(1,1) translate(0,0)" writing-mode="lr" x="227.17" xml:space="preserve" y="1029" zvalue="14">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.7857,576.607) scale(1,1) translate(0,0)" writing-mode="lr" x="70.78571428571445" xml:space="preserve" y="581.1071428571429" zvalue="16">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,195.684,203.949) scale(1,1) translate(0,0)" writing-mode="lr" x="195.68" xml:space="preserve" y="208.45" zvalue="17">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,300.684,203.949) scale(1,1) translate(0,0)" writing-mode="lr" x="300.68" xml:space="preserve" y="208.45" zvalue="18">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,52.7857,278.107) scale(1,1) translate(0,0)" writing-mode="lr" x="10.29" xml:space="preserve" y="282.61" zvalue="19">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,233.286,278.107) scale(1,1) translate(0,0)" writing-mode="lr" x="190.79" xml:space="preserve" y="282.61" zvalue="20">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.9732,351.357) scale(1,1) translate(0,0)" writing-mode="lr" x="55.97" xml:space="preserve" y="355.86" zvalue="21">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,51.7857,304.107) scale(1,1) translate(0,0)" writing-mode="lr" x="9.289999999999999" xml:space="preserve" y="308.61" zvalue="23">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,232.286,304.107) scale(1,1) translate(0,0)" writing-mode="lr" x="189.79" xml:space="preserve" y="308.61" zvalue="24">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,51.7857,327.107) scale(1,1) translate(0,0)" writing-mode="lr" x="9.289999999999999" xml:space="preserve" y="331.61" zvalue="29">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,231.786,326.107) scale(1,1) translate(0,0)" writing-mode="lr" x="189.29" xml:space="preserve" y="330.61" zvalue="30">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.973,351.357) scale(1,1) translate(0,0)" writing-mode="lr" x="235.97" xml:space="preserve" y="355.86" zvalue="31">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1302.62,293.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1302.63" xml:space="preserve" y="297.75" zvalue="34">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,729.812,254.5) scale(1,1) translate(0,0)" writing-mode="lr" x="729.8099999999999" xml:space="preserve" y="259" zvalue="36">361</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,732.577,162) scale(1,1) translate(0,0)" writing-mode="lr" x="732.58" xml:space="preserve" y="166.5" zvalue="37">362</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,763.75,51.5) scale(1,1) translate(0,0)" writing-mode="lr" x="763.75" xml:space="preserve" y="56" zvalue="38">35kV遮龙线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,709.5,388.25) scale(1,1) translate(0,0)" writing-mode="lr" x="709.5" xml:space="preserve" y="392.75" zvalue="45">381</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,686.062,622.125) scale(1,1) translate(0,0)" writing-mode="lr" x="686.0625" xml:space="preserve" y="626.625" zvalue="46">#1厂用变   250kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,600.625,523.75) scale(1,1) translate(0,0)" writing-mode="lr" x="600.63" xml:space="preserve" y="528.25" zvalue="54">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1153.38,526) scale(1,1) translate(0,0)" writing-mode="lr" x="1153.38" xml:space="preserve" y="530.5" zvalue="57">#1主变  3150kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1070,387.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1070" xml:space="preserve" y="391.75" zvalue="59">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1071.5,616.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1071.5" xml:space="preserve" y="620.75" zvalue="61">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,960.625,518.75) scale(1,1) translate(0,0)" writing-mode="lr" x="960.63" xml:space="preserve" y="523.25" zvalue="68">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1252.5,647) scale(1,1) translate(0,0)" writing-mode="lr" x="1252.5" xml:space="preserve" y="651.5" zvalue="79">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,974.929,757.857) scale(1,1) translate(0,0)" writing-mode="lr" x="974.9299999999999" xml:space="preserve" y="762.36" zvalue="81">061</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,941,893.679) scale(1,1) translate(0,0)" writing-mode="lr" x="941" xml:space="preserve" y="898.1785714285716" zvalue="82">#1发电机   3000kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1212.07,750.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1212.07" xml:space="preserve" y="755.21" zvalue="87">062</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1181,876.536) scale(1,1) translate(0,0)" writing-mode="lr" x="1181" xml:space="preserve" y="881.0357142857144" zvalue="88">#2发电机   3000kW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,141.175,1023) scale(1,1) translate(0,0)" writing-mode="lr" x="141.17" xml:space="preserve" y="1029" zvalue="121">杨立超</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="73.56" x="43.47" y="191.36" zvalue="22"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="32">
   <path class="kv35" d="M 617.5 315.5 L 1295 315.5" stroke-width="4" zvalue="33"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674248228868" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674248228868"/></metadata>
  <path d="M 617.5 315.5 L 1295 315.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv10" d="M 896 681 L 1264 681" stroke-width="4" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674248294404" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674248294404"/></metadata>
  <path d="M 896 681 L 1264 681" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="35">
   <use class="kv35" height="20" transform="rotate(0,766.562,254.25) scale(2.75,2.75) translate(-479.062,-144.295)" width="10" x="752.8125" xlink:href="#Breaker:小车断路器_0" y="226.75" zvalue="35"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924530601989" ObjectName="35kV遮龙线361断路器"/>
   <cge:TPSR_Ref TObjectID="6473924530601989"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,766.562,254.25) scale(2.75,2.75) translate(-479.062,-144.295)" width="10" x="752.8125" y="226.75"/></g>
  <g id="37">
   <use class="kv35" height="26" transform="rotate(0,766.563,162.375) scale(1.83894,-2.45192) translate(-341.323,-209.724)" width="20" x="748.1730772165151" xlink:href="#Breaker:小车开关带避雷器_0" y="130.5" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924530667525" ObjectName="35kV遮龙线362断路器"/>
   <cge:TPSR_Ref TObjectID="6473924530667525"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,766.563,162.375) scale(1.83894,-2.45192) translate(-341.323,-209.724)" width="20" x="748.1730772165151" y="130.5"/></g>
  <g id="47">
   <use class="kv35" height="20" transform="rotate(0,681.25,389.25) scale(2.75,2.75) translate(-424.773,-230.205)" width="10" x="667.5" xlink:href="#Breaker:小车断路器_0" y="361.75" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924530733061" ObjectName="#1厂用变381断路器"/>
   <cge:TPSR_Ref TObjectID="6473924530733061"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,681.25,389.25) scale(2.75,2.75) translate(-424.773,-230.205)" width="10" x="667.5" y="361.75"/></g>
  <g id="64">
   <use class="kv35" height="20" transform="rotate(0,1044.25,388.25) scale(2.75,2.75) translate(-655.773,-229.568)" width="10" x="1030.5" xlink:href="#Breaker:小车断路器_0" y="360.75" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924530798597" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924530798597"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1044.25,388.25) scale(2.75,2.75) translate(-655.773,-229.568)" width="10" x="1030.5" y="360.75"/></g>
  <g id="66">
   <use class="kv10" height="20" transform="rotate(0,1045.25,617.25) scale(2.75,2.75) translate(-656.409,-375.295)" width="10" x="1031.5" xlink:href="#Breaker:小车断路器_0" y="589.75" zvalue="60"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924530864133" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924530864133"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1045.25,617.25) scale(2.75,2.75) translate(-656.409,-375.295)" width="10" x="1031.5" y="589.75"/></g>
  <g id="86">
   <use class="kv10" height="20" transform="rotate(0,941.429,755.857) scale(2.2,2.2) translate(-507.506,-400.286)" width="10" x="930.4285714285714" xlink:href="#Breaker:小车断路器_0" y="733.8571428571429" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924530929669" ObjectName="#1发电机061断路器"/>
   <cge:TPSR_Ref TObjectID="6473924530929669"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,941.429,755.857) scale(2.2,2.2) translate(-507.506,-400.286)" width="10" x="930.4285714285714" y="733.8571428571429"/></g>
  <g id="97">
   <use class="kv10" height="20" transform="rotate(0,1178.57,748.714) scale(2.2,2.2) translate(-636.857,-396.39)" width="10" x="1167.571428571429" xlink:href="#Breaker:小车断路器_0" y="726.7142857142857" zvalue="85"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924530995205" ObjectName="#2发电机062断路器"/>
   <cge:TPSR_Ref TObjectID="6473924530995205"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1178.57,748.714) scale(2.2,2.2) translate(-636.857,-396.39)" width="10" x="1167.571428571429" y="726.7142857142857"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="41">
   <use class="kv35" height="40" transform="rotate(0,832.5,250.5) scale(1.25,1.25) translate(-161.5,-45.1)" width="40" x="807.5" xlink:href="#Accessory:线路PT11带避雷器_0" y="225.5" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449882423301" ObjectName="35kV遮龙线线路PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,832.5,250.5) scale(1.25,1.25) translate(-161.5,-45.1)" width="40" x="807.5" y="225.5"/></g>
  <g id="53">
   <use class="kv35" height="20" transform="rotate(0,648.062,491.562) scale(1.84375,1.84375) translate(-288.133,-216.515)" width="20" x="629.625" xlink:href="#Accessory:线路PT3_0" y="473.125" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449882554373" ObjectName="#1厂用变避雷器1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,648.062,491.562) scale(1.84375,1.84375) translate(-288.133,-216.515)" width="20" x="629.625" y="473.125"/></g>
  <g id="54">
   <use class="kv35" height="20" transform="rotate(0,732.312,464.656) scale(1.84375,1.84375) translate(-326.689,-204.202)" width="20" x="713.875" xlink:href="#Accessory:线路PT3_0" y="446.21875" zvalue="49"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449882619909" ObjectName="#1厂用变避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,732.312,464.656) scale(1.84375,1.84375) translate(-326.689,-204.202)" width="20" x="713.875" y="446.21875"/></g>
  <g id="74">
   <use class="kv35" height="20" transform="rotate(0,1002.06,489.562) scale(1.84375,1.84375) translate(-450.133,-215.6)" width="20" x="983.625" xlink:href="#Accessory:线路PT3_0" y="471.125" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449882947589" ObjectName="#1主变避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1002.06,489.562) scale(1.84375,1.84375) translate(-450.133,-215.6)" width="20" x="983.625" y="471.125"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="42">
   <path class="kv35" d="M 766.56 279 L 766.56 315.5" stroke-width="1" zvalue="39"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@1" LinkObjectIDznd="32@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 766.56 279 L 766.56 315.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv35" d="M 766.56 228.81 L 766.56 194.25" stroke-width="1" zvalue="41"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="37@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 766.56 228.81 L 766.56 194.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv35" d="M 766.56 130.5 L 766.56 92.49" stroke-width="1" zvalue="42"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@1" LinkObjectIDznd="39@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 766.56 130.5 L 766.56 92.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv35" d="M 832.5 226.65 L 832.5 211.53 L 766.56 211.53" stroke-width="1" zvalue="43"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="44" MaxPinNum="2"/>
   </metadata>
  <path d="M 832.5 226.65 L 832.5 211.53 L 766.56 211.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv35" d="M 681.25 363.81 L 681.25 315.5" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="32@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 681.25 363.81 L 681.25 315.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv35" d="M 682.5 536.75 L 682.5 414" stroke-width="1" zvalue="51"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="47@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 682.5 536.75 L 682.5 414" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv35" d="M 732.31 451.75 L 682.5 451.75" stroke-width="1" zvalue="52"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@0" LinkObjectIDznd="56" MaxPinNum="2"/>
   </metadata>
  <path d="M 732.31 451.75 L 682.5 451.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv35" d="M 1044.25 362.81 L 1044.25 315.5" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@0" LinkObjectIDznd="32@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1044.25 362.81 L 1044.25 315.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv35" d="M 1044.25 413 L 1044.25 495.06" stroke-width="1" zvalue="71"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@1" LinkObjectIDznd="62@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1044.25 413 L 1044.25 495.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv10" d="M 1044.75 556.56 L 1044.75 591.81" stroke-width="1" zvalue="72"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@1" LinkObjectIDznd="66@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1044.75 556.56 L 1044.75 591.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv35" d="M 961.31 475.88 L 961.31 451 L 1044.25 451" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@0" LinkObjectIDznd="76" MaxPinNum="2"/>
   </metadata>
  <path d="M 961.31 475.88 L 961.31 451 L 1044.25 451" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv35" d="M 1002.06 476.66 L 1002.06 451" stroke-width="1" zvalue="77"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="81" MaxPinNum="2"/>
   </metadata>
  <path d="M 1002.06 476.66 L 1002.06 451" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv10" d="M 1045.25 642 L 1045.25 681" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@1" LinkObjectIDznd="83@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1045.25 642 L 1045.25 681" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv10" d="M 941.43 735.51 L 941.43 681" stroke-width="1" zvalue="82"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="83@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 941.43 735.51 L 941.43 681" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv10" d="M 941.43 775.66 L 941.43 824.88" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@1" LinkObjectIDznd="88@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 941.43 775.66 L 941.43 824.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv10" d="M 1178.57 728.36 L 1178.57 681" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="83@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1178.57 728.36 L 1178.57 681" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv10" d="M 1178.57 768.51 L 1178.57 803.45" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@1" LinkObjectIDznd="96@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1178.57 768.51 L 1178.57 803.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv35" d="M 603.31 477.88 L 603.31 451.08 L 682.5 451.08" stroke-width="1" zvalue="91"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@0" LinkObjectIDznd="56" MaxPinNum="2"/>
   </metadata>
  <path d="M 603.31 477.88 L 603.31 451.08 L 682.5 451.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv35" d="M 648.06 478.66 L 648.06 451.08" stroke-width="1" zvalue="93"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="98" MaxPinNum="2"/>
   </metadata>
  <path d="M 648.06 478.66 L 648.06 451.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="49">
   <use class="kv35" height="35" transform="rotate(0,682.5,554.25) scale(1.25,1.25) translate(-132.5,-106.475)" width="32" x="662.5" xlink:href="#EnergyConsumer:站用变13_0" y="532.375" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449882488837" ObjectName="#1厂用变"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,682.5,554.25) scale(1.25,1.25) translate(-132.5,-106.475)" width="32" x="662.5" y="532.375"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="58">
   <use class="kv35" height="20" transform="rotate(0,603.25,489.75) scale(1.25,1.25) translate(-119.4,-95.45)" width="10" x="597" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="477.25" zvalue="53"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449882750981" ObjectName="#1厂用变38117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449882750981"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,603.25,489.75) scale(1.25,1.25) translate(-119.4,-95.45)" width="10" x="597" y="477.25"/></g>
  <g id="73">
   <use class="kv35" height="20" transform="rotate(0,961.25,487.75) scale(1.25,1.25) translate(-191,-95.05)" width="10" x="955" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="475.25" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449882882053" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449882882053"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,961.25,487.75) scale(1.25,1.25) translate(-191,-95.05)" width="10" x="955" y="475.25"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="62">
   <g id="620">
    <use class="kv35" height="50" transform="rotate(0,1044.75,525.75) scale(1.25,1.25) translate(-205.2,-98.9)" width="30" x="1026" xlink:href="#PowerTransformer2:586_0" y="494.5" zvalue="56"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874444177411" ObjectName="35"/>
    </metadata>
   </g>
   <g id="621">
    <use class="kv10" height="50" transform="rotate(0,1044.75,525.75) scale(1.25,1.25) translate(-205.2,-98.9)" width="30" x="1026" xlink:href="#PowerTransformer2:586_1" y="494.5" zvalue="56"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874444242947" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399454359555" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399454359555"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1044.75,525.75) scale(1.25,1.25) translate(-205.2,-98.9)" width="30" x="1026" y="494.5"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="88">
   <use class="kv10" height="30" transform="rotate(0,940,847) scale(1.5,1.5) translate(-305.833,-274.833)" width="30" x="917.5" xlink:href="#Generator:发电机_0" y="824.5" zvalue="81"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449883013125" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449883013125"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,940,847) scale(1.5,1.5) translate(-305.833,-274.833)" width="30" x="917.5" y="824.5"/></g>
  <g id="96">
   <use class="kv10" height="30" transform="rotate(0,1178.57,825.571) scale(1.5,1.5) translate(-385.357,-267.69)" width="30" x="1156.071428571428" xlink:href="#Generator:发电机_0" y="803.0714285714287" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449883078661" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449883078661"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1178.57,825.571) scale(1.5,1.5) translate(-385.357,-267.69)" width="30" x="1156.071428571428" y="803.0714285714287"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="101" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,875.134,24.7321) scale(1,1) translate(0,0)" writing-mode="lr" x="874.67" xml:space="preserve" y="29.51" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125812043780" ObjectName="P"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="102" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,875.134,54.5893) scale(1,1) translate(0,0)" writing-mode="lr" x="874.67" xml:space="preserve" y="59.37" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125812109316" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="103" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,876.562,87.3036) scale(1,1) translate(0,0)" writing-mode="lr" x="876.09" xml:space="preserve" y="92.08" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125812174852" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="104" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,929.071,930.429) scale(1,1) translate(0,0)" writing-mode="lr" x="928.49" xml:space="preserve" y="936.71" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125818335238" ObjectName="P"/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="105" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,930.5,960.286) scale(1,1) translate(0,0)" writing-mode="lr" x="929.92" xml:space="preserve" y="966.5599999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125818400774" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="106">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="106" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,931.929,988.714) scale(1,1) translate(0,0)" writing-mode="lr" x="931.35" xml:space="preserve" y="994.99" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125818466310" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="107" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,942.857,1018.86) scale(1,1) translate(0,0)" writing-mode="lr" x="942.28" xml:space="preserve" y="1025.13" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125818531846" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="108">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="108" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1170.5,917.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1169.92" xml:space="preserve" y="923.85" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125818990598" ObjectName="P"/>
   </metadata>
  </g>
  <g id="109">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="109" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1179.07,951.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1178.49" xml:space="preserve" y="957.99" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125819056134" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="110">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="110" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1177.64,978.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1177.06" xml:space="preserve" y="984.99" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125819121670" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="111" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1188.57,1006) scale(1,1) translate(0,0)" writing-mode="lr" x="1187.99" xml:space="preserve" y="1012.28" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125819187206" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="112">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="112" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1314.71,670.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1314.25" xml:space="preserve" y="674.92" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125817548806" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="113">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="113" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,627.643,294.643) scale(1,1) translate(0,0)" writing-mode="lr" x="627.17" xml:space="preserve" y="299.42" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125810733062" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="114">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="114" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1126.68,407.571) scale(1,1) translate(0,-1.73227e-13)" writing-mode="lr" x="1126.1" xml:space="preserve" y="413.85" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125814206470" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="115" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1126.68,440.286) scale(1,1) translate(0,0)" writing-mode="lr" x="1126.1" xml:space="preserve" y="446.56" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125814272004" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="116" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1132.39,474.429) scale(1,1) translate(0,1.01459e-13)" writing-mode="lr" x="1131.81" xml:space="preserve" y="480.71" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125814468612" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="117" prefix=" 油温:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,956.714,551.893) scale(1,1) translate(0,0)" writing-mode="lr" x="956.14" xml:space="preserve" y="558.17" zvalue="1"> 油温:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125814665222" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="118">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="118" prefix="档位:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,951.357,584.893) scale(1,1) translate(0,1.25987e-13)" writing-mode="lr" x="950.88" xml:space="preserve" y="591.17" zvalue="1">档位:ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125814730758" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="834">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="834" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.417,280.917) scale(1,1) translate(0,0)" writing-mode="lr" x="137.57" xml:space="preserve" y="287.19" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125820170246" ObjectName=""/>
   </metadata>
  </g>
  <g id="119">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="119" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,295.75,279.25) scale(1,1) translate(0,0)" writing-mode="lr" x="295.9" xml:space="preserve" y="285.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125820170246" ObjectName=""/>
   </metadata>
  </g>
  <g id="120">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="120" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,131.583,350.083) scale(1,1) translate(0,0)" writing-mode="lr" x="131.74" xml:space="preserve" y="356.36" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125820170246" ObjectName=""/>
   </metadata>
  </g>
  <g id="121">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="121" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,301.583,353.417) scale(1,1) translate(0,0)" writing-mode="lr" x="301.74" xml:space="preserve" y="359.69" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125820170246" ObjectName=""/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,329,201.733) scale(0.708333,0.665547) translate(131.096,96.359)" width="30" x="318.38" xlink:href="#State:红绿圆(方形)_0" y="191.75" zvalue="125"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374890827779" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329,201.733) scale(0.708333,0.665547) translate(131.096,96.359)" width="30" x="318.38" y="191.75"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,233.375,201.733) scale(0.708333,0.665547) translate(91.7206,96.359)" width="30" x="222.75" xlink:href="#State:红绿圆(方形)_0" y="191.75" zvalue="126"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,233.375,201.733) scale(0.708333,0.665547) translate(91.7206,96.359)" width="30" x="222.75" y="191.75"/></g>
  <g id="830">
   <use height="30" transform="rotate(0,319.812,135.464) scale(1.27778,1.03333) translate(-57.0245,-3.8698)" width="90" x="262.31" xlink:href="#State:全站检修_0" y="119.96" zvalue="130"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549589508098" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,319.812,135.464) scale(1.27778,1.03333) translate(-57.0245,-3.8698)" width="90" x="262.31" y="119.96"/></g>
 </g>
</svg>