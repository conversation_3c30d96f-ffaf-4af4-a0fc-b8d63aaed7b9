<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549583085570" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_0" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3" x2="7" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.25" x2="5.083333333333332" y1="7.083333333333336" y2="19.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_1" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.833333333333333" x2="6.916666666666667" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.05" x2="5.05" y1="5.000000000000004" y2="22.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_2" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.166666666666668" x2="1.333333333333333" y1="5.083333333333332" y2="24.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1" x2="8.833333333333334" y1="4.999999999999998" y2="24.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="3" y1="23" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="12" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="12" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0.3333333333333357" y2="12.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="12" y2="12"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1754.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2334.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸_0" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6.000733890582183" xlink:href="#terminal" y="1.25365326133502"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="16.81276518533035"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.083333333333333" x2="6.083333333333333" y1="10.08333333333333" y2="1.083333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="16.81354468322919" y2="9.527777777777771"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.083333333333333" x2="0.8263923524522143" y1="16.81435756744399" y2="9.527777777777771"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.083333333333332" x2="0.8000000000000016" y1="9.9106081390032" y2="2.616666666666669"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.097965249199815" x2="11.38333333333333" y1="9.903292181069959" y2="2.61666666666667"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸_1" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6.000733890582183" xlink:href="#terminal" y="1.25365326133502"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="16.81276518533035"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="16.81354468322919" y2="9.527777777777771"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.083333333333333" x2="0.8263923524522143" y1="16.81435756744399" y2="9.527777777777771"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6" x2="6" y1="0.9999999999999982" y2="16.80704160951075"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸_2" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6.000733890582183" xlink:href="#terminal" y="1.25365326133502"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="16.81276518533035"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.658956967941879" x2="2.492290301275209" y1="5.063824112178023" y2="13.23049077884469"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.325623634608544" x2="9.825623634608544" y1="5.313824112178023" y2="12.98049077884469"/>
  </symbol>
  <symbol id="Accessory:RT1122_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="22.91666666666667" y2="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="18.16666666666666" y2="20.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="18.16666666666666" y2="20.16666666666666"/>
   <path d="M 13 11 L 17 11 L 15 8 L 13 11 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="15.41666666666666" y2="18.16666666666666"/>
   <ellipse cx="14.95" cy="18.02" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.95" cy="9.92" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV古里卡电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="50.21" xlink:href="logo.png" y="47.57"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.214,79.5714) scale(1,1) translate(0,0)" writing-mode="lr" x="199.21" xml:space="preserve" y="83.06999999999999" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,201.714,77.2618) scale(1,1) translate(0,0)" writing-mode="lr" x="201.71" xml:space="preserve" y="86.26000000000001" zvalue="5">35kV古里卡电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="203" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,84.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="48.19" y="175.25" zvalue="361"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="84.63" xml:space="preserve" y="191.75" zvalue="361">信号一览</text>
  <line fill="none" id="100" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384.2142857142856" x2="384.2142857142856" y1="15.57142857142867" y2="1045.571428571429" zvalue="6"/>
  <line fill="none" id="98" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="151.441921185511" y2="151.441921185511" zvalue="8"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,648.319,306.73) scale(1,1) translate(0,0)" writing-mode="lr" x="648.3200000000001" xml:space="preserve" y="311.23" zvalue="33">371</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" x="569.7734375" xml:space="preserve" y="468.0511475329811" zvalue="35">#1主变      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="569.7734375" xml:space="preserve" y="484.0511475329811" zvalue="35">16000KVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,505.84,617.341) scale(1,1) translate(0,0)" writing-mode="lr" x="505.84" xml:space="preserve" y="621.84" zvalue="38">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" x="628.4609375" xml:space="preserve" y="919.0087240134186" zvalue="40">#3发电机         </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="628.4609375" xml:space="preserve" y="935.0087240134186" zvalue="40">6200KW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1276.25,426.925) scale(1,1) translate(0,0)" writing-mode="lr" x="1276.25" xml:space="preserve" y="431.42" zvalue="42">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,658.74,571) scale(1,1) translate(0,0)" writing-mode="lr" x="658.74" xml:space="preserve" y="575.5" zvalue="47">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,707.222,256.889) scale(1,1) translate(1.24207e-12,0)" writing-mode="lr" x="707.22" xml:space="preserve" y="261.39" zvalue="53">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,652.932,239.675) scale(1,1) translate(0,0)" writing-mode="lr" x="652.9299999999999" xml:space="preserve" y="244.17" zvalue="56">3716</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,712.333,187) scale(1,1) translate(0,0)" writing-mode="lr" x="712.33" xml:space="preserve" y="191.5" zvalue="58">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,598.225,714) scale(1,1) translate(0,0)" writing-mode="lr" x="598.23" xml:space="preserve" y="718.5" zvalue="68">673</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,750.773,741.842) scale(1,1) translate(0,0)" writing-mode="lr" x="750.77" xml:space="preserve" y="746.34" zvalue="75">6932</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,627.53,123.25) scale(1,1) translate(0,0)" writing-mode="lr" x="627.53" xml:space="preserve" y="127.75" zvalue="124">35kV古缅线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,709.667,374.333) scale(1,1) translate(-1.40321e-12,0)" writing-mode="lr" x="709.67" xml:space="preserve" y="378.83" zvalue="153">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,651.913,363.008) scale(1,1) translate(0,0)" writing-mode="lr" x="651.91" xml:space="preserve" y="367.51" zvalue="155">3711</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,708.556,317.444) scale(1,1) translate(0,0)" writing-mode="lr" x="708.5599999999999" xml:space="preserve" y="321.94" zvalue="157">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,598.046,231.675) scale(1,1) translate(0,0)" writing-mode="lr" x="598.05" xml:space="preserve" y="236.17" zvalue="167">3719</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,502.778,184.778) scale(1,1) translate(0,0)" writing-mode="lr" x="502.78" xml:space="preserve" y="189.28" zvalue="173">90</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,503.889,290.778) scale(1,1) translate(0,0)" writing-mode="lr" x="503.89" xml:space="preserve" y="295.28" zvalue="175">97</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,503.836,814.111) scale(1,1) translate(0,0)" writing-mode="lr" x="503.84" xml:space="preserve" y="818.61" zvalue="180">6931</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,752.042,847.269) scale(1,1) translate(0,0)" writing-mode="lr" x="752.04" xml:space="preserve" y="851.77" zvalue="186">6933</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1007.26,433.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1007.26" xml:space="preserve" y="438.11" zvalue="194">6.3kV母线PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,978.668,560.111) scale(1,1) translate(-2.13977e-13,0)" writing-mode="lr" x="978.67" xml:space="preserve" y="564.61" zvalue="195">6901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="middle" x="1068.4453125" xml:space="preserve" y="927.0087240134186" zvalue="201">#2发电机         </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1068.4453125" xml:space="preserve" y="943.0087240134186" zvalue="201">3200KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1038.23,714) scale(1,1) translate(0,0)" writing-mode="lr" x="1038.23" xml:space="preserve" y="718.5" zvalue="204">672</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1190.27,741.842) scale(1,1) translate(0,0)" writing-mode="lr" x="1190.27" xml:space="preserve" y="746.34" zvalue="206">6922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,938.668,814.111) scale(1,1) translate(0,0)" writing-mode="lr" x="938.67" xml:space="preserve" y="818.61" zvalue="210">6921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1193.04,847.269) scale(1,1) translate(0,0)" writing-mode="lr" x="1193.04" xml:space="preserve" y="851.77" zvalue="215">6923</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="216" stroke="rgb(255,255,255)" text-anchor="middle" x="1524.4453125" xml:space="preserve" y="923.0087240134186" zvalue="223">#1发电机         </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="216" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1524.4453125" xml:space="preserve" y="939.0087240134186" zvalue="223">3200KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="215" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1489.61,714) scale(1,1) translate(0,0)" writing-mode="lr" x="1489.61" xml:space="preserve" y="718.5" zvalue="226">671</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="214" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1644.05,741.842) scale(1,1) translate(0,0)" writing-mode="lr" x="1644.05" xml:space="preserve" y="746.34" zvalue="228">6932</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="213" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1399.84,814.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1399.84" xml:space="preserve" y="818.61" zvalue="232">6911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="210" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1645.58,847.269) scale(1,1) translate(0,0)" writing-mode="lr" x="1645.58" xml:space="preserve" y="851.77" zvalue="237">6913</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1238.37,562.219) scale(1,1) translate(-2.70913e-13,0)" writing-mode="lr" x="1238.37" xml:space="preserve" y="566.72" zvalue="245">681</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1485.14,431.369) scale(1,1) translate(0,0)" writing-mode="lr" x="1485.14" xml:space="preserve" y="435.87" zvalue="249">#2近区变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="237" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1447.26,566.664) scale(1,1) translate(0,0)" writing-mode="lr" x="1447.26" xml:space="preserve" y="571.16" zvalue="251">682</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="268" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="337">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="265" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="338">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="339">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="263" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="340">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="341">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="343">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="191.75" zvalue="344">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="191.75" zvalue="345">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.054,956) scale(1,1) translate(0,0)" writing-mode="lr" x="237.05" xml:space="preserve" y="962" zvalue="346"> GuLiKa-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,327.054,988) scale(1,1) translate(0,0)" writing-mode="lr" x="327.05" xml:space="preserve" y="994" zvalue="348">20200806</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="349">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="350">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="249" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63.2361,336.361) scale(1,1) translate(0,0)" writing-mode="lr" x="63.24" xml:space="preserve" y="340.86" zvalue="352">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="362">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="363">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="195" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="366">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="368">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="370">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="372">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="374">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="182" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="376">厂用电率</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="48.19" y="175.25" zvalue="361"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BreakerClass">
  <g id="418">
   <use class="kv35" height="20" transform="rotate(0,627.541,307.73) scale(1.22222,1.11111) translate(-112.987,-29.6619)" width="10" x="621.4303734218488" xlink:href="#Breaker:开关_0" y="296.6190476190475" zvalue="32"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924486561797" ObjectName="35kV古缅线371断路器"/>
   <cge:TPSR_Ref TObjectID="6473924486561797"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,627.541,307.73) scale(1.22222,1.11111) translate(-112.987,-29.6619)" width="10" x="621.4303734218488" y="296.6190476190475"/></g>
  <g id="453">
   <use class="v6300" height="20" transform="rotate(0,627.53,567.33) scale(2.28303,2.28303) translate(-346.248,-306.002)" width="10" x="616.1145221109928" xlink:href="#Breaker:小车断路器_0" y="544.5" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924486496261" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924486496261"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,627.53,567.33) scale(2.28303,2.28303) translate(-346.248,-306.002)" width="10" x="616.1145221109928" y="544.5"/></g>
  <g id="198">
   <use class="v6300" height="20" transform="rotate(0,626.475,713) scale(2.75,2.75) translate(-389.916,-436.227)" width="10" x="612.7252143314928" xlink:href="#Breaker:小车断路器_0" y="685.5" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924486430725" ObjectName="#3发电机673断路器"/>
   <cge:TPSR_Ref TObjectID="6473924486430725"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,626.475,713) scale(2.75,2.75) translate(-389.916,-436.227)" width="10" x="612.7252143314928" y="685.5"/></g>
  <g id="208">
   <use class="v6300" height="20" transform="rotate(0,1066.48,713) scale(2.75,2.75) translate(-669.916,-436.227)" width="10" x="1052.725214331493" xlink:href="#Breaker:小车断路器_0" y="685.5" zvalue="202"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924486627333" ObjectName="#2发电机672断路器"/>
   <cge:TPSR_Ref TObjectID="6473924486627333"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1066.48,713) scale(2.75,2.75) translate(-669.916,-436.227)" width="10" x="1052.725214331493" y="685.5"/></g>
  <g id="231">
   <use class="v6300" height="20" transform="rotate(0,1522.48,713) scale(2.75,2.75) translate(-960.098,-436.227)" width="10" x="1508.725214331493" xlink:href="#Breaker:小车断路器_0" y="685.5" zvalue="224"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924486692869" ObjectName="#1发电机671断路器"/>
   <cge:TPSR_Ref TObjectID="6473924486692869"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1522.48,713) scale(2.75,2.75) translate(-960.098,-436.227)" width="10" x="1508.725214331493" y="685.5"/></g>
  <g id="233">
   <use class="v6300" height="20" transform="rotate(0,1274.28,557.664) scale(2.11637,2.11637) translate(-666.59,-283)" width="10" x="1263.69594049908" xlink:href="#Breaker:小车断路器_0" y="536.5" zvalue="244"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924486758405" ObjectName="#1站用变681断路器"/>
   <cge:TPSR_Ref TObjectID="6473924486758405"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1274.28,557.664) scale(2.11637,2.11637) translate(-666.59,-283)" width="10" x="1263.69594049908" y="536.5"/></g>
  <g id="241">
   <use class="v6300" height="20" transform="rotate(0,1483.17,562.108) scale(2.11637,2.11637) translate(-776.777,-285.344)" width="10" x="1472.584829387969" xlink:href="#Breaker:小车断路器_0" y="540.9444444444445" zvalue="250"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924486823941" ObjectName="#2近区变682断路器"/>
   <cge:TPSR_Ref TObjectID="6473924486823941"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1483.17,562.108) scale(2.11637,2.11637) translate(-776.777,-285.344)" width="10" x="1472.584829387969" y="540.9444444444445"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="414">
   <g id="4140">
    <use class="kv35" height="60" transform="rotate(0,627.53,469.327) scale(1.6125,1.54462) translate(-226.114,-149.143)" width="40" x="595.28" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="422.99" zvalue="34"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874426351620" ObjectName="35"/>
    </metadata>
   </g>
   <g id="4141">
    <use class="v6300" height="60" transform="rotate(0,627.53,469.327) scale(1.6125,1.54462) translate(-226.114,-149.143)" width="40" x="595.28" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="422.99" zvalue="34"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874426548228" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399445381124" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399445381124"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,627.53,469.327) scale(1.6125,1.54462) translate(-226.114,-149.143)" width="40" x="595.28" y="422.99"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="410">
   <path class="v6300" d="M 461.51 642.79 L 1694.51 642.79" stroke-width="6" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674237087749" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674237087749"/></metadata>
  <path d="M 461.51 642.79 L 1694.51 642.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="212">
   <use class="v6300" height="30" transform="rotate(0,626.475,883.307) scale(1.58467,1.58467) translate(-222.371,-317.13)" width="30" x="602.7051208787044" xlink:href="#Generator:发电机_0" y="859.5367361713461" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449659666438" ObjectName="#3发电机"/>
   <cge:TPSR_Ref TObjectID="6192449659666438"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,626.475,883.307) scale(1.58467,1.58467) translate(-222.371,-317.13)" width="30" x="602.7051208787044" y="859.5367361713461"/></g>
  <g id="209">
   <use class="v6300" height="30" transform="rotate(0,1066.48,891.307) scale(1.58467,1.58467) translate(-384.711,-320.082)" width="30" x="1042.705120878705" xlink:href="#Generator:发电机_0" y="867.5367361713461" zvalue="200"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449661304838" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449661304838"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1066.48,891.307) scale(1.58467,1.58467) translate(-384.711,-320.082)" width="30" x="1042.705120878705" y="867.5367361713461"/></g>
  <g id="232">
   <use class="v6300" height="30" transform="rotate(0,1522.48,887.307) scale(1.58467,1.58467) translate(-552.955,-318.606)" width="30" x="1498.705120878704" xlink:href="#Generator:发电机_0" y="863.5367361713461" zvalue="222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449661763589" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449661763589"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1522.48,887.307) scale(1.58467,1.58467) translate(-552.955,-318.606)" width="30" x="1498.705120878704" y="863.5367361713461"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="399">
   <use class="v6300" height="30" transform="rotate(0,1274.12,471.925) scale(1.69643,-1.70833) translate(-513.311,-737.548)" width="28" x="1250.371922478492" xlink:href="#EnergyConsumer:站用变DY接地_0" y="446.2996031746032" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449659600902" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1274.12,471.925) scale(1.69643,-1.70833) translate(-513.311,-737.548)" width="28" x="1250.371922478492" y="446.2996031746032"/></g>
  <g id="242">
   <use class="v6300" height="30" transform="rotate(0,1483.01,476.369) scale(1.69643,-1.70833) translate(-599.065,-744.594)" width="28" x="1459.26081136738" xlink:href="#EnergyConsumer:站用变DY接地_0" y="450.7440476190476" zvalue="248"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449661829125" ObjectName="#2近区变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1483.01,476.369) scale(1.69643,-1.70833) translate(-599.065,-744.594)" width="28" x="1459.26081136738" y="450.7440476190476"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="207">
   <use class="kv35" height="30" transform="rotate(270,711.889,279) scale(-1.11111,1.11111) translate(-1352.03,-26.2333)" width="10" x="706.3333333333334" xlink:href="#GroundDisconnector:配网地刀_0" y="262.3333333333333" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449659535366" ObjectName="35kV古缅线37160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449659535366"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,711.889,279) scale(-1.11111,1.11111) translate(-1352.03,-26.2333)" width="10" x="706.3333333333334" y="262.3333333333333"/></g>
  <g id="204">
   <use class="kv35" height="30" transform="rotate(270,715.667,208.556) scale(-1.11111,1.11111) translate(-1359.21,-19.1889)" width="10" x="710.1111111111112" xlink:href="#GroundDisconnector:配网地刀_0" y="191.8888888888888" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449659338758" ObjectName="35kV古缅线37167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449659338758"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,715.667,208.556) scale(-1.11111,1.11111) translate(-1359.21,-19.1889)" width="10" x="710.1111111111112" y="191.8888888888888"/></g>
  <g id="105">
   <use class="kv35" height="30" transform="rotate(270,709.667,398.111) scale(-1.11111,1.11111) translate(-1347.81,-38.1444)" width="10" x="704.1111111111112" xlink:href="#GroundDisconnector:配网地刀_0" y="381.4444444444445" zvalue="152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449659797509" ObjectName="35kV古缅线37110接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449659797509"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,709.667,398.111) scale(-1.11111,1.11111) translate(-1347.81,-38.1444)" width="10" x="704.1111111111112" y="381.4444444444445"/></g>
  <g id="107">
   <use class="kv35" height="30" transform="rotate(270,708.556,336.778) scale(-1.11111,1.11111) translate(-1345.7,-32.0111)" width="10" x="703" xlink:href="#GroundDisconnector:配网地刀_0" y="320.111111111111" zvalue="156"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449659994117" ObjectName="35kV古缅线37117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449659994117"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,708.556,336.778) scale(-1.11111,1.11111) translate(-1345.7,-32.0111)" width="10" x="703" y="320.111111111111"/></g>
  <g id="125">
   <use class="kv35" height="30" transform="rotate(90,505,206.333) scale(1.11111,1.11111) translate(-49.9444,-18.9667)" width="10" x="499.4444444444445" xlink:href="#GroundDisconnector:配网地刀_0" y="189.6666666666667" zvalue="172"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449660256262" ObjectName="35kV古缅线37190接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449660256262"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,505,206.333) scale(1.11111,1.11111) translate(-49.9444,-18.9667)" width="10" x="499.4444444444445" y="189.6666666666667"/></g>
  <g id="126">
   <use class="kv35" height="30" transform="rotate(90,503.889,263.444) scale(1.11111,1.11111) translate(-49.8333,-24.6778)" width="10" x="498.3333333333334" xlink:href="#GroundDisconnector:配网地刀_0" y="246.7777777777777" zvalue="174"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449660387334" ObjectName="35kV古缅线37197接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449660387334"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,503.889,263.444) scale(1.11111,1.11111) translate(-49.8333,-24.6778)" width="10" x="498.3333333333334" y="246.7777777777777"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="206">
   <use class="kv35" height="30" transform="rotate(0,627.598,240.675) scale(-1.11111,-0.814815) translate(-1191.6,-538.826)" width="15" x="619.2649405153977" xlink:href="#Disconnector:刀闸_0" y="228.4523946217128" zvalue="54"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449659404294" ObjectName="35kV古缅线3716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449659404294"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,627.598,240.675) scale(-1.11111,-0.814815) translate(-1191.6,-538.826)" width="15" x="619.2649405153977" y="228.4523946217128"/></g>
  <g id="191">
   <use class="v6300" height="26" transform="rotate(0,718.023,739.424) scale(1.40192,-1.40192) translate(-203.441,-1261.64)" width="12" x="709.6116401027276" xlink:href="#Disconnector:手车刀闸2020_0" y="721.1990231990231" zvalue="74"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449659207686" ObjectName="#3发电机6932隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449659207686"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,718.023,739.424) scale(1.40192,-1.40192) translate(-203.441,-1261.64)" width="12" x="709.6116401027276" y="721.1990231990231"/></g>
  <g id="106">
   <use class="kv35" height="30" transform="rotate(0,627.691,364.008) scale(-1.11111,-0.814815) translate(-1191.78,-813.523)" width="15" x="619.3575931523364" xlink:href="#Disconnector:刀闸_0" y="351.7857279550461" zvalue="154"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449659863045" ObjectName="35kV古缅线3711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449659863045"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,627.691,364.008) scale(-1.11111,-0.814815) translate(-1191.78,-813.523)" width="15" x="619.3575931523364" y="351.7857279550461"/></g>
  <g id="119">
   <use class="kv35" height="30" transform="rotate(0,568.712,232.675) scale(-1.11111,-0.814815) translate(-1079.72,-521.008)" width="15" x="560.3790115379934" xlink:href="#Disconnector:刀闸_0" y="220.4523946217128" zvalue="166"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449660059654" ObjectName="35kV古缅线3719隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449660059654"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,568.712,232.675) scale(-1.11111,-0.814815) translate(-1079.72,-521.008)" width="15" x="560.3790115379934" y="220.4523946217128"/></g>
  <g id="132">
   <use class="v6300" height="18" transform="rotate(0,526.392,824) scale(1.11111,-2.09877) translate(-51.9725,-1206.72)" width="12" x="519.7251104919457" xlink:href="#Disconnector:单手车刀闸_0" y="805.1111111111112" zvalue="179"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449660518406" ObjectName="#3发电机6931隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449660518406"/></metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,526.392,824) scale(1.11111,-2.09877) translate(-51.9725,-1206.72)" width="12" x="519.7251104919457" y="805.1111111111112"/></g>
  <g id="140">
   <use class="v6300" height="26" transform="rotate(0,718.023,838.148) scale(1.40192,1.40192) translate(-203.441,-235.067)" width="12" x="709.6116401027276" xlink:href="#Disconnector:手车刀闸2020_0" y="819.9230769230769" zvalue="185"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449660715014" ObjectName="#3发电机6933隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449660715014"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,718.023,838.148) scale(1.40192,1.40192) translate(-203.441,-235.067)" width="12" x="709.6116401027276" y="819.9230769230769"/></g>
  <g id="147">
   <use class="v6300" height="18" transform="rotate(0,1021.39,564) scale(1.11111,2.09877) translate(-101.473,-285.382)" width="12" x="1014.725110491946" xlink:href="#Disconnector:单手车刀闸_0" y="545.1111111111112" zvalue="194"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449660780550" ObjectName="6.3kV母线PT6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449660780550"/></metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1021.39,564) scale(1.11111,2.09877) translate(-101.473,-285.382)" width="12" x="1014.725110491946" y="545.1111111111112"/></g>
  <g id="201">
   <use class="v6300" height="26" transform="rotate(0,1158.02,739.424) scale(1.40192,-1.40192) translate(-329.586,-1261.64)" width="12" x="1149.611640102728" xlink:href="#Disconnector:手车刀闸2020_0" y="721.1990231990231" zvalue="205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449661239302" ObjectName="#2发电机6922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449661239302"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1158.02,739.424) scale(1.40192,-1.40192) translate(-329.586,-1261.64)" width="12" x="1149.611640102728" y="721.1990231990231"/></g>
  <g id="196">
   <use class="v6300" height="18" transform="rotate(0,966.392,824) scale(1.11111,-2.09877) translate(-95.9725,-1206.72)" width="12" x="959.7251104919457" xlink:href="#Disconnector:单手车刀闸_0" y="805.1111111111112" zvalue="209"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449661108230" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449661108230"/></metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,966.392,824) scale(1.11111,-2.09877) translate(-95.9725,-1206.72)" width="12" x="959.7251104919457" y="805.1111111111112"/></g>
  <g id="179">
   <use class="v6300" height="26" transform="rotate(0,1158.02,838.148) scale(1.40192,1.40192) translate(-329.586,-235.067)" width="12" x="1149.611640102728" xlink:href="#Disconnector:手车刀闸2020_0" y="819.9230769230769" zvalue="214"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449660977158" ObjectName="#2发电机6923隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449660977158"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1158.02,838.148) scale(1.40192,1.40192) translate(-329.586,-235.067)" width="12" x="1149.611640102728" y="819.9230769230769"/></g>
  <g id="229">
   <use class="v6300" height="26" transform="rotate(0,1614.02,739.424) scale(1.40192,-1.40192) translate(-460.318,-1261.64)" width="12" x="1605.611640102728" xlink:href="#Disconnector:手车刀闸2020_0" y="721.1990231990231" zvalue="227"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449661698053" ObjectName="#1发电机6912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449661698053"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1614.02,739.424) scale(1.40192,-1.40192) translate(-460.318,-1261.64)" width="12" x="1605.611640102728" y="721.1990231990231"/></g>
  <g id="226">
   <use class="v6300" height="18" transform="rotate(0,1422.39,824) scale(1.11111,-2.09877) translate(-141.573,-1206.72)" width="12" x="1415.725110491946" xlink:href="#Disconnector:单手车刀闸_0" y="805.1111111111112" zvalue="231"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449661566982" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449661566982"/></metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1422.39,824) scale(1.11111,-2.09877) translate(-141.573,-1206.72)" width="12" x="1415.725110491946" y="805.1111111111112"/></g>
  <g id="222">
   <use class="v6300" height="26" transform="rotate(0,1614.02,838.148) scale(1.40192,1.40192) translate(-460.318,-235.067)" width="12" x="1605.611640102728" xlink:href="#Disconnector:手车刀闸2020_0" y="819.9230769230769" zvalue="236"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449661435910" ObjectName="#1发电机6913隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449661435910"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1614.02,838.148) scale(1.40192,1.40192) translate(-460.318,-235.067)" width="12" x="1605.611640102728" y="819.9230769230769"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="67">
   <path class="v6300" d="M 626.48 642.79 L 626.48 687.56" stroke-width="1" zvalue="67"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="410@5" LinkObjectIDznd="198@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.48 642.79 L 626.48 687.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="v6300" d="M 627.53 546.21 L 627.53 515.01" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="453@0" LinkObjectIDznd="414@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 627.53 546.21 L 627.53 515.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="v6300" d="M 626.48 737.75 L 626.48 859.93" stroke-width="1" zvalue="129"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@1" LinkObjectIDznd="212@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.48 737.75 L 626.48 859.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv35" d="M 627.59 423.83 L 627.59 375.83" stroke-width="1" zvalue="157"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@0" LinkObjectIDznd="106@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 627.59 423.83 L 627.59 375.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv35" d="M 627.62 351.99 L 627.62 318.34" stroke-width="1" zvalue="158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@1" LinkObjectIDznd="418@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 627.62 351.99 L 627.62 318.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv35" d="M 627.5 297.1 L 627.5 252.49" stroke-width="1" zvalue="159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="418@0" LinkObjectIDznd="206@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 627.5 297.1 L 627.5 252.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv35" d="M 627.53 228.66 L 627.53 184.52" stroke-width="1" zvalue="160"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@1" LinkObjectIDznd="156@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 627.53 228.66 L 627.53 184.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv35" d="M 693.75 398.1 L 627.59 398.1" stroke-width="1" zvalue="161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="108" MaxPinNum="2"/>
   </metadata>
  <path d="M 693.75 398.1 L 627.59 398.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv35" d="M 692.64 336.77 L 627.62 336.77" stroke-width="1" zvalue="162"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="109" MaxPinNum="2"/>
   </metadata>
  <path d="M 692.64 336.77 L 627.62 336.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv35" d="M 695.97 278.99 L 627.5 278.99" stroke-width="1" zvalue="163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="110" MaxPinNum="2"/>
   </metadata>
  <path d="M 695.97 278.99 L 627.5 278.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv35" d="M 699.75 208.54 L 627.53 208.54" stroke-width="1" zvalue="164"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="204@0" LinkObjectIDznd="111" MaxPinNum="2"/>
   </metadata>
  <path d="M 699.75 208.54 L 627.53 208.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv35" d="M 568.61 276.33 L 568.61 244.49" stroke-width="1" zvalue="168"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="119@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 568.61 276.33 L 568.61 244.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv35" d="M 627.53 194.67 L 568.64 194.67 L 568.64 220.66" stroke-width="1" zvalue="169"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111" LinkObjectIDznd="119@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 627.53 194.67 L 568.64 194.67 L 568.64 220.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="v6300" d="M 627.53 587.88 L 627.53 642.79" stroke-width="1" zvalue="170"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="453@1" LinkObjectIDznd="67" MaxPinNum="2"/>
   </metadata>
  <path d="M 627.53 587.88 L 627.53 642.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv35" d="M 520.92 206.32 L 568.64 206.32" stroke-width="1" zvalue="175"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="122" MaxPinNum="2"/>
   </metadata>
  <path d="M 520.92 206.32 L 568.64 206.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv35" d="M 519.8 263.43 L 568.61 263.43" stroke-width="1" zvalue="176"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="121" MaxPinNum="2"/>
   </metadata>
  <path d="M 519.8 263.43 L 568.61 263.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="v6300" d="M 718.14 871.01 L 718.14 856.33" stroke-width="1" zvalue="188"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@0" LinkObjectIDznd="140@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 718.14 871.01 L 718.14 856.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="v6300" d="M 718.14 707.33 L 718.14 721.24" stroke-width="1" zvalue="189"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="191@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 718.14 707.33 L 718.14 721.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="v6300" d="M 626.48 785.69 L 718.03 785.69 L 718.03 757.54" stroke-width="1" zvalue="190"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53" LinkObjectIDznd="191@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.48 785.69 L 718.03 785.69 L 718.03 757.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="v6300" d="M 718.03 784.92 L 718.03 820.03" stroke-width="1" zvalue="191"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="143" LinkObjectIDznd="140@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 718.03 784.92 L 718.03 820.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="v6300" d="M 1021.39 580.4 L 1021.39 642.79" stroke-width="1" zvalue="197"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="147@1" LinkObjectIDznd="410@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1021.39 580.4 L 1021.39 642.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="205">
   <path class="v6300" d="M 1066.48 642.79 L 1066.48 687.56" stroke-width="1" zvalue="203"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="410@3" LinkObjectIDznd="208@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1066.48 642.79 L 1066.48 687.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="v6300" d="M 1066.48 737.75 L 1066.48 867.93" stroke-width="1" zvalue="207"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@1" LinkObjectIDznd="209@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1066.48 737.75 L 1066.48 867.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="v6300" d="M 966.39 807.6 L 966.39 786.89 L 1066.48 786.89" stroke-width="1" zvalue="212"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@1" LinkObjectIDznd="199" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.39 807.6 L 966.39 786.89 L 1066.48 786.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="v6300" d="M 1158.14 871.01 L 1158.14 856.33" stroke-width="1" zvalue="217"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="173@0" LinkObjectIDznd="179@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1158.14 871.01 L 1158.14 856.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="v6300" d="M 1158.14 707.33 L 1158.14 721.24" stroke-width="1" zvalue="218"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@0" LinkObjectIDznd="201@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1158.14 707.33 L 1158.14 721.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="v6300" d="M 1066.48 785.69 L 1158.03 785.69 L 1158.03 757.54" stroke-width="1" zvalue="219"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199" LinkObjectIDznd="201@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1066.48 785.69 L 1158.03 785.69 L 1158.03 757.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="v6300" d="M 1158.03 784.92 L 1158.03 820.03" stroke-width="1" zvalue="220"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164" LinkObjectIDznd="179@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1158.03 784.92 L 1158.03 820.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="230">
   <path class="v6300" d="M 1522.48 642.79 L 1522.48 687.56" stroke-width="1" zvalue="225"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="410@2" LinkObjectIDznd="231@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1522.48 642.79 L 1522.48 687.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="v6300" d="M 1522.48 737.75 L 1522.48 863.93" stroke-width="1" zvalue="229"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@1" LinkObjectIDznd="232@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1522.48 737.75 L 1522.48 863.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="v6300" d="M 1422.39 807.6 L 1422.39 786.89 L 1522.48 786.89" stroke-width="1" zvalue="234"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@1" LinkObjectIDznd="228" MaxPinNum="2"/>
   </metadata>
  <path d="M 1422.39 807.6 L 1422.39 786.89 L 1522.48 786.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="220">
   <path class="v6300" d="M 1614.14 871.01 L 1614.14 856.33" stroke-width="1" zvalue="239"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="222@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1614.14 871.01 L 1614.14 856.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="219">
   <path class="v6300" d="M 1614.14 707.33 L 1614.14 721.24" stroke-width="1" zvalue="240"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@0" LinkObjectIDznd="229@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1614.14 707.33 L 1614.14 721.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="218">
   <path class="v6300" d="M 1522.48 785.69 L 1614.03 785.69 L 1614.03 757.54" stroke-width="1" zvalue="241"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="228" LinkObjectIDznd="229@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1522.48 785.69 L 1614.03 785.69 L 1614.03 757.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="v6300" d="M 1614.03 784.92 L 1614.03 820.03" stroke-width="1" zvalue="242"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218" LinkObjectIDznd="222@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1614.03 784.92 L 1614.03 820.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="234">
   <path class="v6300" d="M 1274.28 496.53 L 1274.28 538.09" stroke-width="1" zvalue="245"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="399@0" LinkObjectIDznd="233@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1274.28 496.53 L 1274.28 538.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="235">
   <path class="v6300" d="M 1274.28 576.71 L 1274.28 642.79" stroke-width="1" zvalue="246"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@1" LinkObjectIDznd="410@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1274.28 576.71 L 1274.28 642.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="v6300" d="M 1483.17 500.98 L 1483.17 542.53" stroke-width="1" zvalue="252"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="242@0" LinkObjectIDznd="241@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1483.17 500.98 L 1483.17 542.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="239">
   <path class="v6300" d="M 1483.17 581.16 L 1483.17 642.79" stroke-width="1" zvalue="253"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="241@1" LinkObjectIDznd="410@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1483.17 581.16 L 1483.17 642.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="v6300" d="M 526.39 807.6 L 526.39 788.91 L 626.48 788.91" stroke-width="1" zvalue="379"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@1" LinkObjectIDznd="53" MaxPinNum="2"/>
   </metadata>
  <path d="M 526.39 807.6 L 526.39 788.91 L 626.48 788.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="v6300" d="M 526.39 840.26 L 526.39 878.83" stroke-width="1" zvalue="380"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="131@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 526.39 840.26 L 526.39 878.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="v6300" d="M 966.39 876.1 L 966.39 840.26" stroke-width="1" zvalue="382"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="196@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.39 876.1 L 966.39 840.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="v6300" d="M 1422.39 876.1 L 1422.39 840.26" stroke-width="1" zvalue="383"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="227@0" LinkObjectIDznd="226@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1422.39 876.1 L 1422.39 840.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="v6300" d="M 1021.39 547.74 L 1020.39 547.74" stroke-width="1" zvalue="384"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="147@0" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 1021.39 547.74 L 1020.39 547.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="v6300" d="M 1021.39 547.74 L 1021.39 505.45" stroke-width="1" zvalue="385"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38" LinkObjectIDznd="148@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1021.39 547.74 L 1021.39 505.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="120">
   <use class="kv35" height="30" transform="rotate(0,570.236,298) scale(1.43056,1.55556) translate(-165.166,-98.0952)" width="30" x="548.7777777777778" xlink:href="#Accessory:避雷器PT带熔断器_0" y="274.6666666666666" zvalue="167"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449660125190" ObjectName="35kV古缅线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,570.236,298) scale(1.43056,1.55556) translate(-165.166,-98.0952)" width="30" x="548.7777777777778" y="274.6666666666666"/></g>
  <g id="131">
   <use class="v6300" height="30" transform="rotate(0,521.65,900.505) scale(1.43056,1.55556) translate(-150.543,-313.276)" width="30" x="500.1919191919192" xlink:href="#Accessory:避雷器PT带熔断器_0" y="877.1717171717171" zvalue="178"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449660452870" ObjectName="#3发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,521.65,900.505) scale(1.43056,1.55556) translate(-150.543,-313.276)" width="30" x="500.1919191919192" y="877.1717171717171"/></g>
  <g id="137">
   <use class="v6300" height="30" transform="rotate(0,718.141,692.808) scale(1.32051,1.32051) translate(-169.498,-163.35)" width="30" x="698.3333333333335" xlink:href="#Accessory:RT1122_0" y="673" zvalue="183"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449660583942" ObjectName="#3发电机PT2"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,718.141,692.808) scale(1.32051,1.32051) translate(-169.498,-163.35)" width="30" x="698.3333333333335" y="673"/></g>
  <g id="139">
   <use class="v6300" height="30" transform="rotate(0,718.141,885.534) scale(1.32051,-1.32051) translate(-169.498,-1551.32)" width="30" x="698.3333333333335" xlink:href="#Accessory:RT1122_0" y="865.7259090778094" zvalue="187"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449660649478" ObjectName="#3发电机PT3"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,718.141,885.534) scale(1.32051,-1.32051) translate(-169.498,-1551.32)" width="30" x="698.3333333333335" y="865.7259090778094"/></g>
  <g id="148">
   <use class="v6300" height="30" transform="rotate(0,1018.01,483.778) scale(1.43056,-1.55556) translate(-299.934,-786.444)" width="30" x="996.5555555555555" xlink:href="#Accessory:避雷器PT带熔断器_0" y="460.4444444444445" zvalue="193"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449660846086" ObjectName="6.3kV母线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1018.01,483.778) scale(1.43056,-1.55556) translate(-299.934,-786.444)" width="30" x="996.5555555555555" y="460.4444444444445"/></g>
  <g id="197">
   <use class="v6300" height="30" transform="rotate(0,962.101,897.778) scale(1.43056,1.55556) translate(-283.106,-312.302)" width="30" x="940.6425925925926" xlink:href="#Accessory:避雷器PT带熔断器_0" y="874.4444444444445" zvalue="208"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449661173766" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,962.101,897.778) scale(1.43056,1.55556) translate(-283.106,-312.302)" width="30" x="940.6425925925926" y="874.4444444444445"/></g>
  <g id="180">
   <use class="v6300" height="30" transform="rotate(0,1158.14,692.808) scale(1.32051,1.32051) translate(-276.294,-163.35)" width="30" x="1138.333333333333" xlink:href="#Accessory:RT1122_0" y="673" zvalue="213"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449661042694" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1158.14,692.808) scale(1.32051,1.32051) translate(-276.294,-163.35)" width="30" x="1138.333333333333" y="673"/></g>
  <g id="173">
   <use class="v6300" height="30" transform="rotate(0,1158.14,885.534) scale(1.32051,-1.32051) translate(-276.294,-1551.32)" width="30" x="1138.333333333333" xlink:href="#Accessory:RT1122_0" y="865.7259090778094" zvalue="216"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449660911622" ObjectName="#2发电机PT3"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1158.14,885.534) scale(1.32051,-1.32051) translate(-276.294,-1551.32)" width="30" x="1138.333333333333" y="865.7259090778094"/></g>
  <g id="227">
   <use class="v6300" height="30" transform="rotate(0,1418.1,897.778) scale(1.43056,1.55556) translate(-420.349,-312.302)" width="30" x="1396.642592592593" xlink:href="#Accessory:避雷器PT带熔断器_0" y="874.4444444444445" zvalue="230"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449661632517" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1418.1,897.778) scale(1.43056,1.55556) translate(-420.349,-312.302)" width="30" x="1396.642592592593" y="874.4444444444445"/></g>
  <g id="223">
   <use class="v6300" height="30" transform="rotate(0,1614.14,692.808) scale(1.32051,1.32051) translate(-386.974,-163.35)" width="30" x="1594.333333333333" xlink:href="#Accessory:RT1122_0" y="673" zvalue="235"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449661501445" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1614.14,692.808) scale(1.32051,1.32051) translate(-386.974,-163.35)" width="30" x="1594.333333333333" y="673"/></g>
  <g id="221">
   <use class="v6300" height="30" transform="rotate(0,1614.14,885.534) scale(1.32051,-1.32051) translate(-386.974,-1551.32)" width="30" x="1594.333333333333" xlink:href="#Accessory:RT1122_0" y="865.7259090778094" zvalue="238"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449661370373" ObjectName="#1发电机PT3"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1614.14,885.534) scale(1.32051,-1.32051) translate(-386.974,-1551.32)" width="30" x="1594.333333333333" y="865.7259090778094"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="2" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,627.53,55.0287) scale(1,1) translate(0,-5.82867e-14)" writing-mode="lr" x="627.0599999999999" xml:space="preserve" y="59.71" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124540645380" ObjectName="P"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="3" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,627.53,78.875) scale(1,1) translate(0,-9.00564e-14)" writing-mode="lr" x="627.0599999999999" xml:space="preserve" y="83.55" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124540710916" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="4" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,627.53,102.721) scale(1,1) translate(0,-1.21826e-13)" writing-mode="lr" x="627.0599999999999" xml:space="preserve" y="107.4" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124540776452" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="5" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,543.53,367.389) scale(1,1) translate(0,2.37136e-13)" writing-mode="lr" x="542.98" xml:space="preserve" y="372.07" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124564238340" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="6" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,543.53,391.494) scale(1,1) translate(0,2.53193e-13)" writing-mode="lr" x="542.98" xml:space="preserve" y="396.18" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124564303876" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="7" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,547.53,522.958) scale(1,1) translate(0,0)" writing-mode="lr" x="546.98" xml:space="preserve" y="527.64" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124544512004" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="8" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,547.53,546.833) scale(1,1) translate(0,0)" writing-mode="lr" x="546.98" xml:space="preserve" y="551.51" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124544577540" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="9" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,543.53,415.599) scale(1,1) translate(0,2.6925e-13)" writing-mode="lr" x="542.98" xml:space="preserve" y="420.28" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124564369412" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="10" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,547.53,570.708) scale(1,1) translate(0,0)" writing-mode="lr" x="546.98" xml:space="preserve" y="575.39" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124544970756" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="12" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,623.475,953.63) scale(1,1) translate(0,1.04481e-12)" writing-mode="lr" x="622.92" xml:space="preserve" y="958.33" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124543201284" ObjectName="P"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="13" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1063.48,959.368) scale(1,1) translate(0,1.05118e-12)" writing-mode="lr" x="1062.92" xml:space="preserve" y="964.0700000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124547461124" ObjectName="P"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="14" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1519.48,956.499) scale(1,1) translate(0,1.04799e-12)" writing-mode="lr" x="1518.92" xml:space="preserve" y="961.2" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124548640772" ObjectName="P"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="15" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,623.475,980.169) scale(1,1) translate(0,1.07427e-12)" writing-mode="lr" x="622.92" xml:space="preserve" y="984.87" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124543266820" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="16" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1063.48,985.908) scale(1,1) translate(0,1.08064e-12)" writing-mode="lr" x="1062.92" xml:space="preserve" y="990.61" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124547526660" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1519.48,983.038) scale(1,1) translate(0,1.07746e-12)" writing-mode="lr" x="1518.92" xml:space="preserve" y="987.74" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124548706308" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="20" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,623.475,1006.71) scale(1,1) translate(0,1.10374e-12)" writing-mode="lr" x="622.92" xml:space="preserve" y="1011.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124543332356" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="24">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="24" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1063.48,1012.45) scale(1,1) translate(0,1.11011e-12)" writing-mode="lr" x="1062.92" xml:space="preserve" y="1017.15" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124547592196" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="26">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="26" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1519.48,1009.58) scale(1,1) translate(0,1.10692e-12)" writing-mode="lr" x="1518.92" xml:space="preserve" y="1014.28" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124548771844" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124560699396" ObjectName="F"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124560764932" ObjectName="F"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="246" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,158.333,336.278) scale(1,1) translate(0,0)" writing-mode="lr" x="158.49" xml:space="preserve" y="341.19" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124544249860" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124560568324" ObjectName="F"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124560633860" ObjectName="F"/>
   </metadata>
  </g>
  <g id="194">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="194" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124566073348" ObjectName="F"/>
   </metadata>
  </g>
  <g id="192">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="192" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="337.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124566204420" ObjectName="F"/>
   </metadata>
  </g>
  <g id="189">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="189" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,402.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="407.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124566138884" ObjectName="F"/>
   </metadata>
  </g>
  <g id="186">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="186" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.611,402.389) scale(1,1) translate(0,0)" writing-mode="lr" x="337.77" xml:space="preserve" y="407.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124566335492" ObjectName="F"/>
   </metadata>
  </g>
  <g id="183">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="183" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127185612804" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="181">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="181" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127185547268" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="40">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="40" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1006.51,342.286) scale(1,1) translate(0,0)" writing-mode="lr" x="1006.04" xml:space="preserve" y="347.06" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124543856644" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="41">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="41" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1006.51,367.286) scale(1,1) translate(0,0)" writing-mode="lr" x="1006.04" xml:space="preserve" y="372.06" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124543922180" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="42">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="42" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1006.51,396.286) scale(1,1) translate(0,0)" writing-mode="lr" x="1006.04" xml:space="preserve" y="401.06" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124543987716" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="43">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="43" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,478.506,664.786) scale(1,1) translate(2.88773e-13,0)" writing-mode="lr" x="478.04" xml:space="preserve" y="669.5599999999999" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124544118788" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="32">
   <use height="30" transform="rotate(0,341.048,187.25) scale(0.708333,0.665547) translate(136.056,89.0809)" width="30" x="330.42" xlink:href="#State:红绿圆(方形)_0" y="177.27" zvalue="387"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374886764547" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,341.048,187.25) scale(0.708333,0.665547) translate(136.056,89.0809)" width="30" x="330.42" y="177.27"/></g>
  <g id="35">
   <use height="30" transform="rotate(0,249.048,187.25) scale(0.708333,0.665547) translate(98.174,89.0809)" width="30" x="238.42" xlink:href="#State:红绿圆(方形)_0" y="177.27" zvalue="389"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950251216904" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,249.048,187.25) scale(0.708333,0.665547) translate(98.174,89.0809)" width="30" x="238.42" y="177.27"/></g>
 </g>
</svg>