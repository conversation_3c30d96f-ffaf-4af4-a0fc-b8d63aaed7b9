<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549685846017" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:线路PT三绕组_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="20" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="24" y2="24"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,11) scale(1,1) translate(0,0)" width="6" x="17" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="32" y1="17" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.98040845230975" x2="31.98040845230975" y1="16.76788570496156" y2="23.53635804601289"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.98821300514558" x2="19.98821300514558" y1="1.029523490692871" y2="18.75"/>
   <path d="M 30.0147 23.4481 L 34.2865 23.4481 L 32.115 28.6155 L 30.0147 23.4481" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <rect fill-opacity="0" height="6.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,32.04,24.56) scale(1,1) translate(0,0)" width="15.34" x="24.37" y="21.53"/>
   <ellipse cx="19.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.22179295459144" x2="32.22179295459144" y1="32.22550978083666" y2="36.2625308385742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.01788570496155" x2="35.42570020422134" y1="36.23023467011234" y2="36.23023467011234"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.26384963537316" x2="34.53572596821302" y1="37.52208140858838" y2="37.52208140858838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.15382387138148" x2="33.11176719059974" y1="38.49096646244535" y2="38.49096646244535"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="32.66666666666667" y2="32.66666666666667"/>
   <ellipse cx="12.01" cy="28.48" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.166666666666664" x2="15.16666666666666" y1="28.91666666666667" y2="28.91666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Accessory:线路PT99_0" viewBox="0,0,35,30">
   <use terminal-index="0" type="0" x="35" xlink:href="#terminal" y="6.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.5" x2="1.5" y1="4" y2="9"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.27377700368697" x2="20.43246971500333" y1="6.765818666751574" y2="6.765818666751574"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.6817302644139" x2="34.98333333333333" y1="6.765818666751574" y2="6.765818666751574"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.55947355115261" x2="24.55947355115261" y1="6.895129129519741" y2="14.09675167759026"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.46102695297582" x2="5.583333333333334" y1="6.846289710456816" y2="6.846289710456816"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="20.47373975336484" x2="20.47373975336484" y1="6.846289710456816" y2="6.846289710456816"/>
   <ellipse cx="24.54" cy="18.35" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="20.47373975336483" x2="20.47373975336483" y1="3.466505874834564" y2="10.30654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.61665011096792" x2="31.61665011096792" y1="6.846289710456816" y2="6.846289710456816"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="17.37848687625285" x2="17.37848687625285" y1="3.466505874834558" y2="10.30654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="31.6166501109679" x2="31.6166501109679" y1="3.466505874834564" y2="10.30654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="28.52139723385593" x2="28.52139723385593" y1="3.466505874834564" y2="10.30654458978453"/>
   <ellipse cx="27.04" cy="22.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="22.04" cy="22.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV南司龙开关站" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="66.95999999999999" xlink:href="logo.png" y="45.64"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.589,75.6429) scale(1,1) translate(0,0)" writing-mode="lr" x="201.59" xml:space="preserve" y="79.14" zvalue="10493"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,203.298,75.3332) scale(1,1) translate(9.43293e-15,0)" writing-mode="lr" x="203.3" xml:space="preserve" y="84.33" zvalue="10494">110kV南司龙开关站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="16" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" width="72.88" x="71.94" y="327" zvalue="10496"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" writing-mode="lr" x="108.38" xml:space="preserve" y="343.5" zvalue="10496">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,716.076,506.091) scale(1,1) translate(0,0)" writing-mode="lr" x="716.08" xml:space="preserve" y="510.59" zvalue="7577">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1434.98,473.52) scale(1,1) translate(0,0)" writing-mode="lr" x="1434.98" xml:space="preserve" y="478.02" zvalue="8016">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1482.61,459.394) scale(1,1) translate(0,0)" writing-mode="lr" x="1482.61" xml:space="preserve" y="463.89" zvalue="8030">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1486.61,512.644) scale(1,1) translate(0,0)" writing-mode="lr" x="1486.61" xml:space="preserve" y="517.14" zvalue="8035">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="219" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1455.75,369.091) scale(1,1) translate(0,0)" writing-mode="lr" x="1455.75" xml:space="preserve" y="373.59" zvalue="9703">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1234.15,441.645) scale(1,1) translate(0,0)" writing-mode="lr" x="1234.15" xml:space="preserve" y="446.14" zvalue="9968">132</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1177.53,310.63) scale(1,1) translate(0,0)" writing-mode="lr" x="1177.53" xml:space="preserve" y="315.13" zvalue="9972">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1233.42,496.549) scale(1,1) translate(0,1.08036e-13)" writing-mode="lr" x="1233.42" xml:space="preserve" y="501.05" zvalue="9975">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1178.28,398.065) scale(1,1) translate(0,0)" writing-mode="lr" x="1178.28" xml:space="preserve" y="402.56" zvalue="10028">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1232.92,348.088) scale(1,1) translate(0,0)" writing-mode="lr" x="1232.92" xml:space="preserve" y="352.59" zvalue="10032">6</text>
  <line fill="none" id="174" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="152.8704926140824" y2="152.8704926140824" zvalue="10076"/>
  <line fill="none" id="173" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="380" x2="380" y1="11" y2="1041" zvalue="10077"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="260.75" y2="283.5"/>
  <line fill="none" id="171" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="622.8704926140824" y2="622.8704926140824" zvalue="10079"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="582" y2="606.6794"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="938" y2="938"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="938" y2="938"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1005.0816" y2="1033"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,958) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="964" zvalue="10083">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,992) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="998" zvalue="10084">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237,992) scale(1,1) translate(0,0)" writing-mode="lr" x="237" xml:space="preserve" y="998" zvalue="10085">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1026" zvalue="10086">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="1026" zvalue="10087">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" x="137.53125" xml:space="preserve" y="462.3993055555555" zvalue="10088">35kV     母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="137.53125" xml:space="preserve" y="479.3993055555555" zvalue="10088">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5,652.5) scale(1,1) translate(0,0)" writing-mode="lr" x="78.5" xml:space="preserve" y="657" zvalue="10090">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,212.399,338.071) scale(1,1) translate(0,0)" writing-mode="lr" x="212.4" xml:space="preserve" y="342.57" zvalue="10091">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,317.399,338.071) scale(1,1) translate(0,0)" writing-mode="lr" x="317.4" xml:space="preserve" y="342.57" zvalue="10092">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" x="256.9375" xml:space="preserve" y="460.8368055555555" zvalue="10093">10kV    母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="256.9375" xml:space="preserve" y="477.8368055555555" zvalue="10093">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90,495.75) scale(1,1) translate(0,0)" writing-mode="lr" x="90" xml:space="preserve" y="500.25" zvalue="10095">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90,521.25) scale(1,1) translate(0,0)" writing-mode="lr" x="90" xml:space="preserve" y="525.75" zvalue="10096">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90,544.25) scale(1,1) translate(0,0)" writing-mode="lr" x="90" xml:space="preserve" y="548.75" zvalue="10097">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90,567.25) scale(1,1) translate(0,0)" writing-mode="lr" x="90" xml:space="preserve" y="571.75" zvalue="10098">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90,594.25) scale(1,1) translate(0,0)" writing-mode="lr" x="90" xml:space="preserve" y="598.75" zvalue="10099">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.054,960) scale(1,1) translate(0,0)" writing-mode="lr" x="237.05" xml:space="preserve" y="966" zvalue="10100">南司龙开关站 NanSiLong-01-2019</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,148.054,992) scale(1,1) translate(0,0)" writing-mode="lr" x="148.05" xml:space="preserve" y="998" zvalue="10101">段勇</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,328.054,992) scale(1,1) translate(0,0)" writing-mode="lr" x="328.05" xml:space="preserve" y="998" zvalue="10102">20210301</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52,179) scale(1,1) translate(0,0)" writing-mode="lr" x="52" xml:space="preserve" y="183.5" zvalue="10103">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232,179) scale(1,1) translate(0,0)" writing-mode="lr" x="232" xml:space="preserve" y="183.5" zvalue="10104">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,203.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="207.75" zvalue="10105">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.1875,251.409) scale(1,1) translate(0,0)" writing-mode="lr" x="54.19" xml:space="preserve" y="255.91" zvalue="10107">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.1875,272.591) scale(1,1) translate(0,-3.49841e-13)" writing-mode="lr" x="54.19" xml:space="preserve" y="277.09" zvalue="10109">2号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.1875,227.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.19" xml:space="preserve" y="231.75" zvalue="10110">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1209.13,225.446) scale(1,1) translate(3.88898e-13,0)" writing-mode="lr" x="1209.13" xml:space="preserve" y="229.95" zvalue="10140">110kV南昔线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1184.2,490.545) scale(1,1) translate(0,0)" writing-mode="lr" x="1184.2" xml:space="preserve" y="495.05" zvalue="10259">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,841.155,439.645) scale(1,1) translate(0,0)" writing-mode="lr" x="841.15" xml:space="preserve" y="444.14" zvalue="10267">131</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,784.526,308.63) scale(1,1) translate(0,0)" writing-mode="lr" x="784.53" xml:space="preserve" y="313.13" zvalue="10271">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,840.425,494.549) scale(1,1) translate(0,-1.07592e-13)" writing-mode="lr" x="840.42" xml:space="preserve" y="499.05" zvalue="10274">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,785.276,396.065) scale(1,1) translate(0,0)" writing-mode="lr" x="785.28" xml:space="preserve" y="400.56" zvalue="10278">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,839.925,346.088) scale(1,1) translate(0,0)" writing-mode="lr" x="839.92" xml:space="preserve" y="350.59" zvalue="10280">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,817,224.875) scale(1,1) translate(0,0)" writing-mode="lr" x="817" xml:space="preserve" y="229.38" zvalue="10284">110kV户南线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,791.196,488.545) scale(1,1) translate(0,0)" writing-mode="lr" x="791.2" xml:space="preserve" y="493.05" zvalue="10291">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,875.155,611.821) scale(1,1) translate(0,0)" writing-mode="lr" x="875.15" xml:space="preserve" y="616.3200000000001" zvalue="10435">133</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,814.526,745.836) scale(1,1) translate(0,0)" writing-mode="lr" x="814.53" xml:space="preserve" y="750.34" zvalue="10439">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,866.425,560.917) scale(1,1) translate(0,0)" writing-mode="lr" x="866.42" xml:space="preserve" y="565.42" zvalue="10442">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,794.276,671.401) scale(1,1) translate(0,0)" writing-mode="lr" x="794.28" xml:space="preserve" y="675.9" zvalue="10445">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,867.925,701.378) scale(1,1) translate(0,0)" writing-mode="lr" x="867.92" xml:space="preserve" y="705.88" zvalue="10447">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,849,827.591) scale(1,1) translate(0,0)" writing-mode="lr" x="849" xml:space="preserve" y="832.09" zvalue="10451">110kV真南线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,800.196,578.92) scale(1,1) translate(0,0)" writing-mode="lr" x="800.2" xml:space="preserve" y="583.42" zvalue="10458">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1262.15,612.821) scale(1,1) translate(0,0)" writing-mode="lr" x="1262.15" xml:space="preserve" y="617.3200000000001" zvalue="10470">134</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1253.42,561.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1253.42" xml:space="preserve" y="566.42" zvalue="10474">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1236,828.591) scale(1,1) translate(0,0)" writing-mode="lr" x="1236" xml:space="preserve" y="833.09" zvalue="10482">110kV南司龙一级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1187.2,579.92) scale(1,1) translate(0,0)" writing-mode="lr" x="1187.2" xml:space="preserve" y="584.42" zvalue="10488">17</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="71.94" y="327" zvalue="10496"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="48">
   <path class="kv110" d="M 686 525.09 L 1542.25 525.09" stroke-width="6" zvalue="7576"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674425110531" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674425110531"/></metadata>
  <path d="M 686 525.09 L 1542.25 525.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="1453">
   <use class="kv110" height="30" transform="rotate(180,1454.01,472.828) scale(0.947693,-0.6712) translate(79.8605,-1182.21)" width="15" x="1446.902401042228" xlink:href="#Disconnector:刀闸_0" y="462.7597602301333" zvalue="8015"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454904709122" ObjectName="110kV母线1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454904709122"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1454.01,472.828) scale(0.947693,-0.6712) translate(79.8605,-1182.21)" width="15" x="1446.902401042228" y="462.7597602301333"/></g>
  <g id="96">
   <use class="kv110" height="30" transform="rotate(180,1208.39,495.857) scale(0.947693,-0.6712) translate(66.3038,-1239.55)" width="15" x="1201.284133425995" xlink:href="#Disconnector:刀闸_0" y="485.7885305077181" zvalue="9974"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454903922690" ObjectName="110kV南昔线1321隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454903922690"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1208.39,495.857) scale(0.947693,-0.6712) translate(66.3038,-1239.55)" width="15" x="1201.284133425995" y="485.7885305077181"/></g>
  <g id="32">
   <use class="kv110" height="30" transform="rotate(0,1208.21,354.088) scale(-0.947693,0.6712) translate(-2483.5,168.524)" width="15" x="1201.103492892157" xlink:href="#Disconnector:刀闸_0" y="344.0197927208583" zvalue="10031"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454904250370" ObjectName="110kV南昔线1326隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454904250370"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1208.21,354.088) scale(-0.947693,0.6712) translate(-2483.5,168.524)" width="15" x="1201.103492892157" y="344.0197927208583"/></g>
  <g id="155">
   <use class="kv110" height="30" transform="rotate(180,815.392,493.857) scale(0.947693,-0.6712) translate(44.6125,-1234.57)" width="15" x="808.2841334259955" xlink:href="#Disconnector:刀闸_0" y="483.7885305077181" zvalue="10273"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454905430018" ObjectName="110kV户南线1311隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454905430018"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,815.392,493.857) scale(0.947693,-0.6712) translate(44.6125,-1234.57)" width="15" x="808.2841334259955" y="483.7885305077181"/></g>
  <g id="145">
   <use class="kv110" height="30" transform="rotate(0,815.211,352.088) scale(-0.947693,0.6712) translate(-1675.81,167.545)" width="15" x="808.1034928921569" xlink:href="#Disconnector:刀闸_0" y="342.0197927208583" zvalue="10279"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454905233410" ObjectName="110kV户南线1316隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454905233410"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,815.211,352.088) scale(-0.947693,0.6712) translate(-1675.81,167.545)" width="15" x="808.1034928921569" y="342.0197927208583"/></g>
  <g id="53">
   <use class="kv110" height="30" transform="rotate(180,847.392,556.609) scale(0.947693,0.6712) translate(46.3787,267.733)" width="15" x="840.2841334259955" xlink:href="#Disconnector:刀闸_0" y="546.5413658803764" zvalue="10441"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454906019842" ObjectName="110kV真南线1331隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454906019842"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,847.392,556.609) scale(0.947693,0.6712) translate(46.3787,267.733)" width="15" x="840.2841334259955" y="546.5413658803764"/></g>
  <g id="50">
   <use class="kv110" height="30" transform="rotate(0,847.211,698.378) scale(-0.947693,-0.6712) translate(-1741.58,-1743.8)" width="15" x="840.1034928921569" xlink:href="#Disconnector:刀闸_0" y="688.3101036672361" zvalue="10446"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454906216450" ObjectName="110kV真南线1336隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454906216450"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,847.211,698.378) scale(-0.947693,-0.6712) translate(-1741.58,-1743.8)" width="15" x="840.1034928921569" y="688.3101036672361"/></g>
  <g id="91">
   <use class="kv110" height="30" transform="rotate(180,1234.39,557.609) scale(0.947693,0.6712) translate(67.7389,268.223)" width="15" x="1227.284133425996" xlink:href="#Disconnector:刀闸_0" y="547.5413658803764" zvalue="10473"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454906478594" ObjectName="110kV南司龙一级线1341隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454906478594"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1234.39,557.609) scale(0.947693,0.6712) translate(67.7389,268.223)" width="15" x="1227.284133425996" y="547.5413658803764"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="150">
   <use class="kv110" height="20" transform="rotate(90,1480.37,445.393) scale(1.24619,-1.0068) translate(-291.22,-887.709)" width="10" x="1474.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="435.3248086033777" zvalue="8029"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454904643586" ObjectName="110kV母线19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454904643586"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1480.37,445.393) scale(1.24619,-1.0068) translate(-291.22,-887.709)" width="10" x="1474.143242399862" y="435.3248086033777"/></g>
  <g id="170">
   <use class="kv110" height="20" transform="rotate(90,1484.37,498.643) scale(1.24619,-1.0068) translate(-292.01,-993.849)" width="10" x="1478.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="488.5748086033776" zvalue="8034"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454904512514" ObjectName="110kV母线19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454904512514"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1484.37,498.643) scale(1.24619,-1.0068) translate(-292.01,-993.849)" width="10" x="1478.143242399862" y="488.5748086033776"/></g>
  <g id="98">
   <use class="kv110" height="20" transform="rotate(270,1178.2,329.315) scale(-1.24619,-1.0068) translate(-2122.41,-656.337)" width="10" x="1171.965076813859" xlink:href="#GroundDisconnector:地刀_0" y="319.246507813326" zvalue="9971"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454904053762" ObjectName="110kV南昔线13267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454904053762"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1178.2,329.315) scale(-1.24619,-1.0068) translate(-2122.41,-656.337)" width="10" x="1171.965076813859" y="319.246507813326"/></g>
  <g id="29">
   <use class="kv110" height="20" transform="rotate(270,1178.2,381.315) scale(-1.24619,-1.0068) translate(-2122.41,-759.985)" width="10" x="1171.965076813859" xlink:href="#GroundDisconnector:地刀_0" y="371.246507813326" zvalue="10027"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454904184834" ObjectName="110kV南昔线13260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454904184834"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1178.2,381.315) scale(-1.24619,-1.0068) translate(-2122.41,-759.985)" width="10" x="1171.965076813859" y="371.246507813326"/></g>
  <g id="114">
   <use class="kv110" height="20" transform="rotate(270,1184.2,473.315) scale(-1.24619,-1.0068) translate(-2133.22,-943.364)" width="10" x="1177.965076813859" xlink:href="#GroundDisconnector:地刀_0" y="463.246507813326" zvalue="10258"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454904905730" ObjectName="110kV南昔线13217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454904905730"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1184.2,473.315) scale(-1.24619,-1.0068) translate(-2133.22,-943.364)" width="10" x="1177.965076813859" y="463.246507813326"/></g>
  <g id="158">
   <use class="kv110" height="20" transform="rotate(270,785.196,327.315) scale(-1.24619,-1.0068) translate(-1414.04,-652.35)" width="10" x="778.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="317.246507813326" zvalue="10270"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454905561090" ObjectName="110kV户南线13167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454905561090"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,785.196,327.315) scale(-1.24619,-1.0068) translate(-1414.04,-652.35)" width="10" x="778.9650768138584" y="317.246507813326"/></g>
  <g id="146">
   <use class="kv110" height="20" transform="rotate(270,785.196,379.315) scale(-1.24619,-1.0068) translate(-1414.04,-755.999)" width="10" x="778.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="369.246507813326" zvalue="10277"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454905364482" ObjectName="110kV户南线13160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454905364482"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,785.196,379.315) scale(-1.24619,-1.0068) translate(-1414.04,-755.999)" width="10" x="778.9650768138584" y="369.246507813326"/></g>
  <g id="135">
   <use class="kv110" height="20" transform="rotate(270,791.196,471.315) scale(-1.24619,-1.0068) translate(-1424.86,-939.377)" width="10" x="784.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="461.246507813326" zvalue="10289"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454905036802" ObjectName="110kV户南线13117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454905036802"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,791.196,471.315) scale(-1.24619,-1.0068) translate(-1424.86,-939.377)" width="10" x="784.9650768138584" y="461.246507813326"/></g>
  <g id="59">
   <use class="kv110" height="20" transform="rotate(90,817.196,723.151) scale(-1.24619,1.0068) translate(-1471.72,-4.81666)" width="10" x="810.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="713.0833885747684" zvalue="10438"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454906150914" ObjectName="110kV真南线13367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454906150914"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,817.196,723.151) scale(-1.24619,1.0068) translate(-1471.72,-4.81666)" width="10" x="810.9650768138584" y="713.0833885747684"/></g>
  <g id="51">
   <use class="kv110" height="20" transform="rotate(90,817.196,671.151) scale(-1.24619,1.0068) translate(-1471.72,-4.46542)" width="10" x="810.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="661.0833885747684" zvalue="10444"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454905954306" ObjectName="110kV真南线13360接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454905954306"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,817.196,671.151) scale(-1.24619,1.0068) translate(-1471.72,-4.46542)" width="10" x="810.9650768138584" y="661.0833885747684"/></g>
  <g id="28">
   <use class="kv110" height="20" transform="rotate(90,823.196,579.151) scale(-1.24619,1.0068) translate(-1482.54,-3.84399)" width="10" x="816.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="569.0833885747684" zvalue="10456"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454905692162" ObjectName="110kV真南线13317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454905692162"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,823.196,579.151) scale(-1.24619,1.0068) translate(-1482.54,-3.84399)" width="10" x="816.9650768138584" y="569.0833885747684"/></g>
  <g id="34">
   <use class="kv110" height="20" transform="rotate(90,1210.2,580.151) scale(-1.24619,1.0068) translate(-2180.08,-3.85074)" width="10" x="1203.965076813859" xlink:href="#GroundDisconnector:地刀_0" y="570.0833885747684" zvalue="10486"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454906347522" ObjectName="110kV南司龙一级线13417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454906347522"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1210.2,580.151) scale(-1.24619,1.0068) translate(-2180.08,-3.85074)" width="10" x="1203.965076813859" y="570.0833885747684"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="151">
   <path class="kv110" d="M 1453.95 482.72 L 1453.95 525.09" stroke-width="1" zvalue="8031"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1453.95 482.72 L 1453.95 525.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv110" d="M 1453.93 463.09 L 1453.93 420.23" stroke-width="1" zvalue="8038"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@0" LinkObjectIDznd="218@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1453.93 463.09 L 1453.93 420.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv110" d="M 1208.31 454.95 L 1208.31 486.12" stroke-width="1" zvalue="9978"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@1" LinkObjectIDznd="96@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1208.31 454.95 L 1208.31 486.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv110" d="M 1208.33 505.75 L 1208.33 525.09" stroke-width="1" zvalue="9981"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@1" LinkObjectIDznd="48@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1208.33 505.75 L 1208.33 525.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv110" d="M 1208.13 291.07 L 1208.13 344.35" stroke-width="1" zvalue="10040"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="32@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1208.13 291.07 L 1208.13 344.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv110" d="M 1188.01 381.38 L 1208.15 381.38" stroke-width="1" zvalue="10141"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="29@0" LinkObjectIDznd="73" MaxPinNum="2"/>
   </metadata>
  <path d="M 1188.01 381.38 L 1208.15 381.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv110" d="M 1188.01 329.38 L 1208.13 329.38" stroke-width="1" zvalue="10255"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="41" MaxPinNum="2"/>
   </metadata>
  <path d="M 1188.01 329.38 L 1208.13 329.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv110" d="M 1208 329.38 L 1228.91 329.38 L 1228.91 322" stroke-width="1" zvalue="10256"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49" LinkObjectIDznd="42@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1208 329.38 L 1228.91 329.38 L 1228.91 322" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv110" d="M 1194.01 473.38 L 1208.31 473.38" stroke-width="1" zvalue="10259"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="93" MaxPinNum="2"/>
   </metadata>
  <path d="M 1194.01 473.38 L 1208.31 473.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv110" d="M 815.31 452.95 L 815.31 484.12" stroke-width="1" zvalue="10275"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@1" LinkObjectIDznd="155@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 815.31 452.95 L 815.31 484.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv110" d="M 816 290.5 L 816 342.35" stroke-width="1" zvalue="10281"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="142@0" LinkObjectIDznd="145@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 816 290.5 L 816 342.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="kv110" d="M 795.01 379.38 L 815.15 379.38" stroke-width="1" zvalue="10286"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="71" MaxPinNum="2"/>
   </metadata>
  <path d="M 795.01 379.38 L 815.15 379.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv110" d="M 795.01 327.38 L 816 327.38" stroke-width="1" zvalue="10287"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="144" MaxPinNum="2"/>
   </metadata>
  <path d="M 795.01 327.38 L 816 327.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv110" d="M 815 327.38 L 835.91 327.38 L 835.91 320" stroke-width="1" zvalue="10288"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139" LinkObjectIDznd="143@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 815 327.38 L 835.91 327.38 L 835.91 320" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv110" d="M 801.01 471.38 L 815.31 471.38" stroke-width="1" zvalue="10290"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@0" LinkObjectIDznd="152" MaxPinNum="2"/>
   </metadata>
  <path d="M 801.01 471.38 L 815.31 471.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="369">
   <path class="kv110" d="M 815.33 503.75 L 815.33 525.09" stroke-width="1" zvalue="10429"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@1" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 815.33 503.75 L 815.33 525.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv110" d="M 847.31 597.51 L 847.31 566.34" stroke-width="1" zvalue="10443"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@1" LinkObjectIDznd="53@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 847.31 597.51 L 847.31 566.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv110" d="M 848 759.97 L 848 708.11" stroke-width="1" zvalue="10448"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="50@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 848 759.97 L 848 708.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv110" d="M 827.01 671.09 L 847.15 671.09" stroke-width="1" zvalue="10453"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="85" MaxPinNum="2"/>
   </metadata>
  <path d="M 827.01 671.09 L 847.15 671.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv110" d="M 827.01 723.09 L 848 723.09" stroke-width="1" zvalue="10454"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="40" MaxPinNum="2"/>
   </metadata>
  <path d="M 827.01 723.09 L 848 723.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv110" d="M 847 723.09 L 867.91 723.09 L 867.91 730.47" stroke-width="1" zvalue="10455"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31" LinkObjectIDznd="39@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 847 723.09 L 867.91 723.09 L 867.91 730.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv110" d="M 833.01 579.09 L 847.31 579.09" stroke-width="1" zvalue="10457"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="52" MaxPinNum="2"/>
   </metadata>
  <path d="M 833.01 579.09 L 847.31 579.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv110" d="M 847.33 546.71 L 847.33 525.09" stroke-width="1" zvalue="10459"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@1" LinkObjectIDznd="48@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 847.33 546.71 L 847.33 525.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv110" d="M 815.15 361.98 L 815.15 427.06" stroke-width="1" zvalue="10462"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@1" LinkObjectIDznd="178@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 815.15 361.98 L 815.15 427.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv110" d="M 1208.15 363.98 L 1208.15 429.06" stroke-width="1" zvalue="10463"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@1" LinkObjectIDznd="100@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1208.15 363.98 L 1208.15 429.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv110" d="M 847.14 688.48 L 847.14 623.4" stroke-width="1" zvalue="10465"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@1" LinkObjectIDznd="61@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 847.14 688.48 L 847.14 623.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="kv110" d="M 1474.56 498.71 L 1453.95 498.71" stroke-width="1" zvalue="10466"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="151" MaxPinNum="2"/>
   </metadata>
  <path d="M 1474.56 498.71 L 1453.95 498.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="kv110" d="M 1470.56 445.46 L 1453.93 445.46" stroke-width="1" zvalue="10467"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 1470.56 445.46 L 1453.93 445.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv110" d="M 1234.31 598.51 L 1234.31 567.34" stroke-width="1" zvalue="10475"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@1" LinkObjectIDznd="91@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.31 598.51 L 1234.31 567.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv110" d="M 1220.01 580.09 L 1234.31 580.09" stroke-width="1" zvalue="10487"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@0" LinkObjectIDznd="83" MaxPinNum="2"/>
   </metadata>
  <path d="M 1220.01 580.09 L 1234.31 580.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv110" d="M 1234.33 547.71 L 1234.33 525.09" stroke-width="1" zvalue="10489"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@1" LinkObjectIDznd="48@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.33 547.71 L 1234.33 525.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv110" d="M 1235 760.97 L 1235 624.4" stroke-width="1" zvalue="10491"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="99@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1235 760.97 L 1235 624.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="218">
   <use class="kv110" height="40" transform="rotate(0,1453.5,402.341) scale(0.9375,-0.9375) translate(95.65,-832.755)" width="40" x="1434.75" xlink:href="#Accessory:线路PT三绕组_0" y="383.5909090909091" zvalue="9702"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454904381442" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1453.5,402.341) scale(0.9375,-0.9375) translate(95.65,-832.755)" width="40" x="1434.75" y="383.5909090909091"/></g>
  <g id="42">
   <use class="kv110" height="30" transform="rotate(90,1241.34,296.313) scale(1.46781,-1.48949) translate(-387.442,-487.907)" width="35" x="1215.655761595727" xlink:href="#Accessory:线路PT99_0" y="273.9710619052636" zvalue="10041"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454904315906" ObjectName="110kV南昔线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1241.34,296.313) scale(1.46781,-1.48949) translate(-387.442,-487.907)" width="35" x="1215.655761595727" y="273.9710619052636"/></g>
  <g id="143">
   <use class="kv110" height="30" transform="rotate(90,848.342,294.313) scale(1.46781,-1.48949) translate(-262.189,-484.564)" width="35" x="822.6557615957269" xlink:href="#Accessory:线路PT99_0" y="271.9710619052636" zvalue="10282"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454905167874" ObjectName="110kV户南线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,848.342,294.313) scale(1.46781,-1.48949) translate(-262.189,-484.564)" width="35" x="822.6557615957269" y="271.9710619052636"/></g>
  <g id="39">
   <use class="kv110" height="30" transform="rotate(270,880.342,756.152) scale(1.46781,1.48949) translate(-272.388,-241.151)" width="35" x="854.6557615957269" xlink:href="#Accessory:线路PT99_0" y="733.8101474951845" zvalue="10449"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454905823234" ObjectName="110kV真南线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,880.342,756.152) scale(1.46781,1.48949) translate(-272.388,-241.151)" width="35" x="854.6557615957269" y="733.8101474951845"/></g>
 </g>
 <g id="BreakerClass">
  <g id="100">
   <use class="kv110" height="20" transform="rotate(0,1208.21,442.02) scale(1.5542,1.35421) translate(-428.053,-112.074)" width="10" x="1200.434034534565" xlink:href="#Breaker:开关_0" y="428.477975852458" zvalue="9966"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925230002179" ObjectName="110kV南昔线132断路器"/>
   <cge:TPSR_Ref TObjectID="6473925230002179"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1208.21,442.02) scale(1.5542,1.35421) translate(-428.053,-112.074)" width="10" x="1200.434034534565" y="428.477975852458"/></g>
  <g id="178">
   <use class="kv110" height="20" transform="rotate(0,815.205,440.02) scale(1.5542,1.35421) translate(-287.916,-111.551)" width="10" x="807.4340345345652" xlink:href="#Breaker:开关_0" y="426.477975852458" zvalue="10266"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925230067715" ObjectName="110kV户南线131断路器"/>
   <cge:TPSR_Ref TObjectID="6473925230067715"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,815.205,440.02) scale(1.5542,1.35421) translate(-287.916,-111.551)" width="10" x="807.4340345345652" y="426.477975852458"/></g>
  <g id="61">
   <use class="kv110" height="20" transform="rotate(0,847.205,610.446) scale(1.5542,-1.35421) translate(-299.327,-1057.68)" width="10" x="839.4340345345652" xlink:href="#Breaker:开关_0" y="596.9037238148987" zvalue="10434"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925230133251" ObjectName="110kV真南线133断路器"/>
   <cge:TPSR_Ref TObjectID="6473925230133251"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,847.205,610.446) scale(1.5542,-1.35421) translate(-299.327,-1057.68)" width="10" x="839.4340345345652" y="596.9037238148987"/></g>
  <g id="99">
   <use class="kv110" height="20" transform="rotate(0,1234.21,611.446) scale(1.5542,-1.35421) translate(-437.324,-1059.42)" width="10" x="1226.434034534565" xlink:href="#Breaker:开关_0" y="597.9037238148987" zvalue="10469"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925230198787" ObjectName="110kV南司龙一级线134断路器"/>
   <cge:TPSR_Ref TObjectID="6473925230198787"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1234.21,611.446) scale(1.5542,-1.35421) translate(-437.324,-1059.42)" width="10" x="1226.434034534565" y="597.9037238148987"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,521.25) scale(1,1) translate(1.24653e-14,0)" writing-mode="lr" x="137.75" xml:space="preserve" y="526.02" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="67">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,544.25) scale(1,1) translate(1.24653e-14,5.96467e-14)" writing-mode="lr" x="137.75" xml:space="preserve" y="549.02" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,567.25) scale(1,1) translate(1.24653e-14,-1.244e-13)" writing-mode="lr" x="137.75" xml:space="preserve" y="572.02" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,495.75) scale(1,1) translate(1.24653e-14,-1.08524e-13)" writing-mode="lr" x="137.75" xml:space="preserve" y="500.52" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,142,203.111) scale(1,1) translate(0,0)" writing-mode="lr" x="142.15" xml:space="preserve" y="209.38" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,521.25) scale(1,1) translate(2.57217e-14,0)" writing-mode="lr" x="257.15" xml:space="preserve" y="526.02" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="58">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,544.25) scale(1,1) translate(2.57217e-14,5.96467e-14)" writing-mode="lr" x="257.15" xml:space="preserve" y="549.02" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,567.25) scale(1,1) translate(2.57217e-14,-1.244e-13)" writing-mode="lr" x="257.15" xml:space="preserve" y="572.02" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,495.75) scale(1,1) translate(2.57217e-14,-1.08524e-13)" writing-mode="lr" x="257.15" xml:space="preserve" y="500.52" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="54" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,142,228) scale(1,1) translate(0,0)" writing-mode="lr" x="142.15" xml:space="preserve" y="234.27" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,148.091,250.104) scale(1,1) translate(0,0)" writing-mode="lr" x="148.24" xml:space="preserve" y="256.38" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,148.091,273.104) scale(1,1) translate(0,-3.54521e-13)" writing-mode="lr" x="148.24" xml:space="preserve" y="279.38" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,594.25) scale(1,1) translate(1.24653e-14,1.30396e-13)" writing-mode="lr" x="137.75" xml:space="preserve" y="599.02" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,594.25) scale(1,1) translate(2.57217e-14,1.30396e-13)" writing-mode="lr" x="257.15" xml:space="preserve" y="599.02" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1208.63,149.821) scale(1,1) translate(3.89065e-13,0)" writing-mode="lr" x="1208.8" xml:space="preserve" y="154.69" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136764616705" ObjectName="P"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="18" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1208.63,170.821) scale(1,1) translate(3.89065e-13,0)" writing-mode="lr" x="1208.8" xml:space="preserve" y="175.69" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136764682241" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1208.63,191.821) scale(1,1) translate(3.89065e-13,0)" writing-mode="lr" x="1208.8" xml:space="preserve" y="196.69" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136764747777" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="21" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,816,157.25) scale(1,1) translate(-1.72196e-13,0)" writing-mode="lr" x="815.58" xml:space="preserve" y="161.9" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136765665281" ObjectName="P"/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="35" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,816,182.25) scale(1,1) translate(-1.72196e-13,0)" writing-mode="lr" x="815.58" xml:space="preserve" y="186.9" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136765730817" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="38" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,816,203.25) scale(1,1) translate(-1.72196e-13,0)" writing-mode="lr" x="815.58" xml:space="preserve" y="207.9" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136765796353" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="43">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="43" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,848,845.216) scale(1,1) translate(-1.79301e-13,0)" writing-mode="lr" x="847.58" xml:space="preserve" y="849.87" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136767238145" ObjectName="P"/>
   </metadata>
  </g>
  <g id="46">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="46" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,848,866.216) scale(1,1) translate(-1.79301e-13,0)" writing-mode="lr" x="847.58" xml:space="preserve" y="870.87" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136767303681" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="60">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="60" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,848,883.216) scale(1,1) translate(-1.79301e-13,0)" writing-mode="lr" x="847.58" xml:space="preserve" y="887.87" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136767369217" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="62" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1235,846.216) scale(1,1) translate(-2.65232e-13,0)" writing-mode="lr" x="1234.58" xml:space="preserve" y="850.87" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136768811009" ObjectName="P"/>
   </metadata>
  </g>
  <g id="69">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="69" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1235,867.216) scale(1,1) translate(-2.65232e-13,0)" writing-mode="lr" x="1234.58" xml:space="preserve" y="871.87" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136768876545" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="75">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="75" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1235,884.216) scale(1,1) translate(-2.65232e-13,0)" writing-mode="lr" x="1234.58" xml:space="preserve" y="888.87" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136768942081" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="182">
   <use class="kv110" height="30" transform="rotate(0,1208.13,266.321) scale(5.35714,1.66667) translate(-967.361,-96.5286)" width="7" x="1189.378004087979" xlink:href="#ACLineSegment:线路_0" y="241.3214285714286" zvalue="10139"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249333628930" ObjectName="110kV南昔线"/>
   <cge:TPSR_Ref TObjectID="8444249333628930_5066549685846017"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1208.13,266.321) scale(5.35714,1.66667) translate(-967.361,-96.5286)" width="7" x="1189.378004087979" y="241.3214285714286"/></g>
  <g id="142">
   <use class="kv110" height="30" transform="rotate(0,816,265.75) scale(5.35714,1.66667) translate(-648.43,-96.3)" width="7" x="797.25" xlink:href="#ACLineSegment:线路_0" y="240.75" zvalue="10283"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249333497858" ObjectName="110kV户南线"/>
   <cge:TPSR_Ref TObjectID="8444249333497858_5066549685846017"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,816,265.75) scale(5.35714,1.66667) translate(-648.43,-96.3)" width="7" x="797.25" y="240.75"/></g>
 </g>
 <g id="StateClass">
  <g id="1056">
   <use height="30" transform="rotate(0,265.286,338.071) scale(0.708333,0.665547) translate(104.86,164.872)" width="30" x="254.66" xlink:href="#State:红绿圆(方形)_0" y="328.09" zvalue="10247"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,265.286,338.071) scale(0.708333,0.665547) translate(104.86,164.872)" width="30" x="254.66" y="328.09"/></g>
  <g id="1057">
   <use height="30" transform="rotate(0,352.911,338.071) scale(0.708333,0.665547) translate(140.941,164.872)" width="30" x="342.29" xlink:href="#State:红绿圆(方形)_0" y="328.09" zvalue="10249"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374892990467" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,352.911,338.071) scale(0.708333,0.665547) translate(140.941,164.872)" width="30" x="342.29" y="328.09"/></g>
 </g>
</svg>