<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="2000" id="thSvg" source="NR-PCS9000" viewBox="0 0 2000 2000" width="2000">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:避雷器PT1_0" viewBox="0,0,38,38">
   <use terminal-index="0" type="0" x="18.77349690599886" xlink:href="#terminal" y="36.79316976066781"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.7559822394171" x2="18.7559822394171" y1="29.08333333333334" y2="36.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.88529270218527" x2="24.41666666666667" y1="21.56930997848709" y2="21.56930997848709"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.7559822394171" x2="18.7559822394171" y1="25.91666666666667" y2="17.4423061423378"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.83645328312235" x2="18.83645328312235" y1="17.48357618069931" y2="17.48357618069931"/>
   <ellipse cx="28.59" cy="21.55" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.4566694475001" x2="22.29670816245006" y1="17.4835761806993" y2="17.4835761806993"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.66978661645568" x2="18.66978661645568" y1="29.04315320496906" y2="29.04315320496906"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.45666944750009" x2="22.29670816245006" y1="14.38832330358733" y2="14.38832330358733"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.29000278083343" x2="22.1300414957834" y1="29.04315320496904" y2="29.04315320496904"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.29000278083343" x2="22.1300414957834" y1="25.94790032785706" y2="25.94790032785706"/>
   <ellipse cx="32.84" cy="24.05" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="32.84" cy="19.05" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.82811977618069" x2="18.82811977618069" y1="14.41666666666667" y2="7.039733668812222"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.92895180590147" x2="16.65213596121762" y1="6.971825133297724" y2="6.971825133297724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.09561847256814" x2="17.48546929455096" y1="5.596825133297724" y2="5.596825133297724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.25" x2="18.3310877671191" y1="4.221825133297724" y2="4.221825133297724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.883333333333329" x2="4.883333333333329" y1="7.049999999999994" y2="3.449999999999994"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.133333333333331" x2="6.333333333333336" y1="11.79999999999999" y2="15.58333333333334"/>
   <rect fill-opacity="0" height="12.12" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.12,13.11) scale(1,-1) translate(0,-1764.33)" width="6.08" x="2.08" y="7.05"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.133333333333331" x2="3.750000000000004" y1="11.79999999999999" y2="15.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.133333333333329" x2="5.133333333333329" y1="21.5" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.218626035518611" x2="18.58333333333333" y1="21.48597664515375" y2="21.48597664515375"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.012285139234802" x2="2.735469294550956" y1="3.30515846663106" y2="3.30515846663106"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.178951805901468" x2="3.56880262788429" y1="1.93015846663106" y2="1.93015846663106"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.333333333333332" x2="4.414421100452426" y1="0.5551584666310596" y2="0.5551584666310596"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV胞波变" InitShowingPlane="" fill="rgb(0,0,0)" height="2000" width="2000" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="594.0599999999999" xlink:href="logo.png" y="366.34"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,718.903,407.63) scale(1,1) translate(-1.31907e-13,0)" writing-mode="lr" x="718.9" xml:space="preserve" y="411.13" zvalue="110"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,747.75,407.607) scale(1,1) translate(0,0)" writing-mode="lr" x="747.75" xml:space="preserve" y="416.61" zvalue="111">110kV胞波变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="85" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,725.71,738.167) scale(1,1) translate(0,0)" width="72.88" x="689.27" y="726.17" zvalue="194"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,725.71,738.167) scale(1,1) translate(0,0)" writing-mode="lr" x="725.71" xml:space="preserve" y="742.67" zvalue="194">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="84" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,623.804,738.167) scale(1,1) translate(0,0)" width="72.88" x="587.37" y="726.17" zvalue="195"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,623.804,738.167) scale(1,1) translate(0,0)" writing-mode="lr" x="623.8" xml:space="preserve" y="742.67" zvalue="195">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="83" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,623.804,697.667) scale(1,1) translate(0,0)" width="72.88" x="587.37" y="685.67" zvalue="196"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,623.804,697.667) scale(1,1) translate(0,0)" writing-mode="lr" x="623.8" xml:space="preserve" y="702.17" zvalue="196">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1040.93,679.619) scale(1,1) translate(0,0)" writing-mode="lr" x="1040.93" xml:space="preserve" y="684.12" zvalue="3">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1206.26,410.8) scale(1,1) translate(0,0)" writing-mode="lr" x="1206.26" xml:space="preserve" y="415.3" zvalue="24">110kV胞波II回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1180.24,594.561) scale(1,1) translate(0,0)" writing-mode="lr" x="1180.24" xml:space="preserve" y="599.0599999999999" zvalue="25">151</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1193.8,534.171) scale(1,1) translate(0,0)" writing-mode="lr" x="1193.8" xml:space="preserve" y="538.67" zvalue="27">1516</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1242.19,483.232) scale(1,1) translate(9.50608e-13,0)" writing-mode="lr" x="1242.19" xml:space="preserve" y="487.73" zvalue="29">15167</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1195.11,665.633) scale(1,1) translate(0,0)" writing-mode="lr" x="1195.11" xml:space="preserve" y="670.13" zvalue="31">1511</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1240.91,619.685) scale(1,1) translate(0,0)" writing-mode="lr" x="1240.91" xml:space="preserve" y="624.1799999999999" zvalue="35">15117</text>
  <line fill="none" id="150" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="566.7500000000002" x2="934.7499999999998" y1="484.7871592807492" y2="484.7871592807492" zvalue="113"/>
  <line fill="none" id="149" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="935.75" x2="935.75" y1="342.9166666666667" y2="1372.916666666667" zvalue="114"/>
  <line fill="none" id="148" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="568.0000000000002" x2="935.9999999999998" y1="943.7871592807491" y2="943.7871592807491" zvalue="115"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="566.75" x2="618.6070999999999" y1="763.1666666666667" y2="763.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="566.75" x2="618.6070999999999" y1="795.8333666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="566.75" x2="566.75" y1="763.1666666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.6070999999999" x2="618.6070999999999" y1="763.1666666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.607" x2="670.4641" y1="763.1666666666667" y2="763.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.607" x2="670.4641" y1="795.8333666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.607" x2="618.607" y1="763.1666666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4641" x2="670.4641" y1="763.1666666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4643" x2="722.3214" y1="763.1666666666667" y2="763.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4643" x2="722.3214" y1="795.8333666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4643" x2="670.4643" y1="763.1666666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="722.3214" y1="763.1666666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="774.1785" y1="763.1666666666667" y2="763.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="774.1785" y1="795.8333666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="722.3214" y1="763.1666666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1785" x2="774.1785" y1="763.1666666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1786" x2="826.0357" y1="763.1666666666667" y2="763.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1786" x2="826.0357" y1="795.8333666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1786" x2="774.1786" y1="763.1666666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="826.0357" y1="763.1666666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="877.8928000000001" y1="763.1666666666667" y2="763.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="877.8928000000001" y1="795.8333666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="826.0357" y1="763.1666666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.8928000000001" x2="877.8928000000001" y1="763.1666666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.893" x2="929.7501" y1="763.1666666666667" y2="763.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.893" x2="929.7501" y1="795.8333666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.893" x2="877.893" y1="763.1666666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="929.7501" x2="929.7501" y1="763.1666666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="566.75" x2="618.6070999999999" y1="795.8333666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="566.75" x2="618.6070999999999" y1="822.5000666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="566.75" x2="566.75" y1="795.8333666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.6070999999999" x2="618.6070999999999" y1="795.8333666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.607" x2="670.4641" y1="795.8333666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.607" x2="670.4641" y1="822.5000666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.607" x2="618.607" y1="795.8333666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4641" x2="670.4641" y1="795.8333666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4643" x2="722.3214" y1="795.8333666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4643" x2="722.3214" y1="822.5000666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4643" x2="670.4643" y1="795.8333666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="722.3214" y1="795.8333666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="774.1785" y1="795.8333666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="774.1785" y1="822.5000666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="722.3214" y1="795.8333666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1785" x2="774.1785" y1="795.8333666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1786" x2="826.0357" y1="795.8333666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1786" x2="826.0357" y1="822.5000666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1786" x2="774.1786" y1="795.8333666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="826.0357" y1="795.8333666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="877.8928000000001" y1="795.8333666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="877.8928000000001" y1="822.5000666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="826.0357" y1="795.8333666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.8928000000001" x2="877.8928000000001" y1="795.8333666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.893" x2="929.7501" y1="795.8333666666667" y2="795.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.893" x2="929.7501" y1="822.5000666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.893" x2="877.893" y1="795.8333666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="929.7501" x2="929.7501" y1="795.8333666666667" y2="822.5000666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="566.75" x2="618.6070999999999" y1="822.4999666666668" y2="822.4999666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="566.75" x2="618.6070999999999" y1="849.1666666666667" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="566.75" x2="566.75" y1="822.4999666666668" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.6070999999999" x2="618.6070999999999" y1="822.4999666666668" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.607" x2="670.4641" y1="822.4999666666668" y2="822.4999666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.607" x2="670.4641" y1="849.1666666666667" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.607" x2="618.607" y1="822.4999666666668" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4641" x2="670.4641" y1="822.4999666666668" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4643" x2="722.3214" y1="822.4999666666668" y2="822.4999666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4643" x2="722.3214" y1="849.1666666666667" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4643" x2="670.4643" y1="822.4999666666668" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="722.3214" y1="822.4999666666668" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="774.1785" y1="822.4999666666668" y2="822.4999666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="774.1785" y1="849.1666666666667" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="722.3214" y1="822.4999666666668" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1785" x2="774.1785" y1="822.4999666666668" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1786" x2="826.0357" y1="822.4999666666668" y2="822.4999666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1786" x2="826.0357" y1="849.1666666666667" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1786" x2="774.1786" y1="822.4999666666668" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="826.0357" y1="822.4999666666668" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="877.8928000000001" y1="822.4999666666668" y2="822.4999666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="877.8928000000001" y1="849.1666666666667" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="826.0357" y1="822.4999666666668" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.8928000000001" x2="877.8928000000001" y1="822.4999666666668" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.893" x2="929.7501" y1="822.4999666666668" y2="822.4999666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.893" x2="929.7501" y1="849.1666666666667" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.893" x2="877.893" y1="822.4999666666668" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="929.7501" x2="929.7501" y1="822.4999666666668" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="566.75" x2="618.6070999999999" y1="849.1666666666667" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="566.75" x2="618.6070999999999" y1="875.8333666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="566.75" x2="566.75" y1="849.1666666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.6070999999999" x2="618.6070999999999" y1="849.1666666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.607" x2="670.4641" y1="849.1666666666667" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.607" x2="670.4641" y1="875.8333666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.607" x2="618.607" y1="849.1666666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4641" x2="670.4641" y1="849.1666666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4643" x2="722.3214" y1="849.1666666666667" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4643" x2="722.3214" y1="875.8333666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4643" x2="670.4643" y1="849.1666666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="722.3214" y1="849.1666666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="774.1785" y1="849.1666666666667" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="774.1785" y1="875.8333666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="722.3214" y1="849.1666666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1785" x2="774.1785" y1="849.1666666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1786" x2="826.0357" y1="849.1666666666667" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1786" x2="826.0357" y1="875.8333666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1786" x2="774.1786" y1="849.1666666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="826.0357" y1="849.1666666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="877.8928000000001" y1="849.1666666666667" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="877.8928000000001" y1="875.8333666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="826.0357" y1="849.1666666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.8928000000001" x2="877.8928000000001" y1="849.1666666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.893" x2="929.7501" y1="849.1666666666667" y2="849.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.893" x2="929.7501" y1="875.8333666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.893" x2="877.893" y1="849.1666666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="929.7501" x2="929.7501" y1="849.1666666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="566.75" x2="618.6070999999999" y1="875.8333666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="566.75" x2="618.6070999999999" y1="902.5000666666668" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="566.75" x2="566.75" y1="875.8333666666667" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.6070999999999" x2="618.6070999999999" y1="875.8333666666667" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.607" x2="670.4641" y1="875.8333666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.607" x2="670.4641" y1="902.5000666666668" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.607" x2="618.607" y1="875.8333666666667" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4641" x2="670.4641" y1="875.8333666666667" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4643" x2="722.3214" y1="875.8333666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4643" x2="722.3214" y1="902.5000666666668" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4643" x2="670.4643" y1="875.8333666666667" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="722.3214" y1="875.8333666666667" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="774.1785" y1="875.8333666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="774.1785" y1="902.5000666666668" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="722.3214" y1="875.8333666666667" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1785" x2="774.1785" y1="875.8333666666667" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1786" x2="826.0357" y1="875.8333666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1786" x2="826.0357" y1="902.5000666666668" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1786" x2="774.1786" y1="875.8333666666667" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="826.0357" y1="875.8333666666667" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="877.8928000000001" y1="875.8333666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="877.8928000000001" y1="902.5000666666668" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="826.0357" y1="875.8333666666667" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.8928000000001" x2="877.8928000000001" y1="875.8333666666667" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.893" x2="929.7501" y1="875.8333666666667" y2="875.8333666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.893" x2="929.7501" y1="902.5000666666668" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.893" x2="877.893" y1="875.8333666666667" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="929.7501" x2="929.7501" y1="875.8333666666667" y2="902.5000666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="566.75" x2="618.6070999999999" y1="902.4999666666668" y2="902.4999666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="566.75" x2="618.6070999999999" y1="929.1666666666667" y2="929.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="566.75" x2="566.75" y1="902.4999666666668" y2="929.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.6070999999999" x2="618.6070999999999" y1="902.4999666666668" y2="929.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.607" x2="670.4641" y1="902.4999666666668" y2="902.4999666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.607" x2="670.4641" y1="929.1666666666667" y2="929.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="618.607" x2="618.607" y1="902.4999666666668" y2="929.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4641" x2="670.4641" y1="902.4999666666668" y2="929.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4643" x2="722.3214" y1="902.4999666666668" y2="902.4999666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4643" x2="722.3214" y1="929.1666666666667" y2="929.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="670.4643" x2="670.4643" y1="902.4999666666668" y2="929.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="722.3214" y1="902.4999666666668" y2="929.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="774.1785" y1="902.4999666666668" y2="902.4999666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="774.1785" y1="929.1666666666667" y2="929.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="722.3214" x2="722.3214" y1="902.4999666666668" y2="929.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1785" x2="774.1785" y1="902.4999666666668" y2="929.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1786" x2="826.0357" y1="902.4999666666668" y2="902.4999666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1786" x2="826.0357" y1="929.1666666666667" y2="929.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="774.1786" x2="774.1786" y1="902.4999666666668" y2="929.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="826.0357" y1="902.4999666666668" y2="929.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="877.8928000000001" y1="902.4999666666668" y2="902.4999666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="877.8928000000001" y1="929.1666666666667" y2="929.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="826.0357" x2="826.0357" y1="902.4999666666668" y2="929.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.8928000000001" x2="877.8928000000001" y1="902.4999666666668" y2="929.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.893" x2="929.7501" y1="902.4999666666668" y2="902.4999666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.893" x2="929.7501" y1="929.1666666666667" y2="929.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="877.893" x2="877.893" y1="902.4999666666668" y2="929.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="929.7501" x2="929.7501" y1="902.4999666666668" y2="929.1666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="565" x2="655" y1="1256.916666666667" y2="1256.916666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="565" x2="655" y1="1309.079966666667" y2="1309.079966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="565" x2="565" y1="1256.916666666667" y2="1309.079966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="655" x2="655" y1="1256.916666666667" y2="1309.079966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="655" x2="925" y1="1256.916666666667" y2="1256.916666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="655" x2="925" y1="1309.079966666667" y2="1309.079966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="655" x2="655" y1="1256.916666666667" y2="1309.079966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="925" x2="925" y1="1256.916666666667" y2="1309.079966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="565" x2="655" y1="1309.079936666667" y2="1309.079936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="565" x2="655" y1="1336.998336666667" y2="1336.998336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="565" x2="565" y1="1309.079936666667" y2="1336.998336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="655" x2="655" y1="1309.079936666667" y2="1336.998336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="655" x2="745" y1="1309.079936666667" y2="1309.079936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="655" x2="745" y1="1336.998336666667" y2="1336.998336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="655" x2="655" y1="1309.079936666667" y2="1336.998336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="745" x2="745" y1="1309.079936666667" y2="1336.998336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="745.0000000000001" x2="835.0000000000001" y1="1309.079936666667" y2="1309.079936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="745.0000000000001" x2="835.0000000000001" y1="1336.998336666667" y2="1336.998336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="745.0000000000001" x2="745.0000000000001" y1="1309.079936666667" y2="1336.998336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="835.0000000000001" x2="835.0000000000001" y1="1309.079936666667" y2="1336.998336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="835" x2="925" y1="1309.079936666667" y2="1309.079936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="835" x2="925" y1="1336.998336666667" y2="1336.998336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="835" x2="835" y1="1309.079936666667" y2="1336.998336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="925" x2="925" y1="1309.079936666667" y2="1336.998336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="565" x2="655" y1="1336.998266666667" y2="1336.998266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="565" x2="655" y1="1364.916666666667" y2="1364.916666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="565" x2="565" y1="1336.998266666667" y2="1364.916666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="655" x2="655" y1="1336.998266666667" y2="1364.916666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="655" x2="745" y1="1336.998266666667" y2="1336.998266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="655" x2="745" y1="1364.916666666667" y2="1364.916666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="655" x2="655" y1="1336.998266666667" y2="1364.916666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="745" x2="745" y1="1336.998266666667" y2="1364.916666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="745.0000000000001" x2="835.0000000000001" y1="1336.998266666667" y2="1336.998266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="745.0000000000001" x2="835.0000000000001" y1="1364.916666666667" y2="1364.916666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="745.0000000000001" x2="745.0000000000001" y1="1336.998266666667" y2="1364.916666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="835.0000000000001" x2="835.0000000000001" y1="1336.998266666667" y2="1364.916666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="835" x2="925" y1="1336.998266666667" y2="1336.998266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="835" x2="925" y1="1364.916666666667" y2="1364.916666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="835" x2="835" y1="1336.998266666667" y2="1364.916666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="925" x2="925" y1="1336.998266666667" y2="1364.916666666667"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="145" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,615.089,1285.17) scale(1,1) translate(0,0)" writing-mode="lr" x="582" xml:space="preserve" y="1291.17" zvalue="118">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="144" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,616.089,1323.92) scale(1,1) translate(0,0)" writing-mode="lr" x="594" xml:space="preserve" y="1329.92" zvalue="119">制图              </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="143" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,836,1323.92) scale(1,1) translate(0,0)" writing-mode="lr" x="756.75" xml:space="preserve" y="1329.92" zvalue="120">绘制日期      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,608.75,1351.92) scale(1,1) translate(0,0)" writing-mode="lr" x="608.75" xml:space="preserve" y="1357.92" zvalue="121">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,790.75,1351.92) scale(1,1) translate(0,0)" writing-mode="lr" x="790.75" xml:space="preserve" y="1357.92" zvalue="122">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" x="747.75" xml:space="preserve" y="777.671875" zvalue="123">35kV     Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="747.75" xml:space="preserve" y="793.671875" zvalue="123">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" x="798.421875" xml:space="preserve" y="777.171875" zvalue="124">35kV    Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="798.421875" xml:space="preserve" y="793.171875" zvalue="124">母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,592.75,812.667) scale(1,1) translate(0,0)" writing-mode="lr" x="592.75" xml:space="preserve" y="817.1666666666667" zvalue="125">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,592.75,838.167) scale(1,1) translate(0,0)" writing-mode="lr" x="592.75" xml:space="preserve" y="842.6666666666667" zvalue="126">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,592.75,863.667) scale(1,1) translate(0,0)" writing-mode="lr" x="592.75" xml:space="preserve" y="868.1666666666667" zvalue="127">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,592.75,889.167) scale(1,1) translate(0,0)" writing-mode="lr" x="592.75" xml:space="preserve" y="893.6666666666667" zvalue="128">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,592.75,914.667) scale(1,1) translate(0,0)" writing-mode="lr" x="592.75" xml:space="preserve" y="919.1666666666667" zvalue="129">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" x="643.75" xml:space="preserve" y="777.671875" zvalue="130">110kV   Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="643.75" xml:space="preserve" y="793.671875" zvalue="130">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="middle" x="694.421875" xml:space="preserve" y="777.171875" zvalue="131">110kV   Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="694.421875" xml:space="preserve" y="793.171875" zvalue="131">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" x="852.75" xml:space="preserve" y="777.671875" zvalue="132">10kV     Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="852.75" xml:space="preserve" y="793.671875" zvalue="132">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" x="903.421875" xml:space="preserve" y="777.171875" zvalue="133">10kV      Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="903.421875" xml:space="preserve" y="793.171875" zvalue="133">母</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="568.75" x2="749.75" y1="493.9166666666667" y2="493.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="568.75" x2="749.75" y1="519.9166666666667" y2="519.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="568.75" x2="568.75" y1="493.9166666666667" y2="519.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="749.75" y1="493.9166666666667" y2="519.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="930.75" y1="493.9166666666667" y2="493.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="930.75" y1="519.9166666666667" y2="519.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="749.75" y1="493.9166666666667" y2="519.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="930.75" x2="930.75" y1="493.9166666666667" y2="519.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="568.75" x2="749.75" y1="519.9166666666667" y2="519.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="568.75" x2="749.75" y1="544.1666666666667" y2="544.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="568.75" x2="568.75" y1="519.9166666666667" y2="544.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="749.75" y1="519.9166666666667" y2="544.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="930.75" y1="519.9166666666667" y2="519.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="930.75" y1="544.1666666666667" y2="544.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="749.75" y1="519.9166666666667" y2="544.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="930.75" x2="930.75" y1="519.9166666666667" y2="544.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="568.75" x2="749.75" y1="544.1666666666667" y2="544.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="568.75" x2="749.75" y1="568.4166666666667" y2="568.4166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="568.75" x2="568.75" y1="544.1666666666667" y2="568.4166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="749.75" y1="544.1666666666667" y2="568.4166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="930.75" y1="544.1666666666667" y2="544.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="930.75" y1="568.4166666666667" y2="568.4166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="749.75" y1="544.1666666666667" y2="568.4166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="930.75" x2="930.75" y1="544.1666666666667" y2="568.4166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="568.75" x2="749.75" y1="568.4166666666667" y2="568.4166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="568.75" x2="749.75" y1="591.1666666666667" y2="591.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="568.75" x2="568.75" y1="568.4166666666667" y2="591.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="749.75" y1="568.4166666666667" y2="591.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="930.75" y1="568.4166666666667" y2="568.4166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="930.75" y1="591.1666666666667" y2="591.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="749.75" y1="568.4166666666667" y2="591.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="930.75" x2="930.75" y1="568.4166666666667" y2="591.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="568.75" x2="749.75" y1="591.1666666666667" y2="591.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="568.75" x2="749.75" y1="613.9166666666667" y2="613.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="568.75" x2="568.75" y1="591.1666666666667" y2="613.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="749.75" y1="591.1666666666667" y2="613.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="930.75" y1="591.1666666666667" y2="591.1666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="930.75" y1="613.9166666666667" y2="613.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="749.75" y1="591.1666666666667" y2="613.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="930.75" x2="930.75" y1="591.1666666666667" y2="613.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="568.75" x2="749.75" y1="613.9166666666667" y2="613.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="568.75" x2="749.75" y1="636.6666666666667" y2="636.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="568.75" x2="568.75" y1="613.9166666666667" y2="636.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="749.75" y1="613.9166666666667" y2="636.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="930.75" y1="613.9166666666667" y2="613.9166666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="930.75" y1="636.6666666666667" y2="636.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="749.75" x2="749.75" y1="613.9166666666667" y2="636.6666666666667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="930.75" x2="930.75" y1="613.9166666666667" y2="636.6666666666667"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,606.75,507.917) scale(1,1) translate(0,0)" writing-mode="lr" x="606.75" xml:space="preserve" y="513.42" zvalue="135">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,786.75,507.917) scale(1,1) translate(0,0)" writing-mode="lr" x="786.75" xml:space="preserve" y="513.42" zvalue="136">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,609.438,533.167) scale(1,1) translate(0,0)" writing-mode="lr" x="609.4400000000001" xml:space="preserve" y="537.67" zvalue="137">110kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,792.5,532.917) scale(1,1) translate(0,0)" writing-mode="lr" x="792.5" xml:space="preserve" y="537.42" zvalue="138">110kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,621.938,603.917) scale(1,1) translate(0,0)" writing-mode="lr" x="621.9400000000001" xml:space="preserve" y="608.42" zvalue="139">110kV#1主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,805.5,603.417) scale(1,1) translate(0,0)" writing-mode="lr" x="805.5" xml:space="preserve" y="607.92" zvalue="140">110kV#2主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,623.938,626.917) scale(1,1) translate(0,0)" writing-mode="lr" x="623.9400000000001" xml:space="preserve" y="631.42" zvalue="141">110kV#1主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,804.25,626.417) scale(1,1) translate(0,0)" writing-mode="lr" x="804.25" xml:space="preserve" y="630.92" zvalue="142">110kV#2主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,609.438,558.167) scale(1,1) translate(0,0)" writing-mode="lr" x="609.4400000000001" xml:space="preserve" y="562.67" zvalue="143">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,792.5,557.917) scale(1,1) translate(0,0)" writing-mode="lr" x="792.5" xml:space="preserve" y="562.42" zvalue="144">35kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,610.688,580.667) scale(1,1) translate(0,0)" writing-mode="lr" x="610.6900000000001" xml:space="preserve" y="585.17" zvalue="145">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,792.5,581.667) scale(1,1) translate(0,0)" writing-mode="lr" x="792.5" xml:space="preserve" y="586.17" zvalue="146">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,749.149,671.258) scale(1,1) translate(4.85044e-13,0)" writing-mode="lr" x="749.15" xml:space="preserve" y="675.76" zvalue="147">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,854.149,671.258) scale(1,1) translate(0,0)" writing-mode="lr" x="854.15" xml:space="preserve" y="675.76" zvalue="148">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,635,965.167) scale(1,1) translate(0,0)" writing-mode="lr" x="635" xml:space="preserve" y="969.666666666667" zvalue="150">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,786.464,1286.92) scale(1,1) translate(-1.58262e-13,0)" writing-mode="lr" x="786.46" xml:space="preserve" y="1292.92" zvalue="197">DongJiao-02-2023</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,696.75,1350.92) scale(1,1) translate(0,0)" writing-mode="lr" x="696.75" xml:space="preserve" y="1356.92" zvalue="198">李文杰</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,878.464,1351.92) scale(1,1) translate(0,0)" writing-mode="lr" x="878.46" xml:space="preserve" y="1357.92" zvalue="202">2023-11-28</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,832.414,739.917) scale(1,1) translate(0,-3.2326e-13)" writing-mode="lr" x="832.4135872977123" xml:space="preserve" y="744.4166666666667" zvalue="208">小电流接地</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,623.464,658.456) scale(1,1) translate(0,7.1871e-13)" writing-mode="lr" x="623.4642769949779" xml:space="preserve" y="662.9555308024087" zvalue="209">全站公用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1242.89,538.875) scale(1,1) translate(9.50764e-13,0)" writing-mode="lr" x="1242.89" xml:space="preserve" y="543.38" zvalue="260">15160</text>
 </g>
 <g id="ButtonClass">
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="689.27" y="726.17" zvalue="194"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="587.37" y="726.17" zvalue="195"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="587.37" y="685.67" zvalue="196"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="199">
   <path class="kv110" d="M 1051.18 694.01 L 1388.18 694.01" stroke-width="6" zvalue="2"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674266251269" ObjectName="110kV母线"/>
   </metadata>
  <path d="M 1051.18 694.01 L 1388.18 694.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="191">
   <use class="kv110" height="30" transform="rotate(0,1213.67,439.707) scale(1.98323,0.522926) translate(-598.264,393.995)" width="7" x="1206.73250554831" xlink:href="#ACLineSegment:线路_0" y="431.863085176489" zvalue="22"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450422177797" ObjectName="110kV胞波II回线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1213.67,439.707) scale(1.98323,0.522926) translate(-598.264,393.995)" width="7" x="1206.73250554831" y="431.863085176489"/></g>
 </g>
 <g id="BreakerClass">
  <g id="189">
   <use class="kv110" height="20" transform="rotate(0,1213.05,599.937) scale(1.5542,1.35421) translate(-429.782,-153.379)" width="10" x="1205.282093471367" xlink:href="#Breaker:开关_0" y="586.394642548927" zvalue="23"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924607737861" ObjectName="151"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1213.05,599.937) scale(1.5542,1.35421) translate(-429.782,-153.379)" width="10" x="1205.282093471367" y="586.394642548927"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="188">
   <use class="kv110" height="30" transform="rotate(0,1214.22,534.504) scale(0.947693,0.6712) translate(66.6253,256.905)" width="15" x="1207.107922881834" xlink:href="#Disconnector:刀闸_0" y="524.436459387525" zvalue="26"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450422112261" ObjectName="1516"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1214.22,534.504) scale(0.947693,0.6712) translate(66.6253,256.905)" width="15" x="1207.107922881834" y="524.436459387525"/></g>
  <g id="186">
   <use class="kv110" height="30" transform="rotate(180,1213.07,665.773) scale(-0.947693,-0.6712) translate(-2493.49,-1662.62)" width="15" x="1205.965820983863" xlink:href="#Disconnector:刀闸_0" y="655.7051971594835" zvalue="30"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450421915653" ObjectName="1511"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1213.07,665.773) scale(-0.947693,-0.6712) translate(-2493.49,-1662.62)" width="15" x="1205.965820983863" y="655.7051971594835"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="187">
   <use class="kv110" height="20" transform="rotate(90,1244.18,499.231) scale(1.24619,-1.0068) translate(-244.559,-995.022)" width="10" x="1237.946798569645" xlink:href="#GroundDisconnector:地刀_0" y="489.1631744799927" zvalue="28"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450422046725" ObjectName="15167"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1244.18,499.231) scale(1.24619,-1.0068) translate(-244.559,-995.022)" width="10" x="1237.946798569645" y="489.1631744799927"/></g>
  <g id="184">
   <use class="kv110" height="20" transform="rotate(90,1243.18,635.517) scale(1.24619,-1.0068) translate(-244.361,-1266.67)" width="10" x="1236.946798569645" xlink:href="#GroundDisconnector:地刀_0" y="625.4488952030085" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450421850117" ObjectName="15117"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1243.18,635.517) scale(1.24619,-1.0068) translate(-244.361,-1266.67)" width="10" x="1236.946798569645" y="625.4488952030085"/></g>
  <g id="202">
   <use class="kv110" height="20" transform="rotate(90,1244.88,554.874) scale(1.24619,-1.0068) translate(-244.697,-1105.93)" width="10" x="1238.647993113054" xlink:href="#GroundDisconnector:地刀_0" y="544.8059640822714" zvalue="259"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450422308869" ObjectName="15160"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1244.88,554.874) scale(1.24619,-1.0068) translate(-244.697,-1105.93)" width="10" x="1238.647993113054" y="544.8059640822714"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="183">
   <path class="kv110" d="M 1213.13 675.67 L 1213.13 694.01" stroke-width="1" zvalue="36"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@1" LinkObjectIDznd="199@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1213.13 675.67 L 1213.13 694.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="165">
   <path class="kv110" d="M 1213.67 447.47 L 1213.67 524.77" stroke-width="1" zvalue="98"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="188@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1213.67 447.47 L 1213.67 524.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv110" d="M 1214.27 544.4 L 1214.27 586.98" stroke-width="1" zvalue="99"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@1" LinkObjectIDznd="189@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1214.27 544.4 L 1214.27 586.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv110" d="M 1213.16 612.87 L 1213.16 656.04" stroke-width="1" zvalue="100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189@1" LinkObjectIDznd="186@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1213.16 612.87 L 1213.16 656.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv110" d="M 1233.36 635.58 L 1213.16 635.58" stroke-width="1" zvalue="101"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@0" LinkObjectIDznd="163" MaxPinNum="2"/>
   </metadata>
  <path d="M 1233.36 635.58 L 1213.16 635.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv110" d="M 1234.36 499.29 L 1213.67 499.29" stroke-width="1" zvalue="103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@0" LinkObjectIDznd="165" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.36 499.29 L 1213.67 499.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="kv110" d="M 1190.31 480.92 L 1213.67 480.92" stroke-width="1" zvalue="104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="165" MaxPinNum="2"/>
   </metadata>
  <path d="M 1190.31 480.92 L 1213.67 480.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv110" d="M 1235.06 554.94 L 1214.27 554.94" stroke-width="1" zvalue="261"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="164" MaxPinNum="2"/>
   </metadata>
  <path d="M 1235.06 554.94 L 1214.27 554.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="182">
   <use class="kv110" height="38" transform="rotate(0,1190.51,464.852) scale(0.902882,0.902882) translate(126.211,48.1561)" width="38" x="1173.355146862212" xlink:href="#Accessory:避雷器PT1_0" y="447.6967684053118" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450421719045" ObjectName="110kV东弄线电压互感器"/>
   </metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,1190.51,464.852) scale(0.902882,0.902882) translate(126.211,48.1561)" width="38" x="1173.355146862212" y="447.6967684053118"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
</svg>