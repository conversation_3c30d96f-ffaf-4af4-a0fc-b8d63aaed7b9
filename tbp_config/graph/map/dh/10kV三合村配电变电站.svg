<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549581185026" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:586_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.75" x2="10.08333333333333" y1="17.11381925541057" y2="17.11381925541057"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.98518194534241" x2="9.999999999999998" y1="9.386991192770131" y2="16.99800064973551"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00299079104462" x2="19.66666666666667" y1="9.301854138598824" y2="16.8218426122152"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:586_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="30.58333333333333" y2="36.5"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="21" y1="36.5" y2="40.5"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="10" y1="36.5" y2="40.5"/>
  </symbol>
  <symbol id="Accessory:传输线_0" viewBox="0,0,12,22">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="1"/>
   <path d="M 1.16667 1 L 10.8333 1 L 6 7.63889 L 1.16667 1 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="14.27777777777778" y2="7.638888888888888"/>
   <path d="M 1.16667 20.9167 L 10.8333 20.9167 L 6 14.2778 L 1.16667 20.9167 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_0" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="3.333333333333332" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="24.08333333333333" y2="29.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="7" y1="19.5" y2="24.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_1" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="18.25" y2="32.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_2" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="4" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="10" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="25.83333333333333" y2="32.41666666666667"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Accessory:4绕组PT带接地_0" viewBox="0,0,30,35">
   <use terminal-index="0" type="0" x="14.94238998217831" xlink:href="#terminal" y="31.09945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="13" y1="18.19406992327969" y2="17.19406992327969"/>
   <rect fill-opacity="0" height="14.44" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,4.46,16.81) scale(1,-1) translate(0,-1024.21)" width="5.58" x="1.67" y="9.58"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="13" y1="14.19406992327969" y2="15.19406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.583333333333325" x2="4.583333333333325" y1="30.69406992327969" y2="16.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.19406992327969" y2="14.19406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.583333333333325" x2="3.583333333333325" y1="15.69406992327969" y2="20.69406992327969"/>
   <path d="M 20.6667 26.75 L 20.6667 30.75 L 4.66667 30.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.583333333333325" x2="5.583333333333325" y1="15.69406992327969" y2="20.69406992327969"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5" x2="26.5" y1="16.5" y2="16.5"/>
   <path d="M 20.75 22.8333 L 26.5833 22.8333 L 26.5833 6.83333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.59610027172149" x2="4.59610027172149" y1="9.527403256613018" y2="6.379186935280762"/>
   <ellipse cx="20.73" cy="22.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.625884107343747" x2="2.349068262659902" y1="6.31127839976627" y2="6.31127839976627"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.792550774010413" x2="3.182401595993234" y1="5.06127839976627" y2="5.06127839976627"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="5.209217440677081" x2="3.765734929326568" y1="3.81127839976627" y2="3.81127839976627"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="23.09906826265991" y1="22.70662962336982" y2="23.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="18.29906826265991" y1="22.70662962336982" y2="23.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="20.69906826265991" y1="20.23154965466559" y2="22.7066296233698"/>
   <ellipse cx="14.48" cy="22.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.73" cy="16.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.44906826265991" x2="16.84906826265991" y1="22.70662962336982" y2="23.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.44906826265991" x2="14.44906826265991" y1="20.23154965466559" y2="22.7066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="18.29906826265991" y1="16.45662962336982" y2="17.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="23.09906826265991" y1="16.45662962336982" y2="17.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69906826265991" x2="20.69906826265991" y1="13.9815496546656" y2="16.4566296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.44906826265991" x2="12.04906826265991" y1="22.70662962336982" y2="23.94416960772192"/>
   <ellipse cx="14.48" cy="16.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="28.79255077401042" x2="24.51573492932657" y1="6.727945066432934" y2="6.727945066432934"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.95921744067708" x2="25.34906826265991" y1="5.477945066432934" y2="5.477945066432934"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.37588410734375" x2="25.93240159599324" y1="4.227945066432941" y2="4.227945066432941"/>
  </symbol>
  <symbol id="Disconnector:20210316_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,18.75) scale(1,1) translate(0,0)" width="6" x="3" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="6.66666666666667" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="6.727157046327149" y2="14.02109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="6.734473004260392" y2="14.02109851866368"/>
  </symbol>
  <symbol id="Disconnector:20210316_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,18.75) scale(1,1) translate(0,0)" width="6" x="3" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="0.25" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="0.7271570463271502" y2="8.021098518663681"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="0.7344730042603906" y2="8.021098518663679"/>
  </symbol>
  <symbol id="Disconnector:20210316_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Compensator:三合电容_0" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="2.199999999999999"/>
   <path d="M 9.83333 9.08333 L 16.6667 9.08333 L 16.6667 14.9167" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="35.16666666666667" y2="38.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.971296296296289" x2="5.971296296296289" y1="22.13611111111111" y2="24.37685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00833333333333" x2="6" y1="24.35833333333333" y2="24.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.00833333333333" x2="6.00833333333333" y1="11.35833333333333" y2="9.154629629629628"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.916666666666666" x2="10.00833333333334" y1="9.108333333333336" y2="9.108333333333336"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00833333333333" x2="10.00833333333333" y1="9.043518518518514" y2="13.775"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00833333333333" x2="10.00833333333333" y1="13.85833333333333" y2="16.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.94090594744122" x2="4.94090594744122" y1="38.44166666666666" y2="36.44166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.09166666666667" x2="15.09166666666667" y1="36.44166666666666" y2="38.44166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.258333333333324" x2="2.258333333333324" y1="11.10833333333333" y2="22.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.08333333333333" x2="5.000000000000002" y1="38.60833333333333" y2="38.60833333333333"/>
   <path d="M 6.00833 14.9572 A 2.96392 1.81747 180 0 1 6.00833 11.3223" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.00833 18.5921 A 2.96392 1.81747 180 0 1 6.00833 14.9572" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.00833 22.1271 A 2.96392 1.81747 180 0 1 6.00833 18.4921" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.50833333333333" x2="8.508333333333331" y1="16.60833333333333" y2="16.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.50833333333333" x2="8.508333333333331" y1="17.60833333333333" y2="17.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10" x2="10" y1="1.999999999999996" y2="9.166666666666663"/>
   <path d="M 5.26667 30.3333 A 4.91667 4.75 -450 1 1 10.0167 35.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 5.33333 30.35 L 10 30.2667 L 10 17.6667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.67247097844111" x2="15.97595356550579" y1="14.92589623050598" y2="14.12217410254644"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.67247097844115" x2="17.36898839137647" y1="14.92589623050598" y2="14.12217410254644"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.34033536023585" x2="17.15616362631287" y1="18.25241281567187" y2="18.25241281567187"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.11783674221487" x2="17.30449603832691" y1="17.88227762516416" y2="17.88227762516416"/>
   <rect fill-opacity="0" height="4.29" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,16.67,14.39) scale(1,1) translate(0,0)" width="2.33" x="15.5" y="12.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.67247097844114" x2="16.67247097844114" y1="16.53334048642508" y2="17.49780703997653"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.82117191818687" x2="17.60116086235491" y1="17.51214243465651" y2="17.51214243465651"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷带壁雷器_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="28.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11.75" y1="17.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="14.75" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="12" y1="11.75" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="9" y1="12.75" y2="12.75"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.04,18.25) scale(1,1) translate(0,0)" width="3.25" x="9.42" y="14.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.5" y1="10.83333333333333" y2="10.83333333333333"/>
   <path d="M 5.025 2.775 L 4.16667 9.5 L 5.025 7.60833 L 5.91667 9.5 L 5.025 2.775" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 5 23.75 L 11 23.75 L 11 17.75 L 10.25 20.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.000411522633746" x2="5.000411522633746" y1="7.627914951989029" y2="28.23902606310013"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV三合村配电变电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="81" id="1" preserveAspectRatio="xMidYMid slice" width="266" x="53" xlink:href="logo.png" y="38"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,186,78.5) scale(1,1) translate(0,0)" writing-mode="lr" x="186" xml:space="preserve" y="83" zvalue="1439"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="2" stroke="rgb(0,0,0)" text-anchor="middle" x="208.5" xml:space="preserve" y="72.2002936782294" zvalue="1440">10kV三合村配电</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="2" stroke="rgb(0,0,0)" text-anchor="middle" writing-mode="lr" x="208.5" xml:space="preserve" y="98.2002936782294" zvalue="1440">变电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="141" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,174.25,393) scale(1,1) translate(0,0)" width="72.88" x="137.81" y="381" zvalue="1587"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,174.25,393) scale(1,1) translate(0,0)" writing-mode="lr" x="174.25" xml:space="preserve" y="397.5" zvalue="1587">小电流接地</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="140" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,74.4375,320) scale(1,1) translate(0,0)" width="72.88" x="38" y="308" zvalue="1588"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.4375,320) scale(1,1) translate(0,0)" writing-mode="lr" x="74.44" xml:space="preserve" y="324.5" zvalue="1588">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="139" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,74.4375,393) scale(1,1) translate(0,0)" width="72.88" x="38" y="381" zvalue="1589"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.4375,393) scale(1,1) translate(0,0)" writing-mode="lr" x="74.44" xml:space="preserve" y="397.5" zvalue="1589">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="137" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,74.4375,355.5) scale(1,1) translate(0,0)" width="72.88" x="38" y="343.5" zvalue="1591"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,74.4375,355.5) scale(1,1) translate(0,0)" writing-mode="lr" x="74.44" xml:space="preserve" y="360" zvalue="1591">信号一览</text>
  <line fill="none" id="67" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382" x2="382" y1="11" y2="1041" zvalue="58"/>
  <line fill="none" id="666" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="146.8704926140824" y2="146.8704926140824" zvalue="1056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="159" y2="159"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="159" y2="159"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="277.5" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="254.75" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="254.75" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="277.5" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="254.75" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="254.75" y2="277.5"/>
  <line fill="none" id="613" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="616.8704926140824" y2="616.8704926140824" zvalue="1058"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="999.0816" y2="1027"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="609" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,952) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="958" zvalue="1062">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="608" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50,986) scale(1,1) translate(0,0)" writing-mode="lr" x="50" xml:space="preserve" y="992" zvalue="1063">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="607" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232,986) scale(1,1) translate(0,0)" writing-mode="lr" x="232" xml:space="preserve" y="992" zvalue="1064">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="606" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="49" xml:space="preserve" y="1020" zvalue="1065">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="605" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="231" xml:space="preserve" y="1020" zvalue="1066">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="602" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.5,646.5) scale(1,1) translate(0,0)" writing-mode="lr" x="73.5" xml:space="preserve" y="651" zvalue="1069">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="599" stroke="rgb(255,255,255)" text-anchor="middle" x="203.3359375" xml:space="preserve" y="454.5" zvalue="1072">6.3kV母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="599" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="203.3359375" xml:space="preserve" y="471.5" zvalue="1072">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="598" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,327.333,458.5) scale(1,1) translate(0,0)" writing-mode="lr" x="327.3333333333335" xml:space="preserve" y="463" zvalue="1073">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="596" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85,489.5) scale(1,1) translate(0,0)" writing-mode="lr" x="85" xml:space="preserve" y="494" zvalue="1075">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="595" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85,515) scale(1,1) translate(0,0)" writing-mode="lr" x="85" xml:space="preserve" y="519.5" zvalue="1076">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="594" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85,540.5) scale(1,1) translate(0,0)" writing-mode="lr" x="85" xml:space="preserve" y="545" zvalue="1077">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="593" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84,565) scale(1,1) translate(0,0)" writing-mode="lr" x="84" xml:space="preserve" y="569.5" zvalue="1078">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="592" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85,591.5) scale(1,1) translate(0,0)" writing-mode="lr" x="85" xml:space="preserve" y="596" zvalue="1079">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.054,954) scale(1,1) translate(0,0)" writing-mode="lr" x="233.05" xml:space="preserve" y="960" zvalue="1080">SanHeCun-01-2020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,143.054,986) scale(1,1) translate(0,0)" writing-mode="lr" x="143.05" xml:space="preserve" y="992" zvalue="1081">段勇</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,323.054,986) scale(1,1) translate(0,0)" writing-mode="lr" x="323.05" xml:space="preserve" y="992" zvalue="1082">20210105</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47,173) scale(1,1) translate(0,0)" writing-mode="lr" x="47" xml:space="preserve" y="177.5" zvalue="1083">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227,173) scale(1,1) translate(0,0)" writing-mode="lr" x="227" xml:space="preserve" y="177.5" zvalue="1084">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.6875,196.25) scale(1,1) translate(0,0)" writing-mode="lr" x="54.69" xml:space="preserve" y="200.75" zvalue="1085">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.125,197.25) scale(1,1) translate(0,0)" writing-mode="lr" x="234.13" xml:space="preserve" y="201.75" zvalue="1086">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.1875,245) scale(1,1) translate(0,0)" writing-mode="lr" x="54.19" xml:space="preserve" y="249.5" zvalue="1087">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1268.25,274.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1268.25" xml:space="preserve" y="279" zvalue="1442">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1762.38,695) scale(1,1) translate(0,0)" writing-mode="lr" x="1762.38" xml:space="preserve" y="699.5" zvalue="1443">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" x="1154.75" xml:space="preserve" y="467.625" zvalue="1444">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1154.75" xml:space="preserve" y="484.625" zvalue="1444">2000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1114.88,639.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1114.88" xml:space="preserve" y="644.25" zvalue="1455">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1113.6,327.135) scale(1,1) translate(0,0)" writing-mode="lr" x="1113.6" xml:space="preserve" y="331.63" zvalue="1465">601</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1175.54,103.596) scale(1,1) translate(0,0)" writing-mode="lr" x="1175.54" xml:space="preserve" y="108.1" zvalue="1468">6.3kV三合村线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1140.88,227.192) scale(1,1) translate(0,0)" writing-mode="lr" x="1140.88" xml:space="preserve" y="231.69" zvalue="1470">6548</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,847.667,417.75) scale(1,1) translate(8.71155e-14,0)" writing-mode="lr" x="847.67" xml:space="preserve" y="422.25" zvalue="1473">6.3kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,816.083,325.333) scale(1,1) translate(0,0)" writing-mode="lr" x="816.08" xml:space="preserve" y="329.83" zvalue="1475">6901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1637.39,554.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1637.39" xml:space="preserve" y="558.89" zvalue="1482">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1605.81,646.806) scale(1,1) translate(0,0)" writing-mode="lr" x="1605.81" xml:space="preserve" y="651.3099999999999" zvalue="1484">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" x="667.2265625" xml:space="preserve" y="1007.515622562832" zvalue="1491">10kV1号电容</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="667.2265625" xml:space="preserve" y="1024.515622562832" zvalue="1491">器组</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" x="748.2265625" xml:space="preserve" y="1007.515622562832" zvalue="1493">10kV2号电容</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="748.2265625" xml:space="preserve" y="1024.515622562832" zvalue="1493">器组</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,644.333,887.444) scale(1,1) translate(0,0)" writing-mode="lr" x="644.33" xml:space="preserve" y="891.9400000000001" zvalue="1495">0511</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,722.667,887.444) scale(1,1) translate(0,0)" writing-mode="lr" x="722.67" xml:space="preserve" y="891.9400000000001" zvalue="1500">0512</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,734.485,739.75) scale(1,1) translate(0,0)" writing-mode="lr" x="734.48" xml:space="preserve" y="744.25" zvalue="1505">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,770,816.111) scale(1,1) translate(0,0)" writing-mode="lr" x="770" xml:space="preserve" y="820.61" zvalue="1517">05167</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,921.778,928.556) scale(1,1) translate(0,0)" writing-mode="lr" x="921.78" xml:space="preserve" y="933.0599999999999" zvalue="1520">10kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,892.13,739.75) scale(1,1) translate(0,0)" writing-mode="lr" x="892.13" xml:space="preserve" y="744.25" zvalue="1526">0521</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1145.96,978.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1145.96" xml:space="preserve" y="982.61" zvalue="1529">10kV铜壁关线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1119.56,875.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1119.56" xml:space="preserve" y="879.9400000000001" zvalue="1535">0536</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1172.52,739.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1172.52" xml:space="preserve" y="744.25" zvalue="1539">053</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1205.5,818.333) scale(1,1) translate(-7.90368e-13,0)" writing-mode="lr" x="1205.5" xml:space="preserve" y="822.83" zvalue="1545">05317</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1385.96,978.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1385.96" xml:space="preserve" y="982.61" zvalue="1548">10kV堂梨坝线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1359.56,875.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1359.56" xml:space="preserve" y="879.9400000000001" zvalue="1552">0546</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1412.52,739.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1412.52" xml:space="preserve" y="744.25" zvalue="1556">054</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1445.5,818.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1445.5" xml:space="preserve" y="822.83" zvalue="1560">05417</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1613.96,978.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1613.96" xml:space="preserve" y="982.61" zvalue="1564">10kV小寨线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1587.56,875.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1587.56" xml:space="preserve" y="879.9400000000001" zvalue="1568">0556</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1640.52,739.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1640.52" xml:space="preserve" y="744.25" zvalue="1572">055</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1673.5,818.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1673.5" xml:space="preserve" y="822.83" zvalue="1576">05517</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,190.961,320.591) scale(1,1) translate(0,0)" writing-mode="lr" x="190.96" xml:space="preserve" y="325.09" zvalue="1583">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,295.961,320.591) scale(1,1) translate(0,0)" writing-mode="lr" x="295.96" xml:space="preserve" y="325.09" zvalue="1584">通道</text>
  <rect fill="none" fill-opacity="0" height="47" id="54" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,630.5,889.5) scale(1,1) translate(0,0)" width="279" x="491" y="866" zvalue="1629"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" x="541.5" xml:space="preserve" y="884.5" zvalue="1632">没有辅助接点信号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="541.5" xml:space="preserve" y="901.5" zvalue="1632">未采集</text>
 </g>
 <g id="ButtonClass">
  <g href="小电流装置20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="137.81" y="381" zvalue="1587"/></g>
  <g href="全站公用20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="38" y="308" zvalue="1588"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="38" y="381" zvalue="1589"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="38" y="343.5" zvalue="1591"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="235">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="235" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,205.222,489.667) scale(1,1) translate(-3.9598e-14,0)" writing-mode="lr" x="205.34" xml:space="preserve" y="494.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123819028484" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,146.222,172.167) scale(1,1) translate(0,0)" writing-mode="lr" x="146.42" xml:space="preserve" y="177.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123830693892" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,325.222,173.167) scale(1,1) translate(0,0)" writing-mode="lr" x="325.42" xml:space="preserve" y="178.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123830759428" ObjectName="LOAD_QSUM"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="138" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,145,196.5) scale(1,1) translate(0,0)" writing-mode="lr" x="145.2" xml:space="preserve" y="201.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123819159556" ObjectName="F"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,325,197.5) scale(1,1) translate(0,0)" writing-mode="lr" x="325.2" xml:space="preserve" y="202.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123819683844" ObjectName="F"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="145" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,145,242.5) scale(1,1) translate(0,0)" writing-mode="lr" x="145.2" xml:space="preserve" y="247.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123820273668" ObjectName="YW1"/>
   </metadata>
  </g>
  <g id="146">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="146" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,205.222,513.5) scale(1,1) translate(-3.9598e-14,0)" writing-mode="lr" x="205.34" xml:space="preserve" y="518.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123818766340" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="147">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="147" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,205.222,537.5) scale(1,1) translate(-3.9598e-14,0)" writing-mode="lr" x="205.34" xml:space="preserve" y="542.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123818831876" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="148" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,205.222,561.5) scale(1,1) translate(-3.9598e-14,0)" writing-mode="lr" x="205.34" xml:space="preserve" y="566.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123818897412" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="149">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,205.222,586.5) scale(1,1) translate(-3.9598e-14,0)" writing-mode="lr" x="205.34" xml:space="preserve" y="591.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123819225092" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="154">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,329.222,489.667) scale(1,1) translate(-6.71315e-14,0)" writing-mode="lr" x="329.34" xml:space="preserve" y="494.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123819552772" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="153">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="153" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,329.222,513.5) scale(1,1) translate(-6.71315e-14,0)" writing-mode="lr" x="329.34" xml:space="preserve" y="518.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123819290628" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="152">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="152" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,329.222,537.5) scale(1,1) translate(-6.71315e-14,0)" writing-mode="lr" x="329.34" xml:space="preserve" y="542.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123819356164" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="151">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="151" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,329.222,561.5) scale(1,1) translate(-6.71315e-14,0)" writing-mode="lr" x="329.34" xml:space="preserve" y="566.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123819421700" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="13" id="150" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,329.222,586.5) scale(1,1) translate(-6.71315e-14,0)" writing-mode="lr" x="329.34" xml:space="preserve" y="591.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123819749380" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="155">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="155" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1010.31,595.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1009.76" xml:space="preserve" y="601.65" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123819814916" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="156">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="156" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1010.31,620.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1009.76" xml:space="preserve" y="626.65" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123819880452" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="157">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="157" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1010.31,314) scale(1,1) translate(0,0)" writing-mode="lr" x="1009.76" xml:space="preserve" y="320.28" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123819945988" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="158">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="158" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1010.31,339) scale(1,1) translate(0,0)" writing-mode="lr" x="1009.76" xml:space="preserve" y="345.28" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123820011524" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="159">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="159" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1010.31,645.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1009.76" xml:space="preserve" y="651.65" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123820077060" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="160">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="160" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1010.31,364) scale(1,1) translate(0,0)" writing-mode="lr" x="1009.76" xml:space="preserve" y="370.28" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123820404740" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="161">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="161" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,633.818,728.837) scale(1,1) translate(0,0)" writing-mode="lr" x="633.27" xml:space="preserve" y="733.61" zvalue="1">Ia:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123825254404" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="162">
   <text Format="f5.1" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="162" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,625.818,749.663) scale(1,1) translate(0,-6.50292e-13)" writing-mode="lr" x="625.35" xml:space="preserve" y="754.4400000000001" zvalue="1">Q:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123825516548" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="163">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="163" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1141.57,998.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1141.1" xml:space="preserve" y="1003.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123826368516" ObjectName="P"/>
   </metadata>
  </g>
  <g id="164">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="164" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1141.57,1015.72) scale(1,1) translate(0,0)" writing-mode="lr" x="1141.1" xml:space="preserve" y="1020.5" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123826434052" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="165">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="165" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1141.57,1032.72) scale(1,1) translate(0,0)" writing-mode="lr" x="1141.1" xml:space="preserve" y="1037.5" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123826499588" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="166">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="166" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1383.57,997.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1383.1" xml:space="preserve" y="1002.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123828334596" ObjectName="P"/>
   </metadata>
  </g>
  <g id="167">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="167" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1383.57,1014.72) scale(1,1) translate(0,0)" writing-mode="lr" x="1383.1" xml:space="preserve" y="1019.5" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123828400132" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="168">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="168" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1383.57,1031.72) scale(1,1) translate(0,0)" writing-mode="lr" x="1383.1" xml:space="preserve" y="1036.5" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123828465668" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="169">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="169" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1612.57,994.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1612.1" xml:space="preserve" y="999.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123829776388" ObjectName="P"/>
   </metadata>
  </g>
  <g id="170">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="170" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1612.57,1011.72) scale(1,1) translate(0,0)" writing-mode="lr" x="1612.1" xml:space="preserve" y="1016.5" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123829841924" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="171">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="171" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1612.57,1028.72) scale(1,1) translate(0,0)" writing-mode="lr" x="1612.1" xml:space="preserve" y="1033.5" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123829907460" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="91" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,850.889,440.5) scale(1,1) translate(-1.82965e-13,0)" writing-mode="lr" x="851.01" xml:space="preserve" y="445.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123819028484" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1634.89,528.5) scale(1,1) translate(-3.57048e-13,0)" writing-mode="lr" x="1635.01" xml:space="preserve" y="533.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123819552772" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="BusbarSectionClass">
  <g id="12">
   <path class="v6300" d="M 791.25 275.5 L 1228.75 275.5" stroke-width="4" zvalue="1441"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674232107013" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674232107013"/></metadata>
  <path d="M 791.25 275.5 L 1228.75 275.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv10" d="M 602.25 696 L 1724.75 696" stroke-width="4" zvalue="1442"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674232172548" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674232172548"/></metadata>
  <path d="M 602.25 696 L 1724.75 696" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="18">
   <g id="180">
    <use class="v6300" height="50" transform="rotate(0,1086.31,476.188) scale(1.8125,1.8125) translate(-474.78,-193.151)" width="30" x="1059.13" xlink:href="#PowerTransformer2:586_0" y="430.88" zvalue="1443"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874418618372" ObjectName="6.3"/>
    </metadata>
   </g>
   <g id="181">
    <use class="kv10" height="50" transform="rotate(0,1086.31,476.188) scale(1.8125,1.8125) translate(-474.78,-193.151)" width="30" x="1059.13" xlink:href="#PowerTransformer2:586_1" y="430.88" zvalue="1443"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874418552836" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399441448964" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399441448964"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1086.31,476.188) scale(1.8125,1.8125) translate(-474.78,-193.151)" width="30" x="1059.13" y="430.88"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="25">
   <use class="kv10" height="22" transform="rotate(0,1086,549.5) scale(1.25,1.25) translate(-215.7,-107.15)" width="12" x="1078.5" xlink:href="#Accessory:传输线_0" y="535.75" zvalue="1449"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449507426310" ObjectName="#1主变10kV侧电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1086,549.5) scale(1.25,1.25) translate(-215.7,-107.15)" width="12" x="1078.5" y="535.75"/></g>
  <g id="27">
   <use class="kv10" height="20" transform="rotate(270,1113.21,579.5) scale(1.5625,1.5625) translate(-395.131,-202.995)" width="20" x="1097.586532592773" xlink:href="#Accessory:线路PT3_0" y="563.875" zvalue="1451"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449507491846" ObjectName="#1主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1113.21,579.5) scale(1.5625,1.5625) translate(-395.131,-202.995)" width="20" x="1097.586532592773" y="563.875"/></g>
  <g id="34">
   <use class="v6300" height="20" transform="rotate(270,1113.21,419) scale(1.5625,1.5625) translate(-395.131,-145.215)" width="20" x="1097.586532592773" xlink:href="#Accessory:线路PT3_0" y="403.375" zvalue="1458"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449507557382" ObjectName="#1主变6.3kV侧避雷器1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1113.21,419) scale(1.5625,1.5625) translate(-395.131,-145.215)" width="20" x="1097.586532592773" y="403.375"/></g>
  <g id="36">
   <use class="v6300" height="20" transform="rotate(270,1113.21,369.308) scale(1.5625,1.5625) translate(-395.131,-127.326)" width="20" x="1097.586532592773" xlink:href="#Accessory:线路PT3_0" y="353.6826923076923" zvalue="1461"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449507622918" ObjectName="#1主变6.3kV侧避雷器2"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1113.21,369.308) scale(1.5625,1.5625) translate(-395.131,-127.326)" width="20" x="1097.586532592773" y="353.6826923076923"/></g>
  <g id="2">
   <use class="v6300" height="35" transform="rotate(180,845.583,383.667) scale(1.36111,1.28571) translate(-218.922,-80.2593)" width="30" x="825.1666666666666" xlink:href="#Accessory:4绕组PT带接地_0" y="361.1666666666667" zvalue="1472"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449507819526" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(180,845.583,383.667) scale(1.36111,1.28571) translate(-218.922,-80.2593)" width="30" x="825.1666666666666" y="361.1666666666667"/></g>
  <g id="9">
   <use class="v6300" height="20" transform="rotate(270,879.958,299.958) scale(1.5625,1.5625) translate(-311.16,-102.36)" width="20" x="864.3333333333334" xlink:href="#Accessory:线路PT3_0" y="284.3333333333334" zvalue="1478"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449507950597" ObjectName="6.3kV母线电压互感器侧避雷器2"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,879.958,299.958) scale(1.5625,1.5625) translate(-311.16,-102.36)" width="20" x="864.3333333333334" y="284.3333333333334"/></g>
  <g id="29">
   <use class="kv10" height="35" transform="rotate(180,1635.31,588.472) scale(1.36111,-1.28571) translate(-428.44,-1041.17)" width="30" x="1614.888888888889" xlink:href="#Accessory:4绕组PT带接地_0" y="565.9722258779738" zvalue="1481"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449508147206" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(180,1635.31,588.472) scale(1.36111,-1.28571) translate(-428.44,-1041.17)" width="30" x="1614.888888888889" y="565.9722258779738"/></g>
  <g id="21">
   <use class="kv10" height="20" transform="rotate(90,1669.68,672.181) scale(1.5625,-1.5625) translate(-595.46,-1096.75)" width="20" x="1654.055555555556" xlink:href="#Accessory:线路PT3_0" y="656.555559211307" zvalue="1487"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449508016133" ObjectName="10kV母线电压互感器侧避雷器2"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1669.68,672.181) scale(1.5625,-1.5625) translate(-595.46,-1096.75)" width="20" x="1654.055555555556" y="656.555559211307"/></g>
  <g id="76">
   <use class="kv10" height="22" transform="rotate(0,709.111,814.167) scale(1.25,1.25) translate(-140.322,-160.083)" width="12" x="701.6111111111111" xlink:href="#Accessory:传输线_0" y="800.4166666666667" zvalue="1508"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449508474885" ObjectName="10kV电容器电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,709.111,814.167) scale(1.25,1.25) translate(-140.322,-160.083)" width="12" x="701.6111111111111" y="800.4166666666667"/></g>
  <g id="78">
   <use class="kv10" height="20" transform="rotate(90,682.767,847.5) scale(-1.5625,1.5625) translate(-1114.11,-299.475)" width="20" x="667.1420881483289" xlink:href="#Accessory:线路PT3_0" y="831.8749999999999" zvalue="1511"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449508540422" ObjectName="10kV电容器避雷器1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,682.767,847.5) scale(-1.5625,1.5625) translate(-1114.11,-299.475)" width="20" x="667.1420881483289" y="831.8749999999999"/></g>
  <g id="80">
   <use class="kv10" height="20" transform="rotate(90,683.878,783.5) scale(-1.5625,1.5625) translate(-1115.94,-276.435)" width="20" x="668.2531992594402" xlink:href="#Accessory:线路PT3_0" y="767.8749999999999" zvalue="1514"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449508605958" ObjectName="10kV电容器避雷器2"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,683.878,783.5) scale(-1.5625,1.5625) translate(-1115.94,-276.435)" width="20" x="668.2531992594402" y="767.8749999999999"/></g>
  <g id="56">
   <use class="kv10" height="22" transform="rotate(0,921.556,813.056) scale(1.25,1.25) translate(-182.811,-159.861)" width="12" x="914.0555555555555" xlink:href="#Accessory:传输线_0" y="799.3055555555557" zvalue="1522"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449508868102" ObjectName="1号站用变电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,921.556,813.056) scale(1.25,1.25) translate(-182.811,-159.861)" width="12" x="914.0555555555555" y="799.3055555555557"/></g>
  <g id="92">
   <use class="kv10" height="22" transform="rotate(0,1146.22,829.056) scale(0.777792,1.25) translate(326.132,-163.061)" width="12" x="1141.55551232232" xlink:href="#Accessory:传输线_0" y="815.3055555555557" zvalue="1531"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449509064710" ObjectName="铜壁关线电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1146.22,829.056) scale(0.777792,1.25) translate(326.132,-163.061)" width="12" x="1141.55551232232" y="815.3055555555557"/></g>
  <g id="102">
   <use class="kv10" height="20" transform="rotate(0,1123.21,809.5) scale(1.5625,1.5625) translate(-398.731,-285.795)" width="20" x="1107.586532592773" xlink:href="#Accessory:线路PT3_0" y="793.8749999999999" zvalue="1542"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449509195782" ObjectName="铜壁关线避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1123.21,809.5) scale(1.5625,1.5625) translate(-398.731,-285.795)" width="20" x="1107.586532592773" y="793.8749999999999"/></g>
  <g id="119">
   <use class="kv10" height="22" transform="rotate(0,1386.22,829.056) scale(0.777792,1.25) translate(394.697,-163.061)" width="12" x="1381.55551232232" xlink:href="#Accessory:传输线_0" y="815.3055555555557" zvalue="1549"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449509654533" ObjectName="堂梨坝线电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1386.22,829.056) scale(0.777792,1.25) translate(394.697,-163.061)" width="12" x="1381.55551232232" y="815.3055555555557"/></g>
  <g id="112">
   <use class="kv10" height="20" transform="rotate(0,1363.21,809.5) scale(1.5625,1.5625) translate(-485.131,-285.795)" width="20" x="1347.586532592773" xlink:href="#Accessory:线路PT3_0" y="793.8749999999999" zvalue="1558"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449509523462" ObjectName="堂梨坝线避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1363.21,809.5) scale(1.5625,1.5625) translate(-485.131,-285.795)" width="20" x="1347.586532592773" y="793.8749999999999"/></g>
  <g id="134">
   <use class="kv10" height="22" transform="rotate(0,1614.22,829.056) scale(0.777792,1.25) translate(459.835,-163.061)" width="12" x="1609.55551232232" xlink:href="#Accessory:传输线_0" y="815.3055555555557" zvalue="1565"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449510047749" ObjectName="小寨线电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1614.22,829.056) scale(0.777792,1.25) translate(459.835,-163.061)" width="12" x="1609.55551232232" y="815.3055555555557"/></g>
  <g id="127">
   <use class="kv10" height="20" transform="rotate(0,1591.21,809.5) scale(1.5625,1.5625) translate(-567.211,-285.795)" width="20" x="1575.586532592773" xlink:href="#Accessory:线路PT3_0" y="793.8749999999999" zvalue="1574"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449509916677" ObjectName="小寨线避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1591.21,809.5) scale(1.5625,1.5625) translate(-567.211,-285.795)" width="20" x="1575.586532592773" y="793.8749999999999"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="26">
   <path class="kv10" d="M 1086 537 L 1086.31 537" stroke-width="1" zvalue="1450"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="25@0" LinkObjectIDznd="32" MaxPinNum="2"/>
   </metadata>
  <path d="M 1086 537 L 1086.31 537" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv10" d="M 1099.54 579.5 L 1086.31 579.5" stroke-width="1" zvalue="1452"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="27@0" LinkObjectIDznd="32" MaxPinNum="2"/>
   </metadata>
  <path d="M 1099.54 579.5 L 1086.31 579.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="kv10" d="M 1086.31 520.86 L 1086.31 619.09" stroke-width="1" zvalue="1455"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@1" LinkObjectIDznd="30@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1086.31 520.86 L 1086.31 619.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv10" d="M 1086 661.82 L 1086 696" stroke-width="1" zvalue="1456"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="30@1" LinkObjectIDznd="16@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1086 661.82 L 1086 696" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="v6300" d="M 1099.54 419 L 1086.37 419" stroke-width="1" zvalue="1459"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@0" LinkObjectIDznd="39" MaxPinNum="2"/>
   </metadata>
  <path d="M 1099.54 419 L 1086.37 419" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="v6300" d="M 1099.54 369.31 L 1086.37 369.31" stroke-width="1" zvalue="1462"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="39" MaxPinNum="2"/>
   </metadata>
  <path d="M 1099.54 369.31 L 1086.37 369.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="v6300" d="M 1086.37 431.69 L 1086.37 349.21" stroke-width="1" zvalue="1465"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@0" LinkObjectIDznd="38@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1086.37 431.69 L 1086.37 349.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="v6300" d="M 1086.77 306.48 L 1086.77 275.5" stroke-width="1" zvalue="1466"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="12@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1086.77 306.48 L 1086.77 275.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="v6300" d="M 1174.77 139.56 L 1174.77 200.21" stroke-width="1" zvalue="1470"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="44@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1174.77 139.56 L 1174.77 200.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="v6300" d="M 1175.84 246.04 L 1175.84 275.5" stroke-width="1" zvalue="1471"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@1" LinkObjectIDznd="12@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1175.84 246.04 L 1175.84 275.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="v6300" d="M 845.89 366.72 L 845.89 347.96" stroke-width="1" zvalue="1475"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2@0" LinkObjectIDznd="5@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 845.89 366.72 L 845.89 347.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="v6300" d="M 845.17 315.59 L 845.17 275.5" stroke-width="1" zvalue="1476"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@1" LinkObjectIDznd="12@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 845.17 315.59 L 845.17 275.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="v6300" d="M 869.02 299.96 L 845.17 299.96" stroke-width="1" zvalue="1479"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="8" MaxPinNum="2"/>
   </metadata>
  <path d="M 869.02 299.96 L 845.17 299.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv10" d="M 1635.61 605.42 L 1635.61 624.18" stroke-width="1" zvalue="1485"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="29@0" LinkObjectIDznd="24@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1635.61 605.42 L 1635.61 624.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv10" d="M 1634.89 656.55 L 1634.89 696" stroke-width="1" zvalue="1486"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@1" LinkObjectIDznd="16@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1634.89 656.55 L 1634.89 696" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv10" d="M 1658.74 672.18 L 1634.89 672.18" stroke-width="1" zvalue="1488"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@0" LinkObjectIDznd="22" MaxPinNum="2"/>
   </metadata>
  <path d="M 1658.74 672.18 L 1634.89 672.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv10" d="M 669.67 907.41 L 669.67 900.46" stroke-width="1" zvalue="1496"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@0" LinkObjectIDznd="52@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 669.67 907.41 L 669.67 900.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv10" d="M 670.32 876.63 L 670.32 862.89 L 749.21 862.89 L 749.21 876.63" stroke-width="1" zvalue="1500"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="66@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 670.32 876.63 L 670.32 862.89 L 749.21 862.89 L 749.21 876.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv10" d="M 749.18 900.46 L 749.18 907.41" stroke-width="1" zvalue="1501"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@1" LinkObjectIDznd="51@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 749.18 900.46 L 749.18 907.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv10" d="M 709.11 862.89 L 709.11 761.82" stroke-width="1" zvalue="1505"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68" LinkObjectIDznd="72@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 709.11 862.89 L 709.11 761.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 709.11 719.09 L 709.11 696" stroke-width="1" zvalue="1506"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@0" LinkObjectIDznd="16@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 709.11 719.09 L 709.11 696" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv10" d="M 709.11 801.67 L 709.11 793.67" stroke-width="1" zvalue="1509"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@0" LinkObjectIDznd="73" MaxPinNum="2"/>
   </metadata>
  <path d="M 709.11 801.67 L 709.11 793.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv10" d="M 696.44 847.5 L 709.11 847.5" stroke-width="1" zvalue="1512"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@0" LinkObjectIDznd="73" MaxPinNum="2"/>
   </metadata>
  <path d="M 696.44 847.5 L 709.11 847.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv10" d="M 697.55 783.5 L 709.11 783.5" stroke-width="1" zvalue="1515"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="73" MaxPinNum="2"/>
   </metadata>
  <path d="M 697.55 783.5 L 709.11 783.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv10" d="M 709.11 783.78 L 741.41 783.78 L 741.41 801.27" stroke-width="1" zvalue="1518"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73" LinkObjectIDznd="82@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 709.11 783.78 L 741.41 783.78 L 741.41 801.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv10" d="M 921.56 800.56 L 921.73 800.56" stroke-width="1" zvalue="1523"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@0" LinkObjectIDznd="87" MaxPinNum="2"/>
   </metadata>
  <path d="M 921.56 800.56 L 921.73 800.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv10" d="M 921.73 855.93 L 921.73 763.45" stroke-width="1" zvalue="1526"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="4@0" LinkObjectIDznd="84@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 921.73 855.93 L 921.73 763.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv10" d="M 921.37 717.62 L 921.37 696" stroke-width="1" zvalue="1527"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@0" LinkObjectIDznd="16@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 921.37 717.62 L 921.37 696" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv10" d="M 1146.62 922.98 L 1146.62 888.46" stroke-width="1" zvalue="1535"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="94@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1146.62 922.98 L 1146.62 888.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv10" d="M 1146.6 719.09 L 1146.6 696" stroke-width="1" zvalue="1540"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="16@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1146.6 719.09 L 1146.6 696" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="kv10" d="M 1386.62 922.98 L 1386.62 888.46" stroke-width="1" zvalue="1553"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="117@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1386.62 922.98 L 1386.62 888.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv10" d="M 1386.6 719.09 L 1386.6 696" stroke-width="1" zvalue="1557"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="16@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1386.6 719.09 L 1386.6 696" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="kv10" d="M 1614.62 922.98 L 1614.62 888.46" stroke-width="1" zvalue="1569"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@0" LinkObjectIDznd="132@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1614.62 922.98 L 1614.62 888.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv10" d="M 1614.6 719.09 L 1614.6 696" stroke-width="1" zvalue="1573"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@0" LinkObjectIDznd="16@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1614.6 719.09 L 1614.6 696" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv10" d="M 1363.21 795.83 L 1363.21 783 L 1386.6 783" stroke-width="1" zvalue="1634"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="110" MaxPinNum="2"/>
   </metadata>
  <path d="M 1363.21 795.83 L 1363.21 783 L 1386.6 783" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv10" d="M 1386.6 782 L 1414.96 782 L 1414.96 803.49" stroke-width="1" zvalue="1635"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110" LinkObjectIDznd="111@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1386.6 782 L 1414.96 782 L 1414.96 803.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv10" d="M 1146.6 761.82 L 1146.6 813.22 L 1146.65 813.22 L 1146.65 864.63" stroke-width="1" zvalue="1637"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@1" LinkObjectIDznd="94@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1146.6 761.82 L 1146.6 813.22 L 1146.65 813.22 L 1146.65 864.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv10" d="M 1386.6 761.82 L 1386.6 813.22 L 1386.65 813.22 L 1386.65 864.63" stroke-width="1" zvalue="1638"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@1" LinkObjectIDznd="117@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1386.6 761.82 L 1386.6 813.22 L 1386.65 813.22 L 1386.65 864.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv10" d="M 1614.6 761.82 L 1614.6 813.22 L 1614.65 813.22 L 1614.65 864.63" stroke-width="1" zvalue="1639"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@1" LinkObjectIDznd="132@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1614.6 761.82 L 1614.6 813.22 L 1614.65 813.22 L 1614.65 864.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="kv10" d="M 1146.22 816.56 L 1146.65 816.56" stroke-width="1" zvalue="1640"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="105" MaxPinNum="2"/>
   </metadata>
  <path d="M 1146.22 816.56 L 1146.65 816.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv10" d="M 1386.22 816.56 L 1386.65 816.56" stroke-width="1" zvalue="1641"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="110" MaxPinNum="2"/>
   </metadata>
  <path d="M 1386.22 816.56 L 1386.65 816.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="kv10" d="M 1591.21 795.83 L 1591.21 782 L 1614.6 782" stroke-width="1" zvalue="1642"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@0" LinkObjectIDznd="125" MaxPinNum="2"/>
   </metadata>
  <path d="M 1591.21 795.83 L 1591.21 782 L 1614.6 782" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv10" d="M 1642.96 803.49 L 1642.96 782 L 1612 782" stroke-width="1" zvalue="1643"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="176" MaxPinNum="2"/>
   </metadata>
  <path d="M 1642.96 803.49 L 1642.96 782 L 1612 782" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv10" d="M 1614.22 816.56 L 1614.65 816.56" stroke-width="1" zvalue="1644"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@0" LinkObjectIDznd="125" MaxPinNum="2"/>
   </metadata>
  <path d="M 1614.22 816.56 L 1614.65 816.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv10" d="M 1123.21 795.83 L 1123.21 784 L 1146.6 784" stroke-width="1" zvalue="1645"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="105" MaxPinNum="2"/>
   </metadata>
  <path d="M 1123.21 795.83 L 1123.21 784 L 1146.6 784" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv10" d="M 1174.96 803.49 L 1174.96 784 L 1146.6 784" stroke-width="1" zvalue="1646"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 1174.96 803.49 L 1174.96 784 L 1146.6 784" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="30">
   <use class="kv10" height="20" transform="rotate(0,1086,640.75) scale(2.34135,2.34135) translate(-615.458,-353.669)" width="10" x="1074.293269230769" xlink:href="#Breaker:小车断路器_0" y="617.3365384615385" zvalue="1454"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924466835461" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924466835461"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1086,640.75) scale(2.34135,2.34135) translate(-615.458,-353.669)" width="10" x="1074.293269230769" y="617.3365384615385"/></g>
  <g id="38">
   <use class="v6300" height="20" transform="rotate(0,1086.77,328.135) scale(2.34135,2.34135) translate(-615.898,-174.573)" width="10" x="1075.0625" xlink:href="#Breaker:小车断路器_0" y="304.7211538461538" zvalue="1464"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924466900997" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924466900997"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1086.77,328.135) scale(2.34135,2.34135) translate(-615.898,-174.573)" width="10" x="1075.0625" y="304.7211538461538"/></g>
  <g id="72">
   <use class="kv10" height="20" transform="rotate(0,709.111,740.75) scale(2.34135,2.34135) translate(-399.54,-410.959)" width="10" x="697.4043803418804" xlink:href="#Breaker:小车断路器_0" y="717.3365325927733" zvalue="1504"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924466966533" ObjectName="10kV电容器051断路器"/>
   <cge:TPSR_Ref TObjectID="6473924466966533"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,709.111,740.75) scale(2.34135,2.34135) translate(-399.54,-410.959)" width="10" x="697.4043803418804" y="717.3365325927733"/></g>
  <g id="98">
   <use class="kv10" height="20" transform="rotate(0,1146.6,740.75) scale(2.34135,2.34135) translate(-650.172,-410.959)" width="10" x="1134.888888888889" xlink:href="#Breaker:小车断路器_0" y="717.3365325993962" zvalue="1538"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924467032069" ObjectName="10kV铜壁关线053断路器"/>
   <cge:TPSR_Ref TObjectID="6473924467032069"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1146.6,740.75) scale(2.34135,2.34135) translate(-650.172,-410.959)" width="10" x="1134.888888888889" y="717.3365325993962"/></g>
  <g id="115">
   <use class="kv10" height="20" transform="rotate(0,1386.6,740.75) scale(2.34135,2.34135) translate(-787.667,-410.959)" width="10" x="1374.888888888889" xlink:href="#Breaker:小车断路器_0" y="717.3365325993962" zvalue="1554"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924467097605" ObjectName="10kV堂梨坝线054断路器"/>
   <cge:TPSR_Ref TObjectID="6473924467097605"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1386.6,740.75) scale(2.34135,2.34135) translate(-787.667,-410.959)" width="10" x="1374.888888888889" y="717.3365325993962"/></g>
  <g id="130">
   <use class="kv10" height="20" transform="rotate(0,1614.6,740.75) scale(2.34135,2.34135) translate(-918.287,-410.959)" width="10" x="1602.888888888889" xlink:href="#Breaker:小车断路器_0" y="717.3365325993962" zvalue="1570"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924467163141" ObjectName="10kV小寨线055断路器"/>
   <cge:TPSR_Ref TObjectID="6473924467163141"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1614.6,740.75) scale(2.34135,2.34135) translate(-918.287,-410.959)" width="10" x="1602.888888888889" y="717.3365325993962"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="44">
   <use class="v6300" height="33" transform="rotate(0,1175.84,223.337) scale(1.67239,1.419) translate(-468.043,-59.0327)" width="14" x="1164.130341880342" xlink:href="#Disconnector:手车隔离开关13_0" y="199.9230769230771" zvalue="1469"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449507753990" ObjectName="6.3kV三合村线6548隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449507753990"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,1175.84,223.337) scale(1.67239,1.419) translate(-468.043,-59.0327)" width="14" x="1164.130341880342" y="199.9230769230771"/></g>
  <g id="5">
   <use class="v6300" height="26" transform="rotate(0,845.167,331.75) scale(1.25,1.25) translate(-167.533,-63.1)" width="12" x="837.6666666666667" xlink:href="#Disconnector:20210316_0" y="315.5" zvalue="1474"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449507885062" ObjectName="6.3kV母线电压互感器6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449507885062"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,845.167,331.75) scale(1.25,1.25) translate(-167.533,-63.1)" width="12" x="837.6666666666667" y="315.5"/></g>
  <g id="24">
   <use class="kv10" height="26" transform="rotate(0,1634.89,640.389) scale(1.25,-1.25) translate(-325.478,-1149.45)" width="12" x="1627.388888888889" xlink:href="#Disconnector:20210316_0" y="624.1388925446404" zvalue="1483"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449508081670" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449508081670"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1634.89,640.389) scale(1.25,-1.25) translate(-325.478,-1149.45)" width="12" x="1627.388888888889" y="624.1388925446404"/></g>
  <g id="52">
   <use class="kv10" height="30" transform="rotate(0,670.222,888.444) scale(1.11111,0.814815) translate(-66.1889,199.141)" width="15" x="661.8888888888888" xlink:href="#Disconnector:刀闸_0" y="876.2222120496958" zvalue="1494"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449508343814" ObjectName="10kV1号电容器组0511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449508343814"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,670.222,888.444) scale(1.11111,0.814815) translate(-66.1889,199.141)" width="15" x="661.8888888888888" y="876.2222120496958"/></g>
  <g id="66">
   <use class="kv10" height="30" transform="rotate(0,749.111,888.444) scale(1.11111,0.814815) translate(-74.0778,199.141)" width="15" x="740.7777777777777" xlink:href="#Disconnector:刀闸_0" y="876.2222120496958" zvalue="1499"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449508409349" ObjectName="10kV2号电容器组0512隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449508409349"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,749.111,888.444) scale(1.11111,0.814815) translate(-74.0778,199.141)" width="15" x="740.7777777777777" y="876.2222120496958"/></g>
  <g id="84">
   <use class="kv10" height="33" transform="rotate(0,921.393,740.75) scale(1.67239,1.419) translate(-365.742,-211.813)" width="14" x="909.6858974358975" xlink:href="#Disconnector:手车隔离开关13_0" y="717.3365325948113" zvalue="1525"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449508933637" ObjectName="10kV1号站用变0521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449508933637"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,921.393,740.75) scale(1.67239,1.419) translate(-365.742,-211.813)" width="14" x="909.6858974358975" y="717.3365325948113"/></g>
  <g id="94">
   <use class="kv10" height="30" transform="rotate(0,1146.56,876.444) scale(1.11111,0.814815) translate(-113.822,196.414)" width="15" x="1138.222222222222" xlink:href="#Disconnector:刀闸_0" y="864.2222222222223" zvalue="1534"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449509130246" ObjectName="10kV铜壁关线0536隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449509130246"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1146.56,876.444) scale(1.11111,0.814815) translate(-113.822,196.414)" width="15" x="1138.222222222222" y="864.2222222222223"/></g>
  <g id="117">
   <use class="kv10" height="30" transform="rotate(0,1386.56,876.444) scale(1.11111,0.814815) translate(-137.822,196.414)" width="15" x="1378.222222222222" xlink:href="#Disconnector:刀闸_0" y="864.2222222222223" zvalue="1551"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449509588998" ObjectName="10kV堂梨坝线0546隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449509588998"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1386.56,876.444) scale(1.11111,0.814815) translate(-137.822,196.414)" width="15" x="1378.222222222222" y="864.2222222222223"/></g>
  <g id="132">
   <use class="kv10" height="30" transform="rotate(0,1614.56,876.444) scale(1.11111,0.814815) translate(-160.622,196.414)" width="15" x="1606.222222222222" xlink:href="#Disconnector:刀闸_0" y="864.2222222222223" zvalue="1567"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449509982213" ObjectName="10kV小寨线0556隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449509982213"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1614.56,876.444) scale(1.11111,0.814815) translate(-160.622,196.414)" width="15" x="1606.222222222222" y="864.2222222222223"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="48">
   <use class="kv10" height="40" transform="rotate(0,669.667,944) scale(2.05556,2.05556) translate(-333.327,-463.646)" width="20" x="649.1111111111112" xlink:href="#Compensator:三合电容_0" y="902.8888888888889" zvalue="1490"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449508212742" ObjectName="10kV1号电容器组"/>
   <cge:TPSR_Ref TObjectID="6192449508212742"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,669.667,944) scale(2.05556,2.05556) translate(-333.327,-463.646)" width="20" x="649.1111111111112" y="902.8888888888889"/></g>
  <g id="51">
   <use class="kv10" height="40" transform="rotate(0,749.667,944) scale(2.05556,2.05556) translate(-374.408,-463.646)" width="20" x="729.1111111111112" xlink:href="#Compensator:三合电容_0" y="902.8888888888889" zvalue="1492"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449508278278" ObjectName="10kV2号电容器组"/>
   <cge:TPSR_Ref TObjectID="6192449508278278"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,749.667,944) scale(2.05556,2.05556) translate(-374.408,-463.646)" width="20" x="729.1111111111112" y="902.8888888888889"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="82">
   <use class="kv10" height="20" transform="rotate(0,741.333,817.111) scale(1.5,1.625) translate(-244.611,-308.024)" width="10" x="733.8333333333333" xlink:href="#GroundDisconnector:地刀_0" y="800.8611111111111" zvalue="1516"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449508737029" ObjectName="10kV电容器05167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449508737029"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,741.333,817.111) scale(1.5,1.625) translate(-244.611,-308.024)" width="10" x="733.8333333333333" y="800.8611111111111"/></g>
  <g id="103">
   <use class="kv10" height="20" transform="rotate(0,1174.89,819.333) scale(1.5,1.625) translate(-389.13,-308.878)" width="10" x="1167.388888888889" xlink:href="#GroundDisconnector:地刀_0" y="803.0833333333335" zvalue="1544"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449509326854" ObjectName="10kV铜壁关线05317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449509326854"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1174.89,819.333) scale(1.5,1.625) translate(-389.13,-308.878)" width="10" x="1167.388888888889" y="803.0833333333335"/></g>
  <g id="111">
   <use class="kv10" height="20" transform="rotate(0,1414.89,819.333) scale(1.5,1.625) translate(-469.13,-308.878)" width="10" x="1407.388888888889" xlink:href="#GroundDisconnector:地刀_0" y="803.0833333333335" zvalue="1559"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449509457926" ObjectName="10kV堂梨坝线05417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449509457926"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1414.89,819.333) scale(1.5,1.625) translate(-469.13,-308.878)" width="10" x="1407.388888888889" y="803.0833333333335"/></g>
  <g id="126">
   <use class="kv10" height="20" transform="rotate(0,1642.89,819.333) scale(1.5,1.625) translate(-545.13,-308.878)" width="10" x="1635.388888888889" xlink:href="#GroundDisconnector:地刀_0" y="803.0833333333335" zvalue="1575"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449509851141" ObjectName="10kV小寨线05517接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449509851141"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1642.89,819.333) scale(1.5,1.625) translate(-545.13,-308.878)" width="10" x="1635.388888888889" y="803.0833333333335"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="4">
   <use class="kv10" height="30" transform="rotate(0,921.556,883.561) scale(1.90476,1.91813) translate(-425.072,-409.152)" width="28" x="894.8888888888889" xlink:href="#EnergyConsumer:站用变DY接地_0" y="854.7894736842104" zvalue="1519"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449508802565" ObjectName="10kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,921.556,883.561) scale(1.90476,1.91813) translate(-425.072,-409.152)" width="28" x="894.8888888888889" y="854.7894736842104"/></g>
  <g id="89">
   <use class="kv10" height="30" transform="rotate(0,1150.57,943.889) scale(1.57778,-1.57778) translate(-417.001,-1533.46)" width="15" x="1138.734627137895" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="920.2222222222222" zvalue="1528"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449508999174" ObjectName="10kV铜壁关线"/>
   <cge:TPSR_Ref TObjectID="6192449508999174"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1150.57,943.889) scale(1.57778,-1.57778) translate(-417.001,-1533.46)" width="15" x="1138.734627137895" y="920.2222222222222"/></g>
  <g id="120">
   <use class="kv10" height="30" transform="rotate(0,1390.57,943.889) scale(1.57778,-1.57778) translate(-504.889,-1533.46)" width="15" x="1378.734627137895" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="920.2222222222222" zvalue="1547"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449509720069" ObjectName="10kV堂梨坝线"/>
   <cge:TPSR_Ref TObjectID="6192449509720069"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1390.57,943.889) scale(1.57778,-1.57778) translate(-504.889,-1533.46)" width="15" x="1378.734627137895" y="920.2222222222222"/></g>
  <g id="135">
   <use class="kv10" height="30" transform="rotate(0,1618.57,943.889) scale(1.57778,-1.57778) translate(-588.382,-1533.46)" width="15" x="1606.734627137895" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="920.2222222222222" zvalue="1563"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449510113285" ObjectName="10kV小寨线"/>
   <cge:TPSR_Ref TObjectID="6192449510113285"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1618.57,943.889) scale(1.57778,-1.57778) translate(-588.382,-1533.46)" width="15" x="1606.734627137895" y="920.2222222222222"/></g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,329.188,321.25) scale(0.708333,0.665547) translate(131.173,156.419)" width="30" x="318.56" xlink:href="#State:红绿圆(方形)_0" y="311.27" zvalue="1585"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374886240260" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.188,321.25) scale(0.708333,0.665547) translate(131.173,156.419)" width="30" x="318.56" y="311.27"/></g>
  <g id="1035">
   <use height="30" transform="rotate(0,233.562,321.25) scale(0.708333,0.665547) translate(91.7978,156.419)" width="30" x="222.94" xlink:href="#State:红绿圆(方形)_0" y="311.27" zvalue="1586"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562951871594500" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,233.562,321.25) scale(0.708333,0.665547) translate(91.7978,156.419)" width="30" x="222.94" y="311.27"/></g>
 </g>
</svg>