<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549588852738" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="EnergyConsumer:硅厂炉变YY_0" viewBox="0,0,17,30">
   <use terminal-index="0" type="0" x="8.666666666666666" xlink:href="#terminal" y="0.1666666666666714"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="8.5" y1="25.25" y2="30.08333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.597025948103791" x2="8.597025948103791" y1="1.79293756914176" y2="0.2382945839350779"/>
   <ellipse cx="8.42" cy="8.35" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.5" cy="18.92" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.611718118722354" x2="8.611718118722354" y1="16.35874008086165" y2="18.95772818087859"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.2052656081938" x2="8.611718118722351" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.018170629250881" x2="8.611718118722335" y1="21.55671628089551" y2="18.95772818087858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.611718118722354" x2="8.611718118722354" y1="5.503560677018264" y2="8.102548777035198"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.2052656081938" x2="8.611718118722351" y1="10.70153687705211" y2="8.102548777035178"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.018170629250881" x2="8.611718118722335" y1="10.70153687705211" y2="8.102548777035178"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="4.583333333333332" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="EnergyConsumer:发电负荷_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.75"/>
   <ellipse cx="14.98" cy="14.89" fill-opacity="0" rx="14.14" ry="14.14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.043 14.936 A 5.91667 4.875 0 0 0 26.8746 14.765" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 14.9746 15.015 A 5.91667 4.875 -180 0 0 3.14303 15.186" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-y不带中性点_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="1" x="10" xlink:href="#terminal" y="2.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.02838478538902" x2="10.02838478538902" y1="6.047799292079558" y2="8.646787392096492"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.434837295917547" x2="10.028384785389" y1="11.24577549211341" y2="8.646787392096474"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.62193227486047" x2="10.02838478538902" y1="11.24577549211341" y2="8.646787392096474"/>
   <ellipse cx="10.03" cy="9.050000000000001" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-y不带中性点_1" viewBox="0,0,20,30">
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.434837295917547" x2="10.028384785389" y1="22.74577549211341" y2="20.14678739209647"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.02838478538902" x2="10.02838478538902" y1="17.54779929207956" y2="20.14678739209649"/>
   <use terminal-index="1" type="1" x="10.08333333333333" xlink:href="#terminal" y="26.16666666666667"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.62193227486047" x2="10.02838478538902" y1="22.74577549211341" y2="20.14678739209647"/>
   <ellipse cx="10.03" cy="19.8" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV沙坝糖厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="71.94" y="327" zvalue="31"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="1" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" width="72.88" x="71.94" y="327" zvalue="31"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" writing-mode="lr" x="108.38" xml:space="preserve" y="343.5" zvalue="31">信号一览</text>
  <image height="60" id="2" preserveAspectRatio="xMidYMid slice" width="269.25" x="66.95999999999999" xlink:href="logo.png" y="45.64"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.589,75.6429) scale(1,1) translate(0,0)" writing-mode="lr" x="201.59" xml:space="preserve" y="79.14" zvalue="138"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,203.298,75.3332) scale(1,1) translate(9.43293e-15,0)" writing-mode="lr" x="203.3" xml:space="preserve" y="84.33" zvalue="139">10kV沙坝糖厂</text>
  <line fill="none" id="26" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="388.75" x2="388.75" y1="11" y2="1039.25" zvalue="6"/>
  <line fill="none" id="24" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.22692454998003" x2="331.8381846035992" y1="168.8779458827686" y2="168.8779458827686" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="927.8127909390723" y2="927.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="979.1522909390724" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="361.3219285714284" y1="927.8127909390723" y2="927.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="361.3219285714284" y1="979.1522909390724" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3219285714284" x2="361.3219285714284" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5593285714284" x2="279.5593285714284" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="279.5592285714284" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3213285714285" x2="361.3213285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5593285714284" x2="279.5593285714284" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="279.5592285714284" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3213285714285" x2="361.3213285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,92.7828,957.386) scale(1,1) translate(1.07783e-13,1.05089e-13)" writing-mode="lr" x="60.68" xml:space="preserve" y="963.39" zvalue="10">参考图号 </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,132.899,994.755) scale(1,1) translate(-6.25312e-14,-1.52933e-12)" writing-mode="lr" x="70.40000000000001" xml:space="preserve" y="1000.75" zvalue="11">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,276.202,995.755) scale(1,1) translate(1.38222e-13,-1.53089e-12)" writing-mode="lr" x="207.5" xml:space="preserve" y="1001.75" zvalue="12">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.9923,1024.31) scale(1,1) translate(-6.10858e-14,-1.57527e-12)" writing-mode="lr" x="83.98999999999999" xml:space="preserve" y="1030.31" zvalue="13">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,280.836,1022.31) scale(1,1) translate(-4.58902e-14,1.12298e-13)" writing-mode="lr" x="206.67" xml:space="preserve" y="1028.31" zvalue="14">更新日期  </text>
  <line fill="none" id="17" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="54.60611355802337" x2="359.2173736116425" y1="623.6445306693694" y2="623.6445306693694" zvalue="15"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,97.6707,640.686) scale(1,1) translate(5.99964e-15,-1.38109e-13)" writing-mode="lr" x="97.67070806204492" xml:space="preserve" y="645.1863811688671" zvalue="17">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="172.1071428571429" y2="172.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="172.1071428571429" y2="172.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="290.6071428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="290.6071428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,502.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="507.1071428571429" zvalue="23">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,528.107) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="532.6071428571429" zvalue="24">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,553.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="558.1071428571429" zvalue="25">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,579.107) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="583.6071428571429" zvalue="26">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,604.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="609.1071428571429" zvalue="27">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.3214,186.107) scale(1,1) translate(0,0)" writing-mode="lr" x="54.32" xml:space="preserve" y="191.61" zvalue="28">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.571,186.107) scale(1,1) translate(0,0)" writing-mode="lr" x="240.57" xml:space="preserve" y="191.61" zvalue="29">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" x="200.7890625" xml:space="preserve" y="468.359375" zvalue="114">10kV母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="200.7890625" xml:space="preserve" y="484.359375" zvalue="114">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,250.009,210.357) scale(1,1) translate(0,0)" writing-mode="lr" x="250.01" xml:space="preserve" y="214.86" zvalue="122">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,208.399,340.5) scale(1,1) translate(0,0)" writing-mode="lr" x="208.4" xml:space="preserve" y="345" zvalue="379">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,296.399,340.5) scale(1,1) translate(0,0)" writing-mode="lr" x="296.4" xml:space="preserve" y="345" zvalue="380">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="69" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,246.444,960.048) scale(1,1) translate(0,1.05385e-13)" writing-mode="lr" x="135.89" xml:space="preserve" y="966.05" zvalue="522">沙坝糖厂</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,671.889,591) scale(1,1) translate(0,0)" writing-mode="lr" x="671.89" xml:space="preserve" y="595.5" zvalue="592">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,871.667,149.111) scale(1,1) translate(0,0)" writing-mode="lr" x="871.67" xml:space="preserve" y="153.61" zvalue="593">10kV沙糖Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,723.778,909.722) scale(1,1) translate(-3.04991e-13,0)" writing-mode="lr" x="723.78" xml:space="preserve" y="914.22" zvalue="595">10kV备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,750.222,694.333) scale(1,1) translate(0,0)" writing-mode="lr" x="750.22" xml:space="preserve" y="698.83" zvalue="597">055</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,654.778,794.333) scale(1,1) translate(0,0)" writing-mode="lr" x="654.78" xml:space="preserve" y="798.83" zvalue="600">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,854.222,909.722) scale(1,1) translate(0,0)" writing-mode="lr" x="854.22" xml:space="preserve" y="914.22" zvalue="603">#1泵变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,874.667,694.333) scale(1,1) translate(0,0)" writing-mode="lr" x="874.67" xml:space="preserve" y="698.83" zvalue="605">056</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,786.111,793.222) scale(1,1) translate(0,0)" writing-mode="lr" x="786.11" xml:space="preserve" y="797.72" zvalue="608">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,976.778,909.722) scale(1,1) translate(0,0)" writing-mode="lr" x="976.78" xml:space="preserve" y="914.22" zvalue="612">压榨间</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1000.22,694.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1000.22" xml:space="preserve" y="698.83" zvalue="614">057</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,911.667,793.222) scale(1,1) translate(0,0)" writing-mode="lr" x="911.67" xml:space="preserve" y="797.72" zvalue="617">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1098.78,909.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1098.78" xml:space="preserve" y="914.22" zvalue="621">电力间</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1124.22,694.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1124.22" xml:space="preserve" y="698.83" zvalue="623">058</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1035.67,793.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1035.67" xml:space="preserve" y="797.72" zvalue="626">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1208.22,849.444) scale(1,1) translate(-5.20127e-13,0)" writing-mode="lr" x="1208.22" xml:space="preserve" y="853.9400000000001" zvalue="629">10kV站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1241.22,684.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1241.22" xml:space="preserve" y="689.39" zvalue="632">0569</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1341.56,838) scale(1,1) translate(0,0)" writing-mode="lr" x="1341.56" xml:space="preserve" y="842.5" zvalue="635">10kV#1F</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1369.11,692.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1369.11" xml:space="preserve" y="697.33" zvalue="638">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,899.778,533.5) scale(1,1) translate(2.96022e-13,0)" writing-mode="lr" x="899.78" xml:space="preserve" y="538" zvalue="642">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,967.889,534.111) scale(1,1) translate(0,0)" writing-mode="lr" x="967.89" xml:space="preserve" y="538.61" zvalue="646">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,991.778,498.222) scale(1,1) translate(0,0)" writing-mode="lr" x="991.78" xml:space="preserve" y="502.72" zvalue="651">0519</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,770.444,579.667) scale(1,1) translate(0,0)" writing-mode="lr" x="770.4400000000001" xml:space="preserve" y="584.17" zvalue="655">10kV#1台变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,794.173,481.667) scale(1,1) translate(0,0)" writing-mode="lr" x="794.17" xml:space="preserve" y="486.17" zvalue="657">054</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,743.378,433.889) scale(1,1) translate(0,0)" writing-mode="lr" x="743.38" xml:space="preserve" y="438.39" zvalue="660">0546</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,819.556,418.333) scale(1,1) translate(0,0)" writing-mode="lr" x="819.5599999999999" xml:space="preserve" y="422.83" zvalue="663">0548</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,846.333,311.667) scale(1,1) translate(0,0)" writing-mode="lr" x="846.33" xml:space="preserve" y="316.17" zvalue="666">052</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,845.296,368.333) scale(1,1) translate(0,0)" writing-mode="lr" x="845.3" xml:space="preserve" y="372.83" zvalue="668">0526</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,971.981,436.333) scale(1,1) translate(0,0)" writing-mode="lr" x="971.98" xml:space="preserve" y="440.83" zvalue="674">10kV#2台变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1044.28,262.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1044.28" xml:space="preserve" y="266.83" zvalue="675">053</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1063.65,436.333) scale(1,1) translate(2.2774e-13,0)" writing-mode="lr" x="1063.65" xml:space="preserve" y="440.83" zvalue="677">10kV#3台变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,944.556,339.444) scale(1,1) translate(0,0)" writing-mode="lr" x="944.5599999999999" xml:space="preserve" y="343.94" zvalue="682">0532</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1039,339.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1039" xml:space="preserve" y="343.94" zvalue="686">0533</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1309.38,568.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1309.38" xml:space="preserve" y="572.61" zvalue="690">10kV生活区变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1307.94,258) scale(1,1) translate(0,0)" writing-mode="lr" x="1307.94" xml:space="preserve" y="262.5" zvalue="692">10kV沙糖Ⅱ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1334.22,413.944) scale(1,1) translate(0,0)" writing-mode="lr" x="1334.22" xml:space="preserve" y="418.44" zvalue="695">041</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1280.85,349.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1280.85" xml:space="preserve" y="353.94" zvalue="699">0416</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1345,857.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1345" xml:space="preserve" y="862" zvalue="703">3000kW</text>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="33" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,161.571,186) scale(1,1) translate(0,0)" writing-mode="lr" x="161.72" xml:space="preserve" y="192.43" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125732220932" ObjectName=""/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="35" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,343.571,185.857) scale(1,1) translate(0,0)" writing-mode="lr" x="343.72" xml:space="preserve" y="192.29" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125732286468" ObjectName=""/>
   </metadata>
  </g>
  <g id="42">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,202.071,528.593) scale(1,1) translate(0,0)" writing-mode="lr" x="202.2" xml:space="preserve" y="533.5" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="49">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="49" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,202.071,553.718) scale(1,1) translate(0,-1.20619e-13)" writing-mode="lr" x="202.2" xml:space="preserve" y="558.63" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,203.071,577.843) scale(1,1) translate(0,0)" writing-mode="lr" x="203.2" xml:space="preserve" y="582.75" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,203.5,503.468) scale(1,1) translate(0,1.09461e-13)" writing-mode="lr" x="203.63" xml:space="preserve" y="508.38" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,202.321,602.968) scale(1,1) translate(0,0)" writing-mode="lr" x="202.45" xml:space="preserve" y="607.88" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="54" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,343.571,209.968) scale(1,1) translate(0,0)" writing-mode="lr" x="343.72" xml:space="preserve" y="216.4" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="132">
   <use height="30" transform="rotate(0,329.768,340.5) scale(0.708333,0.665547) translate(131.412,166.093)" width="30" x="319.14" xlink:href="#State:红绿圆(方形)_0" y="330.52" zvalue="381"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.768,340.5) scale(0.708333,0.665547) translate(131.412,166.093)" width="30" x="319.14" y="330.52"/></g>
  <g id="94">
   <use height="30" transform="rotate(0,247.143,340.5) scale(0.708333,0.665547) translate(97.3897,166.093)" width="30" x="236.52" xlink:href="#State:红绿圆(方形)_0" y="330.52" zvalue="382"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,247.143,340.5) scale(0.708333,0.665547) translate(97.3897,166.093)" width="30" x="236.52" y="330.52"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="2">
   <path class="kv10" d="M 644.89 612 L 1417.11 612" stroke-width="6" zvalue="591"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674247376900" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674247376900"/></metadata>
  <path d="M 644.89 612 L 1417.11 612" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="28">
   <use class="kv10" height="30" transform="rotate(0,723.778,848.667) scale(1.38889,-1.37037) translate(-200.324,-1462.41)" width="12" x="715.4444444444445" xlink:href="#EnergyConsumer:负荷_0" y="828.1111111111111" zvalue="594"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449861910534" ObjectName="10kV备用线055"/>
   <cge:TPSR_Ref TObjectID="6192449861910534"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,723.778,848.667) scale(1.38889,-1.37037) translate(-200.324,-1462.41)" width="12" x="715.4444444444445" y="828.1111111111111"/></g>
  <g id="50">
   <use class="kv10" height="30" transform="rotate(0,848.222,848.667) scale(1.38889,-1.37037) translate(-235.169,-1462.41)" width="12" x="839.8888888888889" xlink:href="#EnergyConsumer:负荷_0" y="828.1111111111111" zvalue="602"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449862238213" ObjectName="10kV#1泵变056"/>
   <cge:TPSR_Ref TObjectID="6192449862238213"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,848.222,848.667) scale(1.38889,-1.37037) translate(-235.169,-1462.41)" width="12" x="839.8888888888889" y="828.1111111111111"/></g>
  <g id="62">
   <use class="kv10" height="30" transform="rotate(0,973.778,848.667) scale(1.38889,-1.37037) translate(-270.324,-1462.41)" width="12" x="965.4444444444445" xlink:href="#EnergyConsumer:负荷_0" y="828.1111111111111" zvalue="611"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449862434821" ObjectName="10kV压榨间057"/>
   <cge:TPSR_Ref TObjectID="6192449862434821"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,973.778,848.667) scale(1.38889,-1.37037) translate(-270.324,-1462.41)" width="12" x="965.4444444444445" y="828.1111111111111"/></g>
  <g id="80">
   <use class="kv10" height="30" transform="rotate(0,1097.78,848.667) scale(1.38889,-1.37037) translate(-305.044,-1462.41)" width="12" x="1089.444444444444" xlink:href="#EnergyConsumer:负荷_0" y="828.1111111111111" zvalue="620"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449862631429" ObjectName="10kV电力间058"/>
   <cge:TPSR_Ref TObjectID="6192449862631429"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1097.78,848.667) scale(1.38889,-1.37037) translate(-305.044,-1462.41)" width="12" x="1089.444444444444" y="828.1111111111111"/></g>
  <g id="83">
   <use class="kv10" height="30" transform="rotate(0,1209.06,802.608) scale(1.86275,1.86275) translate(-552.65,-358.793)" width="17" x="1193.222222222222" xlink:href="#EnergyConsumer:硅厂炉变YY_0" y="774.6666666666667" zvalue="628"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449862696965" ObjectName="10kV站用变"/>
   <cge:TPSR_Ref TObjectID="6192449862696965"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1209.06,802.608) scale(1.86275,1.86275) translate(-552.65,-358.793)" width="17" x="1193.222222222222" y="774.6666666666667"/></g>
  <g id="92">
   <use class="kv10" height="30" transform="rotate(0,1342.67,799.333) scale(1.11111,1.11111) translate(-132.6,-78.2667)" width="30" x="1326" xlink:href="#EnergyConsumer:发电负荷_0" y="782.6666666666667" zvalue="634"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449862828037" ObjectName="10kV#1F"/>
   <cge:TPSR_Ref TObjectID="6192449862828037"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1342.67,799.333) scale(1.11111,1.11111) translate(-132.6,-78.2667)" width="30" x="1326" y="782.6666666666667"/></g>
  <g id="115">
   <use class="kv10" height="30" transform="rotate(0,770.87,535.444) scale(1.81481,1.81481) translate(-339.179,-228.181)" width="17" x="755.4444444444445" xlink:href="#EnergyConsumer:硅厂炉变YY_0" y="508.2222222222222" zvalue="654"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449863155717" ObjectName="10kV#1台变"/>
   <cge:TPSR_Ref TObjectID="6192449863155717"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,770.87,535.444) scale(1.81481,1.81481) translate(-339.179,-228.181)" width="17" x="755.4444444444445" y="508.2222222222222"/></g>
  <g id="139">
   <use class="kv10" height="30" transform="rotate(0,970.87,393.222) scale(1.81481,1.81481) translate(-428.975,-164.327)" width="17" x="955.4444444444445" xlink:href="#EnergyConsumer:硅厂炉变YY_0" y="366.0000000529819" zvalue="673"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449863417861" ObjectName="10kV#2台变"/>
   <cge:TPSR_Ref TObjectID="6192449863417861"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,970.87,393.222) scale(1.81481,1.81481) translate(-428.975,-164.327)" width="17" x="955.4444444444445" y="366.0000000529819"/></g>
  <g id="143">
   <use class="kv10" height="30" transform="rotate(0,1064.76,393.222) scale(1.81481,1.81481) translate(-471.129,-164.327)" width="17" x="1049.333333333333" xlink:href="#EnergyConsumer:硅厂炉变YY_0" y="366" zvalue="676"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449863483397" ObjectName="10kV#3台变"/>
   <cge:TPSR_Ref TObjectID="6192449863483397"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1064.76,393.222) scale(1.81481,1.81481) translate(-471.129,-164.327)" width="17" x="1049.333333333333" y="366"/></g>
  <g id="194">
   <use class="v400" height="30" transform="rotate(0,1307.7,542.056) scale(1.38889,-0.685185) translate(-363.823,-1337.89)" width="12" x="1299.366292235218" xlink:href="#EnergyConsumer:负荷_0" y="531.7777777777778" zvalue="701"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449863811077" ObjectName="10kV生活区"/>
   <cge:TPSR_Ref TObjectID="6192449863811077"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1307.7,542.056) scale(1.38889,-0.685185) translate(-363.823,-1337.89)" width="12" x="1299.366292235218" y="531.7777777777778"/></g>
 </g>
 <g id="BreakerClass">
  <g id="30">
   <use class="kv10" height="20" transform="rotate(0,723.5,693.833) scale(2.61111,2.35) translate(-438.359,-385.085)" width="10" x="710.4444444444445" xlink:href="#Breaker:小车断路器_0" y="670.3333282470702" zvalue="596"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924526669829" ObjectName="10kV备用线055断路器"/>
   <cge:TPSR_Ref TObjectID="6473924526669829"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,723.5,693.833) scale(2.61111,2.35) translate(-438.359,-385.085)" width="10" x="710.4444444444445" y="670.3333282470702"/></g>
  <g id="48">
   <use class="kv10" height="20" transform="rotate(0,847.944,693.833) scale(2.61111,2.35) translate(-515.144,-385.085)" width="10" x="834.8888888888888" xlink:href="#Breaker:小车断路器_0" y="670.3333282470702" zvalue="603"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924526735365" ObjectName="10kV#1泵变056断路器"/>
   <cge:TPSR_Ref TObjectID="6473924526735365"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,847.944,693.833) scale(2.61111,2.35) translate(-515.144,-385.085)" width="10" x="834.8888888888888" y="670.3333282470702"/></g>
  <g id="61">
   <use class="kv10" height="20" transform="rotate(0,973.5,693.833) scale(2.61111,2.35) translate(-592.615,-385.085)" width="10" x="960.4444444444445" xlink:href="#Breaker:小车断路器_0" y="670.3333282470702" zvalue="612"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924526800901" ObjectName="10kV压榨间057断路器"/>
   <cge:TPSR_Ref TObjectID="6473924526800901"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,973.5,693.833) scale(2.61111,2.35) translate(-592.615,-385.085)" width="10" x="960.4444444444445" y="670.3333282470702"/></g>
  <g id="77">
   <use class="kv10" height="20" transform="rotate(0,1097.5,693.833) scale(2.61111,2.35) translate(-669.125,-385.085)" width="10" x="1084.444444444444" xlink:href="#Breaker:小车断路器_0" y="670.3333282470702" zvalue="621"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924526866437" ObjectName="10kV电力间058断路器"/>
   <cge:TPSR_Ref TObjectID="6473924526866437"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1097.5,693.833) scale(2.61111,2.35) translate(-669.125,-385.085)" width="10" x="1084.444444444444" y="670.3333282470702"/></g>
  <g id="96">
   <use class="kv10" height="20" transform="rotate(0,1342.39,693.833) scale(2.61111,2.35) translate(-820.227,-385.085)" width="10" x="1329.333333333333" xlink:href="#Breaker:小车断路器_0" y="670.3333280881245" zvalue="637"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924526931973" ObjectName="10kV#1F001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924526931973"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1342.39,693.833) scale(2.61111,2.35) translate(-820.227,-385.085)" width="10" x="1329.333333333333" y="670.3333280881245"/></g>
  <g id="100">
   <use class="kv10" height="20" transform="rotate(0,873.056,534.5) scale(2.61111,2.35) translate(-530.638,-293.553)" width="10" x="860" xlink:href="#Breaker:小车断路器_0" y="511.0000000000001" zvalue="641"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924526997509" ObjectName="051"/>
   <cge:TPSR_Ref TObjectID="6473924526997509"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,873.056,534.5) scale(2.61111,2.35) translate(-530.638,-293.553)" width="10" x="860" y="511.0000000000001"/></g>
  <g id="118">
   <use class="kv10" height="20" transform="rotate(0,771.062,482.667) scale(1.66667,1.5) translate(-305.091,-155.889)" width="10" x="762.7283950617284" xlink:href="#Breaker:开关_0" y="467.6666666666666" zvalue="656"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924527063045" ObjectName="10kV#1台变054断路器"/>
   <cge:TPSR_Ref TObjectID="6473924527063045"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,771.062,482.667) scale(1.66667,1.5) translate(-305.091,-155.889)" width="10" x="762.7283950617284" y="467.6666666666666"/></g>
  <g id="130">
   <use class="kv10" height="20" transform="rotate(0,871.556,312.667) scale(1.66667,1.5) translate(-345.289,-99.2222)" width="10" x="863.2222222222222" xlink:href="#Breaker:开关_0" y="297.6666666666667" zvalue="665"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924527128581" ObjectName="052"/>
   <cge:TPSR_Ref TObjectID="6473924527128581"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,871.556,312.667) scale(1.66667,1.5) translate(-345.289,-99.2222)" width="10" x="863.2222222222222" y="297.6666666666667"/></g>
  <g id="140">
   <use class="kv10" height="20" transform="rotate(0,1016.17,262.222) scale(1.66667,1.5) translate(-403.136,-82.4074)" width="10" x="1007.839506172839" xlink:href="#Breaker:开关_0" y="247.2222222222221" zvalue="674"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924527194117" ObjectName="053"/>
   <cge:TPSR_Ref TObjectID="6473924527194117"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1016.17,262.222) scale(1.66667,1.5) translate(-403.136,-82.4074)" width="10" x="1007.839506172839" y="247.2222222222221"/></g>
  <g id="170">
   <use class="kv10" height="20" transform="rotate(0,1307.5,414.944) scale(2.61111,2.35) translate(-798.7,-224.872)" width="10" x="1294.444444444444" xlink:href="#Breaker:小车断路器_0" y="391.4444393581813" zvalue="694"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924527259653" ObjectName="041"/>
   <cge:TPSR_Ref TObjectID="6473924527259653"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1307.5,414.944) scale(2.61111,2.35) translate(-798.7,-224.872)" width="10" x="1294.444444444444" y="391.4444393581813"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="32">
   <path class="kv10" d="M 723.78 830.17 L 723.78 714.98" stroke-width="1" zvalue="597"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="30@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 723.78 830.17 L 723.78 714.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv10" d="M 723.5 672.1 L 723.5 612" stroke-width="1" zvalue="598"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="30@0" LinkObjectIDznd="2@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 723.5 672.1 L 723.5 612" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv10" d="M 681.42 776.89 L 681.42 747.56 L 723.78 747.56" stroke-width="1" zvalue="600"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="32" MaxPinNum="2"/>
   </metadata>
  <path d="M 681.42 776.89 L 681.42 747.56 L 723.78 747.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv10" d="M 848.22 830.17 L 848.22 714.98" stroke-width="1" zvalue="604"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 848.22 830.17 L 848.22 714.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv10" d="M 847.94 672.1 L 847.94 612" stroke-width="1" zvalue="606"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@0" LinkObjectIDznd="2@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 847.94 672.1 L 847.94 612" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv10" d="M 805.87 776.89 L 805.87 747.56 L 848.22 747.56" stroke-width="1" zvalue="609"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="47" MaxPinNum="2"/>
   </metadata>
  <path d="M 805.87 776.89 L 805.87 747.56 L 848.22 747.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv10" d="M 973.78 830.17 L 973.78 714.98" stroke-width="1" zvalue="613"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="61@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 973.78 830.17 L 973.78 714.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv10" d="M 973.5 672.1 L 973.5 612" stroke-width="1" zvalue="615"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="2@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 973.5 672.1 L 973.5 612" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv10" d="M 931.42 776.89 L 931.42 747.56 L 973.78 747.56" stroke-width="1" zvalue="618"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="59" MaxPinNum="2"/>
   </metadata>
  <path d="M 931.42 776.89 L 931.42 747.56 L 973.78 747.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv10" d="M 1097.78 830.17 L 1097.78 714.98" stroke-width="1" zvalue="622"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="77@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.78 830.17 L 1097.78 714.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv10" d="M 1097.5 672.1 L 1097.5 612" stroke-width="1" zvalue="624"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@0" LinkObjectIDznd="2@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.5 672.1 L 1097.5 612" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv10" d="M 1055.42 776.89 L 1055.42 747.56 L 1097.78 747.56" stroke-width="1" zvalue="627"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="68" MaxPinNum="2"/>
   </metadata>
  <path d="M 1055.42 776.89 L 1055.42 747.56 L 1097.78 747.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv10" d="M 1209.37 774.98 L 1209.43 699.19" stroke-width="1" zvalue="632"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@0" LinkObjectIDznd="88@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1209.37 774.98 L 1209.43 699.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv10" d="M 1209.34 670.42 L 1209.34 612" stroke-width="1" zvalue="633"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@1" LinkObjectIDznd="2@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1209.34 670.42 L 1209.34 612" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv10" d="M 1342.67 783.5 L 1342.67 714.98" stroke-width="1" zvalue="638"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="96@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1342.67 783.5 L 1342.67 714.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv10" d="M 1342.39 672.1 L 1342.39 612" stroke-width="1" zvalue="639"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="2@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1342.39 672.1 L 1342.39 612" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv10" d="M 873.06 555.65 L 873.06 612" stroke-width="1" zvalue="643"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@1" LinkObjectIDznd="2@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 873.06 555.65 L 873.06 612" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv10" d="M 1017.43 533.37 L 1017.43 513.63" stroke-width="1" zvalue="651"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="109@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1017.43 533.37 L 1017.43 513.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv10" d="M 1017.11 484.86 L 1017.11 459.33 L 872.2 459.33" stroke-width="1" zvalue="652"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@1" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 1017.11 484.86 L 1017.11 459.33 L 872.2 459.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv10" d="M 944.31 520 L 944.31 459.33" stroke-width="1" zvalue="653"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="112" MaxPinNum="2"/>
   </metadata>
  <path d="M 944.31 520 L 944.31 459.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv10" d="M 771.17 496.99 L 771.17 508.52" stroke-width="1" zvalue="658"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@1" LinkObjectIDznd="115@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 771.17 496.99 L 771.17 508.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv10" d="M 771.01 452.36 L 771.01 468.32" stroke-width="1" zvalue="661"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@1" LinkObjectIDznd="118@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 771.01 452.36 L 771.01 468.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv10" d="M 872.2 398.31 L 831.83 398.31" stroke-width="1" zvalue="663"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135" LinkObjectIDznd="126@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 872.2 398.31 L 831.83 398.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv10" d="M 803.5 398.13 L 771.04 398.13 L 771.04 417.7" stroke-width="1" zvalue="664"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@1" LinkObjectIDznd="122@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 803.5 398.13 L 771.04 398.13 L 771.04 417.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv10" d="M 872.2 382.94 L 872.2 512.76" stroke-width="1" zvalue="669"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@1" LinkObjectIDznd="100@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 872.2 382.94 L 872.2 512.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="137">
   <path class="kv10" d="M 871.39 193.15 L 871.39 298.32" stroke-width="1" zvalue="670"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="11@0" LinkObjectIDznd="130@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 871.39 193.15 L 871.39 298.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="kv10" d="M 871.67 326.99 L 871.67 354.61" stroke-width="1" zvalue="671"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@1" LinkObjectIDznd="131@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 871.67 326.99 L 871.67 354.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv10" d="M 1016.12 247.87 L 1016.12 217.11 L 871.39 217.11" stroke-width="1" zvalue="677"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="137" MaxPinNum="2"/>
   </metadata>
  <path d="M 1016.12 247.87 L 1016.12 217.11 L 871.39 217.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv10" d="M 1016.28 276.55 L 1016.28 304.89" stroke-width="1" zvalue="679"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@1" LinkObjectIDznd="155" MaxPinNum="2"/>
   </metadata>
  <path d="M 1016.28 276.55 L 1016.28 304.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv10" d="M 971.17 366.3 L 971.17 354.06" stroke-width="1" zvalue="682"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@0" LinkObjectIDznd="148@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 971.17 366.3 L 971.17 354.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv10" d="M 971.09 325.72 L 971.09 304.89 L 1065.54 304.89 L 1065.54 325.72" stroke-width="1" zvalue="686"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="148@0" LinkObjectIDznd="152@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 971.09 325.72 L 971.09 304.89 L 1065.54 304.89 L 1065.54 325.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv10" d="M 1065.35 354.06 L 1065.35 366.3" stroke-width="1" zvalue="687"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@1" LinkObjectIDznd="143@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1065.35 354.06 L 1065.35 366.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv10" d="M 1307.5 436.09 L 1307.5 467.87" stroke-width="1" zvalue="696"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@1" LinkObjectIDznd="157@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1307.5 436.09 L 1307.5 467.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="kv10" d="M 1307.94 298.71 L 1307.94 335.72" stroke-width="1" zvalue="699"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="187@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1307.94 298.71 L 1307.94 335.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv10" d="M 1307.76 364.06 L 1307.76 393.21" stroke-width="1" zvalue="700"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@1" LinkObjectIDznd="170@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1307.76 364.06 L 1307.76 393.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="v400" d="M 1307.7 532.81 L 1307.7 523.55" stroke-width="1" zvalue="702"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@0" LinkObjectIDznd="157@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1307.7 532.81 L 1307.7 523.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="36">
   <use class="kv10" height="20" transform="rotate(0,681.333,794.222) scale(1.77778,1.77778) translate(-294.194,-339.694)" width="10" x="672.4444444444445" xlink:href="#GroundDisconnector:地刀_0" y="776.4444444444446" zvalue="599"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449862041605" ObjectName="10kV备用线05567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449862041605"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,681.333,794.222) scale(1.77778,1.77778) translate(-294.194,-339.694)" width="10" x="672.4444444444445" y="776.4444444444446"/></g>
  <g id="45">
   <use class="kv10" height="20" transform="rotate(0,805.778,794.222) scale(1.77778,1.77778) translate(-348.639,-339.694)" width="10" x="796.8888888888888" xlink:href="#GroundDisconnector:地刀_0" y="776.4444444444446" zvalue="607"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449862172677" ObjectName="10kV#1泵变05667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449862172677"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,805.778,794.222) scale(1.77778,1.77778) translate(-348.639,-339.694)" width="10" x="796.8888888888888" y="776.4444444444446"/></g>
  <g id="57">
   <use class="kv10" height="20" transform="rotate(0,931.333,794.222) scale(1.77778,1.77778) translate(-403.569,-339.694)" width="10" x="922.4444444444443" xlink:href="#GroundDisconnector:地刀_0" y="776.4444444444446" zvalue="616"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449862369286" ObjectName="10kV压榨间05767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449862369286"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,931.333,794.222) scale(1.77778,1.77778) translate(-403.569,-339.694)" width="10" x="922.4444444444443" y="776.4444444444446"/></g>
  <g id="66">
   <use class="kv10" height="20" transform="rotate(0,1055.33,794.222) scale(1.77778,1.77778) translate(-457.819,-339.694)" width="10" x="1046.444444444444" xlink:href="#GroundDisconnector:地刀_0" y="776.4444444444446" zvalue="625"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449862565893" ObjectName="10kV电力间05867接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449862565893"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1055.33,794.222) scale(1.77778,1.77778) translate(-457.819,-339.694)" width="10" x="1046.444444444444" y="776.4444444444446"/></g>
  <g id="104">
   <use class="kv10" height="20" transform="rotate(0,944.222,537.333) scale(1.77778,1.77778) translate(-409.208,-227.306)" width="10" x="935.3333333333333" xlink:href="#GroundDisconnector:地刀_0" y="519.5555555555557" zvalue="645"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449862959109" ObjectName="05167"/>
   <cge:TPSR_Ref TObjectID="6192449862959109"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,944.222,537.333) scale(1.77778,1.77778) translate(-409.208,-227.306)" width="10" x="935.3333333333333" y="519.5555555555557"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="88">
   <use class="kv10" height="26" transform="rotate(0,1209.33,684.778) scale(1.11111,1.11111) translate(-120.267,-67.0333)" width="12" x="1202.666666666667" xlink:href="#Disconnector:小车隔刀熔断器_0" y="670.3333279291788" zvalue="631"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449862762501" ObjectName="10kV站用变0569隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449862762501"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1209.33,684.778) scale(1.11111,1.11111) translate(-120.267,-67.0333)" width="12" x="1202.666666666667" y="670.3333279291788"/></g>
  <g id="109">
   <use class="kv10" height="26" transform="rotate(0,1017.11,499.222) scale(1.11111,1.11111) translate(-101.044,-48.4778)" width="12" x="1010.444444444445" xlink:href="#Disconnector:小车隔刀熔断器_0" y="484.7777723736233" zvalue="650"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449863090181" ObjectName="0519"/>
   <cge:TPSR_Ref TObjectID="6192449863090181"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1017.11,499.222) scale(1.11111,1.11111) translate(-101.044,-48.4778)" width="12" x="1010.444444444445" y="484.7777723736233"/></g>
  <g id="122">
   <use class="kv10" height="30" transform="rotate(0,770.934,434.889) scale(1.18519,1.18519) translate(-119.069,-65.1736)" width="15" x="762.0447927813068" xlink:href="#Disconnector:刀闸_0" y="417.1111111111111" zvalue="659"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449863221253" ObjectName="10kV#1台变0546隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449863221253"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,770.934,434.889) scale(1.18519,1.18519) translate(-119.069,-65.1736)" width="15" x="762.0447927813068" y="417.1111111111111"/></g>
  <g id="126">
   <use class="kv10" height="30" transform="rotate(90,817.111,398.222) scale(1.11111,1.11111) translate(-80.8778,-38.1556)" width="15" x="808.7777777777777" xlink:href="#Disconnector:令克_0" y="381.5555555555555" zvalue="662"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449863286789" ObjectName="10kV#1台变0548隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449863286789"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,817.111,398.222) scale(1.11111,1.11111) translate(-80.8778,-38.1556)" width="15" x="808.7777777777777" y="381.5555555555555"/></g>
  <g id="131">
   <use class="kv10" height="30" transform="rotate(0,872.296,369.333) scale(1.11111,1.11111) translate(-86.3963,-35.2667)" width="15" x="863.9629629629629" xlink:href="#Disconnector:令克_0" y="352.6666666666667" zvalue="667"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449863352325" ObjectName="0526"/>
   <cge:TPSR_Ref TObjectID="6192449863352325"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,872.296,369.333) scale(1.11111,1.11111) translate(-86.3963,-35.2667)" width="15" x="863.9629629629629" y="352.6666666666667"/></g>
  <g id="148">
   <use class="kv10" height="30" transform="rotate(0,971,340.444) scale(1.11111,1.11111) translate(-96.2667,-32.3778)" width="15" x="962.6666666666666" xlink:href="#Disconnector:令克_0" y="323.7777710225847" zvalue="681"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449863548933" ObjectName="0532"/>
   <cge:TPSR_Ref TObjectID="6192449863548933"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,971,340.444) scale(1.11111,1.11111) translate(-96.2667,-32.3778)" width="15" x="962.6666666666666" y="323.7777710225847"/></g>
  <g id="152">
   <use class="kv10" height="30" transform="rotate(0,1065.44,340.444) scale(1.11111,1.11111) translate(-105.711,-32.3778)" width="15" x="1057.111111111111" xlink:href="#Disconnector:令克_0" y="323.7777709960938" zvalue="685"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449863614469" ObjectName="0533"/>
   <cge:TPSR_Ref TObjectID="6192449863614469"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1065.44,340.444) scale(1.11111,1.11111) translate(-105.711,-32.3778)" width="15" x="1057.111111111111" y="323.7777709960938"/></g>
  <g id="187">
   <use class="kv10" height="30" transform="rotate(0,1307.85,350.444) scale(1.11111,1.11111) translate(-129.952,-33.3778)" width="15" x="1299.518518518518" xlink:href="#Disconnector:令克_0" y="333.7777709960938" zvalue="698"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449863745541" ObjectName="0416"/>
   <cge:TPSR_Ref TObjectID="6192449863745541"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1307.85,350.444) scale(1.11111,1.11111) translate(-129.952,-33.3778)" width="15" x="1299.518518518518" y="333.7777709960938"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="107">
   <use class="kv10" height="18" transform="rotate(0,1020.07,557.111) scale(2.83951,2.83951) translate(-647.034,-344.356)" width="15" x="998.7777777777777" xlink:href="#Accessory:PT8_0" y="531.5555555555555" zvalue="647"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449863024645" ObjectName="051PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1020.07,557.111) scale(2.83951,2.83951) translate(-647.034,-344.356)" width="15" x="998.7777777777777" y="531.5555555555555"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="157">
   <g id="1570">
    <use class="kv10" height="30" transform="rotate(0,1307.5,497) scale(2.39551,2.37778) translate(-747.731,-267.315)" width="20" x="1283.54" xlink:href="#PowerTransformer2:Y-y不带中性点_0" y="461.33" zvalue="689"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874442866691" ObjectName="10"/>
    </metadata>
   </g>
   <g id="1571">
    <use class="v400" height="30" transform="rotate(0,1307.5,497) scale(2.39551,2.37778) translate(-747.731,-267.315)" width="20" x="1283.54" xlink:href="#PowerTransformer2:Y-y不带中性点_1" y="461.33" zvalue="689"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874442932227" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399453704195" ObjectName="10kV生活区变"/>
   <cge:TPSR_Ref TObjectID="6755399453704195"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1307.5,497) scale(2.39551,2.37778) translate(-747.731,-267.315)" width="20" x="1283.54" y="461.33"/></g>
 </g>
</svg>