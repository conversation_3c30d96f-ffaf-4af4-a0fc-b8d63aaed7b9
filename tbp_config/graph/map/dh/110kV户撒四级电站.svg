<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549678309377" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:传输线_0" viewBox="0,0,12,22">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="1"/>
   <path d="M 1.16667 1 L 10.8333 1 L 6 7.63889 L 1.16667 1 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="14.27777777777778" y2="7.638888888888888"/>
   <path d="M 1.16667 20.9167 L 10.8333 20.9167 L 6 14.2778 L 1.16667 20.9167 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:5绕组母线PT带避雷器_0" viewBox="0,0,40,35">
   <use terminal-index="0" type="0" x="22.10905664884498" xlink:href="#terminal" y="34.51612485684674"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.91666666666667" x2="34.5" y1="28.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="21.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="22" y1="28.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.46666666666667" x2="35.46666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.46666666666667" x2="33.46666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.13333333333333" x2="18.13333333333333" y1="19.27740325661302" y2="15.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.13333333333333" x2="20.13333333333333" y1="19.27740325661302" y2="18.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.13333333333333" x2="20.13333333333333" y1="15.27740325661302" y2="16.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.5" x2="34.5" y1="28.5" y2="14.5"/>
   <rect fill-opacity="0" height="15.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,34.5,14.36) scale(1,-1) translate(0,-926.43)" width="7" x="31" y="6.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.42943360505483" x2="34.42943360505483" y1="6.610736589946352" y2="3.462520268614096"/>
   <ellipse cx="11.81" cy="17.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="36.45921744067709" x2="32.18240159599324" y1="3.3946117330996" y2="3.3946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.62588410734375" x2="33.01573492932658" y1="2.144611733099605" y2="2.144611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.04255077401042" x2="33.59906826265991" y1="0.8946117330996053" y2="0.8946117330996053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.78240159599324" x2="9.382401595993244" y1="17.45662962336982" y2="18.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.78240159599325" x2="14.18240159599324" y1="17.45662962336982" y2="18.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.78240159599324" x2="11.78240159599324" y1="14.98154965466559" y2="17.4566296233698"/>
   <ellipse cx="4.73" cy="17.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.98" cy="10.41" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.949068262659907" x2="5.549068262659912" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.949068262659912" x2="10.34906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.69906826265991" x2="4.69906826265991" y1="15.23154965466559" y2="17.7066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.699068262659912" x2="7.099068262659907" y1="17.70662962336982" y2="18.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.94906826265991" x2="7.94906826265991" y1="8.148216321332258" y2="10.62329629003646"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.699068262659907" x2="2.299068262659912" y1="17.70662962336982" y2="18.94416960772192"/>
   <ellipse cx="18.81" cy="17.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.48" cy="10.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.44906826265991" x2="17.84906826265991" y1="10.53996295670315" y2="11.77750294105526"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.4490682626599" x2="13.04906826265991" y1="10.53996295670315" y2="11.77750294105526"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.44906826265991" x2="15.44906826265991" y1="8.064882987998928" y2="10.53996295670313"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:线路PT三绕组_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="20" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="24" y2="24"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,11) scale(1,1) translate(0,0)" width="6" x="17" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="32" y1="17" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.98040845230975" x2="31.98040845230975" y1="16.76788570496156" y2="23.53635804601289"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.98821300514558" x2="19.98821300514558" y1="1.029523490692871" y2="18.75"/>
   <path d="M 30.0147 23.4481 L 34.2865 23.4481 L 32.115 28.6155 L 30.0147 23.4481" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <rect fill-opacity="0" height="6.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,32.04,24.56) scale(1,1) translate(0,0)" width="15.34" x="24.37" y="21.53"/>
   <ellipse cx="19.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.22179295459144" x2="32.22179295459144" y1="32.22550978083666" y2="36.2625308385742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.01788570496155" x2="35.42570020422134" y1="36.23023467011234" y2="36.23023467011234"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.26384963537316" x2="34.53572596821302" y1="37.52208140858838" y2="37.52208140858838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.15382387138148" x2="33.11176719059974" y1="38.49096646244535" y2="38.49096646244535"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="32.66666666666667" y2="32.66666666666667"/>
   <ellipse cx="12.01" cy="28.48" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.166666666666664" x2="15.16666666666666" y1="28.91666666666667" y2="28.91666666666667"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT232_0" viewBox="0,0,25,35">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="0.2666666666666657"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="12.5" y1="15.16666666666667" y2="0.5"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12.5,7.5) scale(1,1) translate(0,0)" width="6" x="9.5" y="2.5"/>
   <ellipse cx="12.75" cy="20" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.75" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.67" cy="29.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.82" cy="24.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333336" x2="5.583333333333336" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="5.583333333333334" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="5.5" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.75" x2="12.75" y1="22.25" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="12.75" y1="17.63888888888889" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="12.75" y1="17.63888888888889" y2="19.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="32" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.58333333333333" x2="12.58333333333333" y1="27.38888888888889" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.58333333333333" x2="12.58333333333333" y1="27.38888888888889" y2="29.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="19.31481481481482" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.94444444444444" x2="20.62962962962963" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="21.94444444444444" y1="24.96612466124661" y2="24.96612466124661"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:PT2525_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,4.96) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.25" y2="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="8.166666666666663" y1="24.91666666666666" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="10.75" y1="21.91666666666666" y2="24.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="13.75" y1="24.91666666666666" y2="26.91666666666666"/>
  </symbol>
  <symbol id="Accessory:PT1515_0" viewBox="0,0,15,23">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="0.2666666666666639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="3.916666666666664" y2="0.2499999999999982"/>
   <ellipse cx="7.67" cy="16.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.57" cy="8.85" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.583333333333334" x2="7.583333333333334" y1="14.63888888888889" y2="16.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333334" x2="7.583333333333334" y1="19.25" y2="16.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333334" x2="7.583333333333334" y1="14.63888888888889" y2="16.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.749999999999996" x2="9.694444444444443" y1="9.549457994579948" y2="9.549457994579948"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.750000000000003" x2="7.064814814814818" y1="9.549457994579944" y2="7.083333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.694444444444443" x2="8.379629629629626" y1="9.549457994579944" y2="7.083333333333332"/>
  </symbol>
  <symbol id="Accessory:带熔断器35kVPT11_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="1.1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="26.25" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="18.06481481481482" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="20.69444444444444" y1="24.96612466124661" y2="24.96612466124661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69444444444444" x2="19.37962962962963" y1="24.96612466124661" y2="22.5"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,5.71) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="13" y2="1"/>
   <ellipse cx="15.03" cy="18.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.67" cy="23.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="18.5" cy="23.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="20.5" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变YD2022_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="24" y1="24" y2="18"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.98980556533989" x2="12.83661970177291" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.9898055653399" x2="15.14299142890688" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.83661970177294" x2="15.1429914289069" y1="27.75505857643125" y2="27.75505857643125"/>
  </symbol>
  <symbol id="PowerTransformer3:中压侧无中性点_0" viewBox="0,0,50,50">
   <ellipse cx="14.93" cy="15" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.33333333333333" x2="15.15740740740741" y1="8.083333333333332" y2="11.6754686785551"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.08333333333333" x2="15.17203932327389" y1="8.166666666666668" y2="11.6754686785551"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.16666666666666" x2="15.16666666666666" y1="11.66666666666666" y2="16.41666666666666"/>
   <path d="M 33.5 -1.41667 L 30 -0.166667 L 31.5 1.41667 z" fill="rgb(255,0,0)" fill-opacity="1" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.66666666666667" x2="-4.916666666666675" y1="-0.6666666666666679" y2="26.25"/>
   <use terminal-index="0" type="1" x="14.91666666666666" xlink:href="#terminal" y="0.3333333333333286"/>
   <use terminal-index="3" type="2" x="15.16666666666666" xlink:href="#terminal" y="11.66666666666667"/>
  </symbol>
  <symbol id="PowerTransformer3:中压侧无中性点_1" viewBox="0,0,50,50">
   <use terminal-index="1" type="1" x="49.58333333333334" xlink:href="#terminal" y="25"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="36.33333333333333" x2="36.33333333333333" y1="20.75" y2="25.04869684499314"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="36.33996342021034" x2="40.91666666666666" y1="25.05601280292639" y2="29.58333333333334"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="36.33264746227709" x2="31.41666666666667" y1="25.04869684499314" y2="29.5"/>
   <ellipse cx="34.87" cy="25.17" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer3:中压侧无中性点_2" viewBox="0,0,50,50">
   <use terminal-index="2" type="1" x="15.08333333333333" xlink:href="#terminal" y="50"/>
   <ellipse cx="15.03" cy="35.08" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0833 33.0833 L 10 41.0833 L 20.0833 41.0833 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV户撒四级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="48" xlink:href="logo.png" y="43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,176,73) scale(1,1) translate(0,0)" writing-mode="lr" x="176" xml:space="preserve" y="76.5" zvalue="54"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,176.5,72.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="176.5" xml:space="preserve" y="81.69" zvalue="55">110kV户撒四级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="213" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,82.625,185.25) scale(1,1) translate(0,0)" width="72.88" x="46.19" y="173.25" zvalue="1438"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.625,185.25) scale(1,1) translate(0,0)" writing-mode="lr" x="82.63" xml:space="preserve" y="189.75" zvalue="1438">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,435.222,281.889) scale(1,1) translate(0,0)" writing-mode="lr" x="435.22" xml:space="preserve" y="286.39" zvalue="7">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1802.44,317.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1802.44" xml:space="preserve" y="321.72" zvalue="12">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1024.13,689.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1024.13" xml:space="preserve" y="694.0599999999999" zvalue="16">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,793.333,220.111) scale(1,1) translate(0,0)" writing-mode="lr" x="793.33" xml:space="preserve" y="224.61" zvalue="17">161</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,784.556,267.667) scale(1,1) translate(0,0)" writing-mode="lr" x="784.5599999999999" xml:space="preserve" y="272.17" zvalue="18">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,786.222,159.667) scale(1,1) translate(0,0)" writing-mode="lr" x="786.22" xml:space="preserve" y="164.17" zvalue="20">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,771.932,69.4444) scale(1,1) translate(0,0)" writing-mode="lr" x="771.9299999999999" xml:space="preserve" y="73.94" zvalue="25">110kV撒腊线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,725.056,258.778) scale(1,1) translate(0,0)" writing-mode="lr" x="725.0599999999999" xml:space="preserve" y="263.28" zvalue="27">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,725.056,206.333) scale(1,1) translate(0,0)" writing-mode="lr" x="725.0599999999999" xml:space="preserve" y="210.83" zvalue="29">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,725.056,151.889) scale(1,1) translate(0,0)" writing-mode="lr" x="725.0599999999999" xml:space="preserve" y="156.39" zvalue="32">67</text>
  <line fill="none" id="67" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382" x2="382" y1="11" y2="1041" zvalue="58"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,576.556,255.667) scale(1,1) translate(0,0)" writing-mode="lr" x="576.5599999999999" xml:space="preserve" y="260.17" zvalue="94">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,509.333,246.944) scale(1,1) translate(0,0)" writing-mode="lr" x="509.33" xml:space="preserve" y="251.44" zvalue="96">19017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,692.778,329.222) scale(1,1) translate(0,0)" writing-mode="lr" x="692.78" xml:space="preserve" y="333.72" zvalue="102">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,702.111,369.222) scale(1,1) translate(0,0)" writing-mode="lr" x="702.11" xml:space="preserve" y="373.72" zvalue="103">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,695.111,415.889) scale(1,1) translate(0,0)" writing-mode="lr" x="695.11" xml:space="preserve" y="420.39" zvalue="105">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,609.556,521.667) scale(1,1) translate(0,0)" writing-mode="lr" x="609.5599999999999" xml:space="preserve" y="526.17" zvalue="106">1020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,632.444,358.222) scale(1,1) translate(0,0)" writing-mode="lr" x="632.4400000000001" xml:space="preserve" y="362.72" zvalue="115">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,632.444,402.667) scale(1,1) translate(0,0)" writing-mode="lr" x="632.4400000000001" xml:space="preserve" y="407.17" zvalue="117">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1084.89,519.699) scale(1,1) translate(0,0)" writing-mode="lr" x="1084.89" xml:space="preserve" y="524.2" zvalue="138">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,706.222,650.556) scale(1,1) translate(-7.70186e-13,-4.26696e-13)" writing-mode="lr" x="706.22" xml:space="preserve" y="655.0599999999999" zvalue="142">602</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,632.444,617.111) scale(1,1) translate(0,0)" writing-mode="lr" x="632.4400000000001" xml:space="preserve" y="621.61" zvalue="146">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="309" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,774.03,756.111) scale(1,1) translate(0,0)" writing-mode="lr" x="774.03" xml:space="preserve" y="760.61" zvalue="613">661</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="308" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,735.641,807.222) scale(1,1) translate(0,0)" writing-mode="lr" x="735.64" xml:space="preserve" y="811.72" zvalue="615">67</text>
  <line fill="none" id="666" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="146.8704926140824" y2="146.8704926140824" zvalue="1056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="247" y2="247"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="273" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="247" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="247" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="247" y2="247"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="273" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="247" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="247" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="273" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="297.25" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="273" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="273" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="273" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="297.25" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="273" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="273" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="297.25" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="320" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="297.25" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="297.25" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="297.25" y2="297.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="320" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="297.25" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="297.25" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="320" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="342.75" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="320" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="320" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="320" y2="320"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="342.75" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="320" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="320" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="342.75" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="365.5" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="342.75" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="342.75" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="342.75" y2="342.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="365.5" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="342.75" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="342.75" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="365.5" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="388.25" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="365.5" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="365.5" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="365.5" y2="365.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="388.25" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="365.5" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="365.5" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="388.25" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="411" y2="411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="388.25" y2="411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="388.25" y2="411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="388.25" y2="388.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="411" y2="411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="388.25" y2="411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="388.25" y2="411"/>
  <line fill="none" id="613" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="492.8704926140824" y2="492.8704926140824" zvalue="1058"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="999.0816" y2="1027"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="609" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,952) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="958" zvalue="1062">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="608" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50,986) scale(1,1) translate(0,0)" writing-mode="lr" x="50" xml:space="preserve" y="992" zvalue="1063">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="607" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232,986) scale(1,1) translate(0,0)" writing-mode="lr" x="232" xml:space="preserve" y="992" zvalue="1064">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="606" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="49" xml:space="preserve" y="1020" zvalue="1065">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="605" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="231" xml:space="preserve" y="1020" zvalue="1066">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="602" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.5,566.5) scale(1,1) translate(0,0)" writing-mode="lr" x="73.5" xml:space="preserve" y="571" zvalue="1069">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="601" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,198.399,185.841) scale(1,1) translate(0,0)" writing-mode="lr" x="198.4" xml:space="preserve" y="190.34" zvalue="1070">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="600" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,303.399,185.841) scale(1,1) translate(0,0)" writing-mode="lr" x="303.4" xml:space="preserve" y="190.34" zvalue="1071">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.054,954) scale(1,1) translate(0,0)" writing-mode="lr" x="233.05" xml:space="preserve" y="960" zvalue="1080"> HuSa4-01-2020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,323.054,986) scale(1,1) translate(0,0)" writing-mode="lr" x="323.05" xml:space="preserve" y="992" zvalue="1082">20200806</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,55.5,260) scale(1,1) translate(0,0)" writing-mode="lr" x="13" xml:space="preserve" y="264.5" zvalue="1083">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,236,260) scale(1,1) translate(0,0)" writing-mode="lr" x="193.5" xml:space="preserve" y="264.5" zvalue="1084">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.6875,333.25) scale(1,1) translate(0,0)" writing-mode="lr" x="58.69" xml:space="preserve" y="337.75" zvalue="1085">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.125,333.25) scale(1,1) translate(0,0)" writing-mode="lr" x="234.13" xml:space="preserve" y="337.75" zvalue="1086">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6875,357.25) scale(1,1) translate(0,0)" writing-mode="lr" x="55.69" xml:space="preserve" y="361.75" zvalue="1091">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,543.412,149) scale(1,1) translate(0,0)" writing-mode="lr" x="543.41" xml:space="preserve" y="153.5" zvalue="1094">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,509.333,288.111) scale(1,1) translate(0,0)" writing-mode="lr" x="509.33" xml:space="preserve" y="292.61" zvalue="1096">19010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,783.527,524.25) scale(1,1) translate(0,0)" writing-mode="lr" x="783.5267647058824" xml:space="preserve" y="528.75" zvalue="1098">#2主变20MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,632.444,459.111) scale(1,1) translate(0,0)" writing-mode="lr" x="632.4400000000001" xml:space="preserve" y="463.61" zvalue="1101">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1250.5,533.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1250.5" xml:space="preserve" y="537.75" zvalue="1104">#1主变5MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,954.444,329.556) scale(1,1) translate(0,0)" writing-mode="lr" x="954.4400000000001" xml:space="preserve" y="334.06" zvalue="1108">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,963.778,369.556) scale(1,1) translate(0,0)" writing-mode="lr" x="963.78" xml:space="preserve" y="374.06" zvalue="1109">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,956.778,416.222) scale(1,1) translate(0,0)" writing-mode="lr" x="956.78" xml:space="preserve" y="420.72" zvalue="1111">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,894.111,358.556) scale(1,1) translate(0,0)" writing-mode="lr" x="894.11" xml:space="preserve" y="363.06" zvalue="1115">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,894.111,403) scale(1,1) translate(0,0)" writing-mode="lr" x="894.11" xml:space="preserve" y="407.5" zvalue="1118">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,894.111,459.444) scale(1,1) translate(3.93512e-13,0)" writing-mode="lr" x="894.11" xml:space="preserve" y="463.94" zvalue="1124">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="676" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1506.44,331.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1506.44" xml:space="preserve" y="336.39" zvalue="1130">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="675" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1515.78,371.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1515.78" xml:space="preserve" y="376.39" zvalue="1131">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="674" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1508.78,418.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1508.78" xml:space="preserve" y="423.06" zvalue="1133">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="673" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1446.11,360.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1446.11" xml:space="preserve" y="365.39" zvalue="1137">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="672" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1446.11,405.333) scale(1,1) translate(0,2.63345e-13)" writing-mode="lr" x="1446.11" xml:space="preserve" y="409.83" zvalue="1140">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="671" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1446.11,461.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1446.11" xml:space="preserve" y="466.28" zvalue="1144">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="729" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1213.83,216.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1213.83" xml:space="preserve" y="220.67" zvalue="1150">361</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="728" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1205.06,263.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1205.06" xml:space="preserve" y="268.22" zvalue="1151">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="727" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1206.72,163.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1206.72" xml:space="preserve" y="168.22" zvalue="1154">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="726" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1192.43,77.5) scale(1,1) translate(3.84836e-13,0)" writing-mode="lr" x="1192.43" xml:space="preserve" y="82" zvalue="1158">35kV三四线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="725" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1145.56,254.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1145.56" xml:space="preserve" y="259.33" zvalue="1160">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="724" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1145.56,206.075) scale(1,1) translate(0,0)" writing-mode="lr" x="1145.56" xml:space="preserve" y="210.57" zvalue="1162">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="723" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1145.56,143.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1145.56" xml:space="preserve" y="148.12" zvalue="1164">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="769" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1215.33,141.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1215.33" xml:space="preserve" y="146.06" zvalue="1176">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="828" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1649.67,220.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1649.67" xml:space="preserve" y="224.61" zvalue="1239">366</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="827" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1640.89,267.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1640.89" xml:space="preserve" y="272.17" zvalue="1240">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="834" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1636,76.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1636" xml:space="preserve" y="81" zvalue="1244">35kV大盈江四级施工电源线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="839" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1795.64,254.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1795.64" xml:space="preserve" y="258.67" zvalue="1247">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="838" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1728.42,245.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1728.42" xml:space="preserve" y="249.94" zvalue="1249">39017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="837" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1762.5,147.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1762.5" xml:space="preserve" y="152" zvalue="1255">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="836" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1728.42,286.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1728.42" xml:space="preserve" y="291.11" zvalue="1258">39010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="851" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,750,970.25) scale(1,1) translate(0,0)" writing-mode="lr" x="750" xml:space="preserve" y="974.75" zvalue="1261">#1发电机7MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,709,887) scale(1,1) translate(0,0)" writing-mode="lr" x="709" xml:space="preserve" y="891.5" zvalue="1264">6911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,824,891) scale(1,1) translate(0,0)" writing-mode="lr" x="824" xml:space="preserve" y="895.5" zvalue="1268">6912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,973.53,756.222) scale(1,1) translate(0,0)" writing-mode="lr" x="973.53" xml:space="preserve" y="760.72" zvalue="1278">662</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,935.141,807.333) scale(1,1) translate(0,0)" writing-mode="lr" x="935.14" xml:space="preserve" y="811.83" zvalue="1280">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,949.5,970.361) scale(1,1) translate(0,0)" writing-mode="lr" x="949.5" xml:space="preserve" y="974.8611128065321" zvalue="1289">#2发电机7MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,910.75,887.111) scale(1,1) translate(0,0)" writing-mode="lr" x="910.75" xml:space="preserve" y="891.61" zvalue="1292">6921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1023,891.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1023" xml:space="preserve" y="895.61" zvalue="1296">6922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,514,757.222) scale(1,1) translate(0,0)" writing-mode="lr" x="514" xml:space="preserve" y="761.72" zvalue="1306">663</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,475.611,808.333) scale(1,1) translate(0,0)" writing-mode="lr" x="475.61" xml:space="preserve" y="812.83" zvalue="1308">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,487,927.5) scale(1,1) translate(0,0)" writing-mode="lr" x="487" xml:space="preserve" y="932" zvalue="1318">至生活区变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,638,758.333) scale(1,1) translate(0,0)" writing-mode="lr" x="638" xml:space="preserve" y="762.83" zvalue="1320">664</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,598.111,809.444) scale(1,1) translate(0,0)" writing-mode="lr" x="598.11" xml:space="preserve" y="813.9400000000001" zvalue="1322">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="170" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,599.56,931) scale(1,1) translate(0,0)" writing-mode="lr" x="599.5599999999999" xml:space="preserve" y="935.5" zvalue="1333">6.3kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="177" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1135,769) scale(1,1) translate(0,0)" writing-mode="lr" x="1135" xml:space="preserve" y="773.5" zvalue="1338">6901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" x="1110.25" xml:space="preserve" y="869" zvalue="1339">6.3kV母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1110.25" xml:space="preserve" y="886" zvalue="1339">互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="250" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,54.5,286) scale(1,1) translate(0,0)" writing-mode="lr" x="12" xml:space="preserve" y="290.5" zvalue="1440">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="255" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235,286) scale(1,1) translate(0,0)" writing-mode="lr" x="192.5" xml:space="preserve" y="290.5" zvalue="1441">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6875,379.25) scale(1,1) translate(0,0)" writing-mode="lr" x="55.69" xml:space="preserve" y="383.75" zvalue="1446">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="231" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,223.688,378.25) scale(1,1) translate(0,0)" writing-mode="lr" x="223.69" xml:space="preserve" y="382.75" zvalue="1449">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="235" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6875,402.25) scale(1,1) translate(0,0)" writing-mode="lr" x="55.69" xml:space="preserve" y="406.75" zvalue="1456">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="237" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,223.688,401.25) scale(1,1) translate(0,0)" writing-mode="lr" x="223.69" xml:space="preserve" y="405.75" zvalue="1459">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="233" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,54.5,309) scale(1,1) translate(0,0)" writing-mode="lr" x="12" xml:space="preserve" y="313.5" zvalue="1462">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="239" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,234.5,308) scale(1,1) translate(0,0)" writing-mode="lr" x="192" xml:space="preserve" y="312.5" zvalue="1465">厂用电率</text>
 </g>
 <g id="ButtonClass">
  <g href="户撒四级电站_110kV母线.svg"><rect fill-opacity="0" height="40" width="12" x="545" y="230" zvalue="1053"/></g>
  <g href="户撒四级电站_110kV母线.svg"><rect fill-opacity="0" height="40" width="12" x="1764.09" y="228.5" zvalue="1253"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="46.19" y="173.25" zvalue="1438"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="89">
   <use class="kv110" height="30" transform="rotate(0,551.222,256.667) scale(-1.11111,-0.814815) translate(-1046.49,-574.444)" width="15" x="542.8888888888888" xlink:href="#Disconnector:刀闸_0" y="244.4444581137762" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453693800450" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453693800450"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,551.222,256.667) scale(-1.11111,-0.814815) translate(-1046.49,-574.444)" width="15" x="542.8888888888888" y="244.4444581137762"/></g>
  <g id="848">
   <use class="kv35" height="30" transform="rotate(0,1770.31,255.167) scale(-1.11111,-0.814815) translate(-3362.76,-571.104)" width="15" x="1761.976400781394" xlink:href="#Disconnector:刀闸_0" y="242.9444581137762" zvalue="1246"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453698453506" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453698453506"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1770.31,255.167) scale(-1.11111,-0.814815) translate(-3362.76,-571.104)" width="15" x="1761.976400781394" y="242.9444581137762"/></g>
  <g id="21">
   <use class="kv110" height="30" transform="rotate(0,772,268.667) scale(-1.11111,-0.814815) translate(-1465.97,-601.172)" width="15" x="763.6666666666666" xlink:href="#Disconnector:刀闸_0" y="256.4444580078124" zvalue="17"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453693210626" ObjectName="110kV撒腊线1611隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453693210626"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,772,268.667) scale(-1.11111,-0.814815) translate(-1465.97,-601.172)" width="15" x="763.6666666666666" y="256.4444580078124"/></g>
  <g id="22">
   <use class="kv110" height="30" transform="rotate(0,772,160.667) scale(-1.11111,-0.814815) translate(-1465.97,-360.626)" width="15" x="763.6666666931577" xlink:href="#Disconnector:刀闸_0" y="148.4444444444445" zvalue="19"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453693276162" ObjectName="110kV撒腊线1616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453693276162"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,772,160.667) scale(-1.11111,-0.814815) translate(-1465.97,-360.626)" width="15" x="763.6666666931577" y="148.4444444444445"/></g>
  <g id="99">
   <use class="kv110" height="30" transform="rotate(0,681.333,330.222) scale(1.11111,0.814815) translate(-67.3,72.2727)" width="15" x="673.0000101725263" xlink:href="#Disconnector:刀闸_0" y="318" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453693997058" ObjectName="#2主变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453693997058"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,681.333,330.222) scale(1.11111,0.814815) translate(-67.3,72.2727)" width="15" x="673.0000101725263" y="318"/></g>
  <g id="103">
   <use class="kv110" height="30" transform="rotate(0,681.333,416.889) scale(1.11111,0.814815) translate(-67.3,91.9697)" width="15" x="673.0000100665623" xlink:href="#Disconnector:刀闸_0" y="404.6666666666667" zvalue="104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453694062594" ObjectName="#2主变110kV侧1026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453694062594"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,681.333,416.889) scale(1.11111,0.814815) translate(-67.3,91.9697)" width="15" x="673.0000100665623" y="404.6666666666667"/></g>
  <g id="668">
   <use class="kv110" height="30" transform="rotate(0,943,330.556) scale(1.11111,0.814815) translate(-93.4667,72.3485)" width="15" x="934.666666666667" xlink:href="#Disconnector:刀闸_0" y="318.3333282470703" zvalue="1106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453695897602" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453695897602"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,943,330.556) scale(1.11111,0.814815) translate(-93.4667,72.3485)" width="15" x="934.666666666667" y="318.3333282470703"/></g>
  <g id="662">
   <use class="kv110" height="30" transform="rotate(0,943,417.222) scale(1.11111,0.814815) translate(-93.4667,92.0455)" width="15" x="934.6666665607029" xlink:href="#Disconnector:刀闸_0" y="404.999994913737" zvalue="1110"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453695832066" ObjectName="#1主变110kV侧1016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453695832066"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,943,417.222) scale(1.11111,0.814815) translate(-93.4667,92.0455)" width="15" x="934.6666665607029" y="404.999994913737"/></g>
  <g id="688">
   <use class="kv35" height="30" transform="rotate(0,1495,332.889) scale(1.11111,0.814815) translate(-148.667,72.8788)" width="15" x="1486.666666666667" xlink:href="#Disconnector:刀闸_0" y="320.6666564941406" zvalue="1128"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453696421890" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453696421890"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1495,332.889) scale(1.11111,0.814815) translate(-148.667,72.8788)" width="15" x="1486.666666666667" y="320.6666564941406"/></g>
  <g id="686">
   <use class="kv35" height="30" transform="rotate(0,1495,419.556) scale(1.11111,0.814815) translate(-148.667,92.5758)" width="15" x="1486.666666560703" xlink:href="#Disconnector:刀闸_0" y="407.3333231608073" zvalue="1132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453696356354" ObjectName="#1主变35kV侧3016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453696356354"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1495,419.556) scale(1.11111,0.814815) translate(-148.667,92.5758)" width="15" x="1486.666666560703" y="407.3333231608073"/></g>
  <g id="763">
   <use class="kv35" height="30" transform="rotate(0,1192.5,264.722) scale(-1.11111,-0.814815) translate(-2264.92,-592.386)" width="15" x="1184.166666666667" xlink:href="#Disconnector:刀闸_0" y="252.4999862247043" zvalue="1149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453697011714" ObjectName="35kV三四线3611隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453697011714"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1192.5,264.722) scale(-1.11111,-0.814815) translate(-2264.92,-592.386)" width="15" x="1184.166666666667" y="252.4999862247043"/></g>
  <g id="762">
   <use class="kv35" height="30" transform="rotate(0,1192.5,164.722) scale(-1.11111,-0.814815) translate(-2264.92,-369.659)" width="15" x="1184.166666693158" xlink:href="#Disconnector:刀闸_0" y="152.4999862247043" zvalue="1152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453696946178" ObjectName="35kV三四线3616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453696946178"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1192.5,164.722) scale(-1.11111,-0.814815) translate(-2264.92,-369.659)" width="15" x="1184.166666693158" y="152.4999862247043"/></g>
  <g id="766">
   <use class="kv35" height="30" transform="rotate(90,1215.33,129.222) scale(-1.11111,-0.814815) translate(-2308.3,-290.591)" width="15" x="1207" xlink:href="#Disconnector:刀闸_0" y="116.9999999999999" zvalue="1175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453697142786" ObjectName="35kV三四线3619隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453697142786"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1215.33,129.222) scale(-1.11111,-0.814815) translate(-2308.3,-290.591)" width="15" x="1207" y="116.9999999999999"/></g>
  <g id="831">
   <use class="kv35" height="30" transform="rotate(0,1628.33,268.667) scale(-1.11111,-0.814815) translate(-3093,-601.172)" width="15" x="1620" xlink:href="#Disconnector:刀闸_0" y="256.4444444444443" zvalue="1238"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453697994754" ObjectName="35kV大盈江四级施工电源线3661隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453697994754"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1628.33,268.667) scale(-1.11111,-0.814815) translate(-3093,-601.172)" width="15" x="1620" y="256.4444444444443"/></g>
  <g id="9">
   <use class="v6300" height="26" transform="rotate(0,687,888) scale(1,1) translate(0,0)" width="12" x="681" xlink:href="#Disconnector:单手车刀闸1212_0" y="875" zvalue="1263"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453698650114" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453698650114"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,687,888) scale(1,1) translate(0,0)" width="12" x="681" y="875"/></g>
  <g id="18">
   <use class="v6300" height="26" transform="rotate(0,797,888) scale(1,1) translate(0,0)" width="12" x="791" xlink:href="#Disconnector:单手车刀闸1212_0" y="875" zvalue="1267"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453698715650" ObjectName="#1发电机6912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453698715650"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,797,888) scale(1,1) translate(0,0)" width="12" x="791" y="875"/></g>
  <g id="124">
   <use class="v6300" height="26" transform="rotate(0,886.5,888.111) scale(1,1) translate(0,0)" width="12" x="880.5" xlink:href="#Disconnector:单手车刀闸1212_0" y="875.1111128065321" zvalue="1291"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453699239938" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453699239938"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,886.5,888.111) scale(1,1) translate(0,0)" width="12" x="880.5" y="875.1111128065321"/></g>
  <g id="121">
   <use class="v6300" height="26" transform="rotate(0,996.5,888.111) scale(1,1) translate(0,0)" width="12" x="990.5" xlink:href="#Disconnector:单手车刀闸1212_0" y="875.1111128065321" zvalue="1295"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453699174402" ObjectName="#2发电机6922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453699174402"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,996.5,888.111) scale(1,1) translate(0,0)" width="12" x="990.5" y="875.1111128065321"/></g>
  <g id="174">
   <use class="v6300" height="26" transform="rotate(0,1109,767) scale(1,1) translate(0,0)" width="12" x="1103" xlink:href="#Disconnector:单手车刀闸1212_0" y="754" zvalue="1337"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453700354050" ObjectName="6.3kV母线电压互感器6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453700354050"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1109,767) scale(1,1) translate(0,0)" width="12" x="1103" y="754"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="6">
   <path class="kv110" d="M 435.44 300.67 L 959 300.67" stroke-width="4" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674396864515" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674396864515"/></metadata>
  <path d="M 435.44 300.67 L 959 300.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv35" d="M 1126 300.67 L 1820.44 300.67" stroke-width="4" zvalue="11"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674396930051" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674396930051"/></metadata>
  <path d="M 1126 300.67 L 1820.44 300.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="v6300" d="M 437.13 708.89 L 1126 708.89" stroke-width="4" zvalue="15"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674396995587" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674396995587"/></metadata>
  <path d="M 437.13 708.89 L 1126 708.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="19">
   <use class="kv110" height="20" transform="rotate(0,772,221.111) scale(1.22222,1.11111) translate(-139.253,-21)" width="10" x="765.8888888888889" xlink:href="#Breaker:开关_0" y="210" zvalue="16"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925080580101" ObjectName="110kV撒腊线161断路器"/>
   <cge:TPSR_Ref TObjectID="6473925080580101"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,772,221.111) scale(1.22222,1.11111) translate(-139.253,-21)" width="10" x="765.8888888888889" y="210"/></g>
  <g id="101">
   <use class="kv110" height="20" transform="rotate(0,681.333,370.222) scale(1.22222,1.11111) translate(-122.768,-35.9111)" width="10" x="675.2222324079938" xlink:href="#Breaker:开关_0" y="359.1111111111111" zvalue="102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925080645635" ObjectName="#2主变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473925080645635"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,681.333,370.222) scale(1.22222,1.11111) translate(-122.768,-35.9111)" width="10" x="675.2222324079938" y="359.1111111111111"/></g>
  <g id="141">
   <use class="v6300" height="20" transform="rotate(0,680.632,651.556) scale(2,2) translate(-335.316,-315.778)" width="10" x="670.6323529411766" xlink:href="#Breaker:小车断路器_0" y="631.5555555555557" zvalue="141"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925080711171" ObjectName="#2主变6.3kV侧602断路器"/>
   <cge:TPSR_Ref TObjectID="6473925080711171"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,680.632,651.556) scale(2,2) translate(-335.316,-315.778)" width="10" x="670.6323529411766" y="631.5555555555557"/></g>
  <g id="341">
   <use class="v6300" height="20" transform="rotate(0,749.03,757.111) scale(2,2) translate(-369.515,-368.556)" width="10" x="739.0295695852833" xlink:href="#Breaker:小车断路器_0" y="737.1111128065321" zvalue="612"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925080776707" ObjectName="#1发电机661断路器"/>
   <cge:TPSR_Ref TObjectID="6473925080776707"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,749.03,757.111) scale(2,2) translate(-369.515,-368.556)" width="10" x="739.0295695852833" y="737.1111128065321"/></g>
  <g id="664">
   <use class="kv110" height="20" transform="rotate(0,943,370.556) scale(1.22222,1.11111) translate(-170.343,-35.9444)" width="10" x="936.8888889021345" xlink:href="#Breaker:开关_0" y="359.4444393581814" zvalue="1107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925080842243" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925080842243"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,943,370.556) scale(1.22222,1.11111) translate(-170.343,-35.9444)" width="10" x="936.8888889021345" y="359.4444393581814"/></g>
  <g id="687">
   <use class="kv35" height="20" transform="rotate(0,1495,372.889) scale(1.22222,1.11111) translate(-270.707,-36.1778)" width="10" x="1488.888888902135" xlink:href="#Breaker:开关_0" y="361.7777676052517" zvalue="1129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925080907781" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925080907781"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1495,372.889) scale(1.22222,1.11111) translate(-270.707,-36.1778)" width="10" x="1488.888888902135" y="361.7777676052517"/></g>
  <g id="764">
   <use class="kv35" height="20" transform="rotate(0,1192.5,217.167) scale(1.22222,1.11111) translate(-215.707,-20.6056)" width="10" x="1186.388888888889" xlink:href="#Breaker:开关_0" y="206.05554178026" zvalue="1148"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925080973317" ObjectName="35kV三四线361断路器"/>
   <cge:TPSR_Ref TObjectID="6473925080973317"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1192.5,217.167) scale(1.22222,1.11111) translate(-215.707,-20.6056)" width="10" x="1186.388888888889" y="206.05554178026"/></g>
  <g id="832">
   <use class="kv35" height="20" transform="rotate(0,1628.33,221.111) scale(1.22222,1.11111) translate(-294.949,-21)" width="10" x="1622.222222222222" xlink:href="#Breaker:开关_0" y="210" zvalue="1237"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925081169925" ObjectName="35kV大盈江四级施工电源线366断路器"/>
   <cge:TPSR_Ref TObjectID="6473925081169925"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1628.33,221.111) scale(1.22222,1.11111) translate(-294.949,-21)" width="10" x="1622.222222222222" y="210"/></g>
  <g id="135">
   <use class="v6300" height="20" transform="rotate(0,948.53,757.222) scale(2,2) translate(-469.265,-368.611)" width="10" x="938.5295695852833" xlink:href="#Breaker:小车断路器_0" y="737.2222290039062" zvalue="1277"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925081235461" ObjectName="#2发电机662断路器"/>
   <cge:TPSR_Ref TObjectID="6473925081235461"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,948.53,757.222) scale(2,2) translate(-469.265,-368.611)" width="10" x="938.5295695852833" y="737.2222290039062"/></g>
  <g id="152">
   <use class="v6300" height="20" transform="rotate(0,489,758.222) scale(2,2) translate(-239.5,-369.111)" width="10" x="479.0000000000002" xlink:href="#Breaker:小车断路器_0" y="738.2222256130642" zvalue="1305"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925081300997" ObjectName="至生活区变663断路器"/>
   <cge:TPSR_Ref TObjectID="6473925081300997"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,489,758.222) scale(2,2) translate(-239.5,-369.111)" width="10" x="479.0000000000002" y="738.2222256130642"/></g>
  <g id="168">
   <use class="v6300" height="20" transform="rotate(0,611.5,759.333) scale(2,2) translate(-300.75,-369.667)" width="10" x="601.5000000000002" xlink:href="#Breaker:小车断路器_0" y="739.3333384195963" zvalue="1319"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925081366533" ObjectName="6.3kV1号站用变664断路器"/>
   <cge:TPSR_Ref TObjectID="6473925081366533"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,611.5,759.333) scale(2,2) translate(-300.75,-369.667)" width="10" x="601.5000000000002" y="739.3333384195963"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="23">
   <path class="kv110" d="M 771.9 280.48 L 771.9 300.67" stroke-width="1" zvalue="20"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@0" LinkObjectIDznd="6@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 771.9 280.48 L 771.9 300.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv110" d="M 771.93 256.65 L 771.93 231.72" stroke-width="1" zvalue="21"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@1" LinkObjectIDznd="19@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 771.93 256.65 L 771.93 231.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv110" d="M 771.96 210.48 L 771.9 172.48" stroke-width="1" zvalue="22"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="19@0" LinkObjectIDznd="22@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 771.96 210.48 L 771.9 172.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv110" d="M 771.93 148.65 L 771.93 128.22" stroke-width="1" zvalue="25"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@1" LinkObjectIDznd="27@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 771.93 148.65 L 771.93 128.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv110" d="M 734.44 244.28 L 771.93 244.28" stroke-width="1" zvalue="33"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="24" MaxPinNum="2"/>
   </metadata>
  <path d="M 734.44 244.28 L 771.93 244.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv110" d="M 734.44 191.83 L 771.93 191.83" stroke-width="1" zvalue="34"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="8@0" LinkObjectIDznd="25" MaxPinNum="2"/>
   </metadata>
  <path d="M 734.44 191.83 L 771.93 191.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv110" d="M 734.44 137.39 L 771.93 137.39" stroke-width="1" zvalue="35"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 734.44 137.39 L 771.93 137.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv110" d="M 551.12 268.48 L 551.12 300.67" stroke-width="1" zvalue="98"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="6@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 551.12 268.48 L 551.12 300.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv110" d="M 551.15 244.65 L 551.15 217.37" stroke-width="1" zvalue="99"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@1" LinkObjectIDznd="68@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 551.15 244.65 L 551.15 217.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv110" d="M 520.17 230.78 L 551.15 230.78" stroke-width="1" zvalue="100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="97" MaxPinNum="2"/>
   </metadata>
  <path d="M 520.17 230.78 L 551.15 230.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv110" d="M 681.4 342.24 L 681.4 359.59" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@1" LinkObjectIDznd="101@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 681.4 342.24 L 681.4 359.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv110" d="M 681.41 380.83 L 681.43 405.07" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@1" LinkObjectIDznd="103@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 681.41 380.83 L 681.43 405.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv110" d="M 643.28 346.06 L 681.4 346.06" stroke-width="1" zvalue="117"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="108" MaxPinNum="2"/>
   </metadata>
  <path d="M 643.28 346.06 L 681.4 346.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv110" d="M 643.28 390.5 L 681.42 390.5" stroke-width="1" zvalue="118"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="109" MaxPinNum="2"/>
   </metadata>
  <path d="M 643.28 390.5 L 681.42 390.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv110" d="M 680.29 462.76 L 680.29 428.9" stroke-width="1" zvalue="119"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@0" LinkObjectIDznd="103@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 680.29 462.76 L 680.29 428.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv110" d="M 1184.93 507.14 L 1029.78 507.14" stroke-width="1" zvalue="139"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@2" LinkObjectIDznd="136@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1184.93 507.14 L 1029.78 507.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="295">
   <path class="v6300" d="M 680.63 669.56 L 680.63 708.89" stroke-width="1" zvalue="609"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@1" LinkObjectIDznd="15@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 680.63 669.56 L 680.63 708.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="296">
   <path class="kv110" d="M 681.43 318.4 L 681.43 300.67" stroke-width="1" zvalue="610"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="6@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 681.43 318.4 L 681.43 300.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="334">
   <path class="v6300" d="M 749.03 738.61 L 749.03 708.89" stroke-width="1" zvalue="623"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@0" LinkObjectIDznd="15@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 749.03 738.61 L 749.03 708.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="330">
   <path class="v6300" d="M 720.64 797.39 L 720.64 783.11 L 749.03 783.11" stroke-width="1" zvalue="628"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="340@0" LinkObjectIDznd="849" MaxPinNum="2"/>
   </metadata>
  <path d="M 720.64 797.39 L 720.64 783.11 L 749.03 783.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="329">
   <path class="v6300" d="M 772.06 802.74 L 772.06 783.11 L 749.03 783.11" stroke-width="1" zvalue="629"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="338@0" LinkObjectIDznd="330" MaxPinNum="2"/>
   </metadata>
  <path d="M 772.06 802.74 L 772.06 783.11 L 749.03 783.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="326">
   <path class="v6300" d="M 749.03 820.11 L 749.02 802.89" stroke-width="1" zvalue="632"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="331@0" LinkObjectIDznd="849" MaxPinNum="2"/>
   </metadata>
  <path d="M 749.03 820.11 L 749.02 802.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv110" d="M 520.17 274.61 L 551.12 274.61" stroke-width="1" zvalue="1096"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 520.17 274.61 L 551.12 274.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv110" d="M 643.28 445.61 L 680.29 445.61" stroke-width="1" zvalue="1101"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="3@0" LinkObjectIDznd="120" MaxPinNum="2"/>
   </metadata>
  <path d="M 643.28 445.61 L 680.29 445.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="660">
   <path class="kv110" d="M 943.07 342.57 L 943.07 359.93" stroke-width="1" zvalue="1112"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="668@1" LinkObjectIDznd="664@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 943.07 342.57 L 943.07 359.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="658">
   <path class="kv110" d="M 943.08 381.17 L 943.1 405.4" stroke-width="1" zvalue="1113"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="664@1" LinkObjectIDznd="662@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 943.08 381.17 L 943.1 405.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="281">
   <path class="kv110" d="M 904.94 346.39 L 943.07 346.39" stroke-width="1" zvalue="1117"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="583@0" LinkObjectIDznd="660" MaxPinNum="2"/>
   </metadata>
  <path d="M 904.94 346.39 L 943.07 346.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv110" d="M 904.94 390.83 L 943.09 390.83" stroke-width="1" zvalue="1119"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="581@0" LinkObjectIDznd="658" MaxPinNum="2"/>
   </metadata>
  <path d="M 904.94 390.83 L 943.09 390.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv110" d="M 943.1 318.74 L 943.1 300.67" stroke-width="1" zvalue="1121"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="668@0" LinkObjectIDznd="6@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 943.1 318.74 L 943.1 300.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="669">
   <path class="kv110" d="M 943.07 429.24 L 943.07 459 L 1184.97 459 L 1184.97 477.93" stroke-width="1" zvalue="1125"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="662@1" LinkObjectIDznd="77@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 943.07 429.24 L 943.07 459 L 1184.97 459 L 1184.97 477.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="670">
   <path class="kv110" d="M 904.94 445.94 L 943.07 445.94" stroke-width="1" zvalue="1126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@0" LinkObjectIDznd="669" MaxPinNum="2"/>
   </metadata>
  <path d="M 904.94 445.94 L 943.07 445.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="685">
   <path class="kv35" d="M 1495.07 344.9 L 1495.07 362.26" stroke-width="1" zvalue="1134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="688@1" LinkObjectIDznd="687@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1495.07 344.9 L 1495.07 362.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="684">
   <path class="kv35" d="M 1495.08 383.5 L 1495.1 407.74" stroke-width="1" zvalue="1135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="687@1" LinkObjectIDznd="686@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1495.08 383.5 L 1495.1 407.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="681">
   <path class="kv35" d="M 1456.94 348.72 L 1495.07 348.72" stroke-width="1" zvalue="1139"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="683@0" LinkObjectIDznd="685" MaxPinNum="2"/>
   </metadata>
  <path d="M 1456.94 348.72 L 1495.07 348.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="680">
   <path class="kv35" d="M 1456.94 393.17 L 1495.09 393.17" stroke-width="1" zvalue="1141"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="682@0" LinkObjectIDznd="684" MaxPinNum="2"/>
   </metadata>
  <path d="M 1456.94 393.17 L 1495.09 393.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="679">
   <path class="kv35" d="M 1495.1 321.07 L 1495.1 300.67" stroke-width="1" zvalue="1142"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="688@0" LinkObjectIDznd="10@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1495.1 321.07 L 1495.1 300.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="677">
   <path class="kv35" d="M 1456.94 448.28 L 1495.07 448.28" stroke-width="1" zvalue="1145"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="678@0" LinkObjectIDznd="689" MaxPinNum="2"/>
   </metadata>
  <path d="M 1456.94 448.28 L 1495.07 448.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="689">
   <path class="kv35" d="M 1184.9 579.27 L 1184.9 622 L 1495.07 622 L 1495.07 431.57" stroke-width="1" zvalue="1146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@1" LinkObjectIDznd="686@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1184.9 579.27 L 1184.9 622 L 1495.07 622 L 1495.07 431.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="761">
   <path class="kv35" d="M 1192.4 276.54 L 1192.4 300.67" stroke-width="1" zvalue="1153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="763@0" LinkObjectIDznd="10@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1192.4 276.54 L 1192.4 300.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="760">
   <path class="kv35" d="M 1192.43 252.71 L 1192.43 227.78" stroke-width="1" zvalue="1155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="763@1" LinkObjectIDznd="764@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1192.43 252.71 L 1192.43 227.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="759">
   <path class="kv35" d="M 1192.46 206.54 L 1192.4 176.54" stroke-width="1" zvalue="1156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="764@0" LinkObjectIDznd="762@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1192.46 206.54 L 1192.4 176.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="754">
   <path class="kv35" d="M 1154.94 240.33 L 1192.43 240.33" stroke-width="1" zvalue="1165"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="757@0" LinkObjectIDznd="760" MaxPinNum="2"/>
   </metadata>
  <path d="M 1154.94 240.33 L 1192.43 240.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="753">
   <path class="kv35" d="M 1154.94 191.57 L 1192.43 191.57" stroke-width="1" zvalue="1166"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="756@0" LinkObjectIDznd="759" MaxPinNum="2"/>
   </metadata>
  <path d="M 1154.94 191.57 L 1192.43 191.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="734">
   <path class="kv35" d="M 1192.43 152.71 L 1192.43 118.88" stroke-width="1" zvalue="1168"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="762@1" LinkObjectIDznd="758@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1192.43 152.71 L 1192.43 118.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="733">
   <path class="kv35" d="M 1154.94 129.12 L 1192.43 129.12" stroke-width="1" zvalue="1169"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="755@0" LinkObjectIDznd="734" MaxPinNum="2"/>
   </metadata>
  <path d="M 1154.94 129.12 L 1192.43 129.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="767">
   <path class="kv35" d="M 1203.52 129.12 L 1192.43 129.12" stroke-width="1" zvalue="1176"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="766@0" LinkObjectIDznd="734" MaxPinNum="2"/>
   </metadata>
  <path d="M 1203.52 129.12 L 1192.43 129.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="768">
   <path class="kv35" d="M 1227.35 129.15 L 1227.35 126.08" stroke-width="1" zvalue="1177"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="766@1" LinkObjectIDznd="765@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1227.35 129.15 L 1227.35 126.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="830">
   <path class="kv35" d="M 1628.24 280.48 L 1628.24 300.67" stroke-width="1" zvalue="1241"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="831@0" LinkObjectIDznd="10@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1628.24 280.48 L 1628.24 300.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="829">
   <path class="kv35" d="M 1628.27 256.65 L 1628.27 231.72" stroke-width="1" zvalue="1242"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="831@1" LinkObjectIDznd="832@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1628.27 256.65 L 1628.27 231.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="835">
   <path class="kv35" d="M 1628.29 210.48 L 1628.29 119.56" stroke-width="1" zvalue="1244"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="832@0" LinkObjectIDznd="833@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1628.29 210.48 L 1628.29 119.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="846">
   <path class="kv35" d="M 1770.21 266.98 L 1770.21 300.67" stroke-width="1" zvalue="1250"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="848@0" LinkObjectIDznd="10@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1770.21 266.98 L 1770.21 300.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="845">
   <path class="kv35" d="M 1770.24 243.15 L 1770.24 215.87" stroke-width="1" zvalue="1251"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="848@1" LinkObjectIDznd="842@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1770.24 243.15 L 1770.24 215.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="844">
   <path class="kv35" d="M 1739.25 229.28 L 1770.24 229.28" stroke-width="1" zvalue="1252"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="847@0" LinkObjectIDznd="845" MaxPinNum="2"/>
   </metadata>
  <path d="M 1739.25 229.28 L 1770.24 229.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="840">
   <path class="kv35" d="M 1739.25 273.11 L 1770.21 273.11" stroke-width="1" zvalue="1257"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="841@0" LinkObjectIDznd="846" MaxPinNum="2"/>
   </metadata>
  <path d="M 1739.25 273.11 L 1770.21 273.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="849">
   <path class="v6300" d="M 749 901.88 L 749.03 775.11" stroke-width="1" zvalue="1259"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="341@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 749 901.88 L 749.03 775.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="v6300" d="M 687 875.08 L 687 867 L 749.01 867" stroke-width="1" zvalue="1264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@1" LinkObjectIDznd="43" MaxPinNum="2"/>
   </metadata>
  <path d="M 687 875.08 L 687 867 L 749.01 867" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="v6300" d="M 687.08 900.97 L 687 911.77" stroke-width="1" zvalue="1265"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="4@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 687.08 900.97 L 687 911.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="v6300" d="M 749.01 867 L 797 867 L 797 875.08" stroke-width="1" zvalue="1269"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="849" LinkObjectIDznd="18@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 749.01 867 L 797 867 L 797 875.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="v6300" d="M 797.08 900.97 L 797.08 916.85" stroke-width="1" zvalue="1270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@0" LinkObjectIDznd="41@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 797.08 900.97 L 797.08 916.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="v6300" d="M 797 867 L 852 867 L 852 923.77" stroke-width="1" zvalue="1274"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43" LinkObjectIDznd="46@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 797 867 L 852 867 L 852 923.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="v6300" d="M 852 890 L 852 867" stroke-width="1" zvalue="1275"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="47" MaxPinNum="2"/>
   </metadata>
  <path d="M 852 890 L 852 867" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="v6300" d="M 948.53 738.72 L 948.53 708.89" stroke-width="1" zvalue="1282"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@0" LinkObjectIDznd="15@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 948.53 738.72 L 948.53 708.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="v6300" d="M 920.14 797.5 L 920.14 783.22 L 948.53 783.22" stroke-width="1" zvalue="1284"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@0" LinkObjectIDznd="127" MaxPinNum="2"/>
   </metadata>
  <path d="M 920.14 797.5 L 920.14 783.22 L 948.53 783.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="v6300" d="M 971.56 802.86 L 971.56 783.22 L 948.53 783.22" stroke-width="1" zvalue="1285"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@0" LinkObjectIDznd="130" MaxPinNum="2"/>
   </metadata>
  <path d="M 971.56 802.86 L 971.56 783.22 L 948.53 783.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="v6300" d="M 948.53 820.22 L 948.52 803" stroke-width="1" zvalue="1286"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="127" MaxPinNum="2"/>
   </metadata>
  <path d="M 948.53 820.22 L 948.52 803" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="v6300" d="M 948.5 901.99 L 948.53 775.22" stroke-width="1" zvalue="1287"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="135@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 948.5 901.99 L 948.53 775.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="v6300" d="M 886.5 875.19 L 886.5 867.11 L 948.51 867.11" stroke-width="1" zvalue="1293"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@1" LinkObjectIDznd="111" MaxPinNum="2"/>
   </metadata>
  <path d="M 886.5 875.19 L 886.5 867.11 L 948.51 867.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="v6300" d="M 886.58 901.08 L 886.5 911.88" stroke-width="1" zvalue="1294"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@0" LinkObjectIDznd="125@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 886.58 901.08 L 886.5 911.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="v6300" d="M 948.51 867.11 L 996.5 867.11 L 996.5 875.19" stroke-width="1" zvalue="1298"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127" LinkObjectIDznd="121@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 948.51 867.11 L 996.5 867.11 L 996.5 875.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="v6300" d="M 996.58 901.08 L 996.58 916.96" stroke-width="1" zvalue="1299"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@0" LinkObjectIDznd="112@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 996.58 901.08 L 996.58 916.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="v6300" d="M 996.5 867.11 L 1051.5 867.11 L 1051.5 923.88" stroke-width="1" zvalue="1302"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111" LinkObjectIDznd="94@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 996.5 867.11 L 1051.5 867.11 L 1051.5 923.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="v6300" d="M 1051.5 890.11 L 1051.5 867.11" stroke-width="1" zvalue="1303"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="92" MaxPinNum="2"/>
   </metadata>
  <path d="M 1051.5 890.11 L 1051.5 867.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="v6300" d="M 489 739.72 L 489 708.89" stroke-width="1" zvalue="1310"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="15@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 489 739.72 L 489 708.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="v6300" d="M 460.61 798.5 L 460.61 784.22 L 489 784.22" stroke-width="1" zvalue="1312"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@0" LinkObjectIDznd="153" MaxPinNum="2"/>
   </metadata>
  <path d="M 460.61 798.5 L 460.61 784.22 L 489 784.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="v6300" d="M 512.03 803.86 L 512.03 784.22 L 489 784.22" stroke-width="1" zvalue="1313"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="147" MaxPinNum="2"/>
   </metadata>
  <path d="M 512.03 803.86 L 512.03 784.22 L 489 784.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="v6300" d="M 489 882.5 L 489 776.22" stroke-width="1" zvalue="1315"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@0" LinkObjectIDznd="152@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 489 882.5 L 489 776.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="165">
   <path class="v6300" d="M 611.5 740.83 L 611.5 708.89" stroke-width="1" zvalue="1324"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@0" LinkObjectIDznd="15@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 611.5 740.83 L 611.5 708.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="v6300" d="M 583.11 799.61 L 583.11 785.33 L 611.5 785.33" stroke-width="1" zvalue="1326"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="167@0" LinkObjectIDznd="160" MaxPinNum="2"/>
   </metadata>
  <path d="M 583.11 799.61 L 583.11 785.33 L 611.5 785.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="v6300" d="M 634.53 804.97 L 634.53 785.33 L 611.77 785.33" stroke-width="1" zvalue="1327"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="163" MaxPinNum="2"/>
   </metadata>
  <path d="M 634.53 804.97 L 634.53 785.33 L 611.77 785.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="v6300" d="M 611.77 829.67 L 611.77 811.89" stroke-width="1" zvalue="1328"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="164@0" LinkObjectIDznd="160" MaxPinNum="2"/>
   </metadata>
  <path d="M 611.77 829.67 L 611.77 811.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="v6300" d="M 611.5 851.84 L 611.5 777.33" stroke-width="1" zvalue="1329"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@0" LinkObjectIDznd="168@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 611.5 851.84 L 611.5 777.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="v6300" d="M 489 792 L 489 827" stroke-width="1" zvalue="1334"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153" LinkObjectIDznd="153" MaxPinNum="2"/>
   </metadata>
  <path d="M 489 792 L 489 827" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="v6300" d="M 489 822.22 L 489 826.22" stroke-width="1" zvalue="1335"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="148@0" LinkObjectIDznd="172" MaxPinNum="2"/>
   </metadata>
  <path d="M 489 822.22 L 489 826.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="v6300" d="M 1109 754.08 L 1109 708.89" stroke-width="1" zvalue="1341"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@1" LinkObjectIDznd="15@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1109 754.08 L 1109 708.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="v6300" d="M 1109.08 779.97 L 1109.08 805.76" stroke-width="1" zvalue="1342"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="175@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1109.08 779.97 L 1109.08 805.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="181">
   <path class="v6300" d="M 1137.03 731.63 L 1137.03 722 L 1109 722" stroke-width="1" zvalue="1343"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 1137.03 731.63 L 1137.03 722 L 1109 722" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv110" d="M 680.81 488.6 L 550.89 488.6 L 550.89 509.11" stroke-width="1" zvalue="1700"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@3" LinkObjectIDznd="105@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 680.81 488.6 L 550.89 488.6 L 550.89 509.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="v6300" d="M 680.63 633.06 L 680.63 576" stroke-width="1" zvalue="1712"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@0" LinkObjectIDznd="73@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 680.63 633.06 L 680.63 576" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="v6300" d="M 643.28 603.61 L 680.63 603.61" stroke-width="1" zvalue="1713"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="198" MaxPinNum="2"/>
   </metadata>
  <path d="M 643.28 603.61 L 680.63 603.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="200">
   <path class="v6300" d="M 730.03 616.63 L 730.03 605 L 680.63 605" stroke-width="1" zvalue="1714"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@0" LinkObjectIDznd="198" MaxPinNum="2"/>
   </metadata>
  <path d="M 730.03 616.63 L 730.03 605 L 680.63 605" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="27">
   <use class="kv110" height="30" transform="rotate(0,771.932,106.222) scale(6.34921,1.48148) translate(-631.631,-27.3)" width="7" x="749.7098173330401" xlink:href="#ACLineSegment:线路_0" y="83.99999999999994" zvalue="24"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249322946564" ObjectName="110kV撒腊线"/>
   <cge:TPSR_Ref TObjectID="8444249322946564_5066549678309377"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,771.932,106.222) scale(6.34921,1.48148) translate(-631.631,-27.3)" width="7" x="749.7098173330401" y="83.99999999999994"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="1">
   <use class="kv110" height="20" transform="rotate(90,723.611,244.222) scale(1.11111,1.11111) translate(-71.8056,-23.3111)" width="10" x="718.0555556615193" xlink:href="#GroundDisconnector:地刀_0" y="233.1111111111111" zvalue="26"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453693472770" ObjectName="110kV撒腊线16117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453693472770"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,723.611,244.222) scale(1.11111,1.11111) translate(-71.8056,-23.3111)" width="10" x="718.0555556615193" y="233.1111111111111"/></g>
  <g id="8">
   <use class="kv110" height="20" transform="rotate(90,723.611,191.778) scale(1.11111,1.11111) translate(-71.8056,-18.0667)" width="10" x="718.0555556615193" xlink:href="#GroundDisconnector:地刀_0" y="180.6666666666667" zvalue="28"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453693603842" ObjectName="110kV撒腊线16160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453693603842"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,723.611,191.778) scale(1.11111,1.11111) translate(-71.8056,-18.0667)" width="10" x="718.0555556615193" y="180.6666666666667"/></g>
  <g id="32">
   <use class="kv110" height="20" transform="rotate(90,723.611,137.333) scale(1.11111,1.11111) translate(-71.8056,-12.6222)" width="10" x="718.0555555555555" xlink:href="#GroundDisconnector:地刀_0" y="126.2222222222221" zvalue="31"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453693734914" ObjectName="110kV撒腊线16167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453693734914"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,723.611,137.333) scale(1.11111,1.11111) translate(-71.8056,-12.6222)" width="10" x="718.0555555555555" y="126.2222222222221"/></g>
  <g id="91">
   <use class="kv110" height="20" transform="rotate(90,509.333,230.722) scale(1.11111,1.11111) translate(-50.3778,-21.9611)" width="10" x="503.7777845594618" xlink:href="#GroundDisconnector:地刀_0" y="219.6111008326212" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453693931522" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453693931522"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,509.333,230.722) scale(1.11111,1.11111) translate(-50.3778,-21.9611)" width="10" x="503.7777845594618" y="219.6111008326212"/></g>
  <g id="105">
   <use class="kv110" height="40" transform="rotate(0,565.778,522.667) scale(1.11111,-1.11111) translate(-54.3556,-990.844)" width="40" x="543.5555555555555" xlink:href="#GroundDisconnector:中性点地刀12_0" y="500.4444444444445" zvalue="105"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453694193666" ObjectName="#2主变110kV侧1020中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453694193666"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,565.778,522.667) scale(1.11111,-1.11111) translate(-54.3556,-990.844)" width="40" x="543.5555555555555" y="500.4444444444445"/></g>
  <g id="114">
   <use class="kv110" height="20" transform="rotate(90,632.444,346) scale(1.11111,1.11111) translate(-62.6889,-33.4889)" width="10" x="626.888899061415" xlink:href="#GroundDisconnector:地刀_0" y="334.8888888888889" zvalue="114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453694324738" ObjectName="#2主变110kV侧10217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453694324738"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,632.444,346) scale(1.11111,1.11111) translate(-62.6889,-33.4889)" width="10" x="626.888899061415" y="334.8888888888889"/></g>
  <g id="115">
   <use class="kv110" height="20" transform="rotate(90,632.444,390.444) scale(1.11111,1.11111) translate(-62.6889,-37.9333)" width="10" x="626.8888990614151" xlink:href="#GroundDisconnector:地刀_0" y="379.3333333333333" zvalue="116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453694455810" ObjectName="#2主变110kV侧10260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453694455810"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,632.444,390.444) scale(1.11111,1.11111) translate(-62.6889,-37.9333)" width="10" x="626.8888990614151" y="379.3333333333333"/></g>
  <g id="136">
   <use class="kv110" height="40" transform="rotate(0,1044.67,520.699) scale(1.11111,-1.11111) translate(-102.244,-987.107)" width="40" x="1022.444444444444" xlink:href="#GroundDisconnector:中性点地刀12_0" y="498.4771522633745" zvalue="137"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453694586882" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453694586882"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1044.67,520.699) scale(1.11111,-1.11111) translate(-102.244,-987.107)" width="40" x="1022.444444444444" y="498.4771522633745"/></g>
  <g id="36">
   <use class="v6300" height="20" transform="rotate(90,632.444,603.556) scale(1.11111,1.11111) translate(-62.6889,-59.2444)" width="10" x="626.8889057371351" xlink:href="#GroundDisconnector:地刀_0" y="592.4444444444445" zvalue="145"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453694717954" ObjectName="#2主变6.3kV侧60267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453694717954"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,632.444,603.556) scale(1.11111,1.11111) translate(-62.6889,-59.2444)" width="10" x="626.8889057371351" y="592.4444444444445"/></g>
  <g id="340">
   <use class="v6300" height="20" transform="rotate(0,720.585,808.222) scale(1.11111,1.11111) translate(-71.503,-79.7111)" width="10" x="715.0295695852832" xlink:href="#GroundDisconnector:地刀_0" y="797.1111128065322" zvalue="614"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453695045634" ObjectName="#1发电机66167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453695045634"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,720.585,808.222) scale(1.11111,1.11111) translate(-71.503,-79.7111)" width="10" x="715.0295695852832" y="797.1111128065322"/></g>
  <g id="70">
   <use class="kv110" height="20" transform="rotate(90,509.333,274.556) scale(1.11111,1.11111) translate(-50.3778,-26.3444)" width="10" x="503.7777845594617" xlink:href="#GroundDisconnector:地刀_0" y="263.4444444444444" zvalue="1095"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453695242242" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453695242242"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,509.333,274.556) scale(1.11111,1.11111) translate(-50.3778,-26.3444)" width="10" x="503.7777845594617" y="263.4444444444444"/></g>
  <g id="3">
   <use class="kv110" height="20" transform="rotate(90,632.444,445.556) scale(1.11111,1.11111) translate(-62.6889,-43.4444)" width="10" x="626.888899061415" xlink:href="#GroundDisconnector:地刀_0" y="434.4444444444445" zvalue="1100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453695373314" ObjectName="#2主变110kV侧10267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453695373314"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,632.444,445.556) scale(1.11111,1.11111) translate(-62.6889,-43.4444)" width="10" x="626.888899061415" y="434.4444444444445"/></g>
  <g id="583">
   <use class="kv110" height="20" transform="rotate(90,894.111,346.333) scale(1.11111,1.11111) translate(-88.8556,-33.5222)" width="10" x="888.5555555555557" xlink:href="#GroundDisconnector:地刀_0" y="335.2222171359592" zvalue="1114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453695766530" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453695766530"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,894.111,346.333) scale(1.11111,1.11111) translate(-88.8556,-33.5222)" width="10" x="888.5555555555557" y="335.2222171359592"/></g>
  <g id="581">
   <use class="kv110" height="20" transform="rotate(90,894.111,390.778) scale(1.11111,1.11111) translate(-88.8556,-37.9667)" width="10" x="888.5555555555558" xlink:href="#GroundDisconnector:地刀_0" y="379.6666615804036" zvalue="1116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453695635458" ObjectName="#1主变110kV侧10160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453695635458"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,894.111,390.778) scale(1.11111,1.11111) translate(-88.8556,-37.9667)" width="10" x="888.5555555555558" y="379.6666615804036"/></g>
  <g id="87">
   <use class="kv110" height="20" transform="rotate(90,894.111,445.889) scale(1.11111,1.11111) translate(-88.8556,-43.4778)" width="10" x="888.5555555555555" xlink:href="#GroundDisconnector:地刀_0" y="434.7777726915148" zvalue="1122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453695504386" ObjectName="#1主变110kV侧10167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453695504386"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,894.111,445.889) scale(1.11111,1.11111) translate(-88.8556,-43.4778)" width="10" x="888.5555555555555" y="434.7777726915148"/></g>
  <g id="683">
   <use class="kv35" height="20" transform="rotate(90,1446.11,348.667) scale(1.11111,1.11111) translate(-144.056,-33.7556)" width="10" x="1440.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="337.5555453830295" zvalue="1136"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453696290818" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453696290818"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1446.11,348.667) scale(1.11111,1.11111) translate(-144.056,-33.7556)" width="10" x="1440.555555555556" y="337.5555453830295"/></g>
  <g id="682">
   <use class="kv35" height="20" transform="rotate(90,1446.11,393.111) scale(1.11111,1.11111) translate(-144.056,-38.2)" width="10" x="1440.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="381.9999898274739" zvalue="1138"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453696159746" ObjectName="#1主变35kV侧30160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453696159746"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1446.11,393.111) scale(1.11111,1.11111) translate(-144.056,-38.2)" width="10" x="1440.555555555556" y="381.9999898274739"/></g>
  <g id="678">
   <use class="kv35" height="20" transform="rotate(90,1446.11,448.222) scale(1.11111,1.11111) translate(-144.056,-43.7111)" width="10" x="1440.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="437.1111009385851" zvalue="1143"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453696028674" ObjectName="#1主变35kV侧30167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453696028674"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1446.11,448.222) scale(1.11111,1.11111) translate(-144.056,-43.7111)" width="10" x="1440.555555555556" y="437.1111009385851"/></g>
  <g id="757">
   <use class="kv35" height="20" transform="rotate(90,1144.11,240.278) scale(1.11111,1.11111) translate(-113.856,-22.9167)" width="10" x="1138.555555661519" xlink:href="#GroundDisconnector:地刀_0" y="229.1666429307722" zvalue="1159"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453696815106" ObjectName="35kV三四线36117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453696815106"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1144.11,240.278) scale(1.11111,1.11111) translate(-113.856,-22.9167)" width="10" x="1138.555555661519" y="229.1666429307722"/></g>
  <g id="756">
   <use class="kv35" height="20" transform="rotate(90,1144.11,191.519) scale(1.11111,1.11111) translate(-113.856,-18.0408)" width="10" x="1138.555555661519" xlink:href="#GroundDisconnector:地刀_0" y="180.4079355248182" zvalue="1161"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453696684034" ObjectName="35kV三四线36160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453696684034"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1144.11,191.519) scale(1.11111,1.11111) translate(-113.856,-18.0408)" width="10" x="1138.555555661519" y="180.4079355248182"/></g>
  <g id="755">
   <use class="kv35" height="20" transform="rotate(90,1144.11,129.069) scale(1.11111,1.11111) translate(-113.856,-11.7958)" width="10" x="1138.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="117.9580254990436" zvalue="1163"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453696552962" ObjectName="35kV三四线36167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453696552962"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1144.11,129.069) scale(1.11111,1.11111) translate(-113.856,-11.7958)" width="10" x="1138.555555555556" y="117.9580254990436"/></g>
  <g id="847">
   <use class="kv35" height="20" transform="rotate(90,1728.42,229.222) scale(1.11111,1.11111) translate(-172.287,-21.8111)" width="10" x="1722.865296451967" xlink:href="#GroundDisconnector:地刀_0" y="218.1111008326212" zvalue="1248"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453698387970" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453698387970"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1728.42,229.222) scale(1.11111,1.11111) translate(-172.287,-21.8111)" width="10" x="1722.865296451967" y="218.1111008326212"/></g>
  <g id="841">
   <use class="kv35" height="20" transform="rotate(90,1728.42,273.056) scale(1.11111,1.11111) translate(-172.287,-26.1944)" width="10" x="1722.865296451967" xlink:href="#GroundDisconnector:地刀_0" y="261.9444444444444" zvalue="1256"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453698191362" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453698191362"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1728.42,273.056) scale(1.11111,1.11111) translate(-172.287,-26.1944)" width="10" x="1722.865296451967" y="261.9444444444444"/></g>
  <g id="134">
   <use class="v6300" height="20" transform="rotate(0,920.085,808.333) scale(1.11111,1.11111) translate(-91.453,-79.7222)" width="10" x="914.5295695852832" xlink:href="#GroundDisconnector:地刀_0" y="797.2222256130643" zvalue="1279"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453699633154" ObjectName="#2发电机66267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453699633154"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,920.085,808.333) scale(1.11111,1.11111) translate(-91.453,-79.7222)" width="10" x="914.5295695852832" y="797.2222256130643"/></g>
  <g id="151">
   <use class="v6300" height="20" transform="rotate(0,460.556,809.333) scale(1.11111,1.11111) translate(-45.5,-79.8222)" width="10" x="454.9999999999999" xlink:href="#GroundDisconnector:地刀_0" y="798.2222256130643" zvalue="1307"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453699895298" ObjectName="至生活区变66367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453699895298"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,460.556,809.333) scale(1.11111,1.11111) translate(-45.5,-79.8222)" width="10" x="454.9999999999999" y="798.2222256130643"/></g>
  <g id="167">
   <use class="v6300" height="20" transform="rotate(0,583.056,810.444) scale(1.11111,1.11111) translate(-57.75,-79.9333)" width="10" x="577.5" xlink:href="#GroundDisconnector:地刀_0" y="799.3333384195964" zvalue="1321"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453700222978" ObjectName="6.3kV1号站用变66467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453700222978"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,583.056,810.444) scale(1.11111,1.11111) translate(-57.75,-79.9333)" width="10" x="577.5" y="799.3333384195964"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="39">
   <use class="v6300" height="26" transform="rotate(0,730,629) scale(1,1) translate(0,0)" width="12" x="724" xlink:href="#Accessory:避雷器1_0" y="616" zvalue="147"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453694783490" ObjectName="#2主变6.3kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,730,629) scale(1,1) translate(0,0)" width="12" x="724" y="616"/></g>
  <g id="338">
   <use class="v6300" height="26" transform="rotate(0,772.03,815.111) scale(1,1) translate(0,0)" width="12" x="766.0295695852833" xlink:href="#Accessory:避雷器1_0" y="802.1111128065322" zvalue="618"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453694914562" ObjectName="#1发电机避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,772.03,815.111) scale(1,1) translate(0,0)" width="12" x="766.0295695852833" y="802.1111128065322"/></g>
  <g id="331">
   <use class="v6300" height="22" transform="rotate(0,749.03,830.111) scale(1,1) translate(0,0)" width="12" x="743.0295695852833" xlink:href="#Accessory:传输线_0" y="819.1111128065322" zvalue="627"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453694849026" ObjectName="#1发电机电缆1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,749.03,830.111) scale(1,1) translate(0,0)" width="12" x="743.0295695852833" y="819.1111128065322"/></g>
  <g id="68">
   <use class="kv110" height="35" transform="rotate(0,548.412,195.25) scale(1.3,1.3) translate(-120.557,-39.8077)" width="40" x="522.4124881074952" xlink:href="#Accessory:5绕组母线PT带避雷器_0" y="172.5" zvalue="1093"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453695111170" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,548.412,195.25) scale(1.3,1.3) translate(-120.557,-39.8077)" width="40" x="522.4124881074952" y="172.5"/></g>
  <g id="765">
   <use class="kv35" height="40" transform="rotate(0,1227.35,107) scale(1,-1) translate(0,-214)" width="40" x="1207.346558321262" xlink:href="#Accessory:线路PT三绕组_0" y="87" zvalue="1173"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453697077250" ObjectName="35kV三四线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1227.35,107) scale(1,-1) translate(0,-214)" width="40" x="1207.346558321262" y="87"/></g>
  <g id="842">
   <use class="kv35" height="35" transform="rotate(0,1767.5,193.75) scale(1.3,1.3) translate(-401.885,-39.4615)" width="40" x="1741.5" xlink:href="#Accessory:5绕组母线PT带避雷器_0" y="171" zvalue="1254"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453698256898" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1767.5,193.75) scale(1.3,1.3) translate(-401.885,-39.4615)" width="40" x="1741.5" y="171"/></g>
  <g id="4">
   <use class="v6300" height="35" transform="rotate(0,687,929) scale(1,1) translate(0,0)" width="25" x="674.5" xlink:href="#Accessory:PT232_0" y="911.5" zvalue="1262"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453698584578" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,687,929) scale(1,1) translate(0,0)" width="25" x="674.5" y="911.5"/></g>
  <g id="41">
   <use class="v6300" height="30" transform="rotate(0,798,931.5) scale(1,1) translate(0,0)" width="30" x="783" xlink:href="#Accessory:PT2525_0" y="916.5" zvalue="1268"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453698781186" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,798,931.5) scale(1,1) translate(0,0)" width="30" x="783" y="916.5"/></g>
  <g id="45">
   <use class="v6300" height="22" transform="rotate(0,852,900) scale(1,1) translate(0,0)" width="12" x="846" xlink:href="#Accessory:传输线_0" y="889" zvalue="1272"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453698846722" ObjectName="#1发电机电缆2"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,852,900) scale(1,1) translate(0,0)" width="12" x="846" y="889"/></g>
  <g id="46">
   <use class="v6300" height="23" transform="rotate(0,852,935) scale(1,1) translate(0,0)" width="15" x="844.5" xlink:href="#Accessory:PT1515_0" y="923.5" zvalue="1273"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453698912258" ObjectName="#1发电机PT3"/>
   </metadata>
  <rect fill="white" height="23" opacity="0" stroke="white" transform="rotate(0,852,935) scale(1,1) translate(0,0)" width="15" x="844.5" y="923.5"/></g>
  <g id="133">
   <use class="v6300" height="26" transform="rotate(0,971.53,815.222) scale(1,1) translate(0,0)" width="12" x="965.5295695852833" xlink:href="#Accessory:避雷器1_0" y="802.2222256130643" zvalue="1281"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453699502082" ObjectName="#2发电机避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,971.53,815.222) scale(1,1) translate(0,0)" width="12" x="965.5295695852833" y="802.2222256130643"/></g>
  <g id="131">
   <use class="v6300" height="22" transform="rotate(0,948.53,830.222) scale(1,1) translate(0,0)" width="12" x="942.5295695852833" xlink:href="#Accessory:传输线_0" y="819.2222256130643" zvalue="1283"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453699436546" ObjectName="#2发电机电缆1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,948.53,830.222) scale(1,1) translate(0,0)" width="12" x="942.5295695852833" y="819.2222256130643"/></g>
  <g id="125">
   <use class="v6300" height="35" transform="rotate(0,886.5,929.111) scale(1,1) translate(0,0)" width="25" x="874" xlink:href="#Accessory:PT232_0" y="911.6111128065321" zvalue="1290"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453699305474" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,886.5,929.111) scale(1,1) translate(0,0)" width="25" x="874" y="911.6111128065321"/></g>
  <g id="112">
   <use class="v6300" height="30" transform="rotate(0,997.5,931.611) scale(1,1) translate(0,0)" width="30" x="982.5" xlink:href="#Accessory:PT2525_0" y="916.6111128065321" zvalue="1297"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453699108866" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,997.5,931.611) scale(1,1) translate(0,0)" width="30" x="982.5" y="916.6111128065321"/></g>
  <g id="107">
   <use class="v6300" height="22" transform="rotate(0,1051.5,900.111) scale(1,1) translate(0,0)" width="12" x="1045.5" xlink:href="#Accessory:传输线_0" y="889.1111128065321" zvalue="1300"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453699043330" ObjectName="#2发电机电缆2"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1051.5,900.111) scale(1,1) translate(0,0)" width="12" x="1045.5" y="889.1111128065321"/></g>
  <g id="94">
   <use class="v6300" height="23" transform="rotate(0,1051.5,935.111) scale(1,1) translate(0,0)" width="15" x="1044" xlink:href="#Accessory:PT1515_0" y="923.6111128065321" zvalue="1301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453698977794" ObjectName="#2发电机PT3"/>
   </metadata>
  <rect fill="white" height="23" opacity="0" stroke="white" transform="rotate(0,1051.5,935.111) scale(1,1) translate(0,0)" width="15" x="1044" y="923.6111128065321"/></g>
  <g id="150">
   <use class="v6300" height="26" transform="rotate(0,512,816.222) scale(1,1) translate(0,0)" width="12" x="506.0000000000001" xlink:href="#Accessory:避雷器1_0" y="803.2222256130643" zvalue="1309"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453699764226" ObjectName="至生活区变避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,512,816.222) scale(1,1) translate(0,0)" width="12" x="506.0000000000001" y="803.2222256130643"/></g>
  <g id="148">
   <use class="v6300" height="22" transform="rotate(0,489,832.222) scale(1,1) translate(0,0)" width="12" x="483.0000000000001" xlink:href="#Accessory:传输线_0" y="821.2222256130643" zvalue="1311"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453699698690" ObjectName="至生活区变电缆1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,489,832.222) scale(1,1) translate(0,0)" width="12" x="483.0000000000001" y="821.2222256130643"/></g>
  <g id="166">
   <use class="v6300" height="26" transform="rotate(0,634.5,817.333) scale(1,1) translate(0,0)" width="12" x="628.5000000000001" xlink:href="#Accessory:避雷器1_0" y="804.3333384195964" zvalue="1323"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453700091906" ObjectName="6.3kV1号站用变避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,634.5,817.333) scale(1,1) translate(0,0)" width="12" x="628.5000000000001" y="804.3333384195964"/></g>
  <g id="164">
   <use class="v6300" height="22" transform="rotate(0,611.769,839.523) scale(0.984848,0.984848) translate(9.32107,12.7493)" width="12" x="605.8601412473025" xlink:href="#Accessory:传输线_0" y="828.6894179615144" zvalue="1325"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453700026370" ObjectName="6.3kV1号站用变电缆1"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,611.769,839.523) scale(0.984848,0.984848) translate(9.32107,12.7493)" width="12" x="605.8601412473025" y="828.6894179615144"/></g>
  <g id="175">
   <use class="v6300" height="30" transform="rotate(0,1109,828) scale(1.6,1.6) translate(-406.875,-301.5)" width="30" x="1085" xlink:href="#Accessory:带熔断器35kVPT11_0" y="804" zvalue="1338"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453700419586" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1109,828) scale(1.6,1.6) translate(-406.875,-301.5)" width="30" x="1085" y="804"/></g>
  <g id="178">
   <use class="v6300" height="26" transform="rotate(0,1137,744) scale(1,1) translate(0,0)" width="12" x="1131" xlink:href="#Accessory:避雷器1_0" y="731" zvalue="1340"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453700485122" ObjectName="6.3kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1137,744) scale(1,1) translate(0,0)" width="12" x="1131" y="731"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="657">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="657" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,771.932,11.5) scale(1,1) translate(0,0)" writing-mode="lr" x="772.13" xml:space="preserve" y="16.41" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132684345348" ObjectName="P"/>
   </metadata>
  </g>
  <g id="659">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="659" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,771.932,26.5) scale(1,1) translate(0,0)" writing-mode="lr" x="772.13" xml:space="preserve" y="31.41" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132684410884" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="661">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="661" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,771.932,41.5) scale(1,1) translate(0,0)" writing-mode="lr" x="772.13" xml:space="preserve" y="46.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132684476420" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="663">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="663" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,771.932,53.9445) scale(1,1) translate(0,0)" writing-mode="lr" x="772.13" xml:space="preserve" y="58.85" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132685131780" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="690">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="690" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,550.222,59.1667) scale(1,1) translate(0,0)" writing-mode="lr" x="550.42" xml:space="preserve" y="64.08" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132682248196" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="692">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="692" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1767.06,59.1667) scale(1,1) translate(0,0)" writing-mode="lr" x="1767.25" xml:space="preserve" y="64.08" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132682772484" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="694">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="694" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1107.13,903.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1107.33" xml:space="preserve" y="908.3" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132683296771" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="695">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="695" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,550.222,76.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="550.42" xml:space="preserve" y="81.58" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132682313732" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="697">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="697" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1767.06,76.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="1767.25" xml:space="preserve" y="81.58" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132682838020" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="699">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="699" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1107.13,920.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1107.33" xml:space="preserve" y="925.3" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132683362308" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="700">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="700" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,550.222,94.1667) scale(1,1) translate(0,0)" writing-mode="lr" x="550.42" xml:space="preserve" y="99.08" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132682379268" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="702">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="702" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1767.06,94.1667) scale(1,1) translate(0,0)" writing-mode="lr" x="1767.25" xml:space="preserve" y="99.08" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132682903556" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="704">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="704" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1107.13,937.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1107.33" xml:space="preserve" y="942.3" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132683427844" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="705">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="705" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,550.222,111.667) scale(1,1) translate(0,0)" writing-mode="lr" x="550.42" xml:space="preserve" y="116.58" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132682510340" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="707">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="707" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1767.06,111.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1767.25" xml:space="preserve" y="116.58" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132683034628" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="709">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="709" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1107.13,954.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1107.33" xml:space="preserve" y="959.3" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132683558916" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="710">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="710" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,550.222,129.167) scale(1,1) translate(0,0)" writing-mode="lr" x="550.42" xml:space="preserve" y="134.08" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132682706948" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="712">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="712" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1767.06,129.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1767.25" xml:space="preserve" y="134.08" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132683231235" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="714">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="714" prefix="U0:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1107.13,971.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1107.33" xml:space="preserve" y="976.3" zvalue="1">U0:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132683755524" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="216">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="216" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,748.5,992) scale(1,1) translate(0,0)" writing-mode="lr" x="748.74" xml:space="preserve" y="998.38" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132696993795" ObjectName="P"/>
   </metadata>
  </g>
  <g id="217">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="217" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,949,993) scale(1,1) translate(0,0)" writing-mode="lr" x="949.24" xml:space="preserve" y="999.38" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132697518084" ObjectName="P"/>
   </metadata>
  </g>
  <g id="218">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="218" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,749.5,1012) scale(1,1) translate(0,0)" writing-mode="lr" x="749.74" xml:space="preserve" y="1018.38" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132697059331" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="219">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="219" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,949,1012) scale(1,1) translate(0,0)" writing-mode="lr" x="949.24" xml:space="preserve" y="1018.38" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132697583620" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="220">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="220" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,749.5,1032.25) scale(1,1) translate(0,0)" writing-mode="lr" x="749.74" xml:space="preserve" y="1038.63" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132697124867" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="221">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="221" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,949,1032.25) scale(1,1) translate(0,-2.95153e-12)" writing-mode="lr" x="949.24" xml:space="preserve" y="1038.63" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132697649156" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="222">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="222" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1192.43,11.5) scale(1,1) translate(3.82172e-13,0)" writing-mode="lr" x="1192.63" xml:space="preserve" y="16.41" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132691357699" ObjectName="P"/>
   </metadata>
  </g>
  <g id="223">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="223" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1192.43,26.5) scale(1,1) translate(3.82172e-13,0)" writing-mode="lr" x="1192.63" xml:space="preserve" y="31.41" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132691423235" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="224">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="224" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1192.43,41.5) scale(1,1) translate(3.82172e-13,0)" writing-mode="lr" x="1192.63" xml:space="preserve" y="46.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132691488771" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="225">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="225" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1192.43,53.9445) scale(1,1) translate(3.82172e-13,0)" writing-mode="lr" x="1192.63" xml:space="preserve" y="58.85" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132692144131" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="143" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,500.747,380.5) scale(1,1) translate(0,0)" writing-mode="lr" x="500.99" xml:space="preserve" y="386.99" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132686901251" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="145" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,501.747,406.5) scale(1,1) translate(0,0)" writing-mode="lr" x="501.99" xml:space="preserve" y="412.99" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132686966787" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="156">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="156" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,501.747,433.5) scale(1,1) translate(0,0)" writing-mode="lr" x="501.99" xml:space="preserve" y="439.99" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132687163395" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="159">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="159" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,503.747,456.5) scale(1,1) translate(0,0)" writing-mode="lr" x="503.97" xml:space="preserve" y="462.95" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132687949827" ObjectName="HCos"/>
   </metadata>
  </g>
  <g id="171">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="171" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1052.4,344.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1052.64" xml:space="preserve" y="350.99" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132688605187" ObjectName="P"/>
   </metadata>
  </g>
  <g id="182">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="182" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1052.4,371.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1052.64" xml:space="preserve" y="377.99" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132688670723" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="183">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="183" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1052.4,398.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1052.64" xml:space="preserve" y="404.99" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132688867331" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="193">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="193" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1053.4,423.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1053.62" xml:space="preserve" y="429.95" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132689653763" ObjectName="HCos"/>
   </metadata>
  </g>
  <g id="228">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="228" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1592.4,359.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1592.64" xml:space="preserve" y="365.99" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132688736259" ObjectName="P"/>
   </metadata>
  </g>
  <g id="229">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="229" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1592.4,386.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1592.64" xml:space="preserve" y="392.99" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132688801795" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="230">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="230" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1592.4,413.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1592.64" xml:space="preserve" y="419.99" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132689195012" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="243">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="243" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.611,332.167) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="337.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132682641412" ObjectName="F"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,155.611,260.167) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="265.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132704464899" ObjectName="F"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,333.222,261.167) scale(1,1) translate(0,0)" writing-mode="lr" x="333.38" xml:space="preserve" y="266.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132704530436" ObjectName="F"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="246" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,333.222,333.167) scale(1,1) translate(0,0)" writing-mode="lr" x="333.38" xml:space="preserve" y="338.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132683165699" ObjectName="F"/>
   </metadata>
  </g>
  <g id="247">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="247" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.611,355.389) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="360.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132683689988" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,155.611,285.167) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="290.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132704333827" ObjectName="F"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,333.222,286.167) scale(1,1) translate(0,0)" writing-mode="lr" x="333.38" xml:space="preserve" y="291.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132704399363" ObjectName="F"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.611,377.389) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="382.3" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="232">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="232" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,333.611,377.389) scale(1,1) translate(0,0)" writing-mode="lr" x="333.77" xml:space="preserve" y="382.3" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="236">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.611,400.389) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="405.3" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="238">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="238" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,333.611,400.389) scale(1,1) translate(0,0)" writing-mode="lr" x="333.77" xml:space="preserve" y="405.3" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,155.611,309.167) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="314.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127179059207" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,335.611,308.167) scale(1,1) translate(0,0)" writing-mode="lr" x="335.77" xml:space="preserve" y="313.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127178993669" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="63" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,597.164,952.5) scale(1,1) translate(6.00994e-14,0)" writing-mode="lr" x="596.58" xml:space="preserve" y="958.21" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132700401667" ObjectName="P"/>
   </metadata>
  </g>
  <g id="241">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="241" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,599.828,974.5) scale(1,1) translate(6.03952e-14,0)" writing-mode="lr" x="599.25" xml:space="preserve" y="980.21" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132700467203" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="242">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="242" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,599.828,995.5) scale(1,1) translate(6.03952e-14,0)" writing-mode="lr" x="599.25" xml:space="preserve" y="1001.21" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481132700532739" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="113">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="113" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,797.247,669.5) scale(1,1) translate(0,0)" writing-mode="lr" x="744.0599999999999" xml:space="preserve" y="673.9" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128257454084" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="187">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="187" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,797.247,595.5) scale(1,1) translate(0,0)" writing-mode="lr" x="744.0599999999999" xml:space="preserve" y="599.9" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128257716228" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="188">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="188" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,797.247,632.5) scale(1,1) translate(0,0)" writing-mode="lr" x="744.0599999999999" xml:space="preserve" y="636.9" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128257781764" ObjectName="LQ"/>
   </metadata>
  </g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="PowerTransformer3Class">
  <g id="73">
   <g id="730">
    <use class="kv110" height="50" transform="rotate(0,701.247,519) scale(2.07882,2.28) translate(-336.948,-259.368)" width="50" x="649.28" xlink:href="#PowerTransformer3:中压侧无中性点_0" y="462" zvalue="1097"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874562469890" ObjectName="110"/>
    </metadata>
   </g>
   <g id="731">
    <use class="kv35" height="50" transform="rotate(0,701.247,519) scale(2.07882,2.28) translate(-336.948,-259.368)" width="50" x="649.28" xlink:href="#PowerTransformer3:中压侧无中性点_1" y="462" zvalue="1097"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874467377155" ObjectName="35"/>
    </metadata>
   </g>
   <g id="732">
    <use class="v6300" height="50" transform="rotate(0,701.247,519) scale(2.07882,2.28) translate(-336.948,-259.368)" width="50" x="649.28" xlink:href="#PowerTransformer3:中压侧无中性点_2" y="462" zvalue="1097"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874467442691" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399522451458" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399522451458"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,701.247,519) scale(2.07882,2.28) translate(-336.948,-259.368)" width="50" x="649.28" y="462"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="77">
   <g id="770">
    <use class="kv110" height="50" transform="rotate(0,1184.9,528.5) scale(2.06,2.06) translate(-593.806,-245.447)" width="30" x="1154" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="477" zvalue="1103"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874562600962" ObjectName="110"/>
    </metadata>
   </g>
   <g id="771">
    <use class="kv35" height="50" transform="rotate(0,1184.9,528.5) scale(2.06,2.06) translate(-593.806,-245.447)" width="30" x="1154" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="477" zvalue="1103"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874562666498" ObjectName="35"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399522516994" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399522516994"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1184.9,528.5) scale(2.06,2.06) translate(-593.806,-245.447)" width="30" x="1154" y="477"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="833">
   <use class="kv35" height="30" transform="rotate(0,1629,106.056) scale(1,1) translate(0,0)" width="12" x="1623" xlink:href="#EnergyConsumer:负荷_0" y="91.0555419921875" zvalue="1243"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453698060290" ObjectName="35kV大盈江四级施工电源线"/>
   <cge:TPSR_Ref TObjectID="6192453698060290"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1629,106.056) scale(1,1) translate(0,0)" width="12" x="1623" y="91.0555419921875"/></g>
  <g id="154">
   <use class="v6300" height="30" transform="rotate(0,489,896) scale(1,-1) translate(0,-1792)" width="12" x="483" xlink:href="#EnergyConsumer:负荷_0" y="881" zvalue="1317"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453699960834" ObjectName="至生活区变"/>
   <cge:TPSR_Ref TObjectID="6192453699960834"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,489,896) scale(1,-1) translate(0,-1792)" width="12" x="483" y="881"/></g>
  <g id="169">
   <use class="v6300" height="30" transform="rotate(0,611.328,884.25) scale(1.87623,2.25) translate(-273.233,-472.5)" width="28" x="585.060394940871" xlink:href="#EnergyConsumer:站用变YD2022_0" y="850.5" zvalue="1332"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453700288514" ObjectName="6.3kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,611.328,884.25) scale(1.87623,2.25) translate(-273.233,-472.5)" width="28" x="585.060394940871" y="850.5"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v6300" height="30" transform="rotate(0,749,924) scale(1.5,1.5) translate(-242.167,-300.5)" width="30" x="726.5" xlink:href="#Generator:发电机_0" y="901.5" zvalue="1260"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453698519042" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192453698519042"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,749,924) scale(1.5,1.5) translate(-242.167,-300.5)" width="30" x="726.5" y="901.5"/></g>
  <g id="126">
   <use class="v6300" height="30" transform="rotate(0,948.5,924.111) scale(1.5,1.5) translate(-308.667,-300.537)" width="30" x="926" xlink:href="#Generator:发电机_0" y="901.6111128065321" zvalue="1288"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453699371010" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192453699371010"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,948.5,924.111) scale(1.5,1.5) translate(-308.667,-300.537)" width="30" x="926" y="901.6111128065321"/></g>
 </g>
 <g id="StateClass">
  <g id="52">
   <use height="30" transform="rotate(0,330.673,186.357) scale(0.708333,0.665547) translate(131.784,88.6322)" width="30" x="320.05" xlink:href="#State:红绿圆(方形)_0" y="176.37" zvalue="1429"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374921236481" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,330.673,186.357) scale(0.708333,0.665547) translate(131.784,88.6322)" width="30" x="320.05" y="176.37"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,235.048,186.357) scale(0.708333,0.665547) translate(92.4093,88.6322)" width="30" x="224.42" xlink:href="#State:红绿圆(方形)_0" y="176.37" zvalue="1430"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562958555545604" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,235.048,186.357) scale(0.708333,0.665547) translate(92.4093,88.6322)" width="30" x="224.42" y="176.37"/></g>
 </g>
</svg>