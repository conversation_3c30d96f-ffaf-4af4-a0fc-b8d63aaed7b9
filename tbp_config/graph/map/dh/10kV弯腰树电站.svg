<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549586690050" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.45181617405996" x2="1.714850492606708" y1="5.276370517142858" y2="14.05696281619048"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.583333333333333" x2="8.583333333333334" y1="5.166666666666667" y2="14.16666666666667"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:线路PT三绕组_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="20" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="24" y2="24"/>
   <rect fill-opacity="0" height="10" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20,11) scale(1,1) translate(0,0)" width="6" x="17" y="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32" x2="32" y1="17" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.98040845230975" x2="31.98040845230975" y1="16.76788570496156" y2="23.53635804601289"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.98821300514558" x2="19.98821300514558" y1="1.029523490692871" y2="18.75"/>
   <path d="M 30.0147 23.4481 L 34.2865 23.4481 L 32.115 28.6155 L 30.0147 23.4481" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <rect fill-opacity="0" height="6.05" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,32.04,24.56) scale(1,1) translate(0,0)" width="15.34" x="24.37" y="21.53"/>
   <ellipse cx="19.76" cy="32.23" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.76" cy="24.17" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.22179295459144" x2="32.22179295459144" y1="32.22550978083666" y2="36.2625308385742"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.01788570496155" x2="35.42570020422134" y1="36.23023467011234" y2="36.23023467011234"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.26384963537316" x2="34.53572596821302" y1="37.52208140858838" y2="37.52208140858838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.15382387138148" x2="33.11176719059974" y1="38.49096646244535" y2="38.49096646244535"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.91666666666667" x2="22.91666666666667" y1="32.66666666666667" y2="32.66666666666667"/>
   <ellipse cx="12.01" cy="28.48" fill-opacity="0" rx="4.98" ry="5.49" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.166666666666664" x2="15.16666666666666" y1="28.91666666666667" y2="28.91666666666667"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸_0" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6.000733890582183" xlink:href="#terminal" y="1.25365326133502"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="16.81276518533035"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.083333333333333" x2="6.083333333333333" y1="10.08333333333333" y2="1.083333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="16.81354468322919" y2="9.527777777777771"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.083333333333333" x2="0.8263923524522143" y1="16.81435756744399" y2="9.527777777777771"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.083333333333332" x2="0.8000000000000016" y1="9.9106081390032" y2="2.616666666666669"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.097965249199815" x2="11.38333333333333" y1="9.903292181069959" y2="2.61666666666667"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸_1" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6.000733890582183" xlink:href="#terminal" y="1.25365326133502"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="16.81276518533035"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="16.81354468322919" y2="9.527777777777771"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.083333333333333" x2="0.8263923524522143" y1="16.81435756744399" y2="9.527777777777771"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6" x2="6" y1="0.9999999999999982" y2="16.80704160951075"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸_2" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6.000733890582183" xlink:href="#terminal" y="1.25365326133502"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="16.81276518533035"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.658956967941879" x2="2.492290301275209" y1="5.063824112178023" y2="13.23049077884469"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.325623634608544" x2="9.825623634608544" y1="5.313824112178023" y2="12.98049077884469"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-Y_0" viewBox="0,0,30,50">
   <ellipse cx="14.99" cy="14.99" fill-opacity="0" rx="14.76" ry="14.76" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.99138374485597" x2="10.08333333333333" y1="11.06149193548387" y2="16.08333333333334"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01984739368999" x2="20" y1="11.06354954865259" y2="16"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00235768175583" x2="15.00235768175583" y1="6.416666666666664" y2="11.06149193548386"/>
   <use terminal-index="0" type="1" x="14.99138374485597" xlink:href="#terminal" y="0.2276829173857209"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-Y_1" viewBox="0,0,30,50">
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00235768175583" x2="15.00235768175583" y1="32" y2="37.04670698924731"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00546553497943" x2="20" y1="37.04651063100137" y2="41.75"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00235768175583" x2="10" y1="37.04670698924731" y2="41.91666666666666"/>
   <ellipse cx="15.01" cy="35.25" fill-opacity="0" rx="14.76" ry="14.76" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="1" x="15.01333161865569" xlink:href="#terminal" y="50.00705645161292"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV弯腰树电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="43.75" xlink:href="logo.png" y="45.25"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,178.375,75.25) scale(1,1) translate(0,0)" writing-mode="lr" x="178.37" xml:space="preserve" y="78.75" zvalue="54"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,180.083,74.9403) scale(1,1) translate(6.85563e-15,0)" writing-mode="lr" x="180.08" xml:space="preserve" y="83.94" zvalue="55">10kV弯腰树电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="30" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,73.4375,320) scale(1,1) translate(0,0)" width="72.88" x="37" y="308" zvalue="100"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.4375,320) scale(1,1) translate(0,0)" writing-mode="lr" x="73.44" xml:space="preserve" y="324.5" zvalue="100">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,427.679,704.179) scale(1,1) translate(0,0)" writing-mode="lr" x="427.68" xml:space="preserve" y="708.6799999999999" zvalue="2">0.4kV Ⅰ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1881.43,702.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1881.43" xml:space="preserve" y="707.21" zvalue="3">0.4kV Ⅱ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,615.162,858.994) scale(1,1) translate(0,0)" writing-mode="lr" x="615.16" xml:space="preserve" y="863.49" zvalue="6">451</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,611.039,781.793) scale(1,1) translate(1.34536e-13,1.37453e-12)" writing-mode="lr" x="611.04" xml:space="preserve" y="786.29" zvalue="8">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" x="595.375" xml:space="preserve" y="990.957590511867" zvalue="12">#1发电机        </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="595.375" xml:space="preserve" y="1006.957590511867" zvalue="12">500KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1509.28,857.721) scale(1,1) translate(0,0)" writing-mode="lr" x="1509.28" xml:space="preserve" y="862.22" zvalue="15">452</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1505.15,780.52) scale(1,1) translate(0,-1.37227e-12)" writing-mode="lr" x="1505.15" xml:space="preserve" y="785.02" zvalue="17">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" x="1491.421875" xml:space="preserve" y="986.8127041482306" zvalue="21">#2发电机             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1491.421875" xml:space="preserve" y="1002.812704148231" zvalue="21">125KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,802.787,754.543) scale(1,1) translate(0,0)" writing-mode="lr" x="802.79" xml:space="preserve" y="759.04" zvalue="29">4901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" x="528.9375" xml:space="preserve" y="588.4090909090909" zvalue="36">#1主变              </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="528.9375" xml:space="preserve" y="604.4090909090909" zvalue="36">630KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,602.093,501.01) scale(1,1) translate(0,1.0947e-13)" writing-mode="lr" x="602.09" xml:space="preserve" y="505.51" zvalue="39">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,599.818,414.871) scale(1,1) translate(0,0)" writing-mode="lr" x="599.8200000000001" xml:space="preserve" y="419.37" zvalue="42">0516</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,581.436,130.182) scale(1,1) translate(0,0)" writing-mode="lr" x="581.4400000000001" xml:space="preserve" y="134.68" zvalue="47">10kV州水泥厂线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" x="1506.0078125" xml:space="preserve" y="574.9090909090909" zvalue="49">#2主变           </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1506.0078125" xml:space="preserve" y="590.9090909090909" zvalue="49">160KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1433.18,383.909) scale(1,1) translate(0,0)" writing-mode="lr" x="1433.18" xml:space="preserve" y="388.41" zvalue="51">0526</text>
  <line fill="none" id="74" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.75" x2="377.75" y1="13.25" y2="1043.25" zvalue="56"/>
  <line fill="none" id="72" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.750000000000227" x2="370.7499999999998" y1="149.1204926140824" y2="149.1204926140824" zvalue="58"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="161.2500000000001" y2="161.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="187.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="4.75" y1="161.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="161.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="161.2500000000001" y2="161.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="187.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="161.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.75" x2="366.75" y1="161.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="187.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="211.5000000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="4.75" y1="187.2500000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="187.2500000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="187.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="211.5000000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="187.2500000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.75" x2="366.75" y1="187.2500000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="211.5000000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="234.2500000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="4.75" y1="211.5000000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="211.5000000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="211.5000000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="234.2500000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="211.5000000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.75" x2="366.75" y1="211.5000000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="234.2500000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="257.0000000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="4.75" y1="234.2500000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="234.2500000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="234.2500000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="257.0000000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="234.2500000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.75" x2="366.75" y1="234.2500000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="257.0000000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="279.7500000000001" y2="279.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="4.75" y1="257.0000000000001" y2="279.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="257.0000000000001" y2="279.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="257.0000000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="279.7500000000001" y2="279.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="257.0000000000001" y2="279.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.75" x2="366.75" y1="257.0000000000001" y2="279.7500000000001"/>
  <line fill="none" id="70" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.750000000000227" x2="370.7499999999998" y1="619.1204926140825" y2="619.1204926140825" zvalue="60"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="934.2500000000002" y2="934.2500000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="973.4133000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="934.2500000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="934.2500000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="363.75" y1="934.2500000000002" y2="934.2500000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="363.75" y1="973.4133000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="934.2500000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="934.2500000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="973.4132700000002" y2="973.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1001.33167" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="973.4132700000002" y2="973.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1001.33167" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.75" x2="183.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="973.4132700000002" y2="973.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1001.33167" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="183.7500000000001" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.7500000000001" x2="273.7500000000001" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="973.4132700000002" y2="973.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1001.33167" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="273.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1001.3316" y2="1001.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1029.25" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1001.3316" y2="1001.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1029.25" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.75" x2="183.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1001.3316" y2="1001.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1029.25" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="183.7500000000001" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.7500000000001" x2="273.7500000000001" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1001.3316" y2="1001.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1029.25" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="273.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="1001.3316" y2="1029.25"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.75,954.25) scale(1,1) translate(0,0)" writing-mode="lr" x="48.75" xml:space="preserve" y="960.25" zvalue="64">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.75,988.25) scale(1,1) translate(0,0)" writing-mode="lr" x="45.75" xml:space="preserve" y="994.25" zvalue="65">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.75,988.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.75" xml:space="preserve" y="994.25" zvalue="66">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.75,1016.25) scale(1,1) translate(0,0)" writing-mode="lr" x="44.75" xml:space="preserve" y="1022.25" zvalue="67">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.75,1016.25) scale(1,1) translate(0,0)" writing-mode="lr" x="226.75" xml:space="preserve" y="1022.25" zvalue="68">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.25,648.75) scale(1,1) translate(0,2.09749e-13)" writing-mode="lr" x="69.25" xml:space="preserve" y="653.2500000000001" zvalue="71">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.804,956.25) scale(1,1) translate(0,0)" writing-mode="lr" x="228.8" xml:space="preserve" y="962.25" zvalue="79">WanYaoShu-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,138.804,1016.25) scale(1,1) translate(0,0)" writing-mode="lr" x="138.8" xml:space="preserve" y="1022.25" zvalue="80">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,42.75,175.25) scale(1,1) translate(0,0)" writing-mode="lr" x="42.75" xml:space="preserve" y="180.75" zvalue="82">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.75,175.25) scale(1,1) translate(0,0)" writing-mode="lr" x="222.75" xml:space="preserve" y="180.75" zvalue="83">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,61.2917,197.583) scale(1,1) translate(0,0)" writing-mode="lr" x="61.29" xml:space="preserve" y="202.08" zvalue="84">0.4kVI段母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.9375,247.25) scale(1,1) translate(0,0)" writing-mode="lr" x="49.94" xml:space="preserve" y="251.75" zvalue="85">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,245.25,200) scale(1,1) translate(0,0)" writing-mode="lr" x="245.25" xml:space="preserve" y="204.5" zvalue="87">0.4kVII段母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.667,245.5) scale(1,1) translate(0,0)" writing-mode="lr" x="227.67" xml:space="preserve" y="250" zvalue="91">10kV#2变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,189.211,320.591) scale(1,1) translate(0,0)" writing-mode="lr" x="189.21" xml:space="preserve" y="325.09" zvalue="96">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,294.211,320.591) scale(1,1) translate(0,0)" writing-mode="lr" x="294.21" xml:space="preserve" y="325.09" zvalue="97">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="10kV弯腰树电站.svg"><rect fill-opacity="0" height="24" width="72.88" x="37" y="308" zvalue="100"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="1">
   <path class="v400" d="M 473 708 L 1035.86 708" stroke-width="6" zvalue="1"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674243510276" ObjectName="0.4kV I段母线"/>
   <cge:TPSR_Ref TObjectID="9288674243510276"/></metadata>
  <path d="M 473 708 L 1035.86 708" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="v400" d="M 1209.43 703.71 L 1822.29 703.71" stroke-width="6" zvalue="2"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674243575812" ObjectName="0.4kVII段母线"/>
   <cge:TPSR_Ref TObjectID="9288674243575812"/></metadata>
  <path d="M 1209.43 703.71 L 1822.29 703.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="49">
   <use class="v400" height="20" transform="rotate(0,595.321,859.994) scale(1.75,2.88514) translate(-251.388,-543.065)" width="10" x="586.5714285714288" xlink:href="#Breaker:手车开关_0" y="831.1428571428571" zvalue="5"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924511268869" ObjectName="#1发电机451断路器"/>
   <cge:TPSR_Ref TObjectID="6473924511268869"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,595.321,859.994) scale(1.75,2.88514) translate(-251.388,-543.065)" width="10" x="586.5714285714288" y="831.1428571428571"/></g>
  <g id="19">
   <use class="v400" height="20" transform="rotate(0,1489.44,858.721) scale(1.75,2.88514) translate(-634.579,-542.234)" width="10" x="1480.685064935065" xlink:href="#Breaker:手车开关_0" y="829.8701298701299" zvalue="14"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924511334405" ObjectName="#2发电机452断路器"/>
   <cge:TPSR_Ref TObjectID="6473924511334405"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1489.44,858.721) scale(1.75,2.88514) translate(-634.579,-542.234)" width="10" x="1480.685064935065" y="829.8701298701299"/></g>
  <g id="56">
   <use class="kv10" height="20" transform="rotate(0,578.593,498.566) scale(1.22222,1.11111) translate(-104.088,-48.7455)" width="10" x="572.4822147399924" xlink:href="#Breaker:开关_0" y="487.4545454545454" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924511399941" ObjectName="#1主变10kV侧051断路器"/>
   <cge:TPSR_Ref TObjectID="6473924511399941"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,578.593,498.566) scale(1.22222,1.11111) translate(-104.088,-48.7455)" width="10" x="572.4822147399924" y="487.4545454545454"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="165">
   <use class="v400" height="30" transform="rotate(0,596.433,782.793) scale(1.9625,1.2338) translate(-285.299,-144.827)" width="15" x="581.7142857142858" xlink:href="#Disconnector:刀闸_0" y="764.2857142857142" zvalue="7"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449798406150" ObjectName="#1发电机4511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449798406150"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,596.433,782.793) scale(1.9625,1.2338) translate(-285.299,-144.827)" width="15" x="581.7142857142858" y="764.2857142857142"/></g>
  <g id="18">
   <use class="v400" height="30" transform="rotate(0,1490.55,781.52) scale(1.9625,1.2338) translate(-723.814,-144.586)" width="15" x="1475.827922077922" xlink:href="#Disconnector:刀闸_0" y="763.012987012987" zvalue="16"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449798602758" ObjectName="#2发电机4521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449798602758"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1490.55,781.52) scale(1.9625,1.2338) translate(-723.814,-144.586)" width="15" x="1475.827922077922" y="763.012987012987"/></g>
  <g id="25">
   <use class="v400" height="18" transform="rotate(0,827.818,762.815) scale(1.71717,1.71717) translate(-341.433,-312.133)" width="12" x="817.5145584722566" xlink:href="#Disconnector:单手车刀闸_0" y="747.3606842332647" zvalue="28"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449798733830" ObjectName="0.4kV I段母线电压互感器4901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449798733830"/></metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,827.818,762.815) scale(1.71717,1.71717) translate(-341.433,-312.133)" width="12" x="817.5145584722566" y="747.3606842332647"/></g>
  <g id="59">
   <use class="kv10" height="30" transform="rotate(0,578.537,414.961) scale(1.9625,1.2338) translate(-276.522,-75.1255)" width="15" x="563.818181818182" xlink:href="#Disconnector:刀闸_0" y="396.4545454545456" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449798799366" ObjectName="#1主变10kV侧0516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449798799366"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,578.537,414.961) scale(1.9625,1.2338) translate(-276.522,-75.1255)" width="15" x="563.818181818182" y="396.4545454545456"/></g>
  <g id="38">
   <use class="kv10" height="30" transform="rotate(0,1455.64,384.909) scale(0.909091,0.909091) translate(144.882,37.1273)" width="15" x="1448.818181818182" xlink:href="#Disconnector:令克_0" y="371.2727272727273" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449798930437" ObjectName="#2主变10kV侧0526隔离刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449798930437"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1455.64,384.909) scale(0.909091,0.909091) translate(144.882,37.1273)" width="15" x="1448.818181818182" y="371.2727272727273"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="5">
   <path class="v400" d="M 596.61 764.9 L 596.61 708" stroke-width="1" zvalue="8"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 596.61 764.9 L 596.61 708" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="v400" d="M 595.32 833.31 L 595.32 800.98" stroke-width="1" zvalue="9"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="165@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 595.32 833.31 L 595.32 800.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="v400" d="M 595.32 925.24 L 595.32 885.96" stroke-width="1" zvalue="12"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="49@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 595.32 925.24 L 595.32 885.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="v400" d="M 1490.72 763.62 L 1490.72 703.71" stroke-width="1" zvalue="18"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="18@0" LinkObjectIDznd="2@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1490.72 763.62 L 1490.72 703.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="v400" d="M 1489.44 832.03 L 1489.44 799.71" stroke-width="1" zvalue="19"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="19@0" LinkObjectIDznd="18@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1489.44 832.03 L 1489.44 799.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="v400" d="M 1489.44 923.97 L 1489.44 884.69" stroke-width="1" zvalue="22"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15@0" LinkObjectIDznd="19@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1489.44 923.97 L 1489.44 884.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="v400" d="M 579.36 627.92 L 579.36 708" stroke-width="1" zvalue="36"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@1" LinkObjectIDznd="1@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 579.36 627.92 L 579.36 708" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv10" d="M 578.66 433.15 L 578.55 487.94" stroke-width="1" zvalue="41"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@1" LinkObjectIDznd="56@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 578.66 433.15 L 578.55 487.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv10" d="M 578.67 509.18 L 579.32 549.27" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@1" LinkObjectIDznd="51@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 578.67 509.18 L 579.32 549.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="kv10" d="M 578.71 397.07 L 578.71 177.56" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="33@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 578.71 397.07 L 578.71 177.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="v400" d="M 1455.36 617.92 L 1455.36 703.71" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@1" LinkObjectIDznd="2@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1455.36 617.92 L 1455.36 703.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv10" d="M 1455.56 396.05 L 1455.56 539.27" stroke-width="1" zvalue="51"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@1" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1455.56 396.05 L 1455.56 539.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv10" d="M 1455.71 372.86 L 1455.71 292.55 L 578.71 292.55" stroke-width="1" zvalue="52"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="32" MaxPinNum="2"/>
   </metadata>
  <path d="M 1455.71 372.86 L 1455.71 292.55 L 578.71 292.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="v400" d="M 828.91 824.36 L 828.91 776.23" stroke-width="1" zvalue="93"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@0" LinkObjectIDznd="25@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 828.91 824.36 L 828.91 776.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="v400" d="M 827.82 749.51 L 827.82 708" stroke-width="1" zvalue="94"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="25@0" LinkObjectIDznd="1@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 827.82 749.51 L 827.82 708" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="129">
   <use class="v400" height="30" transform="rotate(0,595.321,948.038) scale(1.54536,1.54536) translate(-201.909,-326.383)" width="30" x="572.1410455446357" xlink:href="#Generator:发电机_0" y="924.8571428571428" zvalue="11"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449798471686" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449798471686"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,595.321,948.038) scale(1.54536,1.54536) translate(-201.909,-326.383)" width="30" x="572.1410455446357" y="924.8571428571428"/></g>
  <g id="15">
   <use class="v400" height="30" transform="rotate(0,1489.44,946.765) scale(1.54536,1.54536) translate(-517.443,-325.934)" width="30" x="1466.254681908272" xlink:href="#Generator:发电机_0" y="923.5844155844154" zvalue="20"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449798537222" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449798537222"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1489.44,946.765) scale(1.54536,1.54536) translate(-517.443,-325.934)" width="30" x="1466.254681908272" y="923.5844155844154"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="22">
   <use class="v400" height="40" transform="rotate(0,828.911,846.909) scale(1.18182,1.18182) translate(-123.888,-126.657)" width="40" x="805.2748987514218" xlink:href="#Accessory:线路PT三绕组_0" y="823.272727272727" zvalue="25"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449798668294" ObjectName="0.4kV I段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,828.911,846.909) scale(1.18182,1.18182) translate(-123.888,-126.657)" width="40" x="805.2748987514218" y="823.272727272727"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="51">
   <g id="510">
    <use class="kv10" height="50" transform="rotate(0,579.336,588.409) scale(1.58,1.58) translate(-203.968,-201.498)" width="30" x="555.64" xlink:href="#PowerTransformer2:Y-Y_0" y="548.91" zvalue="35"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874437033988" ObjectName="10"/>
    </metadata>
   </g>
   <g id="511">
    <use class="v400" height="50" transform="rotate(0,579.336,588.409) scale(1.58,1.58) translate(-203.968,-201.498)" width="30" x="555.64" xlink:href="#PowerTransformer2:Y-Y_1" y="548.91" zvalue="35"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874437099524" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399450755076" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399450755076"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,579.336,588.409) scale(1.58,1.58) translate(-203.968,-201.498)" width="30" x="555.64" y="548.91"/></g>
  <g id="35">
   <g id="350">
    <use class="kv10" height="50" transform="rotate(0,1455.34,578.409) scale(1.58,1.58) translate(-525.537,-197.827)" width="30" x="1431.64" xlink:href="#PowerTransformer2:Y-Y_0" y="538.91" zvalue="48"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874437165060" ObjectName="10"/>
    </metadata>
   </g>
   <g id="351">
    <use class="v400" height="50" transform="rotate(0,1455.34,578.409) scale(1.58,1.58) translate(-525.537,-197.827)" width="30" x="1431.64" xlink:href="#PowerTransformer2:Y-Y_1" y="538.91" zvalue="48"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874437230596" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399450820612" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399450820612"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1455.34,578.409) scale(1.58,1.58) translate(-525.537,-197.827)" width="30" x="1431.64" y="538.91"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,321.485,321.107) scale(0.708333,0.665547) translate(128.001,156.347)" width="30" x="310.86" xlink:href="#State:红绿圆(方形)_0" y="311.12" zvalue="98"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,321.485,321.107) scale(0.708333,0.665547) translate(128.001,156.347)" width="30" x="310.86" y="311.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,225.86,321.107) scale(0.708333,0.665547) translate(88.6262,156.347)" width="30" x="215.24" xlink:href="#State:红绿圆(方形)_0" y="311.12" zvalue="99"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,225.86,321.107) scale(0.708333,0.665547) translate(88.6262,156.347)" width="30" x="215.24" y="311.12"/></g>
 </g>
</svg>