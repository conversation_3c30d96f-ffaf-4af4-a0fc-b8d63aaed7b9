<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549587083266" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.45181617405996" x2="1.714850492606708" y1="5.276370517142858" y2="14.05696281619048"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.583333333333333" x2="8.583333333333334" y1="5.166666666666667" y2="14.16666666666667"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="12.0194189818494"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="5.416666666666668" y2="12.02321291373541"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.92126160277786" x2="6.666666666666666" y1="12.10207526123773" y2="18.50000000000001"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.18646053143751" x2="24.5" y1="12.18904818695178" y2="19.00000000000001"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 7.33333 43.5833 L 23 43.5833 L 15 31.5 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="4.416666666666668" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.249999999999998" y2="20.16666666666667"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV南马河二级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="36.25" xlink:href="logo.png" y="34.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,185.25,64.75) scale(1,1) translate(0,0)" writing-mode="lr" x="185.25" xml:space="preserve" y="68.25" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,181.75,64.4403) scale(1,1) translate(0,0)" writing-mode="lr" x="181.75" xml:space="preserve" y="73.44" zvalue="3">35kV南马河二级</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="84" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,96.4375,305) scale(1,1) translate(0,0)" width="72.88" x="60" y="293" zvalue="123"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,96.4375,305) scale(1,1) translate(0,0)" writing-mode="lr" x="96.44" xml:space="preserve" y="309.5" zvalue="123">信号一览</text>
  <line fill="none" id="29" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="374.25" x2="374.25" y1="14.75" y2="1044.75" zvalue="4"/>
  <line fill="none" id="27" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.250000000000455" x2="371.25" y1="138.6204926140824" y2="138.6204926140824" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.25" x2="186.25" y1="150.75" y2="150.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.25" x2="186.25" y1="176.75" y2="176.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.25" x2="5.25" y1="150.75" y2="176.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.25" x2="186.25" y1="150.75" y2="176.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.25" x2="367.25" y1="150.75" y2="150.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.25" x2="367.25" y1="176.75" y2="176.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.25" x2="186.25" y1="150.75" y2="176.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367.25" x2="367.25" y1="150.75" y2="176.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.25" x2="186.25" y1="176.75" y2="176.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.25" x2="186.25" y1="201" y2="201"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.25" x2="5.25" y1="176.75" y2="201"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.25" x2="186.25" y1="176.75" y2="201"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.25" x2="367.25" y1="176.75" y2="176.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.25" x2="367.25" y1="201" y2="201"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.25" x2="186.25" y1="176.75" y2="201"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367.25" x2="367.25" y1="176.75" y2="201"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.25" x2="186.25" y1="201" y2="201"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.25" x2="186.25" y1="223.75" y2="223.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.25" x2="5.25" y1="201" y2="223.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.25" x2="186.25" y1="201" y2="223.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.25" x2="367.25" y1="201" y2="201"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.25" x2="367.25" y1="223.75" y2="223.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.25" x2="186.25" y1="201" y2="223.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367.25" x2="367.25" y1="201" y2="223.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.25" x2="186.25" y1="223.75" y2="223.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.25" x2="186.25" y1="246.5" y2="246.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.25" x2="5.25" y1="223.75" y2="246.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.25" x2="186.25" y1="223.75" y2="246.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.25" x2="367.25" y1="223.75" y2="223.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.25" x2="367.25" y1="246.5" y2="246.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.25" x2="186.25" y1="223.75" y2="246.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367.25" x2="367.25" y1="223.75" y2="246.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.25" x2="186.25" y1="246.5" y2="246.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.25" x2="186.25" y1="269.25" y2="269.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5.25" x2="5.25" y1="246.5" y2="269.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.25" x2="186.25" y1="246.5" y2="269.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.25" x2="367.25" y1="246.5" y2="246.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.25" x2="367.25" y1="269.25" y2="269.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186.25" x2="186.25" y1="246.5" y2="269.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367.25" x2="367.25" y1="246.5" y2="269.25"/>
  <line fill="none" id="25" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.250000000000455" x2="371.25" y1="608.6204926140824" y2="608.6204926140824" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="94.25" y1="923.75" y2="923.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="94.25" y1="962.9132999999999" y2="962.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="4.25" y1="923.75" y2="962.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="94.25" y1="923.75" y2="962.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="364.25" y1="923.75" y2="923.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="364.25" y1="962.9132999999999" y2="962.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="94.25" y1="923.75" y2="962.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="364.25" x2="364.25" y1="923.75" y2="962.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="94.25" y1="962.91327" y2="962.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="94.25" y1="990.83167" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="4.25" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="94.25" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="184.25" y1="962.91327" y2="962.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="184.25" y1="990.83167" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="94.25" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.25" x2="184.25" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.2500000000001" x2="274.2500000000001" y1="962.91327" y2="962.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.2500000000001" x2="274.2500000000001" y1="990.83167" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.2500000000001" x2="184.2500000000001" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.2500000000001" x2="274.2500000000001" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.25" x2="364.25" y1="962.91327" y2="962.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.25" x2="364.25" y1="990.83167" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.25" x2="274.25" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="364.25" x2="364.25" y1="962.91327" y2="990.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="94.25" y1="990.8316" y2="990.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="94.25" y1="1018.75" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="4.25" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="94.25" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="184.25" y1="990.8316" y2="990.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="184.25" y1="1018.75" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="94.25" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.25" x2="184.25" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.2500000000001" x2="274.2500000000001" y1="990.8316" y2="990.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.2500000000001" x2="274.2500000000001" y1="1018.75" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.2500000000001" x2="184.2500000000001" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.2500000000001" x2="274.2500000000001" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.25" x2="364.25" y1="990.8316" y2="990.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.25" x2="364.25" y1="1018.75" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.25" x2="274.25" y1="990.8316" y2="1018.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="364.25" x2="364.25" y1="990.8316" y2="1018.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.25,943.75) scale(1,1) translate(0,0)" writing-mode="lr" x="49.25" xml:space="preserve" y="949.75" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46.25,977.75) scale(1,1) translate(0,0)" writing-mode="lr" x="46.25" xml:space="preserve" y="983.75" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.25,977.75) scale(1,1) translate(0,0)" writing-mode="lr" x="228.25" xml:space="preserve" y="983.75" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.25,1005.75) scale(1,1) translate(0,0)" writing-mode="lr" x="45.25" xml:space="preserve" y="1011.75" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.25,1005.75) scale(1,1) translate(0,0)" writing-mode="lr" x="227.25" xml:space="preserve" y="1011.75" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.75,638.25) scale(1,1) translate(0,0)" writing-mode="lr" x="69.75" xml:space="preserve" y="642.75" zvalue="18">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.649,305.591) scale(1,1) translate(0,0)" writing-mode="lr" x="202.65" xml:space="preserve" y="310.09" zvalue="19">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.649,305.591) scale(1,1) translate(0,0)" writing-mode="lr" x="307.65" xml:space="preserve" y="310.09" zvalue="20">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,43.25,164.75) scale(1,1) translate(0,0)" writing-mode="lr" x="43.25" xml:space="preserve" y="170.25" zvalue="27">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,223.25,164.75) scale(1,1) translate(0,0)" writing-mode="lr" x="223.25" xml:space="preserve" y="170.25" zvalue="28">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.426,189.202) scale(1,1) translate(0,0)" writing-mode="lr" x="235.43" xml:space="preserve" y="193.7" zvalue="29">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.2153,236.75) scale(1,1) translate(0,0)" writing-mode="lr" x="58.22" xml:space="preserve" y="241.25" zvalue="30">35kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.5,189.318) scale(1,1) translate(0,0)" writing-mode="lr" x="55.5" xml:space="preserve" y="193.82" zvalue="31">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,498.25,692) scale(1,1) translate(0,0)" writing-mode="lr" x="498.25" xml:space="preserve" y="696.5" zvalue="34">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,495.125,347) scale(1,1) translate(0,0)" writing-mode="lr" x="495.13" xml:space="preserve" y="351.5" zvalue="35">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,701.265,779.115) scale(1,1) translate(0,0)" writing-mode="lr" x="701.26" xml:space="preserve" y="783.62" zvalue="38">631</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" x="673.7890625" xml:space="preserve" y="976.6962240134186" zvalue="42">#1发电机      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="673.7890625" xml:space="preserve" y="992.6962240134186" zvalue="42">2000KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1113.25,780.456) scale(1,1) translate(0,1.28557e-12)" writing-mode="lr" x="1113.25" xml:space="preserve" y="784.96" zvalue="57">632</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" x="1085.7578125" xml:space="preserve" y="978.0399740134186" zvalue="61">#2发电机        </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1085.7578125" xml:space="preserve" y="994.0399740134186" zvalue="61">2000KW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1469.48,901.932) scale(1,1) translate(1.92976e-12,0)" writing-mode="lr" x="1469.48" xml:space="preserve" y="906.4299999999999" zvalue="64">厂用电</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1504.07,767.52) scale(1,1) translate(0,0)" writing-mode="lr" x="1504.07" xml:space="preserve" y="772.02" zvalue="75">633</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" x="692.5078125" xml:space="preserve" y="542.9772727272727" zvalue="79">#1主变             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="692.5078125" xml:space="preserve" y="558.9772727272727" zvalue="79">5000KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,603.338,626.793) scale(1,1) translate(0,0)" writing-mode="lr" x="603.34" xml:space="preserve" y="631.29" zvalue="81">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,659.205,469.02) scale(1,1) translate(0,0)" writing-mode="lr" x="659.21" xml:space="preserve" y="473.52" zvalue="85">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,614.727,393.871) scale(1,1) translate(0,0)" writing-mode="lr" x="614.73" xml:space="preserve" y="398.37" zvalue="88">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,560.48,465.207) scale(1,1) translate(0,0)" writing-mode="lr" x="560.48" xml:space="preserve" y="469.71" zvalue="92">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1129.27,492.091) scale(1,1) translate(0,0)" writing-mode="lr" x="1129.27" xml:space="preserve" y="496.59" zvalue="94">6.3kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1110.27,591.364) scale(1,1) translate(0,-2.58843e-13)" writing-mode="lr" x="1110.27" xml:space="preserve" y="595.86" zvalue="95">6901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,720.545,54.7273) scale(1,1) translate(-1.50334e-13,0)" writing-mode="lr" x="720.55" xml:space="preserve" y="59.23" zvalue="102">35kV南三二线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1083.57,232.003) scale(1,1) translate(0,0)" writing-mode="lr" x="1083.57" xml:space="preserve" y="236.5" zvalue="105">39017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1127.82,261.575) scale(1,1) translate(0,0)" writing-mode="lr" x="1127.82" xml:space="preserve" y="266.08" zvalue="107">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1083.39,327.207) scale(1,1) translate(0,0)" writing-mode="lr" x="1083.39" xml:space="preserve" y="331.71" zvalue="114">39010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,208.125,943.75) scale(1,1) translate(0,0)" writing-mode="lr" x="208.13" xml:space="preserve" y="949.75" zvalue="119">NanMaHe-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,141.25,1005.75) scale(1,1) translate(0,0)" writing-mode="lr" x="141.25" xml:space="preserve" y="1011.75" zvalue="121">杨立超</text>
 </g>
 <g id="ButtonClass">
  <g href="35kV南马河二级电站.svg"><rect fill-opacity="0" height="24" width="72.88" x="60" y="293" zvalue="123"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="32">
   <path class="v6300" d="M 533.75 691.75 L 1730 691.75" stroke-width="6" zvalue="33"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674244100100" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674244100100"/></metadata>
  <path d="M 533.75 691.75 L 1730 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv35" d="M 536.25 351.75 L 1710 351.75" stroke-width="6" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674244165636" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674244165636"/></metadata>
  <path d="M 536.25 351.75 L 1710 351.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="341">
   <use class="v6300" height="20" transform="rotate(0,674.251,780.196) scale(2.16108,2.16108) translate(-356.449,-407.564)" width="10" x="663.4456274226947" xlink:href="#Breaker:手车开关_0" y="758.584974364315" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924513038341" ObjectName="#1发电机631断路器"/>
   <cge:TPSR_Ref TObjectID="6473924513038341"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,674.251,780.196) scale(2.16108,2.16108) translate(-356.449,-407.564)" width="10" x="663.4456274226947" y="758.584974364315"/></g>
  <g id="53">
   <use class="v6300" height="20" transform="rotate(0,1086.23,781.537) scale(2.16108,2.16108) translate(-577.795,-408.285)" width="10" x="1075.428605181537" xlink:href="#Breaker:手车开关_0" y="759.925883455224" zvalue="56"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924513103877" ObjectName="#2发电机632断路器"/>
   <cge:TPSR_Ref TObjectID="6473924513103877"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1086.23,781.537) scale(2.16108,2.16108) translate(-577.795,-408.285)" width="10" x="1075.428605181537" y="759.925883455224"/></g>
  <g id="45">
   <use class="v6300" height="20" transform="rotate(0,1471.71,767.611) scale(2.16108,2.16108) translate(-784.902,-400.803)" width="10" x="1460.909090909091" xlink:href="#Breaker:手车开关_0" y="746" zvalue="74"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924513169413" ObjectName="#1站用变633断路器"/>
   <cge:TPSR_Ref TObjectID="6473924513169413"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1471.71,767.611) scale(2.16108,2.16108) translate(-784.902,-400.803)" width="10" x="1460.909090909091" y="746"/></g>
  <g id="37">
   <use class="v6300" height="20" transform="rotate(0,634.624,626.884) scale(2.16108,2.16108) translate(-335.158,-325.194)" width="10" x="623.8181818181819" xlink:href="#Breaker:手车开关_0" y="605.2727272727273" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924513234949" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924513234949"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,634.624,626.884) scale(2.16108,2.16108) translate(-335.158,-325.194)" width="10" x="623.8181818181819" y="605.2727272727273"/></g>
  <g id="91">
   <use class="kv35" height="20" transform="rotate(0,634.731,468.202) scale(1.22222,1.11111) translate(-114.294,-45.7091)" width="10" x="628.6194089038536" xlink:href="#Breaker:开关_0" y="457.0909090909091" zvalue="84"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924513300486" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924513300486"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,634.731,468.202) scale(1.22222,1.11111) translate(-114.294,-45.7091)" width="10" x="628.6194089038536" y="457.0909090909091"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="47">
   <path class="v6300" d="M 674.25 760.21 L 674.25 691.75" stroke-width="1" zvalue="39"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@0" LinkObjectIDznd="32@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 674.25 760.21 L 674.25 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="v6300" d="M 673 906.47 L 673 799.65" stroke-width="1" zvalue="40"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="341@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 673 906.47 L 673 799.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="v6300" d="M 1086.23 761.55 L 1086.23 691.75" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="32@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1086.23 761.55 L 1086.23 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="v6300" d="M 1084.98 907.81 L 1084.98 800.99" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="53@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1084.98 907.81 L 1084.98 800.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="v6300" d="M 1471.71 747.62 L 1471.71 691.75" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="32@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1471.71 747.62 L 1471.71 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="v6300" d="M 1471.71 787.06 L 1473.51 833.33" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@1" LinkObjectIDznd="205@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1471.71 787.06 L 1473.51 833.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="v6300" d="M 634.62 606.89 L 634.76 581.66" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="88@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 634.62 606.89 L 634.76 581.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="v6300" d="M 634.62 646.33 L 634.62 691.75" stroke-width="1" zvalue="82"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@1" LinkObjectIDznd="32@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 634.62 646.33 L 634.62 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv35" d="M 634.81 478.81 L 634.81 509.39" stroke-width="1" zvalue="85"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@1" LinkObjectIDznd="88@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 634.81 478.81 L 634.81 509.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv35" d="M 633.44 376.98 L 633.44 351.75" stroke-width="1" zvalue="88"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="33@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 633.44 376.98 L 633.44 351.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv35" d="M 633.38 413.06 L 634.69 457.57" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@1" LinkObjectIDznd="91@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 633.38 413.06 L 634.69 457.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv35" d="M 573.26 432.68 L 633.96 432.68" stroke-width="1" zvalue="92"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="58" MaxPinNum="2"/>
   </metadata>
  <path d="M 573.26 432.68 L 633.96 432.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="v6300" d="M 1130.99 580.58 L 1130.99 551.9" stroke-width="1" zvalue="96"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="63@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1130.99 580.58 L 1130.99 551.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="v6300" d="M 1130.91 604.11 L 1130.91 691.75" stroke-width="1" zvalue="97"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@1" LinkObjectIDznd="32@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1130.91 604.11 L 1130.91 691.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="v6300" d="M 1130.91 627.09 L 1194.59 627.09 L 1194.59 583.98" stroke-width="1" zvalue="100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69" LinkObjectIDznd="70@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1130.91 627.09 L 1194.59 627.09 L 1194.59 583.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv35" d="M 722.3 147.97 L 722.3 351.75" stroke-width="1" zvalue="102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@0" LinkObjectIDznd="33@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 722.3 147.97 L 722.3 351.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv35" d="M 1157.44 280.47 L 1157.44 351.75" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="33@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1157.44 280.47 L 1157.44 351.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv35" d="M 1154.72 181.28 L 1157.38 244.38" stroke-width="1" zvalue="110"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="161@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1154.72 181.28 L 1157.38 244.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv35" d="M 1095.45 214.02 L 1156.1 214.02" stroke-width="1" zvalue="111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162@0" LinkObjectIDznd="77" MaxPinNum="2"/>
   </metadata>
  <path d="M 1095.45 214.02 L 1156.1 214.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv35" d="M 1095.26 311.95 L 1157.44 311.95" stroke-width="1" zvalue="114"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="78" MaxPinNum="2"/>
   </metadata>
  <path d="M 1095.26 311.95 L 1157.44 311.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v6300" height="30" transform="rotate(0,673.001,933.886) scale(1.85899,1.85899) translate(-298.091,-418.639)" width="30" x="645.1161969178694" xlink:href="#Generator:发电机_0" y="906.0010218856319" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449806991366" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449806991366"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,673.001,933.886) scale(1.85899,1.85899) translate(-298.091,-418.639)" width="30" x="645.1161969178694" y="906.0010218856319"/></g>
  <g id="50">
   <use class="v6300" height="30" transform="rotate(0,1084.98,935.227) scale(1.85899,1.85899) translate(-488.458,-419.259)" width="30" x="1057.099174676712" xlink:href="#Generator:发电机_0" y="907.3419309765409" zvalue="60"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449807056902" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449807056902"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1084.98,935.227) scale(1.85899,1.85899) translate(-488.458,-419.259)" width="30" x="1057.099174676712" y="907.3419309765409"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="205">
   <use class="v6300" height="30" transform="rotate(0,1473.35,857.932) scale(1.69643,1.70833) translate(-595.1,-345.103)" width="28" x="1449.602272727273" xlink:href="#EnergyConsumer:站用变DY接地_0" y="832.306818181818" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449807122438" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1473.35,857.932) scale(1.69643,1.70833) translate(-595.1,-345.103)" width="28" x="1449.602272727273" y="832.306818181818"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="88">
   <g id="880">
    <use class="kv35" height="50" transform="rotate(0,634.764,545.455) scale(1.46909,1.46909) translate(-195.648,-162.44)" width="30" x="612.73" xlink:href="#PowerTransformer2:Y-D_0" y="508.73" zvalue="78"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874438213636" ObjectName="35"/>
    </metadata>
   </g>
   <g id="881">
    <use class="v6300" height="50" transform="rotate(0,634.764,545.455) scale(1.46909,1.46909) translate(-195.648,-162.44)" width="30" x="612.73" xlink:href="#PowerTransformer2:Y-D_1" y="508.73" zvalue="78"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874438279172" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399451344899" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399451344899"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,634.764,545.455) scale(1.46909,1.46909) translate(-195.648,-162.44)" width="30" x="612.73" y="508.73"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="94">
   <use class="kv35" height="30" transform="rotate(0,633.264,394.871) scale(1.9625,1.2338) translate(-303.363,-71.3184)" width="15" x="618.5454545454546" xlink:href="#Disconnector:刀闸_0" y="376.3636363636364" zvalue="87"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449807187974" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449807187974"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,633.264,394.871) scale(1.9625,1.2338) translate(-303.363,-71.3184)" width="15" x="618.5454545454546" y="376.3636363636364"/></g>
  <g id="65">
   <use class="v6300" height="26" transform="rotate(0,1130.91,592.364) scale(0.909091,-0.909091) translate(112.545,-1245.15)" width="12" x="1125.454545454545" xlink:href="#Disconnector:单手车刀闸1212_0" y="580.5454545454545" zvalue="94"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449807450118" ObjectName="6.3kV母线电压互感器6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449807450118"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1130.91,592.364) scale(0.909091,-0.909091) translate(112.545,-1245.15)" width="12" x="1125.454545454545" y="580.5454545454545"/></g>
  <g id="161">
   <use class="kv35" height="30" transform="rotate(0,1157.26,262.575) scale(1.9625,-1.2338) translate(-560.357,-471.887)" width="15" x="1142.545454545455" xlink:href="#Disconnector:刀闸_0" y="244.0681818181819" zvalue="106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449807712262" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449807712262"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1157.26,262.575) scale(1.9625,-1.2338) translate(-560.357,-471.887)" width="15" x="1142.545454545455" y="244.0681818181819"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="98">
   <use class="kv35" height="30" transform="rotate(90,560.48,432.694) scale(1.11574,0.892592) translate(-57.4465,50.456)" width="12" x="553.7853518399326" xlink:href="#GroundDisconnector:地刀12_0" y="419.3055572509766" zvalue="91"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449807319045" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449807319045"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,560.48,432.694) scale(1.11574,0.892592) translate(-57.4465,50.456)" width="12" x="553.7853518399326" y="419.3055572509766"/></g>
  <g id="162">
   <use class="kv35" height="30" transform="rotate(90,1082.66,214.035) scale(1.11574,0.892592) translate(-111.615,24.1442)" width="12" x="1075.967170021751" xlink:href="#GroundDisconnector:地刀12_0" y="200.6464663418855" zvalue="104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449807843334" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449807843334"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1082.66,214.035) scale(1.11574,0.892592) translate(-111.615,24.1442)" width="12" x="1075.967170021751" y="200.6464663418855"/></g>
  <g id="79">
   <use class="kv35" height="30" transform="rotate(90,1082.48,311.967) scale(1.11574,0.892592) translate(-111.596,35.9286)" width="12" x="1075.785351839932" xlink:href="#GroundDisconnector:地刀12_0" y="298.5782845237038" zvalue="113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449807974406" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449807974406"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1082.48,311.967) scale(1.11574,0.892592) translate(-111.596,35.9286)" width="12" x="1075.785351839932" y="298.5782845237038"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="63">
   <use class="v6300" height="30" transform="rotate(0,1129.27,532.364) scale(1.33333,-1.33333) translate(-277.318,-926.636)" width="30" x="1109.272727272727" xlink:href="#Accessory:PT789_0" y="512.3636363636364" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449807384582" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1129.27,532.364) scale(1.33333,-1.33333) translate(-277.318,-926.636)" width="30" x="1109.272727272727" y="512.3636363636364"/></g>
  <g id="70">
   <use class="v6300" height="20" transform="rotate(0,1194.59,570.773) scale(1.88636,-1.88636) translate(-552.45,-864.487)" width="20" x="1175.727272727273" xlink:href="#Accessory:线路PT3_0" y="551.9090909090909" zvalue="98"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449807515654" ObjectName="6.3kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1194.59,570.773) scale(1.88636,-1.88636) translate(-552.45,-864.487)" width="20" x="1175.727272727273" y="551.9090909090909"/></g>
  <g id="159">
   <use class="kv35" height="18" transform="rotate(0,1152.73,163.413) scale(-2.13675,-2.13675) translate(-1683.69,-229.659)" width="15" x="1136.707067316229" xlink:href="#Accessory:PT8_0" y="144.1818181818181" zvalue="109"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449807646726" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1152.73,163.413) scale(-2.13675,-2.13675) translate(-1683.69,-229.659)" width="15" x="1136.707067316229" y="144.1818181818181"/></g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,341.25,305.983) scale(0.708333,0.665547) translate(136.14,148.747)" width="30" x="330.63" xlink:href="#State:红绿圆(方形)_0" y="296" zvalue="116"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374888402947" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,341.25,305.983) scale(0.708333,0.665547) translate(136.14,148.747)" width="30" x="330.63" y="296"/></g>
  <g id="160">
   <use height="30" transform="rotate(0,241.625,309.983) scale(0.708333,0.665547) translate(95.1176,150.757)" width="30" x="231" xlink:href="#State:红绿圆(方形)_0" y="300" zvalue="117"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,241.625,309.983) scale(0.708333,0.665547) translate(95.1176,150.757)" width="30" x="231" y="300"/></g>
  <g id="10">
   <use height="30" transform="rotate(0,311.812,116.464) scale(1.27778,1.03333) translate(-55.2853,-3.2569)" width="90" x="254.31" xlink:href="#State:全站检修_0" y="100.96" zvalue="132"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549587083266" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,311.812,116.464) scale(1.27778,1.03333) translate(-55.2853,-3.2569)" width="90" x="254.31" y="100.96"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,722.296,10.4091) scale(1,1) translate(2.25585e-13,0)" writing-mode="lr" x="721.83" xml:space="preserve" y="15.19" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125320654857" ObjectName="P"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="7" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,722.296,47.4091) scale(1,1) translate(2.25585e-13,0)" writing-mode="lr" x="721.83" xml:space="preserve" y="52.19" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125320720393" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="8" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,722.296,84.4091) scale(1,1) translate(2.25585e-13,0)" writing-mode="lr" x="721.83" xml:space="preserve" y="89.19" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125320785926" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
</svg>