<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1200" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="段勇20230822" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="34.81" xlink:href="logo.png" y="103.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="219" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,159.653,144.714) scale(1,1) translate(-7.72864e-15,0)" writing-mode="lr" x="159.65" xml:space="preserve" y="148.21" zvalue="403"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,188.5,144.69) scale(1,1) translate(0,0)" writing-mode="lr" x="188.5" xml:space="preserve" y="153.69" zvalue="404">段勇20230822</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,996.507,581) scale(1,1) translate(0,0)" writing-mode="lr" x="996.51" xml:space="preserve" y="585.5" zvalue="448">35kV#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,931.5,437) scale(1,1) translate(0,0)" writing-mode="lr" x="931.5" xml:space="preserve" y="441.5" zvalue="449">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,932,491) scale(1,1) translate(0,0)" writing-mode="lr" x="932" xml:space="preserve" y="495.5" zvalue="450">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,934,385) scale(1,1) translate(0,0)" writing-mode="lr" x="934" xml:space="preserve" y="389.5" zvalue="451">3016</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1037.71,343) scale(1,1) translate(0,0)" writing-mode="lr" x="1037.71" xml:space="preserve" y="347.5" zvalue="452">35kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,932.5,245) scale(1,1) translate(0,0)" writing-mode="lr" x="932.5" xml:space="preserve" y="249.5" zvalue="459">311</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,931.027,299) scale(1,1) translate(-2.03954e-13,0)" writing-mode="lr" x="931.03" xml:space="preserve" y="303.5" zvalue="461">3111</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,935,193) scale(1,1) translate(0,0)" writing-mode="lr" x="935" xml:space="preserve" y="197.5" zvalue="462">3116</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,909,102) scale(1,1) translate(0,0)" writing-mode="lr" x="909" xml:space="preserve" y="106.5" zvalue="467">35kV考试线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,932.5,713) scale(1,1) translate(0,0)" writing-mode="lr" x="932.5" xml:space="preserve" y="717.5" zvalue="471">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,933,767) scale(1,1) translate(0,0)" writing-mode="lr" x="933" xml:space="preserve" y="771.5" zvalue="473">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,935,661) scale(1,1) translate(0,0)" writing-mode="lr" x="935" xml:space="preserve" y="665.5" zvalue="474">0016</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1035.05,809) scale(1,1) translate(0,0)" writing-mode="lr" x="1035.05" xml:space="preserve" y="813.5" zvalue="480">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,933.5,905) scale(1,1) translate(0,0)" writing-mode="lr" x="933.5" xml:space="preserve" y="909.5" zvalue="484">011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,934,959) scale(1,1) translate(0,0)" writing-mode="lr" x="934" xml:space="preserve" y="963.5" zvalue="486">0111</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,936,853) scale(1,1) translate(0,0)" writing-mode="lr" x="936" xml:space="preserve" y="857.5" zvalue="487">0116</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,911,1051) scale(1,1) translate(0,0)" writing-mode="lr" x="911" xml:space="preserve" y="1055.5" zvalue="491">10kV考试线</text>
 </g>
 <g id="ButtonClass"/>
 <g id="ClockClass">
  
 </g>
 <g id="PowerTransformer2Class">
  <g id="1">
   <g id="10">
    <use class="kv35" height="60" transform="rotate(0,909.007,574.833) scale(1.375,1.52778) translate(-240.411,-182.745)" width="40" x="881.51" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="529" zvalue="447"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="35"/>
    </metadata>
   </g>
   <g id="11">
    <use class="kv10" height="60" transform="rotate(0,909.007,574.833) scale(1.375,1.52778) translate(-240.411,-182.745)" width="40" x="881.51" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="529" zvalue="447"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV#1主变"/>
   </metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,909.007,574.833) scale(1.375,1.52778) translate(-240.411,-182.745)" width="40" x="881.51" y="529"/></g>
 </g>
 <g id="BreakerClass">
  <g id="3">
   <use class="kv35" height="20" transform="rotate(0,908,437) scale(1.5,1.35) translate(-300.167,-109.796)" width="10" x="900.5" xlink:href="#Breaker:开关_0" y="423.5" zvalue="448"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="301"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,908,437) scale(1.5,1.35) translate(-300.167,-109.796)" width="10" x="900.5" y="423.5"/></g>
  <g id="22">
   <use class="kv35" height="20" transform="rotate(0,909,245) scale(1.5,1.35) translate(-300.5,-60.0185)" width="10" x="901.5" xlink:href="#Breaker:开关_0" y="231.5" zvalue="457"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="311"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,909,245) scale(1.5,1.35) translate(-300.5,-60.0185)" width="10" x="901.5" y="231.5"/></g>
  <g id="34">
   <use class="kv10" height="20" transform="rotate(0,909,713) scale(1.5,1.35) translate(-300.5,-181.352)" width="10" x="901.5" xlink:href="#Breaker:开关_0" y="699.5" zvalue="469"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="001"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,909,713) scale(1.5,1.35) translate(-300.5,-181.352)" width="10" x="901.5" y="699.5"/></g>
  <g id="46">
   <use class="kv10" height="20" transform="rotate(0,910,905) scale(1.5,1.35) translate(-300.833,-231.13)" width="10" x="902.5" xlink:href="#Breaker:开关_0" y="891.5" zvalue="482"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="011"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,910,905) scale(1.5,1.35) translate(-300.833,-231.13)" width="10" x="902.5" y="891.5"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="5">
   <use class="kv35" height="30" transform="rotate(0,909,489) scale(1,0.733333) translate(0,173.818)" width="15" x="901.5" xlink:href="#Disconnector:刀闸_0" y="478" zvalue="449"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,909,489) scale(1,0.733333) translate(0,173.818)" width="15" x="901.5" y="478"/></g>
  <g id="7">
   <use class="kv35" height="30" transform="rotate(0,908,386) scale(1,0.733333) translate(0,136.364)" width="15" x="900.5" xlink:href="#Disconnector:刀闸_0" y="375" zvalue="450"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3016"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,908,386) scale(1,0.733333) translate(0,136.364)" width="15" x="900.5" y="375"/></g>
  <g id="21">
   <use class="kv35" height="30" transform="rotate(0,908.027,297) scale(1,0.733333) translate(0,104)" width="15" x="900.526612626755" xlink:href="#Disconnector:刀闸_0" y="286" zvalue="458"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3111"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,908.027,297) scale(1,0.733333) translate(0,104)" width="15" x="900.526612626755" y="286"/></g>
  <g id="20">
   <use class="kv35" height="30" transform="rotate(0,909,194) scale(1,0.733333) translate(0,66.5455)" width="15" x="901.5" xlink:href="#Disconnector:刀闸_0" y="183" zvalue="460"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="3116"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,909,194) scale(1,0.733333) translate(0,66.5455)" width="15" x="901.5" y="183"/></g>
  <g id="33">
   <use class="kv10" height="30" transform="rotate(0,910,765) scale(1,0.733333) translate(0,274.182)" width="15" x="902.5" xlink:href="#Disconnector:刀闸_0" y="754" zvalue="470"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,910,765) scale(1,0.733333) translate(0,274.182)" width="15" x="902.5" y="754"/></g>
  <g id="32">
   <use class="kv10" height="30" transform="rotate(0,909,662) scale(1,0.733333) translate(0,236.727)" width="15" x="901.5" xlink:href="#Disconnector:刀闸_0" y="651" zvalue="472"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0016"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,909,662) scale(1,0.733333) translate(0,236.727)" width="15" x="901.5" y="651"/></g>
  <g id="45">
   <use class="kv10" height="30" transform="rotate(0,911,957) scale(1,0.733333) translate(0,344)" width="15" x="903.5" xlink:href="#Disconnector:刀闸_0" y="946" zvalue="483"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0111"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,911,957) scale(1,0.733333) translate(0,344)" width="15" x="903.5" y="946"/></g>
  <g id="44">
   <use class="kv10" height="30" transform="rotate(0,910,854) scale(1,0.733333) translate(0,306.545)" width="15" x="902.5" xlink:href="#Disconnector:刀闸_0" y="843" zvalue="485"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="0116"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,910,854) scale(1,0.733333) translate(0,306.545)" width="15" x="902.5" y="843"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="9">
   <path class="kv35" d="M 832.29 341 L 998 341" stroke-width="6" zvalue="451"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kVⅠ母"/>
   </metadata>
  <path d="M 832.29 341 L 998 341" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv10" d="M 832.34 807 L 998.05 807" stroke-width="6" zvalue="479"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kVⅠ母"/>
   </metadata>
  <path d="M 832.34 807 L 998.05 807" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="11">
   <path class="kv35" d="M 909.06 529.83 L 909.06 499.81" stroke-width="1" zvalue="452"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="5@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 909.06 529.83 L 909.06 499.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv35" d="M 909.09 478.36 L 909.09 449.89" stroke-width="1" zvalue="453"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@0" LinkObjectIDznd="3@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 909.09 478.36 L 909.09 449.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv35" d="M 908.09 375.36 L 908.09 341" stroke-width="1" zvalue="454"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="7@0" LinkObjectIDznd="9@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 908.09 375.36 L 908.09 341" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv35" d="M 908.06 396.81 L 908.06 424.09" stroke-width="1" zvalue="455"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="7@1" LinkObjectIDznd="3@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 908.06 396.81 L 908.06 424.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv35" d="M 908.11 286.36 L 908.11 257.89" stroke-width="1" zvalue="463"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@0" LinkObjectIDznd="22@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 908.11 286.36 L 908.11 257.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv35" d="M 909.06 204.81 L 909.06 232.09" stroke-width="1" zvalue="464"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@1" LinkObjectIDznd="22@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 909.06 204.81 L 909.06 232.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv35" d="M 908.09 307.81 L 908.09 341" stroke-width="1" zvalue="465"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@1" LinkObjectIDznd="9@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 908.09 307.81 L 908.09 341" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv35" d="M 908 159.65 L 908 183.36" stroke-width="1" zvalue="467"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@0" LinkObjectIDznd="20@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 908 159.65 L 908 183.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv10" d="M 910.09 754.36 L 910.09 725.89" stroke-width="1" zvalue="475"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="34@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 910.09 754.36 L 910.09 725.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv10" d="M 909.06 672.81 L 909.06 700.09" stroke-width="1" zvalue="476"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@1" LinkObjectIDznd="34@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 909.06 672.81 L 909.06 700.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv10" d="M 909.09 651.36 L 909.01 620.02" stroke-width="1" zvalue="477"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="1@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 909.09 651.36 L 909.01 620.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv10" d="M 910.06 775.81 L 910.06 807" stroke-width="1" zvalue="480"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@1" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 910.06 775.81 L 910.06 807" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv10" d="M 911.09 946.36 L 911.09 917.89" stroke-width="1" zvalue="488"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="46@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 911.09 946.36 L 911.09 917.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv10" d="M 910.06 864.81 L 910.06 892.09" stroke-width="1" zvalue="489"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@1" LinkObjectIDznd="46@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 910.06 864.81 L 910.06 892.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv10" d="M 911 993.35 L 911.06 967.81" stroke-width="1" zvalue="491"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="45@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 911 993.35 L 911.06 967.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv10" d="M 910.09 843.36 L 910.09 807" stroke-width="1" zvalue="493"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="36@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 910.09 843.36 L 910.09 807" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="24">
   <use class="kv35" height="30" transform="rotate(0,908,143) scale(1.25,1.23333) translate(-180.1,-23.5541)" width="12" x="900.5" xlink:href="#EnergyConsumer:负荷_0" y="124.5" zvalue="466"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="35kV考试线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,908,143) scale(1.25,1.23333) translate(-180.1,-23.5541)" width="12" x="900.5" y="124.5"/></g>
  <g id="47">
   <use class="kv10" height="30" transform="rotate(0,911,1010) scale(1.25,-1.23333) translate(-180.7,-1825.42)" width="12" x="903.5" xlink:href="#EnergyConsumer:负荷_0" y="991.5" zvalue="490"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="10kV考试线"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,911,1010) scale(1.25,-1.23333) translate(-180.7,-1825.42)" width="12" x="903.5" y="991.5"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="50">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="50" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,834,138) scale(1,1) translate(0,0)" writing-mode="lr" x="790.88" xml:space="preserve" y="142.36" zvalue="1">P:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="52" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,834,175) scale(1,1) translate(0,0)" writing-mode="lr" x="790.88" xml:space="preserve" y="179.36" zvalue="1">Q:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="53" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,834,212) scale(1,1) translate(0,0)" writing-mode="lr" x="790.88" xml:space="preserve" y="216.36" zvalue="1">Ia:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="54" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,811.007,397.5) scale(1,1) translate(0,0)" writing-mode="lr" x="760.22" xml:space="preserve" y="403.36" zvalue="1">P:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="55" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,811.007,434.5) scale(1,1) translate(0,0)" writing-mode="lr" x="760.22" xml:space="preserve" y="440.36" zvalue="1">Q:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="56" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,811.007,672.167) scale(1,1) translate(0,0)" writing-mode="lr" x="760.22" xml:space="preserve" y="678.03" zvalue="1">P:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="57" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,811.007,709.167) scale(1,1) translate(0,0)" writing-mode="lr" x="760.22" xml:space="preserve" y="715.03" zvalue="1">Q:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="58">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="58" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,811.007,471.5) scale(1,1) translate(0,0)" writing-mode="lr" x="760.22" xml:space="preserve" y="477.36" zvalue="1">Ia:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="59">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="59" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,811.007,746.167) scale(1,1) translate(0,0)" writing-mode="lr" x="760.22" xml:space="preserve" y="752.03" zvalue="1">Ia:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="60">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="60" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,838.5,919.25) scale(1,1) translate(0,0)" writing-mode="lr" x="795.38" xml:space="preserve" y="923.61" zvalue="1">P:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="61">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="61" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,838.5,957.25) scale(1,1) translate(0,0)" writing-mode="lr" x="795.38" xml:space="preserve" y="961.61" zvalue="1">Q:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="62" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,838.5,994.25) scale(1,1) translate(0,0)" writing-mode="lr" x="795.38" xml:space="preserve" y="998.61" zvalue="1">Ia:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="63" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1124.34,780.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1081.22" xml:space="preserve" y="784.86" zvalue="1">Ua:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="64" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1124.34,794.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1081.22" xml:space="preserve" y="798.86" zvalue="1">Ub:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="65" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1122.34,810.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1079.22" xml:space="preserve" y="814.86" zvalue="1">Uc:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="66" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1120.34,825) scale(1,1) translate(0,0)" writing-mode="lr" x="1077.22" xml:space="preserve" y="829.36" zvalue="1">Uab:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="67">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="67" prefix="F:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1120.2,840.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1077.07" xml:space="preserve" y="844.86" zvalue="1">F:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="68" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1124.15,301.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1081.02" xml:space="preserve" y="305.86" zvalue="1">Ua:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="69">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="69" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1124.15,323) scale(1,1) translate(0,0)" writing-mode="lr" x="1081.02" xml:space="preserve" y="327.36" zvalue="1">Ub:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="70">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="70" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1124.15,366) scale(1,1) translate(0,0)" writing-mode="lr" x="1081.02" xml:space="preserve" y="370.36" zvalue="1">Uc:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="71">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="71" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1124.15,344.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1081.02" xml:space="preserve" y="348.86" zvalue="1">Uab:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="72">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="72" prefix="F:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1124.15,387.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1081.02" xml:space="preserve" y="391.86" zvalue="1">F:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
 </g>
</svg>