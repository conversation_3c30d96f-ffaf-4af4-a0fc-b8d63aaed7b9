<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549580988418" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:设备233_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.097643878606505" xlink:href="#terminal" y="23.59422303238503"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="4" y1="2" y2="4"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="8" y1="2" y2="4"/>
   <rect fill-opacity="0" height="12.18" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.04,12.51) scale(1,1) translate(0,0)" width="7.42" x="2.33" y="6.42"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.5" x2="8.611111111111114" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.5" x2="8.611111111111114" y1="22.41666666666666" y2="22.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="2.166666666666664" y2="6.599999999999997"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.08333333333333" x2="9.833333333333332" y1="16.5" y2="18.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14" y1="9.75" y2="16.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="11" y1="11.58333333333333" y2="9.666666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="9.75" y1="11.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8" x2="15.08333333333333" y1="8.416666666666668" y2="25.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_1" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.96,13) scale(1,1) translate(0,0)" width="4.42" x="12.75" y="9"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="5.416666666666668" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_2" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="20" y1="6" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="10.08333333333333" y1="6.000000000000004" y2="25.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV木笼河四级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="43.75" xlink:href="logo.png" y="49.25"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,178.375,79.25) scale(1,1) translate(0,0)" writing-mode="lr" x="178.37" xml:space="preserve" y="82.75" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,180.083,78.9403) scale(1,1) translate(6.85563e-15,0)" writing-mode="lr" x="180.08" xml:space="preserve" y="87.94" zvalue="3">10kV木笼河四级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="18" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,82.625,220) scale(1,1) translate(0,0)" width="72.88" x="46.19" y="208" zvalue="244"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.625,220) scale(1,1) translate(0,0)" writing-mode="lr" x="82.63" xml:space="preserve" y="224.5" zvalue="244">信号一览</text>
  <line fill="none" id="174" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.75" x2="377.75" y1="17.25000000000006" y2="1047.25" zvalue="4"/>
  <line fill="none" id="171" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.750000000000227" x2="370.7499999999998" y1="153.1204926140824" y2="153.1204926140824" zvalue="6"/>
  <line fill="none" id="168" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.750000000000227" x2="370.7499999999998" y1="623.1204926140825" y2="623.1204926140825" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="938.2500000000002" y2="938.2500000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="977.4133000000002" y2="977.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="938.2500000000002" y2="977.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="938.2500000000002" y2="977.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="363.75" y1="938.2500000000002" y2="938.2500000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="363.75" y1="977.4133000000002" y2="977.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="938.2500000000002" y2="977.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="938.2500000000002" y2="977.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="977.4132700000002" y2="977.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1005.33167" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="977.4132700000002" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="977.4132700000002" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="977.4132700000002" y2="977.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1005.33167" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="977.4132700000002" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.75" x2="183.75" y1="977.4132700000002" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="977.4132700000002" y2="977.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1005.33167" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="183.7500000000001" y1="977.4132700000002" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.7500000000001" x2="273.7500000000001" y1="977.4132700000002" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="977.4132700000002" y2="977.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1005.33167" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="273.75" y1="977.4132700000002" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="977.4132700000002" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1005.3316" y2="1005.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1033.25" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1005.3316" y2="1005.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1033.25" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.75" x2="183.75" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1005.3316" y2="1005.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1033.25" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="183.7500000000001" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.7500000000001" x2="273.7500000000001" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1005.3316" y2="1005.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1033.25" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="273.75" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="1005.3316" y2="1033.25"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.75,958.25) scale(1,1) translate(0,0)" writing-mode="lr" x="48.75" xml:space="preserve" y="964.25" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.75,992.25) scale(1,1) translate(0,0)" writing-mode="lr" x="45.75" xml:space="preserve" y="998.25" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.75,992.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.75" xml:space="preserve" y="998.25" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.75,1020.25) scale(1,1) translate(0,0)" writing-mode="lr" x="44.75" xml:space="preserve" y="1026.25" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.75,1020.25) scale(1,1) translate(0,0)" writing-mode="lr" x="226.75" xml:space="preserve" y="1026.25" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.25,652.75) scale(1,1) translate(0,2.11081e-13)" writing-mode="lr" x="69.25" xml:space="preserve" y="657.2500000000001" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.804,960.25) scale(1,1) translate(0,0)" writing-mode="lr" x="228.8" xml:space="preserve" y="966.25" zvalue="28">MGS4-01-2008</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,138.804,992.25) scale(1,1) translate(0,0)" writing-mode="lr" x="138.8" xml:space="preserve" y="998.25" zvalue="29">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,318.804,992.25) scale(1,1) translate(0,0)" writing-mode="lr" x="318.8" xml:space="preserve" y="998.25" zvalue="30">20210218</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,555.533,489.449) scale(1,1) translate(0,0)" writing-mode="lr" x="555.53" xml:space="preserve" y="493.95" zvalue="37">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,645.884,536.196) scale(1,1) translate(0,0)" writing-mode="lr" x="645.88" xml:space="preserve" y="540.7" zvalue="39">061</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,690.668,758.628) scale(1,1) translate(-2.91163e-13,-4.93852e-13)" writing-mode="lr" x="690.6684752281723" xml:space="preserve" y="763.1277576427464" zvalue="42">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,718.27,398.446) scale(1,1) translate(0,0)" writing-mode="lr" x="718.27" xml:space="preserve" y="402.95" zvalue="71">063</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1069.62,592.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1069.62" xml:space="preserve" y="596.83" zvalue="158">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,689.669,244.889) scale(1,1) translate(0,0)" writing-mode="lr" x="689.67" xml:space="preserve" y="249.39" zvalue="190">10kV木笼河三四级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1453.88,536.196) scale(1,1) translate(0,0)" writing-mode="lr" x="1453.88" xml:space="preserve" y="540.7" zvalue="196">062</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1498.67,756.628) scale(1,1) translate(1.13748e-12,-4.9252e-13)" writing-mode="lr" x="1498.668475228172" xml:space="preserve" y="761.1277576427464" zvalue="199">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1683.88,358.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1683.88" xml:space="preserve" y="363" zvalue="223">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1686.93,62.8056) scale(1,1) translate(0,0)" writing-mode="lr" x="1686.93" xml:space="preserve" y="67.31" zvalue="228">至勐嘎河四级电站线</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="281.75" y2="281.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="307.75" y2="307.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="281.75" y2="307.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="281.75" y2="307.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="281.75" y2="281.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="307.75" y2="307.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="281.75" y2="307.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="281.75" y2="307.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="307.75" y2="307.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="332" y2="332"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="307.75" y2="332"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="307.75" y2="332"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="307.75" y2="307.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="332" y2="332"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="307.75" y2="332"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="307.75" y2="332"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="332" y2="332"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="354.75" y2="354.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="332" y2="354.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="332" y2="354.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="332" y2="332"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="354.75" y2="354.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="332" y2="354.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="332" y2="354.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="354.75" y2="354.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="377.5" y2="377.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="354.75" y2="377.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="354.75" y2="377.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="354.75" y2="354.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="377.5" y2="377.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="354.75" y2="377.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="354.75" y2="377.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="377.5" y2="377.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="400.25" y2="400.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="377.5" y2="400.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="377.5" y2="400.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="377.5" y2="377.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="400.25" y2="400.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="377.5" y2="400.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="377.5" y2="400.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="400.25" y2="400.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="423" y2="423"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="400.25" y2="423"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="400.25" y2="423"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="400.25" y2="400.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="423" y2="423"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="400.25" y2="423"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="400.25" y2="423"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="423" y2="423"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="445.75" y2="445.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="423" y2="445.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="423" y2="445.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="423" y2="423"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="445.75" y2="445.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="423" y2="445.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="423" y2="445.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,198.399,220.591) scale(1,1) translate(0,0)" writing-mode="lr" x="198.4" xml:space="preserve" y="225.09" zvalue="231">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,303.399,220.591) scale(1,1) translate(0,0)" writing-mode="lr" x="303.4" xml:space="preserve" y="225.09" zvalue="232">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,55.5,294.75) scale(1,1) translate(0,0)" writing-mode="lr" x="13" xml:space="preserve" y="299.25" zvalue="233">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,236,294.75) scale(1,1) translate(0,0)" writing-mode="lr" x="193.5" xml:space="preserve" y="299.25" zvalue="234">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.625,368.75) scale(1,1) translate(0,0)" writing-mode="lr" x="54.63" xml:space="preserve" y="373.25" zvalue="236">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,54.5,320.75) scale(1,1) translate(0,0)" writing-mode="lr" x="12" xml:space="preserve" y="325.25" zvalue="245">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235,320.75) scale(1,1) translate(0,0)" writing-mode="lr" x="192.5" xml:space="preserve" y="325.25" zvalue="246">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6875,414) scale(1,1) translate(0,0)" writing-mode="lr" x="55.69" xml:space="preserve" y="418.5" zvalue="249">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,223.688,413) scale(1,1) translate(0,0)" writing-mode="lr" x="223.69" xml:space="preserve" y="417.5" zvalue="251">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.6875,437) scale(1,1) translate(0,0)" writing-mode="lr" x="55.69" xml:space="preserve" y="441.5" zvalue="252">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,223.688,436) scale(1,1) translate(0,0)" writing-mode="lr" x="223.69" xml:space="preserve" y="440.5" zvalue="253">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,54.5,343.75) scale(1,1) translate(0,0)" writing-mode="lr" x="12" xml:space="preserve" y="348.25" zvalue="254">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,234.5,342.75) scale(1,1) translate(0,0)" writing-mode="lr" x="192" xml:space="preserve" y="347.25" zvalue="256">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,689,777) scale(1,1) translate(0,0)" writing-mode="lr" x="689" xml:space="preserve" y="781.5" zvalue="277">2.5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1499,774) scale(1,1) translate(0,0)" writing-mode="lr" x="1499" xml:space="preserve" y="778.5" zvalue="278">2.5MW</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="46.19" y="208" zvalue="244"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="253">
   <path class="kv10" d="M 560.25 467.43 L 1635.56 467.43" stroke-width="6" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674231975941" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674231975941"/></metadata>
  <path d="M 560.25 467.43 L 1635.56 467.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="195">
   <use class="kv10" height="20" transform="rotate(0,689.669,531.56) scale(2.06048,2.06048) translate(-349.653,-262.976)" width="10" x="679.3663072734205" xlink:href="#Breaker:小车断路器_0" y="510.9550984421437" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924466442245" ObjectName="#1发电机061断路器"/>
   <cge:TPSR_Ref TObjectID="6473924466442245"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,689.669,531.56) scale(2.06048,2.06048) translate(-349.653,-262.976)" width="10" x="679.3663072734205" y="510.9550984421437"/></g>
  <g id="92">
   <use class="kv10" height="20" transform="rotate(0,689.669,394.563) scale(2.77222,2.77222) translate(-432.029,-234.514)" width="10" x="675.8075886186618" xlink:href="#Breaker:小车断路器_0" y="366.8412698412699" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924466376709" ObjectName="10kV木笼河三四级线063断路器"/>
   <cge:TPSR_Ref TObjectID="6473924466376709"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,689.669,394.563) scale(2.77222,2.77222) translate(-432.029,-234.514)" width="10" x="675.8075886186618" y="366.8412698412699"/></g>
  <g id="199">
   <use class="kv10" height="20" transform="rotate(0,1497.67,531.56) scale(2.06048,2.06048) translate(-765.511,-262.976)" width="10" x="1487.366307273421" xlink:href="#Breaker:小车断路器_0" y="510.9550984421437" zvalue="195"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924466507781" ObjectName="#2发电机062断路器"/>
   <cge:TPSR_Ref TObjectID="6473924466507781"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1497.67,531.56) scale(2.06048,2.06048) translate(-765.511,-262.976)" width="10" x="1487.366307273421" y="510.9550984421437"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="120">
   <path class="kv10" d="M 689.64 697.19 L 689.67 550.1" stroke-width="1" zvalue="40"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@0" LinkObjectIDznd="195@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 689.64 697.19 L 689.67 550.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv10" d="M 739.97 573.85 L 689.66 573.85" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@0" LinkObjectIDznd="120" MaxPinNum="2"/>
   </metadata>
  <path d="M 739.97 573.85 L 689.66 573.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv10" d="M 689.67 512.5 L 689.67 467.43" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@0" LinkObjectIDznd="253@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 689.67 512.5 L 689.67 467.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv10" d="M 689.67 419.51 L 689.67 467.43" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@1" LinkObjectIDznd="253@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 689.67 419.51 L 689.67 467.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv10" d="M 689.67 305.33 L 689.67 368.92" stroke-width="1" zvalue="149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@0" LinkObjectIDznd="92@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 689.67 305.33 L 689.67 368.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv10" d="M 1121.96 467.43 L 1121.96 580.55" stroke-width="1" zvalue="159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@2" LinkObjectIDznd="55@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1121.96 467.43 L 1121.96 580.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv10" d="M 806.44 714.69 L 806.44 656.67 L 689.65 656.67" stroke-width="1" zvalue="160"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="120" MaxPinNum="2"/>
   </metadata>
  <path d="M 806.44 714.69 L 806.44 656.67 L 689.65 656.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv10" d="M 736.27 343.11 L 689.67 343.11" stroke-width="1" zvalue="191"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@0" LinkObjectIDznd="63" MaxPinNum="2"/>
   </metadata>
  <path d="M 736.27 343.11 L 689.67 343.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv10" d="M 569.85 714.42 L 569.85 658 L 689.65 658" stroke-width="1" zvalue="192"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="120" MaxPinNum="2"/>
   </metadata>
  <path d="M 569.85 714.42 L 569.85 658 L 689.65 658" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="kv10" d="M 1497.64 697.19 L 1497.67 550.1" stroke-width="1" zvalue="197"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="199@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1497.64 697.19 L 1497.67 550.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv10" d="M 1547.97 573.85 L 1497.66 573.85" stroke-width="1" zvalue="201"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@0" LinkObjectIDznd="198" MaxPinNum="2"/>
   </metadata>
  <path d="M 1547.97 573.85 L 1497.66 573.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="kv10" d="M 1497.67 512.5 L 1497.67 467.43" stroke-width="1" zvalue="202"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@0" LinkObjectIDznd="253@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1497.67 512.5 L 1497.67 467.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv10" d="M 1614.44 714.69 L 1614.44 656.67 L 1497.65 656.67" stroke-width="1" zvalue="205"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="198" MaxPinNum="2"/>
   </metadata>
  <path d="M 1614.44 714.69 L 1614.44 656.67 L 1497.65 656.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv10" d="M 1377.85 714.42 L 1377.85 658 L 1497.65 658" stroke-width="1" zvalue="206"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="198" MaxPinNum="2"/>
   </metadata>
  <path d="M 1377.85 714.42 L 1377.85 658 L 1497.65 658" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv10" d="M 1495.77 374.08 L 1495.77 467.43" stroke-width="1" zvalue="218"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="253@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1495.77 374.08 L 1495.77 467.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="kv10" d="M 1687.91 289.89 L 1687.91 202.59" stroke-width="1" zvalue="225"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@0" LinkObjectIDznd="7@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1687.91 289.89 L 1687.91 202.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv10" d="M 1687.11 161.83 L 1687.11 120.53" stroke-width="1" zvalue="226"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="7@0" LinkObjectIDznd="232@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1687.11 161.83 L 1687.11 120.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="193">
   <use class="kv10" height="30" transform="rotate(0,689.638,719.979) scale(1.54536,1.54536) translate(-235.194,-245.901)" width="30" x="666.4578529557438" xlink:href="#Generator:发电机_0" y="696.7988095053232" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449503494150" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449503494150"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,689.638,719.979) scale(1.54536,1.54536) translate(-235.194,-245.901)" width="30" x="666.4578529557438" y="696.7988095053232"/></g>
  <g id="197">
   <use class="kv10" height="30" transform="rotate(0,1497.64,719.979) scale(1.54536,1.54536) translate(-520.338,-245.901)" width="30" x="1474.457852955744" xlink:href="#Generator:发电机_0" y="696.7988095053232" zvalue="198"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449503887366" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449503887366"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1497.64,719.979) scale(1.54536,1.54536) translate(-520.338,-245.901)" width="30" x="1474.457852955744" y="696.7988095053232"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="186">
   <use class="kv10" height="29" transform="rotate(0,569.854,734.619) scale(1.41717,-1.41717) translate(-161.49,-1246.94)" width="30" x="548.5959806595417" xlink:href="#Accessory:PT12321_0" y="714.0702610846947" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449503428614" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,569.854,734.619) scale(1.41717,-1.41717) translate(-161.49,-1246.94)" width="30" x="548.5959806595417" y="714.0702610846947"/></g>
  <g id="108">
   <use class="kv10" height="30" transform="rotate(0,806.508,734.444) scale(1.34815,1.34815) translate(-203.052,-184.442)" width="30" x="786.286111111111" xlink:href="#Accessory:PT789_0" y="714.2222222222223" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449503363078" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,806.508,734.444) scale(1.34815,1.34815) translate(-203.052,-184.442)" width="30" x="786.286111111111" y="714.2222222222223"/></g>
  <g id="101">
   <use class="kv10" height="26" transform="rotate(90,755.25,573.987) scale(-1.44231,1.44231) translate(-1276.24,-170.273)" width="12" x="746.5961538461538" xlink:href="#Accessory:设备233_0" y="555.2371794871794" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449503297542" ObjectName="#1发电机附属"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,755.25,573.987) scale(-1.44231,1.44231) translate(-1276.24,-170.273)" width="12" x="746.5961538461538" y="555.2371794871794"/></g>
  <g id="181">
   <use class="kv10" height="26" transform="rotate(90,758,343.317) scale(-2.07672,2.05128) translate(-1116.54,-162.284)" width="12" x="745.5396825396824" xlink:href="#Accessory:设备233_0" y="316.6507936507937" zvalue="190"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449503625222" ObjectName="10kV木笼河三四级线接地"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,758,343.317) scale(-2.07672,2.05128) translate(-1116.54,-162.284)" width="12" x="745.5396825396824" y="316.6507936507937"/></g>
  <g id="196">
   <use class="kv10" height="29" transform="rotate(0,1377.85,734.619) scale(1.41717,-1.41717) translate(-399.34,-1246.94)" width="30" x="1356.595980659542" xlink:href="#Accessory:PT12321_0" y="714.0702610846947" zvalue="200"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449503821829" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1377.85,734.619) scale(1.41717,-1.41717) translate(-399.34,-1246.94)" width="30" x="1356.595980659542" y="714.0702610846947"/></g>
  <g id="191">
   <use class="kv10" height="30" transform="rotate(0,1614.51,734.444) scale(1.34815,1.34815) translate(-411.711,-184.442)" width="30" x="1594.286111111111" xlink:href="#Accessory:PT789_0" y="714.2222222222223" zvalue="203"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449503756293" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1614.51,734.444) scale(1.34815,1.34815) translate(-411.711,-184.442)" width="30" x="1594.286111111111" y="714.2222222222223"/></g>
  <g id="190">
   <use class="kv10" height="26" transform="rotate(90,1563.25,573.987) scale(-1.44231,1.44231) translate(-2644.45,-170.273)" width="12" x="1554.596153846154" xlink:href="#Accessory:设备233_0" y="555.2371794871794" zvalue="204"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449503690758" ObjectName="#2发电机附属"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1563.25,573.987) scale(-1.44231,1.44231) translate(-2644.45,-170.273)" width="12" x="1554.596153846154" y="555.2371794871794"/></g>
  <g id="207">
   <use class="kv10" height="18" transform="rotate(0,1498.24,351.889) scale(2.65432,-2.65432) translate(-921.38,-469.572)" width="15" x="1478.333333333333" xlink:href="#Accessory:PT8_0" y="328.0000000000001" zvalue="213"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449504149510" ObjectName="10kV母线PT"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1498.24,351.889) scale(2.65432,-2.65432) translate(-921.38,-469.572)" width="15" x="1478.333333333333" y="328.0000000000001"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="55">
   <use class="kv10" height="30" transform="rotate(0,1121.83,601.833) scale(1.46748,1.47778) translate(-350.825,-187.411)" width="28" x="1101.284749097668" xlink:href="#EnergyConsumer:站用变DY接地_0" y="579.6666666666667" zvalue="157"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449503232006" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1121.83,601.833) scale(1.46748,1.47778) translate(-350.825,-187.411)" width="28" x="1101.284749097668" y="579.6666666666667"/></g>
  <g id="153">
   <use class="kv10" height="30" transform="rotate(0,1687.75,314.5) scale(1.69643,1.70833) translate(-683.116,-119.777)" width="28" x="1664" xlink:href="#EnergyConsumer:站用变DY接地_0" y="288.875" zvalue="222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449503952902" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1687.75,314.5) scale(1.69643,1.70833) translate(-683.116,-119.777)" width="28" x="1664" y="288.875"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="7">
   <use class="kv10" height="30" transform="rotate(0,1687.11,182.917) scale(3.89103,1.4055) translate(-1210.16,-46.691)" width="30" x="1628.745610127697" xlink:href="#Disconnector:跌落刀闸_0" y="161.834974364315" zvalue="224"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449504280582" ObjectName="#2站用变刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449504280582"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1687.11,182.917) scale(3.89103,1.4055) translate(-1210.16,-46.691)" width="30" x="1628.745610127697" y="161.834974364315"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="232">
   <use class="kv10" height="30" transform="rotate(0,1686.93,98.5278) scale(6.34921,1.48148) translate(-1402.52,-24.7993)" width="7" x="1664.70981733304" xlink:href="#ACLineSegment:线路_0" y="76.30555534362799" zvalue="227"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449504215046" ObjectName="至勐嘎河四级电站线"/>
   <cge:TPSR_Ref TObjectID="6192449504215046_5066549580988418"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1686.93,98.5278) scale(6.34921,1.48148) translate(-1402.52,-24.7993)" width="7" x="1664.70981733304" y="76.30555534362799"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="167">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="167" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,155.611,294.917) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="299.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123801268230" ObjectName="F"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="19" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,333.222,295.917) scale(1,1) translate(0,0)" writing-mode="lr" x="333.38" xml:space="preserve" y="300.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123801333764" ObjectName="F"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,150.222,369.917) scale(1,1) translate(0,0)" writing-mode="lr" x="150.38" xml:space="preserve" y="374.83" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="81">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="81" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,155.611,319.917) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="324.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123801137156" ObjectName="F"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,333.222,320.917) scale(1,1) translate(0,0)" writing-mode="lr" x="333.38" xml:space="preserve" y="325.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123801202695" ObjectName="F"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="78" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.611,412.139) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="417.05" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,155.611,343.917) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="348.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123801137156" ObjectName="F"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,335.611,342.917) scale(1,1) translate(0,0)" writing-mode="lr" x="335.77" xml:space="preserve" y="347.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123801137156" ObjectName="F"/>
   </metadata>
  </g>
  <g id="130">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.611,434.139) scale(1,1) translate(0,0)" writing-mode="lr" x="155.77" xml:space="preserve" y="439.05" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="坝下水位"/>
   </metadata>
  </g>
  <g id="135">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,331.611,412.139) scale(1,1) translate(0,0)" writing-mode="lr" x="331.77" xml:space="preserve" y="417.05" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="雨量采集"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="2" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,689.669,184.944) scale(1,1) translate(0,0)" writing-mode="lr" x="689.2" xml:space="preserve" y="189.72" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123795566596" ObjectName="P"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="20" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,689.669,200.944) scale(1,1) translate(0,0)" writing-mode="lr" x="689.2" xml:space="preserve" y="205.72" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123795632132" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="21" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,691.669,221.944) scale(1,1) translate(0,0)" writing-mode="lr" x="691.2" xml:space="preserve" y="226.72" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123795697668" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="24">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="24" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,687.638,793.66) scale(1,1) translate(0,0)" writing-mode="lr" x="687.09" xml:space="preserve" y="799.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123793862660" ObjectName="P"/>
   </metadata>
  </g>
  <g id="30">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="30" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,688.638,812.66) scale(1,1) translate(0,6.17963e-13)" writing-mode="lr" x="688.09" xml:space="preserve" y="818.9400000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123793928196" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="31" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,689.638,836.66) scale(1,1) translate(0,0)" writing-mode="lr" x="689.09" xml:space="preserve" y="842.9400000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123793993733" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="32" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,693.819,858.979) scale(1,1) translate(0,0)" writing-mode="lr" x="693.27" xml:space="preserve" y="865.26" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123794059268" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="33" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1492.64,785.66) scale(1,1) translate(0,0)" writing-mode="lr" x="1492.09" xml:space="preserve" y="791.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123796615172" ObjectName="P"/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="35" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1495.64,802.66) scale(1,1) translate(0,6.10192e-13)" writing-mode="lr" x="1495.09" xml:space="preserve" y="808.9400000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123796680708" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="36">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="36" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1494.64,824.66) scale(1,1) translate(0,0)" writing-mode="lr" x="1494.09" xml:space="preserve" y="830.9400000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123796746244" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="37" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1503.82,842.979) scale(1,1) translate(0,0)" writing-mode="lr" x="1503.27" xml:space="preserve" y="849.26" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123796811780" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="38" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1415.25,304.927) scale(1,1) translate(0,0)" writing-mode="lr" x="1414.79" xml:space="preserve" y="309.71" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123795042308" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="42">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="42" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1415.25,328.927) scale(1,1) translate(0,0)" writing-mode="lr" x="1414.79" xml:space="preserve" y="333.71" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123795107844" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="43">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="43" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1414.25,346.927) scale(1,1) translate(0,0)" writing-mode="lr" x="1413.79" xml:space="preserve" y="351.71" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123795173380" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="44">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="44" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1416.25,368.427) scale(1,1) translate(0,0)" writing-mode="lr" x="1415.79" xml:space="preserve" y="373.21" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123795304452" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="45">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="45" prefix="F:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1413.91,388.927) scale(1,1) translate(0,0)" writing-mode="lr" x="1413.44" xml:space="preserve" y="393.71" zvalue="1">F:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123795435524" ObjectName="F"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="86">
   <use height="30" transform="rotate(0,330.673,221.107) scale(0.708333,0.665547) translate(131.784,106.095)" width="30" x="320.05" xlink:href="#State:红绿圆(方形)_0" y="211.12" zvalue="242"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374883815428" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,330.673,221.107) scale(0.708333,0.665547) translate(131.784,106.095)" width="30" x="320.05" y="211.12"/></g>
  <g id="85">
   <use height="30" transform="rotate(0,235.048,221.107) scale(0.708333,0.665547) translate(92.4093,106.095)" width="30" x="224.42" xlink:href="#State:红绿圆(方形)_0" y="211.12" zvalue="243"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562952994291719" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,235.048,221.107) scale(0.708333,0.665547) translate(92.4093,106.095)" width="30" x="224.42" y="211.12"/></g>
 </g>
</svg>