<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549581119490" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id=":线路带避雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15.16666666666667" xlink:href="#terminal" y="39.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.16022336769755" x2="15.16022336769755" y1="39.75" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="6.5" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="8.5" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="7.5" y1="32.25" y2="32.25"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6.04,21.17) scale(1,1) translate(0,0)" width="6.08" x="3" y="14"/>
   <path d="M 15 9.25 L 6 9.25 L 6 21.25 L 6 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="7" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="5" y1="22" y2="16"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV昔马简易变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="285" x="36" xlink:href="logo.png" y="43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,178.5,73) scale(1,1) translate(0,0)" writing-mode="lr" x="178.5" xml:space="preserve" y="76.5" zvalue="54"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,180.5,73.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="180.5" xml:space="preserve" y="82.69" zvalue="55">35kV昔马简易变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="612" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,88.5,314) scale(1,1) translate(0,0)" width="97" x="40" y="302" zvalue="1059"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,88.5,314) scale(1,1) translate(0,0)" writing-mode="lr" x="88.5" xml:space="preserve" y="318.5" zvalue="1059">全站公用</text>
  <line fill="none" id="67" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382" x2="382" y1="11" y2="1041" zvalue="58"/>
  <line fill="none" id="666" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="146.8704926140824" y2="146.8704926140824" zvalue="1056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="159" y2="159"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="159" y2="159"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="159" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="185" y2="185"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="185" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="209.25" y2="209.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="209.25" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="232" y2="232"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="232" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="190" y1="277.5" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="9" x2="9" y1="254.75" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="254.75" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="371" y1="277.5" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="190" x2="190" y1="254.75" y2="277.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371" x2="371" y1="254.75" y2="277.5"/>
  <line fill="none" id="613" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="616.8704926140824" y2="616.8704926140824" zvalue="1058"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="439.6666435058594" y2="439.6666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="477.1566435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="439.6666435058594" y2="477.1566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="477.1567435058594" y2="477.1567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="477.1567435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="501.3253435058594" y2="501.3253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="525.4939435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="501.3253435058594" y2="525.4939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="525.4939835058594" y2="525.4939835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="549.6625835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="525.4939835058594" y2="549.6625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="549.6627435058595" y2="549.6627435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="549.6627435058595" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="109.5670617373469" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.22236173734677" x2="61.22236173734677" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5670617373469" x2="109.5670617373469" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="171.6756617373469" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.5673617373468" x2="109.5673617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6756617373469" x2="171.6756617373469" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="234.9999617373469" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.6751617373468" x2="171.6751617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9999617373469" x2="234.9999617373469" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="297.1081617373468" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="234.9998617373468" x2="234.9998617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="573.8313435058594" y2="573.8313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="359.2164617373469" y1="597.9999435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="297.1081617373468" x2="297.1081617373468" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="359.2164617373469" x2="359.2164617373469" y1="573.8313435058594" y2="597.9999435058594"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="999.0816" y2="1027"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="609" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,952) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="958" zvalue="1062">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="608" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50,986) scale(1,1) translate(0,0)" writing-mode="lr" x="50" xml:space="preserve" y="992" zvalue="1063">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="607" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232,986) scale(1,1) translate(0,0)" writing-mode="lr" x="232" xml:space="preserve" y="992" zvalue="1064">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="606" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="49" xml:space="preserve" y="1020" zvalue="1065">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="605" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="231" xml:space="preserve" y="1020" zvalue="1066">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="602" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.5,646.5) scale(1,1) translate(0,0)" writing-mode="lr" x="73.5" xml:space="preserve" y="651" zvalue="1069">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="601" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,206.399,313.841) scale(1,1) translate(0,0)" writing-mode="lr" x="206.4" xml:space="preserve" y="318.34" zvalue="1070">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="600" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,311.399,313.841) scale(1,1) translate(0,0)" writing-mode="lr" x="311.4" xml:space="preserve" y="318.34" zvalue="1071">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="599" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.667,456.5) scale(1,1) translate(0,0)" writing-mode="lr" x="196.6666666666665" xml:space="preserve" y="461" zvalue="1072">35kV  母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="598" stroke="rgb(255,255,255)" text-anchor="middle" x="254.34375" xml:space="preserve" y="454" zvalue="1073">10kV    母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="598" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="254.34375" xml:space="preserve" y="470" zvalue="1073">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="596" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85,489.5) scale(1,1) translate(0,0)" writing-mode="lr" x="85" xml:space="preserve" y="494" zvalue="1075">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="595" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85,515) scale(1,1) translate(0,0)" writing-mode="lr" x="85" xml:space="preserve" y="519.5" zvalue="1076">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="594" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85,540.5) scale(1,1) translate(0,0)" writing-mode="lr" x="85" xml:space="preserve" y="545" zvalue="1077">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="593" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84,565) scale(1,1) translate(0,0)" writing-mode="lr" x="84" xml:space="preserve" y="569.5" zvalue="1078">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="592" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85,591.5) scale(1,1) translate(0,0)" writing-mode="lr" x="85" xml:space="preserve" y="596" zvalue="1079">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.054,953) scale(1,1) translate(0,0)" writing-mode="lr" x="227.05" xml:space="preserve" y="959" zvalue="1080">XiMaJianYi-01-2020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,143.054,986) scale(1,1) translate(0,0)" writing-mode="lr" x="143.05" xml:space="preserve" y="992" zvalue="1081">段勇</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,323.054,986) scale(1,1) translate(0,0)" writing-mode="lr" x="323.05" xml:space="preserve" y="992" zvalue="1082">20210105</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47,173) scale(1,1) translate(0,0)" writing-mode="lr" x="47" xml:space="preserve" y="178.5" zvalue="1083">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227,173) scale(1,1) translate(0,0)" writing-mode="lr" x="227" xml:space="preserve" y="178.5" zvalue="1084">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.6875,197.25) scale(1,1) translate(0,0)" writing-mode="lr" x="54.69" xml:space="preserve" y="201.75" zvalue="1085">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.125,197.25) scale(1,1) translate(0,0)" writing-mode="lr" x="234.13" xml:space="preserve" y="201.75" zvalue="1086">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.1875,245) scale(1,1) translate(0,0)" writing-mode="lr" x="54.19" xml:space="preserve" y="249.5" zvalue="1087">35kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" x="1152.7421875" xml:space="preserve" y="430.0357142857143" zvalue="1425">#1主变      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1152.7421875" xml:space="preserve" y="446.0357142857143" zvalue="1425">2000kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1063.9,125) scale(1,1) translate(0,0)" writing-mode="lr" x="1063.9" xml:space="preserve" y="129.5" zvalue="1427">35kV弄昔线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1027.81,308.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1027.81" xml:space="preserve" y="313.07" zvalue="1429">3016</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1055.18,850.944) scale(1,1) translate(-2.26526e-13,0)" writing-mode="lr" x="1055.18" xml:space="preserve" y="855.4400000000001" zvalue="1437">10kV勐昔线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1036.78,692.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1036.78" xml:space="preserve" y="697.28" zvalue="1439">0016</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1085.91,601.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1085.91" xml:space="preserve" y="606.17" zvalue="1442">001</text>
 </g>
 <g id="ButtonClass">
  <g href="500kV德宏变_全站公用.svg"><rect fill-opacity="0" height="24" width="97" x="40" y="302" zvalue="1059"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="231">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="231" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,140.222,515.167) scale(1,1) translate(-2.51651e-14,0)" writing-mode="lr" x="140.34" xml:space="preserve" y="520.08" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="232">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="232" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,140.222,537.667) scale(1,1) translate(0,0)" writing-mode="lr" x="140.34" xml:space="preserve" y="542.58" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="233">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="233" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,140.222,562.167) scale(1,1) translate(-2.51651e-14,0)" writing-mode="lr" x="140.34" xml:space="preserve" y="567.08" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,140.222,489.667) scale(1,1) translate(-2.51651e-14,0)" writing-mode="lr" x="140.34" xml:space="preserve" y="494.58" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="236">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,205.222,515.167) scale(1,1) translate(-3.9598e-14,0)" writing-mode="lr" x="205.34" xml:space="preserve" y="520.08" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="237">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="237" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,205.222,537.667) scale(1,1) translate(0,0)" writing-mode="lr" x="205.34" xml:space="preserve" y="542.58" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="238">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="238" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,205.222,562.167) scale(1,1) translate(-3.9598e-14,0)" writing-mode="lr" x="205.34" xml:space="preserve" y="567.08" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="235">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="235" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,205.222,489.667) scale(1,1) translate(-3.9598e-14,0)" writing-mode="lr" x="205.34" xml:space="preserve" y="494.58" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,328.222,515.167) scale(1,1) translate(-6.69094e-14,0)" writing-mode="lr" x="328.34" xml:space="preserve" y="520.08" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="241">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="241" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,328.222,537.667) scale(1,1) translate(0,0)" writing-mode="lr" x="328.34" xml:space="preserve" y="542.58" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="242">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="242" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,328.222,562.167) scale(1,1) translate(-6.69094e-14,0)" writing-mode="lr" x="328.34" xml:space="preserve" y="567.08" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="239">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="239" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,328.222,489.667) scale(1,1) translate(-6.69094e-14,0)" writing-mode="lr" x="328.34" xml:space="preserve" y="494.58" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,146.222,172.167) scale(1,1) translate(0,0)" writing-mode="lr" x="146.42" xml:space="preserve" y="177.08" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,325.222,173.167) scale(1,1) translate(0,0)" writing-mode="lr" x="325.42" xml:space="preserve" y="178.08" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="3" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,910.357,286.5) scale(1,1) translate(0,0)" writing-mode="lr" x="909.78" xml:space="preserve" y="292.78" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123810770948" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="10" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,910.357,323.5) scale(1,1) translate(0,0)" writing-mode="lr" x="909.78" xml:space="preserve" y="329.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123810836484" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="15" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,910.357,360.5) scale(1,1) translate(0,0)" writing-mode="lr" x="909.78" xml:space="preserve" y="366.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123811033092" ObjectName="HIa"/>
   </metadata>
  </g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="1">
   <g id="10">
    <use class="kv35" height="30" transform="rotate(0,1063.86,435) scale(3.75,3.66667) translate(-747.162,-276.364)" width="24" x="1018.86" xlink:href="#PowerTransformer2:可调不带中性点_0" y="380" zvalue="1424"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874418421764" ObjectName="35"/>
    </metadata>
   </g>
   <g id="11">
    <use class="kv10" height="30" transform="rotate(0,1063.86,435) scale(3.75,3.66667) translate(-747.162,-276.364)" width="24" x="1018.86" xlink:href="#PowerTransformer2:可调不带中性点_1" y="380" zvalue="1424"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874418487300" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399441383428" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399441383428"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1063.86,435) scale(3.75,3.66667) translate(-747.162,-276.364)" width="24" x="1018.86" y="380"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="7">
   <use class="kv35" height="30" transform="rotate(0,1063.74,309.571) scale(1.95238,1.95238) translate(-511.753,-136.725)" width="15" x="1049.092739564962" xlink:href="#Disconnector:令克_0" y="280.2857142857143" zvalue="1428"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449505787910" ObjectName="#1主变35kV高压侧3016跌落式熔断器"/>
   <cge:TPSR_Ref TObjectID="6192449505787910"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1063.74,309.571) scale(1.95238,1.95238) translate(-511.753,-136.725)" width="15" x="1049.092739564962" y="280.2857142857143"/></g>
  <g id="11">
   <use class="kv10" height="30" transform="rotate(0,1063.78,693.778) scale(1.11111,1.11111) translate(-105.544,-67.7111)" width="15" x="1055.444444444444" xlink:href="#Disconnector:令克_0" y="677.1111111111111" zvalue="1438"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449505918982" ObjectName="#1主变10kV低压侧0016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449505918982"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1063.78,693.778) scale(1.11111,1.11111) translate(-105.544,-67.7111)" width="15" x="1055.444444444444" y="677.1111111111111"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="8">
   <path class="kv35" d="M 1063.9 383.95 L 1063.9 333.49" stroke-width="1" zvalue="1429"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="7@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.9 383.95 L 1063.9 333.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="kv35" d="M 1063.9 283.7 L 1063.9 244.32" stroke-width="1" zvalue="1430"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="7@0" LinkObjectIDznd="4@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.9 283.7 L 1063.9 244.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv10" d="M 1063.69 707.39 L 1063.69 741.54" stroke-width="1" zvalue="1440"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="11@1" LinkObjectIDznd="13@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.69 707.39 L 1063.69 741.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv10" d="M 1063.86 486.5 L 1063.86 588.32" stroke-width="1" zvalue="1442"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@1" LinkObjectIDznd="17@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1063.86 486.5 L 1063.86 588.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv10" d="M 1064.02 616.99 L 1064.02 679.06" stroke-width="1" zvalue="1443"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="17@1" LinkObjectIDznd="11@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1064.02 616.99 L 1064.02 679.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="17">
   <use class="kv10" height="20" transform="rotate(0,1063.91,602.667) scale(1.66667,1.5) translate(-422.232,-195.889)" width="10" x="1055.579365079365" xlink:href="#Breaker:开关_0" y="587.6666666666666" zvalue="1441"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924466769925" ObjectName="#1主变10kV低压侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924466769925"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1063.91,602.667) scale(1.66667,1.5) translate(-422.232,-195.889)" width="10" x="1055.579365079365" y="587.6666666666666"/></g>
 </g>
</svg>