<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549586886658" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2019.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.08333333333333" x2="9.833333333333332" y1="16.5" y2="18.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14" y1="9.75" y2="16.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="11" y1="11.58333333333333" y2="9.666666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="9.75" y1="11.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8" x2="15.08333333333333" y1="8.416666666666668" y2="25.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_1" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.96,13) scale(1,1) translate(0,0)" width="4.42" x="12.75" y="9"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="5.416666666666668" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_2" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="20" y1="6" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="10.08333333333333" y1="6.000000000000004" y2="25.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="EnergyConsumer:YY站用变_0" viewBox="0,0,32,40">
   <use terminal-index="0" type="0" x="16" xlink:href="#terminal" y="0.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="17" y1="39" y2="37"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="16" y1="30" y2="39"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="15" y1="39" y2="37"/>
   <ellipse cx="16.04" cy="9.220000000000001" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.97" cy="21.39" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067018" x2="16.03525619067018" y1="18.1" y2="22.20894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067017" x2="10.66666666666667" y1="22.23394833233988" y2="25.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.06302903072498" x2="21.16666666666667" y1="22.18990500916851" y2="25.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067018" x2="16.03525619067018" y1="4.85" y2="8.958948332339871"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067017" x2="12.05" y1="8.983948332339878" y2="12"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.06302903072498" x2="20.15" y1="8.939905009168511" y2="11.75"/>
  </symbol>
  <symbol id="EnergyConsumer:D-YY变压器_0" viewBox="0,0,40,45">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="3.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="20" y1="14.75" y2="7.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16" x2="24" y1="14.75" y2="14.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="16" y1="7.75" y2="14.75"/>
   <ellipse cx="13.04" cy="25.56" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="27.06" cy="25.47" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.95192285733684" x2="12.95192285733684" y1="21.43333333333333" y2="25.5422816656732"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.95192285733684" x2="8.966666666666663" y1="25.56728166567321" y2="28.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.97969569739165" x2="17.06666666666667" y1="25.52323834250185" y2="28.33333333333334"/>
   <ellipse cx="19.96" cy="11.81" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="28.03525619067017" x2="28.03525619067017" y1="21.6" y2="25.70894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="28.03525619067017" x2="24.05" y1="25.73394833233987" y2="28.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="28.06302903072498" x2="32.15" y1="25.68990500916851" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.08333333333334" x2="27.08333333333334" y1="43.08333333333333" y2="41.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.08333333333334" x2="29.08333333333334" y1="43.08333333333333" y2="41.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.08333333333334" x2="28.08333333333334" y1="34.08333333333333" y2="43.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="12" y1="43.33333333333334" y2="41.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="13" y1="34.33333333333334" y2="43.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="14" y1="43.33333333333334" y2="41.33333333333334"/>
  </symbol>
  <symbol id="Accessory:五卷PT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="24.60905664884498" xlink:href="#terminal" y="8.261278399766271"/>
   <path d="M 5.11667 17.3167 L 5.11667 21.3167 L 8.11667 19.3167 L 5.11667 17.3167" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.75" x2="24.5990682626599" y1="18.54585360194742" y2="18.54585360194742"/>
   <path d="M 10 25 L 4 25 L 4 32" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.65990464876007" x2="24.65990464876007" y1="14.7557301451573" y2="22.82880868476295"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.65990464876007" x2="24.65990464876007" y1="11.57954813535237" y2="8.27794506643294"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.67943360505483" x2="24.67943360505483" y1="25.80025144679045" y2="32.48154965466559"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.57943360505483" x2="24.57943360505483" y1="22.78753864640143" y2="22.78753864640143"/>
   <ellipse cx="13.48" cy="11.95" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="28.04255077401041" x2="21.20251205906045" y1="22.87087197973477" y2="22.87087197973477"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.57943360505483" x2="24.57943360505483" y1="11.64462828879835" y2="11.64462828879835"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="27.95921744067709" x2="21.11917872572711" y1="25.88279152351342" y2="25.88279152351342"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="27.95921744067708" x2="21.11917872572711" y1="11.64462828879838" y2="11.64462828879838"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="27.95921744067708" x2="21.11917872572711" y1="14.73988116591035" y2="14.73988116591035"/>
   <ellipse cx="9.890000000000001" cy="25.37" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="26.87588410734375" x2="22.59906826265991" y1="32.54945819018009" y2="32.54945819018009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="26.12588410734375" x2="23.51573492932657" y1="33.79945819018008" y2="33.79945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="25.62588410734375" x2="24.18240159599324" y1="35.29945819018008" y2="35.29945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="14.21252026861409" y2="11.73744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="11.73744029990987" y2="10.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="11.73744029990987" y2="10.49990031555776"/>
   <ellipse cx="13.48" cy="18.95" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.48" cy="11.95" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="18.73744029990987" y2="17.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659912" x2="8.849068262659907" y1="11.73744029990987" y2="10.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.44906826265991" x2="6.44906826265991" y1="14.21252026861409" y2="11.73744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="18.73744029990987" y2="17.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659907" x2="4.049068262659912" y1="11.73744029990987" y2="10.49990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="21.21252026861409" y2="18.73744029990989"/>
   <ellipse cx="6.48" cy="18.95" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.98240159599324" x2="9.98240159599324" y1="27.74585360194743" y2="25.27077363324322"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982401595993244" x2="12.38240159599323" y1="25.2707736332432" y2="24.0332336488911"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982401595993235" x2="7.582401595993243" y1="25.2707736332432" y2="24.0332336488911"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.125884107343754" x2="1.849068262659905" y1="32.04945819018009" y2="32.04945819018009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.375884107343754" x2="2.765734929326577" y1="33.29945819018008" y2="33.29945819018008"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.875884107343754" x2="3.432401595993237" y1="34.79945819018008" y2="34.79945819018008"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="12.0194189818494"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="5.416666666666668" y2="12.02321291373541"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.92126160277786" x2="6.666666666666666" y1="12.10207526123773" y2="18.50000000000001"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.18646053143751" x2="24.5" y1="12.18904818695178" y2="19.00000000000001"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 7.33333 43.5833 L 23 43.5833 L 15 31.5 z" fill-opacity="0" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="Compensator:10kV电容器_0" viewBox="0,0,24,40">
   <use terminal-index="0" type="0" x="12" xlink:href="#terminal" y="4.699999999999999"/>
   <path d="M 12 20.1 L 17.85 20.1 L 17.85 24.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="4.749074074074073" y1="35.35833333333333" y2="35.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.72129629629629" x2="4.72129629629629" y1="33.13611111111111" y2="35.37685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="24.85833333333333" y2="30.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.258333333333324" x2="0.258333333333324" y1="22.10833333333333" y2="33.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.75833333333333" x2="4.75833333333333" y1="22.35833333333333" y2="20.15462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="31.85833333333333" y2="35.37685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="20.04351851851851" y2="24.775"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.024239280774554" x2="4.024239280774554" y1="39.35833333333333" y2="37.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.786111111111114" x2="12.00833333333334" y1="20.10833333333333" y2="20.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="35.35833333333333" y2="39.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.84166666666666" x2="4.024239280774546" y1="39.35833333333333" y2="39.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.84166666666667" x2="19.84166666666667" y1="37.35833333333333" y2="39.35833333333333"/>
   <path d="M 4.75833 25.9572 A 2.96392 1.81747 180 0 1 4.75833 22.3223" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 4.75833 29.5921 A 2.96392 1.81747 180 0 1 4.75833 25.9572" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 4.75833 33.1271 A 2.96392 1.81747 180 0 1 4.75833 29.4921" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="30.85833333333333" y2="30.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="31.85833333333333" y2="31.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="12" y1="4.5" y2="7.166666666666668"/>
   <path d="M 7.26667 12.1 A 4.91667 4.75 -450 1 0 12.0167 7.18333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 7.33333 12.0833 L 12 12.1667 L 12 20.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510778" x2="17.14262023217246" y1="27.75922956383932" y2="26.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="29.36667381975841" y2="30.33114037330986"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.50700202690252" x2="18.32283029297954" y1="31.0857461490052" y2="31.0857461490052"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.28450340888154" x2="18.47116270499357" y1="30.7156109584975" y2="30.7156109584975"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.98783858485353" x2="18.76782752902158" y1="30.34547576798984" y2="30.34547576798984"/>
   <rect fill-opacity="0" height="4.29" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,17.83,27.22) scale(1,1) translate(0,0)" width="2.33" x="16.67" y="25.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="24.56666666666668" y2="25.08015580397416"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510781" x2="18.53565505804314" y1="27.75922956383932" y2="26.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="25.08015580397418" y2="27.73243882624067"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变1节点_0" viewBox="0,0,26,38">
   <use terminal-index="0" type="0" x="9.116666666666667" xlink:href="#terminal" y="0.2500000000000071"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.45" x2="25.45" y1="30" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.45" x2="24.45" y1="31" y2="31"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="33" y2="38"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.45" x2="23.45" y1="32" y2="32"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="23" y1="24" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="19" y1="24" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="23" y1="24" y2="24"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="2.25" y2="0.25"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="9" y1="30" y2="37"/>
   <ellipse cx="9.210000000000001" cy="10.97" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="9.201922857336841" y1="6.6" y2="10.70894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="5.216666666666669" y1="10.73394833233988" y2="13.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.22969569739165" x2="13.31666666666667" y1="10.68990500916851" y2="13.5"/>
   <ellipse cx="9.210000000000001" cy="24.47" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="9.201922857336841" y1="20.1" y2="24.20894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="5.216666666666669" y1="24.23394833233988" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.22969569739165" x2="13.31666666666667" y1="24.18990500916851" y2="27"/>
  </symbol>
  <symbol id=":线路带避雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15.16666666666667" xlink:href="#terminal" y="39.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.16022336769755" x2="15.16022336769755" y1="39.75" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="6.5" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="8.5" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="7.5" y1="32.25" y2="32.25"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6.04,21.17) scale(1,1) translate(0,0)" width="6.08" x="3" y="14"/>
   <path d="M 15 9.25 L 6 9.25 L 6 21.25 L 6 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="7" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="5" y1="22" y2="16"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV中亚硅厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="71.94" y="327" zvalue="31"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="1" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" width="72.88" x="71.94" y="327" zvalue="31"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" writing-mode="lr" x="108.38" xml:space="preserve" y="343.5" zvalue="31">信号一览</text>
  <image height="60" id="2" preserveAspectRatio="xMidYMid slice" width="269.25" x="66.95999999999999" xlink:href="logo.png" y="45.64"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.589,75.6429) scale(1,1) translate(0,0)" writing-mode="lr" x="201.59" xml:space="preserve" y="79.14" zvalue="138"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,203.298,75.3332) scale(1,1) translate(9.43293e-15,0)" writing-mode="lr" x="203.3" xml:space="preserve" y="84.33" zvalue="139">110kV中亚硅厂</text>
  <line fill="none" id="26" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="388.75" x2="388.75" y1="11" y2="1039.25" zvalue="6"/>
  <line fill="none" id="24" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.22692454998003" x2="331.8381846035992" y1="168.8779458827686" y2="168.8779458827686" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="927.8127909390723" y2="927.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="979.1522909390724" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="361.3219285714284" y1="927.8127909390723" y2="927.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="361.3219285714284" y1="979.1522909390724" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3219285714284" x2="361.3219285714284" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5593285714284" x2="279.5593285714284" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="279.5592285714284" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3213285714285" x2="361.3213285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5593285714284" x2="279.5593285714284" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="279.5592285714284" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3213285714285" x2="361.3213285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,202.374,957.386) scale(1,1) translate(-1.34729e-14,1.05089e-13)" writing-mode="lr" x="60.68" xml:space="preserve" y="963.39" zvalue="10">参考图号      ZhongYa-01-2019</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,132.899,994.755) scale(1,1) translate(-6.25312e-14,-1.52933e-12)" writing-mode="lr" x="70.40000000000001" xml:space="preserve" y="1000.75" zvalue="11">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,276.202,995.755) scale(1,1) translate(1.38222e-13,-1.53089e-12)" writing-mode="lr" x="207.5" xml:space="preserve" y="1001.75" zvalue="12">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.9923,1024.31) scale(1,1) translate(-6.10858e-14,-1.57527e-12)" writing-mode="lr" x="83.98999999999999" xml:space="preserve" y="1030.31" zvalue="13">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,280.836,1022.31) scale(1,1) translate(-4.58902e-14,1.12298e-13)" writing-mode="lr" x="206.67" xml:space="preserve" y="1028.31" zvalue="14">更新日期  </text>
  <line fill="none" id="17" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="54.60611355802337" x2="359.2173736116425" y1="623.6445306693694" y2="623.6445306693694" zvalue="15"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,97.6707,640.686) scale(1,1) translate(5.99964e-15,-1.38109e-13)" writing-mode="lr" x="97.67070806204492" xml:space="preserve" y="645.1863811688671" zvalue="17">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="172.1071428571429" y2="172.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="172.1071428571429" y2="172.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="290.6071428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="290.6071428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" x="139.5390625" xml:space="preserve" y="467.109375" zvalue="20">110kV母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="139.5390625" xml:space="preserve" y="483.109375" zvalue="20">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,502.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="507.1071428571429" zvalue="23">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,528.107) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="532.6071428571429" zvalue="24">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,553.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="558.1071428571429" zvalue="25">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,579.107) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="583.6071428571429" zvalue="26">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,604.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="609.1071428571429" zvalue="27">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.3214,186.107) scale(1,1) translate(0,0)" writing-mode="lr" x="54.32" xml:space="preserve" y="191.61" zvalue="28">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.571,186.107) scale(1,1) translate(0,0)" writing-mode="lr" x="240.57" xml:space="preserve" y="191.61" zvalue="29">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,65.0089,210.357) scale(1,1) translate(0,0)" writing-mode="lr" x="65.01000000000001" xml:space="preserve" y="214.86" zvalue="30">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,497.375,541.75) scale(1,1) translate(0,0)" writing-mode="lr" x="497.38" xml:space="preserve" y="546.25" zvalue="43">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1350.12,250.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1350.13" xml:space="preserve" y="255" zvalue="80">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1463.14,25.6442) scale(1,1) translate(0,0)" writing-mode="lr" x="1463.14" xml:space="preserve" y="30.14" zvalue="82">35kV梁中线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1472.24,162.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1472.24" xml:space="preserve" y="167.25" zvalue="84">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1336.28,442.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1336.28" xml:space="preserve" y="447.13" zvalue="87">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1490.4,227.615) scale(1,1) translate(0,0)" writing-mode="lr" x="1490.4" xml:space="preserve" y="232.12" zvalue="89">321</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1518.46,385.35) scale(1,1) translate(0,0)" writing-mode="lr" x="1518.46" xml:space="preserve" y="389.85" zvalue="91">302</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" x="1489.8125" xml:space="preserve" y="503.9879819980034" zvalue="99">#2环保变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1489.8125" xml:space="preserve" y="519.9879819980034" zvalue="99">2000kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" x="200.7890625" xml:space="preserve" y="468.359375" zvalue="114">35kV母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="200.7890625" xml:space="preserve" y="484.359375" zvalue="114">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,250.009,210.357) scale(1,1) translate(0,0)" writing-mode="lr" x="250.01" xml:space="preserve" y="214.86" zvalue="122">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1401.88,108.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1401.88" xml:space="preserve" y="112.75" zvalue="141">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1404.25,183.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1404.25" xml:space="preserve" y="187.75" zvalue="144">60</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1650.03,63.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1650.03" xml:space="preserve" y="67.63" zvalue="146">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1673.57,205.099) scale(1,1) translate(0,0)" writing-mode="lr" x="1673.57" xml:space="preserve" y="209.6" zvalue="148">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1587.66,164.782) scale(1,1) translate(0,0)" writing-mode="lr" x="1587.66" xml:space="preserve" y="169.28" zvalue="149">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1586.41,241.548) scale(1,1) translate(0,0)" writing-mode="lr" x="1586.41" xml:space="preserve" y="246.05" zvalue="150">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1461.16,366.032) scale(1,1) translate(0,0)" writing-mode="lr" x="1461.16" xml:space="preserve" y="370.53" zvalue="157">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1473.61,316.817) scale(1,1) translate(0,0)" writing-mode="lr" x="1473.61" xml:space="preserve" y="321.32" zvalue="161">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1626.21,385.35) scale(1,1) translate(0,0)" writing-mode="lr" x="1626.21" xml:space="preserve" y="389.85" zvalue="167">301</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" x="1598.6875" xml:space="preserve" y="502.7379819980034" zvalue="169">#1环保变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1598.6875" xml:space="preserve" y="518.7379819980034" zvalue="169">2000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1568.91,366.032) scale(1,1) translate(0,0)" writing-mode="lr" x="1568.91" xml:space="preserve" y="370.53" zvalue="172">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1581.36,316.817) scale(1,1) translate(0,0)" writing-mode="lr" x="1581.36" xml:space="preserve" y="321.32" zvalue="176">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1753.51,385.35) scale(1,1) translate(0,0)" writing-mode="lr" x="1753.51" xml:space="preserve" y="389.85" zvalue="182">303</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="161" stroke="rgb(255,255,255)" text-anchor="middle" x="1727.140625" xml:space="preserve" y="503.9879819980034" zvalue="184">#3环保变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="161" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1727.140625" xml:space="preserve" y="519.9879819980034" zvalue="184">6300kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1692.91,366.032) scale(1,1) translate(0,0)" writing-mode="lr" x="1692.91" xml:space="preserve" y="370.53" zvalue="187">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1708.65,315.817) scale(1,1) translate(0,0)" writing-mode="lr" x="1708.65" xml:space="preserve" y="320.32" zvalue="191">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,616.739,349.75) scale(1,1) translate(0,0)" writing-mode="lr" x="616.74" xml:space="preserve" y="354.25" zvalue="202">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,628.648,437.615) scale(1,1) translate(0,0)" writing-mode="lr" x="628.65" xml:space="preserve" y="442.12" zvalue="204">121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,540.125,310.25) scale(1,1) translate(0,0)" writing-mode="lr" x="540.13" xml:space="preserve" y="314.75" zvalue="208">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,542.5,385.25) scale(1,1) translate(0,0)" writing-mode="lr" x="542.5" xml:space="preserve" y="389.75" zvalue="212">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="177" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,615.845,519.664) scale(1,1) translate(0,0)" writing-mode="lr" x="615.85" xml:space="preserve" y="524.16" zvalue="216">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,541.25,481.782) scale(1,1) translate(0,0)" writing-mode="lr" x="541.25" xml:space="preserve" y="486.28" zvalue="220">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,780.547,346.375) scale(1,1) translate(0,0)" writing-mode="lr" x="780.55" xml:space="preserve" y="350.88" zvalue="223">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,808.071,487.099) scale(1,1) translate(0,0)" writing-mode="lr" x="808.0700000000001" xml:space="preserve" y="491.6" zvalue="225">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="182" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,753.414,469.282) scale(1,1) translate(0,-2.04628e-13)" writing-mode="lr" x="753.41" xml:space="preserve" y="473.78" zvalue="227">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,753.414,542.298) scale(1,1) translate(0,0)" writing-mode="lr" x="753.41" xml:space="preserve" y="546.8" zvalue="229">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,618.744,751.164) scale(1,1) translate(0,0)" writing-mode="lr" x="618.74" xml:space="preserve" y="755.66" zvalue="239">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="317" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,629.579,674.865) scale(1,1) translate(0,0)" writing-mode="lr" x="629.58" xml:space="preserve" y="679.37" zvalue="240">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,538.75,718.247) scale(1,1) translate(0,0)" writing-mode="lr" x="538.75" xml:space="preserve" y="722.75" zvalue="241">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,615.688,603.067) scale(1,1) translate(0,0)" writing-mode="lr" x="615.6900000000001" xml:space="preserve" y="607.5700000000001" zvalue="242">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,538.75,642.481) scale(1,1) translate(0,0)" writing-mode="lr" x="538.75" xml:space="preserve" y="646.98" zvalue="243">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" x="537.046875" xml:space="preserve" y="831.7986111111111" zvalue="244">#1炉变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="537.046875" xml:space="preserve" y="847.7986111111111" zvalue="244">13MVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,602.993,949.625) scale(1,1) translate(0,0)" writing-mode="lr" x="602.99" xml:space="preserve" y="954.13" zvalue="251">#1电炉</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,717.5,855.75) scale(1,1) translate(0,0)" writing-mode="lr" x="717.5" xml:space="preserve" y="860.25" zvalue="256">1010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" x="514.109375" xml:space="preserve" y="1026.381944444444" zvalue="258">#1电容补偿装置</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="514.109375" xml:space="preserve" y="1042.381944444444" zvalue="258">9600kVar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,544.171,907.493) scale(1,1) translate(0,0)" writing-mode="lr" x="544.17" xml:space="preserve" y="911.99" zvalue="261">3236</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,489,914.107) scale(1,1) translate(0,0)" writing-mode="lr" x="489" xml:space="preserve" y="918.61" zvalue="265">323</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,906.494,753.664) scale(1,1) translate(0,0)" writing-mode="lr" x="906.49" xml:space="preserve" y="758.16" zvalue="273">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="318" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,912.329,678.365) scale(1,1) translate(0,0)" writing-mode="lr" x="912.33" xml:space="preserve" y="682.87" zvalue="275">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,826.5,720.747) scale(1,1) translate(0,0)" writing-mode="lr" x="826.5" xml:space="preserve" y="725.25" zvalue="277">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,903.438,605.567) scale(1,1) translate(0,0)" writing-mode="lr" x="903.4400000000001" xml:space="preserve" y="610.0700000000001" zvalue="278">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,826.5,644.981) scale(1,1) translate(0,0)" writing-mode="lr" x="826.5" xml:space="preserve" y="649.48" zvalue="280">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" x="824.796875" xml:space="preserve" y="834.2986111111111" zvalue="282">#2炉变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="824.796875" xml:space="preserve" y="850.2986111111111" zvalue="282">13MVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,890.743,952.125) scale(1,1) translate(0,0)" writing-mode="lr" x="890.74" xml:space="preserve" y="956.63" zvalue="287">#2电炉</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1005.25,858.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1005.25" xml:space="preserve" y="862.75" zvalue="292">1020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" x="801.859375" xml:space="preserve" y="1028.881944444444" zvalue="295">#2电容补偿装置</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="801.859375" xml:space="preserve" y="1044.881944444444" zvalue="295">9600kVar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,831.921,909.993) scale(1,1) translate(-3.6212e-13,0)" writing-mode="lr" x="831.92" xml:space="preserve" y="914.49" zvalue="297">3246</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,776.75,916.607) scale(1,1) translate(0,0)" writing-mode="lr" x="776.75" xml:space="preserve" y="921.11" zvalue="300">324</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1219.99,749.914) scale(1,1) translate(0,0)" writing-mode="lr" x="1219.99" xml:space="preserve" y="754.41" zvalue="307">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="319" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1227.83,674.615) scale(1,1) translate(0,0)" writing-mode="lr" x="1227.83" xml:space="preserve" y="679.12" zvalue="309">103</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="235" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1140,716.997) scale(1,1) translate(0,0)" writing-mode="lr" x="1140" xml:space="preserve" y="721.5" zvalue="311">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1216.94,601.817) scale(1,1) translate(0,0)" writing-mode="lr" x="1216.94" xml:space="preserve" y="606.3200000000001" zvalue="312">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="233" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1140,641.231) scale(1,1) translate(0,0)" writing-mode="lr" x="1140" xml:space="preserve" y="645.73" zvalue="314">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="232" stroke="rgb(255,255,255)" text-anchor="middle" x="1138.28125" xml:space="preserve" y="830.5486111111111" zvalue="316">#3炉变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="232" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1138.28125" xml:space="preserve" y="846.5486111111111" zvalue="316">13MVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1204.24,948.375) scale(1,1) translate(7.87865e-13,0)" writing-mode="lr" x="1204.24" xml:space="preserve" y="952.88" zvalue="321">#3电炉</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="231" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1318.75,854.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1318.75" xml:space="preserve" y="859" zvalue="326">1030</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="230" stroke="rgb(255,255,255)" text-anchor="middle" x="1115.34375" xml:space="preserve" y="1025.131944444444" zvalue="329">#3电容补偿装置</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="230" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1115.34375" xml:space="preserve" y="1041.131944444444" zvalue="329">9600kVar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1145.42,906.243) scale(1,1) translate(0,0)" writing-mode="lr" x="1145.42" xml:space="preserve" y="910.74" zvalue="331">3256</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1090.25,912.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1090.25" xml:space="preserve" y="917.36" zvalue="334">325</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="269" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1552.49,748.664) scale(1,1) translate(0,0)" writing-mode="lr" x="1552.49" xml:space="preserve" y="753.16" zvalue="341">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="320" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1558.33,674.365) scale(1,1) translate(0,0)" writing-mode="lr" x="1558.33" xml:space="preserve" y="678.87" zvalue="343">104</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="268" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1469.5,715.747) scale(1,1) translate(0,0)" writing-mode="lr" x="1469.5" xml:space="preserve" y="720.25" zvalue="345">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="267" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1549.44,600.567) scale(1,1) translate(0,0)" writing-mode="lr" x="1549.44" xml:space="preserve" y="605.0700000000001" zvalue="346">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1472.5,639.981) scale(1,1) translate(0,0)" writing-mode="lr" x="1472.5" xml:space="preserve" y="644.48" zvalue="348">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="265" stroke="rgb(255,255,255)" text-anchor="middle" x="1470.78125" xml:space="preserve" y="829.2986111111111" zvalue="350">#4炉变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="265" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1470.78125" xml:space="preserve" y="845.2986111111111" zvalue="350">13MVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1536.74,947.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1536.74" xml:space="preserve" y="951.63" zvalue="355">#4电炉</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1651.25,853.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1651.25" xml:space="preserve" y="857.75" zvalue="360">1040</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="263" stroke="rgb(255,255,255)" text-anchor="middle" x="1447.84375" xml:space="preserve" y="1023.881944444444" zvalue="363">#4电容补偿装置</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="263" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1447.84375" xml:space="preserve" y="1039.881944444444" zvalue="363">9600kVar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1477.92,904.993) scale(1,1) translate(0,0)" writing-mode="lr" x="1477.92" xml:space="preserve" y="909.49" zvalue="365">3266</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="261" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1422.75,911.607) scale(1,1) translate(0,0)" writing-mode="lr" x="1422.75" xml:space="preserve" y="916.11" zvalue="368">326</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,208.399,340.5) scale(1,1) translate(0,0)" writing-mode="lr" x="208.4" xml:space="preserve" y="345" zvalue="379">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,296.399,340.5) scale(1,1) translate(0,0)" writing-mode="lr" x="296.4" xml:space="preserve" y="345" zvalue="380">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="298" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1132.75,777.259) scale(1,1) translate(0,1.28024e-12)" writing-mode="lr" x="1132.75" xml:space="preserve" y="781.76" zvalue="393">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="301" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1464.25,773.009) scale(1,1) translate(-5.17364e-12,0)" writing-mode="lr" x="1464.25" xml:space="preserve" y="777.51" zvalue="396">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="306" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,601,224.5) scale(1,1) translate(0,0)" writing-mode="lr" x="601" xml:space="preserve" y="229" zvalue="401">110kV梁亚线</text>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="36">
   <path class="kv110" d="M 473.75 562.75 L 1666 562.75" stroke-width="4" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674243772420" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674243772420"/></metadata>
  <path d="M 473.75 562.75 L 1666 562.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv35" d="M 1310.25 274 L 1775 274" stroke-width="4" zvalue="79"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674243837956" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674243837956"/></metadata>
  <path d="M 1310.25 274 L 1775 274" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="111">
   <use class="kv35" height="30" transform="rotate(0,1463.45,146.567) scale(1.2562,1.1276) translate(-296.542,-14.6717)" width="15" x="1454.026448616529" xlink:href="#Disconnector:刀闸_0" y="129.6527269865601" zvalue="83"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449800503302" ObjectName="35kV梁中线3216隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449800503302"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1463.45,146.567) scale(1.2562,1.1276) translate(-296.542,-14.6717)" width="15" x="1454.026448616529" y="129.6527269865601"/></g>
  <g id="110">
   <use class="kv35" height="30" transform="rotate(0,1337.53,324.157) scale(0.595249,1.1905) translate(903.408,-49.0127)" width="30" x="1328.602941590854" xlink:href="#Disconnector:跌落刀闸_0" y="306.2992960338104" zvalue="85"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449800437765" ObjectName="35kV1号站用变高压侧跌落保险"/>
   <cge:TPSR_Ref TObjectID="6192449800437765"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1337.53,324.157) scale(0.595249,1.1905) translate(903.408,-49.0127)" width="30" x="1328.602941590854" y="306.2992960338104"/></g>
  <g id="120">
   <use class="kv35" height="30" transform="rotate(0,1647.59,206.099) scale(1.2562,1.1276) translate(-334.097,-21.4085)" width="15" x="1638.165765522697" xlink:href="#Disconnector:刀闸_0" y="189.1850141152678" zvalue="147"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449801224198" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449801224198"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1647.59,206.099) scale(1.2562,1.1276) translate(-334.097,-21.4085)" width="15" x="1638.165765522697" y="189.1850141152678"/></g>
  <g id="134">
   <use class="kv35" height="30" transform="rotate(0,1489.4,317.817) scale(1.2562,1.1276) translate(-301.836,-34.0507)" width="15" x="1479.980635751206" xlink:href="#Disconnector:刀闸_0" y="300.9027269865601" zvalue="160"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449801420806" ObjectName="35kV#2环保变3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449801420806"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1489.4,317.817) scale(1.2562,1.1276) translate(-301.836,-34.0507)" width="15" x="1479.980635751206" y="300.9027269865601"/></g>
  <g id="152">
   <use class="kv35" height="30" transform="rotate(0,1597.15,317.817) scale(1.2562,1.1276) translate(-323.811,-34.0507)" width="15" x="1587.730635751206" xlink:href="#Disconnector:刀闸_0" y="300.9027269865601" zvalue="175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449801486342" ObjectName="35kV#3环保变3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449801486342"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1597.15,317.817) scale(1.2562,1.1276) translate(-323.811,-34.0507)" width="15" x="1587.730635751206" y="300.9027269865601"/></g>
  <g id="166">
   <use class="kv35" height="30" transform="rotate(0,1724.45,316.817) scale(1.2562,1.1276) translate(-349.772,-33.9375)" width="15" x="1715.026880058086" xlink:href="#Disconnector:刀闸_0" y="299.9027269865601" zvalue="190"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449801748486" ObjectName="35kV#3环保变3031隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449801748486"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1724.45,316.817) scale(1.2562,1.1276) translate(-349.772,-33.9375)" width="15" x="1715.026880058086" y="299.9027269865601"/></g>
  <g id="121">
   <use class="kv110" height="30" transform="rotate(0,601.698,348.567) scale(1.2562,1.1276) translate(-120.792,-37.5304)" width="15" x="592.276448616529" xlink:href="#Disconnector:刀闸_0" y="331.6527269865601" zvalue="201"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449802272774" ObjectName="110kV梁亚线1216隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449802272774"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,601.698,348.567) scale(1.2562,1.1276) translate(-120.792,-37.5304)" width="15" x="592.276448616529" y="331.6527269865601"/></g>
  <g id="174">
   <use class="kv110" height="30" transform="rotate(0,601.767,520.664) scale(1.2562,1.1276) translate(-120.806,-57.0053)" width="15" x="592.3454821213952" xlink:href="#Disconnector:刀闸_0" y="503.75" zvalue="215"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449802403846" ObjectName="110kV梁亚线1211隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449802403846"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,601.767,520.664) scale(1.2562,1.1276) translate(-120.806,-57.0053)" width="15" x="592.3454821213952" y="503.75"/></g>
  <g id="191">
   <use class="kv110" height="30" transform="rotate(0,782.087,488.099) scale(1.2562,1.1276) translate(-157.582,-53.3201)" width="15" x="772.6657655226969" xlink:href="#Disconnector:刀闸_0" y="471.1850141152678" zvalue="224"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449802862598" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449802862598"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,782.087,488.099) scale(1.2562,1.1276) translate(-157.582,-53.3201)" width="15" x="772.6657655226969" y="471.1850141152678"/></g>
  <g id="196">
   <use class="kv110" height="30" transform="rotate(0,602.948,752.164) scale(1.2562,-1.1276) translate(-121.047,-1417.3)" width="15" x="593.526448616529" xlink:href="#Disconnector:刀闸_0" y="735.25" zvalue="238"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449803059206" ObjectName="110kV#1炉变110kV侧1016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449803059206"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,602.948,752.164) scale(1.2562,-1.1276) translate(-121.047,-1417.3)" width="15" x="593.526448616529" y="735.25"/></g>
  <g id="197">
   <use class="kv110" height="30" transform="rotate(0,601.767,604.067) scale(1.2562,-1.1276) translate(-120.806,-1137.86)" width="15" x="592.3454821213952" xlink:href="#Disconnector:刀闸_0" y="587.1527269865601" zvalue="241"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449803124742" ObjectName="110kV#1炉变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449803124742"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,601.767,604.067) scale(1.2562,-1.1276) translate(-120.806,-1137.86)" width="15" x="592.3454821213952" y="587.1527269865601"/></g>
  <g id="64">
   <use class="kv35" height="30" transform="rotate(90,546.671,889.164) scale(1.2562,1.1276) translate(-109.57,-98.7054)" width="15" x="537.25" xlink:href="#Disconnector:刀闸_0" y="872.25" zvalue="260"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449803714566" ObjectName="#1电容补偿装置3236隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449803714566"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,546.671,889.164) scale(1.2562,1.1276) translate(-109.57,-98.7054)" width="15" x="537.25" y="872.25"/></g>
  <g id="227">
   <use class="kv110" height="30" transform="rotate(0,890.698,754.664) scale(1.2562,-1.1276) translate(-179.732,-1422.01)" width="15" x="881.2764486165289" xlink:href="#Disconnector:刀闸_0" y="737.75" zvalue="272"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449804566534" ObjectName="110kV#2炉变110kV侧1026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449804566534"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,890.698,754.664) scale(1.2562,-1.1276) translate(-179.732,-1422.01)" width="15" x="881.2764486165289" y="737.75"/></g>
  <g id="224">
   <use class="kv110" height="30" transform="rotate(0,889.517,606.567) scale(1.2562,-1.1276) translate(-179.492,-1142.58)" width="15" x="880.0954821213952" xlink:href="#Disconnector:刀闸_0" y="589.6527269865601" zvalue="276"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449804369926" ObjectName="110kV#2炉变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449804369926"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,889.517,606.567) scale(1.2562,-1.1276) translate(-179.492,-1142.58)" width="15" x="880.0954821213952" y="589.6527269865601"/></g>
  <g id="210">
   <use class="kv35" height="30" transform="rotate(90,834.421,891.664) scale(1.2562,1.1276) translate(-168.255,-98.9883)" width="15" x="825" xlink:href="#Disconnector:刀闸_0" y="874.75" zvalue="296"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449803911174" ObjectName="#2电容补偿装置3246隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449803911174"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,834.421,891.664) scale(1.2562,1.1276) translate(-168.255,-98.9883)" width="15" x="825" y="874.75"/></g>
  <g id="260">
   <use class="kv110" height="30" transform="rotate(0,1204.2,750.914) scale(1.2562,-1.1276) translate(-243.669,-1414.94)" width="15" x="1194.776448616529" xlink:href="#Disconnector:刀闸_0" y="734" zvalue="306"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449805352966" ObjectName="110kV#3炉变110kV侧1036隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449805352966"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1204.2,750.914) scale(1.2562,-1.1276) translate(-243.669,-1414.94)" width="15" x="1194.776448616529" y="734"/></g>
  <g id="257">
   <use class="kv110" height="30" transform="rotate(0,1203.02,602.817) scale(1.2562,-1.1276) translate(-243.429,-1135.5)" width="15" x="1193.595482121395" xlink:href="#Disconnector:刀闸_0" y="585.9027269865601" zvalue="310"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449805156358" ObjectName="110kV#3炉变110kV侧1031隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449805156358"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1203.02,602.817) scale(1.2562,-1.1276) translate(-243.429,-1135.5)" width="15" x="1193.595482121395" y="585.9027269865601"/></g>
  <g id="243">
   <use class="kv35" height="30" transform="rotate(90,1147.92,887.914) scale(1.2562,1.1276) translate(-232.192,-98.5639)" width="15" x="1138.5" xlink:href="#Disconnector:刀闸_0" y="871" zvalue="330"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449804697605" ObjectName="#3电容补偿装置3256隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449804697605"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1147.92,887.914) scale(1.2562,1.1276) translate(-232.192,-98.5639)" width="15" x="1138.5" y="871"/></g>
  <g id="293">
   <use class="kv110" height="30" transform="rotate(0,1536.7,749.664) scale(1.2562,-1.1276) translate(-311.481,-1412.58)" width="15" x="1527.276448616529" xlink:href="#Disconnector:刀闸_0" y="732.75" zvalue="340"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449806139397" ObjectName="110kV#4炉变110kV侧1046隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449806139397"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1536.7,749.664) scale(1.2562,-1.1276) translate(-311.481,-1412.58)" width="15" x="1527.276448616529" y="732.75"/></g>
  <g id="290">
   <use class="kv110" height="30" transform="rotate(0,1535.52,601.567) scale(1.2562,-1.1276) translate(-311.24,-1133.15)" width="15" x="1526.095482121395" xlink:href="#Disconnector:刀闸_0" y="584.6527269865601" zvalue="344"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449805942789" ObjectName="110kV#4炉变110kV侧1041隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449805942789"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1535.52,601.567) scale(1.2562,-1.1276) translate(-311.24,-1133.15)" width="15" x="1526.095482121395" y="584.6527269865601"/></g>
  <g id="276">
   <use class="kv35" height="30" transform="rotate(90,1480.42,886.664) scale(1.2562,1.1276) translate(-300.004,-98.4225)" width="15" x="1471" xlink:href="#Disconnector:刀闸_0" y="869.75" zvalue="364"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449805484037" ObjectName="#4电容补偿装置3266隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449805484037"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1480.42,886.664) scale(1.2562,1.1276) translate(-300.004,-98.4225)" width="15" x="1471" y="869.75"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="109">
   <use class="kv35" height="38" transform="rotate(0,1342.85,397.392) scale(1.43962,1.48114) translate(-404.356,-119.948)" width="26" x="1324.139899256382" xlink:href="#EnergyConsumer:站用变1节点_0" y="369.25" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449800372229" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,1342.85,397.392) scale(1.43962,1.48114) translate(-404.356,-119.948)" width="26" x="1324.139899256382" y="369.25"/></g>
  <g id="99">
   <use class="kv35" height="40" transform="rotate(0,1489.66,448.567) scale(1.875,1.84663) translate(-681.174,-188.724)" width="32" x="1459.657511386239" xlink:href="#EnergyConsumer:YY站用变_0" y="411.6346129270701" zvalue="98"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449800306693" ObjectName="#2环保变"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1489.66,448.567) scale(1.875,1.84663) translate(-681.174,-188.724)" width="32" x="1459.657511386239" y="411.6346129270701"/></g>
  <g id="157">
   <use class="kv35" height="40" transform="rotate(0,1597.41,448.567) scale(1.875,1.84663) translate(-731.457,-188.724)" width="32" x="1567.407511386239" xlink:href="#EnergyConsumer:YY站用变_0" y="411.6346130371094" zvalue="168"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449801682950" ObjectName="#1环保变"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1597.41,448.567) scale(1.875,1.84663) translate(-731.457,-188.724)" width="32" x="1567.407511386239" y="411.6346130371094"/></g>
  <g id="171">
   <use class="kv35" height="45" transform="rotate(0,1724.7,451.317) scale(1.91481,1.85256) translate(-805.691,-188.517)" width="40" x="1686.407511386239" xlink:href="#EnergyConsumer:D-YY变压器_0" y="409.6346153846155" zvalue="183"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449801945094" ObjectName="#3环保变"/>
   </metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,1724.7,451.317) scale(1.91481,1.85256) translate(-805.691,-188.517)" width="40" x="1686.407511386239" y="409.6346153846155"/></g>
  <g id="45">
   <use class="kv35" height="30" transform="rotate(0,602.993,921.438) scale(1.5625,-0.954167) translate(-213.702,-1887.82)" width="12" x="593.6175011300124" xlink:href="#EnergyConsumer:负荷_0" y="907.125" zvalue="250"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449803452422" ObjectName="#1电炉"/>
   <cge:TPSR_Ref TObjectID="6192449803452422"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,602.993,921.438) scale(1.5625,-0.954167) translate(-213.702,-1887.82)" width="12" x="593.6175011300124" y="907.125"/></g>
  <g id="218">
   <use class="kv35" height="30" transform="rotate(0,890.743,923.938) scale(1.5625,-0.954167) translate(-317.292,-1892.94)" width="12" x="881.3675011300124" xlink:href="#EnergyConsumer:负荷_0" y="909.625" zvalue="286"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449804173318" ObjectName="#2电炉"/>
   <cge:TPSR_Ref TObjectID="6192449804173318"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,890.743,923.938) scale(1.5625,-0.954167) translate(-317.292,-1892.94)" width="12" x="881.3675011300124" y="909.625"/></g>
  <g id="251">
   <use class="kv35" height="30" transform="rotate(0,1204.24,920.188) scale(1.5625,-0.954167) translate(-430.152,-1885.26)" width="12" x="1194.867501130012" xlink:href="#EnergyConsumer:负荷_0" y="905.875" zvalue="320"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449804959749" ObjectName="#3电炉"/>
   <cge:TPSR_Ref TObjectID="6192449804959749"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1204.24,920.188) scale(1.5625,-0.954167) translate(-430.152,-1885.26)" width="12" x="1194.867501130012" y="905.875"/></g>
  <g id="284">
   <use class="kv35" height="30" transform="rotate(0,1536.74,918.938) scale(1.5625,-0.954167) translate(-549.852,-1882.7)" width="12" x="1527.367501130012" xlink:href="#EnergyConsumer:负荷_0" y="904.625" zvalue="354"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449805746181" ObjectName="#4电炉"/>
   <cge:TPSR_Ref TObjectID="6192449805746181"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1536.74,918.938) scale(1.5625,-0.954167) translate(-549.852,-1882.7)" width="12" x="1527.367501130012" y="904.625"/></g>
 </g>
 <g id="BreakerClass">
  <g id="108">
   <use class="kv35" height="20" transform="rotate(0,1463.51,225.865) scale(1.78575,1.78575) translate(-640.03,-91.5257)" width="10" x="1454.579425216151" xlink:href="#Breaker:开关_0" y="208.0079074487931" zvalue="88"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924511924229" ObjectName="35kV梁中线321断路器"/>
   <cge:TPSR_Ref TObjectID="6473924511924229"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1463.51,225.865) scale(1.78575,1.78575) translate(-640.03,-91.5257)" width="10" x="1454.579425216151" y="208.0079074487931"/></g>
  <g id="107">
   <use class="kv35" height="20" transform="rotate(0,1489.54,385.1) scale(1.78575,1.78575) translate(-651.484,-161.591)" width="10" x="1480.609722955166" xlink:href="#Breaker:开关_0" y="367.2427520967142" zvalue="90"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924511858693" ObjectName="35kV#2环保变302断路器"/>
   <cge:TPSR_Ref TObjectID="6473924511858693"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1489.54,385.1) scale(1.78575,1.78575) translate(-651.484,-161.591)" width="10" x="1480.609722955166" y="367.2427520967142"/></g>
  <g id="158">
   <use class="kv35" height="20" transform="rotate(0,1597.29,385.1) scale(1.78575,1.78575) translate(-698.895,-161.591)" width="10" x="1588.359722955166" xlink:href="#Breaker:开关_0" y="367.2427520967142" zvalue="166"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924511989765" ObjectName="35kV#3环保变301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924511989765"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1597.29,385.1) scale(1.78575,1.78575) translate(-698.895,-161.591)" width="10" x="1588.359722955166" y="367.2427520967142"/></g>
  <g id="172">
   <use class="kv35" height="20" transform="rotate(0,1724.58,385.1) scale(1.78575,1.78575) translate(-754.907,-161.591)" width="10" x="1715.655967262046" xlink:href="#Breaker:开关_0" y="367.2427520967142" zvalue="181"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924512055301" ObjectName="35kV#3环保变303断路器"/>
   <cge:TPSR_Ref TObjectID="6473924512055301"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1724.58,385.1) scale(1.78575,1.78575) translate(-754.907,-161.591)" width="10" x="1715.655967262046" y="367.2427520967142"/></g>
  <g id="102">
   <use class="kv110" height="20" transform="rotate(0,601.758,435.865) scale(1.78575,1.78575) translate(-260.851,-183.928)" width="10" x="592.8294252161514" xlink:href="#Breaker:开关_0" y="418.0079074487931" zvalue="203"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924512120837" ObjectName="110kV梁亚线121断路器"/>
   <cge:TPSR_Ref TObjectID="6473924512120837"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,601.758,435.865) scale(1.78575,1.78575) translate(-260.851,-183.928)" width="10" x="592.8294252161514" y="418.0079074487931"/></g>
  <g id="200">
   <use class="kv110" height="20" transform="rotate(0,603.008,676.865) scale(1.78575,-1.78575) translate(-261.401,-1048.05)" width="10" x="594.0794252161514" xlink:href="#Breaker:开关_0" y="659.0079074487933" zvalue="239"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924512186373" ObjectName="110kV#1炉变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473924512186373"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,603.008,676.865) scale(1.78575,-1.78575) translate(-261.401,-1048.05)" width="10" x="594.0794252161514" y="659.0079074487933"/></g>
  <g id="68">
   <use class="kv35" height="20" transform="rotate(0,512.429,915.107) scale(1.78575,-1.78575) translate(-221.545,-1419.7)" width="10" x="503.4999999999999" xlink:href="#Breaker:开关_0" y="897.25" zvalue="264"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924512251909" ObjectName="#1电容补偿装置323断路器"/>
   <cge:TPSR_Ref TObjectID="6473924512251909"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,512.429,915.107) scale(1.78575,-1.78575) translate(-221.545,-1419.7)" width="10" x="503.4999999999999" y="897.25"/></g>
  <g id="226">
   <use class="kv110" height="20" transform="rotate(0,890.758,679.365) scale(1.78575,-1.78575) translate(-388.014,-1051.95)" width="10" x="881.8294252161514" xlink:href="#Breaker:开关_0" y="661.5079074487933" zvalue="274"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924512382982" ObjectName="110kV#2炉变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473924512382982"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,890.758,679.365) scale(1.78575,-1.78575) translate(-388.014,-1051.95)" width="10" x="881.8294252161514" y="661.5079074487933"/></g>
  <g id="208">
   <use class="kv35" height="20" transform="rotate(0,800.179,917.607) scale(1.78575,-1.78575) translate(-348.158,-1423.6)" width="10" x="791.2499999999999" xlink:href="#Breaker:开关_0" y="899.75" zvalue="299"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924512317445" ObjectName="#2电容补偿装置324断路器"/>
   <cge:TPSR_Ref TObjectID="6473924512317445"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,800.179,917.607) scale(1.78575,-1.78575) translate(-348.158,-1423.6)" width="10" x="791.2499999999999" y="899.75"/></g>
  <g id="259">
   <use class="kv110" height="20" transform="rotate(0,1204.26,675.615) scale(1.78575,-1.78575) translate(-525.957,-1046.1)" width="10" x="1195.329425216152" xlink:href="#Breaker:开关_0" y="657.7579074487933" zvalue="308"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924512514053" ObjectName="110kV#3炉变110kV侧103断路器"/>
   <cge:TPSR_Ref TObjectID="6473924512514053"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1204.26,675.615) scale(1.78575,-1.78575) translate(-525.957,-1046.1)" width="10" x="1195.329425216152" y="657.7579074487933"/></g>
  <g id="241">
   <use class="kv35" height="20" transform="rotate(0,1113.68,913.857) scale(1.78575,-1.78575) translate(-486.102,-1417.75)" width="10" x="1104.75" xlink:href="#Breaker:开关_0" y="896" zvalue="333"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924512448517" ObjectName="#3电容补偿装置325断路器"/>
   <cge:TPSR_Ref TObjectID="6473924512448517"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1113.68,913.857) scale(1.78575,-1.78575) translate(-486.102,-1417.75)" width="10" x="1104.75" y="896"/></g>
  <g id="292">
   <use class="kv110" height="20" transform="rotate(0,1536.76,674.365) scale(1.78575,-1.78575) translate(-672.261,-1044.15)" width="10" x="1527.829425216151" xlink:href="#Breaker:开关_0" y="656.5079074487933" zvalue="342"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924512645125" ObjectName="110kV#4炉变110kV侧104断路器"/>
   <cge:TPSR_Ref TObjectID="6473924512645125"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1536.76,674.365) scale(1.78575,-1.78575) translate(-672.261,-1044.15)" width="10" x="1527.829425216151" y="656.5079074487933"/></g>
  <g id="274">
   <use class="kv35" height="20" transform="rotate(0,1446.18,912.607) scale(1.78575,-1.78575) translate(-632.405,-1415.8)" width="10" x="1437.25" xlink:href="#Breaker:开关_0" y="894.75" zvalue="367"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924512579589" ObjectName="#4电容补偿装置326断路器"/>
   <cge:TPSR_Ref TObjectID="6473924512579589"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1446.18,912.607) scale(1.78575,-1.78575) translate(-632.405,-1415.8)" width="10" x="1437.25" y="894.75"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="106">
   <path class="kv35" d="M 1463.45 208.78 L 1463.52 163.19" stroke-width="1" zvalue="92"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="111@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1463.45 208.78 L 1463.52 163.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv35" d="M 1463.63 242.92 L 1463.63 274" stroke-width="1" zvalue="93"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@1" LinkObjectIDznd="113@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1463.63 242.92 L 1463.63 274" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv35" d="M 1337.53 306.3 L 1337.53 274" stroke-width="1" zvalue="94"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="113@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1337.53 306.3 L 1337.53 274" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv35" d="M 1337.26 369.62 L 1337.26 340.82" stroke-width="1" zvalue="95"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="110@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1337.26 369.62 L 1337.26 340.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv35" d="M 1463.56 130.21 L 1463.56 88.34" stroke-width="1" zvalue="97"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="112@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1463.56 130.21 L 1463.56 88.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv35" d="M 1489.66 413.02 L 1489.66 402.15" stroke-width="1" zvalue="100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="107@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1489.66 413.02 L 1489.66 402.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv35" d="M 1449.49 106.07 L 1463.56 106.07" stroke-width="1" zvalue="141"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="27@0" LinkObjectIDznd="101" MaxPinNum="2"/>
   </metadata>
  <path d="M 1449.49 106.07 L 1463.56 106.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv35" d="M 1449.49 183.08 L 1463.49 183.08" stroke-width="1" zvalue="144"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="30@0" LinkObjectIDznd="106" MaxPinNum="2"/>
   </metadata>
  <path d="M 1449.49 183.08 L 1463.49 183.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv35" d="M 1647.7 150.67 L 1647.7 189.74" stroke-width="1" zvalue="151"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="120@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1647.7 150.67 L 1647.7 189.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv35" d="M 1647.66 222.72 L 1647.66 274" stroke-width="1" zvalue="152"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@1" LinkObjectIDznd="113@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1647.66 222.72 L 1647.66 274" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv35" d="M 1635.41 242.11 L 1647.66 242.11" stroke-width="1" zvalue="153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="123" MaxPinNum="2"/>
   </metadata>
  <path d="M 1635.41 242.11 L 1647.66 242.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv35" d="M 1635.41 165.1 L 1647.7 165.1" stroke-width="1" zvalue="154"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@0" LinkObjectIDznd="122" MaxPinNum="2"/>
   </metadata>
  <path d="M 1635.41 165.1 L 1647.7 165.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv35" d="M 1489.48 368.02 L 1489.48 334.44" stroke-width="1" zvalue="161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="134@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1489.48 368.02 L 1489.48 334.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv35" d="M 1489.51 301.46 L 1489.51 274" stroke-width="1" zvalue="162"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@0" LinkObjectIDznd="113@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1489.51 301.46 L 1489.51 274" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv35" d="M 1477.66 346.35 L 1489.48 346.35" stroke-width="1" zvalue="164"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@0" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 1477.66 346.35 L 1489.48 346.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv35" d="M 1597.41 413.02 L 1597.41 402.15" stroke-width="1" zvalue="170"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="158@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1597.41 413.02 L 1597.41 402.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv35" d="M 1597.23 368.02 L 1597.23 334.44" stroke-width="1" zvalue="177"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="152@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1597.23 368.02 L 1597.23 334.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv35" d="M 1597.26 301.46 L 1597.26 274" stroke-width="1" zvalue="178"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="113@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1597.26 301.46 L 1597.26 274" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv35" d="M 1585.41 346.35 L 1597.23 346.35" stroke-width="1" zvalue="179"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="151" MaxPinNum="2"/>
   </metadata>
  <path d="M 1585.41 346.35 L 1597.23 346.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv35" d="M 1724.7 415.66 L 1724.7 402.15" stroke-width="1" zvalue="185"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="172@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1724.7 415.66 L 1724.7 402.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="165">
   <path class="kv35" d="M 1724.53 368.02 L 1724.53 333.44" stroke-width="1" zvalue="192"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@0" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1724.53 368.02 L 1724.53 333.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv35" d="M 1724.56 300.46 L 1724.56 274" stroke-width="1" zvalue="193"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="113@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1724.56 300.46 L 1724.56 274" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv35" d="M 1709.41 346.35 L 1724.53 346.35" stroke-width="1" zvalue="194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@0" LinkObjectIDznd="165" MaxPinNum="2"/>
   </metadata>
  <path d="M 1709.41 346.35 L 1724.53 346.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv110" d="M 601.7 418.78 L 601.77 365.19" stroke-width="1" zvalue="205"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="121@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 601.7 418.78 L 601.77 365.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv110" d="M 601.81 332.21 L 601.81 277.67" stroke-width="1" zvalue="206"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@0" LinkObjectIDznd="305@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 601.81 332.21 L 601.81 277.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv110" d="M 587.74 385.08 L 601.75 385.08" stroke-width="1" zvalue="211"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@0" LinkObjectIDznd="97" MaxPinNum="2"/>
   </metadata>
  <path d="M 587.74 385.08 L 601.75 385.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv110" d="M 601.88 452.92 L 601.88 504.31" stroke-width="1" zvalue="216"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@1" LinkObjectIDznd="174@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 601.88 452.92 L 601.88 504.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="kv110" d="M 601.84 537.29 L 601.84 562.75" stroke-width="1" zvalue="217"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@1" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 601.84 537.29 L 601.84 562.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv110" d="M 587.74 480.85 L 601.88 480.85" stroke-width="1" zvalue="220"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="175" MaxPinNum="2"/>
   </metadata>
  <path d="M 587.74 480.85 L 601.88 480.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv110" d="M 782.2 418.78 L 782.2 471.74" stroke-width="1" zvalue="230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@0" LinkObjectIDznd="191@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 782.2 418.78 L 782.2 471.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv110" d="M 782.16 504.72 L 782.16 562.75" stroke-width="1" zvalue="231"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@1" LinkObjectIDznd="36@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 782.16 504.72 L 782.16 562.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv110" d="M 769.91 524.11 L 782.16 524.11" stroke-width="1" zvalue="232"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189@0" LinkObjectIDznd="187" MaxPinNum="2"/>
   </metadata>
  <path d="M 769.91 524.11 L 782.16 524.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv110" d="M 769.91 447.1 L 782.2 447.1" stroke-width="1" zvalue="233"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@0" LinkObjectIDznd="188" MaxPinNum="2"/>
   </metadata>
  <path d="M 769.91 447.1 L 782.2 447.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv110" d="M 809.52 415.59 L 809.52 448.75 L 782.2 448.75" stroke-width="1" zvalue="235"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@0" LinkObjectIDznd="188" MaxPinNum="2"/>
   </metadata>
  <path d="M 809.52 415.59 L 809.52 448.75 L 782.2 448.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv110" d="M 601.84 587.44 L 601.84 562.75" stroke-width="1" zvalue="244"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@1" LinkObjectIDznd="36@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 601.84 587.44 L 601.84 562.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv110" d="M 601.88 620.42 L 601.88 659.81" stroke-width="1" zvalue="245"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="200@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 601.88 620.42 L 601.88 659.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv110" d="M 602.95 693.95 L 603.02 735.54" stroke-width="1" zvalue="246"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="200@0" LinkObjectIDznd="196@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 602.95 693.95 L 603.02 735.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv35" d="M 602.99 908.56 L 602.99 888.55" stroke-width="1" zvalue="251"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="31@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 602.99 908.56 L 602.99 888.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv110" d="M 603.06 768.52 L 603.06 790.4" stroke-width="1" zvalue="252"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="31@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 603.06 768.52 L 603.06 790.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv110" d="M 588.99 643.88 L 601.88 643.88" stroke-width="1" zvalue="253"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@0" LinkObjectIDznd="40" MaxPinNum="2"/>
   </metadata>
  <path d="M 588.99 643.88 L 601.88 643.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv110" d="M 588.99 719.65 L 603 719.65" stroke-width="1" zvalue="254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@0" LinkObjectIDznd="41" MaxPinNum="2"/>
   </metadata>
  <path d="M 588.99 719.65 L 603 719.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv110" d="M 603.03 813.48 L 656 813.5 L 656.25 841.5" stroke-width="1" zvalue="256"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31@2" LinkObjectIDznd="44@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 603.03 813.48 L 656 813.5 L 656.25 841.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv35" d="M 602.99 889.27 L 563.03 889.27" stroke-width="1" zvalue="261"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47" LinkObjectIDznd="64@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 602.99 889.27 L 563.03 889.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv35" d="M 530.05 889.24 L 512.55 889.24 L 512.55 898.05" stroke-width="1" zvalue="265"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@1" LinkObjectIDznd="68@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 530.05 889.24 L 512.55 889.24 L 512.55 898.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv35" d="M 512.37 932.19 L 512.37 945.33" stroke-width="1" zvalue="266"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="61@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 512.37 932.19 L 512.37 945.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv110" d="M 629.54 777.16 L 603.06 777.16" stroke-width="1" zvalue="270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="46" MaxPinNum="2"/>
   </metadata>
  <path d="M 629.54 777.16 L 603.06 777.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="kv110" d="M 889.59 589.94 L 889.59 562.75" stroke-width="1" zvalue="283"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="224@1" LinkObjectIDznd="36@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 889.59 589.94 L 889.59 562.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="220">
   <path class="kv110" d="M 889.63 622.92 L 889.63 662.31" stroke-width="1" zvalue="284"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="224@0" LinkObjectIDznd="226@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 889.63 622.92 L 889.63 662.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="219">
   <path class="kv110" d="M 890.7 696.45 L 890.77 738.04" stroke-width="1" zvalue="285"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@0" LinkObjectIDznd="227@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 890.7 696.45 L 890.77 738.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv35" d="M 890.74 911.06 L 890.74 891.05" stroke-width="1" zvalue="287"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@0" LinkObjectIDznd="222@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 890.74 911.06 L 890.74 891.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv110" d="M 890.81 771.02 L 890.81 792.9" stroke-width="1" zvalue="288"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="227@0" LinkObjectIDznd="222@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 890.81 771.02 L 890.81 792.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv110" d="M 876.74 646.38 L 889.63 646.38" stroke-width="1" zvalue="289"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@0" LinkObjectIDznd="220" MaxPinNum="2"/>
   </metadata>
  <path d="M 876.74 646.38 L 889.63 646.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv110" d="M 876.74 722.15 L 890.75 722.15" stroke-width="1" zvalue="290"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="225@0" LinkObjectIDznd="219" MaxPinNum="2"/>
   </metadata>
  <path d="M 876.74 722.15 L 890.75 722.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="kv110" d="M 890.78 815.98 L 943.75 816 L 944 844" stroke-width="1" zvalue="293"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="222@2" LinkObjectIDznd="213@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 890.78 815.98 L 943.75 816 L 944 844" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv35" d="M 890.74 891.77 L 850.78 891.77" stroke-width="1" zvalue="298"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217" LinkObjectIDznd="210@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 890.74 891.77 L 850.78 891.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="207">
   <path class="kv35" d="M 817.8 891.74 L 800.3 891.74 L 800.3 900.55" stroke-width="1" zvalue="301"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="210@1" LinkObjectIDznd="208@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 817.8 891.74 L 800.3 891.74 L 800.3 900.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="206">
   <path class="kv35" d="M 800.12 934.69 L 800.12 947.83" stroke-width="1" zvalue="302"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@0" LinkObjectIDznd="211@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 800.12 934.69 L 800.12 947.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="kv110" d="M 917.29 779.66 L 890.81 779.66" stroke-width="1" zvalue="304"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="216" MaxPinNum="2"/>
   </metadata>
  <path d="M 917.29 779.66 L 890.81 779.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="254">
   <path class="kv110" d="M 1203.09 586.19 L 1203.09 562.75" stroke-width="1" zvalue="317"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="257@1" LinkObjectIDznd="36@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1203.09 586.19 L 1203.09 562.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="253">
   <path class="kv110" d="M 1203.13 619.17 L 1203.13 658.56" stroke-width="1" zvalue="318"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="257@0" LinkObjectIDznd="259@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1203.13 619.17 L 1203.13 658.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="252">
   <path class="kv110" d="M 1204.2 692.7 L 1204.27 734.29" stroke-width="1" zvalue="319"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="259@0" LinkObjectIDznd="260@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1204.2 692.7 L 1204.27 734.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv35" d="M 1204.24 907.31 L 1204.24 887.3" stroke-width="1" zvalue="321"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="251@0" LinkObjectIDznd="255@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1204.24 907.31 L 1204.24 887.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="249">
   <path class="kv110" d="M 1204.31 767.27 L 1204.31 789.15" stroke-width="1" zvalue="322"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="260@0" LinkObjectIDznd="255@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1204.31 767.27 L 1204.31 789.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="248">
   <path class="kv110" d="M 1190.24 642.63 L 1203.13 642.63" stroke-width="1" zvalue="323"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="256@0" LinkObjectIDznd="253" MaxPinNum="2"/>
   </metadata>
  <path d="M 1190.24 642.63 L 1203.13 642.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="247">
   <path class="kv110" d="M 1190.24 718.4 L 1204.25 718.4" stroke-width="1" zvalue="324"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="258@0" LinkObjectIDznd="252" MaxPinNum="2"/>
   </metadata>
  <path d="M 1190.24 718.4 L 1204.25 718.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="245">
   <path class="kv110" d="M 1204.28 812.23 L 1257.25 812.25 L 1257.5 840.25" stroke-width="1" zvalue="327"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="255@2" LinkObjectIDznd="246@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1204.28 812.23 L 1257.25 812.25 L 1257.5 840.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="kv35" d="M 1204.24 888.02 L 1164.28 888.02" stroke-width="1" zvalue="332"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250" LinkObjectIDznd="243@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1204.24 888.02 L 1164.28 888.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv35" d="M 1131.3 887.99 L 1113.8 887.99 L 1113.8 896.8" stroke-width="1" zvalue="335"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="243@1" LinkObjectIDznd="241@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1131.3 887.99 L 1113.8 887.99 L 1113.8 896.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="239">
   <path class="kv35" d="M 1113.62 930.94 L 1113.62 944.08" stroke-width="1" zvalue="336"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="241@0" LinkObjectIDznd="244@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1113.62 930.94 L 1113.62 944.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="237">
   <path class="kv110" d="M 1230.79 775.91 L 1204.31 775.91" stroke-width="1" zvalue="338"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="238@0" LinkObjectIDznd="249" MaxPinNum="2"/>
   </metadata>
  <path d="M 1230.79 775.91 L 1204.31 775.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="286">
   <path class="kv110" d="M 1535.63 617.92 L 1535.63 657.31" stroke-width="1" zvalue="352"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="290@0" LinkObjectIDznd="292@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1535.63 617.92 L 1535.63 657.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="285">
   <path class="kv110" d="M 1536.7 691.45 L 1536.77 733.04" stroke-width="1" zvalue="353"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="292@0" LinkObjectIDznd="293@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1536.7 691.45 L 1536.77 733.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="283">
   <path class="kv35" d="M 1536.74 906.06 L 1536.74 886.05" stroke-width="1" zvalue="355"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="284@0" LinkObjectIDznd="288@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1536.74 906.06 L 1536.74 886.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="282">
   <path class="kv110" d="M 1536.81 766.02 L 1536.81 787.9" stroke-width="1" zvalue="356"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="293@0" LinkObjectIDznd="288@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1536.81 766.02 L 1536.81 787.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="281">
   <path class="kv110" d="M 1522.74 641.38 L 1535.63 641.38" stroke-width="1" zvalue="357"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="289@0" LinkObjectIDznd="286" MaxPinNum="2"/>
   </metadata>
  <path d="M 1522.74 641.38 L 1535.63 641.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="280">
   <path class="kv110" d="M 1522.74 717.15 L 1536.75 717.15" stroke-width="1" zvalue="358"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="291@0" LinkObjectIDznd="285" MaxPinNum="2"/>
   </metadata>
  <path d="M 1522.74 717.15 L 1536.75 717.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="kv110" d="M 1536.78 810.98 L 1589.75 811 L 1590 839" stroke-width="1" zvalue="361"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="288@2" LinkObjectIDznd="279@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1536.78 810.98 L 1589.75 811 L 1590 839" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="275">
   <path class="kv35" d="M 1536.74 886.77 L 1496.78 886.77" stroke-width="1" zvalue="366"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="283" LinkObjectIDznd="276@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1536.74 886.77 L 1496.78 886.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="273">
   <path class="kv35" d="M 1463.8 886.74 L 1446.3 886.74 L 1446.3 895.55" stroke-width="1" zvalue="369"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@1" LinkObjectIDznd="274@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1463.8 886.74 L 1446.3 886.74 L 1446.3 895.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="272">
   <path class="kv35" d="M 1446.12 929.69 L 1446.12 942.83" stroke-width="1" zvalue="370"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="277@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1446.12 929.69 L 1446.12 942.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="kv110" d="M 1563.29 774.66 L 1536.81 774.66" stroke-width="1" zvalue="372"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@0" LinkObjectIDznd="282" MaxPinNum="2"/>
   </metadata>
  <path d="M 1563.29 774.66 L 1536.81 774.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="295">
   <path class="kv110" d="M 1535.59 584.94 L 1535.59 562.75" stroke-width="1" zvalue="374"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="290@1" LinkObjectIDznd="36@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1535.59 584.94 L 1535.59 562.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="kv110" d="M 587.74 308.07 L 601.81 308.07" stroke-width="1" zvalue="377"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 587.74 308.07 L 601.81 308.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="299">
   <path class="kv110" d="M 1184.24 775.91 L 1204.31 775.91" stroke-width="1" zvalue="393"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="297@0" LinkObjectIDznd="249" MaxPinNum="2"/>
   </metadata>
  <path d="M 1184.24 775.91 L 1204.31 775.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="302">
   <path class="kv110" d="M 1515.74 774.66 L 1540 774.66" stroke-width="1" zvalue="396"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="300@0" LinkObjectIDznd="270" MaxPinNum="2"/>
   </metadata>
  <path d="M 1515.74 774.66 L 1540 774.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="MeasurementClass">
  <g id="34">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="34" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,161.571,210.968) scale(1,1) translate(0,0)" writing-mode="lr" x="161.72" xml:space="preserve" y="217.4" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125273993222" ObjectName="F"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="33" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,161.571,186) scale(1,1) translate(0,0)" writing-mode="lr" x="161.72" xml:space="preserve" y="192.43" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125302108164" ObjectName=""/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="35" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,343.571,185.857) scale(1,1) translate(0,0)" writing-mode="lr" x="343.72" xml:space="preserve" y="192.29" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125302173700" ObjectName=""/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,146.071,528.593) scale(1,1) translate(0,0)" writing-mode="lr" x="146.2" xml:space="preserve" y="533.5" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125273600006" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="143" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,146.071,553.718) scale(1,1) translate(0,-1.20619e-13)" writing-mode="lr" x="146.2" xml:space="preserve" y="558.63" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125273665542" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,147.071,577.843) scale(1,1) translate(0,0)" writing-mode="lr" x="147.2" xml:space="preserve" y="582.75" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125273731078" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,147.5,503.468) scale(1,1) translate(0,1.09461e-13)" writing-mode="lr" x="147.63" xml:space="preserve" y="508.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125273862150" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="148" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,146.321,602.968) scale(1,1) translate(0,0)" writing-mode="lr" x="146.45" xml:space="preserve" y="607.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125274058758" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="42">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,202.071,528.593) scale(1,1) translate(0,0)" writing-mode="lr" x="202.2" xml:space="preserve" y="533.5" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125276680198" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="49">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="49" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,202.071,553.718) scale(1,1) translate(0,-1.20619e-13)" writing-mode="lr" x="202.2" xml:space="preserve" y="558.63" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125276745734" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,203.071,577.843) scale(1,1) translate(0,0)" writing-mode="lr" x="203.2" xml:space="preserve" y="582.75" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125276811270" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,203.5,503.468) scale(1,1) translate(0,1.09461e-13)" writing-mode="lr" x="203.63" xml:space="preserve" y="508.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125276942342" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,202.321,602.968) scale(1,1) translate(0,0)" writing-mode="lr" x="202.45" xml:space="preserve" y="607.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125277138950" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="54" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,343.571,209.968) scale(1,1) translate(0,0)" writing-mode="lr" x="343.72" xml:space="preserve" y="216.4" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125277073414" ObjectName="F"/>
   </metadata>
  </g>
  <g id="195">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="195" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1378.67,36.5192) scale(1,1) translate(-8.88403e-13,0)" writing-mode="lr" x="1378.2" xml:space="preserve" y="41.3" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125275762694" ObjectName="P"/>
   </metadata>
  </g>
  <g id="287">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="287" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1378.67,54.7692) scale(1,1) translate(-8.88403e-13,0)" writing-mode="lr" x="1378.2" xml:space="preserve" y="59.55" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125275828230" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="294">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="294" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1378.67,73.0192) scale(1,1) translate(-8.88403e-13,0)" writing-mode="lr" x="1378.2" xml:space="preserve" y="77.8" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125275893766" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="296">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="296" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1725.7,534.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1725.15" xml:space="preserve" y="540.78" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125278318598" ObjectName="P"/>
   </metadata>
  </g>
  <g id="303">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="303" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1725.7,551) scale(1,1) translate(0,0)" writing-mode="lr" x="1725.15" xml:space="preserve" y="557.28" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125278384134" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="304">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="304" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1725.7,567.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1725.15" xml:space="preserve" y="573.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125278449670" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="307">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="307" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,601,158.5) scale(1,1) translate(0,0)" writing-mode="lr" x="600.53" xml:space="preserve" y="163.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125279956996" ObjectName="P"/>
   </metadata>
  </g>
  <g id="308">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="308" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,601,183.5) scale(1,1) translate(0,0)" writing-mode="lr" x="600.53" xml:space="preserve" y="188.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125280022532" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="309">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="309" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,601,205.5) scale(1,1) translate(0,0)" writing-mode="lr" x="600.53" xml:space="preserve" y="210.17" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125280088068" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="10" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,714.083,736.909) scale(1,1) translate(0,0)" writing-mode="lr" x="713.5599999999999" xml:space="preserve" y="743.16" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125281398788" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="11" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1001.83,739.409) scale(1,1) translate(0,0)" writing-mode="lr" x="1001.31" xml:space="preserve" y="745.66" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125287755782" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="90">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="90" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1315.33,735.659) scale(1,1) translate(0,0)" writing-mode="lr" x="1314.81" xml:space="preserve" y="741.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125292474372" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="137" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1648.74,733.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1648.22" xml:space="preserve" y="739.75" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125297192964" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="168">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="168" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,714.083,769.909) scale(1,1) translate(0,-6.7005e-13)" writing-mode="lr" x="713.5599999999999" xml:space="preserve" y="776.16" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125281464324" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="310">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="310" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1001.83,772.409) scale(1,1) translate(0,-6.7227e-13)" writing-mode="lr" x="1001.31" xml:space="preserve" y="778.66" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125287821318" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="311">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="311" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1315.33,768.659) scale(1,1) translate(0,-6.6894e-13)" writing-mode="lr" x="1314.81" xml:space="preserve" y="774.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125292539908" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="312">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="312" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1648.74,766.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1648.22" xml:space="preserve" y="772.75" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125297258500" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="313">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="313" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,714.083,802.909) scale(1,1) translate(0,0)" writing-mode="lr" x="713.5599999999999" xml:space="preserve" y="809.16" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125281660932" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="314">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="314" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1001.83,805.409) scale(1,1) translate(0,0)" writing-mode="lr" x="1001.31" xml:space="preserve" y="811.66" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125288017926" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="315">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="315" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1315.33,801.659) scale(1,1) translate(0,0)" writing-mode="lr" x="1314.81" xml:space="preserve" y="807.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125292736516" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="316">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="316" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1648.74,799.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1648.22" xml:space="preserve" y="805.75" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125297455108" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="321">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="321" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1490.16,524.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1435.05" xml:space="preserve" y="530.17" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125274124294" ObjectName="P"/>
   </metadata>
  </g>
  <g id="322">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="322" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1490.16,542) scale(1,1) translate(0,0)" writing-mode="lr" x="1435.05" xml:space="preserve" y="547.42" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125274189830" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="323">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="323" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1490.16,559) scale(1,1) translate(0,1.21958e-13)" writing-mode="lr" x="1435.05" xml:space="preserve" y="564.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125274255366" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="324">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="15" id="324" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1597.91,525) scale(1,1) translate(0,0)" writing-mode="lr" x="1542.8" xml:space="preserve" y="530.42" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125277204486" ObjectName="P"/>
   </metadata>
  </g>
  <g id="325">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" id="325" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1597.91,542) scale(1,1) translate(0,-1.18183e-13)" writing-mode="lr" x="1542.8" xml:space="preserve" y="547.42" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125277270022" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="326">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="15" id="326" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1597.91,559) scale(1,1) translate(0,1.21958e-13)" writing-mode="lr" x="1542.8" xml:space="preserve" y="564.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125277335558" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="27">
   <use class="kv35" height="20" transform="rotate(90,1433,105.984) scale(1.65323,1.6914) translate(-562.945,-36.4095)" width="10" x="1424.733856435646" xlink:href="#GroundDisconnector:地刀_0" y="89.06983531354149" zvalue="140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449800699910" ObjectName="35kV梁中线32167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449800699910"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1433,105.984) scale(1.65323,1.6914) translate(-562.945,-36.4095)" width="10" x="1424.733856435646" y="89.06983531354149"/></g>
  <g id="30">
   <use class="kv35" height="20" transform="rotate(90,1433,183) scale(1.65323,1.6914) translate(-562.945,-67.8917)" width="10" x="1424.733856435646" xlink:href="#GroundDisconnector:地刀_0" y="166.0859788778954" zvalue="143"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449800830982" ObjectName="35kV梁中线32160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449800830982"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1433,183) scale(1.65323,1.6914) translate(-562.945,-67.8917)" width="10" x="1424.733856435646" y="166.0859788778954"/></g>
  <g id="117">
   <use class="kv35" height="20" transform="rotate(90,1618.91,165.016) scale(1.65323,1.6914) translate(-636.404,-60.5404)" width="10" x="1610.647877557751" xlink:href="#GroundDisconnector:地刀_0" y="148.1021224422492" zvalue="148"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449801027590" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449801027590"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1618.91,165.016) scale(1.65323,1.6914) translate(-636.404,-60.5404)" width="10" x="1610.647877557751" y="148.1021224422492"/></g>
  <g id="119">
   <use class="kv35" height="20" transform="rotate(90,1618.91,242.032) scale(1.65323,1.6914) translate(-636.404,-92.0226)" width="10" x="1610.647877557751" xlink:href="#GroundDisconnector:地刀_0" y="225.1182660066031" zvalue="149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449801158662" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449801158662"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1618.91,242.032) scale(1.65323,1.6914) translate(-636.404,-92.0226)" width="10" x="1610.647877557751" y="225.1182660066031"/></g>
  <g id="130">
   <use class="kv35" height="20" transform="rotate(90,1461.16,346.266) scale(1.65323,1.6914) translate(-574.073,-134.631)" width="10" x="1452.897877557751" xlink:href="#GroundDisconnector:地刀_0" y="329.3521224422493" zvalue="156"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449801355270" ObjectName="35kV#2环保变30217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449801355270"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1461.16,346.266) scale(1.65323,1.6914) translate(-574.073,-134.631)" width="10" x="1452.897877557751" y="329.3521224422493"/></g>
  <g id="155">
   <use class="kv35" height="20" transform="rotate(90,1568.91,346.266) scale(1.65323,1.6914) translate(-616.648,-134.631)" width="10" x="1560.647877557751" xlink:href="#GroundDisconnector:地刀_0" y="329.3521224422493" zvalue="171"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449801617414" ObjectName="35kV#3环保变30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449801617414"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1568.91,346.266) scale(1.65323,1.6914) translate(-616.648,-134.631)" width="10" x="1560.647877557751" y="329.3521224422493"/></g>
  <g id="169">
   <use class="kv35" height="20" transform="rotate(90,1692.91,346.266) scale(1.65323,1.6914) translate(-665.643,-134.631)" width="10" x="1684.647877557751" xlink:href="#GroundDisconnector:地刀_0" y="329.3521224422493" zvalue="186"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449801879558" ObjectName="35kV#3环保变30317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449801879558"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1692.91,346.266) scale(1.65323,1.6914) translate(-665.643,-134.631)" width="10" x="1684.647877557751" y="329.3521224422493"/></g>
  <g id="95">
   <use class="kv110" height="20" transform="rotate(90,571.25,307.984) scale(1.65323,1.6914) translate(-222.448,-118.982)" width="10" x="562.9838672959634" xlink:href="#GroundDisconnector:地刀_0" y="291.0698353135415" zvalue="207"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449802207238" ObjectName="110kV梁亚线12167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449802207238"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,571.25,307.984) scale(1.65323,1.6914) translate(-222.448,-118.982)" width="10" x="562.9838672959634" y="291.0698353135415"/></g>
  <g id="93">
   <use class="kv110" height="20" transform="rotate(90,571.25,385) scale(1.65323,1.6914) translate(-222.448,-150.464)" width="10" x="562.9838672959634" xlink:href="#GroundDisconnector:地刀_0" y="368.0859788778954" zvalue="210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449802076166" ObjectName="110kV梁亚线12160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449802076166"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,571.25,385) scale(1.65323,1.6914) translate(-222.448,-150.464)" width="10" x="562.9838672959634" y="368.0859788778954"/></g>
  <g id="178">
   <use class="kv110" height="20" transform="rotate(90,571.25,480.766) scale(1.65323,1.6914) translate(-222.448,-189.611)" width="10" x="562.9838672714195" xlink:href="#GroundDisconnector:地刀_0" y="463.8521224422493" zvalue="219"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449802534918" ObjectName="110kV梁亚线12117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449802534918"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,571.25,480.766) scale(1.65323,1.6914) translate(-222.448,-189.611)" width="10" x="562.9838672714195" y="463.8521224422493"/></g>
  <g id="190">
   <use class="kv110" height="20" transform="rotate(90,753.414,447.016) scale(1.65323,1.6914) translate(-294.425,-175.815)" width="10" x="745.1478775577509" xlink:href="#GroundDisconnector:地刀_0" y="430.1021224422492" zvalue="226"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449802797062" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449802797062"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,753.414,447.016) scale(1.65323,1.6914) translate(-294.425,-175.815)" width="10" x="745.1478775577509" y="430.1021224422492"/></g>
  <g id="189">
   <use class="kv110" height="20" transform="rotate(90,753.414,524.032) scale(1.65323,1.6914) translate(-294.425,-207.297)" width="10" x="745.1478775577509" xlink:href="#GroundDisconnector:地刀_0" y="507.1182660066031" zvalue="228"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449802665990" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449802665990"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,753.414,524.032) scale(1.65323,1.6914) translate(-294.425,-207.297)" width="10" x="745.1478775577509" y="507.1182660066031"/></g>
  <g id="199">
   <use class="kv110" height="20" transform="rotate(270,572.5,719.731) scale(1.65323,-1.6914) translate(-222.942,-1138.34)" width="10" x="564.2338672959634" xlink:href="#GroundDisconnector:地刀_0" y="702.8167481086647" zvalue="240"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449803386886" ObjectName="110kV#1炉变110kV侧10167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449803386886"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,572.5,719.731) scale(1.65323,-1.6914) translate(-222.942,-1138.34)" width="10" x="564.2338672959634" y="702.8167481086647"/></g>
  <g id="198">
   <use class="kv110" height="20" transform="rotate(270,572.5,643.965) scale(1.65323,-1.6914) translate(-222.942,-1017.78)" width="10" x="564.2338672714195" xlink:href="#GroundDisconnector:地刀_0" y="627.0506045443108" zvalue="242"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449803255814" ObjectName="110kV#1炉变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449803255814"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,572.5,643.965) scale(1.65323,-1.6914) translate(-222.942,-1017.78)" width="10" x="564.2338672714195" y="627.0506045443108"/></g>
  <g id="44">
   <use class="kv110" height="40" transform="rotate(0,673,856.75) scale(1.25,-1.25) translate(-129.6,-1537.15)" width="40" x="648" xlink:href="#GroundDisconnector:中性点地刀12_0" y="831.75" zvalue="255"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449803583494" ObjectName="110kV#1炉变110kV中性点1010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449803583494"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,673,856.75) scale(1.25,-1.25) translate(-129.6,-1537.15)" width="40" x="648" y="831.75"/></g>
  <g id="225">
   <use class="kv110" height="20" transform="rotate(270,860.25,722.231) scale(1.65323,-1.6914) translate(-336.638,-1142.32)" width="10" x="851.9838672959634" xlink:href="#GroundDisconnector:地刀_0" y="705.3167481086647" zvalue="275"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449804500998" ObjectName="110kV#2炉变110kV侧10267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449804500998"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,860.25,722.231) scale(1.65323,-1.6914) translate(-336.638,-1142.32)" width="10" x="851.9838672959634" y="705.3167481086647"/></g>
  <g id="223">
   <use class="kv110" height="20" transform="rotate(270,860.25,646.465) scale(1.65323,-1.6914) translate(-336.638,-1021.76)" width="10" x="851.9838672714195" xlink:href="#GroundDisconnector:地刀_0" y="629.5506045443108" zvalue="279"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449804304390" ObjectName="110kV#2炉变110kV侧10217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449804304390"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,860.25,646.465) scale(1.65323,-1.6914) translate(-336.638,-1021.76)" width="10" x="851.9838672714195" y="629.5506045443108"/></g>
  <g id="213">
   <use class="kv110" height="40" transform="rotate(0,960.75,859.25) scale(1.25,-1.25) translate(-187.15,-1541.65)" width="40" x="935.75" xlink:href="#GroundDisconnector:中性点地刀12_0" y="834.25" zvalue="291"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449804107782" ObjectName="110kV#2炉变110kV中性点1020接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449804107782"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,960.75,859.25) scale(1.25,-1.25) translate(-187.15,-1541.65)" width="40" x="935.75" y="834.25"/></g>
  <g id="258">
   <use class="kv110" height="20" transform="rotate(270,1173.75,718.481) scale(1.65323,-1.6914) translate(-460.509,-1136.35)" width="10" x="1165.483867295963" xlink:href="#GroundDisconnector:地刀_0" y="701.5667481086647" zvalue="309"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449805287430" ObjectName="110kV#3炉变110kV侧10360接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449805287430"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1173.75,718.481) scale(1.65323,-1.6914) translate(-460.509,-1136.35)" width="10" x="1165.483867295963" y="701.5667481086647"/></g>
  <g id="256">
   <use class="kv110" height="20" transform="rotate(270,1173.75,642.715) scale(1.65323,-1.6914) translate(-460.509,-1015.79)" width="10" x="1165.48386727142" xlink:href="#GroundDisconnector:地刀_0" y="625.8006045443108" zvalue="313"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449805090821" ObjectName="110kV#3炉变110kV侧10317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449805090821"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1173.75,642.715) scale(1.65323,-1.6914) translate(-460.509,-1015.79)" width="10" x="1165.48386727142" y="625.8006045443108"/></g>
  <g id="246">
   <use class="kv110" height="40" transform="rotate(0,1274.25,855.5) scale(1.25,-1.25) translate(-249.85,-1534.9)" width="40" x="1249.25" xlink:href="#GroundDisconnector:中性点地刀12_0" y="830.5" zvalue="325"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449804894213" ObjectName="110kV#3炉变110kV中性点1030接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449804894213"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1274.25,855.5) scale(1.25,-1.25) translate(-249.85,-1534.9)" width="40" x="1249.25" y="830.5"/></g>
  <g id="291">
   <use class="kv110" height="20" transform="rotate(270,1506.25,717.231) scale(1.65323,-1.6914) translate(-591.888,-1134.36)" width="10" x="1497.983867295963" xlink:href="#GroundDisconnector:地刀_0" y="700.3167481086647" zvalue="343"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449806073861" ObjectName="110kV#4炉变110kV侧10460接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449806073861"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1506.25,717.231) scale(1.65323,-1.6914) translate(-591.888,-1134.36)" width="10" x="1497.983867295963" y="700.3167481086647"/></g>
  <g id="289">
   <use class="kv110" height="20" transform="rotate(270,1506.25,641.465) scale(1.65323,-1.6914) translate(-591.888,-1013.8)" width="10" x="1497.98386727142" xlink:href="#GroundDisconnector:地刀_0" y="624.5506045443108" zvalue="347"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449805877253" ObjectName="110kV#4炉变110kV侧10417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449805877253"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1506.25,641.465) scale(1.65323,-1.6914) translate(-591.888,-1013.8)" width="10" x="1497.98386727142" y="624.5506045443108"/></g>
  <g id="279">
   <use class="kv110" height="40" transform="rotate(0,1606.75,854.25) scale(1.25,-1.25) translate(-316.35,-1532.65)" width="40" x="1581.75" xlink:href="#GroundDisconnector:中性点地刀12_0" y="829.25" zvalue="359"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449805680645" ObjectName="110kV#4炉变110kV中性点1040接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449805680645"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1606.75,854.25) scale(1.25,-1.25) translate(-316.35,-1532.65)" width="40" x="1581.75" y="829.25"/></g>
  <g id="297">
   <use class="kv110" height="20" transform="rotate(270,1167.75,775.992) scale(1.65323,-1.6914) translate(-458.139,-1227.86)" width="10" x="1159.483867295963" xlink:href="#GroundDisconnector:地刀_0" y="759.0783625357612" zvalue="392"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449882161157" ObjectName="110kV#3炉变110kV侧10367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449882161157"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1167.75,775.992) scale(1.65323,-1.6914) translate(-458.139,-1227.86)" width="10" x="1159.483867295963" y="759.0783625357612"/></g>
  <g id="300">
   <use class="kv110" height="20" transform="rotate(270,1499.25,774.742) scale(1.65323,-1.6914) translate(-589.122,-1225.88)" width="10" x="1490.983867295963" xlink:href="#GroundDisconnector:地刀_0" y="757.8283625357612" zvalue="395"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449882292229" ObjectName="110kV#4炉变110kV侧10467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449882292229"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1499.25,774.742) scale(1.65323,-1.6914) translate(-589.122,-1225.88)" width="10" x="1490.983867295963" y="757.8283625357612"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="115">
   <use class="kv35" height="42" transform="rotate(0,1664.13,115.5) scale(1.72619,1.72619) translate(-689.19,-33.3397)" width="30" x="1638.239483325173" xlink:href="#Accessory:4卷PT带容断器_0" y="79.25" zvalue="145"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449800896518" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1664.13,115.5) scale(1.72619,1.72619) translate(-689.19,-33.3397)" width="30" x="1638.239483325173" y="79.25"/></g>
  <g id="192">
   <use class="kv110" height="40" transform="rotate(0,772.892,397.5) scale(2.01901,-1.8125) translate(-369.705,-600.56)" width="40" x="732.5114938025059" xlink:href="#Accessory:五卷PT_0" y="361.25" zvalue="222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449802928134" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,772.892,397.5) scale(2.01901,-1.8125) translate(-369.705,-600.56)" width="40" x="732.5114938025059" y="361.25"/></g>
  <g id="193">
   <use class="kv110" height="20" transform="rotate(0,809.521,400.493) scale(2.15625,-2.15625) translate(-422.528,-574.666)" width="20" x="787.9583333333333" xlink:href="#Accessory:线路PT3_0" y="378.9305555555556" zvalue="234"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449802993670" ObjectName="110kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,809.521,400.493) scale(2.15625,-2.15625) translate(-422.528,-574.666)" width="20" x="787.9583333333333" y="378.9305555555556"/></g>
  <g id="67">
   <use class="kv110" height="20" transform="rotate(90,648.41,777.16) scale(2.15625,-2.15625) translate(-336.135,-1126.02)" width="20" x="626.8472222222222" xlink:href="#Accessory:线路PT3_0" y="755.5972222222223" zvalue="269"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449803780102" ObjectName="110kV#1炉变避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,648.41,777.16) scale(2.15625,-2.15625) translate(-336.135,-1126.02)" width="20" x="626.8472222222222" y="755.5972222222223"/></g>
  <g id="205">
   <use class="kv110" height="20" transform="rotate(90,936.16,779.66) scale(2.15625,-2.15625) translate(-490.436,-1129.68)" width="20" x="914.5972222222222" xlink:href="#Accessory:线路PT3_0" y="758.0972222222223" zvalue="303"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449803845638" ObjectName="110kV#2炉变避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,936.16,779.66) scale(2.15625,-2.15625) translate(-490.436,-1129.68)" width="20" x="914.5972222222222" y="758.0972222222223"/></g>
  <g id="238">
   <use class="kv110" height="20" transform="rotate(90,1249.66,775.91) scale(2.15625,-2.15625) translate(-658.545,-1124.19)" width="20" x="1228.097222222222" xlink:href="#Accessory:线路PT3_0" y="754.3472222222223" zvalue="337"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449804632070" ObjectName="110kV#3炉变避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1249.66,775.91) scale(2.15625,-2.15625) translate(-658.545,-1124.19)" width="20" x="1228.097222222222" y="754.3472222222223"/></g>
  <g id="271">
   <use class="kv110" height="20" transform="rotate(90,1582.16,774.66) scale(2.15625,-2.15625) translate(-836.842,-1122.36)" width="20" x="1560.597222222222" xlink:href="#Accessory:线路PT3_0" y="753.0972222222223" zvalue="371"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449805418501" ObjectName="110kV#4炉变避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1582.16,774.66) scale(2.15625,-2.15625) translate(-836.842,-1122.36)" width="20" x="1560.597222222222" y="753.0972222222223"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="31">
   <g id="310">
    <use class="kv110" height="50" transform="rotate(0,602.993,839.375) scale(1.995,1.995) translate(-285.816,-393.761)" width="30" x="573.0700000000001" xlink:href="#PowerTransformer2:Y-D_0" y="789.5" zvalue="243"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874437558276" ObjectName="110"/>
    </metadata>
   </g>
   <g id="311">
    <use class="kv35" height="50" transform="rotate(0,602.993,839.375) scale(1.995,1.995) translate(-285.816,-393.761)" width="30" x="573.0700000000001" xlink:href="#PowerTransformer2:Y-D_1" y="789.5" zvalue="243"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874437623812" ObjectName="35"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399451017219" ObjectName="110kV#1炉变"/>
   <cge:TPSR_Ref TObjectID="6755399451017219"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,602.993,839.375) scale(1.995,1.995) translate(-285.816,-393.761)" width="30" x="573.0700000000001" y="789.5"/></g>
  <g id="222">
   <g id="2220">
    <use class="kv110" height="50" transform="rotate(0,890.743,841.875) scale(1.995,1.995) translate(-429.33,-395.008)" width="30" x="860.8200000000001" xlink:href="#PowerTransformer2:Y-D_0" y="792" zvalue="281"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874437689348" ObjectName="110"/>
    </metadata>
   </g>
   <g id="2221">
    <use class="kv35" height="50" transform="rotate(0,890.743,841.875) scale(1.995,1.995) translate(-429.33,-395.008)" width="30" x="860.8200000000001" xlink:href="#PowerTransformer2:Y-D_1" y="792" zvalue="281"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874437754884" ObjectName="35"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399451082755" ObjectName="110kV#2炉变"/>
   <cge:TPSR_Ref TObjectID="6755399451082755"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,890.743,841.875) scale(1.995,1.995) translate(-429.33,-395.008)" width="30" x="860.8200000000001" y="792"/></g>
  <g id="255">
   <g id="2550">
    <use class="kv110" height="50" transform="rotate(0,1204.24,838.125) scale(1.995,1.995) translate(-585.687,-393.137)" width="30" x="1174.32" xlink:href="#PowerTransformer2:Y-D_0" y="788.25" zvalue="315"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874437820420" ObjectName="110"/>
    </metadata>
   </g>
   <g id="2551">
    <use class="kv35" height="50" transform="rotate(0,1204.24,838.125) scale(1.995,1.995) translate(-585.687,-393.137)" width="30" x="1174.32" xlink:href="#PowerTransformer2:Y-D_1" y="788.25" zvalue="315"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874437885956" ObjectName="35"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399451148291" ObjectName="110kV#3炉变"/>
   <cge:TPSR_Ref TObjectID="6755399451148291"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1204.24,838.125) scale(1.995,1.995) translate(-585.687,-393.137)" width="30" x="1174.32" y="788.25"/></g>
  <g id="288">
   <g id="2880">
    <use class="kv110" height="50" transform="rotate(0,1536.74,836.875) scale(1.995,1.995) translate(-751.521,-392.514)" width="30" x="1506.82" xlink:href="#PowerTransformer2:Y-D_0" y="787" zvalue="349"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874437951492" ObjectName="110"/>
    </metadata>
   </g>
   <g id="2881">
    <use class="kv35" height="50" transform="rotate(0,1536.74,836.875) scale(1.995,1.995) translate(-751.521,-392.514)" width="30" x="1506.82" xlink:href="#PowerTransformer2:Y-D_1" y="787" zvalue="349"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874438017028" ObjectName="35"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399451213827" ObjectName="110kV#4炉变"/>
   <cge:TPSR_Ref TObjectID="6755399451213827"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1536.74,836.875) scale(1.995,1.995) translate(-751.521,-392.514)" width="30" x="1506.82" y="787"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="61">
   <use class="kv35" height="40" transform="rotate(0,512.875,971.625) scale(1.71875,1.71875) translate(-205.85,-391.941)" width="24" x="492.25" xlink:href="#Compensator:10kV电容器_0" y="937.25" zvalue="257"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449803649030" ObjectName="#1电容补偿装置"/>
   <cge:TPSR_Ref TObjectID="6192449803649030"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,512.875,971.625) scale(1.71875,1.71875) translate(-205.85,-391.941)" width="24" x="492.25" y="937.25"/></g>
  <g id="211">
   <use class="kv35" height="40" transform="rotate(0,800.625,974.125) scale(1.71875,1.71875) translate(-326.182,-392.986)" width="24" x="780" xlink:href="#Compensator:10kV电容器_0" y="939.75" zvalue="294"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449803976710" ObjectName="#2电容补偿装置"/>
   <cge:TPSR_Ref TObjectID="6192449803976710"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,800.625,974.125) scale(1.71875,1.71875) translate(-326.182,-392.986)" width="24" x="780" y="939.75"/></g>
  <g id="244">
   <use class="kv35" height="40" transform="rotate(0,1114.12,970.375) scale(1.71875,1.71875) translate(-457.282,-391.418)" width="24" x="1093.5" xlink:href="#Compensator:10kV电容器_0" y="936" zvalue="328"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449804763141" ObjectName="#3电容补偿装置"/>
   <cge:TPSR_Ref TObjectID="6192449804763141"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1114.12,970.375) scale(1.71875,1.71875) translate(-457.282,-391.418)" width="24" x="1093.5" y="936"/></g>
  <g id="277">
   <use class="kv35" height="40" transform="rotate(0,1446.62,969.125) scale(1.71875,1.71875) translate(-596.327,-390.895)" width="24" x="1426" xlink:href="#Compensator:10kV电容器_0" y="934.75" zvalue="362"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449805549573" ObjectName="#4电容补偿装置"/>
   <cge:TPSR_Ref TObjectID="6192449805549573"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1446.62,969.125) scale(1.71875,1.71875) translate(-596.327,-390.895)" width="24" x="1426" y="934.75"/></g>
 </g>
 <g id="StateClass">
  <g id="132">
   <use height="30" transform="rotate(0,329.768,340.5) scale(0.708333,0.665547) translate(131.412,166.093)" width="30" x="319.14" xlink:href="#State:红绿圆(方形)_0" y="330.52" zvalue="381"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374891155459" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.768,340.5) scale(0.708333,0.665547) translate(131.412,166.093)" width="30" x="319.14" y="330.52"/></g>
  <g id="94">
   <use height="30" transform="rotate(0,247.143,340.5) scale(0.708333,0.665547) translate(97.3897,166.093)" width="30" x="236.52" xlink:href="#State:红绿圆(方形)_0" y="330.52" zvalue="382"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562951470710788" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,247.143,340.5) scale(0.708333,0.665547) translate(97.3897,166.093)" width="30" x="236.52" y="330.52"/></g>
 </g>
</svg>