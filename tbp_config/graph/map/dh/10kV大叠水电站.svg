<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549592981506" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-Y_0" viewBox="0,0,30,50">
   <ellipse cx="14.99" cy="14.99" fill-opacity="0" rx="14.76" ry="14.76" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.99138374485597" x2="10.08333333333333" y1="11.06149193548387" y2="16.08333333333334"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01984739368999" x2="20" y1="11.06354954865259" y2="16"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00235768175583" x2="15.00235768175583" y1="6.416666666666664" y2="11.06149193548386"/>
   <use terminal-index="0" type="1" x="14.99138374485597" xlink:href="#terminal" y="0.2276829173857209"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-Y_1" viewBox="0,0,30,50">
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00235768175583" x2="15.00235768175583" y1="32" y2="37.04670698924731"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00546553497943" x2="20" y1="37.04651063100137" y2="41.75"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00235768175583" x2="10" y1="37.04670698924731" y2="41.91666666666666"/>
   <ellipse cx="15.01" cy="35.25" fill-opacity="0" rx="14.76" ry="14.76" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="1" x="15.01333161865569" xlink:href="#terminal" y="50.00705645161292"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV大叠水电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="42.25" xlink:href="logo.png" y="42.5"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,176.875,72.5) scale(1,1) translate(0,0)" writing-mode="lr" x="176.88" xml:space="preserve" y="76" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,178.583,72.1903) scale(1,1) translate(6.68909e-15,0)" writing-mode="lr" x="178.58" xml:space="preserve" y="81.19" zvalue="3">10kV大叠水电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="25" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,82.75,313.5) scale(1,1) translate(0,0)" width="97" x="34.25" y="301.5" zvalue="9"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.75,313.5) scale(1,1) translate(0,0)" writing-mode="lr" x="82.75" xml:space="preserve" y="318" zvalue="9">全站公用</text>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="376.25" x2="376.25" y1="10.5" y2="1040.5" zvalue="4"/>
  <line fill="none" id="28" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.250000000000227" x2="369.2499999999998" y1="146.3704926140824" y2="146.3704926140824" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.25" x2="184.25" y1="158.5" y2="158.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.25" x2="184.25" y1="184.5" y2="184.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.25" x2="3.25" y1="158.5" y2="184.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.25" x2="184.25" y1="158.5" y2="184.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.25" x2="365.25" y1="158.5" y2="158.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.25" x2="365.25" y1="184.5" y2="184.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.25" x2="184.25" y1="158.5" y2="184.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.25" x2="365.25" y1="158.5" y2="184.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.25" x2="184.25" y1="184.5" y2="184.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.25" x2="184.25" y1="208.75" y2="208.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.25" x2="3.25" y1="184.5" y2="208.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.25" x2="184.25" y1="184.5" y2="208.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.25" x2="365.25" y1="184.5" y2="184.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.25" x2="365.25" y1="208.75" y2="208.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.25" x2="184.25" y1="184.5" y2="208.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.25" x2="365.25" y1="184.5" y2="208.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.25" x2="184.25" y1="208.75" y2="208.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.25" x2="184.25" y1="231.5" y2="231.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.25" x2="3.25" y1="208.75" y2="231.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.25" x2="184.25" y1="208.75" y2="231.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.25" x2="365.25" y1="208.75" y2="208.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.25" x2="365.25" y1="231.5" y2="231.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.25" x2="184.25" y1="208.75" y2="231.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.25" x2="365.25" y1="208.75" y2="231.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.25" x2="184.25" y1="231.5" y2="231.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.25" x2="184.25" y1="254.25" y2="254.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.25" x2="3.25" y1="231.5" y2="254.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.25" x2="184.25" y1="231.5" y2="254.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.25" x2="365.25" y1="231.5" y2="231.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.25" x2="365.25" y1="254.25" y2="254.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.25" x2="184.25" y1="231.5" y2="254.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.25" x2="365.25" y1="231.5" y2="254.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.25" x2="184.25" y1="254.25" y2="254.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.25" x2="184.25" y1="277" y2="277"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.25" x2="3.25" y1="254.25" y2="277"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.25" x2="184.25" y1="254.25" y2="277"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.25" x2="365.25" y1="254.25" y2="254.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.25" x2="365.25" y1="277" y2="277"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.25" x2="184.25" y1="254.25" y2="277"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.25" x2="365.25" y1="254.25" y2="277"/>
  <line fill="none" id="26" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.250000000000227" x2="369.2499999999998" y1="616.3704926140824" y2="616.3704926140824" zvalue="8"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.47236173734677" x2="103.8170617373469" y1="439.1666435058594" y2="439.1666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.47236173734677" x2="103.8170617373469" y1="476.6566435058594" y2="476.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.47236173734677" x2="55.47236173734677" y1="439.1666435058594" y2="476.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8170617373469" x2="103.8170617373469" y1="439.1666435058594" y2="476.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8173617373468" x2="165.9256617373469" y1="439.1666435058594" y2="439.1666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8173617373468" x2="165.9256617373469" y1="476.6566435058594" y2="476.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8173617373468" x2="103.8173617373468" y1="439.1666435058594" y2="476.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9256617373469" x2="165.9256617373469" y1="439.1666435058594" y2="476.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9251617373468" x2="229.2499617373469" y1="439.1666435058594" y2="439.1666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9251617373468" x2="229.2499617373469" y1="476.6566435058594" y2="476.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9251617373468" x2="165.9251617373468" y1="439.1666435058594" y2="476.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2499617373469" x2="229.2499617373469" y1="439.1666435058594" y2="476.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2498617373468" x2="291.3581617373468" y1="439.1666435058594" y2="439.1666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2498617373468" x2="291.3581617373468" y1="476.6566435058594" y2="476.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2498617373468" x2="229.2498617373468" y1="439.1666435058594" y2="476.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="291.3581617373468" y1="439.1666435058594" y2="476.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="353.4664617373469" y1="439.1666435058594" y2="439.1666435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="353.4664617373469" y1="476.6566435058594" y2="476.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="291.3581617373468" y1="439.1666435058594" y2="476.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="353.4664617373469" x2="353.4664617373469" y1="439.1666435058594" y2="476.6566435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.47236173734677" x2="103.8170617373469" y1="476.6567435058594" y2="476.6567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.47236173734677" x2="103.8170617373469" y1="500.8253435058594" y2="500.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.47236173734677" x2="55.47236173734677" y1="476.6567435058594" y2="500.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8170617373469" x2="103.8170617373469" y1="476.6567435058594" y2="500.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8173617373468" x2="165.9256617373469" y1="476.6567435058594" y2="476.6567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8173617373468" x2="165.9256617373469" y1="500.8253435058594" y2="500.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8173617373468" x2="103.8173617373468" y1="476.6567435058594" y2="500.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9256617373469" x2="165.9256617373469" y1="476.6567435058594" y2="500.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9251617373468" x2="229.2499617373469" y1="476.6567435058594" y2="476.6567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9251617373468" x2="229.2499617373469" y1="500.8253435058594" y2="500.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9251617373468" x2="165.9251617373468" y1="476.6567435058594" y2="500.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2499617373469" x2="229.2499617373469" y1="476.6567435058594" y2="500.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2498617373468" x2="291.3581617373468" y1="476.6567435058594" y2="476.6567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2498617373468" x2="291.3581617373468" y1="500.8253435058594" y2="500.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2498617373468" x2="229.2498617373468" y1="476.6567435058594" y2="500.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="291.3581617373468" y1="476.6567435058594" y2="500.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="353.4664617373469" y1="476.6567435058594" y2="476.6567435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="353.4664617373469" y1="500.8253435058594" y2="500.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="291.3581617373468" y1="476.6567435058594" y2="500.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="353.4664617373469" x2="353.4664617373469" y1="476.6567435058594" y2="500.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.47236173734677" x2="103.8170617373469" y1="500.8253435058594" y2="500.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.47236173734677" x2="103.8170617373469" y1="524.9939435058594" y2="524.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.47236173734677" x2="55.47236173734677" y1="500.8253435058594" y2="524.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8170617373469" x2="103.8170617373469" y1="500.8253435058594" y2="524.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8173617373468" x2="165.9256617373469" y1="500.8253435058594" y2="500.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8173617373468" x2="165.9256617373469" y1="524.9939435058594" y2="524.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8173617373468" x2="103.8173617373468" y1="500.8253435058594" y2="524.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9256617373469" x2="165.9256617373469" y1="500.8253435058594" y2="524.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9251617373468" x2="229.2499617373469" y1="500.8253435058594" y2="500.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9251617373468" x2="229.2499617373469" y1="524.9939435058594" y2="524.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9251617373468" x2="165.9251617373468" y1="500.8253435058594" y2="524.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2499617373469" x2="229.2499617373469" y1="500.8253435058594" y2="524.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2498617373468" x2="291.3581617373468" y1="500.8253435058594" y2="500.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2498617373468" x2="291.3581617373468" y1="524.9939435058594" y2="524.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2498617373468" x2="229.2498617373468" y1="500.8253435058594" y2="524.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="291.3581617373468" y1="500.8253435058594" y2="524.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="353.4664617373469" y1="500.8253435058594" y2="500.8253435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="353.4664617373469" y1="524.9939435058594" y2="524.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="291.3581617373468" y1="500.8253435058594" y2="524.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="353.4664617373469" x2="353.4664617373469" y1="500.8253435058594" y2="524.9939435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.47236173734677" x2="103.8170617373469" y1="524.9939835058593" y2="524.9939835058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.47236173734677" x2="103.8170617373469" y1="549.1625835058594" y2="549.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.47236173734677" x2="55.47236173734677" y1="524.9939835058593" y2="549.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8170617373469" x2="103.8170617373469" y1="524.9939835058593" y2="549.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8173617373468" x2="165.9256617373469" y1="524.9939835058593" y2="524.9939835058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8173617373468" x2="165.9256617373469" y1="549.1625835058594" y2="549.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8173617373468" x2="103.8173617373468" y1="524.9939835058593" y2="549.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9256617373469" x2="165.9256617373469" y1="524.9939835058593" y2="549.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9251617373468" x2="229.2499617373469" y1="524.9939835058593" y2="524.9939835058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9251617373468" x2="229.2499617373469" y1="549.1625835058594" y2="549.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9251617373468" x2="165.9251617373468" y1="524.9939835058593" y2="549.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2499617373469" x2="229.2499617373469" y1="524.9939835058593" y2="549.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2498617373468" x2="291.3581617373468" y1="524.9939835058593" y2="524.9939835058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2498617373468" x2="291.3581617373468" y1="549.1625835058594" y2="549.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2498617373468" x2="229.2498617373468" y1="524.9939835058593" y2="549.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="291.3581617373468" y1="524.9939835058593" y2="549.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="353.4664617373469" y1="524.9939835058593" y2="524.9939835058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="353.4664617373469" y1="549.1625835058594" y2="549.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="291.3581617373468" y1="524.9939835058593" y2="549.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="353.4664617373469" x2="353.4664617373469" y1="524.9939835058593" y2="549.1625835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.47236173734677" x2="103.8170617373469" y1="549.1627435058593" y2="549.1627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.47236173734677" x2="103.8170617373469" y1="573.3313435058594" y2="573.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.47236173734677" x2="55.47236173734677" y1="549.1627435058593" y2="573.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8170617373469" x2="103.8170617373469" y1="549.1627435058593" y2="573.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8173617373468" x2="165.9256617373469" y1="549.1627435058593" y2="549.1627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8173617373468" x2="165.9256617373469" y1="573.3313435058594" y2="573.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8173617373468" x2="103.8173617373468" y1="549.1627435058593" y2="573.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9256617373469" x2="165.9256617373469" y1="549.1627435058593" y2="573.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9251617373468" x2="229.2499617373469" y1="549.1627435058593" y2="549.1627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9251617373468" x2="229.2499617373469" y1="573.3313435058594" y2="573.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9251617373468" x2="165.9251617373468" y1="549.1627435058593" y2="573.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2499617373469" x2="229.2499617373469" y1="549.1627435058593" y2="573.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2498617373468" x2="291.3581617373468" y1="549.1627435058593" y2="549.1627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2498617373468" x2="291.3581617373468" y1="573.3313435058594" y2="573.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2498617373468" x2="229.2498617373468" y1="549.1627435058593" y2="573.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="291.3581617373468" y1="549.1627435058593" y2="573.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="353.4664617373469" y1="549.1627435058593" y2="549.1627435058593"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="353.4664617373469" y1="573.3313435058594" y2="573.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="291.3581617373468" y1="549.1627435058593" y2="573.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="353.4664617373469" x2="353.4664617373469" y1="549.1627435058593" y2="573.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.47236173734677" x2="103.8170617373469" y1="573.3313435058594" y2="573.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.47236173734677" x2="103.8170617373469" y1="597.4999435058594" y2="597.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="55.47236173734677" x2="55.47236173734677" y1="573.3313435058594" y2="597.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8170617373469" x2="103.8170617373469" y1="573.3313435058594" y2="597.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8173617373468" x2="165.9256617373469" y1="573.3313435058594" y2="573.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8173617373468" x2="165.9256617373469" y1="597.4999435058594" y2="597.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.8173617373468" x2="103.8173617373468" y1="573.3313435058594" y2="597.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9256617373469" x2="165.9256617373469" y1="573.3313435058594" y2="597.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9251617373468" x2="229.2499617373469" y1="573.3313435058594" y2="573.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9251617373468" x2="229.2499617373469" y1="597.4999435058594" y2="597.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.9251617373468" x2="165.9251617373468" y1="573.3313435058594" y2="597.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2499617373469" x2="229.2499617373469" y1="573.3313435058594" y2="597.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2498617373468" x2="291.3581617373468" y1="573.3313435058594" y2="573.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2498617373468" x2="291.3581617373468" y1="597.4999435058594" y2="597.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.2498617373468" x2="229.2498617373468" y1="573.3313435058594" y2="597.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="291.3581617373468" y1="573.3313435058594" y2="597.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="353.4664617373469" y1="573.3313435058594" y2="573.3313435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="353.4664617373469" y1="597.4999435058594" y2="597.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="291.3581617373468" x2="291.3581617373468" y1="573.3313435058594" y2="597.4999435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="353.4664617373469" x2="353.4664617373469" y1="573.3313435058594" y2="597.4999435058594"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.25" x2="92.25" y1="931.5000000000002" y2="931.5000000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.25" x2="92.25" y1="970.6633000000002" y2="970.6633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.25" x2="2.25" y1="931.5000000000002" y2="970.6633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.25" x2="92.25" y1="931.5000000000002" y2="970.6633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.25" x2="362.25" y1="931.5000000000002" y2="931.5000000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.25" x2="362.25" y1="970.6633000000002" y2="970.6633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.25" x2="92.25" y1="931.5000000000002" y2="970.6633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362.25" x2="362.25" y1="931.5000000000002" y2="970.6633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.25" x2="92.25" y1="970.6632700000002" y2="970.6632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.25" x2="92.25" y1="998.5816700000003" y2="998.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.25" x2="2.25" y1="970.6632700000002" y2="998.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.25" x2="92.25" y1="970.6632700000002" y2="998.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.25" x2="182.25" y1="970.6632700000002" y2="970.6632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.25" x2="182.25" y1="998.5816700000003" y2="998.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.25" x2="92.25" y1="970.6632700000002" y2="998.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.25" x2="182.25" y1="970.6632700000002" y2="998.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.2500000000001" x2="272.2500000000001" y1="970.6632700000002" y2="970.6632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.2500000000001" x2="272.2500000000001" y1="998.5816700000003" y2="998.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.2500000000001" x2="182.2500000000001" y1="970.6632700000002" y2="998.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.2500000000001" x2="272.2500000000001" y1="970.6632700000002" y2="998.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.25" x2="362.25" y1="970.6632700000002" y2="970.6632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.25" x2="362.25" y1="998.5816700000003" y2="998.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.25" x2="272.25" y1="970.6632700000002" y2="998.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362.25" x2="362.25" y1="970.6632700000002" y2="998.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.25" x2="92.25" y1="998.5816000000002" y2="998.5816000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.25" x2="92.25" y1="1026.5" y2="1026.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.25" x2="2.25" y1="998.5816000000002" y2="1026.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.25" x2="92.25" y1="998.5816000000002" y2="1026.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.25" x2="182.25" y1="998.5816000000002" y2="998.5816000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.25" x2="182.25" y1="1026.5" y2="1026.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.25" x2="92.25" y1="998.5816000000002" y2="1026.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.25" x2="182.25" y1="998.5816000000002" y2="1026.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.2500000000001" x2="272.2500000000001" y1="998.5816000000002" y2="998.5816000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.2500000000001" x2="272.2500000000001" y1="1026.5" y2="1026.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.2500000000001" x2="182.2500000000001" y1="998.5816000000002" y2="1026.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.2500000000001" x2="272.2500000000001" y1="998.5816000000002" y2="1026.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.25" x2="362.25" y1="998.5816000000002" y2="998.5816000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.25" x2="362.25" y1="1026.5" y2="1026.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.25" x2="272.25" y1="998.5816000000002" y2="1026.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362.25" x2="362.25" y1="998.5816000000002" y2="1026.5"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.25,951.5) scale(1,1) translate(0,0)" writing-mode="lr" x="47.25" xml:space="preserve" y="957.5" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.25,985.5) scale(1,1) translate(0,0)" writing-mode="lr" x="44.25" xml:space="preserve" y="991.5" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.25,985.5) scale(1,1) translate(0,0)" writing-mode="lr" x="226.25" xml:space="preserve" y="991.5" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,43.25,1013.5) scale(1,1) translate(0,0)" writing-mode="lr" x="43.25" xml:space="preserve" y="1019.5" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225.25,1013.5) scale(1,1) translate(0,0)" writing-mode="lr" x="225.25" xml:space="preserve" y="1019.5" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.75,646) scale(1,1) translate(0,0)" writing-mode="lr" x="67.75" xml:space="preserve" y="650.5" zvalue="18">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,200.649,313.341) scale(1,1) translate(0,0)" writing-mode="lr" x="200.65" xml:space="preserve" y="317.84" zvalue="19">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,305.649,313.341) scale(1,1) translate(0,0)" writing-mode="lr" x="305.65" xml:space="preserve" y="317.84" zvalue="20">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.25,489) scale(1,1) translate(0,0)" writing-mode="lr" x="79.25" xml:space="preserve" y="493.5" zvalue="21">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.25,514.5) scale(1,1) translate(0,0)" writing-mode="lr" x="79.25" xml:space="preserve" y="519" zvalue="22">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.25,540) scale(1,1) translate(0,0)" writing-mode="lr" x="79.25" xml:space="preserve" y="544.5" zvalue="23">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.25,564.5) scale(1,1) translate(0,0)" writing-mode="lr" x="78.25" xml:space="preserve" y="569" zvalue="24">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,79.25,591) scale(1,1) translate(0,0)" writing-mode="lr" x="79.25" xml:space="preserve" y="595.5" zvalue="25">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.304,953.5) scale(1,1) translate(0,0)" writing-mode="lr" x="227.3" xml:space="preserve" y="959.5" zvalue="26">MGS4-01-2008</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,137.304,985.5) scale(1,1) translate(0,0)" writing-mode="lr" x="137.3" xml:space="preserve" y="991.5" zvalue="27">何成飞</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,317.304,985.5) scale(1,1) translate(0,0)" writing-mode="lr" x="317.3" xml:space="preserve" y="991.5" zvalue="28">20210510</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,41.25,172.5) scale(1,1) translate(0,0)" writing-mode="lr" x="41.25" xml:space="preserve" y="178" zvalue="29">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,221.25,172.5) scale(1,1) translate(0,0)" writing-mode="lr" x="221.25" xml:space="preserve" y="178" zvalue="30">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.4375,244.5) scale(1,1) translate(0,0)" writing-mode="lr" x="48.44" xml:space="preserve" y="249" zvalue="31">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.333,199.194) scale(1,1) translate(0,0)" writing-mode="lr" x="233.33" xml:space="preserve" y="203.69" zvalue="32">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" x="254.625" xml:space="preserve" y="457.25" zvalue="33">0.4kV  母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="254.625" xml:space="preserve" y="473.25" zvalue="33">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1723.88,502) scale(1,1) translate(0,0)" writing-mode="lr" x="1723.88" xml:space="preserve" y="506.5" zvalue="35">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,774,940) scale(1,1) translate(0,0)" writing-mode="lr" x="774" xml:space="preserve" y="944.5" zvalue="37">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,726.125,700.75) scale(1,1) translate(0,0)" writing-mode="lr" x="726.13" xml:space="preserve" y="705.25" zvalue="45">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,795.125,784.5) scale(1,1) translate(0,0)" writing-mode="lr" x="795.13" xml:space="preserve" y="789" zvalue="48">401</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,793.875,590.125) scale(1,1) translate(0,0)" writing-mode="lr" x="793.88" xml:space="preserve" y="594.63" zvalue="52">001</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,730,67.5) scale(1,1) translate(0,0)" writing-mode="lr" x="730" xml:space="preserve" y="72" zvalue="55">10kV大叠线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,752.625,343.875) scale(1,1) translate(0,0)" writing-mode="lr" x="752.63" xml:space="preserve" y="348.38" zvalue="58">061</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1339.88,941.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1339.88" xml:space="preserve" y="945.75" zvalue="62">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1292,702) scale(1,1) translate(0,0)" writing-mode="lr" x="1292" xml:space="preserve" y="706.5" zvalue="64">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1361,785.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1361" xml:space="preserve" y="790.25" zvalue="66">402</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1359.75,591.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1359.75" xml:space="preserve" y="595.88" zvalue="71">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,725,722.5) scale(1,1) translate(0,0)" writing-mode="lr" x="725" xml:space="preserve" y="727" zvalue="75">800kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1295,720.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1295" xml:space="preserve" y="725" zvalue="77">500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,775,961.5) scale(1,1) translate(0,0)" writing-mode="lr" x="775" xml:space="preserve" y="966" zvalue="78">630kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1342,959.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1342" xml:space="preserve" y="964" zvalue="80">400kW</text>
 </g>
 <g id="ButtonClass">
  <g href="500kV德宏变_全站公用.svg"><rect fill-opacity="0" height="24" width="97" x="34.25" y="301.5" zvalue="9"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="33">
   <path class="kv10" d="M 496.25 503 L 1686.25 503" stroke-width="6" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674254258180" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674254258180"/></metadata>
  <path d="M 496.25 503 L 1686.25 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="35">
   <use class="v400" height="30" transform="rotate(0,771.25,899.25) scale(1.875,1.875) translate(-346.792,-406.525)" width="30" x="743.125" xlink:href="#Generator:发电机_0" y="871.125" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450022539269" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192450022539269"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,771.25,899.25) scale(1.875,1.875) translate(-346.792,-406.525)" width="30" x="743.125" y="871.125"/></g>
  <g id="74">
   <use class="v400" height="30" transform="rotate(0,1337.12,900.5) scale(1.875,1.875) translate(-610.867,-407.108)" width="30" x="1309" xlink:href="#Generator:发电机_0" y="872.375" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450022670341" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192450022670341"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1337.12,900.5) scale(1.875,1.875) translate(-610.867,-407.108)" width="30" x="1309" y="872.375"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="42">
   <g id="420">
    <use class="kv10" height="50" transform="rotate(0,771.25,701.75) scale(1.25,1.25) translate(-150.5,-134.1)" width="30" x="752.5" xlink:href="#PowerTransformer2:Y-Y_0" y="670.5" zvalue="44"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874455711747" ObjectName="10"/>
    </metadata>
   </g>
   <g id="421">
    <use class="v400" height="50" transform="rotate(0,771.25,701.75) scale(1.25,1.25) translate(-150.5,-134.1)" width="30" x="752.5" xlink:href="#PowerTransformer2:Y-Y_1" y="670.5" zvalue="44"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874455777283" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399459733507" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399459733507"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,771.25,701.75) scale(1.25,1.25) translate(-150.5,-134.1)" width="30" x="752.5" y="670.5"/></g>
  <g id="73">
   <g id="730">
    <use class="kv10" height="50" transform="rotate(0,1337.12,703) scale(1.25,1.25) translate(-263.675,-134.35)" width="30" x="1318.38" xlink:href="#PowerTransformer2:Y-Y_0" y="671.75" zvalue="63"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874455842819" ObjectName="10"/>
    </metadata>
   </g>
   <g id="731">
    <use class="v400" height="50" transform="rotate(0,1337.12,703) scale(1.25,1.25) translate(-263.675,-134.35)" width="30" x="1318.38" xlink:href="#PowerTransformer2:Y-Y_1" y="671.75" zvalue="63"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874455908355" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399459799043" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399459799043"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1337.12,703) scale(1.25,1.25) translate(-263.675,-134.35)" width="30" x="1318.38" y="671.75"/></g>
 </g>
 <g id="BreakerClass">
  <g id="45">
   <use class="v400" height="20" transform="rotate(0,771.25,785.5) scale(1.875,1.6875) translate(-355.542,-313.144)" width="10" x="761.875" xlink:href="#Breaker:开关_0" y="768.625" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924558258181" ObjectName="#1主变401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924558258181"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,771.25,785.5) scale(1.875,1.6875) translate(-355.542,-313.144)" width="10" x="761.875" y="768.625"/></g>
  <g id="48">
   <use class="kv10" height="20" transform="rotate(0,769.375,591.125) scale(1.875,1.6875) translate(-354.667,-233.954)" width="10" x="760" xlink:href="#Breaker:开关_0" y="574.25" zvalue="51"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924558323718" ObjectName="#1主变001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924558323718"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,769.375,591.125) scale(1.875,1.6875) translate(-354.667,-233.954)" width="10" x="760" y="574.25"/></g>
  <g id="56">
   <use class="kv10" height="20" transform="rotate(0,728.125,344.875) scale(1.875,1.6875) translate(-335.417,-133.63)" width="10" x="718.75" xlink:href="#Breaker:开关_0" y="328" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924558389254" ObjectName="10kV大叠线061断路器"/>
   <cge:TPSR_Ref TObjectID="6473924558389254"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,728.125,344.875) scale(1.875,1.6875) translate(-335.417,-133.63)" width="10" x="718.75" y="328"/></g>
  <g id="72">
   <use class="v400" height="20" transform="rotate(0,1337.12,786.75) scale(1.875,1.6875) translate(-619.617,-313.653)" width="10" x="1327.75" xlink:href="#Breaker:开关_0" y="769.875" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924558520325" ObjectName="#2主变402断路器"/>
   <cge:TPSR_Ref TObjectID="6473924558520325"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1337.12,786.75) scale(1.875,1.6875) translate(-619.617,-313.653)" width="10" x="1327.75" y="769.875"/></g>
  <g id="69">
   <use class="kv10" height="20" transform="rotate(0,1335.25,592.375) scale(1.875,1.6875) translate(-618.742,-234.463)" width="10" x="1325.875" xlink:href="#Breaker:开关_0" y="575.5" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924558454789" ObjectName="#2主变002断路器"/>
   <cge:TPSR_Ref TObjectID="6473924558454789"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1335.25,592.375) scale(1.875,1.6875) translate(-618.742,-234.463)" width="10" x="1325.875" y="575.5"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="46">
   <path class="v400" d="M 771.27 733.01 L 771.19 769.36" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@1" LinkObjectIDznd="45@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 771.27 733.01 L 771.19 769.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="v400" d="M 771.38 801.62 L 771.38 871.59" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@1" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 771.38 801.62 L 771.38 871.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv10" d="M 771.24 670.78 L 771.24 607.24" stroke-width="1" zvalue="52"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@0" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 771.24 670.78 L 771.24 607.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv10" d="M 769.31 574.98 L 769.31 503" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@0" LinkObjectIDznd="33@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 769.31 574.98 L 769.31 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv10" d="M 730 140.89 L 730 328.73" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@0" LinkObjectIDznd="56@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 730 140.89 L 730 328.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv10" d="M 728.25 360.99 L 728.25 503" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@1" LinkObjectIDznd="33@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 728.25 360.99 L 728.25 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="v400" d="M 1337.14 734.26 L 1337.06 770.61" stroke-width="1" zvalue="67"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@1" LinkObjectIDznd="72@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1337.14 734.26 L 1337.06 770.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="v400" d="M 1337.25 802.87 L 1337.25 872.84" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="72@1" LinkObjectIDznd="74@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1337.25 802.87 L 1337.25 872.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv10" d="M 1337.11 672.03 L 1337.11 608.49" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@0" LinkObjectIDznd="69@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1337.11 672.03 L 1337.11 608.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv10" d="M 1335.19 576.23 L 1335.19 503" stroke-width="1" zvalue="72"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="33@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1335.19 576.23 L 1335.19 503" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
</svg>