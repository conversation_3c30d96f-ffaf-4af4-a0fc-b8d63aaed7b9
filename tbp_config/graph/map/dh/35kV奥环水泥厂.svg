<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549586952194" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:母线电压互感器11_0" viewBox="0,0,45,48">
   <use terminal-index="0" type="0" x="25.25" xlink:href="#terminal" y="47.78457520218115"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.5,30.95) scale(1,-1) translate(0,-1334.05)" width="6" x="10.5" y="23.95"/>
   <path d="M 8.64167 11.4096 L 4.64167 11.4096 L 6.50709 7.52624 L 7.67376 9.52624 L 8.64167 11.4096" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="13.5" y1="19.78457520218114" y2="40.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.25" x2="25.25" y1="40.5" y2="47.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="37.25" x2="13.58333333333333" y1="40.5" y2="40.5"/>
   <ellipse cx="13.56" cy="5.42" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799663" x2="13.56620079799663" y1="3.155388266900378" y2="5.82957386674455"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799661" x2="10.88240159599323" y1="5.829573866744575" y2="7.166666666666657"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799662" x2="16.25" y1="5.829573866744575" y2="7.166666666666657"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,36.96,25.94) scale(1,-1) translate(0,-1133.72)" width="7.58" x="33.17" y="17.87"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="34.01790853551448" y2="24.11790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="40.43457520218114" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.7861111111111" x2="39.3" y1="11.60124186884782" y2="11.60124186884782"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.82777777777777" x2="39.93888888888888" y1="12.98282081621623" y2="12.98282081621623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.55" x2="41.21666666666666" y1="14.36439976358464" y2="14.36439976358464"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="34.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="18.01790853551448" y2="14.41790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="40.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <ellipse cx="13.59" cy="15.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.51" cy="10.09" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.67" cy="10.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490864" x2="10.91228003290526" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="13.59607923490865" y1="12.74178419246191" y2="15.41596979230608"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51274590157532" x2="23.19654510357869" y1="10.49930312563944" y2="11.83639592556152"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51274590157532" x2="20.51274590157532" y1="7.825117525795246" y2="10.49930312563942"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="16.27987843691203" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5127459015753" x2="17.82894669957193" y1="10.49930312563944" y2="11.83639592556152"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变20201015_0" viewBox="0,0,18,37">
   <use terminal-index="0" type="0" x="8.949999999999999" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="29.5" y2="36.5"/>
   <ellipse cx="9.039999999999999" cy="8.720000000000001" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.970000000000001" cy="20.89" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.035256190670173" x2="9.035256190670173" y1="17.85" y2="21.95894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.035256190670172" x2="5.05" y1="21.98394833233987" y2="25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.063029030724982" x2="13.15" y1="21.93990500916851" y2="24.75"/>
   <path d="M 9 5.08333 L 4.08333 10.6667 L 14.0833 10.6667 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 9 35.5 L 7.58333 33.5 L 10.25 33.5 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000004" y2="0.9166666666666679"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.89694619969018" y2="15.89694619969018"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.73157590710537" y2="17.73157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.48348701738202" y2="19.48348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.05" x2="5.05" y1="3.566666666666666" y2="15.81666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变无融断_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <path d="M 10 9 L 10 0" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="26.83333333333333" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="10.8868007916835" y2="12.55855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="14.23031840279781" y2="12.55855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="4.583333333333332" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV奥环水泥厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,189.475,86.7) scale(1,1) translate(1.05177e-14,-5.06262e-14)" writing-mode="lr" x="189.48" xml:space="preserve" y="91.2" zvalue="3"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="1" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,213.429,36.2122) scale(1,1) translate(0,1.5769e-15)" writing-mode="lr" x="213.43" xml:space="preserve" y="43.71" zvalue="4">    35kV奥环水泥厂</text>
  <line fill="none" id="31" stroke="rgb(0,39,45)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="43.65079365079418" x2="341.2383422889277" y1="69.77335209120952" y2="69.77335209120952" zvalue="5"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,236.875,64.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="236.88" xml:space="preserve" y="73.69" zvalue="62">35kV奥环水泥厂</text>
  <image height="60" id="3" preserveAspectRatio="xMidYMid slice" width="277.25" x="57.75" xlink:href="logo.png" y="36.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.375,66.75) scale(1,1) translate(0,0)" writing-mode="lr" x="196.38" xml:space="preserve" y="70.25" zvalue="63"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="4" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,191,63.9403) scale(1,1) translate(0,0)" writing-mode="lr" x="191" xml:space="preserve" y="72.94" zvalue="64">35kV奥环水泥厂</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="40" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,99.4375,327) scale(1,1) translate(0,0)" width="72.88" x="63" y="315" zvalue="74"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,99.4375,327) scale(1,1) translate(0,0)" writing-mode="lr" x="99.44" xml:space="preserve" y="331.5" zvalue="74">信号一览</text>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="397.4285714285716" x2="397.4285714285716" y1="42.5727941176483" y2="1032.572794117648" zvalue="6"/>
  <line fill="none" id="28" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.90549597855158" x2="340.5167560321707" y1="162.2007400004169" y2="162.2007400004169" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="64" x2="135.4400000000001" y1="921.1355850567206" y2="921.1355850567206"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="64" x2="135.4400000000001" y1="972.4750850567207" y2="972.4750850567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="64" x2="64" y1="921.1355850567206" y2="972.4750850567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="135.4400000000001" x2="135.4400000000001" y1="921.1355850567206" y2="972.4750850567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="135.4405" x2="370.0005" y1="921.1355850567206" y2="921.1355850567206"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="135.4405" x2="370.0005" y1="972.4750850567207" y2="972.4750850567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="135.4405" x2="135.4405" y1="921.1355850567206" y2="972.4750850567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.0005" x2="370.0005" y1="921.1355850567206" y2="972.4750850567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="64" x2="135.4400000000001" y1="972.4750650567207" y2="972.4750650567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="64" x2="135.4400000000001" y1="999.9525650567207" y2="999.9525650567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="64" x2="64" y1="972.4750650567207" y2="999.9525650567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="135.4400000000001" x2="135.4400000000001" y1="972.4750650567207" y2="999.9525650567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="135.4405" x2="206.4758" y1="972.4750650567207" y2="972.4750650567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="135.4405" x2="206.4758" y1="999.9525650567207" y2="999.9525650567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="135.4405" x2="135.4405" y1="972.4750650567207" y2="999.9525650567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="206.4758" x2="206.4758" y1="972.4750650567207" y2="999.9525650567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="206.4758" x2="288.2379" y1="972.4750650567207" y2="972.4750650567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="206.4758" x2="288.2379" y1="999.9525650567207" y2="999.9525650567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="206.4758" x2="206.4758" y1="972.4750650567207" y2="999.9525650567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="288.2379" x2="288.2379" y1="972.4750650567207" y2="999.9525650567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="288.2378" x2="369.9999" y1="972.4750650567207" y2="972.4750650567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="288.2378" x2="369.9999" y1="999.9525650567207" y2="999.9525650567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="288.2378" x2="288.2378" y1="972.4750650567207" y2="999.9525650567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="369.9999" x2="369.9999" y1="972.4750650567207" y2="999.9525650567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="64" x2="135.4400000000001" y1="999.9524850567207" y2="999.9524850567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="64" x2="135.4400000000001" y1="1027.429985056721" y2="1027.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="64" x2="64" y1="999.9524850567207" y2="1027.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="135.4400000000001" x2="135.4400000000001" y1="999.9524850567207" y2="1027.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="135.4405" x2="206.4758" y1="999.9524850567207" y2="999.9524850567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="135.4405" x2="206.4758" y1="1027.429985056721" y2="1027.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="135.4405" x2="135.4405" y1="999.9524850567207" y2="1027.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="206.4758" x2="206.4758" y1="999.9524850567207" y2="1027.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="206.4758" x2="288.2379" y1="999.9524850567207" y2="999.9524850567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="206.4758" x2="288.2379" y1="1027.429985056721" y2="1027.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="206.4758" x2="206.4758" y1="999.9524850567207" y2="1027.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="288.2379" x2="288.2379" y1="999.9524850567207" y2="1027.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="288.2378" x2="369.9999" y1="999.9524850567207" y2="999.9524850567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="288.2378" x2="369.9999" y1="1027.429985056721" y2="1027.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="288.2378" x2="288.2378" y1="999.9524850567207" y2="1027.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="369.9999" x2="369.9999" y1="999.9524850567207" y2="1027.429985056721"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="26" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,211.053,950.709) scale(1,1) translate(-1.54e-14,1.04348e-13)" writing-mode="lr" x="69.36" xml:space="preserve" y="956.71" zvalue="10">参考图号       AoHuan-01-2019</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="25" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,141.577,988.077) scale(1,1) translate(-7.02393e-14,-1.51895e-12)" writing-mode="lr" x="79.08" xml:space="preserve" y="994.08" zvalue="11">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,284.881,989.077) scale(1,1) translate(1.44003e-13,-1.52051e-12)" writing-mode="lr" x="216.18" xml:space="preserve" y="995.08" zvalue="12">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.6709,1017.64) scale(1,1) translate(-7.07209e-14,-1.5649e-12)" writing-mode="lr" x="92.67" xml:space="preserve" y="1023.64" zvalue="13">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,249.175,1015.64) scale(1,1) translate(0,1.11556e-13)" writing-mode="lr" x="216.35" xml:space="preserve" y="1021.64" zvalue="14">更新日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,106.349,634.009) scale(1,1) translate(7.92667e-15,-1.36626e-13)" writing-mode="lr" x="106.3492794906165" xml:space="preserve" y="638.5091752865154" zvalue="17">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="206" y1="165.4299369747912" y2="165.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="206" y1="191.4299369747912" y2="191.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="25" y1="165.4299369747912" y2="191.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="206" y1="165.4299369747912" y2="191.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="387" y1="165.4299369747912" y2="165.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="387" y1="191.4299369747912" y2="191.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="206" y1="165.4299369747912" y2="191.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="387" x2="387" y1="165.4299369747912" y2="191.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="206" y1="191.4299369747912" y2="191.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="206" y1="215.6799369747912" y2="215.6799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="25" y1="191.4299369747912" y2="215.6799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="206" y1="191.4299369747912" y2="215.6799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="387" y1="191.4299369747912" y2="191.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="387" y1="215.6799369747912" y2="215.6799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="206" y1="191.4299369747912" y2="215.6799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="387" x2="387" y1="191.4299369747912" y2="215.6799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="206" y1="215.6799369747912" y2="215.6799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="206" y1="238.4299369747912" y2="238.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="25" y1="215.6799369747912" y2="238.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="206" y1="215.6799369747912" y2="238.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="387" y1="215.6799369747912" y2="215.6799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="387" y1="238.4299369747912" y2="238.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="206" y1="215.6799369747912" y2="238.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="387" x2="387" y1="215.6799369747912" y2="238.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="206" y1="238.4299369747912" y2="238.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="206" y1="261.1799369747912" y2="261.1799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="25" y1="238.4299369747912" y2="261.1799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="206" y1="238.4299369747912" y2="261.1799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="387" y1="238.4299369747912" y2="238.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="387" y1="261.1799369747912" y2="261.1799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="206" y1="238.4299369747912" y2="261.1799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="387" x2="387" y1="238.4299369747912" y2="261.1799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="206" y1="261.1799369747912" y2="261.1799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="206" y1="283.9299369747912" y2="283.9299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="25" x2="25" y1="261.1799369747912" y2="283.9299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="206" y1="261.1799369747912" y2="283.9299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="387" y1="261.1799369747912" y2="261.1799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="387" y1="283.9299369747912" y2="283.9299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="206" x2="206" y1="261.1799369747912" y2="283.9299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="387" x2="387" y1="261.1799369747912" y2="283.9299369747912"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63,179.43) scale(1,1) translate(0,0)" writing-mode="lr" x="63" xml:space="preserve" y="184.93" zvalue="29">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,249.25,179.43) scale(1,1) translate(0,0)" writing-mode="lr" x="249.25" xml:space="preserve" y="184.93" zvalue="30">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,66.6875,203.68) scale(1,1) translate(0,0)" writing-mode="lr" x="66.69" xml:space="preserve" y="208.18" zvalue="31">35kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,71.1875,225.43) scale(1,1) translate(0,0)" writing-mode="lr" x="71.19" xml:space="preserve" y="229.93" zvalue="32">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,260.188,229.43) scale(1,1) translate(0,0)" writing-mode="lr" x="260.19" xml:space="preserve" y="233.93" zvalue="33">2号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,699.714,431.714) scale(1,1) translate(0,0)" writing-mode="lr" x="699.71" xml:space="preserve" y="436.21" zvalue="37">35kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,829.214,360.714) scale(1,1) translate(0,0)" writing-mode="lr" x="829.21" xml:space="preserve" y="365.21" zvalue="38">352</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1033.71,533.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1033.71" xml:space="preserve" y="538.21" zvalue="43">302</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" x="1073.953125" xml:space="preserve" y="727.768465909091" zvalue="47">#2主变   </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1073.953125" xml:space="preserve" y="743.768465909091" zvalue="47">10000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,981.214,622.714) scale(1,1) translate(0,0)" writing-mode="lr" x="981.21" xml:space="preserve" y="627.21" zvalue="48">30267</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1350.22,706.607) scale(1,1) translate(1.17037e-12,-1.54369e-13)" writing-mode="lr" x="1350.22" xml:space="preserve" y="711.11" zvalue="52">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1250.21,614.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1250.21" xml:space="preserve" y="619.21" zvalue="54">38267</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1391.21,537.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1391.21" xml:space="preserve" y="542.21" zvalue="55">382</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1256.21,396.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1256.21" xml:space="preserve" y="401.21" zvalue="59">3902</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,864,250.5) scale(1,1) translate(0,0)" writing-mode="lr" x="864" xml:space="preserve" y="255" zvalue="66">35kV东奥线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,172.671,1013.64) scale(1,1) translate(-1.59539e-13,-1.55868e-12)" writing-mode="lr" x="172.67" xml:space="preserve" y="1019.64" zvalue="68">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,204.068,325.091) scale(1,1) translate(0,0)" writing-mode="lr" x="204.07" xml:space="preserve" y="329.59" zvalue="72">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,309.068,325.091) scale(1,1) translate(0,0)" writing-mode="lr" x="309.07" xml:space="preserve" y="329.59" zvalue="73">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="63" y="315" zvalue="74"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="35">
   <path class="kv35" d="M 750.71 453.71 L 1463.71 453.71" stroke-width="4" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674243903492" ObjectName="35kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674243903492"/></metadata>
  <path d="M 750.71 453.71 L 1463.71 453.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="37">
   <use class="kv35" height="20" transform="rotate(0,862.714,360.714) scale(2.2,2.2) translate(-464.571,-184.753)" width="10" x="851.7142857142857" xlink:href="#Breaker:小车断路器_0" y="338.7142857142857" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924512710662" ObjectName="35kV东奥线352断路器"/>
   <cge:TPSR_Ref TObjectID="6473924512710662"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,862.714,360.714) scale(2.2,2.2) translate(-464.571,-184.753)" width="10" x="851.7142857142857" y="338.7142857142857"/></g>
  <g id="43">
   <use class="kv35" height="20" transform="rotate(0,1072.71,527.714) scale(2.2,2.2) translate(-579.117,-275.844)" width="10" x="1061.714285714286" xlink:href="#Breaker:小车断路器_0" y="505.7142857142857" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924512776197" ObjectName="#2主变302断路器"/>
   <cge:TPSR_Ref TObjectID="6473924512776197"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1072.71,527.714) scale(2.2,2.2) translate(-579.117,-275.844)" width="10" x="1061.714285714286" y="505.7142857142857"/></g>
  <g id="60">
   <use class="kv35" height="20" transform="rotate(0,1339.71,534.714) scale(2.2,2.2) translate(-724.753,-279.662)" width="10" x="1328.714285714286" xlink:href="#Breaker:小车断路器_0" y="512.7142857142857" zvalue="54"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924512841733" ObjectName="#2站用变382断路器"/>
   <cge:TPSR_Ref TObjectID="6473924512841733"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1339.71,534.714) scale(2.2,2.2) translate(-724.753,-279.662)" width="10" x="1328.714285714286" y="512.7142857142857"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="41">
   <path class="kv35" d="M 862.71 380.51 L 862.71 453.71" stroke-width="1" zvalue="39"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@1" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 862.71 380.51 L 862.71 453.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv35" d="M 1072.71 507.36 L 1072.71 453.71" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="35@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1072.71 507.36 L 1072.71 453.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv35" d="M 1072.25 617.7 L 1072.25 547.51" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@0" LinkObjectIDznd="43@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1072.25 617.7 L 1072.25 547.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv35" d="M 1020.76 608.21 L 1020.76 575.71 L 1072.25 575.71" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="54" MaxPinNum="2"/>
   </metadata>
  <path d="M 1020.76 608.21 L 1020.76 575.71 L 1072.25 575.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv35" d="M 1339.71 514.36 L 1339.71 453.71" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="35@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1339.71 514.36 L 1339.71 453.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv35" d="M 1341.05 604.7 L 1341.05 554.51" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@0" LinkObjectIDznd="60@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1341.05 604.7 L 1341.05 554.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv35" d="M 1299.77 604.21 L 1299.77 577.71 L 1341.05 577.71" stroke-width="1" zvalue="57"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@0" LinkObjectIDznd="63" MaxPinNum="2"/>
   </metadata>
  <path d="M 1299.77 604.21 L 1299.77 577.71 L 1341.05 577.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv35" d="M 1214.22 375.31 L 1214.22 339.5" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@1" LinkObjectIDznd="44@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1214.22 375.31 L 1214.22 339.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv35" d="M 1214.32 407.67 L 1214.32 453.71" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="35@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1214.32 407.67 L 1214.32 453.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv35" d="M 862.71 340.36 L 862.75 293.85" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="34@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 862.71 340.36 L 862.75 293.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="44">
   <use class="kv35" height="48" transform="rotate(0,1209.71,315.714) scale(1,1) translate(0,0)" width="45" x="1187.214285714286" xlink:href="#Accessory:母线电压互感器11_0" y="291.7142857142857" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449806204933" ObjectName="35kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,1209.71,315.714) scale(1,1) translate(0,0)" width="45" x="1187.214285714286" y="291.7142857142857"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="48">
   <use class="kv35" height="37" transform="rotate(0,1072.35,653.464) scale(1.95946,1.95946) translate(-516.446,-302.222)" width="18" x="1054.714285714286" xlink:href="#EnergyConsumer:站用变20201015_0" y="617.2142857142857" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449806270469" ObjectName="#2主变"/>
   </metadata>
  <rect fill="white" height="37" opacity="0" stroke="white" transform="rotate(0,1072.35,653.464) scale(1.95946,1.95946) translate(-516.446,-302.222)" width="18" x="1054.714285714286" y="617.2142857142857"/></g>
  <g id="56">
   <use class="kv35" height="30" transform="rotate(0,1341.05,637.714) scale(2.33333,2.33333) translate(-752.98,-344.408)" width="20" x="1317.714285714286" xlink:href="#EnergyConsumer:站用变无融断_0" y="602.7142857142857" zvalue="51"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449806467077" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1341.05,637.714) scale(2.33333,2.33333) translate(-752.98,-344.408)" width="20" x="1317.714285714286" y="602.7142857142857"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="50">
   <use class="kv35" height="20" transform="rotate(0,1020.71,617.714) scale(1,1) translate(0,0)" width="10" x="1015.714285714286" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="607.7142857142857" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449806401541" ObjectName="#2主变30267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449806401541"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1020.71,617.714) scale(1,1) translate(0,0)" width="10" x="1015.714285714286" y="607.7142857142857"/></g>
  <g id="58">
   <use class="kv35" height="20" transform="rotate(0,1299.71,613.714) scale(1.2,1) translate(-215.619,0)" width="10" x="1293.714285714286" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="603.7142857142857" zvalue="53"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449806598149" ObjectName="#2站用变38267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449806598149"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1299.71,613.714) scale(1.2,1) translate(-215.619,0)" width="10" x="1293.714285714286" y="603.7142857142857"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="65">
   <use class="kv35" height="26" transform="rotate(0,1214.21,391.464) scale(1.25,1.25) translate(-241.343,-75.0429)" width="12" x="1206.714285714286" xlink:href="#Disconnector:小车隔刀熔断器_0" y="375.2142857142857" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449806663685" ObjectName="35kVⅡ段母线电压互感器3902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449806663685"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1214.21,391.464) scale(1.25,1.25) translate(-241.343,-75.0429)" width="12" x="1206.714285714286" y="375.2142857142857"/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,339.17,325.483) scale(0.708333,0.665547) translate(135.283,158.546)" width="30" x="328.54" xlink:href="#State:红绿圆(方形)_0" y="315.5" zvalue="75"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,339.17,325.483) scale(0.708333,0.665547) translate(135.283,158.546)" width="30" x="328.54" y="315.5"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,243.545,325.483) scale(0.708333,0.665547) translate(95.9081,158.546)" width="30" x="232.92" xlink:href="#State:红绿圆(方形)_0" y="315.5" zvalue="76"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,243.545,325.483) scale(0.708333,0.665547) translate(95.9081,158.546)" width="30" x="232.92" y="315.5"/></g>
  <g id="830">
   <use height="30" transform="rotate(0,335.812,137.464) scale(1.22222,1.03092) translate(-51.0568,-3.65931)" width="90" x="280.81" xlink:href="#State:全站检修_0" y="122" zvalue="80"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549586952194" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,335.812,137.464) scale(1.22222,1.03092) translate(-51.0568,-3.65931)" width="90" x="280.81" y="122"/></g>
 </g>
</svg>