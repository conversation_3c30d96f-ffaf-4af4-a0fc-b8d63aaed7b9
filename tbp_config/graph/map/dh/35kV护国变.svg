<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549681717249" height="1045" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1045" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="12.0194189818494"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="5.416666666666668" y2="12.02321291373541"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.92126160277786" x2="6.666666666666666" y1="12.10207526123773" y2="18.50000000000001"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.18646053143751" x2="24.5" y1="12.18904818695178" y2="19.00000000000001"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 7.33333 43.5833 L 23 43.5833 L 15 31.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:接地可调两卷变（规范制图）_0" viewBox="0,0,15,20">
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.407783003462016" x2="5.342474279636929" y1="6.303393899561708" y2="4.443405305784673"/>
   <use terminal-index="0" type="1" x="7.400350919212375" xlink:href="#terminal" y="2.255340193223605"/>
   <use terminal-index="2" type="2" x="7.5" xlink:href="#terminal" y="6.249999999999999"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.546899427352102" x2="7.404066961337196" y1="4.317578864636594" y2="6.303393899561695"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.401384443428335" x2="7.401384443428335" y1="6.310758227164332" y2="8.34034281757342"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.91666666666667" x2="12.74547107366039" y1="3.553047675216829" y2="3.553047675216829"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.083333333333337" x2="12.75" y1="9.046663465350886" y2="3.552242201885312"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.7491871157852" x2="11.75" y1="3.553047675216865" y2="5.083333333333333"/>
   <ellipse cx="7.44" cy="6.46" fill-opacity="0" rx="4.25" ry="4.21" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:接地可调两卷变（规范制图）_1" viewBox="0,0,15,20">
   <path d="M 5.2379 14.7101 L 9.85166 14.7101 L 7.43896 11.0192 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="1" x="7.396634877087553" xlink:href="#terminal" y="17.07970653458569"/>
   <ellipse cx="7.44" cy="12.87" fill-opacity="0" rx="4.25" ry="4.21" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Accessory:四卷PT_0" viewBox="0,0,18,18">
   <use terminal-index="0" type="0" x="9" xlink:href="#terminal" y="0.2666666666666639"/>
   <ellipse cx="9.25" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.25" cy="9.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.08" cy="12.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.82" cy="9.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="11.75" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.25" x2="9.25" y1="7.500000000000001" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="15.25" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="12.31481481481481" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.94444444444444" x2="13.62962962962963" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14.94444444444444" y1="9.966124661246614" y2="9.966124661246614"/>
  </symbol>
  <symbol id="Compensator:10kV电容器_0" viewBox="0,0,24,40">
   <use terminal-index="0" type="0" x="12" xlink:href="#terminal" y="4.699999999999999"/>
   <path d="M 12 20.1 L 17.85 20.1 L 17.85 24.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="4.749074074074073" y1="35.35833333333333" y2="35.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.72129629629629" x2="4.72129629629629" y1="33.13611111111111" y2="35.37685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="24.85833333333333" y2="30.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.258333333333324" x2="0.258333333333324" y1="22.10833333333333" y2="33.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.75833333333333" x2="4.75833333333333" y1="22.35833333333333" y2="20.15462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="31.85833333333333" y2="35.37685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="20.04351851851851" y2="24.775"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.024239280774554" x2="4.024239280774554" y1="39.35833333333333" y2="37.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.786111111111114" x2="12.00833333333334" y1="20.10833333333333" y2="20.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="35.35833333333333" y2="39.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.84166666666666" x2="4.024239280774546" y1="39.35833333333333" y2="39.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.84166666666667" x2="19.84166666666667" y1="37.35833333333333" y2="39.35833333333333"/>
   <path d="M 4.75833 25.9572 A 2.96392 1.81747 180 0 1 4.75833 22.3223" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 4.75833 29.5921 A 2.96392 1.81747 180 0 1 4.75833 25.9572" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 4.75833 33.1271 A 2.96392 1.81747 180 0 1 4.75833 29.4921" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="30.85833333333333" y2="30.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="31.85833333333333" y2="31.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="12" y1="4.5" y2="7.166666666666668"/>
   <path d="M 7.26667 12.1 A 4.91667 4.75 -450 1 0 12.0167 7.18333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 7.33333 12.0833 L 12 12.1667 L 12 20.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510778" x2="17.14262023217246" y1="27.75922956383932" y2="26.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="29.36667381975841" y2="30.33114037330986"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.50700202690252" x2="18.32283029297954" y1="31.0857461490052" y2="31.0857461490052"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.28450340888154" x2="18.47116270499357" y1="30.7156109584975" y2="30.7156109584975"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.98783858485353" x2="18.76782752902158" y1="30.34547576798984" y2="30.34547576798984"/>
   <rect fill-opacity="0" height="4.29" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,17.83,27.22) scale(1,1) translate(0,0)" width="2.33" x="16.67" y="25.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="24.56666666666668" y2="25.08015580397416"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510781" x2="18.53565505804314" y1="27.75922956383932" y2="26.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="25.08015580397418" y2="27.73243882624067"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV护国变" InitShowingPlane="" fill="rgb(0,0,0)" height="1045" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="395" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,170.653,50.7343) scale(1,1) translate(-1.32242e-14,0)" writing-mode="lr" x="170.65" xml:space="preserve" y="55.23" zvalue="10248"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="1" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,188.625,47.7466) scale(1,1) translate(0,2.85747e-15)" writing-mode="lr" x="188.63" xml:space="preserve" y="55.25" zvalue="10249"> 35kV护国变</text>
  <image height="82.56999999999999" id="2" preserveAspectRatio="xMidYMid slice" width="249.69" x="72.45" xlink:href="logo.png" y="13.36"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="346" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,197.296,54.6435) scale(1,1) translate(-1.6087e-14,0)" writing-mode="lr" x="197.3" xml:space="preserve" y="58.14" zvalue="10297"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,226.143,54.6203) scale(1,1) translate(0,0)" writing-mode="lr" x="226.14" xml:space="preserve" y="63.62" zvalue="10298">35kV护国变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="214" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,288.438,398.75) scale(1,1) translate(0,-2.57627e-13)" width="72.88" x="252" y="386.75" zvalue="10342"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,288.438,398.75) scale(1,1) translate(0,-2.57627e-13)" writing-mode="lr" x="288.44" xml:space="preserve" y="403.25" zvalue="10342">小电流接地</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="212" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,84.625,317.75) scale(1,1) translate(0,0)" width="72.88" x="48.19" y="305.75" zvalue="10343"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84.625,317.75) scale(1,1) translate(0,0)" writing-mode="lr" x="84.63" xml:space="preserve" y="322.25" zvalue="10343">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="213" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,186.531,398.75) scale(1,1) translate(0,0)" width="72.88" x="150.09" y="386.75" zvalue="10344"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,186.531,398.75) scale(1,1) translate(0,0)" writing-mode="lr" x="186.53" xml:space="preserve" y="403.25" zvalue="10344">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="211" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,84.625,398.75) scale(1,1) translate(0,0)" width="72.88" x="48.19" y="386.75" zvalue="10345"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84.625,398.75) scale(1,1) translate(0,0)" writing-mode="lr" x="84.63" xml:space="preserve" y="403.25" zvalue="10345">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="210" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,84.625,358.25) scale(1,1) translate(0,0)" width="72.88" x="48.19" y="346.25" zvalue="10346"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,84.625,358.25) scale(1,1) translate(0,0)" writing-mode="lr" x="84.63" xml:space="preserve" y="362.75" zvalue="10346">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,557.076,354.591) scale(1,1) translate(0,0)" writing-mode="lr" x="557.08" xml:space="preserve" y="359.09" zvalue="7577">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1094.08,667.035) scale(1,1) translate(0,0)" writing-mode="lr" x="1094.08" xml:space="preserve" y="671.54" zvalue="7716">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,796.983,449.084) scale(1,1) translate(0,0)" writing-mode="lr" x="796.98" xml:space="preserve" y="453.58" zvalue="7803">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="250" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,795.555,403.18) scale(1,1) translate(0,0)" writing-mode="lr" x="795.55" xml:space="preserve" y="407.68" zvalue="7810">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,997.98,423.57) scale(1,1) translate(0,0)" writing-mode="lr" x="997.98" xml:space="preserve" y="428.07" zvalue="8016">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1046.61,436.447) scale(1,1) translate(0,0)" writing-mode="lr" x="1046.61" xml:space="preserve" y="440.95" zvalue="8035">39017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,913.586,109.312) scale(1,1) translate(0,0)" writing-mode="lr" x="913.59" xml:space="preserve" y="113.81" zvalue="8808">35kV清护线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,892.809,292.145) scale(1,1) translate(0,0)" writing-mode="lr" x="892.8099999999999" xml:space="preserve" y="296.64" zvalue="8809">351</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,896.297,237.254) scale(1,1) translate(0,0)" writing-mode="lr" x="896.3" xml:space="preserve" y="241.75" zvalue="8811">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,885.232,205.816) scale(1,1) translate(0,0)" writing-mode="lr" x="885.23" xml:space="preserve" y="210.32" zvalue="8813">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,896.425,343.049) scale(1,1) translate(-1.96049e-13,0)" writing-mode="lr" x="896.42" xml:space="preserve" y="347.55" zvalue="8816">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="441" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,847.106,602.042) scale(1,1) translate(0,0)" writing-mode="lr" x="847.11" xml:space="preserve" y="606.54" zvalue="9223">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1690.17,859.635) scale(1,1) translate(0,0)" writing-mode="lr" x="1690.17" xml:space="preserve" y="864.13" zvalue="9338">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="553" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,688.537,928.125) scale(1,1) translate(0,0)" writing-mode="lr" x="688.54" xml:space="preserve" y="932.63" zvalue="9422">备用</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="243" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1194.11,501.214) scale(1,1) translate(0,0)" writing-mode="lr" x="1194.11" xml:space="preserve" y="505.71" zvalue="9705">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="538" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,872.562,516.625) scale(1,1) translate(0,0)" writing-mode="lr" x="872.5599999999999" xml:space="preserve" y="521.13" zvalue="9714">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="550" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,795.25,646.625) scale(1,1) translate(0,0)" writing-mode="lr" x="795.25" xml:space="preserve" y="651.13" zvalue="9718">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="631" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1376.98,449.084) scale(1,1) translate(0,0)" writing-mode="lr" x="1376.98" xml:space="preserve" y="453.58" zvalue="9723">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="629" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1375.55,403.18) scale(1,1) translate(0,0)" writing-mode="lr" x="1375.55" xml:space="preserve" y="407.68" zvalue="9725">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="628" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1427.11,602.042) scale(1,1) translate(0,0)" writing-mode="lr" x="1427.11" xml:space="preserve" y="606.54" zvalue="9729">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="625" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1375.25,646.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1375.25" xml:space="preserve" y="651.13" zvalue="9735">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="823" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1348.83,529.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1348.83" xml:space="preserve" y="533.87" zvalue="9740">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="854" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,667.667,658.596) scale(1,1) translate(0,0)" writing-mode="lr" x="667.67" xml:space="preserve" y="663.1" zvalue="9762">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="215" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,951.394,826.391) scale(1,1) translate(0,0)" writing-mode="lr" x="951.39" xml:space="preserve" y="830.89" zvalue="9859">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="209" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,906.522,776.868) scale(1,1) translate(0,1.27959e-12)" writing-mode="lr" x="906.52" xml:space="preserve" y="781.37" zvalue="9861">052</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,950.394,734.391) scale(1,1) translate(0,0)" writing-mode="lr" x="950.39" xml:space="preserve" y="738.89" zvalue="9869">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1216.2,827.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1216.2" xml:space="preserve" y="832.47" zvalue="9874">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1169.13,780.368) scale(1,1) translate(0,0)" writing-mode="lr" x="1169.13" xml:space="preserve" y="784.87" zvalue="9876">053</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1196.54,932.125) scale(1,1) translate(3.86037e-13,0)" writing-mode="lr" x="1196.54" xml:space="preserve" y="936.63" zvalue="9880">10kV邦掌线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1216.2,735.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1216.2" xml:space="preserve" y="740.47" zvalue="9884">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1419.4,827.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1419.4" xml:space="preserve" y="832.47" zvalue="9889">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1372.33,781.618) scale(1,1) translate(0,0)" writing-mode="lr" x="1372.33" xml:space="preserve" y="786.12" zvalue="9891">054</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1399.82,932.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1399.82" xml:space="preserve" y="936.63" zvalue="9895">10kV护国线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1419.22,735.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1419.22" xml:space="preserve" y="740.47" zvalue="9899">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1645.91,807.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1645.91" xml:space="preserve" y="812.47" zvalue="9904">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1598.84,767.951) scale(1,1) translate(0,0)" writing-mode="lr" x="1598.84" xml:space="preserve" y="772.45" zvalue="9906">055</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1644.2,727.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1644.2" xml:space="preserve" y="732.47" zvalue="9914">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1156.21,404.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1156.21" xml:space="preserve" y="409" zvalue="9923">3811</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,945.575,159.8) scale(1,1) translate(0,0)" writing-mode="lr" x="945.5700000000001" xml:space="preserve" y="164.3" zvalue="9928">9</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1300.03,109.312) scale(1,1) translate(0,0)" writing-mode="lr" x="1300.03" xml:space="preserve" y="113.81" zvalue="9935">35kV别护线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1279.25,292.145) scale(1,1) translate(0,0)" writing-mode="lr" x="1279.25" xml:space="preserve" y="296.64" zvalue="9936">352</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1282.74,237.254) scale(1,1) translate(0,0)" writing-mode="lr" x="1282.74" xml:space="preserve" y="241.75" zvalue="9938">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1271.68,205.816) scale(1,1) translate(0,0)" writing-mode="lr" x="1271.68" xml:space="preserve" y="210.32" zvalue="9940">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1282.87,343.049) scale(1,1) translate(0,0)" writing-mode="lr" x="1282.87" xml:space="preserve" y="347.55" zvalue="9943">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1332.02,159.8) scale(1,1) translate(0,0)" writing-mode="lr" x="1332.02" xml:space="preserve" y="164.3" zvalue="9951">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1427.37,410.217) scale(1,1) translate(0,0)" writing-mode="lr" x="1427.37" xml:space="preserve" y="414.72" zvalue="9957">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,665.722,611.057) scale(1,1) translate(0,0)" writing-mode="lr" x="665.72" xml:space="preserve" y="615.5599999999999" zvalue="9960">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,734.374,620.985) scale(1,1) translate(0,0)" writing-mode="lr" x="734.37" xml:space="preserve" y="625.48" zvalue="9966">09017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1019.08,538.707) scale(1,1) translate(0,0)" writing-mode="lr" x="1019.08" xml:space="preserve" y="543.21" zvalue="9972">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,684.056,531.125) scale(1,1) translate(0,0)" writing-mode="lr" x="684.0599999999999" xml:space="preserve" y="535.63" zvalue="9974">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1630.12,940.423) scale(1,1) translate(0,0)" writing-mode="lr" x="1630.13" xml:space="preserve" y="944.92" zvalue="9978">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="216" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,993.914,865.497) scale(1,1) translate(0,0)" writing-mode="lr" x="993.91" xml:space="preserve" y="870" zvalue="10066">67</text>
  
  <line fill="none" id="393" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="387.5000000000002" x2="387.5000000000002" y1="25.35714285714283" y2="1015.357142857143" zvalue="10250"/>
  <line fill="none" id="392" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.90549597855136" x2="325.5167560321705" y1="159.1279458827686" y2="159.1279458827686" zvalue="10251"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.05065791393122" x2="97.54115791393122" y1="911.3960778229952" y2="911.3960778229952"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.05065791393122" x2="97.54115791393122" y1="969.3262778229951" y2="969.3262778229951"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.05065791393122" x2="14.05065791393122" y1="911.3960778229952" y2="969.3262778229951"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.54115791393122" x2="97.54115791393122" y1="911.3960778229952" y2="969.3262778229951"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.54175791393118" x2="371.6677579139312" y1="911.3960778229952" y2="911.3960778229952"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.54175791393118" x2="371.6677579139312" y1="969.3262778229951" y2="969.3262778229951"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.54175791393118" x2="97.54175791393118" y1="911.3960778229952" y2="969.3262778229951"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="371.6677579139312" x2="371.6677579139312" y1="911.3960778229952" y2="969.3262778229951"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.05065791393122" x2="97.54115791393122" y1="969.3262878229951" y2="969.3262878229951"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.05065791393122" x2="97.54115791393122" y1="1000.331187822995" y2="1000.331187822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.05065791393122" x2="14.05065791393122" y1="969.3262878229951" y2="1000.331187822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.54115791393122" x2="97.54115791393122" y1="969.3262878229951" y2="1000.331187822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.54175791393118" x2="180.5592579139312" y1="969.3262878229951" y2="969.3262878229951"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.54175791393118" x2="180.5592579139312" y1="1000.331187822995" y2="1000.331187822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.54175791393118" x2="97.54175791393118" y1="969.3262878229951" y2="1000.331187822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.5592579139312" x2="180.5592579139312" y1="969.3262878229951" y2="1000.331187822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.5592579139312" x2="276.1129579139313" y1="969.3262878229951" y2="969.3262878229951"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.5592579139312" x2="276.1129579139313" y1="1000.331187822995" y2="1000.331187822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.5592579139312" x2="180.5592579139312" y1="969.3262878229951" y2="1000.331187822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.1129579139313" x2="276.1129579139313" y1="969.3262878229951" y2="1000.331187822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.1128579139312" x2="371.6665579139312" y1="969.3262878229951" y2="969.3262878229951"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.1128579139312" x2="371.6665579139312" y1="1000.331187822995" y2="1000.331187822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.1128579139312" x2="276.1128579139312" y1="969.3262878229951" y2="1000.331187822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="371.6665579139312" x2="371.6665579139312" y1="969.3262878229951" y2="1000.331187822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.05065791393122" x2="97.54115791393122" y1="1000.331077822995" y2="1000.331077822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.05065791393122" x2="97.54115791393122" y1="1031.335977822995" y2="1031.335977822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.05065791393122" x2="14.05065791393122" y1="1000.331077822995" y2="1031.335977822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.54115791393122" x2="97.54115791393122" y1="1000.331077822995" y2="1031.335977822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.54175791393118" x2="180.5592579139312" y1="1000.331077822995" y2="1000.331077822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.54175791393118" x2="180.5592579139312" y1="1031.335977822995" y2="1031.335977822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.54175791393118" x2="97.54175791393118" y1="1000.331077822995" y2="1031.335977822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.5592579139312" x2="180.5592579139312" y1="1000.331077822995" y2="1031.335977822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.5592579139312" x2="276.1129579139313" y1="1000.331077822995" y2="1000.331077822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.5592579139312" x2="276.1129579139313" y1="1031.335977822995" y2="1031.335977822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.5592579139312" x2="180.5592579139312" y1="1000.331077822995" y2="1031.335977822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.1129579139313" x2="276.1129579139313" y1="1000.331077822995" y2="1031.335977822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.1128579139312" x2="371.6665579139312" y1="1000.331077822995" y2="1000.331077822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.1128579139312" x2="371.6665579139312" y1="1031.335977822995" y2="1031.335977822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.1128579139312" x2="276.1128579139312" y1="1000.331077822995" y2="1031.335977822995"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="371.6665579139312" x2="371.6665579139312" y1="1000.331077822995" y2="1031.335977822995"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="390" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,49.6546,944.766) scale(1,1) translate(2.25477e-15,1.03534e-13)" writing-mode="lr" x="20.31" xml:space="preserve" y="949.27" zvalue="10253">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="389" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,49.8386,986.932) scale(1,1) translate(0,1.08215e-13)" writing-mode="lr" x="31.68" xml:space="preserve" y="991.4299999999999" zvalue="10254">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="388" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,272.189,988.06) scale(1,1) translate(1.27829e-13,1.0834e-13)" writing-mode="lr" x="191.9" xml:space="preserve" y="992.5599999999999" zvalue="10255">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="387" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.5578,1020.28) scale(1,1) translate(-1.52098e-14,1.11918e-13)" writing-mode="lr" x="47.56" xml:space="preserve" y="1024.78" zvalue="10256">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="386" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,278.883,1020.03) scale(1,1) translate(-8.39768e-14,1.1189e-13)" writing-mode="lr" x="189.1" xml:space="preserve" y="1024.53" zvalue="10257">更新日期          20200902</text>
  <line fill="none" id="385" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.28468498659515" x2="324.8959450402143" y1="609.8945306693695" y2="609.8945306693695" zvalue="10258"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="383" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,91.3493,630.936) scale(1,1) translate(4.596e-15,-1.35944e-13)" writing-mode="lr" x="91.34927949061671" xml:space="preserve" y="635.4363811688672" zvalue="10260">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.00000000000023" x2="191.0000000000002" y1="162.3571428571428" y2="162.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.00000000000023" x2="191.0000000000002" y1="188.3571428571428" y2="188.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.00000000000023" x2="10.00000000000023" y1="162.3571428571428" y2="188.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.0000000000002" x2="191.0000000000002" y1="162.3571428571428" y2="188.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.0000000000002" x2="372.0000000000002" y1="162.3571428571428" y2="162.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.0000000000002" x2="372.0000000000002" y1="188.3571428571428" y2="188.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.0000000000002" x2="191.0000000000002" y1="162.3571428571428" y2="188.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.0000000000002" x2="372.0000000000002" y1="162.3571428571428" y2="188.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.00000000000023" x2="191.0000000000002" y1="188.3571428571428" y2="188.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.00000000000023" x2="191.0000000000002" y1="212.6071428571428" y2="212.6071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.00000000000023" x2="10.00000000000023" y1="188.3571428571428" y2="212.6071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.0000000000002" x2="191.0000000000002" y1="188.3571428571428" y2="212.6071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.0000000000002" x2="372.0000000000002" y1="188.3571428571428" y2="188.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.0000000000002" x2="372.0000000000002" y1="212.6071428571428" y2="212.6071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.0000000000002" x2="191.0000000000002" y1="188.3571428571428" y2="212.6071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.0000000000002" x2="372.0000000000002" y1="188.3571428571428" y2="212.6071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.00000000000023" x2="191.0000000000002" y1="212.6071428571428" y2="212.6071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.00000000000023" x2="191.0000000000002" y1="235.3571428571428" y2="235.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.00000000000023" x2="10.00000000000023" y1="212.6071428571428" y2="235.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.0000000000002" x2="191.0000000000002" y1="212.6071428571428" y2="235.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.0000000000002" x2="372.0000000000002" y1="212.6071428571428" y2="212.6071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.0000000000002" x2="372.0000000000002" y1="235.3571428571428" y2="235.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.0000000000002" x2="191.0000000000002" y1="212.6071428571428" y2="235.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.0000000000002" x2="372.0000000000002" y1="212.6071428571428" y2="235.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.00000000000023" x2="191.0000000000002" y1="235.3571428571428" y2="235.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.00000000000023" x2="191.0000000000002" y1="258.1071428571428" y2="258.1071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.00000000000023" x2="10.00000000000023" y1="235.3571428571428" y2="258.1071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.0000000000002" x2="191.0000000000002" y1="235.3571428571428" y2="258.1071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.0000000000002" x2="372.0000000000002" y1="235.3571428571428" y2="235.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.0000000000002" x2="372.0000000000002" y1="258.1071428571428" y2="258.1071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.0000000000002" x2="191.0000000000002" y1="235.3571428571428" y2="258.1071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.0000000000002" x2="372.0000000000002" y1="235.3571428571428" y2="258.1071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.00000000000023" x2="191.0000000000002" y1="258.1071428571428" y2="258.1071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.00000000000023" x2="191.0000000000002" y1="280.8571428571428" y2="280.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.00000000000023" x2="10.00000000000023" y1="258.1071428571428" y2="280.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.0000000000002" x2="191.0000000000002" y1="258.1071428571428" y2="280.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.0000000000002" x2="372.0000000000002" y1="258.1071428571428" y2="258.1071428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.0000000000002" x2="372.0000000000002" y1="280.8571428571428" y2="280.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.0000000000002" x2="191.0000000000002" y1="258.1071428571428" y2="280.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.0000000000002" x2="372.0000000000002" y1="258.1071428571428" y2="280.8571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.00000000000023" x2="106.7745000000002" y1="442.3571428571428" y2="442.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.00000000000023" x2="106.7745000000002" y1="480.6394428571429" y2="480.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.00000000000023" x2="61.00000000000023" y1="442.3571428571428" y2="480.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="106.7745000000002" y1="442.3571428571428" y2="480.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="165.5809000000003" y1="442.3571428571428" y2="442.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="165.5809000000003" y1="480.6394428571429" y2="480.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="106.7745000000002" y1="442.3571428571428" y2="480.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="165.5809000000003" y1="442.3571428571428" y2="480.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="224.3873000000002" y1="442.3571428571428" y2="442.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="224.3873000000002" y1="480.6394428571429" y2="480.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="165.5809000000003" y1="442.3571428571428" y2="480.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3873000000002" x2="224.3873000000002" y1="442.3571428571428" y2="480.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3872000000002" x2="283.1936000000003" y1="442.3571428571428" y2="442.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3872000000002" x2="283.1936000000003" y1="480.6394428571429" y2="480.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3872000000002" x2="224.3872000000002" y1="442.3571428571428" y2="480.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="283.1936000000003" y1="442.3571428571428" y2="480.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="342.0000000000002" y1="442.3571428571428" y2="442.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="342.0000000000002" y1="480.6394428571429" y2="480.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="283.1936000000003" y1="442.3571428571428" y2="480.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="342.0000000000002" x2="342.0000000000002" y1="442.3571428571428" y2="480.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.00000000000023" x2="106.7745000000002" y1="480.6394428571429" y2="480.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.00000000000023" x2="106.7745000000002" y1="505.3188428571428" y2="505.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.00000000000023" x2="61.00000000000023" y1="480.6394428571429" y2="505.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="106.7745000000002" y1="480.6394428571429" y2="505.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="165.5809000000003" y1="480.6394428571429" y2="480.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="165.5809000000003" y1="505.3188428571428" y2="505.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="106.7745000000002" y1="480.6394428571429" y2="505.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="165.5809000000003" y1="480.6394428571429" y2="505.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="224.3873000000002" y1="480.6394428571429" y2="480.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="224.3873000000002" y1="505.3188428571428" y2="505.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="165.5809000000003" y1="480.6394428571429" y2="505.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3873000000002" x2="224.3873000000002" y1="480.6394428571429" y2="505.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3872000000002" x2="283.1936000000003" y1="480.6394428571429" y2="480.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3872000000002" x2="283.1936000000003" y1="505.3188428571428" y2="505.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3872000000002" x2="224.3872000000002" y1="480.6394428571429" y2="505.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="283.1936000000003" y1="480.6394428571429" y2="505.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="342.0000000000002" y1="480.6394428571429" y2="480.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="342.0000000000002" y1="505.3188428571428" y2="505.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="283.1936000000003" y1="480.6394428571429" y2="505.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="342.0000000000002" x2="342.0000000000002" y1="480.6394428571429" y2="505.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.00000000000023" x2="106.7745000000002" y1="505.3188428571428" y2="505.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.00000000000023" x2="106.7745000000002" y1="529.9982428571428" y2="529.9982428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.00000000000023" x2="61.00000000000023" y1="505.3188428571428" y2="529.9982428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="106.7745000000002" y1="505.3188428571428" y2="529.9982428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="165.5809000000003" y1="505.3188428571428" y2="505.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="165.5809000000003" y1="529.9982428571428" y2="529.9982428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="106.7745000000002" y1="505.3188428571428" y2="529.9982428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="165.5809000000003" y1="505.3188428571428" y2="529.9982428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="224.3873000000002" y1="505.3188428571428" y2="505.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="224.3873000000002" y1="529.9982428571428" y2="529.9982428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="165.5809000000003" y1="505.3188428571428" y2="529.9982428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3873000000002" x2="224.3873000000002" y1="505.3188428571428" y2="529.9982428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3872000000002" x2="283.1936000000003" y1="505.3188428571428" y2="505.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3872000000002" x2="283.1936000000003" y1="529.9982428571428" y2="529.9982428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3872000000002" x2="224.3872000000002" y1="505.3188428571428" y2="529.9982428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="283.1936000000003" y1="505.3188428571428" y2="529.9982428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="342.0000000000002" y1="505.3188428571428" y2="505.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="342.0000000000002" y1="529.9982428571428" y2="529.9982428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="283.1936000000003" y1="505.3188428571428" y2="529.9982428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="342.0000000000002" x2="342.0000000000002" y1="505.3188428571428" y2="529.9982428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.00000000000023" x2="106.7745000000002" y1="529.9982428571428" y2="529.9982428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.00000000000023" x2="106.7745000000002" y1="554.6776428571428" y2="554.6776428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.00000000000023" x2="61.00000000000023" y1="529.9982428571428" y2="554.6776428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="106.7745000000002" y1="529.9982428571428" y2="554.6776428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="165.5809000000003" y1="529.9982428571428" y2="529.9982428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="165.5809000000003" y1="554.6776428571428" y2="554.6776428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="106.7745000000002" y1="529.9982428571428" y2="554.6776428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="165.5809000000003" y1="529.9982428571428" y2="554.6776428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="224.3873000000002" y1="529.9982428571428" y2="529.9982428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="224.3873000000002" y1="554.6776428571428" y2="554.6776428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="165.5809000000003" y1="529.9982428571428" y2="554.6776428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3873000000002" x2="224.3873000000002" y1="529.9982428571428" y2="554.6776428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3872000000002" x2="283.1936000000003" y1="529.9982428571428" y2="529.9982428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3872000000002" x2="283.1936000000003" y1="554.6776428571428" y2="554.6776428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3872000000002" x2="224.3872000000002" y1="529.9982428571428" y2="554.6776428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="283.1936000000003" y1="529.9982428571428" y2="554.6776428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="342.0000000000002" y1="529.9982428571428" y2="529.9982428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="342.0000000000002" y1="554.6776428571428" y2="554.6776428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="283.1936000000003" y1="529.9982428571428" y2="554.6776428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="342.0000000000002" x2="342.0000000000002" y1="529.9982428571428" y2="554.6776428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.00000000000023" x2="106.7745000000002" y1="554.6777428571428" y2="554.6777428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.00000000000023" x2="106.7745000000002" y1="579.3571428571428" y2="579.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.00000000000023" x2="61.00000000000023" y1="554.6777428571428" y2="579.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="106.7745000000002" y1="554.6777428571428" y2="579.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="165.5809000000003" y1="554.6777428571428" y2="554.6777428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="165.5809000000003" y1="579.3571428571428" y2="579.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="106.7745000000002" y1="554.6777428571428" y2="579.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="165.5809000000003" y1="554.6777428571428" y2="579.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="224.3873000000002" y1="554.6777428571428" y2="554.6777428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="224.3873000000002" y1="579.3571428571428" y2="579.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="165.5809000000003" y1="554.6777428571428" y2="579.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3873000000002" x2="224.3873000000002" y1="554.6777428571428" y2="579.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3872000000002" x2="283.1936000000003" y1="554.6777428571428" y2="554.6777428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3872000000002" x2="283.1936000000003" y1="579.3571428571428" y2="579.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3872000000002" x2="224.3872000000002" y1="554.6777428571428" y2="579.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="283.1936000000003" y1="554.6777428571428" y2="579.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="342.0000000000002" y1="554.6777428571428" y2="554.6777428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="342.0000000000002" y1="579.3571428571428" y2="579.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="283.1936000000003" y1="554.6777428571428" y2="579.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="342.0000000000002" x2="342.0000000000002" y1="554.6777428571428" y2="579.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.00000000000023" x2="106.7745000000002" y1="579.3571428571428" y2="579.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.00000000000023" x2="106.7745000000002" y1="604.0365428571429" y2="604.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="61.00000000000023" x2="61.00000000000023" y1="579.3571428571428" y2="604.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="106.7745000000002" y1="579.3571428571428" y2="604.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="165.5809000000003" y1="579.3571428571428" y2="579.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="165.5809000000003" y1="604.0365428571429" y2="604.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="106.7745000000002" x2="106.7745000000002" y1="579.3571428571428" y2="604.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="165.5809000000003" y1="579.3571428571428" y2="604.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="224.3873000000002" y1="579.3571428571428" y2="579.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="224.3873000000002" y1="604.0365428571429" y2="604.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="165.5809000000003" x2="165.5809000000003" y1="579.3571428571428" y2="604.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3873000000002" x2="224.3873000000002" y1="579.3571428571428" y2="604.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3872000000002" x2="283.1936000000003" y1="579.3571428571428" y2="579.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3872000000002" x2="283.1936000000003" y1="604.0365428571429" y2="604.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="224.3872000000002" x2="224.3872000000002" y1="579.3571428571428" y2="604.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="283.1936000000003" y1="579.3571428571428" y2="604.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="342.0000000000002" y1="579.3571428571428" y2="579.3571428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="342.0000000000002" y1="604.0365428571429" y2="604.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="283.1936000000003" x2="283.1936000000003" y1="579.3571428571428" y2="604.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="342.0000000000002" x2="342.0000000000002" y1="579.3571428571428" y2="604.0365428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="379" stroke="rgb(255,255,255)" text-anchor="middle" x="138.015625" xml:space="preserve" y="457.359375" zvalue="10264">35kV     母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="379" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="138.015625" xml:space="preserve" y="473.359375" zvalue="10264">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="378" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,207.399,317.199) scale(1,1) translate(0,0)" writing-mode="lr" x="207.4" xml:space="preserve" y="321.7" zvalue="10265">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="377" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,312.399,317.199) scale(1,1) translate(0,0)" writing-mode="lr" x="312.4" xml:space="preserve" y="321.7" zvalue="10266">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="376" stroke="rgb(255,255,255)" text-anchor="middle" x="255.34375" xml:space="preserve" y="457.359375" zvalue="10267">10kV     母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="376" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="255.34375" xml:space="preserve" y="473.359375" zvalue="10267">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="375" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86,492.857) scale(1,1) translate(0,-1.06217e-13)" writing-mode="lr" x="86.00000000000023" xml:space="preserve" y="497.3571428571428" zvalue="10268">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="374" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86,518.357) scale(1,1) translate(0,0)" writing-mode="lr" x="86.00000000000023" xml:space="preserve" y="522.8571428571429" zvalue="10269">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="373" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86,543.857) scale(1,1) translate(0,5.87704e-14)" writing-mode="lr" x="86.00000000000023" xml:space="preserve" y="548.3571428571428" zvalue="10270">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="372" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86,569.357) scale(1,1) translate(0,0)" writing-mode="lr" x="86.00000000000023" xml:space="preserve" y="573.8571428571429" zvalue="10271">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="371" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86,594.857) scale(1,1) translate(0,0)" writing-mode="lr" x="86.00000000000023" xml:space="preserve" y="599.3571428571429" zvalue="10272">Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="370" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48,176.357) scale(1,1) translate(0,0)" writing-mode="lr" x="48" xml:space="preserve" y="180.86" zvalue="10273">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="369" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228,176.357) scale(1,1) translate(0,0)" writing-mode="lr" x="228" xml:space="preserve" y="180.86" zvalue="10274">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="368" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.6875,200.607) scale(1,1) translate(0,0)" writing-mode="lr" x="51.69" xml:space="preserve" y="205.11" zvalue="10275">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="367" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.1875,248.357) scale(1,1) translate(0,0)" writing-mode="lr" x="55.19" xml:space="preserve" y="252.86" zvalue="10276">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="366" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.1875,271.357) scale(1,1) translate(0,3.47532e-13)" writing-mode="lr" x="55.19" xml:space="preserve" y="275.86" zvalue="10277">2号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="365" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.6875,224.607) scale(1,1) translate(0,0)" writing-mode="lr" x="52.69" xml:space="preserve" y="229.11" zvalue="10278">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="217" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,230.155,945.766) scale(1,1) translate(0,1.03645e-13)" writing-mode="lr" x="178.31" xml:space="preserve" y="950.27" zvalue="10348">HuGuo-01-2015</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="218" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,138.839,986.932) scale(1,1) translate(0,1.08215e-13)" writing-mode="lr" x="115.68" xml:space="preserve" y="991.4299999999999" zvalue="10350">李文杰</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="220" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,926.37,932.125) scale(1,1) translate(0,0)" writing-mode="lr" x="926.37" xml:space="preserve" y="936.63" zvalue="10352">10kV南护线(电站专线)</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,877.111,538.083) scale(1,1) translate(0,0)" writing-mode="lr" x="877.11" xml:space="preserve" y="542.58" zvalue="10365">1000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1350.11,549.083) scale(1,1) translate(0,1.20034e-13)" writing-mode="lr" x="1350.11" xml:space="preserve" y="553.58" zvalue="10367">2500kVA</text>
 </g>
 <g id="ButtonClass">
  <g href="小电流装置20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="252" y="386.75" zvalue="10342"/></g>
  <g href="全站公用20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="48.19" y="305.75" zvalue="10343"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="150.09" y="386.75" zvalue="10344"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="48.19" y="386.75" zvalue="10345"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="48.19" y="346.25" zvalue="10346"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="48">
   <path class="kv35" d="M 537.67 370.59 L 1727.14 370.59" stroke-width="6" zvalue="7576"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674410233859" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674410233859"/></metadata>
  <path d="M 537.67 370.59 L 1727.14 370.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv10" d="M 537.67 690.59 L 1736 690.59" stroke-width="6" zvalue="7715"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674410299395" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674410299395"/></metadata>
  <path d="M 537.67 690.59 L 1736 690.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="276">
   <use class="kv35" height="20" transform="rotate(180,818.588,448.709) scale(1.5542,1.35421) translate(-289.123,-113.823)" width="10" x="810.8167107229006" xlink:href="#Breaker:开关_0" y="435.1666962122334" zvalue="7801"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925156143107" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925156143107"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,818.588,448.709) scale(1.5542,1.35421) translate(-289.123,-113.823)" width="10" x="810.8167107229006" y="435.1666962122334"/></g>
  <g id="69">
   <use class="kv35" height="20" transform="rotate(0,914.205,292.52) scale(1.5542,1.35421) translate(-323.218,-72.9701)" width="10" x="906.4340345345652" xlink:href="#Breaker:开关_0" y="278.9779758524579" zvalue="8807"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925156208643" ObjectName="35kV清护线351断路器"/>
   <cge:TPSR_Ref TObjectID="6473925156208643"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,914.205,292.52) scale(1.5542,1.35421) translate(-323.218,-72.9701)" width="10" x="906.4340345345652" y="278.9779758524579"/></g>
  <g id="461">
   <use class="kv10" height="20" transform="rotate(180,818.768,598.042) scale(1.5542,1.35421) translate(-289.187,-152.883)" width="10" x="810.9973442678217" xlink:href="#Breaker:开关_0" y="584.5" zvalue="9222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925156274179" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925156274179"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,818.768,598.042) scale(1.5542,1.35421) translate(-289.187,-152.883)" width="10" x="810.9973442678217" y="584.5"/></g>
  <g id="546">
   <use class="kv10" height="20" transform="rotate(180,687.293,778.868) scale(1.5542,1.35421) translate(-242.305,-200.18)" width="10" x="679.5216911906348" xlink:href="#Breaker:开关_0" y="765.3258941321849" zvalue="9393"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925156339715" ObjectName="10kV备用Ⅰ线051断路器"/>
   <cge:TPSR_Ref TObjectID="6473925156339715"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,687.293,778.868) scale(1.5542,1.35421) translate(-242.305,-200.18)" width="10" x="679.5216911906348" y="765.3258941321849"/></g>
  <g id="816">
   <use class="kv35" height="20" transform="rotate(180,1398.59,448.709) scale(1.5542,1.35421) translate(-495.94,-113.823)" width="10" x="1390.816710722901" xlink:href="#Breaker:开关_0" y="435.1666962122334" zvalue="9722"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925156470787" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925156470787"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1398.59,448.709) scale(1.5542,1.35421) translate(-495.94,-113.823)" width="10" x="1390.816710722901" y="435.1666962122334"/></g>
  <g id="811">
   <use class="kv10" height="20" transform="rotate(180,1398.77,598.042) scale(1.5542,1.35421) translate(-496.004,-152.883)" width="10" x="1390.997344267822" xlink:href="#Breaker:开关_0" y="584.5" zvalue="9728"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925156405251" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473925156405251"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1398.77,598.042) scale(1.5542,1.35421) translate(-496.004,-152.883)" width="10" x="1390.997344267822" y="584.5"/></g>
  <g id="115">
   <use class="kv10" height="20" transform="rotate(180,927.293,777.868) scale(1.5542,1.35421) translate(-327.885,-199.919)" width="10" x="919.5216911906346" xlink:href="#Breaker:开关_0" y="764.3258941321849" zvalue="9860"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925156536323" ObjectName="10kV南护线052断路器"/>
   <cge:TPSR_Ref TObjectID="6473925156536323"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,927.293,777.868) scale(1.5542,1.35421) translate(-327.885,-199.919)" width="10" x="919.5216911906346" y="764.3258941321849"/></g>
  <g id="132">
   <use class="kv10" height="20" transform="rotate(180,1195.29,777.618) scale(1.5542,1.35421) translate(-423.449,-199.853)" width="10" x="1187.521691190635" xlink:href="#Breaker:开关_0" y="764.0758941321849" zvalue="9875"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925156601859" ObjectName="10kV邦掌线053断路器"/>
   <cge:TPSR_Ref TObjectID="6473925156601859"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1195.29,777.618) scale(1.5542,1.35421) translate(-423.449,-199.853)" width="10" x="1187.521691190635" y="764.0758941321849"/></g>
  <g id="158">
   <use class="kv10" height="20" transform="rotate(180,1398.49,778.868) scale(1.5542,1.35421) translate(-495.907,-200.18)" width="10" x="1390.723924612378" xlink:href="#Breaker:开关_0" y="765.3258941321849" zvalue="9890"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925156667395" ObjectName="10kV护国线054断路器"/>
   <cge:TPSR_Ref TObjectID="6473925156667395"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1398.49,778.868) scale(1.5542,1.35421) translate(-495.907,-200.18)" width="10" x="1390.723924612378" y="765.3258941321849"/></g>
  <g id="191">
   <use class="kv10" height="20" transform="rotate(180,1625,765.201) scale(1.5542,1.35421) translate(-576.675,-196.606)" width="10" x="1617.229003203369" xlink:href="#Breaker:开关_0" y="751.6592274655184" zvalue="9905"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925156732931" ObjectName="10kV1号电容器055断路器"/>
   <cge:TPSR_Ref TObjectID="6473925156732931"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1625,765.201) scale(1.5542,1.35421) translate(-576.675,-196.606)" width="10" x="1617.229003203369" y="751.6592274655184"/></g>
  <g id="49">
   <use class="kv35" height="20" transform="rotate(0,1300.65,292.52) scale(1.5542,1.35421) translate(-461.017,-72.9701)" width="10" x="1292.87847897901" xlink:href="#Breaker:开关_0" y="278.9779758524579" zvalue="9934"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925156798467" ObjectName="35kV别护线352断路器"/>
   <cge:TPSR_Ref TObjectID="6473925156798467"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1300.65,292.52) scale(1.5542,1.35421) translate(-461.017,-72.9701)" width="10" x="1292.87847897901" y="278.9779758524579"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="272">
   <use class="kv35" height="30" transform="rotate(0,818.588,402.872) scale(0.947693,-0.6712) translate(44.7889,-1008.03)" width="15" x="811.4800105358408" xlink:href="#Disconnector:刀闸_0" y="392.8043387992517" zvalue="7809"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454315474947" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454315474947"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,818.588,402.872) scale(0.947693,-0.6712) translate(44.7889,-1008.03)" width="15" x="811.4800105358408" y="392.8043387992517"/></g>
  <g id="1453">
   <use class="kv35" height="30" transform="rotate(180,1018.01,424.263) scale(0.947693,-0.6712) translate(55.7959,-1061.29)" width="15" x="1010.902401042228" xlink:href="#Disconnector:刀闸_0" y="414.1951361579611" zvalue="8015"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454316326915" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454316326915"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1018.01,424.263) scale(0.947693,-0.6712) translate(55.7959,-1061.29)" width="15" x="1010.902401042228" y="414.1951361579611"/></g>
  <g id="68">
   <use class="kv35" height="30" transform="rotate(0,914.211,235.088) scale(-0.947693,0.6712) translate(-1879.27,110.23)" width="15" x="907.1034928921569" xlink:href="#Disconnector:刀闸_0" y="225.0197927208583" zvalue="8810"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454315737091" ObjectName="35kV清护线3516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454315737091"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,914.211,235.088) scale(-0.947693,0.6712) translate(-1879.27,110.23)" width="15" x="907.1034928921569" y="225.0197927208583"/></g>
  <g id="65">
   <use class="kv35" height="30" transform="rotate(180,914.392,342.357) scale(0.947693,-0.6712) translate(50.0768,-857.355)" width="15" x="907.2841334259956" xlink:href="#Disconnector:刀闸_0" y="332.2885305077181" zvalue="8815"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454315540483" ObjectName="35kV清护线3511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454315540483"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,914.392,342.357) scale(0.947693,-0.6712) translate(50.0768,-857.355)" width="15" x="907.2841334259956" y="332.2885305077181"/></g>
  <g id="549">
   <use class="kv10" height="30" transform="rotate(180,687.287,827.391) scale(-0.947693,0.6712) translate(-1412.9,400.38)" width="15" x="680.1788326557261" xlink:href="#Disconnector:刀闸_0" y="817.3230764122643" zvalue="9387"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454315999235" ObjectName="10kV备用Ⅰ线0516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454315999235"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,687.287,827.391) scale(-0.947693,0.6712) translate(-1412.9,400.38)" width="15" x="680.1788326557261" y="817.3230764122643"/></g>
  <g id="551">
   <use class="kv10" height="30" transform="rotate(0,818.283,646.318) scale(0.947693,-0.6712) translate(44.7721,-1614.18)" width="15" x="811.1752049457245" xlink:href="#Disconnector:刀闸_0" y="636.25" zvalue="9717"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454316457987" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454316457987"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,818.283,646.318) scale(0.947693,-0.6712) translate(44.7721,-1614.18)" width="15" x="811.1752049457245" y="636.25"/></g>
  <g id="815">
   <use class="kv35" height="30" transform="rotate(0,1398.59,402.872) scale(0.947693,-0.6712) translate(76.8015,-1008.03)" width="15" x="1391.480010535841" xlink:href="#Disconnector:刀闸_0" y="392.8043387992517" zvalue="9724"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454316589059" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454316589059"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1398.59,402.872) scale(0.947693,-0.6712) translate(76.8015,-1008.03)" width="15" x="1391.480010535841" y="392.8043387992517"/></g>
  <g id="636">
   <use class="kv10" height="30" transform="rotate(0,1398.28,646.318) scale(0.947693,-0.6712) translate(76.7847,-1614.18)" width="15" x="1391.175204945725" xlink:href="#Disconnector:刀闸_0" y="636.25" zvalue="9734"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454316523523" ObjectName="#2主变10kV侧0021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454316523523"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1398.28,646.318) scale(0.947693,-0.6712) translate(76.7847,-1614.18)" width="15" x="1391.175204945725" y="636.25"/></g>
  <g id="864">
   <use class="kv10" height="30" transform="rotate(180,687.697,657.904) scale(0.947693,-0.6712) translate(37.5645,-1643.02)" width="15" x="680.5894229335038" xlink:href="#Disconnector:刀闸_0" y="647.8355178058908" zvalue="9761"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454316654595" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454316654595"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,687.697,657.904) scale(0.947693,-0.6712) translate(37.5645,-1643.02)" width="15" x="680.5894229335038" y="647.8355178058908"/></g>
  <g id="5">
   <use class="kv10" height="30" transform="rotate(180,687.287,735.391) scale(-0.947693,0.6712) translate(-1412.9,355.313)" width="15" x="680.1788224832005" xlink:href="#Disconnector:刀闸_0" y="725.3230764122643" zvalue="9777"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454316720131" ObjectName="10kV备用Ⅰ线0511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454316720131"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,687.287,735.391) scale(-0.947693,0.6712) translate(-1412.9,355.313)" width="15" x="680.1788224832005" y="725.3230764122643"/></g>
  <g id="116">
   <use class="kv10" height="30" transform="rotate(180,927.287,827.391) scale(-0.947693,0.6712) translate(-1906.15,400.38)" width="15" x="920.1788326557261" xlink:href="#Disconnector:刀闸_0" y="817.3230764122643" zvalue="9858"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454316916739" ObjectName="10kV南护线0526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454316916739"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,927.287,827.391) scale(-0.947693,0.6712) translate(-1906.15,400.38)" width="15" x="920.1788326557261" y="817.3230764122643"/></g>
  <g id="109">
   <use class="kv10" height="30" transform="rotate(180,927.287,735.391) scale(-0.947693,0.6712) translate(-1906.15,355.313)" width="15" x="920.1788224832006" xlink:href="#Disconnector:刀闸_0" y="725.3230764122643" zvalue="9868"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454316785667" ObjectName="10kV南护线0521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454316785667"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,927.287,735.391) scale(-0.947693,0.6712) translate(-1906.15,355.313)" width="15" x="920.1788224832006" y="725.3230764122643"/></g>
  <g id="133">
   <use class="kv10" height="30" transform="rotate(180,1195.29,827.391) scale(-0.947693,0.6712) translate(-2456.94,400.38)" width="15" x="1188.178832655726" xlink:href="#Disconnector:刀闸_0" y="817.3230764122643" zvalue="9873"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454317178883" ObjectName="10kV邦掌线0536隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454317178883"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1195.29,827.391) scale(-0.947693,0.6712) translate(-2456.94,400.38)" width="15" x="1188.178832655726" y="817.3230764122643"/></g>
  <g id="125">
   <use class="kv10" height="30" transform="rotate(180,1195.29,735.391) scale(-0.947693,0.6712) translate(-2456.94,355.313)" width="15" x="1188.178822483201" xlink:href="#Disconnector:刀闸_0" y="725.3230764122643" zvalue="9883"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454316982275" ObjectName="10kV邦掌线0531隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454316982275"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1195.29,735.391) scale(-0.947693,0.6712) translate(-2456.94,355.313)" width="15" x="1188.178822483201" y="725.3230764122643"/></g>
  <g id="159">
   <use class="kv10" height="30" transform="rotate(180,1398.49,827.391) scale(-0.947693,0.6712) translate(-2874.56,400.38)" width="15" x="1391.381066077469" xlink:href="#Disconnector:刀闸_0" y="817.3230764122643" zvalue="9888"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454317441027" ObjectName="10kV护国线0546隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454317441027"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1398.49,827.391) scale(-0.947693,0.6712) translate(-2874.56,400.38)" width="15" x="1391.381066077469" y="817.3230764122643"/></g>
  <g id="145">
   <use class="kv10" height="30" transform="rotate(180,1398.31,735.391) scale(-0.947693,0.6712) translate(-2874.19,355.313)" width="15" x="1391.200425543631" xlink:href="#Disconnector:刀闸_0" y="725.3230764122643" zvalue="9898"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454317244419" ObjectName="10kV护国线0541隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454317244419"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1398.31,735.391) scale(-0.947693,0.6712) translate(-2874.19,355.313)" width="15" x="1391.200425543631" y="725.3230764122643"/></g>
  <g id="197">
   <use class="kv10" height="30" transform="rotate(180,1625,807.391) scale(-0.947693,0.6712) translate(-3340.08,390.583)" width="15" x="1617.892303114711" xlink:href="#Disconnector:刀闸_0" y="797.3230764122643" zvalue="9903"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454317572099" ObjectName="10kV1号电容器0556隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454317572099"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1625,807.391) scale(-0.947693,0.6712) translate(-3340.08,390.583)" width="15" x="1617.892303114711" y="797.3230764122643"/></g>
  <g id="168">
   <use class="kv10" height="30" transform="rotate(180,1623.29,727.391) scale(-0.947693,0.6712) translate(-3336.56,351.394)" width="15" x="1616.178822483201" xlink:href="#Disconnector:刀闸_0" y="717.3230764122643" zvalue="9913"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454317506563" ObjectName="10kV1号电容器0551隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454317506563"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1623.29,727.391) scale(-0.947693,0.6712) translate(-3336.56,351.394)" width="15" x="1616.178822483201" y="717.3230764122643"/></g>
  <g id="2">
   <use class="kv35" height="30" transform="rotate(0,1185,405.5) scale(1,1) translate(0,0)" width="15" x="1177.5" xlink:href="#Disconnector:令克_0" y="390.5" zvalue="9922"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454317637635" ObjectName="35kV1号站用变3811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454317637635"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1185,405.5) scale(1,1) translate(0,0)" width="15" x="1177.5" y="390.5"/></g>
  <g id="80">
   <use class="kv35" height="30" transform="rotate(90,946.879,175.082) scale(0.947693,0.6712) translate(51.8698,80.835)" width="15" x="939.7711851022879" xlink:href="#Disconnector:刀闸_0" y="165.013774186895" zvalue="9927"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454317703171" ObjectName="35kV清护线3519隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454317703171"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,946.879,175.082) scale(0.947693,0.6712) translate(51.8698,80.835)" width="15" x="939.7711851022879" y="165.013774186895"/></g>
  <g id="47">
   <use class="kv35" height="30" transform="rotate(0,1300.66,235.088) scale(-0.947693,0.6712) translate(-2673.49,110.23)" width="15" x="1293.547937336601" xlink:href="#Disconnector:刀闸_0" y="225.0197927208583" zvalue="9937"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454318227459" ObjectName="35kV别护线3526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454318227459"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1300.66,235.088) scale(-0.947693,0.6712) translate(-2673.49,110.23)" width="15" x="1293.547937336601" y="225.0197927208583"/></g>
  <g id="44">
   <use class="kv35" height="30" transform="rotate(180,1300.84,342.357) scale(0.947693,-0.6712) translate(71.4062,-857.355)" width="15" x="1293.72857787044" xlink:href="#Disconnector:刀闸_0" y="332.2885305077181" zvalue="9942"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454318030851" ObjectName="35kV别护线3521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454318030851"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1300.84,342.357) scale(0.947693,-0.6712) translate(71.4062,-857.355)" width="15" x="1293.72857787044" y="332.2885305077181"/></g>
  <g id="37">
   <use class="kv35" height="30" transform="rotate(90,1333.32,175.082) scale(0.947693,0.6712) translate(73.1993,80.835)" width="15" x="1326.215629546732" xlink:href="#Disconnector:刀闸_0" y="165.013774186895" zvalue="9950"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454317834243" ObjectName="35kV别护线3529隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454317834243"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1333.32,175.082) scale(0.947693,0.6712) translate(73.1993,80.835)" width="15" x="1326.215629546732" y="165.013774186895"/></g>
  <g id="71">
   <use class="kv10" height="30" transform="rotate(0,687.889,616.057) scale(-1,1) translate(-1375.78,0)" width="15" x="680.3888888888889" xlink:href="#Disconnector:令克_0" y="601.0570481238101" zvalue="9959"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454318489603" ObjectName="10kV母线电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454318489603"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,687.889,616.057) scale(-1,1) translate(-1375.78,0)" width="15" x="680.3888888888889" y="601.0570481238101"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="263">
   <path class="kv35" d="M 818.65 392.98 L 818.65 370.59" stroke-width="1" zvalue="7822"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.65 392.98 L 818.65 370.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv35" d="M 1017.93 414.53 L 1017.93 370.59" stroke-width="1" zvalue="8031"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@0" LinkObjectIDznd="48@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1017.93 414.53 L 1017.93 370.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv35" d="M 1017.95 434.16 L 1017.95 469.54" stroke-width="1" zvalue="8038"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@1" LinkObjectIDznd="94@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1017.95 434.16 L 1017.95 469.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv35" d="M 1035.56 450.51 L 1017.95 450.51" stroke-width="1" zvalue="8039"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 1035.56 450.51 L 1017.95 450.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv35" d="M 914.15 279.56 L 914.15 244.98" stroke-width="1" zvalue="8814"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="68@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 914.15 279.56 L 914.15 244.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv35" d="M 914.13 225.35 L 914.13 137.11" stroke-width="1" zvalue="8817"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="70@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 914.13 225.35 L 914.13 137.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv35" d="M 887.28 155.31 L 914.13 155.31" stroke-width="1" zvalue="8818"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="64" MaxPinNum="2"/>
   </metadata>
  <path d="M 887.28 155.31 L 914.13 155.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv35" d="M 914.33 352.25 L 914.33 370.59" stroke-width="1" zvalue="8824"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@1" LinkObjectIDznd="48@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 914.33 352.25 L 914.33 370.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv35" d="M 914.31 305.45 L 914.31 332.62" stroke-width="1" zvalue="8825"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@1" LinkObjectIDznd="65@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 914.31 305.45 L 914.31 332.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="kv35" d="M 818.48 435.78 L 818.48 412.61" stroke-width="1" zvalue="8872"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@1" LinkObjectIDznd="272@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.48 435.78 L 818.48 412.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="555">
   <path class="kv10" d="M 687.37 837.13 L 687.29 877.38" stroke-width="1" zvalue="9422"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="549@0" LinkObjectIDznd="552@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 687.37 837.13 L 687.29 877.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="535">
   <path class="kv35" d="M 818.64 461.66 L 818.54 487.56" stroke-width="1" zvalue="9712"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="536@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.64 461.66 L 818.54 487.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="539">
   <path class="kv10" d="M 818.66 585.11 L 818.66 549.06" stroke-width="1" zvalue="9714"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="461@1" LinkObjectIDznd="536@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.66 585.11 L 818.66 549.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="556">
   <path class="kv10" d="M 818.82 611 L 818.82 636.42" stroke-width="1" zvalue="9719"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="461@0" LinkObjectIDznd="551@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.82 611 L 818.82 636.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="623">
   <path class="kv10" d="M 818.37 656.05 L 818.37 690.59" stroke-width="1" zvalue="9720"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="551@0" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.37 656.05 L 818.37 690.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="813">
   <path class="kv35" d="M 1398.65 392.98 L 1398.65 370.59" stroke-width="1" zvalue="9726"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="815@1" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1398.65 392.98 L 1398.65 370.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="812">
   <path class="kv35" d="M 1398.48 435.78 L 1398.48 412.61" stroke-width="1" zvalue="9727"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="816@1" LinkObjectIDznd="815@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1398.48 435.78 L 1398.48 412.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="809">
   <path class="kv35" d="M 1398.64 461.66 L 1398.64 495.94" stroke-width="1" zvalue="9730"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="816@0" LinkObjectIDznd="822@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1398.64 461.66 L 1398.64 495.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="633">
   <path class="kv10" d="M 1398.82 611 L 1398.82 636.42" stroke-width="1" zvalue="9736"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="811@0" LinkObjectIDznd="636@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1398.82 611 L 1398.82 636.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="632">
   <path class="kv10" d="M 1398.37 656.05 L 1398.37 690.59" stroke-width="1" zvalue="9737"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="636@0" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1398.37 656.05 L 1398.37 690.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="824">
   <path class="kv10" d="M 1397.68 551.71 L 1397.68 585.11" stroke-width="1" zvalue="9740"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="822@1" LinkObjectIDznd="811@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1397.68 551.71 L 1397.68 585.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="865">
   <path class="kv10" d="M 687.64 667.8 L 687.64 690.59" stroke-width="1" zvalue="9765"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="864@1" LinkObjectIDznd="166@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 687.64 667.8 L 687.64 690.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv10" d="M 687.34 791.82 L 687.34 817.5" stroke-width="1" zvalue="9773"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="546@0" LinkObjectIDznd="549@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 687.34 791.82 L 687.34 817.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv10" d="M 687.37 745.13 L 687.37 765.94" stroke-width="1" zvalue="9780"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@0" LinkObjectIDznd="546@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 687.37 745.13 L 687.37 765.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv10" d="M 687.34 725.5 L 687.34 690.59" stroke-width="1" zvalue="9781"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@1" LinkObjectIDznd="865" MaxPinNum="2"/>
   </metadata>
  <path d="M 687.34 725.5 L 687.34 690.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv10" d="M 927.37 837.13 L 927.37 880.65" stroke-width="1" zvalue="9864"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="219@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 927.37 837.13 L 927.37 880.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv10" d="M 927.34 790.82 L 927.34 817.5" stroke-width="1" zvalue="9867"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="116@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 927.34 790.82 L 927.34 817.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv10" d="M 927.37 745.13 L 927.37 764.94" stroke-width="1" zvalue="9870"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="115@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 927.37 745.13 L 927.37 764.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv10" d="M 927.34 725.5 L 927.34 690.59" stroke-width="1" zvalue="9871"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@1" LinkObjectIDznd="166@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 927.34 725.5 L 927.34 690.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv10" d="M 1195.37 837.13 L 1195.29 881.38" stroke-width="1" zvalue="9879"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@0" LinkObjectIDznd="129@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1195.37 837.13 L 1195.29 881.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv10" d="M 1224.03 873.25 L 1224.03 860.55 L 1195.33 860.55" stroke-width="1" zvalue="9881"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="128" MaxPinNum="2"/>
   </metadata>
  <path d="M 1224.03 873.25 L 1224.03 860.55 L 1195.33 860.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv10" d="M 1195.34 790.57 L 1195.34 817.5" stroke-width="1" zvalue="9882"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="133@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1195.34 790.57 L 1195.34 817.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv10" d="M 1195.37 745.13 L 1195.37 764.69" stroke-width="1" zvalue="9885"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="132@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1195.37 745.13 L 1195.37 764.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv10" d="M 1195.34 725.5 L 1195.34 690.59" stroke-width="1" zvalue="9886"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@1" LinkObjectIDznd="166@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1195.34 725.5 L 1195.34 690.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv10" d="M 1398.57 837.13 L 1398.57 881.38" stroke-width="1" zvalue="9894"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="155@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1398.57 837.13 L 1398.57 881.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv10" d="M 1432.03 873.25 L 1432.03 859.25 L 1398.57 859.25" stroke-width="1" zvalue="9896"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="152" MaxPinNum="2"/>
   </metadata>
  <path d="M 1432.03 873.25 L 1432.03 859.25 L 1398.57 859.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv10" d="M 1398.55 791.82 L 1398.55 817.5" stroke-width="1" zvalue="9897"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="159@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1398.55 791.82 L 1398.55 817.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv10" d="M 1398.39 745.13 L 1398.39 765.94" stroke-width="1" zvalue="9900"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="158@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1398.39 745.13 L 1398.39 765.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="kv10" d="M 1398.37 725.5 L 1398.37 690.59" stroke-width="1" zvalue="9901"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@1" LinkObjectIDznd="632" MaxPinNum="2"/>
   </metadata>
  <path d="M 1398.37 725.5 L 1398.37 690.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv10" d="M 1625.08 817.13 L 1625.08 857.6" stroke-width="1" zvalue="9909"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="100@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1625.08 817.13 L 1625.08 857.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv10" d="M 1625.05 778.16 L 1625.06 797.5" stroke-width="1" zvalue="9912"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="197@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1625.05 778.16 L 1625.06 797.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv10" d="M 1623.37 737.13 L 1623.37 752.27" stroke-width="1" zvalue="9915"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@0" LinkObjectIDznd="191@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1623.37 737.13 L 1623.37 752.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="165">
   <path class="kv10" d="M 1623.34 717.5 L 1623.34 690.59" stroke-width="1" zvalue="9916"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@1" LinkObjectIDznd="166@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1623.34 717.5 L 1623.34 690.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="kv10" d="M 1625.08 829.17 L 1655.48 829.17 L 1655.51 846.9" stroke-width="1" zvalue="9920"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178" LinkObjectIDznd="175@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1625.08 829.17 L 1655.48 829.17 L 1655.51 846.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="kv35" d="M 1185.08 370.59 L 1185.08 392.25" stroke-width="1" zvalue="9923"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@2" LinkObjectIDznd="2@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1185.08 370.59 L 1185.08 392.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv35" d="M 1184.92 417.75 L 1184.92 440.58" stroke-width="1" zvalue="9924"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2@1" LinkObjectIDznd="533@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1184.92 417.75 L 1184.92 440.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv35" d="M 956.61 175.16 L 972.61 175.16" stroke-width="1" zvalue="9929"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="81@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 956.61 175.16 L 972.61 175.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv35" d="M 936.98 175.14 L 914.13 175.14" stroke-width="1" zvalue="9930"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@1" LinkObjectIDznd="64" MaxPinNum="2"/>
   </metadata>
  <path d="M 936.98 175.14 L 914.13 175.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv35" d="M 889.82 191.88 L 914.13 191.88" stroke-width="1" zvalue="9931"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="64" MaxPinNum="2"/>
   </metadata>
  <path d="M 889.82 191.88 L 914.13 191.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv35" d="M 1300.6 279.56 L 1300.6 244.98" stroke-width="1" zvalue="9941"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="47@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1300.6 279.56 L 1300.6 244.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv35" d="M 1300.57 225.35 L 1300.57 137.11" stroke-width="1" zvalue="9944"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="56@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1300.57 225.35 L 1300.57 137.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv35" d="M 1273.72 155.31 L 1300.57 155.31" stroke-width="1" zvalue="9945"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@0" LinkObjectIDznd="43" MaxPinNum="2"/>
   </metadata>
  <path d="M 1273.72 155.31 L 1300.57 155.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv35" d="M 1300.78 352.25 L 1300.78 370.59" stroke-width="1" zvalue="9946"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@1" LinkObjectIDznd="48@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1300.78 352.25 L 1300.78 370.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv35" d="M 1300.75 305.45 L 1300.75 332.62" stroke-width="1" zvalue="9947"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@1" LinkObjectIDznd="44@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1300.75 305.45 L 1300.75 332.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv35" d="M 1343.06 175.16 L 1359.06 175.16" stroke-width="1" zvalue="9952"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="38@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1343.06 175.16 L 1359.06 175.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv35" d="M 1323.43 175.14 L 1300.57 175.14" stroke-width="1" zvalue="9953"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@1" LinkObjectIDznd="43" MaxPinNum="2"/>
   </metadata>
  <path d="M 1323.43 175.14 L 1300.57 175.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv35" d="M 1276.26 191.88 L 1300.57 191.88" stroke-width="1" zvalue="9954"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="43" MaxPinNum="2"/>
   </metadata>
  <path d="M 1276.26 191.88 L 1300.57 191.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv35" d="M 1417.56 424.8 L 1398.48 424.8" stroke-width="1" zvalue="9957"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="812" MaxPinNum="2"/>
   </metadata>
  <path d="M 1417.56 424.8 L 1398.48 424.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv10" d="M 687.61 648.17 L 687.61 628.31" stroke-width="1" zvalue="9962"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="864@0" LinkObjectIDznd="71@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 687.61 648.17 L 687.61 628.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv10" d="M 687.81 602.81 L 687.81 580.45" stroke-width="1" zvalue="9963"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="93@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 687.81 602.81 L 687.81 580.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv10" d="M 715.56 640.28 L 687.61 640.28" stroke-width="1" zvalue="9966"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@0" LinkObjectIDznd="78" MaxPinNum="2"/>
   </metadata>
  <path d="M 715.56 640.28 L 687.61 640.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv10" d="M 633.36 621.52 L 633.36 640.28 L 687.61 640.28" stroke-width="1" zvalue="9969"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@0" LinkObjectIDznd="78" MaxPinNum="2"/>
   </metadata>
  <path d="M 633.36 621.52 L 633.36 640.28 L 687.61 640.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv10" d="M 687.35 844.72 L 721.06 844.72 L 721.06 852.24" stroke-width="1" zvalue="10066"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="555" LinkObjectIDznd="98@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 687.35 844.72 L 721.06 844.72 L 721.06 852.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="kv10" d="M 966.62 856.68 L 966.62 845.83 L 927.37 845.83" stroke-width="1" zvalue="10067"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="112" MaxPinNum="2"/>
   </metadata>
  <path d="M 966.62 856.68 L 966.62 845.83 L 927.37 845.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="170">
   <use class="kv35" height="20" transform="rotate(90,1045.37,450.448) scale(1.24619,-1.0068) translate(-205.285,-897.786)" width="10" x="1039.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="440.3800877847167" zvalue="8034"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454316261379" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454316261379"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1045.37,450.448) scale(1.24619,-1.0068) translate(-205.285,-897.786)" width="10" x="1039.143242399862" y="440.3800877847167"/></g>
  <g id="67">
   <use class="kv35" height="20" transform="rotate(270,879.999,191.815) scale(-1.24619,-1.0068) translate(-1584.92,-382.265)" width="10" x="873.7682271410732" xlink:href="#GroundDisconnector:地刀_0" y="181.746507813326" zvalue="8812"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454315671555" ObjectName="35kV清护线35167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454315671555"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,879.999,191.815) scale(-1.24619,-1.0068) translate(-1584.92,-382.265)" width="10" x="873.7682271410732" y="181.746507813326"/></g>
  <g id="175">
   <use class="kv10" height="20" transform="rotate(0,1655.57,856.719) scale(-1.24619,1.0068) translate(-2982.85,-5.71887)" width="10" x="1649.340680866889" xlink:href="#GroundDisconnector:地刀_0" y="846.6511097754801" zvalue="9337"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454315933699" ObjectName="10kV1号电容器05567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454315933699"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1655.57,856.719) scale(-1.24619,1.0068) translate(-2982.85,-5.71887)" width="10" x="1649.340680866889" y="846.6511097754801"/></g>
  <g id="46">
   <use class="kv35" height="20" transform="rotate(270,1266.44,191.815) scale(-1.24619,-1.0068) translate(-2281.47,-382.265)" width="10" x="1260.212671585518" xlink:href="#GroundDisconnector:地刀_0" y="181.7465078133259" zvalue="9939"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454318161923" ObjectName="35kV别护线35267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454318161923"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1266.44,191.815) scale(-1.24619,-1.0068) translate(-2281.47,-382.265)" width="10" x="1260.212671585518" y="181.7465078133259"/></g>
  <g id="60">
   <use class="kv35" height="20" transform="rotate(90,1427.37,424.734) scale(1.24619,-1.0068) translate(-280.75,-846.531)" width="10" x="1421.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="414.665802070431" zvalue="9956"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454318424067" ObjectName="#2主变35kV侧30217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454318424067"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1427.37,424.734) scale(1.24619,-1.0068) translate(-280.75,-846.531)" width="10" x="1421.143242399862" y="414.665802070431"/></g>
  <g id="83">
   <use class="kv10" height="20" transform="rotate(90,725.374,640.215) scale(1.24619,-1.0068) translate(-142.068,-1276.04)" width="10" x="719.1432423998622" xlink:href="#GroundDisconnector:地刀_0" y="630.1474620901151" zvalue="9965"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454318620675" ObjectName="10kV母线电压互感器09017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454318620675"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,725.374,640.215) scale(1.24619,-1.0068) translate(-142.068,-1276.04)" width="10" x="719.1432423998622" y="630.1474620901151"/></g>
  <g id="98">
   <use class="kv10" height="20" transform="rotate(0,721.127,862.052) scale(-1.24619,1.0068) translate(-1298.56,-5.7549)" width="10" x="714.896236422445" xlink:href="#GroundDisconnector:地刀_0" y="851.9844431088135" zvalue="10063"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454319013890" ObjectName="10kV备用Ⅰ线05167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454319013890"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,721.127,862.052) scale(-1.24619,1.0068) translate(-1298.56,-5.7549)" width="10" x="714.896236422445" y="851.9844431088135"/></g>
  <g id="104">
   <use class="kv10" height="20" transform="rotate(0,966.683,866.497) scale(-1.24619,1.0068) translate(-1741.16,-5.78492)" width="10" x="960.4517919780005" xlink:href="#GroundDisconnector:地刀_0" y="856.4288875532579" zvalue="10065"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454319144962" ObjectName="10kV南护线05267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454319144962"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,966.683,866.497) scale(-1.24619,1.0068) translate(-1741.16,-5.78492)" width="10" x="960.4517919780005" y="856.4288875532579"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="70">
   <use class="kv35" height="30" transform="rotate(0,914.128,129.344) scale(1.98323,0.522926) translate(-449.757,110.846)" width="7" x="907.1867041796982" xlink:href="#ACLineSegment:线路_0" y="121.4999899383938" zvalue="8806"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249325109252" ObjectName="35kV清护线"/>
   <cge:TPSR_Ref TObjectID="8444249325109252_5066549681717249"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,914.128,129.344) scale(1.98323,0.522926) translate(-449.757,110.846)" width="7" x="907.1867041796982" y="121.4999899383938"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="552">
   <use class="kv10" height="30" transform="rotate(0,687.287,894.25) scale(1.25,-1.25) translate(-135.957,-1605.9)" width="12" x="679.7865295410156" xlink:href="#EnergyConsumer:负荷_0" y="875.5" zvalue="9421"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454316064771" ObjectName="10kV备用Ⅰ线"/>
   <cge:TPSR_Ref TObjectID="6192454316064771"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,687.287,894.25) scale(1.25,-1.25) translate(-135.957,-1605.9)" width="12" x="679.7865295410156" y="875.5"/></g>
  <g id="533">
   <use class="kv35" height="30" transform="rotate(0,1185.02,462.705) scale(1.53571,1.53571) translate(-405.88,-153.373)" width="28" x="1163.52272700799" xlink:href="#EnergyConsumer:站用变DY接地_0" y="439.6688311688312" zvalue="9704"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454316392451" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1185.02,462.705) scale(1.53571,1.53571) translate(-405.88,-153.373)" width="28" x="1163.52272700799" y="439.6688311688312"/></g>
  <g id="129">
   <use class="kv10" height="30" transform="rotate(0,1195.29,898.25) scale(1.25,-1.25) translate(-237.557,-1613.1)" width="12" x="1187.786529541016" xlink:href="#EnergyConsumer:负荷_0" y="879.5" zvalue="9878"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454317047811" ObjectName="10kV邦掌线"/>
   <cge:TPSR_Ref TObjectID="6192454317047811"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1195.29,898.25) scale(1.25,-1.25) translate(-237.557,-1613.1)" width="12" x="1187.786529541016" y="879.5"/></g>
  <g id="155">
   <use class="kv10" height="30" transform="rotate(0,1398.57,898.25) scale(1.25,-1.25) translate(-278.214,-1613.1)" width="12" x="1391.071948652226" xlink:href="#EnergyConsumer:负荷_0" y="879.5" zvalue="9893"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454317309955" ObjectName="10kV护国线"/>
   <cge:TPSR_Ref TObjectID="6192454317309955"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1398.57,898.25) scale(1.25,-1.25) translate(-278.214,-1613.1)" width="12" x="1391.071948652226" y="879.5"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="171">
   <use class="kv35" height="26" transform="rotate(90,875.806,155.278) scale(0.838049,0.927421) translate(168.276,11.2084)" width="12" x="870.7781809263649" xlink:href="#Accessory:避雷器1_0" y="143.2218190736352" zvalue="9700"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454316130307" ObjectName="35kV清护线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,875.806,155.278) scale(0.838049,0.927421) translate(168.276,11.2084)" width="12" x="870.7781809263649" y="143.2218190736352"/></g>
  <g id="131">
   <use class="kv10" height="26" transform="rotate(0,1224.06,884.719) scale(-0.838049,0.927421) translate(-2685.63,68.2937)" width="12" x="1219.028180926365" xlink:href="#Accessory:避雷器1_0" y="872.6626333680676" zvalue="9877"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454317113347" ObjectName="10kV邦掌线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1224.06,884.719) scale(-0.838049,0.927421) translate(-2685.63,68.2937)" width="12" x="1219.028180926365" y="872.6626333680676"/></g>
  <g id="157">
   <use class="kv10" height="26" transform="rotate(0,1432.06,884.719) scale(-0.838049,0.927421) translate(-3141.83,68.2937)" width="12" x="1427.028180926365" xlink:href="#Accessory:避雷器1_0" y="872.6626333680676" zvalue="9892"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454317375491" ObjectName="10kV护国线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1432.06,884.719) scale(-0.838049,0.927421) translate(-3141.83,68.2937)" width="12" x="1427.028180926365" y="872.6626333680676"/></g>
  <g id="81">
   <use class="kv35" height="40" transform="rotate(90,996.667,175.165) scale(1.30016,1.30016) translate(-225.589,-34.4357)" width="30" x="977.1645576121706" xlink:href="#Accessory:带熔断器的线路PT1_0" y="149.1618606377072" zvalue="9926"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454317768707" ObjectName="35kV清护线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,996.667,175.165) scale(1.30016,1.30016) translate(-225.589,-34.4357)" width="30" x="977.1645576121706" y="149.1618606377072"/></g>
  <g id="39">
   <use class="kv35" height="26" transform="rotate(90,1262.25,155.278) scale(0.838049,0.927421) translate(242.956,11.2084)" width="12" x="1257.22262537081" xlink:href="#Accessory:避雷器1_0" y="143.2218190736352" zvalue="9948"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454317965315" ObjectName="35kV别护线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1262.25,155.278) scale(0.838049,0.927421) translate(242.956,11.2084)" width="12" x="1257.22262537081" y="143.2218190736352"/></g>
  <g id="38">
   <use class="kv35" height="40" transform="rotate(90,1383.11,175.165) scale(1.30016,1.30016) translate(-314.804,-34.4357)" width="30" x="1363.609002056615" xlink:href="#Accessory:带熔断器的线路PT1_0" y="149.1618606377072" zvalue="9949"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454317899779" ObjectName="35kV别护线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1383.11,175.165) scale(1.30016,1.30016) translate(-314.804,-34.4357)" width="30" x="1363.609002056615" y="149.1618606377072"/></g>
  <g id="85">
   <use class="kv10" height="26" transform="rotate(0,633.39,610.052) scale(-0.838049,-0.927421) translate(-1390.15,-1268.79)" width="12" x="628.3615142596982" xlink:href="#Accessory:避雷器1_0" y="597.995966701401" zvalue="9968"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454318686211" ObjectName="10kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,633.39,610.052) scale(-0.838049,-0.927421) translate(-1390.15,-1268.79)" width="12" x="628.3615142596982" y="597.995966701401"/></g>
  <g id="94">
   <use class="kv35" height="18" transform="rotate(0,1019.08,496.166) scale(3.13536,3.18512) translate(-678.039,-320.724)" width="15" x="995.5677414212685" xlink:href="#Accessory:PT8_0" y="467.5" zvalue="9971"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454318751747" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1019.08,496.166) scale(3.13536,3.18512) translate(-678.039,-320.724)" width="15" x="995.5677414212685" y="467.5"/></g>
  <g id="93">
   <use class="kv10" height="18" transform="rotate(0,687.806,562.375) scale(2.06944,-2.06944) translate(-345.818,-824.502)" width="18" x="669.1805555555554" xlink:href="#Accessory:四卷PT_0" y="543.75" zvalue="9973"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454318817283" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,687.806,562.375) scale(2.06944,-2.06944) translate(-345.818,-824.502)" width="18" x="669.1805555555554" y="543.75"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="536">
   <g id="5360">
    <use class="kv35" height="50" transform="rotate(0,818.5,518.25) scale(1.25,1.25) translate(-159.95,-97.4)" width="30" x="799.75" xlink:href="#PowerTransformer2:Y-D_0" y="487" zvalue="9713"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874578526210" ObjectName="35"/>
    </metadata>
   </g>
   <g id="5361">
    <use class="kv10" height="50" transform="rotate(0,818.5,518.25) scale(1.25,1.25) translate(-159.95,-97.4)" width="30" x="799.75" xlink:href="#PowerTransformer2:Y-D_1" y="487" zvalue="9713"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874578591746" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399528218626" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399528218626"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,818.5,518.25) scale(1.25,1.25) translate(-159.95,-97.4)" width="30" x="799.75" y="487"/></g>
  <g id="822">
   <g id="8220">
    <use class="kv35" height="20" transform="rotate(0,1398.12,526.625) scale(2.91667,3.9625) translate(-904.393,-364.098)" width="15" x="1376.25" xlink:href="#PowerTransformer2:接地可调两卷变（规范制图）_0" y="487" zvalue="9739"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874578657282" ObjectName="35"/>
    </metadata>
   </g>
   <g id="8221">
    <use class="kv10" height="20" transform="rotate(0,1398.12,526.625) scale(2.91667,3.9625) translate(-904.393,-364.098)" width="15" x="1376.25" xlink:href="#PowerTransformer2:接地可调两卷变（规范制图）_1" y="487" zvalue="9739"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874578722818" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399528284162" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399528284162"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1398.12,526.625) scale(2.91667,3.9625) translate(-904.393,-364.098)" width="15" x="1376.25" y="487"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="100">
   <use class="kv10" height="40" transform="rotate(0,1626.08,885.84) scale(1.81597,1.84588) translate(-720.858,-389.022)" width="24" x="1604.291666666667" xlink:href="#Compensator:10kV电容器_0" y="848.9228099342654" zvalue="9977"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454318882819" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192454318882819"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1626.08,885.84) scale(1.81597,1.84588) translate(-720.858,-389.022)" width="24" x="1604.291666666667" y="848.9228099342654"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="9" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,910.128,45) scale(1,1) translate(0,0)" writing-mode="lr" x="910.3200000000001" xml:space="preserve" y="49.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134821277698" ObjectName="P"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="10" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1288.57,37) scale(1,1) translate(0,0)" writing-mode="lr" x="1288.77" xml:space="preserve" y="41.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134835367938" ObjectName="P"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="11" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,910.128,60) scale(1,1) translate(0,0)" writing-mode="lr" x="910.3200000000001" xml:space="preserve" y="64.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134821343234" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="12" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1288.57,56) scale(1,1) translate(0,0)" writing-mode="lr" x="1288.77" xml:space="preserve" y="60.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134835433474" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="13" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,910.128,75) scale(1,1) translate(0,0)" writing-mode="lr" x="910.3200000000001" xml:space="preserve" y="79.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134821408770" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="14" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1288.57,75) scale(1,1) translate(0,0)" writing-mode="lr" x="1288.77" xml:space="preserve" y="79.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134835499010" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,908.069,89.3439) scale(1,1) translate(0,0)" writing-mode="lr" x="908.26" xml:space="preserve" y="94.25" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134821670914" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="16" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1294.51,93.3439) scale(1,1) translate(0,0)" writing-mode="lr" x="1294.71" xml:space="preserve" y="98.25" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134835761154" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="17" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,899,416.5) scale(1,1) translate(0,0)" writing-mode="lr" x="899.2" xml:space="preserve" y="421.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134824882178" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="18" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,899,443.5) scale(1,1) translate(0,0)" writing-mode="lr" x="899.2" xml:space="preserve" y="448.41" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134824947714" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,899,470.5) scale(1,1) translate(0,0)" writing-mode="lr" x="899.2" xml:space="preserve" y="475.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134825144322" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="20" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,899,568) scale(1,1) translate(0,0)" writing-mode="lr" x="899.2" xml:space="preserve" y="572.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134825013250" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="21" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,899,595) scale(1,1) translate(0,0)" writing-mode="lr" x="899.2" xml:space="preserve" y="599.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134825078786" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="22" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,899,622) scale(1,1) translate(0,0)" writing-mode="lr" x="899.2" xml:space="preserve" y="626.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134825472002" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="55" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1510.62,416.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.82" xml:space="preserve" y="421.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134827962370" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="72">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="72" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1510.62,443.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.82" xml:space="preserve" y="448.41" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134828027906" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="73" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1510.62,470.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.82" xml:space="preserve" y="475.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134828224514" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="74">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="74" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1510.62,580.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.82" xml:space="preserve" y="585.66" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134828093442" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="75">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="75" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1510.62,607.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.82" xml:space="preserve" y="612.66" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134828158978" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="76">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="76" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1510.62,634.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1510.82" xml:space="preserve" y="639.66" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134828552194" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="87" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1195.29,947) scale(1,1) translate(3.83122e-13,0)" writing-mode="lr" x="1195.48" xml:space="preserve" y="951.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134831435778" ObjectName="P"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="88" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1398.57,947) scale(1,1) translate(0,0)" writing-mode="lr" x="1398.77" xml:space="preserve" y="951.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134832877570" ObjectName="P"/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="96" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1195.29,966) scale(1,1) translate(3.83122e-13,0)" writing-mode="lr" x="1195.48" xml:space="preserve" y="970.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134831501314" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="97" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1398.57,966) scale(1,1) translate(0,0)" writing-mode="lr" x="1398.77" xml:space="preserve" y="970.91" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134832943106" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="114">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="114" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1195.29,985) scale(1,1) translate(3.83122e-13,0)" writing-mode="lr" x="1195.48" xml:space="preserve" y="989.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134831566850" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="122">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="122" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1398.57,985) scale(1,1) translate(0,0)" writing-mode="lr" x="1398.77" xml:space="preserve" y="989.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134833008642" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="139">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="139" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1196.54,1002.75) scale(1,1) translate(3.83539e-13,0)" writing-mode="lr" x="1196.73" xml:space="preserve" y="1007.66" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134831960066" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="140" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1404.57,1002.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1404.77" xml:space="preserve" y="1007.41" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134833401858" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" SignFlag="up" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="148" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1626.08,962.258) scale(1,1) translate(0,0)" writing-mode="lr" x="1626.28" xml:space="preserve" y="967.17" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134836416514" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="149">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="149" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1626.08,981.258) scale(1,1) translate(0,0)" writing-mode="lr" x="1626.28" xml:space="preserve" y="986.17" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134836482050" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="4" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,690.144,943.571) scale(1,1) translate(0,0)" writing-mode="lr" x="690.34" xml:space="preserve" y="948.48" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134823374850" ObjectName="P"/>
   </metadata>
  </g>
  <g id="77">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="77" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,690.144,966.571) scale(1,1) translate(0,0)" writing-mode="lr" x="690.34" xml:space="preserve" y="971.48" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134823440386" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="86" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,690.144,989.571) scale(1,1) translate(0,0)" writing-mode="lr" x="690.34" xml:space="preserve" y="994.48" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134823505922" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,693.715,1008.82) scale(1,1) translate(0,0)" writing-mode="lr" x="693.91" xml:space="preserve" y="1013.73" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134823899138" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="364">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="364" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,135.5,519.468) scale(1,1) translate(0,0)" writing-mode="lr" x="135.7" xml:space="preserve" y="524.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134819180546" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="363">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="363" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,135.5,542.468) scale(1,1) translate(0,0)" writing-mode="lr" x="135.7" xml:space="preserve" y="547.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134819246082" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="362">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="362" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,135.5,565.468) scale(1,1) translate(0,0)" writing-mode="lr" x="135.7" xml:space="preserve" y="570.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134819311618" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="361">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="361" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,135.5,493.968) scale(1,1) translate(0,0)" writing-mode="lr" x="135.7" xml:space="preserve" y="498.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134819442690" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="360">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="360" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,139,200.468) scale(1,1) translate(0,0)" writing-mode="lr" x="139.2" xml:space="preserve" y="206.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134819573762" ObjectName="F"/>
   </metadata>
  </g>
  <g id="359">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="359" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,253.333,519.357) scale(1,1) translate(0,0)" writing-mode="lr" x="253.53" xml:space="preserve" y="524.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134819704834" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="358">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="358" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,253.333,542.357) scale(1,1) translate(0,0)" writing-mode="lr" x="253.53" xml:space="preserve" y="547.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134819770370" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="357">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="357" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,253.333,565.357) scale(1,1) translate(0,0)" writing-mode="lr" x="253.53" xml:space="preserve" y="570.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134819835906" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="356">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="356" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,253.333,492.857) scale(1,1) translate(0,-1.07105e-13)" writing-mode="lr" x="253.53" xml:space="preserve" y="497.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134819966978" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="355">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="355" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,139,225.357) scale(1,1) translate(0,0)" writing-mode="lr" x="139.2" xml:space="preserve" y="231.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134820098050" ObjectName="F"/>
   </metadata>
  </g>
  <g id="354">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="354" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,139,247.552) scale(1,1) translate(-1.92139e-14,0)" writing-mode="lr" x="139.23" xml:space="preserve" y="254.04" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134828421122" ObjectName="YW1"/>
   </metadata>
  </g>
  <g id="353">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="353" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,139,270.552) scale(1,1) translate(0,0)" writing-mode="lr" x="139.2" xml:space="preserve" y="277.04" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134828486658" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="350">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="350" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,135.75,593.468) scale(1,1) translate(0,0)" writing-mode="lr" x="135.95" xml:space="preserve" y="598.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134819639298" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="349">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="349" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,254.042,592.357) scale(1,1) translate(0,0)" writing-mode="lr" x="254.24" xml:space="preserve" y="597.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134820163586" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="348">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="348" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,139,177.357) scale(1,1) translate(0,0)" writing-mode="lr" x="139.15" xml:space="preserve" y="183.63" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134839431170" ObjectName=""/>
   </metadata>
  </g>
  <g id="347">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="347" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,316,176.357) scale(1,1) translate(0,0)" writing-mode="lr" x="316.15" xml:space="preserve" y="182.63" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134839496706" ObjectName=""/>
   </metadata>
  </g>
  <g id="222">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="222" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,927.37,948) scale(1,1) translate(0,0)" writing-mode="lr" x="926.9" xml:space="preserve" y="952.65" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134829993986" ObjectName="P"/>
   </metadata>
  </g>
  <g id="223">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="223" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,927.37,967) scale(1,1) translate(0,0)" writing-mode="lr" x="926.9" xml:space="preserve" y="971.65" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134830059522" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="224">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="224" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,927.37,984) scale(1,1) translate(0,0)" writing-mode="lr" x="926.9" xml:space="preserve" y="988.65" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134830125058" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="225">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="225" prefix="Cos:" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,927.87,1003.75) scale(1,1) translate(0,0)" writing-mode="lr" x="927.4" xml:space="preserve" y="1008.4" zvalue="1">Cos:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134830518274" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="226">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="226" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,559.667,331.591) scale(1,1) translate(0,0)" writing-mode="lr" x="559.2" xml:space="preserve" y="336.37" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134819442690" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="232">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="232" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1094.67,640.591) scale(1,1) translate(0,0)" writing-mode="lr" x="1094.2" xml:space="preserve" y="645.37" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481134819966978" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="352">
   <use height="30" transform="rotate(0,345.625,317.857) scale(0.708333,0.665547) translate(137.941,154.714)" width="30" x="335" xlink:href="#State:红绿圆(方形)_0" y="307.87" zvalue="10291"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374925103105" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,345.625,317.857) scale(0.708333,0.665547) translate(137.941,154.714)" width="30" x="335" y="307.87"/></g>
  <g id="351">
   <use height="30" transform="rotate(0,250,317.857) scale(0.708333,0.665547) translate(98.5662,154.714)" width="30" x="239.38" xlink:href="#State:红绿圆(方形)_0" y="307.87" zvalue="10292"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562954689773574" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,250,317.857) scale(0.708333,0.665547) translate(98.5662,154.714)" width="30" x="239.38" y="307.87"/></g>
  <g id="830">
   <use height="30" transform="rotate(0,328.812,133.964) scale(1.27778,1.03333) translate(-58.981,-3.82141)" width="90" x="271.31" xlink:href="#State:全站检修_0" y="118.46" zvalue="10372"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549681717249" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,328.812,133.964) scale(1.27778,1.03333) translate(-58.981,-3.82141)" width="90" x="271.31" y="118.46"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
</svg>