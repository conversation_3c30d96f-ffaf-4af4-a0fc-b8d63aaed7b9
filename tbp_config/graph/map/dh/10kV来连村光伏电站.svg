<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549594619906" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0.25" y2="25.75"/>
  </symbol>
  <symbol id="Disconnector:小车隔刀熔断器_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_0" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="3.333333333333332" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="24.08333333333333" y2="29.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="7" y1="19.5" y2="24.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_1" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="18.25" y2="32.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_2" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="4" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="10" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="25.83333333333333" y2="32.41666666666667"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:4卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2027.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id=":光伏模组_0" viewBox="0,0,80,80">
   <use terminal-index="0" type="0" x="23.86666666666666" xlink:href="#terminal" y="70.93333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="50.8" x2="57.2" y1="10.13333333333333" y2="10.13333333333333"/>
   <path d="M 36.7489 56.5767 L 38.0132 59.1405 L 39.2774 56.5767" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.6" x2="50.66666666666667" y1="21.33333333333333" y2="11.33333333333333"/>
   <path d="M 52.4 16.5333 L 49.2 16.5333 L 49.2 27.7333 L 52.4 27.7333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(170,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.2" x2="55.2" y1="32.53333333333333" y2="43.73333333333333"/>
   <path d="M 38 45.3333 L 38 35.7333 L 66.8 35.7333 L 66.8 30.9333" fill="none" stroke="rgb(170,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="73.59999999999999" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,40.13) scale(1,1) translate(0,0)" width="68.8" x="5.6" y="3.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="66.8" x2="66.8" y1="60.13333333333333" y2="64.93333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="5" x1="14" x2="33.2" y1="70.93333333333332" y2="70.93333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="62" x2="66.8" y1="60.13333333333333" y2="60.13333333333333"/>
   <path d="M 53.7333 10.4 L 53.7333 5.6 L 23.3333 5.6 L 23.7333 71.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="6 2" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.2" x2="55.2" y1="68.13333333333334" y2="79.20000000000002"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="23.6" y1="27.73333333333334" y2="27.73333333333334"/>
   <rect fill-opacity="0" height="6.77" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,37.91,50.21) scale(1,1) translate(0,0)" width="3.37" x="36.22" y="46.82"/>
   <path d="M 36.7489 61.0634 L 38.0132 63.6272 L 39.2774 61.0634" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="65.07886701035602" x2="68.00570387390415" y1="28.93345174664175" y2="28.93345174664175"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="37.9078029848685" x2="37.9078029848685" y1="55.40157383031868" y2="45.46666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.20202391636546" x2="14.96424980017045" y1="38.63150148536967" y2="35.9076739581179"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.20202391636535" x2="14.20202391636535" y1="27.59999999999994" y2="38.17753023082763"/>
   <rect fill-opacity="0" height="8.44" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.27,38.31) scale(1,1) translate(0,0)" width="5.34" x="11.6" y="34.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.20202391636544" x2="13.43979803256045" y1="38.63150148536967" y2="35.9076739581179"/>
   <ellipse cx="55.25" cy="50.35" fill-opacity="0" rx="6.61" ry="6.88" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="63.2906587876924" x2="70.00000000000001" y1="17.85532825265332" y2="17.85532825265332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="64.40888232307701" x2="68.88177646461541" y1="16.86538557734767" y2="16.86538557734767"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="65.40515432248043" x2="67.64160139324963" y1="15.89299537279264" y2="15.89299537279264"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.21175520726185" x2="14.21175520726185" y1="42.19334811824088" y2="44.59300008617765"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="66.58435362585561" x2="66.58435362585561" y1="17.91050011524912" y2="21.03483990886042"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="63.06666666666666" x2="66.60326121012066" y1="27.83642232472767" y2="21.07872108573694"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="66.5726562972967" x2="66.5726562972967" y1="28.99881464929833" y2="31.03687217788674"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="52.85048888888888" x2="54.58955061728395" y1="51.30912086720867" y2="47.91573333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="58.06767407407408" x2="56.32861234567901" y1="51.30912086720867" y2="47.91573333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="52.85048888888888" x2="58.06767407407408" y1="51.30912086720867" y2="51.30912086720867"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.29388998943305" x2="12.96402824408577" y1="44.28768815766198" y2="44.28768815766198"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.83991873489108" x2="13.41799949862773" y1="44.96864503947492" y2="44.96864503947492"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.52213885671171" x2="13.73577937680711" y1="45.64960192128786" y2="45.64960192128786"/>
   <ellipse cx="55.29" cy="61.19" fill-opacity="0" rx="6.67" ry="6.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.25184255358918" x2="51.41184255358919" y1="60.85264603444045" y2="58.87258205947708"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.25184255358919" x2="55.25184255358919" y1="64.81277398436721" y2="60.85264603444048"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.2518425535892" x2="59.09184255358919" y1="60.85264603444045" y2="58.87258205947708"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="70.20141457174999" x2="63.35850922025583" y1="64.84471210629269" y2="64.84471210629269"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="68.86808123841665" x2="64.69184255358917" y1="66.84471210629269" y2="66.84471210629269"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="67.93474790508333" x2="65.62517588692251" y1="68.84471210629269" y2="68.84471210629269"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.06666666666667" x2="55.06666666666667" y1="32.26666666666668" y2="19.86666666666667"/>
   <rect fill-opacity="0" height="8.449999999999999" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,54.93,25.92) scale(1,1) translate(0,0)" width="4.27" x="52.8" y="21.69"/>
   <ellipse cx="37.99" cy="57.88" fill-opacity="0" rx="2.94" ry="2.98" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="37.99" cy="62.37" fill-opacity="0" rx="2.94" ry="2.98" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="54.24" cy="11.23" fill-opacity="0" rx="1.1" ry="1.1" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV来连村光伏电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="46.71" xlink:href="logo.png" y="38.57"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,195.714,70.5714) scale(1,1) translate(0,0)" writing-mode="lr" x="195.71" xml:space="preserve" y="74.06999999999999" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,198.214,68.2618) scale(1,1) translate(0,0)" writing-mode="lr" x="198.21" xml:space="preserve" y="77.26000000000001" zvalue="5">10kV来连村光伏电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="77" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,87.2143,309.571) scale(1,1) translate(0,0)" width="97" x="38.71" y="297.57" zvalue="10"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,87.2143,309.571) scale(1,1) translate(0,0)" writing-mode="lr" x="87.20999999999999" xml:space="preserve" y="314.07" zvalue="10">全站公用</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="436.2380720772879" y2="436.2380720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="473.7280720772879" y2="473.7280720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="58.93664745163255" y1="436.2380720772879" y2="473.7280720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2813474516327" x2="107.2813474516327" y1="436.2380720772879" y2="473.7280720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="436.2380720772879" y2="436.2380720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="473.7280720772879" y2="473.7280720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="107.2816474516326" y1="436.2380720772879" y2="473.7280720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3899474516327" x2="169.3899474516327" y1="436.2380720772879" y2="473.7280720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="436.2380720772879" y2="436.2380720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="473.7280720772879" y2="473.7280720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="169.3894474516326" y1="436.2380720772879" y2="473.7280720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7142474516327" x2="232.7142474516327" y1="436.2380720772879" y2="473.7280720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="436.2380720772879" y2="436.2380720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="473.7280720772879" y2="473.7280720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="232.7141474516326" y1="436.2380720772879" y2="473.7280720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="436.2380720772879" y2="473.7280720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="436.2380720772879" y2="436.2380720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="473.7280720772879" y2="473.7280720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="436.2380720772879" y2="473.7280720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="356.9307474516327" x2="356.9307474516327" y1="436.2380720772879" y2="473.7280720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="473.728172077288" y2="473.728172077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="497.8967720772879" y2="497.8967720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="58.93664745163255" y1="473.728172077288" y2="497.8967720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2813474516327" x2="107.2813474516327" y1="473.728172077288" y2="497.8967720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="473.728172077288" y2="473.728172077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="497.8967720772879" y2="497.8967720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="107.2816474516326" y1="473.728172077288" y2="497.8967720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3899474516327" x2="169.3899474516327" y1="473.728172077288" y2="497.8967720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="473.728172077288" y2="473.728172077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="497.8967720772879" y2="497.8967720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="169.3894474516326" y1="473.728172077288" y2="497.8967720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7142474516327" x2="232.7142474516327" y1="473.728172077288" y2="497.8967720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="473.728172077288" y2="473.728172077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="497.8967720772879" y2="497.8967720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="232.7141474516326" y1="473.728172077288" y2="497.8967720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="473.728172077288" y2="497.8967720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="473.728172077288" y2="473.728172077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="497.8967720772879" y2="497.8967720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="473.728172077288" y2="497.8967720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="356.9307474516327" x2="356.9307474516327" y1="473.728172077288" y2="497.8967720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="497.8967720772879" y2="497.8967720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="522.065372077288" y2="522.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="58.93664745163255" y1="497.8967720772879" y2="522.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2813474516327" x2="107.2813474516327" y1="497.8967720772879" y2="522.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="497.8967720772879" y2="497.8967720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="522.065372077288" y2="522.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="107.2816474516326" y1="497.8967720772879" y2="522.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3899474516327" x2="169.3899474516327" y1="497.8967720772879" y2="522.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="497.8967720772879" y2="497.8967720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="522.065372077288" y2="522.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="169.3894474516326" y1="497.8967720772879" y2="522.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7142474516327" x2="232.7142474516327" y1="497.8967720772879" y2="522.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="497.8967720772879" y2="497.8967720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="522.065372077288" y2="522.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="232.7141474516326" y1="497.8967720772879" y2="522.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="497.8967720772879" y2="522.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="497.8967720772879" y2="497.8967720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="522.065372077288" y2="522.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="497.8967720772879" y2="522.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="356.9307474516327" x2="356.9307474516327" y1="497.8967720772879" y2="522.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="522.065412077288" y2="522.065412077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="546.234012077288" y2="546.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="58.93664745163255" y1="522.065412077288" y2="546.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2813474516327" x2="107.2813474516327" y1="522.065412077288" y2="546.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="522.065412077288" y2="522.065412077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="546.234012077288" y2="546.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="107.2816474516326" y1="522.065412077288" y2="546.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3899474516327" x2="169.3899474516327" y1="522.065412077288" y2="546.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="522.065412077288" y2="522.065412077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="546.234012077288" y2="546.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="169.3894474516326" y1="522.065412077288" y2="546.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7142474516327" x2="232.7142474516327" y1="522.065412077288" y2="546.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="522.065412077288" y2="522.065412077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="546.234012077288" y2="546.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="232.7141474516326" y1="522.065412077288" y2="546.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="522.065412077288" y2="546.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="522.065412077288" y2="522.065412077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="546.234012077288" y2="546.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="522.065412077288" y2="546.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="356.9307474516327" x2="356.9307474516327" y1="522.065412077288" y2="546.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="546.234172077288" y2="546.234172077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="570.402772077288" y2="570.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="58.93664745163255" y1="546.234172077288" y2="570.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2813474516327" x2="107.2813474516327" y1="546.234172077288" y2="570.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="546.234172077288" y2="546.234172077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="570.402772077288" y2="570.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="107.2816474516326" y1="546.234172077288" y2="570.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3899474516327" x2="169.3899474516327" y1="546.234172077288" y2="570.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="546.234172077288" y2="546.234172077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="570.402772077288" y2="570.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="169.3894474516326" y1="546.234172077288" y2="570.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7142474516327" x2="232.7142474516327" y1="546.234172077288" y2="570.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="546.234172077288" y2="546.234172077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="570.402772077288" y2="570.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="232.7141474516326" y1="546.234172077288" y2="570.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="546.234172077288" y2="570.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="546.234172077288" y2="546.234172077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="570.402772077288" y2="570.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="546.234172077288" y2="570.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="356.9307474516327" x2="356.9307474516327" y1="546.234172077288" y2="570.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="570.402772077288" y2="570.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="107.2813474516327" y1="594.5713720772879" y2="594.5713720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58.93664745163255" x2="58.93664745163255" y1="570.402772077288" y2="594.5713720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2813474516327" x2="107.2813474516327" y1="570.402772077288" y2="594.5713720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="570.402772077288" y2="570.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="169.3899474516327" y1="594.5713720772879" y2="594.5713720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.2816474516326" x2="107.2816474516326" y1="570.402772077288" y2="594.5713720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3899474516327" x2="169.3899474516327" y1="570.402772077288" y2="594.5713720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="570.402772077288" y2="570.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="232.7142474516327" y1="594.5713720772879" y2="594.5713720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.3894474516326" x2="169.3894474516326" y1="570.402772077288" y2="594.5713720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7142474516327" x2="232.7142474516327" y1="570.402772077288" y2="594.5713720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="570.402772077288" y2="570.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="294.8224474516326" y1="594.5713720772879" y2="594.5713720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.7141474516326" x2="232.7141474516326" y1="570.402772077288" y2="594.5713720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="570.402772077288" y2="594.5713720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="570.402772077288" y2="570.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="356.9307474516327" y1="594.5713720772879" y2="594.5713720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="294.8224474516326" x2="294.8224474516326" y1="570.402772077288" y2="594.5713720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="356.9307474516327" x2="356.9307474516327" y1="570.402772077288" y2="594.5713720772879"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="188.7142857142858" y1="154.5714285714285" y2="154.5714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="188.7142857142858" y1="180.5714285714285" y2="180.5714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="7.714285714285779" y1="154.5714285714285" y2="180.5714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="188.7142857142858" y1="154.5714285714285" y2="180.5714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="369.7142857142858" y1="154.5714285714285" y2="154.5714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="369.7142857142858" y1="180.5714285714285" y2="180.5714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="188.7142857142858" y1="154.5714285714285" y2="180.5714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.7142857142858" x2="369.7142857142858" y1="154.5714285714285" y2="180.5714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="188.7142857142858" y1="180.5714285714285" y2="180.5714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="188.7142857142858" y1="204.8214285714285" y2="204.8214285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="7.714285714285779" y1="180.5714285714285" y2="204.8214285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="188.7142857142858" y1="180.5714285714285" y2="204.8214285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="369.7142857142858" y1="180.5714285714285" y2="180.5714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="369.7142857142858" y1="204.8214285714285" y2="204.8214285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="188.7142857142858" y1="180.5714285714285" y2="204.8214285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.7142857142858" x2="369.7142857142858" y1="180.5714285714285" y2="204.8214285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="188.7142857142858" y1="204.8214285714285" y2="204.8214285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="188.7142857142858" y1="227.5714285714285" y2="227.5714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="7.714285714285779" y1="204.8214285714285" y2="227.5714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="188.7142857142858" y1="204.8214285714285" y2="227.5714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="369.7142857142858" y1="204.8214285714285" y2="204.8214285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="369.7142857142858" y1="227.5714285714285" y2="227.5714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="188.7142857142858" y1="204.8214285714285" y2="227.5714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.7142857142858" x2="369.7142857142858" y1="204.8214285714285" y2="227.5714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="188.7142857142858" y1="227.5714285714285" y2="227.5714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="188.7142857142858" y1="250.3214285714285" y2="250.3214285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="7.714285714285779" y1="227.5714285714285" y2="250.3214285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="188.7142857142858" y1="227.5714285714285" y2="250.3214285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="369.7142857142858" y1="227.5714285714285" y2="227.5714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="369.7142857142858" y1="250.3214285714285" y2="250.3214285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="188.7142857142858" y1="227.5714285714285" y2="250.3214285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.7142857142858" x2="369.7142857142858" y1="227.5714285714285" y2="250.3214285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="188.7142857142858" y1="250.3214285714285" y2="250.3214285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="188.7142857142858" y1="273.0714285714285" y2="273.0714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.714285714285779" x2="7.714285714285779" y1="250.3214285714285" y2="273.0714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="188.7142857142858" y1="250.3214285714285" y2="273.0714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="369.7142857142858" y1="250.3214285714285" y2="250.3214285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="369.7142857142858" y1="273.0714285714285" y2="273.0714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.7142857142858" x2="188.7142857142858" y1="250.3214285714285" y2="273.0714285714285"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.7142857142858" x2="369.7142857142858" y1="250.3214285714285" y2="273.0714285714285"/>
  <line fill="none" id="81" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="380.7142857142858" x2="380.7142857142858" y1="6.571428571428555" y2="1036.571428571429" zvalue="6"/>
  <line fill="none" id="79" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.714285714286461" x2="373.714285714286" y1="142.4419211855109" y2="142.4419211855109" zvalue="8"/>
  <line fill="none" id="78" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.714285714286461" x2="373.714285714286" y1="612.4419211855109" y2="612.4419211855109" zvalue="9"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.714285714285779" x2="96.71428571428578" y1="927.5714285714287" y2="927.5714285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.714285714285779" x2="96.71428571428578" y1="966.7347285714286" y2="966.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.714285714285779" x2="6.714285714285779" y1="927.5714285714287" y2="966.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="96.71428571428578" y1="927.5714285714287" y2="966.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="366.7142857142858" y1="927.5714285714287" y2="927.5714285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="366.7142857142858" y1="966.7347285714286" y2="966.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="96.71428571428578" y1="927.5714285714287" y2="966.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366.7142857142858" x2="366.7142857142858" y1="927.5714285714287" y2="966.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.714285714285779" x2="96.71428571428578" y1="966.7346985714287" y2="966.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.714285714285779" x2="96.71428571428578" y1="994.6530985714286" y2="994.6530985714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.714285714285779" x2="6.714285714285779" y1="966.7346985714287" y2="994.6530985714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="96.71428571428578" y1="966.7346985714287" y2="994.6530985714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="186.7142857142858" y1="966.7346985714287" y2="966.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="186.7142857142858" y1="994.6530985714286" y2="994.6530985714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="96.71428571428578" y1="966.7346985714287" y2="994.6530985714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.7142857142858" x2="186.7142857142858" y1="966.7346985714287" y2="994.6530985714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.7142857142859" x2="276.7142857142859" y1="966.7346985714287" y2="966.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.7142857142859" x2="276.7142857142859" y1="994.6530985714286" y2="994.6530985714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.7142857142859" x2="186.7142857142859" y1="966.7346985714287" y2="994.6530985714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.7142857142859" x2="276.7142857142859" y1="966.7346985714287" y2="994.6530985714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.7142857142858" x2="366.7142857142858" y1="966.7346985714287" y2="966.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.7142857142858" x2="366.7142857142858" y1="994.6530985714286" y2="994.6530985714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.7142857142858" x2="276.7142857142858" y1="966.7346985714287" y2="994.6530985714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366.7142857142858" x2="366.7142857142858" y1="966.7346985714287" y2="994.6530985714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.714285714285779" x2="96.71428571428578" y1="994.6530285714286" y2="994.6530285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.714285714285779" x2="96.71428571428578" y1="1022.571428571429" y2="1022.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6.714285714285779" x2="6.714285714285779" y1="994.6530285714286" y2="1022.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="96.71428571428578" y1="994.6530285714286" y2="1022.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="186.7142857142858" y1="994.6530285714286" y2="994.6530285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="186.7142857142858" y1="1022.571428571429" y2="1022.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96.71428571428578" x2="96.71428571428578" y1="994.6530285714286" y2="1022.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.7142857142858" x2="186.7142857142858" y1="994.6530285714286" y2="1022.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.7142857142859" x2="276.7142857142859" y1="994.6530285714286" y2="994.6530285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.7142857142859" x2="276.7142857142859" y1="1022.571428571429" y2="1022.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.7142857142859" x2="186.7142857142859" y1="994.6530285714286" y2="1022.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.7142857142859" x2="276.7142857142859" y1="994.6530285714286" y2="1022.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.7142857142858" x2="366.7142857142858" y1="994.6530285714286" y2="994.6530285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.7142857142858" x2="366.7142857142858" y1="1022.571428571429" y2="1022.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.7142857142858" x2="276.7142857142858" y1="994.6530285714286" y2="1022.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366.7142857142858" x2="366.7142857142858" y1="994.6530285714286" y2="1022.571428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.7143,947.571) scale(1,1) translate(0,0)" writing-mode="lr" x="51.71" xml:space="preserve" y="953.5700000000001" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.7143,981.571) scale(1,1) translate(0,0)" writing-mode="lr" x="48.71" xml:space="preserve" y="987.5700000000001" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,230.714,981.571) scale(1,1) translate(0,0)" writing-mode="lr" x="230.71" xml:space="preserve" y="987.5700000000001" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.7143,1009.57) scale(1,1) translate(0,0)" writing-mode="lr" x="47.71" xml:space="preserve" y="1015.57" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229.714,1009.57) scale(1,1) translate(0,0)" writing-mode="lr" x="229.71" xml:space="preserve" y="1015.57" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" x="134.71875" xml:space="preserve" y="449.578125" zvalue="17">110kV  母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="134.71875" xml:space="preserve" y="465.578125" zvalue="17">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,72.2143,642.071) scale(1,1) translate(0,0)" writing-mode="lr" x="72.21428571428578" xml:space="preserve" y="646.5714285714284" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,205.113,309.413) scale(1,1) translate(0,0)" writing-mode="lr" x="205.11" xml:space="preserve" y="313.91" zvalue="20">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,310.113,309.413) scale(1,1) translate(0,0)" writing-mode="lr" x="310.11" xml:space="preserve" y="313.91" zvalue="21">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.7143,485.071) scale(1,1) translate(0,0)" writing-mode="lr" x="83.71428571428578" xml:space="preserve" y="489.5714285714286" zvalue="22">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.7143,510.571) scale(1,1) translate(0,0)" writing-mode="lr" x="83.71428571428578" xml:space="preserve" y="515.0714285714287" zvalue="23">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.7143,536.071) scale(1,1) translate(0,0)" writing-mode="lr" x="83.71428571428578" xml:space="preserve" y="540.5714285714286" zvalue="24">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82.7143,560.571) scale(1,1) translate(0,0)" writing-mode="lr" x="82.71428571428578" xml:space="preserve" y="565.0714285714286" zvalue="25">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.7143,587.071) scale(1,1) translate(0,0)" writing-mode="lr" x="83.71428571428578" xml:space="preserve" y="591.5714285714284" zvalue="26">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231.768,949.571) scale(1,1) translate(0,0)" writing-mode="lr" x="231.77" xml:space="preserve" y="955.5700000000001" zvalue="27">LLCGF-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.7143,168.571) scale(1,1) translate(0,0)" writing-mode="lr" x="45.71" xml:space="preserve" y="174.07" zvalue="28">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225.714,168.571) scale(1,1) translate(0,0)" writing-mode="lr" x="225.71" xml:space="preserve" y="174.07" zvalue="29">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,517.089,533.071) scale(1,1) translate(0,0)" writing-mode="lr" x="517.09" xml:space="preserve" y="537.5700000000001" zvalue="32">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,822.464,815.983) scale(1,1) translate(0,0)" writing-mode="lr" x="822.46" xml:space="preserve" y="820.48" zvalue="33">10kV#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1060.71,812.946) scale(1,1) translate(0,0)" writing-mode="lr" x="1060.71" xml:space="preserve" y="817.45" zvalue="35">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,630.839,674.321) scale(1,1) translate(0,0)" writing-mode="lr" x="630.84" xml:space="preserve" y="678.8200000000001" zvalue="38">013</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,851.25,485.571) scale(1,1) translate(0,0)" writing-mode="lr" x="851.25" xml:space="preserve" y="490.07" zvalue="41">011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,850.839,643.071) scale(1,1) translate(0,0)" writing-mode="lr" x="850.84" xml:space="preserve" y="647.5700000000001" zvalue="43">0121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1021.84,648.071) scale(1,1) translate(0,5.68054e-13)" writing-mode="lr" x="1021.84" xml:space="preserve" y="652.5700000000001" zvalue="48">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,669.714,739.634) scale(1,1) translate(0,0)" writing-mode="lr" x="669.71" xml:space="preserve" y="744.13" zvalue="56">01367</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1449.46,254.196) scale(1,1) translate(0,0)" writing-mode="lr" x="1449.46" xml:space="preserve" y="258.7" zvalue="60">#2箱变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1723.21,259.196) scale(1,1) translate(0,0)" writing-mode="lr" x="1723.21" xml:space="preserve" y="263.7" zvalue="62">#1箱变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,723.214,370.946) scale(1,1) translate(0,0)" writing-mode="lr" x="723.21" xml:space="preserve" y="375.45" zvalue="85">10kV来连村光伏电站线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,999.571,1013.93) scale(1,1) translate(0,-2.21965e-13)" writing-mode="lr" x="999.5700000000001" xml:space="preserve" y="1018.43" zvalue="87">10kV光伏集成线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" x="1475.6640625" xml:space="preserve" y="489.5424107142858" zvalue="89">#2光阵(阳塘</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1475.6640625" xml:space="preserve" y="505.5424107142858" zvalue="89">村)0.357MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1750.68,495.786) scale(1,1) translate(1.88195e-13,0)" writing-mode="lr" x="1750.678571428572" xml:space="preserve" y="500.2857142857142" zvalue="91">#1光阵(芒杏村)0.5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1531.5,446.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1531.5" xml:space="preserve" y="451" zvalue="94">400kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1475.5,447.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1475.5" xml:space="preserve" y="452" zvalue="95">0.38kV</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1811.5,447.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1811.5" xml:space="preserve" y="452" zvalue="97">500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1754.5,448.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1754.5" xml:space="preserve" y="453" zvalue="99">0.38kV</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,824,280) scale(1,1) translate(0,0)" writing-mode="lr" x="824" xml:space="preserve" y="284.5" zvalue="111">10kV河丝线小勐武支线T来连村光伏电站线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,854.5,322.5) scale(1,1) translate(0,0)" writing-mode="lr" x="854.5" xml:space="preserve" y="327" zvalue="111">#8杆</text>
 </g>
 <g id="ButtonClass">
  <g href="500kV德宏变_全站公用.svg"><rect fill-opacity="0" height="24" width="97" x="38.71" y="297.57" zvalue="10"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="48">
   <path class="kv10" d="M 508.21 555.32 L 1259.46 555.32" stroke-width="6" zvalue="30"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674258059268" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674258059268"/></metadata>
  <path d="M 508.21 555.32 L 1259.46 555.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="47">
   <use class="kv10" height="30" transform="rotate(0,822.589,768.233) scale(2.63393,2.65241) translate(-487.409,-453.811)" width="28" x="785.7142857142859" xlink:href="#EnergyConsumer:站用变DY接地_0" y="728.4464285714287" zvalue="31"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450098954245" ObjectName="10kV#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,822.589,768.233) scale(2.63393,2.65241) translate(-487.409,-453.811)" width="28" x="785.7142857142859" y="728.4464285714287"/></g>
  <g id="98">
   <use class="kv10" height="30" transform="rotate(0,823,315) scale(1.25,1.23333) translate(-163.1,-56.0946)" width="12" x="815.5" xlink:href="#EnergyConsumer:负荷_0" y="296.5" zvalue="110"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450388951045" ObjectName="10kV河丝线小勐武支线T来连村光伏电站线"/>
   <cge:TPSR_Ref TObjectID="6192450388951045"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,823,315) scale(1.25,1.23333) translate(-163.1,-56.0946)" width="12" x="815.5" y="296.5"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="45">
   <use class="kv10" height="42" transform="rotate(0,1060.71,772.821) scale(1.25,-1.25) translate(-208.393,-1385.83)" width="30" x="1041.964285714286" xlink:href="#Accessory:4卷PT带容断器_0" y="746.5714285714287" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450098888709" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1060.71,772.821) scale(1.25,-1.25) translate(-208.393,-1385.83)" width="30" x="1041.964285714286" y="746.5714285714287"/></g>
  <g id="63">
   <use class="kv10" height="26" transform="rotate(0,549.464,791.571) scale(1.25,1.25) translate(-108.393,-155.064)" width="12" x="541.9642857142859" xlink:href="#Accessory:避雷器1_0" y="775.3214285714287" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450098495493" ObjectName="013侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,549.464,791.571) scale(1.25,1.25) translate(-108.393,-155.064)" width="12" x="541.9642857142859" y="775.3214285714287"/></g>
  <g id="65">
   <use class="kv10" height="26" transform="rotate(0,780.714,412.821) scale(1.25,-1.25) translate(-154.643,-739.829)" width="12" x="773.2142857142859" xlink:href="#Accessory:避雷器1_0" y="396.5714285714286" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450098429957" ObjectName="011侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,780.714,412.821) scale(1.25,-1.25) translate(-154.643,-739.829)" width="12" x="773.2142857142859" y="396.5714285714286"/></g>
 </g>
 <g id="BreakerClass">
  <g id="44">
   <use class="kv10" height="20" transform="rotate(0,601.964,675.321) scale(2.75,2.75) translate(-374.318,-412.25)" width="10" x="588.2142857142859" xlink:href="#Breaker:小车断路器_0" y="647.8214285714287" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924567957508" ObjectName="013"/>
   <cge:TPSR_Ref TObjectID="6473924567957508"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,601.964,675.321) scale(2.75,2.75) translate(-374.318,-412.25)" width="10" x="588.2142857142859" y="647.8214285714287"/></g>
  <g id="40">
   <use class="kv10" height="20" transform="rotate(0,823,486.571) scale(2.75,2.75) translate(-514.977,-292.136)" width="10" x="809.25" xlink:href="#Breaker:小车断路器_0" y="459.0714285714286" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924567891972" ObjectName="011"/>
   <cge:TPSR_Ref TObjectID="6473924567891972"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,823,486.571) scale(2.75,2.75) translate(-514.977,-292.136)" width="10" x="809.25" y="459.0714285714286"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="41">
   <path class="kv10" d="M 601.96 649.88 L 601.96 555.32" stroke-width="1" zvalue="37"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="48@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 601.96 649.88 L 601.96 555.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv10" d="M 823 511.32 L 823 555.32" stroke-width="1" zvalue="40"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@1" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 823 511.32 L 823 555.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv10" d="M 822.83 730.03 L 822.83 664.45" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="43@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 822.83 730.03 L 822.83 664.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv10" d="M 823.21 624.07 L 823.21 555.32" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@1" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 823.21 624.07 L 823.21 555.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv10" d="M 1048.81 747.35 L 1048.81 665.28" stroke-width="1" zvalue="47"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="46@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1048.81 747.35 L 1048.81 665.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv10" d="M 1049.47 632.92 L 1049.47 555.32" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1049.47 632.92 L 1049.47 555.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv10" d="M 1402.29 438.78 L 1418.21 438.78 L 1418.21 521.57 L 1661.96 521.57 L 1661.96 439.2 L 1681.04 439.2" stroke-width="1" zvalue="63"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24" LinkObjectIDznd="71@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1402.29 438.78 L 1418.21 438.78 L 1418.21 521.57 L 1661.96 521.57 L 1661.96 439.2 L 1681.04 439.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv10" d="M 1402.29 439.2 L 1402.29 991.57 L 601.96 991.57 L 601.96 700.07" stroke-width="1" zvalue="80"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="44@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1402.29 439.2 L 1402.29 991.57 L 601.96 991.57 L 601.96 700.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv10" d="M 657.53 757.82 L 601.96 757.82" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="24" MaxPinNum="2"/>
   </metadata>
  <path d="M 657.53 757.82 L 601.96 757.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv10" d="M 549.51 776.11 L 549.51 756.57 L 601.96 756.57" stroke-width="1" zvalue="82"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="24" MaxPinNum="2"/>
   </metadata>
  <path d="M 549.51 776.11 L 549.51 756.57 L 601.96 756.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv10" d="M 780.76 428.28 L 780.76 441.1 L 823 441.1" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="88" MaxPinNum="2"/>
   </metadata>
  <path d="M 780.76 428.28 L 780.76 441.1 L 823 441.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv10" d="M 823 331.65 L 823 461.13" stroke-width="1" zvalue="109"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="40@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 823 331.65 L 823 461.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="43">
   <use class="kv10" height="33" transform="rotate(0,823.214,644.071) scale(1.25,-1.25) translate(-162.893,-1155.2)" width="14" x="814.464285716217" xlink:href="#Disconnector:手车隔离开关13_0" y="623.4464285714286" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450098823173" ObjectName="10kV#1站用变0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450098823173"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,823.214,644.071) scale(1.25,-1.25) translate(-162.893,-1155.2)" width="14" x="814.464285716217" y="623.4464285714286"/></g>
  <g id="46">
   <use class="kv10" height="26" transform="rotate(0,1049.46,649.071) scale(1.25,1.25) translate(-208.393,-126.564)" width="12" x="1041.964285714286" xlink:href="#Disconnector:小车隔刀熔断器_0" y="632.8214285714286" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450098757637" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450098757637"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1049.46,649.071) scale(1.25,1.25) translate(-208.393,-126.564)" width="12" x="1041.964285714286" y="632.8214285714286"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="61">
   <use class="kv10" height="20" transform="rotate(270,669.714,757.884) scale(1.25,1.25) translate(-132.693,-149.077)" width="10" x="663.4642857142859" xlink:href="#GroundDisconnector:地刀_0" y="745.3839285714287" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450098626565" ObjectName="01367"/>
   <cge:TPSR_Ref TObjectID="6192450098626565"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,669.714,757.884) scale(1.25,1.25) translate(-132.693,-149.077)" width="10" x="663.4642857142859" y="745.3839285714287"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="25">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="25" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,916.5,257.5) scale(1,1) translate(0,0)" writing-mode="lr" x="868.1" xml:space="preserve" y="261.86" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127063322630" ObjectName="GEN_PSUM"/>
   </metadata>
  </g>
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,909,317) scale(1,1) translate(0,0)" writing-mode="lr" x="865.88" xml:space="preserve" y="321.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128388722692" ObjectName="P"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,914,375) scale(1,1) translate(0,0)" writing-mode="lr" x="870.88" xml:space="preserve" y="379.36" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128388853764" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="8" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,916,347) scale(1,1) translate(0,0)" writing-mode="lr" x="872.88" xml:space="preserve" y="351.36" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128389050372" ObjectName="Ua"/>
   </metadata>
  </g>
 </g>
</svg>