<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549589639170" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:避雷器PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1754.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2334.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="Breaker:小车开关带避雷器_0" viewBox="0,0,20,26">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="10" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982326301579349" x2="9.982326301579349" y1="18.5" y2="24.08333333333334"/>
   <rect fill-opacity="0" height="12.42" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.08,12.13) scale(1,1) translate(0,0)" width="6.67" x="6.75" y="5.92"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.06661748931881" x2="10.06661748931881" y1="2.083333333333329" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.75" x2="9.916666666666666" y1="21.25" y2="21.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.7" x2="17.7" y1="12.75" y2="21.08333333333334"/>
   <path d="M 13.6321 23.6556 L 9.98274 25.9797 L 6.33333 23.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.75 2.40358 L 10.0648 0.181096 L 6.45119 2.40358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.6321 21.8223 L 9.98274 24.1463 L 6.33333 21.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.75 4.15358 L 10.0648 1.9311 L 6.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438356" x2="17.20890410958904" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.44863013698632" x2="17.92808219178084" y1="8.036096718478305" y2="8.036096718478305"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438357" x2="18.1678082191781" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.9691780821918" x2="18.40753424657536" y1="8.42469775474782" y2="8.42469775474782"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.48972602739728" x2="18.88698630136988" y1="8.813298791017345" y2="8.813298791017345"/>
   <rect fill-opacity="0" height="5.57" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,17.71,12.73) scale(1,-1) translate(0,-1045.27)" width="2.42" x="16.5" y="9.949999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438358" x2="17.68835616438358" y1="9.979101899825864" y2="8.813298791017306"/>
  </symbol>
  <symbol id="Breaker:小车开关带避雷器_1" viewBox="0,0,20,26">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="10" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.75" x2="9.916666666666666" y1="21.25" y2="21.25"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="12.42" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.08,12.13) scale(1,1) translate(0,0)" width="6.67" x="6.75" y="5.92"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.7" x2="17.7" y1="12.75" y2="21.08333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.06661748931881" x2="10.06661748931881" y1="0.3333333333333321" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982326301579349" x2="9.982326301579349" y1="18.5" y2="26.08333333333333"/>
   <path d="M 13.6321 23.6556 L 9.98274 25.9797 L 6.33333 23.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.75 2.40358 L 10.0648 0.181096 L 6.45119 2.40358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.9691780821918" x2="18.40753424657536" y1="8.42469775474782" y2="8.42469775474782"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.48972602739728" x2="18.88698630136988" y1="8.813298791017345" y2="8.813298791017345"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438357" x2="18.1678082191781" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.44863013698632" x2="17.92808219178084" y1="8.036096718478305" y2="8.036096718478305"/>
   <rect fill-opacity="0" height="5.57" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,17.71,12.73) scale(1,-1) translate(0,-1045.27)" width="2.42" x="16.5" y="9.949999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438356" x2="17.20890410958904" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438358" x2="17.68835616438358" y1="9.979101899825864" y2="8.813298791017306"/>
  </symbol>
  <symbol id="Breaker:小车开关带避雷器_2" viewBox="0,0,20,26">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="10" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333334" x2="13.08333333333333" y1="6.083333333333334" y2="18.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.25" x2="7" y1="6.166666666666669" y2="18"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.7" x2="17.7" y1="12.75" y2="21.08333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.75" x2="9.916666666666666" y1="21.25" y2="21.25"/>
   <rect fill-opacity="0" height="12.42" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.08,12.13) scale(1,1) translate(0,0)" width="6.67" x="6.75" y="5.92"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.06661748931881" x2="10.06661748931881" y1="2.083333333333329" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.982326301579349" x2="9.982326301579349" y1="18.5" y2="24.08333333333334"/>
   <path d="M 13.6321 23.6556 L 9.98274 25.9797 L 6.33333 23.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.75 2.40358 L 10.0648 0.181096 L 6.45119 2.40358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.6321 21.8223 L 9.98274 24.1463 L 6.33333 21.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 13.75 4.15358 L 10.0648 1.9311 L 6.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.9691780821918" x2="18.40753424657536" y1="8.42469775474782" y2="8.42469775474782"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.48972602739728" x2="18.88698630136988" y1="8.813298791017345" y2="8.813298791017345"/>
   <rect fill-opacity="0" height="5.57" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,17.71,12.73) scale(1,-1) translate(0,-1045.27)" width="2.42" x="16.5" y="9.949999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.44863013698632" x2="17.92808219178084" y1="8.036096718478305" y2="8.036096718478305"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438357" x2="18.1678082191781" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438356" x2="17.20890410958904" y1="12.40785837651035" y2="14.73946459412747"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.68835616438358" x2="17.68835616438358" y1="9.979101899825864" y2="8.813298791017306"/>
  </symbol>
  <symbol id="Accessory:四卷带壁雷器母线PT_0" viewBox="0,0,40,35">
   <use terminal-index="0" type="0" x="22.10905664884498" xlink:href="#terminal" y="34.51612485684674"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="28.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <rect fill-opacity="0" height="15.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,30,14.36) scale(1,-1) translate(0,-930.43)" width="7" x="26.5" y="6.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="16.27740325661302" y2="15.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="7.166666666666671" y1="16.27740325661302" y2="12.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="12.27740325661302" y2="13.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="30.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="22" y1="28.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="14" y1="21.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="30" y1="28.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="30" y1="28.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.92943360505483" x2="29.92943360505483" y1="6.610736589946352" y2="3.462520268614096"/>
   <ellipse cx="13.81" cy="17.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.95921744067709" x2="27.68240159599324" y1="3.3946117330996" y2="3.3946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.12588410734375" x2="28.51573492932657" y1="2.144611733099605" y2="2.144611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="30.54255077401042" x2="29.09906826265991" y1="0.8946117330996053" y2="0.8946117330996053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599325" x2="16.18240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="13.78240159599324" y1="15.23154965466559" y2="17.7066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="11.38240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <ellipse cx="20.23" cy="13.99" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.48" cy="10.41" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="17.79906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="20.19906826265991" y1="11.73154965466559" y2="14.2066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="22.59906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="8.148216321332258" y2="10.62329629003646"/>
   <ellipse cx="6.64" cy="14.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:双联刀闸2020_0" viewBox="0,0,32,30">
   <use terminal-index="0" type="0" x="26.06666666666666" xlink:href="#terminal" y="29.9"/>
   <use terminal-index="1" type="0" x="6.999999999999995" xlink:href="#terminal" y="29.68333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.633333333333331" x2="8.599999999999998" y1="7.08899551487831" y2="7.08899551487831"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="26.25" y1="12.08899551487831" y2="12.08899551487831"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.058173394349206" x2="7.058173394349206" y1="7.166666666666663" y2="3.499999999999996"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.966666666666665" x2="6.966666666666665" y1="29.91666666666666" y2="7.083333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.416666666666673" x2="7.583333333333339" y1="1.316666666666666" y2="1.316666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.883333333333336" x2="8.133333333333336" y1="2.30833333333333" y2="2.30833333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.083333333333339" x2="9.166666666666673" y1="3.416666666666664" y2="3.416666666666664"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.383333333333331" x2="8.349999999999998" y1="18.50546183093142" y2="18.50546183093142"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.88333333333333" x2="27.85" y1="7.08899551487831" y2="7.08899551487831"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.3081733943492" x2="26.3081733943492" y1="7.166666666666663" y2="3.499999999999996"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.21666666666667" x2="26.21666666666667" y1="29.91666666666666" y2="7.000000000000002"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="25.66666666666667" x2="26.83333333333334" y1="1.316666666666666" y2="1.316666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.33333333333334" x2="28.41666666666667" y1="3.416666666666664" y2="3.416666666666664"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="25.13333333333334" x2="27.38333333333334" y1="2.30833333333333" y2="2.30833333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.63333333333333" x2="27.6" y1="18.50546183093142" y2="18.50546183093142"/>
  </symbol>
  <symbol id="Disconnector:双联刀闸2020_1" viewBox="0,0,32,30">
   <use terminal-index="0" type="0" x="26.06666666666666" xlink:href="#terminal" y="29.9"/>
   <use terminal-index="1" type="0" x="6.999999999999995" xlink:href="#terminal" y="29.68333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.633333333333331" x2="8.599999999999998" y1="7.08899551487831" y2="7.08899551487831"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.916666666666666" x2="2.811509245842185" y1="17.41666666666666" y2="8.192441231336419"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.058173394349206" x2="7.058173394349206" y1="7.166666666666663" y2="3.499999999999996"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="23.75" y1="12.08899551487831" y2="12.08899551487831"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.866666666666664" x2="6.866666666666664" y1="29.91666666666666" y2="17.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.416666666666673" x2="7.583333333333339" y1="1.316666666666666" y2="1.316666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.083333333333339" x2="9.166666666666673" y1="3.416666666666664" y2="3.416666666666664"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.883333333333336" x2="8.133333333333336" y1="2.30833333333333" y2="2.30833333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.383333333333331" x2="8.349999999999998" y1="18.50546183093142" y2="18.50546183093142"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.88333333333333" x2="27.85" y1="7.08899551487831" y2="7.08899551487831"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.16666666666666" x2="22.06150924584218" y1="17.41666666666666" y2="8.192441231336419"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.3081733943492" x2="26.3081733943492" y1="7.166666666666663" y2="3.499999999999996"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.11666666666666" x2="26.11666666666666" y1="29.91666666666666" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.33333333333334" x2="28.41666666666667" y1="3.416666666666664" y2="3.416666666666664"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="25.66666666666667" x2="26.83333333333334" y1="1.316666666666666" y2="1.316666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="25.13333333333334" x2="27.38333333333334" y1="2.30833333333333" y2="2.30833333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.63333333333333" x2="27.6" y1="18.50546183093142" y2="18.50546183093142"/>
  </symbol>
  <symbol id="Disconnector:双联刀闸2020_2" viewBox="0,0,32,30">
   <use terminal-index="0" type="0" x="26.06666666666666" xlink:href="#terminal" y="29.9"/>
   <use terminal-index="1" type="0" x="6.999999999999995" xlink:href="#terminal" y="29.68333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.633333333333331" x2="8.599999999999998" y1="7.08899551487831" y2="7.08899551487831"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.058173394349206" x2="7.058173394349206" y1="7.166666666666663" y2="3.499999999999996"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="26" y1="12.83899551487831" y2="12.83899551487831"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.866666666666664" x2="6.866666666666664" y1="29.91666666666666" y2="17.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.416666666666673" x2="7.583333333333339" y1="1.316666666666666" y2="1.316666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.083333333333339" x2="9.166666666666673" y1="3.416666666666664" y2="3.416666666666664"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.883333333333336" x2="8.133333333333336" y1="2.30833333333333" y2="2.30833333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.383333333333331" x2="8.349999999999998" y1="18.50546183093142" y2="18.50546183093142"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.88333333333333" x2="27.85" y1="7.08899551487831" y2="7.08899551487831"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.41666666666666" x2="24.31150924584218" y1="17.41666666666666" y2="8.192441231336419"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.3081733943492" x2="26.3081733943492" y1="7.166666666666663" y2="3.499999999999996"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.11666666666666" x2="26.11666666666666" y1="29.91666666666666" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="25.66666666666667" x2="26.83333333333334" y1="1.316666666666666" y2="1.316666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="25.13333333333334" x2="27.38333333333334" y1="2.30833333333333" y2="2.30833333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.33333333333334" x2="28.41666666666667" y1="3.416666666666664" y2="3.416666666666664"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.63333333333333" x2="27.6" y1="18.50546183093142" y2="18.50546183093142"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.31150924584218" x2="28.41666666666666" y1="17.41666666666666" y2="8.192441231336419"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.916666666666666" x2="4.811509245842185" y1="17.41666666666666" y2="8.192441231336419"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.811509245842185" x2="8.916666666666666" y1="17.41666666666666" y2="8.192441231336419"/>
  </symbol>
  <symbol id="Compensator:南赛变电容器_0" viewBox="0,0,100,100">
   <use terminal-index="0" type="0" x="6.666666666666671" xlink:href="#terminal" y="46"/>
   <path d="M 34.5 32.3333 L 34.5 46.3333 L 48.5 46.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21" x2="7" y1="46.16666666666666" y2="46.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="96.66666666666669" x2="97.33333333333334" y1="46.16666666666667" y2="3.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="96.16666666666667" x2="90.16666666666667" y1="46.16666666666666" y2="46.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="58.74999999999999" x2="84.74999999999999" y1="69.58333333333334" y2="69.58333333333334"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,62.92,46.42) scale(1,1) translate(0,0)" width="4" x="60.92" y="42.42"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="89.96666666666667" x2="87.25" y1="62.33485477178424" y2="62.33485477178424"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="89.80000000000001" x2="89.80000000000001" y1="30.86666666666668" y2="62.50152143845091"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="59.46666666666666" x2="55.05925925925925" y1="58.36666666666667" y2="58.36666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="85.46666666666665" x2="85.46666666666665" y1="46.58333333333334" y2="58.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="87.08333333333334" x2="89.96666666666667" y1="30.70000000000002" y2="30.70000000000002"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="85.13333333333334" x2="90.08333333333334" y1="46.36666666666667" y2="46.36666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="54.96666666666665" x2="54.96666666666665" y1="58.41666666666669" y2="46.36666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="48.5" x2="76.46666666666668" y1="46.36666666666667" y2="46.36666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="81.02222222222223" x2="85.50370370370369" y1="58.44074074074074" y2="58.44074074074074"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="78.46666666666667" x2="85.50370370370371" y1="46.36666666666667" y2="46.36666666666667"/>
   <path d="M 66.6644 58.3667 A 5.92784 3.63493 -90 0 1 59.3945 58.3667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 73.9342 58.3667 A 5.92784 3.63493 -90 0 1 66.6644 58.3667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 81.0041 58.3667 A 5.92784 3.63493 -90 0 1 73.7342 58.3667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="78.46666666666665" x2="78.46666666666665" y1="43.36666666666667" y2="49.36666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="76.46666666666667" x2="76.46666666666667" y1="43.36666666666667" y2="49.36666666666667"/>
   <path d="M 34.7333 31.9167 A 13.6833 14.2119 -360 1 1 21.05 46.1286" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.49539340335359" x2="21.50690989496959" y1="98.22317156527681" y2="98.22317156527681"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="18.53565505804311" x2="18.53565505804311" y1="88.36352244246983" y2="93.89516974253819"/>
   <rect fill-opacity="0" height="24.59" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,18.5,76.07) scale(1,1) translate(0,0)" width="14.33" x="11.33" y="63.78"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.3062465450525" x2="24.24046434494196" y1="93.97738964636474" y2="93.97738964636474"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="18.53565505804311" x2="22.81426202321724" y1="79.14411027568923" y2="74.53440419229895"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.12861617836741" x2="22.41809471162705" y1="96.1002806058208" y2="96.1002806058208"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="18.53565505804311" x2="18.53565505804311" y1="46.16666666666666" y2="63.77842333105492"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="18.53565505804311" x2="18.53565505804311" y1="63.77842333105492" y2="78.99045340624288"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="18.53565505804311" x2="14.25704809286898" y1="79.14411027568923" y2="74.53440419229895"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.18199567497548" x2="97.16666666666664" y1="3.49833333333337" y2="3.49833333333337"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV南赛变" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="277.25" x="57.75" xlink:href="logo.png" y="36.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.375,66.75) scale(1,1) translate(0,0)" writing-mode="lr" x="196.38" xml:space="preserve" y="70.25" zvalue="53"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,191,63.9403) scale(1,1) translate(0,0)" writing-mode="lr" x="191" xml:space="preserve" y="72.94" zvalue="54">35kV南赛变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="217" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,87.625,177.25) scale(1,1) translate(0,0)" width="72.88" x="51.19" y="165.25" zvalue="339"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,87.625,177.25) scale(1,1) translate(0,0)" writing-mode="lr" x="87.63" xml:space="preserve" y="181.75" zvalue="339">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1596.75,613.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1596.75" xml:space="preserve" y="617.75" zvalue="45">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,933.397,541.167) scale(1,1) translate(0,0)" writing-mode="lr" x="933.4" xml:space="preserve" y="545.67" zvalue="47">001</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,987.833,449.917) scale(1,1) translate(0,0)" writing-mode="lr" x="987.83" xml:space="preserve" y="454.42" zvalue="49">1号主变12500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,935.208,366.167) scale(1,1) translate(-2.04892e-13,0)" writing-mode="lr" x="935.21" xml:space="preserve" y="370.67" zvalue="50">351</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,925.871,295.976) scale(1,1) translate(0,0)" writing-mode="lr" x="925.87" xml:space="preserve" y="300.48" zvalue="51">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,981.833,300.833) scale(1,1) translate(0,0)" writing-mode="lr" x="981.83" xml:space="preserve" y="305.33" zvalue="52">60</text>
  <line fill="none" id="24" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="390.5" x2="390.5" y1="3.5" y2="1033.5" zvalue="55"/>
  <line fill="none" id="22" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75000000000045" x2="384.75" y1="140.6204926140824" y2="140.6204926140824" zvalue="57"/>
  <line fill="none" id="21" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="666.8704926140824" y2="666.8704926140824" zvalue="58"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="926" y2="926"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="965.1632999999999" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="926" y2="926"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="965.1632999999999" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="926" y2="965.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="965.16327" y2="965.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="993.08167" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="965.16327" y2="993.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="993.0816" y2="993.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1021" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="993.0816" y2="1021"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="993.0816" y2="1021"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,946) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="952" zvalue="60">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.8889,978.889) scale(1,1) translate(0,0)" writing-mode="lr" x="52.89" xml:space="preserve" y="984.89" zvalue="61">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.889,978.889) scale(1,1) translate(0,0)" writing-mode="lr" x="234.89" xml:space="preserve" y="984.89" zvalue="62">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.8889,1006.89) scale(1,1) translate(0,0)" writing-mode="lr" x="51.89" xml:space="preserve" y="1012.89" zvalue="63">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.889,1006.89) scale(1,1) translate(0,0)" writing-mode="lr" x="233.89" xml:space="preserve" y="1012.89" zvalue="64">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,684.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="689" zvalue="66">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233,947.5) scale(1,1) translate(0,0)" writing-mode="lr" x="233" xml:space="preserve" y="952" zvalue="68">NanSai-01-2019</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,973.365,163.108) scale(1,1) translate(-2.15242e-13,0)" writing-mode="lr" x="973.37" xml:space="preserve" y="167.61" zvalue="82">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1018.13,126.897) scale(1,1) translate(4.48588e-13,0)" writing-mode="lr" x="1018.13" xml:space="preserve" y="131.4" zvalue="84">97</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,800.167,232.722) scale(1,1) translate(0,0)" writing-mode="lr" x="800.17" xml:space="preserve" y="237.22" zvalue="86">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,892.962,74.0159) scale(1,1) translate(0,0)" writing-mode="lr" x="892.96" xml:space="preserve" y="78.52" zvalue="91">35kV轩南线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,578.974,702.667) scale(1,1) translate(9.38138e-13,0)" writing-mode="lr" x="578.97" xml:space="preserve" y="707.17" zvalue="102">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,455.357,807.261) scale(1,1) translate(-4.80727e-13,0)" writing-mode="lr" x="455.36" xml:space="preserve" y="811.76" zvalue="107">05167</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,540.799,925.795) scale(1,1) translate(0,0)" writing-mode="lr" x="540.8" xml:space="preserve" y="930.29" zvalue="111">10kV帕底村线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,779.661,702.667) scale(1,1) translate(8.50704e-13,0)" writing-mode="lr" x="779.66" xml:space="preserve" y="707.17" zvalue="116">052</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,655.357,807.261) scale(1,1) translate(0,0)" writing-mode="lr" x="655.36" xml:space="preserve" y="811.76" zvalue="119">05267</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,743.5,924.667) scale(1,1) translate(0,0)" writing-mode="lr" x="743.5" xml:space="preserve" y="929.17" zvalue="123">10kV梁子街线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,943.5,928.667) scale(1,1) translate(0,0)" writing-mode="lr" x="943.5" xml:space="preserve" y="933.17" zvalue="135">备用3</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1115.5,928.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1115.5" xml:space="preserve" y="933.17" zvalue="145">备用4</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1341.81,702.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1341.81" xml:space="preserve" y="707.17" zvalue="150">0551</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1299.45,933.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1299.45" xml:space="preserve" y="937.75" zvalue="151">10kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1522.38,702.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1522.38" xml:space="preserve" y="707.17" zvalue="155">056</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1393.64,755.261) scale(1,1) translate(0,0)" writing-mode="lr" x="1393.64" xml:space="preserve" y="759.76" zvalue="159">05617</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1264.44,536.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1264.44" xml:space="preserve" y="541.25" zvalue="168">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1225.33,388.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1225.33" xml:space="preserve" y="392.83" zvalue="169">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1660.05,522.385) scale(1,1) translate(0,0)" writing-mode="lr" x="1660.05" xml:space="preserve" y="526.89" zvalue="176">10kV外接站用变</text>
  <rect fill="none" fill-opacity="0" height="510" id="110" stroke="rgb(255,255,255)" stroke-dasharray="2 2" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1647.14,307.286) scale(1,1) translate(-3.11497e-13,0)" width="488.57" x="1402.86" y="52.29" zvalue="191"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" x="1642.265625" xml:space="preserve" y="100.5330762988223" zvalue="192">外接站用变T接点：110kV帕底变电站/10kV煤厂线#N83杆T空发</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1642.265625" xml:space="preserve" y="117.5330762988223" zvalue="192">分支线#26杆T牛奶厂支线#N5杆T云南芒梁高速公路投资发展有限</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1642.265625" xml:space="preserve" y="134.5330762988223" zvalue="192">公司分支线#19杆T中冶交通建设集团有限公司分支线#3杆</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" x="1773.171875" xml:space="preserve" y="433.4090909090909" zvalue="193">10kV中冶交通建设集</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1773.171875" xml:space="preserve" y="449.4090909090909" zvalue="193">团有限公司变压器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1773.171875" xml:space="preserve" y="465.4090909090909" zvalue="193">（专）</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1457.9,779.318) scale(1,1) translate(2.56133e-12,0)" writing-mode="lr" x="1457.9" xml:space="preserve" y="783.8200000000001" zvalue="201">0566</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="178" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1498.21,936.523) scale(1,1) translate(0,0)" writing-mode="lr" x="1498.21" xml:space="preserve" y="941.02" zvalue="244">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1572.04,780.043) scale(1,1) translate(0,0)" writing-mode="lr" x="1572.04" xml:space="preserve" y="784.54" zvalue="245">05610</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="239" y2="239"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="265" y2="265"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="14" y1="239" y2="265"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="239" y2="265"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="239" y2="239"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="265" y2="265"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="239" y2="265"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376" x2="376" y1="239" y2="265"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="265" y2="265"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="289.25" y2="289.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="14" y1="265" y2="289.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="265" y2="289.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="265" y2="265"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="289.25" y2="289.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="265" y2="289.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376" x2="376" y1="265" y2="289.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="289.25" y2="289.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="312" y2="312"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="14" y1="289.25" y2="312"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="289.25" y2="312"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="289.25" y2="289.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="312" y2="312"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="289.25" y2="312"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376" x2="376" y1="289.25" y2="312"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="312" y2="312"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="334.75" y2="334.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="14" y1="312" y2="334.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="312" y2="334.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="312" y2="312"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="334.75" y2="334.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="312" y2="334.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376" x2="376" y1="312" y2="334.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="334.75" y2="334.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="195" y1="357.5" y2="357.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="14" x2="14" y1="334.75" y2="357.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="334.75" y2="357.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="334.75" y2="334.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="376" y1="357.5" y2="357.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="195" x2="195" y1="334.75" y2="357.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="376" x2="376" y1="334.75" y2="357.5"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,203.399,177.841) scale(1,1) translate(0,0)" writing-mode="lr" x="203.4" xml:space="preserve" y="182.34" zvalue="328">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,308.399,177.841) scale(1,1) translate(0,0)" writing-mode="lr" x="308.4" xml:space="preserve" y="182.34" zvalue="329">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="227" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,60.5,252) scale(1,1) translate(0,0)" writing-mode="lr" x="18" xml:space="preserve" y="256.5" zvalue="330">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="226" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,241,252) scale(1,1) translate(0,0)" writing-mode="lr" x="198.5" xml:space="preserve" y="256.5" zvalue="331">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63.6875,297.25) scale(1,1) translate(0,0)" writing-mode="lr" x="63.69" xml:space="preserve" y="301.75" zvalue="332">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="216" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,278) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="282.5" zvalue="340">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="214" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,278) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="282.5" zvalue="341">全站无功</text>
  <line fill="none" id="202" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00000000000045" x2="380" y1="408.8704926140824" y2="408.8704926140824" zvalue="350"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,144.889,978.889) scale(1,1) translate(0,0)" writing-mode="lr" x="144.89" xml:space="preserve" y="984.89" zvalue="353">杨立超</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="51.19" y="165.25" zvalue="339"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="43">
   <path class="kv10" d="M 477.25 614.25 L 1559.75 614.25" stroke-width="6" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674248491012" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674248491012"/></metadata>
  <path d="M 477.25 614.25 L 1559.75 614.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv10" d="M 1543.88 173 L 1764.5 173" stroke-width="6" zvalue="171"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674248556549" ObjectName="10kV外接站用变母线"/>
   <cge:TPSR_Ref TObjectID="9288674248556549"/></metadata>
  <path d="M 1543.88 173 L 1764.5 173" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="45">
   <use class="kv10" height="20" transform="rotate(0,903.167,539.667) scale(1.83333,1.83333) translate(-406.364,-236.97)" width="10" x="894" xlink:href="#Breaker:小车断路器_0" y="521.3333333333334" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924531453957" ObjectName="1号主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924531453957"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,903.167,539.667) scale(1.83333,1.83333) translate(-406.364,-236.97)" width="10" x="894" y="521.3333333333334"/></g>
  <g id="49">
   <use class="kv35" height="20" transform="rotate(0,901.833,368) scale(1.25,1.125) translate(-179.117,-39.6389)" width="10" x="895.5833333333334" xlink:href="#Breaker:开关_0" y="356.75" zvalue="49"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924531519493" ObjectName="1号主变35kV侧351断路器"/>
   <cge:TPSR_Ref TObjectID="6473924531519493"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,901.833,368) scale(1.25,1.125) translate(-179.117,-39.6389)" width="10" x="895.5833333333334" y="356.75"/></g>
  <g id="61">
   <use class="kv10" height="20" transform="rotate(0,544.167,703.667) scale(1.83333,1.83333) translate(-243.182,-311.515)" width="10" x="535" xlink:href="#Breaker:小车断路器_0" y="685.3333333333334" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924531585029" ObjectName="10kV帕底村线051断路器"/>
   <cge:TPSR_Ref TObjectID="6473924531585029"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,544.167,703.667) scale(1.83333,1.83333) translate(-243.182,-311.515)" width="10" x="535" y="685.3333333333334"/></g>
  <g id="81">
   <use class="kv10" height="20" transform="rotate(0,744.167,703.667) scale(1.83333,1.83333) translate(-334.091,-311.515)" width="10" x="735.0000000000001" xlink:href="#Breaker:小车断路器_0" y="685.3333333333334" zvalue="115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924531650565" ObjectName="10kV梁子街线052断路器"/>
   <cge:TPSR_Ref TObjectID="6473924531650565"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,744.167,703.667) scale(1.83333,1.83333) translate(-334.091,-311.515)" width="10" x="735.0000000000001" y="685.3333333333334"/></g>
  <g id="92">
   <use class="kv10" height="20" transform="rotate(0,940.167,703.667) scale(1.83333,1.83333) translate(-423.182,-311.515)" width="10" x="931" xlink:href="#Breaker:小车断路器_0" y="685.3333333333334" zvalue="127"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924531716101" ObjectName="备用3断路器"/>
   <cge:TPSR_Ref TObjectID="6473924531716101"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,940.167,703.667) scale(1.83333,1.83333) translate(-423.182,-311.515)" width="10" x="931" y="685.3333333333334"/></g>
  <g id="101">
   <use class="kv10" height="20" transform="rotate(0,1112.17,703.667) scale(1.83333,1.83333) translate(-501.364,-311.515)" width="10" x="1103" xlink:href="#Breaker:小车断路器_0" y="685.3333333333334" zvalue="139"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924531781637" ObjectName="备用4断路器"/>
   <cge:TPSR_Ref TObjectID="6473924531781637"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1112.17,703.667) scale(1.83333,1.83333) translate(-501.364,-311.515)" width="10" x="1103" y="685.3333333333334"/></g>
  <g id="102">
   <use class="kv10" height="20" transform="rotate(0,1300.17,703.667) scale(1.83333,1.83333) translate(-586.818,-311.515)" width="10" x="1291" xlink:href="#Breaker:小车断路器_0" y="685.3333333333334" zvalue="149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924531847173" ObjectName="10kV1号站用变0551断路器"/>
   <cge:TPSR_Ref TObjectID="6473924531847173"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1300.17,703.667) scale(1.83333,1.83333) translate(-586.818,-311.515)" width="10" x="1291" y="685.3333333333334"/></g>
  <g id="107">
   <use class="kv10" height="20" transform="rotate(0,1484.17,703.667) scale(1.83333,1.83333) translate(-670.455,-311.515)" width="10" x="1475" xlink:href="#Breaker:小车断路器_0" y="685.3333333333334" zvalue="154"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924531912709" ObjectName="10kV1号电容器056断路器"/>
   <cge:TPSR_Ref TObjectID="6473924531912709"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1484.17,703.667) scale(1.83333,1.83333) translate(-670.455,-311.515)" width="10" x="1475" y="685.3333333333334"/></g>
  <g id="83">
   <use class="kv10" height="26" transform="rotate(0,1227.79,538.472) scale(1.20994,1.61325) translate(-210.935,-196.719)" width="20" x="1215.694540449294" xlink:href="#Breaker:小车开关带避雷器_0" y="517.5" zvalue="167"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924531978245" ObjectName="10kV母线0901小车开关带避雷器"/>
   <cge:TPSR_Ref TObjectID="6473924531978245"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1227.79,538.472) scale(1.20994,1.61325) translate(-210.935,-196.719)" width="20" x="1215.694540449294" y="517.5"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="47">
   <g id="470">
    <use class="kv35" height="30" transform="rotate(0,903.318,442.583) scale(2.34598,2.36111) translate(-502.116,-234.72)" width="24" x="875.17" xlink:href="#PowerTransformer2:可调不带中性点_0" y="407.17" zvalue="48"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874444308483" ObjectName="35"/>
    </metadata>
   </g>
   <g id="471">
    <use class="kv10" height="30" transform="rotate(0,903.318,442.583) scale(2.34598,2.36111) translate(-502.116,-234.72)" width="24" x="875.17" xlink:href="#PowerTransformer2:可调不带中性点_1" y="407.17" zvalue="48"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874444374019" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399454425091" ObjectName="1号主变"/>
   <cge:TPSR_Ref TObjectID="6755399454425091"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,903.318,442.583) scale(2.34598,2.36111) translate(-502.116,-234.72)" width="24" x="875.17" y="407.17"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="51">
   <use class="kv35" height="30" transform="rotate(0,901.919,291.321) scale(0.983766,0.721429) translate(14.7614,108.312)" width="15" x="894.5411255411256" xlink:href="#Disconnector:刀闸_0" y="280.5" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449884913669" ObjectName="1号主变35kV侧3516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449884913669"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,901.919,291.321) scale(0.983766,0.721429) translate(14.7614,108.312)" width="15" x="894.5411255411256" y="280.5"/></g>
  <g id="29">
   <use class="kv35" height="30" transform="rotate(90,970.842,180.716) scale(0.914506,0.670638) translate(90.1193,83.8126)" width="15" x="963.9828227456445" xlink:href="#Disconnector:刀闸_0" y="170.6567745297819" zvalue="81"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449885175814" ObjectName="35kV轩南线3519隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449885175814"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,970.842,180.716) scale(0.914506,0.670638) translate(90.1193,83.8126)" width="15" x="963.9828227456445" y="170.6567745297819"/></g>
  <g id="252">
   <use class="kv10" height="30" transform="rotate(0,1660.81,308.913) scale(-1.42857,1.42857) translate(-2820.16,-86.2452)" width="15" x="1650.095238095238" xlink:href="#Disconnector:令克_0" y="287.484126984127" zvalue="177"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449887076357" ObjectName="令克"/>
   <cge:TPSR_Ref TObjectID="6192449887076357"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1660.81,308.913) scale(-1.42857,1.42857) translate(-2820.16,-86.2452)" width="15" x="1650.095238095238" y="287.484126984127"/></g>
  <g id="145">
   <use class="kv10" height="30" transform="rotate(0,1483.49,780.015) scale(0.983766,0.721429) translate(24.3583,297.015)" width="15" x="1476.113095238095" xlink:href="#Disconnector:刀闸_0" y="769.193181818182" zvalue="200"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449887141893" ObjectName="10kV1号电容器0566隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449887141893"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1483.49,780.015) scale(0.983766,0.721429) translate(24.3583,297.015)" width="15" x="1476.113095238095" y="769.193181818182"/></g>
  <g id="179">
   <use class="kv10" height="30" transform="rotate(0,1518.88,784.15) scale(0.75,0.75) translate(502.294,257.633)" width="32" x="1506.880769230769" xlink:href="#Disconnector:双联刀闸2020_0" y="772.8999999999996" zvalue="244"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449944551430" ObjectName="10kV1号电容器05610接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449944551430"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1518.88,784.15) scale(0.75,0.75) translate(502.294,257.633)" width="32" x="1506.880769230769" y="772.8999999999996"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="53">
   <use class="kv35" height="20" transform="rotate(90,985.375,324.771) scale(1.1875,-1.1875) translate(-154.648,-596.387)" width="10" x="979.4375" xlink:href="#GroundDisconnector:地刀_0" y="312.8958333333333" zvalue="51"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449885044741" ObjectName="1号主变35kV侧35160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449885044741"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,985.375,324.771) scale(1.1875,-1.1875) translate(-154.648,-596.387)" width="10" x="979.4375" y="312.8958333333333"/></g>
  <g id="31">
   <use class="kv35" height="20" transform="rotate(0,1014.75,149.105) scale(-1.01806,-1.01806) translate(-2011.41,-295.385)" width="10" x="1009.65873015873" xlink:href="#GroundDisconnector:地刀_0" y="138.9246031746032" zvalue="83"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449885306885" ObjectName="35kV轩南线35197接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449885306885"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1014.75,149.105) scale(-1.01806,-1.01806) translate(-2011.41,-295.385)" width="10" x="1009.65873015873" y="138.9246031746032"/></g>
  <g id="33">
   <use class="kv35" height="20" transform="rotate(270,797.806,257.403) scale(-1.06944,-1.06944) translate(-1543.46,-497.397)" width="10" x="792.4583333333335" xlink:href="#GroundDisconnector:地刀_0" y="246.7083333333334" zvalue="85"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449885437957" ObjectName="1号主变35kV侧35167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449885437957"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,797.806,257.403) scale(-1.06944,-1.06944) translate(-1543.46,-497.397)" width="10" x="792.4583333333335" y="246.7083333333334"/></g>
  <g id="65">
   <use class="kv10" height="20" transform="rotate(180,491.13,808.261) scale(-1.1875,-1.1875) translate(-903.775,-1487.03)" width="10" x="485.1924019607841" xlink:href="#GroundDisconnector:地刀_0" y="796.3860294117646" zvalue="106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449885765637" ObjectName="10kV帕底村线05167"/>
   <cge:TPSR_Ref TObjectID="6192449885765637"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,491.13,808.261) scale(-1.1875,-1.1875) translate(-903.775,-1487.03)" width="10" x="485.1924019607841" y="796.3860294117646"/></g>
  <g id="79">
   <use class="kv10" height="20" transform="rotate(180,691.13,808.261) scale(-1.1875,-1.1875) translate(-1272.2,-1487.03)" width="10" x="685.1924019607841" xlink:href="#GroundDisconnector:地刀_0" y="796.3860294117646" zvalue="118"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449886027781" ObjectName="10kV梁子街线05267"/>
   <cge:TPSR_Ref TObjectID="6192449886027781"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,691.13,808.261) scale(-1.1875,-1.1875) translate(-1272.2,-1487.03)" width="10" x="685.1924019607841" y="796.3860294117646"/></g>
  <g id="90">
   <use class="kv10" height="20" transform="rotate(180,887.899,807.492) scale(-1.1875,-1.1875) translate(-1634.67,-1485.61)" width="10" x="881.9616327300149" xlink:href="#GroundDisconnector:地刀_0" y="795.6167986425338" zvalue="130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449886289925" ObjectName="备用3接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449886289925"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,887.899,807.492) scale(-1.1875,-1.1875) translate(-1634.67,-1485.61)" width="10" x="881.9616327300149" y="795.6167986425338"/></g>
  <g id="99">
   <use class="kv10" height="20" transform="rotate(180,1059.13,808.261) scale(-1.1875,-1.1875) translate(-1950.09,-1487.03)" width="10" x="1053.192401960784" xlink:href="#GroundDisconnector:地刀_0" y="796.3860294117646" zvalue="141"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449886552069" ObjectName="备用4接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449886552069"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1059.13,808.261) scale(-1.1875,-1.1875) translate(-1950.09,-1487.03)" width="10" x="1053.192401960784" y="796.3860294117646"/></g>
  <g id="112">
   <use class="kv10" height="20" transform="rotate(180,1425.56,756.261) scale(-1.1875,-1.1875) translate(-2625.09,-1391.24)" width="10" x="1419.620973389355" xlink:href="#GroundDisconnector:地刀_0" y="744.3860294117646" zvalue="158"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449886814213" ObjectName="10kV1号电容器05617接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449886814213"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1425.56,756.261) scale(-1.1875,-1.1875) translate(-2625.09,-1391.24)" width="10" x="1419.620973389355" y="744.3860294117646"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="AccessoryClass">
  <g id="28">
   <use class="kv35" height="30" transform="rotate(90,1066.15,181.563) scale(1.39583,-1.33514) translate(-296.404,-312.522)" width="30" x="1045.214673913043" xlink:href="#Accessory:避雷器PT带熔断器_0" y="161.5353260869567" zvalue="79"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449885110277" ObjectName="35kV轩南线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1066.15,181.563) scale(1.39583,-1.33514) translate(-296.404,-312.522)" width="30" x="1045.214673913043" y="161.5353260869567"/></g>
  <g id="58">
   <use class="kv35" height="20" transform="rotate(0,860.091,221.159) scale(2,1.92654) translate(-420.045,-97.0973)" width="20" x="840.0909090909091" xlink:href="#Accessory:线路PT3_0" y="201.8935406698563" zvalue="97"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449885569030" ObjectName="35kV轩南线避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,860.091,221.159) scale(2,1.92654) translate(-420.045,-97.0973)" width="20" x="840.0909090909091" y="201.8935406698563"/></g>
  <g id="64">
   <use class="kv10" height="26" transform="rotate(0,580.749,804.131) scale(1.02674,1.08257) translate(-14.9632,-60.2567)" width="12" x="574.5882352941177" xlink:href="#Accessory:避雷器1_0" y="790.0575335397319" zvalue="104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449885634566" ObjectName="10kV帕底村线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,580.749,804.131) scale(1.02674,1.08257) translate(-14.9632,-60.2567)" width="12" x="574.5882352941177" y="790.0575335397319"/></g>
  <g id="80">
   <use class="kv10" height="20" transform="rotate(0,794.588,809.323) scale(2,1.92654) translate(-387.294,-379.965)" width="20" x="774.5882352941176" xlink:href="#Accessory:线路PT3_0" y="790.0575335397318" zvalue="117"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449886093317" ObjectName="10kV梁子街线避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,794.588,809.323) scale(2,1.92654) translate(-387.294,-379.965)" width="20" x="774.5882352941176" y="790.0575335397318"/></g>
  <g id="91">
   <use class="kv10" height="20" transform="rotate(0,990.588,809.323) scale(2,1.92654) translate(-485.294,-379.965)" width="20" x="970.5882352941176" xlink:href="#Accessory:线路PT3_0" y="790.0575335397318" zvalue="129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449886355461" ObjectName="备用3避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,990.588,809.323) scale(2,1.92654) translate(-485.294,-379.965)" width="20" x="970.5882352941176" y="790.0575335397318"/></g>
  <g id="100">
   <use class="kv10" height="20" transform="rotate(0,1162.59,809.323) scale(2,1.92654) translate(-571.294,-379.965)" width="20" x="1142.588235294118" xlink:href="#Accessory:线路PT3_0" y="790.0575335397318" zvalue="140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449886617605" ObjectName="备用4避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1162.59,809.323) scale(2,1.92654) translate(-571.294,-379.965)" width="20" x="1142.588235294118" y="790.0575335397318"/></g>
  <g id="115">
   <use class="kv10" height="35" transform="rotate(0,1225.33,443.139) scale(1.16667,1.16667) translate(-171.714,-60.3889)" width="40" x="1202" xlink:href="#Accessory:四卷带壁雷器母线PT_0" y="422.7222222222222" zvalue="168"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449886879749" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1225.33,443.139) scale(1.16667,1.16667) translate(-171.714,-60.3889)" width="40" x="1202" y="422.7222222222222"/></g>
  <g id="126">
   <use class="kv10" height="20" transform="rotate(90,1566.68,243.538) scale(-2.26241,2.17931) translate(-2246.54,-119.995)" width="20" x="1544.060025872279" xlink:href="#Accessory:线路PT3_0" y="221.7448338706612" zvalue="173"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449886945285" ObjectName="外接站用变避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1566.68,243.538) scale(-2.26241,2.17931) translate(-2246.54,-119.995)" width="20" x="1544.060025872279" y="221.7448338706612"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="35">
   <path class="kv10" d="M 903.17 556.17 L 903.17 614.25" stroke-width="1" zvalue="86"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@1" LinkObjectIDznd="43@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 903.17 556.17 L 903.17 614.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv10" d="M 903.32 475.75 L 903.32 522.71" stroke-width="1" zvalue="87"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@1" LinkObjectIDznd="45@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 903.32 475.75 L 903.32 522.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv35" d="M 901.92 378.74 L 901.92 409.71" stroke-width="1" zvalue="88"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@1" LinkObjectIDznd="47@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 901.92 378.74 L 901.92 409.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv35" d="M 901.98 301.96 L 901.98 357.24" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@1" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 901.98 301.96 L 901.98 357.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv35" d="M 902.68 131.26 L 902.68 280.86" stroke-width="1" zvalue="91"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@0" LinkObjectIDznd="51@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 902.68 131.26 L 902.68 280.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv35" d="M 808.23 257.46 L 902.68 257.46" stroke-width="1" zvalue="92"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="41" MaxPinNum="2"/>
   </metadata>
  <path d="M 808.23 257.46 L 902.68 257.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv35" d="M 980.57 180.8 L 1047.55 180.8" stroke-width="1" zvalue="93"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="29@0" LinkObjectIDznd="28@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 980.57 180.8 L 1047.55 180.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv35" d="M 1014.7 159.03 L 1014.7 180.8" stroke-width="1" zvalue="94"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31@0" LinkObjectIDznd="54" MaxPinNum="2"/>
   </metadata>
  <path d="M 1014.7 159.03 L 1014.7 180.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv35" d="M 960.95 180.77 L 902.68 180.77" stroke-width="1" zvalue="95"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="29@1" LinkObjectIDznd="41" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.95 180.77 L 902.68 180.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv35" d="M 973.8 324.83 L 901.98 324.83" stroke-width="1" zvalue="96"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="38" MaxPinNum="2"/>
   </metadata>
  <path d="M 973.8 324.83 L 901.98 324.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv35" d="M 902.68 180.77 L 860.09 180.77 L 860.09 204.3" stroke-width="1" zvalue="99"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41" LinkObjectIDznd="58@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 902.68 180.77 L 860.09 180.77 L 860.09 204.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv10" d="M 544.17 686.71 L 544.17 614.25" stroke-width="1" zvalue="109"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="43@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 544.17 686.71 L 544.17 614.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv10" d="M 544.17 720.17 L 544.17 871.12" stroke-width="1" zvalue="111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@1" LinkObjectIDznd="59@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 544.17 720.17 L 544.17 871.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv10" d="M 491.19 796.68 L 491.19 782 L 544.17 782" stroke-width="1" zvalue="112"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="67" MaxPinNum="2"/>
   </metadata>
  <path d="M 491.19 796.68 L 491.19 782 L 544.17 782" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv10" d="M 544.17 782 L 580.78 782 L 580.78 790.74" stroke-width="1" zvalue="113"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68" LinkObjectIDznd="64@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 544.17 782 L 580.78 782 L 580.78 790.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv10" d="M 744.17 686.71 L 744.17 614.25" stroke-width="1" zvalue="120"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@0" LinkObjectIDznd="43@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 744.17 686.71 L 744.17 614.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv10" d="M 744.17 720.17 L 744.17 871.12" stroke-width="1" zvalue="122"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="81@1" LinkObjectIDznd="77@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 744.17 720.17 L 744.17 871.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv10" d="M 691.19 796.68 L 691.19 782 L 744.17 782" stroke-width="1" zvalue="124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="76" MaxPinNum="2"/>
   </metadata>
  <path d="M 691.19 796.68 L 691.19 782 L 744.17 782" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 744.17 782 L 794.59 782 L 794.59 795.84" stroke-width="1" zvalue="125"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75" LinkObjectIDznd="80@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 744.17 782 L 794.59 782 L 794.59 795.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv10" d="M 940.17 686.71 L 940.17 614.25" stroke-width="1" zvalue="132"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="43@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 940.17 686.71 L 940.17 614.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="kv10" d="M 940.17 720.17 L 940.17 871.12" stroke-width="1" zvalue="134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@1" LinkObjectIDznd="88@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 940.17 720.17 L 940.17 871.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv10" d="M 887.96 795.91 L 887.96 782 L 940.17 782" stroke-width="1" zvalue="136"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="87" MaxPinNum="2"/>
   </metadata>
  <path d="M 887.96 795.91 L 887.96 782 L 940.17 782" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv10" d="M 940.17 782 L 990.59 782 L 990.59 795.84" stroke-width="1" zvalue="137"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86" LinkObjectIDznd="91@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 940.17 782 L 990.59 782 L 990.59 795.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv10" d="M 1112.17 686.71 L 1112.17 614.25" stroke-width="1" zvalue="142"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@0" LinkObjectIDznd="43@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1112.17 686.71 L 1112.17 614.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv10" d="M 1112.17 720.17 L 1112.17 871.12" stroke-width="1" zvalue="144"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@1" LinkObjectIDznd="97@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1112.17 720.17 L 1112.17 871.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv10" d="M 1059.19 796.68 L 1059.19 782 L 1112.17 782" stroke-width="1" zvalue="146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 1059.19 796.68 L 1059.19 782 L 1112.17 782" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv10" d="M 1112.17 782 L 1162.59 782 L 1162.59 795.84" stroke-width="1" zvalue="147"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95" LinkObjectIDznd="100@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1112.17 782 L 1162.59 782 L 1162.59 795.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv10" d="M 1300.17 686.71 L 1300.17 614.25" stroke-width="1" zvalue="151"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="43@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1300.17 686.71 L 1300.17 614.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv10" d="M 1300.17 720.17 L 1300.17 792.46" stroke-width="1" zvalue="152"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@1" LinkObjectIDznd="103@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1300.17 720.17 L 1300.17 792.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv10" d="M 1484.17 686.71 L 1484.17 614.25" stroke-width="1" zvalue="163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="43@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1484.17 686.71 L 1484.17 614.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv10" d="M 1227.79 559.44 L 1227.79 614.25" stroke-width="1" zvalue="169"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@1" LinkObjectIDznd="43@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1227.79 559.44 L 1227.79 614.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv10" d="M 1227.79 462.99 L 1227.79 517.5" stroke-width="1" zvalue="170"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="83@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1227.79 462.99 L 1227.79 517.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv10" d="M 1660.69 289.98 L 1660.69 173" stroke-width="1" zvalue="178"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="252@0" LinkObjectIDznd="125@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1660.69 289.98 L 1660.69 173" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv10" d="M 1660.93 326.41 L 1660.93 404.68" stroke-width="1" zvalue="179"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="252@1" LinkObjectIDznd="127@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1660.93 326.41 L 1660.93 404.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="kv10" d="M 1581.94 243.54 L 1660.69 243.54" stroke-width="1" zvalue="180"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="128" MaxPinNum="2"/>
   </metadata>
  <path d="M 1581.94 243.54 L 1660.69 243.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv10" d="M 1484.17 720.17 L 1484.17 769.55" stroke-width="1" zvalue="201"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@1" LinkObjectIDznd="145@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1484.17 720.17 L 1484.17 769.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv10" d="M 1425.62 744.68 L 1425.62 738 L 1484.17 738" stroke-width="1" zvalue="203"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="147" MaxPinNum="2"/>
   </metadata>
  <path d="M 1425.62 744.68 L 1425.62 738 L 1484.17 738" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="kv10" d="M 1483.55 790.65 L 1483.55 807.32" stroke-width="1" zvalue="242"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@1" LinkObjectIDznd="177@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1483.55 790.65 L 1483.55 807.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="181">
   <path class="kv10" d="M 1512.13 795.16 L 1483.55 795.16" stroke-width="1" zvalue="245"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@1" LinkObjectIDznd="176" MaxPinNum="2"/>
   </metadata>
  <path d="M 1512.13 795.16 L 1483.55 795.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv10" d="M 1526.43 795.32 L 1526.43 811.65" stroke-width="1" zvalue="246"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@0" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 1526.43 795.32 L 1526.43 811.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="59">
   <use class="kv10" height="30" transform="rotate(0,544.167,883.333) scale(1.42857,-0.822222) translate(-161.75,-1960.32)" width="7" x="539.1666666666667" xlink:href="#ACLineSegment:线路_0" y="871" zvalue="110"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449885831173" ObjectName="10kV帕底村线"/>
   <cge:TPSR_Ref TObjectID="6192449885831173_5066549589639170"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,544.167,883.333) scale(1.42857,-0.822222) translate(-161.75,-1960.32)" width="7" x="539.1666666666667" y="871"/></g>
  <g id="77">
   <use class="kv10" height="30" transform="rotate(0,744.167,883.333) scale(1.42857,-0.822222) translate(-221.75,-1960.32)" width="7" x="739.1666666666667" xlink:href="#ACLineSegment:线路_0" y="871" zvalue="121"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449885896709" ObjectName="10kV梁子街线"/>
   <cge:TPSR_Ref TObjectID="6192449885896709_5066549589639170"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,744.167,883.333) scale(1.42857,-0.822222) translate(-221.75,-1960.32)" width="7" x="739.1666666666667" y="871"/></g>
  <g id="88">
   <use class="kv10" height="30" transform="rotate(0,940.167,883.333) scale(1.42857,-0.822222) translate(-280.55,-1960.32)" width="7" x="935.1666666666667" xlink:href="#ACLineSegment:线路_0" y="871" zvalue="133"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449886158853" ObjectName="备用3"/>
   <cge:TPSR_Ref TObjectID="6192449886158853_5066549589639170"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,940.167,883.333) scale(1.42857,-0.822222) translate(-280.55,-1960.32)" width="7" x="935.1666666666667" y="871"/></g>
  <g id="97">
   <use class="kv10" height="30" transform="rotate(0,1112.17,883.333) scale(1.42857,-0.822222) translate(-332.15,-1960.32)" width="7" x="1107.166666666667" xlink:href="#ACLineSegment:线路_0" y="871" zvalue="143"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449886420997" ObjectName="备用4"/>
   <cge:TPSR_Ref TObjectID="6192449886420997_5066549589639170"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1112.17,883.333) scale(1.42857,-0.822222) translate(-332.15,-1960.32)" width="7" x="1107.166666666667" y="871"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="103">
   <use class="kv10" height="30" transform="rotate(0,1301.7,836.719) scale(2.63393,3.07292) translate(-784.619,-533.337)" width="28" x="1264.822916666667" xlink:href="#EnergyConsumer:站用变DY接地_0" y="790.625" zvalue="150"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449886683141" ObjectName="10kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1301.7,836.719) scale(2.63393,3.07292) translate(-784.619,-533.337)" width="28" x="1264.822916666667" y="790.625"/></g>
  <g id="127">
   <use class="kv10" height="30" transform="rotate(0,1662.14,448.941) scale(2.63393,3.07292) translate(-1008.22,-271.751)" width="28" x="1625.267361111111" xlink:href="#EnergyConsumer:站用变DY接地_0" y="402.8472222222222" zvalue="175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449887010821" ObjectName="10kV外接站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1662.14,448.941) scale(2.63393,3.07292) translate(-1008.22,-271.751)" width="28" x="1625.267361111111" y="402.8472222222222"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="132">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="132" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,531.397,953.389) scale(1,1) translate(0,0)" writing-mode="lr" x="530.9299999999999" xml:space="preserve" y="958.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125834588164" ObjectName="P"/>
   </metadata>
  </g>
  <g id="133">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="133" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,531.397,974.389) scale(1,1) translate(0,0)" writing-mode="lr" x="530.9299999999999" xml:space="preserve" y="979.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125834653700" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="134">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="134" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,531.397,997.167) scale(1,1) translate(0,0)" writing-mode="lr" x="530.9299999999999" xml:space="preserve" y="1001.94" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125834719236" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="135">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="135" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,740.167,955.167) scale(1,1) translate(-1.54358e-13,0)" writing-mode="lr" x="739.7" xml:space="preserve" y="959.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125835636742" ObjectName="P"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="136" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,740.167,976.167) scale(1,1) translate(-1.54358e-13,0)" writing-mode="lr" x="739.7" xml:space="preserve" y="980.9400000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125835702276" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="137" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,740.167,997.167) scale(1,1) translate(-1.54358e-13,0)" writing-mode="lr" x="739.7" xml:space="preserve" y="1001.94" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125835767812" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="138" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,821.962,109.873) scale(1,1) translate(8.77034e-14,0)" writing-mode="lr" x="821.63" xml:space="preserve" y="114.56" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125833015300" ObjectName="P"/>
   </metadata>
  </g>
  <g id="139">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="139" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,821.962,127.873) scale(1,1) translate(8.77034e-14,0)" writing-mode="lr" x="821.63" xml:space="preserve" y="132.56" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125833080836" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="140" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,823.962,149.159) scale(1,1) translate(8.79254e-14,0)" writing-mode="lr" x="823.63" xml:space="preserve" y="153.85" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125833146372" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="152">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="152" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,999.318,509.5) scale(1,1) translate(0,0)" writing-mode="lr" x="998.77" xml:space="preserve" y="514.28" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125830590468" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="153">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="153" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,999.318,534.5) scale(1,1) translate(0,0)" writing-mode="lr" x="998.77" xml:space="preserve" y="539.28" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125830656004" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="154">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="154" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,999.318,559.5) scale(1,1) translate(0,0)" writing-mode="lr" x="998.77" xml:space="preserve" y="564.28" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125831049220" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="183">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="183" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1490.02,959.4) scale(1,1) translate(0,0)" writing-mode="lr" x="1489.56" xml:space="preserve" y="964.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125843632132" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="184">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="184" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1492.02,984.4) scale(1,1) translate(0,0)" writing-mode="lr" x="1491.56" xml:space="preserve" y="989.17" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125843697668" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="223">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="223" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,160.611,252.167) scale(1,1) translate(0,0)" writing-mode="lr" x="160.77" xml:space="preserve" y="257.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125844746244" ObjectName="F"/>
   </metadata>
  </g>
  <g id="222">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="222" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,338.222,253.167) scale(1,1) translate(0,0)" writing-mode="lr" x="338.38" xml:space="preserve" y="258.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125844811780" ObjectName="F"/>
   </metadata>
  </g>
  <g id="213">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="213" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,160.611,277.167) scale(1,1) translate(0,0)" writing-mode="lr" x="160.77" xml:space="preserve" y="282.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125844615172" ObjectName="F"/>
   </metadata>
  </g>
  <g id="212">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="212" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,338.222,278.167) scale(1,1) translate(0,0)" writing-mode="lr" x="338.38" xml:space="preserve" y="283.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125844680708" ObjectName="F"/>
   </metadata>
  </g>
  <g id="201">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="201" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,160.611,297.5) scale(1,1) translate(0,0)" writing-mode="lr" x="160.77" xml:space="preserve" y="302.41" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125829804038" ObjectName="F"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="2" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,548.25,435.75) scale(1,1) translate(0,0)" writing-mode="lr" x="547.78" xml:space="preserve" y="440.53" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125829410820" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,548.25,463.25) scale(1,1) translate(0,0)" writing-mode="lr" x="547.78" xml:space="preserve" y="468.03" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125829476356" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="4" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,548.25,518.25) scale(1,1) translate(0,0)" writing-mode="lr" x="547.78" xml:space="preserve" y="523.03" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125829541894" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="5" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,548.25,573.25) scale(1,1) translate(0,0)" writing-mode="lr" x="547.78" xml:space="preserve" y="578.03" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125829607430" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="6" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,548.25,490.75) scale(1,1) translate(0,0)" writing-mode="lr" x="547.78" xml:space="preserve" y="495.53" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125829672964" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="7" prefix="Ubc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,548.25,545.75) scale(1,1) translate(0,0)" writing-mode="lr" x="547.78" xml:space="preserve" y="550.53" zvalue="1">Ubc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125829738500" ObjectName="Ubc"/>
   </metadata>
  </g>
 </g>
 <g id="CompensatorClass">
  <g id="177">
   <use class="kv10" height="100" transform="rotate(90,1479.52,851) scale(1.008,1.008) translate(-11.3422,-6.35397)" width="100" x="1429.119513486485" xlink:href="#Compensator:南赛变电容器_0" y="800.5999999999997" zvalue="243"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449887207429" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192449887207429"/></metadata>
  <rect fill="white" height="100" opacity="0" stroke="white" transform="rotate(90,1479.52,851) scale(1.008,1.008) translate(-11.3422,-6.35397)" width="100" x="1429.119513486485" y="800.5999999999997"/></g>
 </g>
 <g id="StateClass">
  <g id="219">
   <use height="30" transform="rotate(0,335.673,178.357) scale(0.708333,0.665547) translate(133.843,84.612)" width="30" x="325.05" xlink:href="#State:红绿圆(方形)_0" y="168.37" zvalue="337"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374888730627" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,335.673,178.357) scale(0.708333,0.665547) translate(133.843,84.612)" width="30" x="325.05" y="168.37"/></g>
  <g id="218">
   <use height="30" transform="rotate(0,240.048,178.357) scale(0.708333,0.665547) translate(94.4681,84.612)" width="30" x="229.42" xlink:href="#State:红绿圆(方形)_0" y="168.37" zvalue="338"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950522863620" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,240.048,178.357) scale(0.708333,0.665547) translate(94.4681,84.612)" width="30" x="229.42" y="168.37"/></g>
  <g id="830">
   <use height="30" transform="rotate(0,329.812,117.464) scale(1.27778,1.03333) translate(-59.1984,-3.28916)" width="90" x="272.31" xlink:href="#State:全站检修_0" y="101.96" zvalue="363"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549589639170" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.812,117.464) scale(1.27778,1.03333) translate(-59.1984,-3.28916)" width="90" x="272.31" y="101.96"/></g>
 </g>
</svg>