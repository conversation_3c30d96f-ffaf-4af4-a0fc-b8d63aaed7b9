<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549585313794" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":风力发电1_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="6.600000000000001"/>
   <path d="M 15.3143 15.2093 A 3.15741 3.175 -180 0 0 9.00048 15.3207" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.34 15.3 A 3.055 3.25 0 0 0 21.45 15.3" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.3" cy="15.3" fill-opacity="0" rx="8.800000000000001" ry="8.800000000000001" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:变电站_0" viewBox="0,0,60,30">
   <use terminal-index="0" type="0" x="55.04005740209271" xlink:href="#terminal" y="14.9795517105966"/>
   <rect fill="rgb(255,255,0)" fill-opacity="1" height="17.86" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" transform="rotate(0,29.58,14.93) scale(1,1) translate(0,0)" width="51.17" x="4" y="6"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV芒海电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="43.75" xlink:href="logo.png" y="49.25"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,178.375,79.25) scale(1,1) translate(0,0)" writing-mode="lr" x="178.37" xml:space="preserve" y="82.75" zvalue="31"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,180.083,78.9403) scale(1,1) translate(6.85563e-15,0)" writing-mode="lr" x="180.08" xml:space="preserve" y="87.94" zvalue="32">10kV芒海电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="69" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,90.375,191) scale(1,1) translate(0,0)" width="72.88" x="53.94" y="179" zvalue="76"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90.375,191) scale(1,1) translate(0,0)" writing-mode="lr" x="90.38" xml:space="preserve" y="195.5" zvalue="76">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1128.6,352.698) scale(1,1) translate(0,0)" writing-mode="lr" x="1128.6" xml:space="preserve" y="357.2" zvalue="4">A012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1007.78,136) scale(1,1) translate(0,0)" writing-mode="lr" x="1007.78" xml:space="preserve" y="140.5" zvalue="5">043</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1193.7,445.557) scale(1,1) translate(-2.54396e-13,0)" writing-mode="lr" x="1193.7" xml:space="preserve" y="450.06" zvalue="6">#1主变  630kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1007.27,769.799) scale(1,1) translate(0,5.91074e-13)" writing-mode="lr" x="1007.27" xml:space="preserve" y="774.3" zvalue="8">#1F  250KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1130.99,260.294) scale(1,1) translate(0,0)" writing-mode="lr" x="1130.99" xml:space="preserve" y="264.79" zvalue="11">A01</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1030.62,597.252) scale(1,1) translate(-2.26407e-13,1.95848e-13)" writing-mode="lr" x="1030.62" xml:space="preserve" y="601.75" zvalue="12">431</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1200.66,769.189) scale(1,1) translate(1.28562e-13,8.43727e-13)" writing-mode="lr" x="1200.66" xml:space="preserve" y="773.6900000000001" zvalue="14">#2F  250KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1220.69,592.221) scale(1,1) translate(5.36974e-13,-2.58901e-13)" writing-mode="lr" x="1220.69" xml:space="preserve" y="596.72" zvalue="16">432</text>
  <line fill="none" id="56" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.75" x2="377.75" y1="17.25000000000006" y2="1047.25" zvalue="33"/>
  <line fill="none" id="54" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.750000000000227" x2="370.7499999999998" y1="153.1204926140824" y2="153.1204926140824" zvalue="35"/>
  <line fill="none" id="52" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.750000000000227" x2="370.7499999999998" y1="623.1204926140825" y2="623.1204926140825" zvalue="37"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="938.2500000000002" y2="938.2500000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="977.4133000000002" y2="977.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="938.2500000000002" y2="977.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="938.2500000000002" y2="977.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="363.75" y1="938.2500000000002" y2="938.2500000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="363.75" y1="977.4133000000002" y2="977.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="938.2500000000002" y2="977.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="938.2500000000002" y2="977.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="977.4132700000002" y2="977.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1005.33167" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="977.4132700000002" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="977.4132700000002" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="977.4132700000002" y2="977.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1005.33167" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="977.4132700000002" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.75" x2="183.75" y1="977.4132700000002" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="977.4132700000002" y2="977.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1005.33167" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="183.7500000000001" y1="977.4132700000002" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.7500000000001" x2="273.7500000000001" y1="977.4132700000002" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="977.4132700000002" y2="977.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1005.33167" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="273.75" y1="977.4132700000002" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="977.4132700000002" y2="1005.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1005.3316" y2="1005.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1033.25" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1005.3316" y2="1005.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1033.25" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.75" x2="183.75" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1005.3316" y2="1005.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1033.25" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="183.7500000000001" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.7500000000001" x2="273.7500000000001" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1005.3316" y2="1005.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1033.25" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="273.75" y1="1005.3316" y2="1033.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="1005.3316" y2="1033.25"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.75,958.25) scale(1,1) translate(0,0)" writing-mode="lr" x="48.75" xml:space="preserve" y="964.25" zvalue="41">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.75,992.25) scale(1,1) translate(0,0)" writing-mode="lr" x="45.75" xml:space="preserve" y="998.25" zvalue="42">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.75,992.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.75" xml:space="preserve" y="998.25" zvalue="43">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.75,1020.25) scale(1,1) translate(0,0)" writing-mode="lr" x="44.75" xml:space="preserve" y="1026.25" zvalue="44">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.75,1020.25) scale(1,1) translate(0,0)" writing-mode="lr" x="226.75" xml:space="preserve" y="1026.25" zvalue="45">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.25,652.75) scale(1,1) translate(0,2.11081e-13)" writing-mode="lr" x="69.25" xml:space="preserve" y="657.2500000000001" zvalue="48">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,210.899,187.591) scale(1,1) translate(0,0)" writing-mode="lr" x="210.9" xml:space="preserve" y="192.09" zvalue="49">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,315.899,187.591) scale(1,1) translate(0,0)" writing-mode="lr" x="315.9" xml:space="preserve" y="192.09" zvalue="50">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.804,960.25) scale(1,1) translate(0,0)" writing-mode="lr" x="228.8" xml:space="preserve" y="966.25" zvalue="57">MangHai-01-2019</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="188.5" y1="253" y2="253"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="188.5" y1="279" y2="279"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="7.5" y1="253" y2="279"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="188.5" y1="253" y2="279"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="369.5" y1="253" y2="253"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="369.5" y1="279" y2="279"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="188.5" y1="253" y2="279"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.5" x2="369.5" y1="253" y2="279"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="188.5" y1="279" y2="279"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="188.5" y1="303.25" y2="303.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="7.5" y1="279" y2="303.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="188.5" y1="279" y2="303.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="369.5" y1="279" y2="279"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="369.5" y1="303.25" y2="303.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="188.5" y1="279" y2="303.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.5" x2="369.5" y1="279" y2="303.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="188.5" y1="303.25" y2="303.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="188.5" y1="326" y2="326"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="7.5" y1="303.25" y2="326"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="188.5" y1="303.25" y2="326"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="369.5" y1="303.25" y2="303.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="369.5" y1="326" y2="326"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="188.5" y1="303.25" y2="326"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.5" x2="369.5" y1="303.25" y2="326"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="188.5" y1="326" y2="326"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="188.5" y1="348.75" y2="348.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="7.5" y1="326" y2="348.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="188.5" y1="326" y2="348.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="369.5" y1="326" y2="326"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="369.5" y1="348.75" y2="348.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="188.5" y1="326" y2="348.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.5" x2="369.5" y1="326" y2="348.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="188.5" y1="348.75" y2="348.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="188.5" y1="371.5" y2="371.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="7.5" y1="348.75" y2="371.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="188.5" y1="348.75" y2="371.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="369.5" y1="348.75" y2="348.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="369.5" y1="371.5" y2="371.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="188.5" y1="348.75" y2="371.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.5" x2="369.5" y1="348.75" y2="371.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="188.5" y1="371.5" y2="371.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="188.5" y1="394.25" y2="394.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="7.5" y1="371.5" y2="394.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="188.5" y1="371.5" y2="394.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="369.5" y1="371.5" y2="371.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="369.5" y1="394.25" y2="394.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="188.5" y1="371.5" y2="394.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.5" x2="369.5" y1="371.5" y2="394.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="188.5" y1="394.25" y2="394.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="188.5" y1="417" y2="417"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7.5" x2="7.5" y1="394.25" y2="417"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="188.5" y1="394.25" y2="417"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="369.5" y1="394.25" y2="394.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="369.5" y1="417" y2="417"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188.5" x2="188.5" y1="394.25" y2="417"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369.5" x2="369.5" y1="394.25" y2="417"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,60.25,268.5) scale(1,1) translate(0,0)" writing-mode="lr" x="17.75" xml:space="preserve" y="273" zvalue="67">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235.75,268.5) scale(1,1) translate(0,0)" writing-mode="lr" x="193.25" xml:space="preserve" y="273" zvalue="68">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,54.25,294.5) scale(1,1) translate(0,0)" writing-mode="lr" x="11.75" xml:space="preserve" y="299" zvalue="70">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,234.75,294.5) scale(1,1) translate(0,0)" writing-mode="lr" x="192.25" xml:space="preserve" y="299" zvalue="71">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,42.9375,340) scale(1,1) translate(0,0)" writing-mode="lr" x="42.94" xml:space="preserve" y="344.5" zvalue="72">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,54.25,316.25) scale(1,1) translate(0,0)" writing-mode="lr" x="11.75" xml:space="preserve" y="320.75" zvalue="73">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,234.25,316.5) scale(1,1) translate(0,0)" writing-mode="lr" x="191.75" xml:space="preserve" y="321" zvalue="74">厂用电率</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="78" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,774.981,167.393) scale(1,1) translate(0,0)" writing-mode="lr" x="774.98" xml:space="preserve" y="174.89" zvalue="84">35kV芒海变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1192,140.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1192" xml:space="preserve" y="145" zvalue="86">10kV电站线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,136.75,1020.25) scale(1,1) translate(0,0)" writing-mode="lr" x="136.75" xml:space="preserve" y="1026.25" zvalue="88">杨立超</text>
 </g>
 <g id="ButtonClass">
  <g href="10kV芒海电站.svg"><rect fill-opacity="0" height="24" width="72.88" x="53.94" y="179" zvalue="76"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="186">
   <use class="kv10" height="30" transform="rotate(0,1089.43,350.698) scale(1.11111,0.814815) translate(-108.11,76.9264)" width="15" x="1081.095248267764" xlink:href="#Disconnector:刀闸_0" y="338.4761904761906" zvalue="3"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449765376006" ObjectName="#1主变10kV侧A012隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449765376006"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1089.43,350.698) scale(1.11111,0.814815) translate(-108.11,76.9264)" width="15" x="1081.095248267764" y="338.4761904761906"/></g>
 </g>
 <g id="BreakerClass">
  <g id="185">
   <use class="kv10" height="20" transform="rotate(90,1007.78,169.294) scale(2.14127,1.94661) translate(-531.43,-72.8591)" width="10" x="997.0772107629723" xlink:href="#Breaker:开关_0" y="149.8275613275613" zvalue="4"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924503994373" ObjectName="10kV电站线043断路器"/>
   <cge:TPSR_Ref TObjectID="6473924503994373"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1007.78,169.294) scale(2.14127,1.94661) translate(-531.43,-72.8591)" width="10" x="997.0772107629723" y="149.8275613275613"/></g>
  <g id="4">
   <use class="kv10" height="20" transform="rotate(180,1090.78,254.294) scale(-2.14127,-1.94661) translate(-1594.49,-375.462)" width="10" x="1080.077210762972" xlink:href="#Breaker:开关_0" y="234.8275613275613" zvalue="10"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924504059909" ObjectName="#1主变10kV侧A01断路器"/>
   <cge:TPSR_Ref TObjectID="6473924504059909"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1090.78,254.294) scale(-2.14127,-1.94661) translate(-1594.49,-375.462)" width="10" x="1080.077210762972" y="234.8275613275613"/></g>
  <g id="9">
   <use class="v400" height="20" transform="rotate(0,1004.63,598.407) scale(2.54071,2.54071) translate(-601.514,-347.473)" width="10" x="991.9277644991931" xlink:href="#Breaker:小车断路器_0" y="572.9999999999997" zvalue="11"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924504125445" ObjectName="#1发电机431断路器"/>
   <cge:TPSR_Ref TObjectID="6473924504125445"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1004.63,598.407) scale(2.54071,2.54071) translate(-601.514,-347.473)" width="10" x="991.9277644991931" y="572.9999999999997"/></g>
  <g id="15">
   <use class="v400" height="20" transform="rotate(0,1194.74,593.374) scale(2.53741,2.53741) translate(-716.202,-344.15)" width="10" x="1182.052746624175" xlink:href="#Breaker:小车断路器_0" y="568.0000000000002" zvalue="15"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924504190981" ObjectName="#2发电机432断路器"/>
   <cge:TPSR_Ref TObjectID="6473924504190981"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1194.74,593.374) scale(2.53741,2.53741) translate(-716.202,-344.15)" width="10" x="1182.052746624175" y="568.0000000000002"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="163">
   <g id="1630">
    <use class="kv10" height="50" transform="rotate(0,1089.43,441.557) scale(1.91667,1.96323) translate(-507.283,-192.562)" width="30" x="1060.68" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="392.48" zvalue="5"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874433036292" ObjectName="10"/>
    </metadata>
   </g>
   <g id="1631">
    <use class="v400" height="50" transform="rotate(0,1089.43,441.557) scale(1.91667,1.96323) translate(-507.283,-192.562)" width="30" x="1060.68" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="392.48" zvalue="5"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874433101828" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399448788996" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399448788996"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1089.43,441.557) scale(1.91667,1.96323) translate(-507.283,-192.562)" width="30" x="1060.68" y="392.48"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="17">
   <path class="kv10" d="M 989.19 169.44 L 872.29 169.44" stroke-width="1" zvalue="17"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@1" LinkObjectIDznd="77@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 989.19 169.44 L 872.29 169.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv10" d="M 1177.15 169 L 1026.41 169" stroke-width="1" zvalue="19"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="185@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1177.15 169 L 1026.41 169" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv10" d="M 1090.71 235.67 L 1090.71 169" stroke-width="1" zvalue="20"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="4@0" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 1090.71 235.67 L 1090.71 169" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv10" d="M 1090.93 272.88 L 1090.93 305.88 L 1089.53 305.88 L 1089.53 338.88" stroke-width="1" zvalue="22"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="4@1" LinkObjectIDznd="186@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1090.93 272.88 L 1090.93 305.88 L 1089.53 305.88 L 1089.53 338.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv10" d="M 1089.5 393.36 L 1089.5 362.71" stroke-width="1" zvalue="25"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@0" LinkObjectIDznd="186@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1089.5 393.36 L 1089.5 362.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="v400" d="M 1004.63 574.91 L 1004.63 536 L 1194.74 536 L 1194.74 569.9" stroke-width="1" zvalue="26"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="15@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004.63 574.91 L 1004.63 536 L 1194.74 536 L 1194.74 569.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="v400" d="M 1089.43 489.95 L 1089.43 536" stroke-width="1" zvalue="27"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@1" LinkObjectIDznd="26" MaxPinNum="2"/>
   </metadata>
  <path d="M 1089.43 489.95 L 1089.43 536" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="v400" d="M 1004.74 696.99 L 1004.63 621.27" stroke-width="1" zvalue="28"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="9@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004.74 696.99 L 1004.63 621.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="v400" d="M 1194.74 616.21 L 1194.74 697.6" stroke-width="1" zvalue="29"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15@1" LinkObjectIDznd="16@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1194.74 616.21 L 1194.74 697.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="AccessoryClass">
  <g id="77">
   <use class="kv10" height="30" transform="rotate(0,780.764,170.08) scale(3.65509,5.44363) translate(-487.501,-72.182)" width="60" x="671.111107720269" xlink:href="#Accessory:变电站_0" y="88.42594101849733" zvalue="83"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449765572614" ObjectName="35kV芒海变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,780.764,170.08) scale(3.65509,5.44363) translate(-487.501,-72.182)" width="60" x="671.111107720269" y="88.42594101849733"/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,343.25,186.983) scale(0.708333,0.665547) translate(136.963,88.9468)" width="30" x="332.63" xlink:href="#State:红绿圆(方形)_0" y="177" zvalue="90"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,343.25,186.983) scale(0.708333,0.665547) translate(136.963,88.9468)" width="30" x="332.63" y="177"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,247.625,186.983) scale(0.708333,0.665547) translate(97.5882,88.9468)" width="30" x="237" xlink:href="#State:红绿圆(方形)_0" y="177" zvalue="91"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,247.625,186.983) scale(0.708333,0.665547) translate(97.5882,88.9468)" width="30" x="237" y="177"/></g>
 </g>
</svg>