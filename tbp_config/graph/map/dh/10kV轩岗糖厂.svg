<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549588656130" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Breaker:潮流开关_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.516666666666667" xlink:href="#terminal" y="0.3499999999999996"/>
   <use terminal-index="1" type="0" x="7.516666666666667" xlink:href="#terminal" y="29.70000000000001"/>
   <rect fill-opacity="0" height="29.38" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,7.5,15.01) scale(1,1) translate(0,0)" width="14.5" x="0.25" y="0.32"/>
  </symbol>
  <symbol id="Breaker:潮流开关_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.516666666666667" xlink:href="#terminal" y="0.3499999999999996"/>
   <use terminal-index="1" type="0" x="7.516666666666667" xlink:href="#terminal" y="29.70000000000001"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="29.52" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,7.48,14.99) scale(1,1) translate(0,0)" width="14.55" x="0.2" y="0.23"/>
  </symbol>
  <symbol id="Breaker:潮流开关_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.516666666666667" xlink:href="#terminal" y="0.3499999999999996"/>
   <use terminal-index="1" type="0" x="7.516666666666667" xlink:href="#terminal" y="29.70000000000001"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.58333333333333" x2="0.583333333333333" y1="0.5" y2="29.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.583333333333333" x2="14.58333333333333" y1="0.5833333333333321" y2="29.58333333333333"/>
   <rect fill-opacity="0" height="29.38" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,7.5,15.01) scale(1,1) translate(0,0)" width="14.5" x="0.25" y="0.32"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.08333333333333" x2="9.833333333333332" y1="16.5" y2="18.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14" y1="9.75" y2="16.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="11" y1="11.58333333333333" y2="9.666666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="9.75" y1="11.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8" x2="15.08333333333333" y1="8.416666666666668" y2="25.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_1" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.96,13) scale(1,1) translate(0,0)" width="4.42" x="12.75" y="9"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="5.416666666666668" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_2" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="20" y1="6" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="10.08333333333333" y1="6.000000000000004" y2="25.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Compensator:并联电容器组_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="19.25" y2="28.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.33333333333334" x2="29" y1="24" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.25" x2="26.25" y1="17.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29" x2="22.58333333333333" y1="24" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="19.91666666666667" y1="2" y2="9.833333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="9.75" y1="2.249999999999996" y2="10.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1" x2="12" y1="24.25" y2="24.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.25" x2="14" y1="8.333333333333332" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="1" y1="15.25" y2="24.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.75" x2="11.5" y1="12.33333333333333" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.5" x2="23.5" y1="13.25" y2="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.4" x2="17.4" y1="19.35" y2="28.93333333333333"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV轩岗糖厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,166.975,96.7) scale(1,1) translate(8.01973e-15,-5.9508e-14)" writing-mode="lr" x="166.98" xml:space="preserve" y="101.2" zvalue="3"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="1" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,175.429,45.9622) scale(1,1) translate(0,2.65937e-15)" writing-mode="lr" x="175.43" xml:space="preserve" y="53.46" zvalue="4">    10kV轩岗糖厂</text>
  <line fill="none" id="29" stroke="rgb(0,39,45)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.15079365079418" x2="318.7383422889277" y1="79.77335209120957" y2="79.77335209120957" zvalue="5"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,236.875,64.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="236.88" xml:space="preserve" y="73.69" zvalue="164">10kV轩岗糖厂</text>
  <image height="60" id="3" preserveAspectRatio="xMidYMid slice" width="277.25" x="57.75" xlink:href="logo.png" y="36.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.375,66.75) scale(1,1) translate(0,0)" writing-mode="lr" x="196.38" xml:space="preserve" y="70.25" zvalue="165"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="4" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,191,63.9403) scale(1,1) translate(0,0)" writing-mode="lr" x="191" xml:space="preserve" y="72.94" zvalue="166">10kV轩岗糖厂</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="55" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,87.4375,325) scale(1,1) translate(0,0)" width="72.88" x="51" y="313" zvalue="172"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,87.4375,325) scale(1,1) translate(0,0)" writing-mode="lr" x="87.44" xml:space="preserve" y="329.5" zvalue="172">信号一览</text>
  <line fill="none" id="28" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="374.9285714285716" x2="374.9285714285716" y1="52.57279411764836" y2="1042.572794117648" zvalue="6"/>
  <line fill="none" id="26" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.40549597855158" x2="318.0167560321707" y1="172.2007400004169" y2="172.2007400004169" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.5" x2="112.9400000000001" y1="931.1355850567206" y2="931.1355850567206"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.5" x2="112.9400000000001" y1="982.4750850567207" y2="982.4750850567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.5" x2="41.5" y1="931.1355850567206" y2="982.4750850567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.9400000000001" x2="112.9400000000001" y1="931.1355850567206" y2="982.4750850567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.9405" x2="347.5005" y1="931.1355850567206" y2="931.1355850567206"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.9405" x2="347.5005" y1="982.4750850567207" y2="982.4750850567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.9405" x2="112.9405" y1="931.1355850567206" y2="982.4750850567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.5005" x2="347.5005" y1="931.1355850567206" y2="982.4750850567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.5" x2="112.9400000000001" y1="982.4750650567207" y2="982.4750650567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.5" x2="112.9400000000001" y1="1009.952565056721" y2="1009.952565056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.5" x2="41.5" y1="982.4750650567207" y2="1009.952565056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.9400000000001" x2="112.9400000000001" y1="982.4750650567207" y2="1009.952565056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.9405" x2="183.9758" y1="982.4750650567207" y2="982.4750650567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.9405" x2="183.9758" y1="1009.952565056721" y2="1009.952565056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.9405" x2="112.9405" y1="982.4750650567207" y2="1009.952565056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.9758" x2="183.9758" y1="982.4750650567207" y2="1009.952565056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.9758" x2="265.7379" y1="982.4750650567207" y2="982.4750650567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.9758" x2="265.7379" y1="1009.952565056721" y2="1009.952565056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.9758" x2="183.9758" y1="982.4750650567207" y2="1009.952565056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.7379" x2="265.7379" y1="982.4750650567207" y2="1009.952565056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.7378" x2="347.4999" y1="982.4750650567207" y2="982.4750650567207"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.7378" x2="347.4999" y1="1009.952565056721" y2="1009.952565056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.7378" x2="265.7378" y1="982.4750650567207" y2="1009.952565056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.4999" x2="347.4999" y1="982.4750650567207" y2="1009.952565056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.5" x2="112.9400000000001" y1="1009.952485056721" y2="1009.952485056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.5" x2="112.9400000000001" y1="1037.429985056721" y2="1037.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.5" x2="41.5" y1="1009.952485056721" y2="1037.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.9400000000001" x2="112.9400000000001" y1="1009.952485056721" y2="1037.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.9405" x2="183.9758" y1="1009.952485056721" y2="1009.952485056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.9405" x2="183.9758" y1="1037.429985056721" y2="1037.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="112.9405" x2="112.9405" y1="1009.952485056721" y2="1037.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.9758" x2="183.9758" y1="1009.952485056721" y2="1037.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.9758" x2="265.7379" y1="1009.952485056721" y2="1009.952485056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.9758" x2="265.7379" y1="1037.429985056721" y2="1037.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.9758" x2="183.9758" y1="1009.952485056721" y2="1037.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.7379" x2="265.7379" y1="1009.952485056721" y2="1037.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.7378" x2="347.4999" y1="1009.952485056721" y2="1009.952485056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.7378" x2="347.4999" y1="1037.429985056721" y2="1037.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.7378" x2="265.7378" y1="1009.952485056721" y2="1037.429985056721"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.4999" x2="347.4999" y1="1009.952485056721" y2="1037.429985056721"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,188.553,960.709) scale(1,1) translate(-1.0404e-14,1.05458e-13)" writing-mode="lr" x="46.86" xml:space="preserve" y="966.71" zvalue="10">参考图号     XuanGang-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,119.077,998.077) scale(1,1) translate(-5.02553e-14,-1.5345e-12)" writing-mode="lr" x="56.58" xml:space="preserve" y="1004.08" zvalue="11">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,262.381,999.077) scale(1,1) translate(1.29015e-13,-1.53605e-12)" writing-mode="lr" x="193.68" xml:space="preserve" y="1005.08" zvalue="12">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.1709,1027.64) scale(1,1) translate(-4.57409e-14,-1.58044e-12)" writing-mode="lr" x="70.17" xml:space="preserve" y="1033.64" zvalue="13">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,226.925,1024.64) scale(1,1) translate(0,1.12555e-13)" writing-mode="lr" x="191.85" xml:space="preserve" y="1030.64" zvalue="14">更新日期    </text>
  <line fill="none" id="19" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="40.78468498659493" x2="345.3959450402141" y1="626.9673247870178" y2="626.9673247870178" zvalue="15"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.8493,644.009) scale(1,1) translate(2.93067e-15,1.38847e-13)" writing-mode="lr" x="83.84927949061648" xml:space="preserve" y="648.5091752865155" zvalue="17">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.5" x2="183.5" y1="175.4299369747912" y2="175.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.5" x2="183.5" y1="201.4299369747912" y2="201.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.5" x2="2.5" y1="175.4299369747912" y2="201.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5" x2="183.5" y1="175.4299369747912" y2="201.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5" x2="364.5" y1="175.4299369747912" y2="175.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5" x2="364.5" y1="201.4299369747912" y2="201.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5" x2="183.5" y1="175.4299369747912" y2="201.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5" x2="364.5" y1="175.4299369747912" y2="201.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.5" x2="183.5" y1="201.4299369747912" y2="201.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.5" x2="183.5" y1="225.6799369747912" y2="225.6799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.5" x2="2.5" y1="201.4299369747912" y2="225.6799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5" x2="183.5" y1="201.4299369747912" y2="225.6799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5" x2="364.5" y1="201.4299369747912" y2="201.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5" x2="364.5" y1="225.6799369747912" y2="225.6799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5" x2="183.5" y1="201.4299369747912" y2="225.6799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5" x2="364.5" y1="201.4299369747912" y2="225.6799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.5" x2="183.5" y1="225.6799369747912" y2="225.6799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.5" x2="183.5" y1="248.4299369747912" y2="248.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.5" x2="2.5" y1="225.6799369747912" y2="248.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5" x2="183.5" y1="225.6799369747912" y2="248.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5" x2="364.5" y1="225.6799369747912" y2="225.6799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5" x2="364.5" y1="248.4299369747912" y2="248.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5" x2="183.5" y1="225.6799369747912" y2="248.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5" x2="364.5" y1="225.6799369747912" y2="248.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.5" x2="183.5" y1="248.4299369747912" y2="248.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.5" x2="183.5" y1="271.1799369747912" y2="271.1799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.5" x2="2.5" y1="248.4299369747912" y2="271.1799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5" x2="183.5" y1="248.4299369747912" y2="271.1799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5" x2="364.5" y1="248.4299369747912" y2="248.4299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5" x2="364.5" y1="271.1799369747912" y2="271.1799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5" x2="183.5" y1="248.4299369747912" y2="271.1799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5" x2="364.5" y1="248.4299369747912" y2="271.1799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.5" x2="183.5" y1="271.1799369747912" y2="271.1799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.5" x2="183.5" y1="293.9299369747912" y2="293.9299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.5" x2="2.5" y1="271.1799369747912" y2="293.9299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5" x2="183.5" y1="271.1799369747912" y2="293.9299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5" x2="364.5" y1="271.1799369747912" y2="271.1799369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5" x2="364.5" y1="293.9299369747912" y2="293.9299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5" x2="183.5" y1="271.1799369747912" y2="293.9299369747912"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5" x2="364.5" y1="271.1799369747912" y2="293.9299369747912"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,40.5,189.43) scale(1,1) translate(0,0)" writing-mode="lr" x="40.5" xml:space="preserve" y="194.93" zvalue="28">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.75,189.43) scale(1,1) translate(0,0)" writing-mode="lr" x="226.75" xml:space="preserve" y="194.93" zvalue="29">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50.4375,213.68) scale(1,1) translate(0,0)" writing-mode="lr" x="50.44" xml:space="preserve" y="218.18" zvalue="30">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,238.188,213.68) scale(1,1) translate(0,0)" writing-mode="lr" x="238.19" xml:space="preserve" y="218.18" zvalue="32">0.4kVⅠ段母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,66.1875,236.43) scale(1,1) translate(0,0)" writing-mode="lr" x="66.19" xml:space="preserve" y="240.93" zvalue="37">0.4kV Ⅱ段母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1064.5,54.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1064.5" xml:space="preserve" y="58.88" zvalue="39">10kV遮相农场线轩岗糖厂T线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1125.88,180.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1125.88" xml:space="preserve" y="185.25" zvalue="40">031</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,767.125,235.75) scale(1,1) translate(0,0)" writing-mode="lr" x="767.13" xml:space="preserve" y="240.25" zvalue="41">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,850,345.75) scale(1,1) translate(0,0)" writing-mode="lr" x="850" xml:space="preserve" y="350.25" zvalue="46">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,809.125,546) scale(1,1) translate(0,0)" writing-mode="lr" x="809.13" xml:space="preserve" y="550.5" zvalue="48">401</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,622.75,577) scale(1,1) translate(0,0)" writing-mode="lr" x="622.75" xml:space="preserve" y="581.5" zvalue="56">0.4kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,608.75,820.5) scale(1,1) translate(0,0)" writing-mode="lr" x="608.75" xml:space="preserve" y="825" zvalue="59">#2F  1500kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,638.5,667) scale(1,1) translate(0,0)" writing-mode="lr" x="638.5" xml:space="preserve" y="671.5" zvalue="60">441</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,750,671) scale(1,1) translate(0,0)" writing-mode="lr" x="750" xml:space="preserve" y="675.5" zvalue="63">439</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,723.1,820) scale(1,1) translate(-4.64695e-13,0)" writing-mode="lr" x="723.1" xml:space="preserve" y="824.5" zvalue="64">锅炉车间</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,931,826.75) scale(1,1) translate(0,0)" writing-mode="lr" x="931" xml:space="preserve" y="831.25" zvalue="67">#4主变  500kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" x="840" xml:space="preserve" y="435.75" zvalue="68">#1主变  </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="840" xml:space="preserve" y="451.75" zvalue="68">1250kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,920.25,887) scale(1,1) translate(0,0)" writing-mode="lr" x="920.25" xml:space="preserve" y="891.5" zvalue="72">0044</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,862,1005.25) scale(1,1) translate(0,0)" writing-mode="lr" x="862" xml:space="preserve" y="1009.75" zvalue="74">10kV轩岗糖厂水电站 400kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,900.5,735) scale(1,1) translate(0,0)" writing-mode="lr" x="900.5" xml:space="preserve" y="739.5" zvalue="75">404</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1482.5,578.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1482.5" xml:space="preserve" y="583" zvalue="81">0.4kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,916.5,537.5) scale(1,1) translate(0,0)" writing-mode="lr" x="916.5" xml:space="preserve" y="542" zvalue="82">412</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1346,345.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1346" xml:space="preserve" y="350.25" zvalue="88">0021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1305.85,507) scale(1,1) translate(0,0)" writing-mode="lr" x="1305.85" xml:space="preserve" y="511.5" zvalue="90">402</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" x="1336" xml:space="preserve" y="435.75" zvalue="94">#2主变  </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1336" xml:space="preserve" y="451.75" zvalue="94">250kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1339.35,569) scale(1,1) translate(0,0)" writing-mode="lr" x="1339.35" xml:space="preserve" y="573.5" zvalue="98">438负荷调整柜</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1075.9,671) scale(1,1) translate(0,0)" writing-mode="lr" x="1075.9" xml:space="preserve" y="675.5" zvalue="102">437</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1049,818.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1049" xml:space="preserve" y="823.25" zvalue="104">新增动力柜</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1151.9,671) scale(1,1) translate(0,0)" writing-mode="lr" x="1151.9" xml:space="preserve" y="675.5" zvalue="109">436</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1130,818.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1130" xml:space="preserve" y="823.25" zvalue="111">压榨车间</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1212.75,816.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1212.75" xml:space="preserve" y="820.75" zvalue="116">#1F  1500kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1244.4,667) scale(1,1) translate(0,0)" writing-mode="lr" x="1244.4" xml:space="preserve" y="671.5" zvalue="118">435</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1503.9,671) scale(1,1) translate(0,0)" writing-mode="lr" x="1503.9" xml:space="preserve" y="675.5" zvalue="123">432</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1482,818.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1482" xml:space="preserve" y="823.25" zvalue="125">煮炼车间</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1619.9,671) scale(1,1) translate(0,0)" writing-mode="lr" x="1619.9" xml:space="preserve" y="675.5" zvalue="130">431</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1598,818.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1598" xml:space="preserve" y="823.25" zvalue="132">清净车间</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1721.23,854) scale(1,1) translate(0,0)" writing-mode="lr" x="1721.23" xml:space="preserve" y="858.5" zvalue="137">0036</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1713.85,667) scale(1,1) translate(0,0)" writing-mode="lr" x="1713.85" xml:space="preserve" y="671.5" zvalue="138">403</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" x="1745.96875" xml:space="preserve" y="758" zvalue="139">#3主变  </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1745.96875" xml:space="preserve" y="774" zvalue="139">1000kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1685.98,958) scale(1,1) translate(0,0)" writing-mode="lr" x="1685.98" xml:space="preserve" y="962.5" zvalue="143">压榨切丝机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="151" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1313,667) scale(1,1) translate(0,0)" writing-mode="lr" x="1313" xml:space="preserve" y="671.5" zvalue="150">434</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" x="1295.34375" xml:space="preserve" y="813.75" zvalue="151">#2无功补偿 </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1295.34375" xml:space="preserve" y="829.75" zvalue="151">240KVar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1397,667) scale(1,1) translate(0,0)" writing-mode="lr" x="1397" xml:space="preserve" y="671.5" zvalue="156">433</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" x="1380.25" xml:space="preserve" y="813.75" zvalue="158">#1无功补偿 </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1380.25" xml:space="preserve" y="829.75" zvalue="158">240KVar</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,150.171,1027.64) scale(1,1) translate(-1.34559e-13,-1.58044e-12)" writing-mode="lr" x="150.17" xml:space="preserve" y="1033.64" zvalue="168">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,192.068,323.091) scale(1,1) translate(0,0)" writing-mode="lr" x="192.07" xml:space="preserve" y="327.59" zvalue="170">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,297.068,323.091) scale(1,1) translate(0,0)" writing-mode="lr" x="297.07" xml:space="preserve" y="327.59" zvalue="171">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="51" y="313" zvalue="172"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="ACLineSegmentClass">
  <g id="35">
   <use class="kv10" height="30" transform="rotate(0,1071.06,103.841) scale(5.625,1.51442) translate(-864.464,-27.5567)" width="7" x="1051.375" xlink:href="#ACLineSegment:线路_0" y="81.125" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449850376197" ObjectName="10kV遮相农场线轩岗糖厂T线"/>
   <cge:TPSR_Ref TObjectID="6192449850376197_5066549588656130"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1071.06,103.841) scale(5.625,1.51442) translate(-864.464,-27.5567)" width="7" x="1051.375" y="81.125"/></g>
 </g>
 <g id="BreakerClass">
  <g id="37">
   <use class="kv10" height="30" transform="rotate(0,1072,185.5) scale(1,0.9) translate(0,19.1111)" width="15" x="1064.5" xlink:href="#Breaker:潮流开关_0" y="172" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924523589637" ObjectName="10kV遮相农场线轩岗糖厂T线031断路器"/>
   <cge:TPSR_Ref TObjectID="6473924523589637"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1072,185.5) scale(1,0.9) translate(0,19.1111)" width="15" x="1064.5" y="172"/></g>
  <g id="48">
   <use class="v400" height="30" transform="rotate(0,778.25,547) scale(1,0.9) translate(0,59.2778)" width="15" x="770.75" xlink:href="#Breaker:潮流开关_0" y="533.5" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924523655173" ObjectName="#1主变401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924523655173"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,778.25,547) scale(1,0.9) translate(0,59.2778)" width="15" x="770.75" y="533.5"/></g>
  <g id="54">
   <use class="v400" height="20" transform="rotate(0,607,668) scale(1.5,1.35) translate(-199.833,-169.685)" width="10" x="599.5" xlink:href="#Breaker:开关_0" y="654.5" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924523720709" ObjectName="#2发电机441断路器"/>
   <cge:TPSR_Ref TObjectID="6473924523720709"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,607,668) scale(1.5,1.35) translate(-199.833,-169.685)" width="10" x="599.5" y="654.5"/></g>
  <g id="61">
   <use class="v400" height="20" transform="rotate(0,723,668) scale(1.5,1.35) translate(-238.5,-169.685)" width="10" x="715.5" xlink:href="#Breaker:开关_0" y="654.5" zvalue="62"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924523786245" ObjectName="锅炉车间439断路器"/>
   <cge:TPSR_Ref TObjectID="6473924523786245"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,723,668) scale(1.5,1.35) translate(-238.5,-169.685)" width="10" x="715.5" y="654.5"/></g>
  <g id="77">
   <use class="v400" height="20" transform="rotate(0,864,732) scale(1.5,1.35) translate(-285.5,-186.278)" width="10" x="856.5" xlink:href="#Breaker:开关_0" y="718.5" zvalue="74"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924523851781" ObjectName="#4主变404断路器"/>
   <cge:TPSR_Ref TObjectID="6473924523851781"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,864,732) scale(1.5,1.35) translate(-285.5,-186.278)" width="10" x="856.5" y="718.5"/></g>
  <g id="84">
   <use class="v400" height="20" transform="rotate(90,914,567.5) scale(-1.5,-1.35) translate(-1520.83,-984.37)" width="10" x="906.5" xlink:href="#Breaker:开关_0" y="554" zvalue="81"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924523917317" ObjectName="0.4kV母联412断路器"/>
   <cge:TPSR_Ref TObjectID="6473924523917317"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,914,567.5) scale(-1.5,-1.35) translate(-1520.83,-984.37)" width="10" x="906.5" y="554"/></g>
  <g id="98">
   <use class="v400" height="30" transform="rotate(0,1274.98,508) scale(1,0.9) translate(0,54.9444)" width="15" x="1267.479166666667" xlink:href="#Breaker:潮流开关_0" y="494.5" zvalue="89"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924523982853" ObjectName="#2主变402断路器"/>
   <cge:TPSR_Ref TObjectID="6473924523982853"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1274.98,508) scale(1,0.9) translate(0,54.9444)" width="15" x="1267.479166666667" y="494.5"/></g>
  <g id="100">
   <use class="v400" height="30" transform="rotate(0,1274.98,568) scale(1,0.9) translate(0,61.6111)" width="15" x="1267.479166666667" xlink:href="#Breaker:潮流开关_0" y="554.5" zvalue="97"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924524048389" ObjectName="负荷调整柜438断路器"/>
   <cge:TPSR_Ref TObjectID="6473924524048389"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1274.98,568) scale(1,0.9) translate(0,61.6111)" width="15" x="1267.479166666667" y="554.5"/></g>
  <g id="109">
   <use class="v400" height="20" transform="rotate(0,1048.9,668) scale(1.5,1.35) translate(-347.133,-169.685)" width="10" x="1041.4" xlink:href="#Breaker:开关_0" y="654.5" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924524113925" ObjectName="新增动力柜437断路器"/>
   <cge:TPSR_Ref TObjectID="6473924524113925"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1048.9,668) scale(1.5,1.35) translate(-347.133,-169.685)" width="10" x="1041.4" y="654.5"/></g>
  <g id="115">
   <use class="v400" height="20" transform="rotate(0,1124.9,668) scale(1.5,1.35) translate(-372.467,-169.685)" width="10" x="1117.4" xlink:href="#Breaker:开关_0" y="654.5" zvalue="108"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924524179461" ObjectName="压榨车间436断路器"/>
   <cge:TPSR_Ref TObjectID="6473924524179461"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1124.9,668) scale(1.5,1.35) translate(-372.467,-169.685)" width="10" x="1117.4" y="654.5"/></g>
  <g id="120">
   <use class="v400" height="20" transform="rotate(0,1212.9,668) scale(1.5,1.35) translate(-401.8,-169.685)" width="10" x="1205.4" xlink:href="#Breaker:开关_0" y="654.5" zvalue="117"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924524244997" ObjectName="#1发电机435断路器"/>
   <cge:TPSR_Ref TObjectID="6473924524244997"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1212.9,668) scale(1.5,1.35) translate(-401.8,-169.685)" width="10" x="1205.4" y="654.5"/></g>
  <g id="127">
   <use class="v400" height="20" transform="rotate(0,1476.9,668) scale(1.5,1.35) translate(-489.8,-169.685)" width="10" x="1469.4" xlink:href="#Breaker:开关_0" y="654.5" zvalue="122"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924524310533" ObjectName="煮炼车间432断路器"/>
   <cge:TPSR_Ref TObjectID="6473924524310533"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1476.9,668) scale(1.5,1.35) translate(-489.8,-169.685)" width="10" x="1469.4" y="654.5"/></g>
  <g id="133">
   <use class="v400" height="20" transform="rotate(0,1592.9,668) scale(1.5,1.35) translate(-528.467,-169.685)" width="10" x="1585.4" xlink:href="#Breaker:开关_0" y="654.5" zvalue="129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924524376069" ObjectName="清净车间431断路器"/>
   <cge:TPSR_Ref TObjectID="6473924524376069"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1592.9,668) scale(1.5,1.35) translate(-528.467,-169.685)" width="10" x="1585.4" y="654.5"/></g>
  <g id="137">
   <use class="v400" height="30" transform="rotate(0,1685.98,668) scale(1,0.9) translate(0,72.7222)" width="15" x="1678.479187011719" xlink:href="#Breaker:潮流开关_0" y="654.5" zvalue="137"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924524441605" ObjectName="#3主变403断路器"/>
   <cge:TPSR_Ref TObjectID="6473924524441605"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1685.98,668) scale(1,0.9) translate(0,72.7222)" width="15" x="1678.479187011719" y="654.5"/></g>
  <g id="150">
   <use class="v400" height="20" transform="rotate(0,1293,668) scale(1.5,1.35) translate(-428.5,-169.685)" width="10" x="1285.5" xlink:href="#Breaker:开关_0" y="654.5" zvalue="149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924524507141" ObjectName="#2无功补偿434断路器"/>
   <cge:TPSR_Ref TObjectID="6473924524507141"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1293,668) scale(1.5,1.35) translate(-428.5,-169.685)" width="10" x="1285.5" y="654.5"/></g>
  <g id="161">
   <use class="v400" height="20" transform="rotate(0,1377,668) scale(1.5,1.35) translate(-456.5,-169.685)" width="10" x="1369.5" xlink:href="#Breaker:开关_0" y="654.5" zvalue="154"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924524572677" ObjectName="#1无功补偿433断路器"/>
   <cge:TPSR_Ref TObjectID="6473924524572677"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1377,668) scale(1.5,1.35) translate(-456.5,-169.685)" width="10" x="1369.5" y="654.5"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="39">
   <path class="kv10" d="M 724.5 269.25 L 1438.25 269.25" stroke-width="4" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674246918148" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674246918148"/></metadata>
  <path d="M 724.5 269.25 L 1438.25 269.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="v400" d="M 569.5 610.5 L 857 610.5" stroke-width="4" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674246983684" ObjectName="0.4kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674246983684"/></metadata>
  <path d="M 569.5 610.5 L 857 610.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="v400" d="M 979.5 613.5 L 1733 613.5" stroke-width="4" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674247049220" ObjectName="0.4kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674247049220"/></metadata>
  <path d="M 979.5 613.5 L 1733 613.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="41">
   <path class="kv10" d="M 1071.06 126.33 L 1071.06 172.32" stroke-width="1" zvalue="41"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="37@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1071.06 126.33 L 1071.06 172.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv10" d="M 1072.02 198.73 L 1072.02 269.25" stroke-width="1" zvalue="42"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@1" LinkObjectIDznd="39@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1072.02 198.73 L 1072.02 269.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv10" d="M 778.25 324.25 L 778.25 269.25" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="39@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 778.25 324.25 L 778.25 269.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="v400" d="M 778.27 560.23 L 778.27 610.5" stroke-width="1" zvalue="57"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@1" LinkObjectIDznd="56@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 778.27 560.23 L 778.27 610.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="v400" d="M 606.95 655.09 L 606.95 610.5" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@0" LinkObjectIDznd="56@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 606.95 655.09 L 606.95 610.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="v400" d="M 607.1 680.89 L 607.1 741.88" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@1" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 607.1 680.89 L 607.1 741.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="v400" d="M 722.95 655.09 L 722.95 610.5" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="56@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 722.95 655.09 L 722.95 610.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="v400" d="M 723.1 680.89 L 723.1 744.35" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@1" LinkObjectIDznd="63@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 723.1 680.89 L 723.1 744.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv10" d="M 779.04 405.54 L 779.04 360.5" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="46@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 779.04 405.54 L 779.04 360.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="v400" d="M 779 464.58 L 779 533.82" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 779 464.58 L 779 533.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv10" d="M 864 899.5 L 864 929.88" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@1" LinkObjectIDznd="75@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 864 899.5 L 864 929.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv10" d="M 864 863.25 L 864 842.58" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@0" LinkObjectIDznd="67@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 864 863.25 L 864 842.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="v400" d="M 865.04 783.54 L 865.04 744.89" stroke-width="1" zvalue="77"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="77@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 865.04 783.54 L 865.04 744.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="v400" d="M 723.1 690 L 863.95 690 L 863.95 719.09" stroke-width="1" zvalue="78"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66" LinkObjectIDznd="77@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 723.1 690 L 863.95 690 L 863.95 719.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="v400" d="M 901.09 567.55 L 845 567.55 L 845 610.5" stroke-width="1" zvalue="84"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@0" LinkObjectIDznd="56@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 901.09 567.55 L 845 567.55 L 845 610.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="v400" d="M 926.89 567.4 L 1001 567.4 L 1001 613.5" stroke-width="1" zvalue="85"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@1" LinkObjectIDznd="83@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 926.89 567.4 L 1001 567.4 L 1001 613.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv10" d="M 1274.25 324.25 L 1274.25 269.25" stroke-width="1" zvalue="91"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="39@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1274.25 324.25 L 1274.25 269.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv10" d="M 1275.04 405.54 L 1275.04 360.5" stroke-width="1" zvalue="93"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="99@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1275.04 405.54 L 1275.04 360.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="v400" d="M 1275 464.58 L 1275 494.82" stroke-width="1" zvalue="95"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@1" LinkObjectIDznd="98@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1275 464.58 L 1275 494.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="v400" d="M 1275 521.23 L 1275 554.82" stroke-width="1" zvalue="98"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@1" LinkObjectIDznd="100@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1275 521.23 L 1275 554.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="v400" d="M 1275 581.23 L 1275 613.5" stroke-width="1" zvalue="99"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@1" LinkObjectIDznd="83@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1275 581.23 L 1275 613.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="v400" d="M 1048.85 655.09 L 1048.85 613.5" stroke-width="1" zvalue="105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="83@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1048.85 655.09 L 1048.85 613.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="v400" d="M 1049 680.89 L 1049 743.1" stroke-width="1" zvalue="106"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@1" LinkObjectIDznd="108@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1049 680.89 L 1049 743.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="v400" d="M 1124.85 655.09 L 1124.85 613.5" stroke-width="1" zvalue="112"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="83@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1124.85 655.09 L 1124.85 613.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="v400" d="M 1125 680.89 L 1125 743.1" stroke-width="1" zvalue="113"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@1" LinkObjectIDznd="114@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1125 680.89 L 1125 743.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="v400" d="M 1212.85 655.09 L 1212.85 613.5" stroke-width="1" zvalue="119"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="83@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1212.85 655.09 L 1212.85 613.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="v400" d="M 1213 680.89 L 1213 737.63" stroke-width="1" zvalue="120"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@1" LinkObjectIDznd="121@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1213 680.89 L 1213 737.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="v400" d="M 1476.85 655.09 L 1476.85 613.5" stroke-width="1" zvalue="126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@0" LinkObjectIDznd="83@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1476.85 655.09 L 1476.85 613.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="v400" d="M 1477 680.89 L 1477 743.1" stroke-width="1" zvalue="127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@1" LinkObjectIDznd="126@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1477 680.89 L 1477 743.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="v400" d="M 1593 680.89 L 1593 743.1" stroke-width="1" zvalue="134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@1" LinkObjectIDznd="132@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1593 680.89 L 1593 743.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="v400" d="M 1686 654.82 L 1686 613.5" stroke-width="1" zvalue="143"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="83@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1686 654.82 L 1686 613.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv10" d="M 1685.98 789.33 L 1685.98 831.25" stroke-width="1" zvalue="146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="136@1" LinkObjectIDznd="138@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1685.98 789.33 L 1685.98 831.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="kv10" d="M 1685.98 911.35 L 1685.98 867.5" stroke-width="1" zvalue="147"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="142@0" LinkObjectIDznd="138@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1685.98 911.35 L 1685.98 867.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="v400" d="M 1686.02 730.29 L 1686 681.23" stroke-width="1" zvalue="148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="136@0" LinkObjectIDznd="137@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1686.02 730.29 L 1686 681.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="v400" d="M 1293.1 680.89 L 1293.1 746.75" stroke-width="1" zvalue="152"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@1" LinkObjectIDznd="152@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1293.1 680.89 L 1293.1 746.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="v400" d="M 1377.1 680.89 L 1377.1 746.75" stroke-width="1" zvalue="159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@1" LinkObjectIDznd="160@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1377.1 680.89 L 1377.1 746.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="v400" d="M 1376.95 655.09 L 1376.95 613.5" stroke-width="1" zvalue="160"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="83@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1376.95 655.09 L 1376.95 613.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="v400" d="M 1292.95 655.09 L 1292.95 613.5" stroke-width="1" zvalue="161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="83@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1292.95 655.09 L 1292.95 613.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="v400" d="M 1592.85 655.09 L 1592.85 613.5" stroke-width="1" zvalue="162"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@0" LinkObjectIDznd="83@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1592.85 655.09 L 1592.85 613.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="46">
   <use class="kv10" height="30" transform="rotate(0,778.25,343) scale(1.25,1.25) translate(-151.9,-64.85)" width="30" x="759.5" xlink:href="#Disconnector:跌落刀闸_0" y="324.25" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449850441734" ObjectName="#1主变0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449850441734"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,778.25,343) scale(1.25,1.25) translate(-151.9,-64.85)" width="30" x="759.5" y="324.25"/></g>
  <g id="73">
   <use class="kv10" height="30" transform="rotate(0,864,882) scale(1.25,1.25) translate(-169.05,-172.65)" width="30" x="845.25" xlink:href="#Disconnector:跌落刀闸_0" y="863.25" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449850638342" ObjectName="#4主变0044隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449850638342"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,864,882) scale(1.25,1.25) translate(-169.05,-172.65)" width="30" x="845.25" y="863.25"/></g>
  <g id="99">
   <use class="kv10" height="30" transform="rotate(0,1274.25,343) scale(1.25,1.25) translate(-251.1,-64.85)" width="30" x="1255.5" xlink:href="#Disconnector:跌落刀闸_0" y="324.25" zvalue="87"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449850769414" ObjectName="#2主变0021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449850769414"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1274.25,343) scale(1.25,1.25) translate(-251.1,-64.85)" width="30" x="1255.5" y="324.25"/></g>
  <g id="138">
   <use class="kv10" height="30" transform="rotate(0,1685.98,850) scale(1.25,1.25) translate(-333.446,-166.25)" width="30" x="1667.229187011719" xlink:href="#Disconnector:跌落刀闸_0" y="831.25" zvalue="136"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449851162630" ObjectName="#3主变0036隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449851162630"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1685.98,850) scale(1.25,1.25) translate(-333.446,-166.25)" width="30" x="1667.229187011719" y="831.25"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="49">
   <use class="v400" height="30" transform="rotate(0,609,764) scale(1.5,1.5) translate(-195.5,-247.167)" width="30" x="586.5" xlink:href="#Generator:发电机_0" y="741.5" zvalue="58"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449850507270" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449850507270"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,609,764) scale(1.5,1.5) translate(-195.5,-247.167)" width="30" x="586.5" y="741.5"/></g>
  <g id="75">
   <use class="kv10" height="30" transform="rotate(0,864,952) scale(1.5,1.5) translate(-280.5,-309.833)" width="30" x="841.5" xlink:href="#Generator:发电机_0" y="929.5" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449850703878" ObjectName="10kV轩岗糖厂水电站"/>
   <cge:TPSR_Ref TObjectID="6192449850703878"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,864,952) scale(1.5,1.5) translate(-280.5,-309.833)" width="30" x="841.5" y="929.5"/></g>
  <g id="121">
   <use class="v400" height="30" transform="rotate(0,1213,759.75) scale(1.5,1.5) translate(-396.833,-245.75)" width="30" x="1190.5" xlink:href="#Generator:发电机_0" y="737.25" zvalue="115"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449850966022" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449850966022"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1213,759.75) scale(1.5,1.5) translate(-396.833,-245.75)" width="30" x="1190.5" y="737.25"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="63">
   <use class="v400" height="30" transform="rotate(0,723.1,761) scale(1.25,-1.23333) translate(-143.12,-1374.53)" width="12" x="715.6" xlink:href="#EnergyConsumer:负荷_0" y="742.5" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449850572806" ObjectName="锅炉车间"/>
   <cge:TPSR_Ref TObjectID="6192449850572806"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,723.1,761) scale(1.25,-1.23333) translate(-143.12,-1374.53)" width="12" x="715.6" y="742.5"/></g>
  <g id="108">
   <use class="v400" height="30" transform="rotate(0,1049,759.75) scale(1.25,-1.23333) translate(-208.3,-1372.26)" width="12" x="1041.5" xlink:href="#EnergyConsumer:负荷_0" y="741.25" zvalue="103"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449850834950" ObjectName="新增动力柜"/>
   <cge:TPSR_Ref TObjectID="6192449850834950"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1049,759.75) scale(1.25,-1.23333) translate(-208.3,-1372.26)" width="12" x="1041.5" y="741.25"/></g>
  <g id="114">
   <use class="v400" height="30" transform="rotate(0,1125,759.75) scale(1.25,-1.23333) translate(-223.5,-1372.26)" width="12" x="1117.5" xlink:href="#EnergyConsumer:负荷_0" y="741.25" zvalue="110"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449850900486" ObjectName="压榨车间"/>
   <cge:TPSR_Ref TObjectID="6192449850900486"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1125,759.75) scale(1.25,-1.23333) translate(-223.5,-1372.26)" width="12" x="1117.5" y="741.25"/></g>
  <g id="126">
   <use class="v400" height="30" transform="rotate(0,1477,759.75) scale(1.25,-1.23333) translate(-293.9,-1372.26)" width="12" x="1469.5" xlink:href="#EnergyConsumer:负荷_0" y="741.25" zvalue="124"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449851031558" ObjectName="煮炼车间"/>
   <cge:TPSR_Ref TObjectID="6192449851031558"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1477,759.75) scale(1.25,-1.23333) translate(-293.9,-1372.26)" width="12" x="1469.5" y="741.25"/></g>
  <g id="132">
   <use class="v400" height="30" transform="rotate(0,1593,759.75) scale(1.25,-1.23333) translate(-317.1,-1372.26)" width="12" x="1585.5" xlink:href="#EnergyConsumer:负荷_0" y="741.25" zvalue="131"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449851097094" ObjectName="清净车间"/>
   <cge:TPSR_Ref TObjectID="6192449851097094"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1593,759.75) scale(1.25,-1.23333) translate(-317.1,-1372.26)" width="12" x="1585.5" y="741.25"/></g>
  <g id="142">
   <use class="kv10" height="30" transform="rotate(0,1685.98,928) scale(1.25,-1.23333) translate(-335.696,-1676.93)" width="12" x="1678.479187011719" xlink:href="#EnergyConsumer:负荷_0" y="909.5" zvalue="142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449851228166" ObjectName="压榨切丝机"/>
   <cge:TPSR_Ref TObjectID="6192449851228166"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1685.98,928) scale(1.25,-1.23333) translate(-335.696,-1676.93)" width="12" x="1678.479187011719" y="909.5"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="67">
   <g id="670">
    <use class="v400" height="60" transform="rotate(0,865,813) scale(1,1) translate(0,0)" width="40" x="845" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="783" zvalue="66"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874442014723" ObjectName="0.4"/>
    </metadata>
   </g>
   <g id="671">
    <use class="kv10" height="60" transform="rotate(0,865,813) scale(1,1) translate(0,0)" width="40" x="845" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="783" zvalue="66"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874441949188" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399453245443" ObjectName="#4主变"/>
   <cge:TPSR_Ref TObjectID="6755399453245443"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,865,813) scale(1,1) translate(0,0)" width="40" x="845" y="783"/></g>
  <g id="69">
   <g id="690">
    <use class="kv10" height="60" transform="rotate(0,779,435) scale(1,1) translate(0,0)" width="40" x="759" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="405" zvalue="67"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874442080259" ObjectName="10"/>
    </metadata>
   </g>
   <g id="691">
    <use class="v400" height="60" transform="rotate(0,779,435) scale(1,1) translate(0,0)" width="40" x="759" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="405" zvalue="67"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874442145795" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399453310979" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399453310979"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,779,435) scale(1,1) translate(0,0)" width="40" x="759" y="405"/></g>
  <g id="96">
   <g id="960">
    <use class="kv10" height="60" transform="rotate(0,1275,435) scale(1,1) translate(0,0)" width="40" x="1255" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="405" zvalue="92"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874442211331" ObjectName="10"/>
    </metadata>
   </g>
   <g id="961">
    <use class="v400" height="60" transform="rotate(0,1275,435) scale(1,1) translate(0,0)" width="40" x="1255" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="405" zvalue="92"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874442276867" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399453376515" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399453376515"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1275,435) scale(1,1) translate(0,0)" width="40" x="1255" y="405"/></g>
  <g id="136">
   <g id="1360">
    <use class="v400" height="60" transform="rotate(0,1685.98,759.75) scale(1,1) translate(0,0)" width="40" x="1665.98" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="729.75" zvalue="138"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874442407939" ObjectName="0.4"/>
    </metadata>
   </g>
   <g id="1361">
    <use class="kv10" height="60" transform="rotate(0,1685.98,759.75) scale(1,1) translate(0,0)" width="40" x="1665.98" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="729.75" zvalue="138"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874442342403" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399453442051" ObjectName="#3主变"/>
   <cge:TPSR_Ref TObjectID="6755399453442051"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1685.98,759.75) scale(1,1) translate(0,0)" width="40" x="1665.98" y="729.75"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="152">
   <use class="v400" height="30" transform="rotate(0,1293.1,759.75) scale(1,1) translate(0,0)" width="30" x="1278.1" xlink:href="#Compensator:并联电容器组_0" y="744.75" zvalue="150"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449851293702" ObjectName="#2无功补偿"/>
   <cge:TPSR_Ref TObjectID="6192449851293702"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1293.1,759.75) scale(1,1) translate(0,0)" width="30" x="1278.1" y="744.75"/></g>
  <g id="160">
   <use class="v400" height="30" transform="rotate(0,1378,759.75) scale(1,1) translate(0,0)" width="30" x="1363" xlink:href="#Compensator:并联电容器组_0" y="744.75" zvalue="155"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449851359238" ObjectName="#1无功补偿"/>
   <cge:TPSR_Ref TObjectID="6192449851359238"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1378,759.75) scale(1,1) translate(0,0)" width="30" x="1363" y="744.75"/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,327.17,323.483) scale(0.708333,0.665547) translate(130.342,157.541)" width="30" x="316.54" xlink:href="#State:红绿圆(方形)_0" y="313.5" zvalue="173"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,327.17,323.483) scale(0.708333,0.665547) translate(130.342,157.541)" width="30" x="316.54" y="313.5"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,231.545,323.483) scale(0.708333,0.665547) translate(90.9669,157.541)" width="30" x="220.92" xlink:href="#State:红绿圆(方形)_0" y="313.5" zvalue="174"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,231.545,323.483) scale(0.708333,0.665547) translate(90.9669,157.541)" width="30" x="220.92" y="313.5"/></g>
 </g>
</svg>