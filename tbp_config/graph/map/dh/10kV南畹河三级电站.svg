<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549584986114" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变13_0" viewBox="0,0,32,35">
   <use terminal-index="0" type="0" x="16" xlink:href="#terminal" y="3.5"/>
   <ellipse cx="16.04" cy="11.97" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.97" cy="24.14" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067018" x2="16.03525619067018" y1="20.85" y2="24.95894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067017" x2="10.66666666666667" y1="24.98394833233988" y2="28.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.06302903072498" x2="21.16666666666667" y1="24.93990500916851" y2="28.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067018" x2="16.03525619067018" y1="7.6" y2="11.70894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.03525619067017" x2="12.05" y1="11.73394833233987" y2="14.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.06302903072498" x2="20.15" y1="11.68990500916851" y2="14.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV南畹河三级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="50.21" xlink:href="logo.png" y="47.57"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.214,79.5714) scale(1,1) translate(0,0)" writing-mode="lr" x="199.21" xml:space="preserve" y="83.06999999999999" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,201.714,77.2618) scale(1,1) translate(0,0)" writing-mode="lr" x="201.71" xml:space="preserve" y="86.26000000000001" zvalue="5">10kV南畹河三级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="203" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="361"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="361">信号一览</text>
  <line fill="none" id="100" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384.2142857142856" x2="384.2142857142856" y1="15.57142857142867" y2="1045.571428571429" zvalue="6"/>
  <line fill="none" id="98" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="151.441921185511" y2="151.441921185511" zvalue="8"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="268" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="337">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="265" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="338">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="339">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="263" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="340">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="341">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="343">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="344">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="345">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.054,956) scale(1,1) translate(0,0)" writing-mode="lr" x="237.05" xml:space="preserve" y="962" zvalue="346">BieNaiHeYiJi-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="254" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,147.054,988) scale(1,1) translate(0,0)" writing-mode="lr" x="147.05" xml:space="preserve" y="994" zvalue="347">李文杰</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,327.054,988) scale(1,1) translate(0,0)" writing-mode="lr" x="327.05" xml:space="preserve" y="994" zvalue="348">20210922</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="349">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="350">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="249" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63.2361,336.361) scale(1,1) translate(0,0)" writing-mode="lr" x="63.24" xml:space="preserve" y="340.86" zvalue="352">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="362">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="363">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="195" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="366">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="368">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="370">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="372">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="374">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="182" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="376">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1225.5,438.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1225.5" xml:space="preserve" y="443.25" zvalue="379">2500kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1219.71,414) scale(1,1) translate(0,0)" writing-mode="lr" x="1219.71" xml:space="preserve" y="418.5" zvalue="379">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,838,614) scale(1,1) translate(0,0)" writing-mode="lr" x="838" xml:space="preserve" y="618.5" zvalue="383">0.4kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1180.88,324) scale(1,1) translate(0,0)" writing-mode="lr" x="1180.88" xml:space="preserve" y="328.5" zvalue="384">031</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1151,207) scale(1,1) translate(0,0)" writing-mode="lr" x="1151" xml:space="preserve" y="211.5" zvalue="402">10kV南清三线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,887,878) scale(1,1) translate(0,0)" writing-mode="lr" x="887" xml:space="preserve" y="882.5" zvalue="419">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,887.5,903.25) scale(1,1) translate(0,0)" writing-mode="lr" x="887.5" xml:space="preserve" y="907.75" zvalue="420">630kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,919.5,745.5) scale(1,1) translate(0,0)" writing-mode="lr" x="919.5" xml:space="preserve" y="750" zvalue="423">401</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,904.5,690) scale(1,1) translate(0,0)" writing-mode="lr" x="904.5" xml:space="preserve" y="694.5" zvalue="425">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,990.25,786) scale(1,1) translate(0,0)" writing-mode="lr" x="990.25" xml:space="preserve" y="790.5" zvalue="430">4911</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1023,878) scale(1,1) translate(0,0)" writing-mode="lr" x="1023" xml:space="preserve" y="882.5" zvalue="459">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1023.5,903.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1023.5" xml:space="preserve" y="907.75" zvalue="460">630kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1055.5,745.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1055.5" xml:space="preserve" y="750" zvalue="462">402</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1040.5,690) scale(1,1) translate(0,0)" writing-mode="lr" x="1040.5" xml:space="preserve" y="694.5" zvalue="465">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1125.88,786) scale(1,1) translate(0,0)" writing-mode="lr" x="1125.88" xml:space="preserve" y="790.5" zvalue="469">4921</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1167,878) scale(1,1) translate(0,0)" writing-mode="lr" x="1167" xml:space="preserve" y="882.5" zvalue="475">#3发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1167.5,903.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1167.5" xml:space="preserve" y="907.75" zvalue="476">630kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1199.5,745.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1199.5" xml:space="preserve" y="750" zvalue="478">403</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1184.5,690) scale(1,1) translate(0,0)" writing-mode="lr" x="1184.5" xml:space="preserve" y="694.5" zvalue="481">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1271.38,786) scale(1,1) translate(0,0)" writing-mode="lr" x="1271.38" xml:space="preserve" y="790.5" zvalue="485">4931</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1319,878) scale(1,1) translate(0,0)" writing-mode="lr" x="1319" xml:space="preserve" y="882.5" zvalue="491">#4发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1319.5,903.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1319.5" xml:space="preserve" y="907.75" zvalue="492">630kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1351.5,745.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1351.5" xml:space="preserve" y="750" zvalue="494">404</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1336.5,690) scale(1,1) translate(0,0)" writing-mode="lr" x="1336.5" xml:space="preserve" y="694.5" zvalue="497">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1422.25,786) scale(1,1) translate(0,0)" writing-mode="lr" x="1422.25" xml:space="preserve" y="790.5" zvalue="501">4941</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="361"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125031116805" ObjectName="F"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125031182341" ObjectName="F"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="246" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,158.333,336.278) scale(1,1) translate(0,0)" writing-mode="lr" x="158.49" xml:space="preserve" y="341.19" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125030985733" ObjectName="F"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125031051269" ObjectName="F"/>
   </metadata>
  </g>
  <g id="194">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="194" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="384.3" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="192">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="192" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="337.77" xml:space="preserve" y="384.3" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="189">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="189" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,402.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="407.3" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="186">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="186" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.611,402.389) scale(1,1) translate(0,0)" writing-mode="lr" x="337.77" xml:space="preserve" y="407.3" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="183">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="183" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125030985733" ObjectName="F"/>
   </metadata>
  </g>
  <g id="181">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="181" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125030985733" ObjectName="F"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="211">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="359"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374891876355" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="360"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="1">
   <g id="10">
    <use class="kv10" height="60" transform="rotate(0,1152.05,418) scale(1.36667,1.36667) translate(-301.752,-101.146)" width="40" x="1124.71" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="377" zvalue="378"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874432380932" ObjectName="10"/>
    </metadata>
   </g>
   <g id="11">
    <use class="v400" height="60" transform="rotate(0,1152.05,418) scale(1.36667,1.36667) translate(-301.752,-101.146)" width="40" x="1124.71" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="377" zvalue="378"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874432446468" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399448461316" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399448461316"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1152.05,418) scale(1.36667,1.36667) translate(-301.752,-101.146)" width="40" x="1124.71" y="377"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="6">
   <path class="v400" d="M 838 636 L 1436 636" stroke-width="6" zvalue="382"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674241085445" ObjectName="0.4kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674241085445"/></metadata>
  <path d="M 838 636 L 1436 636" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="8">
   <use class="kv10" height="20" transform="rotate(0,1152,325) scale(1.5,1.35) translate(-381.5,-80.7593)" width="10" x="1144.5" xlink:href="#Breaker:开关_0" y="311.5" zvalue="383"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924503273477" ObjectName="#1主变10kV侧031断路器"/>
   <cge:TPSR_Ref TObjectID="6473924503273477"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1152,325) scale(1.5,1.35) translate(-381.5,-80.7593)" width="10" x="1144.5" y="311.5"/></g>
  <g id="51">
   <use class="v400" height="20" transform="rotate(0,891.5,742.5) scale(1.5,1.35) translate(-294.667,-189)" width="10" x="884" xlink:href="#Breaker:开关_0" y="729" zvalue="422"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924503339014" ObjectName="#1发电机401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924503339014"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,891.5,742.5) scale(1.5,1.35) translate(-294.667,-189)" width="10" x="884" y="729"/></g>
  <g id="104">
   <use class="v400" height="20" transform="rotate(0,1027.5,742.5) scale(1.5,1.35) translate(-340,-189)" width="10" x="1020" xlink:href="#Breaker:开关_0" y="729" zvalue="461"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924503404549" ObjectName="#2发电机402断路器"/>
   <cge:TPSR_Ref TObjectID="6473924503404549"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1027.5,742.5) scale(1.5,1.35) translate(-340,-189)" width="10" x="1020" y="729"/></g>
  <g id="119">
   <use class="v400" height="20" transform="rotate(0,1171.5,742.5) scale(1.5,1.35) translate(-388,-189)" width="10" x="1164" xlink:href="#Breaker:开关_0" y="729" zvalue="477"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924503470085" ObjectName="#3发电机403断路器"/>
   <cge:TPSR_Ref TObjectID="6473924503470085"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1171.5,742.5) scale(1.5,1.35) translate(-388,-189)" width="10" x="1164" y="729"/></g>
  <g id="134">
   <use class="v400" height="20" transform="rotate(0,1323.5,742.5) scale(1.5,1.35) translate(-438.667,-189)" width="10" x="1316" xlink:href="#Breaker:开关_0" y="729" zvalue="493"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924503535621" ObjectName="#4发电机404断路器"/>
   <cge:TPSR_Ref TObjectID="6473924503535621"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1323.5,742.5) scale(1.5,1.35) translate(-438.667,-189)" width="10" x="1316" y="729"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="13">
   <path class="kv10" d="M 1151 262.32 L 1151 312.09" stroke-width="1" zvalue="386"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@0" LinkObjectIDznd="8@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1151 262.32 L 1151 312.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv10" d="M 1152.1 337.89 L 1152.1 377.74" stroke-width="1" zvalue="387"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="8@1" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1152.1 337.89 L 1152.1 377.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="v400" d="M 1152.05 636 L 1152.05 458.42" stroke-width="1" zvalue="392"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="6@0" LinkObjectIDznd="1@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1152.05 636 L 1152.05 458.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="v400" d="M 891.59 680.36 L 891.59 636" stroke-width="1" zvalue="425"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="6@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 891.59 680.36 L 891.59 636" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="v400" d="M 891.56 701.81 L 891.56 729.59" stroke-width="1" zvalue="426"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@1" LinkObjectIDznd="51@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 891.56 701.81 L 891.56 729.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="v400" d="M 891.6 755.39 L 891.6 807.88" stroke-width="1" zvalue="427"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 891.6 755.39 L 891.6 807.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="v400" d="M 961.59 773.36 L 961.59 765 L 891.6 765" stroke-width="1" zvalue="431"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@0" LinkObjectIDznd="57" MaxPinNum="2"/>
   </metadata>
  <path d="M 961.59 773.36 L 961.59 765 L 891.6 765" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="v400" d="M 961.56 794.81 L 961.56 810" stroke-width="1" zvalue="432"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@1" LinkObjectIDznd="86@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 961.56 794.81 L 961.56 810" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="v400" d="M 1027.59 680.36 L 1027.59 636" stroke-width="1" zvalue="464"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="6@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1027.59 680.36 L 1027.59 636" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="v400" d="M 1027.56 701.81 L 1027.56 729.59" stroke-width="1" zvalue="466"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@1" LinkObjectIDznd="104@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1027.56 701.81 L 1027.56 729.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="v400" d="M 1027.6 755.39 L 1027.6 807.88" stroke-width="1" zvalue="467"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@1" LinkObjectIDznd="106@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1027.6 755.39 L 1027.6 807.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="v400" d="M 1097.59 773.36 L 1097.59 765 L 1027.6 765" stroke-width="1" zvalue="470"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="95" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.59 773.36 L 1097.59 765 L 1027.6 765" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="v400" d="M 1097.56 794.81 L 1097.56 810" stroke-width="1" zvalue="471"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@1" LinkObjectIDznd="91@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.56 794.81 L 1097.56 810" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="v400" d="M 1171.59 680.36 L 1171.59 636" stroke-width="1" zvalue="480"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@0" LinkObjectIDznd="6@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1171.59 680.36 L 1171.59 636" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="v400" d="M 1171.56 701.81 L 1171.56 729.59" stroke-width="1" zvalue="482"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@1" LinkObjectIDznd="119@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1171.56 701.81 L 1171.56 729.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="v400" d="M 1171.6 755.39 L 1171.6 807.88" stroke-width="1" zvalue="483"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@1" LinkObjectIDznd="121@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1171.6 755.39 L 1171.6 807.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="v400" d="M 1241.59 773.36 L 1241.59 765 L 1171.6 765" stroke-width="1" zvalue="486"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="115" MaxPinNum="2"/>
   </metadata>
  <path d="M 1241.59 773.36 L 1241.59 765 L 1171.6 765" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="v400" d="M 1241.56 794.81 L 1241.56 810" stroke-width="1" zvalue="487"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@1" LinkObjectIDznd="111@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1241.56 794.81 L 1241.56 810" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="v400" d="M 1323.59 680.36 L 1323.59 636" stroke-width="1" zvalue="496"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@0" LinkObjectIDznd="6@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1323.59 680.36 L 1323.59 636" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="v400" d="M 1323.56 701.81 L 1323.56 729.59" stroke-width="1" zvalue="498"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@1" LinkObjectIDznd="134@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1323.56 701.81 L 1323.56 729.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="v400" d="M 1323.6 755.39 L 1323.6 807.88" stroke-width="1" zvalue="499"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@1" LinkObjectIDznd="136@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1323.6 755.39 L 1323.6 807.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="v400" d="M 1393.59 773.36 L 1393.59 765 L 1323.6 765" stroke-width="1" zvalue="502"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="130" MaxPinNum="2"/>
   </metadata>
  <path d="M 1393.59 773.36 L 1393.59 765 L 1323.6 765" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="v400" d="M 1393.56 794.81 L 1393.56 809" stroke-width="1" zvalue="503"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@1" LinkObjectIDznd="126@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1393.56 794.81 L 1393.56 809" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="48">
   <use class="v400" height="30" transform="rotate(0,890,830) scale(1.5,1.5) translate(-289.167,-269.167)" width="30" x="867.5" xlink:href="#Generator:发电机_0" y="807.5" zvalue="418"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449762820101" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449762820101"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,890,830) scale(1.5,1.5) translate(-289.167,-269.167)" width="30" x="867.5" y="807.5"/></g>
  <g id="106">
   <use class="v400" height="30" transform="rotate(0,1026,830) scale(1.5,1.5) translate(-334.5,-269.167)" width="30" x="1003.5" xlink:href="#Generator:发电机_0" y="807.5" zvalue="458"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449763278854" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449763278854"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1026,830) scale(1.5,1.5) translate(-334.5,-269.167)" width="30" x="1003.5" y="807.5"/></g>
  <g id="121">
   <use class="v400" height="30" transform="rotate(0,1170,830) scale(1.5,1.5) translate(-382.5,-269.167)" width="30" x="1147.5" xlink:href="#Generator:发电机_0" y="807.5" zvalue="474"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449763540998" ObjectName="#3发电机"/>
   <cge:TPSR_Ref TObjectID="6192449763540998"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1170,830) scale(1.5,1.5) translate(-382.5,-269.167)" width="30" x="1147.5" y="807.5"/></g>
  <g id="136">
   <use class="v400" height="30" transform="rotate(0,1322,830) scale(1.5,1.5) translate(-433.167,-269.167)" width="30" x="1299.5" xlink:href="#Generator:发电机_0" y="807.5" zvalue="490"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449763803141" ObjectName="#4发电机"/>
   <cge:TPSR_Ref TObjectID="6192449763803141"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1322,830) scale(1.5,1.5) translate(-433.167,-269.167)" width="30" x="1299.5" y="807.5"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="53">
   <use class="v400" height="30" transform="rotate(0,891.5,691) scale(1,0.733333) translate(0,247.273)" width="15" x="884" xlink:href="#Disconnector:刀闸_0" y="680" zvalue="424"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449762885637" ObjectName="#1发电机4011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449762885637"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,891.5,691) scale(1,0.733333) translate(0,247.273)" width="15" x="884" y="680"/></g>
  <g id="58">
   <use class="v400" height="30" transform="rotate(0,961.5,784) scale(1,0.733333) translate(0,281.091)" width="15" x="954" xlink:href="#Disconnector:刀闸_0" y="773" zvalue="429"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449762951173" ObjectName="#1发电机4911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449762951173"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,961.5,784) scale(1,0.733333) translate(0,281.091)" width="15" x="954" y="773"/></g>
  <g id="103">
   <use class="v400" height="30" transform="rotate(0,1027.5,691) scale(1,0.733333) translate(0,247.273)" width="15" x="1020" xlink:href="#Disconnector:刀闸_0" y="680" zvalue="463"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449763213318" ObjectName="#2发电机4021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449763213318"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1027.5,691) scale(1,0.733333) translate(0,247.273)" width="15" x="1020" y="680"/></g>
  <g id="94">
   <use class="v400" height="30" transform="rotate(0,1097.5,784) scale(1,0.733333) translate(0,281.091)" width="15" x="1090" xlink:href="#Disconnector:刀闸_0" y="773" zvalue="468"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449763147782" ObjectName="#2发电机4921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449763147782"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1097.5,784) scale(1,0.733333) translate(0,281.091)" width="15" x="1090" y="773"/></g>
  <g id="118">
   <use class="v400" height="30" transform="rotate(0,1171.5,691) scale(1,0.733333) translate(0,247.273)" width="15" x="1164" xlink:href="#Disconnector:刀闸_0" y="680" zvalue="479"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449763475461" ObjectName="#3发电机4031隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449763475461"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1171.5,691) scale(1,0.733333) translate(0,247.273)" width="15" x="1164" y="680"/></g>
  <g id="114">
   <use class="v400" height="30" transform="rotate(0,1241.5,784) scale(1,0.733333) translate(0,281.091)" width="15" x="1234" xlink:href="#Disconnector:刀闸_0" y="773" zvalue="484"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449763409926" ObjectName="#3发电机4931隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449763409926"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1241.5,784) scale(1,0.733333) translate(0,281.091)" width="15" x="1234" y="773"/></g>
  <g id="133">
   <use class="v400" height="30" transform="rotate(0,1323.5,691) scale(1,0.733333) translate(0,247.273)" width="15" x="1316" xlink:href="#Disconnector:刀闸_0" y="680" zvalue="495"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449763737605" ObjectName="#4发电机4041隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449763737605"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1323.5,691) scale(1,0.733333) translate(0,247.273)" width="15" x="1316" y="680"/></g>
  <g id="129">
   <use class="v400" height="30" transform="rotate(0,1393.5,784) scale(1,0.733333) translate(0,281.091)" width="15" x="1386" xlink:href="#Disconnector:刀闸_0" y="773" zvalue="500"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449763672069" ObjectName="#4发电机4941隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449763672069"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1393.5,784) scale(1,0.733333) translate(0,281.091)" width="15" x="1386" y="773"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="86">
   <use class="v400" height="35" transform="rotate(0,962,824) scale(1,1) translate(0,0)" width="32" x="946" xlink:href="#EnergyConsumer:站用变13_0" y="806.5" zvalue="456"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449763016710" ObjectName="励磁变1"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,962,824) scale(1,1) translate(0,0)" width="32" x="946" y="806.5"/></g>
  <g id="91">
   <use class="v400" height="35" transform="rotate(0,1098,824) scale(1,1) translate(0,0)" width="32" x="1082" xlink:href="#EnergyConsumer:站用变13_0" y="806.5" zvalue="472"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449763082246" ObjectName="励磁变2"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1098,824) scale(1,1) translate(0,0)" width="32" x="1082" y="806.5"/></g>
  <g id="111">
   <use class="v400" height="35" transform="rotate(0,1242,824) scale(1,1) translate(0,0)" width="32" x="1226" xlink:href="#EnergyConsumer:站用变13_0" y="806.5" zvalue="488"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449763344390" ObjectName="励磁变3"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1242,824) scale(1,1) translate(0,0)" width="32" x="1226" y="806.5"/></g>
  <g id="126">
   <use class="v400" height="35" transform="rotate(0,1393,823) scale(1,1) translate(0,0)" width="32" x="1377" xlink:href="#EnergyConsumer:站用变13_0" y="805.5" zvalue="504"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449763606534" ObjectName="励磁变4"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1393,823) scale(1,1) translate(0,0)" width="32" x="1377" y="805.5"/></g>
 </g>
</svg>