<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549584003074" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="3" y1="23" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="12" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="12" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0.3333333333333357" y2="12.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="12" y2="12"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸_0" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6.000733890582183" xlink:href="#terminal" y="1.25365326133502"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="16.81276518533035"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.083333333333333" x2="6.083333333333333" y1="10.08333333333333" y2="1.083333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="16.81354468322919" y2="9.527777777777771"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.083333333333333" x2="0.8263923524522143" y1="16.81435756744399" y2="9.527777777777771"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.083333333333332" x2="0.8000000000000016" y1="9.9106081390032" y2="2.616666666666669"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.097965249199815" x2="11.38333333333333" y1="9.903292181069959" y2="2.61666666666667"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸_1" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6.000733890582183" xlink:href="#terminal" y="1.25365326133502"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="16.81276518533035"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="16.81354468322919" y2="9.527777777777771"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.083333333333333" x2="0.8263923524522143" y1="16.81435756744399" y2="9.527777777777771"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6" x2="6" y1="0.9999999999999982" y2="16.80704160951075"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸_2" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6.000733890582183" xlink:href="#terminal" y="1.25365326133502"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="16.81276518533035"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.658956967941879" x2="2.492290301275209" y1="5.063824112178023" y2="13.23049077884469"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.325623634608544" x2="9.825623634608544" y1="5.313824112178023" y2="12.98049077884469"/>
  </symbol>
  <symbol id="Accessory:避雷器PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1754.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2334.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_0" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3" x2="7" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.25" x2="5.083333333333332" y1="7.083333333333336" y2="19.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_1" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.833333333333333" x2="6.916666666666667" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.05" x2="5.05" y1="5.000000000000004" y2="22.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_2" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.166666666666668" x2="1.333333333333333" y1="5.083333333333332" y2="24.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1" x2="8.833333333333334" y1="4.999999999999998" y2="24.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:四卷PT_0" viewBox="0,0,18,18">
   <use terminal-index="0" type="0" x="9" xlink:href="#terminal" y="0.2666666666666639"/>
   <ellipse cx="9.25" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.25" cy="9.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.08" cy="12.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.82" cy="9.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="11.75" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.25" x2="9.25" y1="7.500000000000001" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="15.25" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="12.31481481481481" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.94444444444444" x2="13.62962962962963" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14.94444444444444" y1="9.966124661246614" y2="9.966124661246614"/>
  </symbol>
  <symbol id="Accessory:避雷器_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="15.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="8.6" y2="5.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000003" y2="8.666666666666668"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="12.6" y2="18.6"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_0" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.05" x2="7.05" y1="1.166666666666664" y2="26.33333333333334"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="10.66666666666667" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="26.5417774506975" y2="23.30486537657277"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="26.5417774506975" y2="23.30486537657277"/>
   <rect fill-opacity="0" height="6.25" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,8.63) scale(1,1) translate(0,0)" width="2.83" x="5.58" y="5.5"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_1" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.05" x2="7.05" y1="2.833333333333334" y2="14.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="2.617622152595477" y2="5.500000000000003"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="10.66666666666666" y1="2.617622152595477" y2="5.500000000000003"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="10.66666666666667" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="24.65357874079139" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="26.5417774506975" y2="23.30486537657277"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="24.65357874079139" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="26.5417774506975" y2="23.30486537657277"/>
   <rect fill-opacity="0" height="6.25" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,8.63) scale(1,1) translate(0,0)" width="2.83" x="5.58" y="5.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="7.049999999999999" y1="16" y2="18.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.133333333333333" x2="7.133333333333333" y1="18.16666666666667" y2="24.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_2" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.33333333333333" x2="3.333333333333333" y1="9.166666666666668" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.666666666666667" x2="10.83333333333333" y1="9.124593892873616" y2="18.41666666666667"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="12.5" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="26.56537356321841" y2="21.7550287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="26.56537356321841" y2="21.7550287356322"/>
  </symbol>
  <symbol id="Accessory:PT1515_0" viewBox="0,0,15,23">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="0.2666666666666639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="3.916666666666664" y2="0.2499999999999982"/>
   <ellipse cx="7.67" cy="16.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.57" cy="8.85" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.583333333333334" x2="7.583333333333334" y1="14.63888888888889" y2="16.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333334" x2="7.583333333333334" y1="19.25" y2="16.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333334" x2="7.583333333333334" y1="14.63888888888889" y2="16.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.749999999999996" x2="9.694444444444443" y1="9.549457994579948" y2="9.549457994579948"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.750000000000003" x2="7.064814814814818" y1="9.549457994579944" y2="7.083333333333332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.694444444444443" x2="8.379629629629626" y1="9.549457994579944" y2="7.083333333333332"/>
  </symbol>
  <symbol id="Accessory:4绕组母线PT_0" viewBox="0,0,25,20">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="0"/>
   <ellipse cx="12.75" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.75" cy="9.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.67" cy="14.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.82" cy="9.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="5.583333333333334" y1="7.38888888888889" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333336" x2="5.583333333333336" y1="12" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="5.5" y1="7.38888888888889" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="12.75" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="12.75" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.75" x2="12.75" y1="7.500000000000002" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.58333333333333" x2="12.58333333333333" y1="12.63888888888889" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.58333333333333" x2="12.58333333333333" y1="12.63888888888889" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="17.25" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="19.31481481481482" y1="10.21612466124661" y2="7.749999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.94444444444444" x2="20.62962962962963" y1="10.21612466124661" y2="7.749999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="21.94444444444444" y1="10.21612466124661" y2="10.21612466124661"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV勐弄河一级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="50.21" xlink:href="logo.png" y="47.57"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="114" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.214,79.5714) scale(1,1) translate(0,0)" writing-mode="lr" x="199.21" xml:space="preserve" y="83.06999999999999" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,201.714,77.2618) scale(1,1) translate(0,0)" writing-mode="lr" x="201.71" xml:space="preserve" y="86.26000000000001" zvalue="3">35kV勐弄河一级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="12" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="224"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="224">信号一览</text>
  <line fill="none" id="112" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384.2142857142856" x2="384.2142857142856" y1="15.57142857142867" y2="1045.571428571429" zvalue="4"/>
  <line fill="none" id="110" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="151.441921185511" y2="151.441921185511" zvalue="6"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1056.32,358.73) scale(1,1) translate(0,0)" writing-mode="lr" x="1056.32" xml:space="preserve" y="363.23" zvalue="8">371</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,944.393,456.801) scale(1,1) translate(0,4.91942e-14)" writing-mode="lr" x="944.39" xml:space="preserve" y="461.3" zvalue="10">#1主变5000kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,493.34,601.841) scale(1,1) translate(0,0)" writing-mode="lr" x="493.34" xml:space="preserve" y="606.34" zvalue="12">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,628.456,925.51) scale(1,1) translate(0,3.01343e-13)" writing-mode="lr" x="628.4563443455019" xml:space="preserve" y="930.0096849061007" zvalue="14">#1发电机2000kW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,704.247,422.925) scale(1,1) translate(4.55134e-13,0)" writing-mode="lr" x="704.25" xml:space="preserve" y="427.42" zvalue="16">站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,991.435,562) scale(1,1) translate(1.0885e-13,0)" writing-mode="lr" x="991.4400000000001" xml:space="preserve" y="566.5" zvalue="18">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1107.22,288.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1107.22" xml:space="preserve" y="293.39" zvalue="20">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1052.93,263.675) scale(1,1) translate(0,0)" writing-mode="lr" x="1052.93" xml:space="preserve" y="268.17" zvalue="22">3716</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,594.863,710) scale(1,1) translate(6.46705e-14,0)" writing-mode="lr" x="594.86" xml:space="preserve" y="714.5" zvalue="27">671</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,591.262,830.592) scale(1,1) translate(0,0)" writing-mode="lr" x="591.26" xml:space="preserve" y="835.09" zvalue="30">6911</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1027.53,127.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1027.53" xml:space="preserve" y="131.75" zvalue="32">35kV勐弄河一级电站线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,998.046,263.675) scale(1,1) translate(0,0)" writing-mode="lr" x="998.05" xml:space="preserve" y="268.17" zvalue="50">3719</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,902.778,212.778) scale(1,1) translate(0,0)" writing-mode="lr" x="902.78" xml:space="preserve" y="217.28" zvalue="55">90</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,903.889,322.778) scale(1,1) translate(0,0)" writing-mode="lr" x="903.89" xml:space="preserve" y="327.28" zvalue="57">97</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,733.839,843.269) scale(1,1) translate(0,0)" writing-mode="lr" x="733.84" xml:space="preserve" y="847.77" zvalue="67">6912</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1004.64,872.361) scale(1,1) translate(0,0)" writing-mode="lr" x="1004.64" xml:space="preserve" y="876.86" zvalue="74">6.3kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,961.418,751.861) scale(1,1) translate(0,0)" writing-mode="lr" x="961.42" xml:space="preserve" y="756.36" zvalue="76">6901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,660.687,558.219) scale(1,1) translate(0,-1.22062e-13)" writing-mode="lr" x="660.6900000000001" xml:space="preserve" y="562.72" zvalue="122">6811</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1327.26,562.664) scale(1,1) translate(0,0)" writing-mode="lr" x="1327.26" xml:space="preserve" y="567.16" zvalue="128">602</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1365.98,406) scale(1,1) translate(0,0)" writing-mode="lr" x="1365.98" xml:space="preserve" y="410.5" zvalue="167">近区变250kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1304.46,925.51) scale(1,1) translate(0,3.01343e-13)" writing-mode="lr" x="1304.456344345502" xml:space="preserve" y="930.0096849061007" zvalue="178">#2发电机2000kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1270.86,710) scale(1,1) translate(0,0)" writing-mode="lr" x="1270.86" xml:space="preserve" y="714.5" zvalue="181">672</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1267.26,830.592) scale(1,1) translate(0,0)" writing-mode="lr" x="1267.26" xml:space="preserve" y="835.09" zvalue="183">6921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1409.84,843.269) scale(1,1) translate(0,0)" writing-mode="lr" x="1409.84" xml:space="preserve" y="847.77" zvalue="186">6922</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="76" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="202"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="204">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="205">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="206">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="207">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="208">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="210">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="212">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.054,956) scale(1,1) translate(0,0)" writing-mode="lr" x="237.05" xml:space="preserve" y="962" zvalue="213">MengNongHeYiJi-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,327.054,988) scale(1,1) translate(0,0)" writing-mode="lr" x="327.05" xml:space="preserve" y="994" zvalue="215">20140722</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="216">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="217">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63.2361,336.361) scale(1,1) translate(0,0)" writing-mode="lr" x="63.24" xml:space="preserve" y="340.86" zvalue="218">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="225">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="226">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="229">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="231">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="233">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="235">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="237">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="239">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.5,187.5) scale(1,1) translate(0,0)" writing-mode="lr" x="201.5" xml:space="preserve" y="192" zvalue="258">事故总</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="224"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BreakerClass">
  <g id="418">
   <use class="kv35" height="20" transform="rotate(0,1027.54,359.73) scale(1.22222,1.11111) translate(-185.715,-34.8619)" width="10" x="1021.430373421849" xlink:href="#Breaker:开关_0" y="348.6190476190475" zvalue="7"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924493443077" ObjectName="35kV勐弄河一级电站线371断路器"/>
   <cge:TPSR_Ref TObjectID="6473924493443077"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1027.54,359.73) scale(1.22222,1.11111) translate(-185.715,-34.8619)" width="10" x="1021.430373421849" y="348.6190476190475"/></g>
  <g id="453">
   <use class="v6300" height="20" transform="rotate(0,1026.48,563.33) scale(2.28303,2.28303) translate(-570.45,-303.754)" width="10" x="1015.060043719461" xlink:href="#Breaker:小车断路器_0" y="540.5" zvalue="17"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924493377541" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924493377541"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1026.48,563.33) scale(2.28303,2.28303) translate(-570.45,-303.754)" width="10" x="1015.060043719461" y="540.5"/></g>
  <g id="198">
   <use class="v6300" height="20" transform="rotate(0,626.475,709) scale(2.75,2.75) translate(-389.916,-433.682)" width="10" x="612.7252143314928" xlink:href="#Breaker:小车断路器_0" y="681.5" zvalue="25"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924493312005" ObjectName="#1发电机671断路器"/>
   <cge:TPSR_Ref TObjectID="6473924493312005"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,626.475,709) scale(2.75,2.75) translate(-389.916,-433.682)" width="10" x="612.7252143314928" y="681.5"/></g>
  <g id="241">
   <use class="v6300" height="20" transform="rotate(0,1363.17,558.108) scale(2.11637,2.11637) translate(-713.478,-283.234)" width="10" x="1352.584829387969" xlink:href="#Breaker:小车断路器_0" y="536.9444444444445" zvalue="127"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924493246469" ObjectName="近区变602断路器"/>
   <cge:TPSR_Ref TObjectID="6473924493246469"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1363.17,558.108) scale(2.11637,2.11637) translate(-713.478,-283.234)" width="10" x="1352.584829387969" y="536.9444444444445"/></g>
  <g id="161">
   <use class="v6300" height="20" transform="rotate(0,1302.48,709) scale(2.75,2.75) translate(-820.098,-433.682)" width="10" x="1288.725214331493" xlink:href="#Breaker:小车断路器_0" y="681.5" zvalue="179"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924493508613" ObjectName="#2发电机672断路器"/>
   <cge:TPSR_Ref TObjectID="6473924493508613"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1302.48,709) scale(2.75,2.75) translate(-820.098,-433.682)" width="10" x="1288.725214331493" y="681.5"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="414">
   <g id="4140">
    <use class="kv35" height="60" transform="rotate(0,1027.53,449.327) scale(1.6125,1.54462) translate(-378.052,-142.091)" width="40" x="995.28" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="402.99" zvalue="9"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874428973060" ObjectName="35"/>
    </metadata>
   </g>
   <g id="4141">
    <use class="v6300" height="60" transform="rotate(0,1027.53,449.327) scale(1.6125,1.54462) translate(-378.052,-142.091)" width="40" x="995.28" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="402.99" zvalue="9"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874429038596" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399446757380" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399446757380"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1027.53,449.327) scale(1.6125,1.54462) translate(-378.052,-142.091)" width="40" x="995.28" y="402.99"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="410">
   <path class="v6300" d="M 460.26 633.54 L 1693.26 633.54" stroke-width="6" zvalue="11"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674239119364" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674239119364"/></metadata>
  <path d="M 460.26 633.54 L 1693.26 633.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="212">
   <use class="v6300" height="30" transform="rotate(0,626.475,879.307) scale(1.58467,1.58467) translate(-222.371,-315.655)" width="30" x="602.7051208787044" xlink:href="#Generator:发电机_0" y="855.5367361713461" zvalue="13"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449702264837" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449702264837"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,626.475,879.307) scale(1.58467,1.58467) translate(-222.371,-315.655)" width="30" x="602.7051208787044" y="855.5367361713461"/></g>
  <g id="162">
   <use class="v6300" height="30" transform="rotate(0,1302.48,879.307) scale(1.58467,1.58467) translate(-471.785,-315.655)" width="30" x="1278.705120878704" xlink:href="#Generator:发电机_0" y="855.5367361713461" zvalue="177"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449702985733" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449702985733"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1302.48,879.307) scale(1.58467,1.58467) translate(-471.785,-315.655)" width="30" x="1278.705120878704" y="855.5367361713461"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="399">
   <use class="v6300" height="30" transform="rotate(0,702.122,467.925) scale(1.69643,-1.70833) translate(-278.49,-731.207)" width="28" x="678.3719224784917" xlink:href="#EnergyConsumer:站用变DY接地_0" y="442.2996031746032" zvalue="15"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449702199301" ObjectName="站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,702.122,467.925) scale(1.69643,-1.70833) translate(-278.49,-731.207)" width="28" x="678.3719224784917" y="442.2996031746032"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="207">
   <use class="kv35" height="30" transform="rotate(270,1111.89,311) scale(-1.11111,1.11111) translate(-2112.03,-29.4333)" width="10" x="1106.333333333333" xlink:href="#GroundDisconnector:配网地刀_0" y="294.3333333333334" zvalue="19"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449702133765" ObjectName="35kV勐弄河一级电站线37167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449702133765"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1111.89,311) scale(-1.11111,1.11111) translate(-2112.03,-29.4333)" width="10" x="1106.333333333333" y="294.3333333333334"/></g>
  <g id="125">
   <use class="kv35" height="30" transform="rotate(90,905,234.333) scale(1.11111,1.11111) translate(-89.9444,-21.7667)" width="10" x="899.4444444444445" xlink:href="#GroundDisconnector:配网地刀_0" y="217.6666666666667" zvalue="54"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449701675013" ObjectName="35kV勐弄河一级电站线37190接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449701675013"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,905,234.333) scale(1.11111,1.11111) translate(-89.9444,-21.7667)" width="10" x="899.4444444444445" y="217.6666666666667"/></g>
  <g id="126">
   <use class="kv35" height="30" transform="rotate(90,903.889,295.444) scale(1.11111,1.11111) translate(-89.8333,-27.8778)" width="10" x="898.3333333333334" xlink:href="#GroundDisconnector:配网地刀_0" y="278.7777777777777" zvalue="56"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449701543941" ObjectName="35kV勐弄河一级电站线37197接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449701543941"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,903.889,295.444) scale(1.11111,1.11111) translate(-89.8333,-27.8778)" width="10" x="898.3333333333334" y="278.7777777777777"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="206">
   <use class="kv35" height="30" transform="rotate(0,1027.6,264.675) scale(-1.11111,-0.814815) translate(-1951.6,-592.28)" width="15" x="1019.264940515398" xlink:href="#Disconnector:刀闸_0" y="252.4523946217128" zvalue="21"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449702002693" ObjectName="35kV勐弄河一级电站线3716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449702002693"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1027.6,264.675) scale(-1.11111,-0.814815) translate(-1951.6,-592.28)" width="15" x="1019.264940515398" y="252.4523946217128"/></g>
  <g id="191">
   <use class="v6300" height="26" transform="rotate(0,556.023,828.174) scale(1.40192,1.40192) translate(-156.997,-232.207)" width="12" x="547.6116401027277" xlink:href="#Disconnector:手车刀闸2020_0" y="809.949023199023" zvalue="29"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449701937157" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449701937157"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,556.023,828.174) scale(1.40192,1.40192) translate(-156.997,-232.207)" width="12" x="547.6116401027277" y="809.949023199023"/></g>
  <g id="119">
   <use class="kv35" height="30" transform="rotate(0,968.712,264.675) scale(-1.11111,-0.814815) translate(-1839.72,-592.28)" width="15" x="960.3790115379935" xlink:href="#Disconnector:刀闸_0" y="252.4523946217128" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449701806085" ObjectName="35kV勐弄河一级电站线3719隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449701806085"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,968.712,264.675) scale(-1.11111,-0.814815) translate(-1839.72,-592.28)" width="15" x="960.3790115379935" y="252.4523946217128"/></g>
  <g id="140">
   <use class="v6300" height="26" transform="rotate(0,699.82,834.148) scale(1.40192,1.40192) translate(-198.222,-233.92)" width="12" x="691.4081144617019" xlink:href="#Disconnector:手车刀闸2020_0" y="815.9230769230769" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449701412869" ObjectName="#1发电机6912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449701412869"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,699.82,834.148) scale(1.40192,1.40192) translate(-198.222,-233.92)" width="12" x="691.4081144617019" y="815.9230769230769"/></g>
  <g id="147">
   <use class="v6300" height="18" transform="rotate(0,1004.14,755.75) scale(1.11111,-2.09877) translate(-99.7475,-1105.95)" width="12" x="997.4751104919458" xlink:href="#Disconnector:单手车刀闸_0" y="736.8611111111111" zvalue="75"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449701281797" ObjectName="6.3kV母线PT6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449701281797"/></metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1004.14,755.75) scale(1.11111,-2.09877) translate(-99.7475,-1105.95)" width="12" x="997.4751104919458" y="736.8611111111111"/></g>
  <g id="233">
   <use class="v6300" height="27" transform="rotate(0,702.278,553.664) scale(1.51169,1.56768) translate(-234.132,-192.826)" width="14" x="691.6959404990798" xlink:href="#Disconnector:带融断手车刀闸_0" y="532.5" zvalue="121"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449701216261" ObjectName="站用变6811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449701216261"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,702.278,553.664) scale(1.51169,1.56768) translate(-234.132,-192.826)" width="14" x="691.6959404990798" y="532.5"/></g>
  <g id="159">
   <use class="v6300" height="26" transform="rotate(0,1232.02,828.174) scale(1.40192,1.40192) translate(-350.801,-232.207)" width="12" x="1223.611640102728" xlink:href="#Disconnector:手车刀闸2020_0" y="809.949023199023" zvalue="182"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449702920197" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449702920197"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1232.02,828.174) scale(1.40192,1.40192) translate(-350.801,-232.207)" width="12" x="1223.611640102728" y="809.949023199023"/></g>
  <g id="157">
   <use class="v6300" height="26" transform="rotate(0,1375.82,834.148) scale(1.40192,1.40192) translate(-392.027,-233.92)" width="12" x="1367.408114461702" xlink:href="#Disconnector:手车刀闸2020_0" y="815.9230769230769" zvalue="185"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449702854661" ObjectName="#2发电机6922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449702854661"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1375.82,834.148) scale(1.40192,1.40192) translate(-392.027,-233.92)" width="12" x="1367.408114461702" y="815.9230769230769"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="109">
   <path class="v6300" d="M 626.48 633.54 L 626.48 683.56" stroke-width="1" zvalue="26"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="410@2" LinkObjectIDznd="198@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.48 633.54 L 626.48 683.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="v6300" d="M 1026.48 542.21 L 1026.48 495.01" stroke-width="1" zvalue="28"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="453@0" LinkObjectIDznd="414@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1026.48 542.21 L 1026.48 495.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="v6300" d="M 626.48 733.75 L 626.48 855.93" stroke-width="1" zvalue="33"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@1" LinkObjectIDznd="212@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.48 733.75 L 626.48 855.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv35" d="M 1027.5 349.1 L 1027.5 276.49" stroke-width="1" zvalue="42"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="418@0" LinkObjectIDznd="206@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1027.5 349.1 L 1027.5 276.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv35" d="M 1027.53 252.66 L 1027.53 188.52" stroke-width="1" zvalue="43"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@1" LinkObjectIDznd="156@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1027.53 252.66 L 1027.53 188.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv35" d="M 1095.97 310.99 L 1027.5 310.99" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="101" MaxPinNum="2"/>
   </metadata>
  <path d="M 1095.97 310.99 L 1027.5 310.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv35" d="M 968.61 308.33 L 968.61 276.49" stroke-width="1" zvalue="51"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="119@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 968.61 308.33 L 968.61 276.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="v6300" d="M 1026.48 583.88 L 1026.48 633.54" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="453@1" LinkObjectIDznd="410@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1026.48 583.88 L 1026.48 633.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv35" d="M 919.8 295.43 L 968.61 295.43" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="95" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.8 295.43 L 968.61 295.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="v6300" d="M 702.28 492.53 L 702.28 533.68" stroke-width="1" zvalue="123"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="399@0" LinkObjectIDznd="233@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 702.28 492.53 L 702.28 533.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="v6300" d="M 702.28 574.04 L 702.28 633.54" stroke-width="1" zvalue="124"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@1" LinkObjectIDznd="410@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 702.28 574.04 L 702.28 633.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="v6300" d="M 1363.17 497.48 L 1363.17 538.53" stroke-width="1" zvalue="129"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@0" LinkObjectIDznd="241@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1363.17 497.48 L 1363.17 538.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="v6300" d="M 1363.17 577.16 L 1363.17 633.54" stroke-width="1" zvalue="130"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="241@1" LinkObjectIDznd="410@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1363.17 577.16 L 1363.17 633.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv35" d="M 1027.59 403.83 L 1027.62 370.34" stroke-width="1" zvalue="158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@0" LinkObjectIDznd="418@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1027.59 403.83 L 1027.62 370.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="v6300" d="M 1004.14 805.39 L 1004.14 772.01" stroke-width="1" zvalue="160"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="148@0" LinkObjectIDznd="147@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004.14 805.39 L 1004.14 772.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="v6300" d="M 1004.14 739.35 L 1004.14 633.54" stroke-width="1" zvalue="161"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="147@1" LinkObjectIDznd="410@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004.14 739.35 L 1004.14 633.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="v6300" d="M 699.94 852.33 L 699.94 878" stroke-width="1" zvalue="168"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="129@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 699.94 852.33 L 699.94 878" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="v6300" d="M 626.48 773 L 767.75 773 L 767.75 860.19" stroke-width="1" zvalue="170"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104" LinkObjectIDznd="133@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.48 773 L 767.75 773 L 767.75 860.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="v6300" d="M 699.82 816.03 L 699.82 773" stroke-width="1" zvalue="171"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@1" LinkObjectIDznd="134" MaxPinNum="2"/>
   </metadata>
  <path d="M 699.82 816.03 L 699.82 773" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="v6300" d="M 556.14 846.35 L 556.14 867.69" stroke-width="1" zvalue="174"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="136@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 556.14 846.35 L 556.14 867.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="v6300" d="M 556.03 810.05 L 556.03 778 L 626.48 778" stroke-width="1" zvalue="175"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@1" LinkObjectIDznd="104" MaxPinNum="2"/>
   </metadata>
  <path d="M 556.03 810.05 L 556.03 778 L 626.48 778" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="v6300" d="M 1302.48 633.54 L 1302.48 683.56" stroke-width="1" zvalue="180"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="410@3" LinkObjectIDznd="161@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1302.48 633.54 L 1302.48 683.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="v6300" d="M 1302.48 733.75 L 1302.48 855.93" stroke-width="1" zvalue="184"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@1" LinkObjectIDznd="162@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1302.48 733.75 L 1302.48 855.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="v6300" d="M 1375.94 852.33 L 1375.94 878" stroke-width="1" zvalue="188"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="155@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1375.94 852.33 L 1375.94 878" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="v6300" d="M 1302.48 773 L 1443.75 773 L 1443.75 860.19" stroke-width="1" zvalue="190"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158" LinkObjectIDznd="153@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1302.48 773 L 1443.75 773 L 1443.75 860.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="v6300" d="M 1375.82 816.03 L 1375.82 773" stroke-width="1" zvalue="191"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@1" LinkObjectIDznd="152" MaxPinNum="2"/>
   </metadata>
  <path d="M 1375.82 816.03 L 1375.82 773" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="v6300" d="M 1232.14 846.35 L 1232.14 867.69" stroke-width="1" zvalue="193"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="150@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1232.14 846.35 L 1232.14 867.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="v6300" d="M 1232.03 810.05 L 1232.03 778 L 1302.48 778" stroke-width="1" zvalue="194"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@1" LinkObjectIDznd="158" MaxPinNum="2"/>
   </metadata>
  <path d="M 1232.03 810.05 L 1232.03 778 L 1302.48 778" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv35" d="M 1027.53 215.5 L 968.64 215.5 L 968.64 252.66" stroke-width="1" zvalue="195"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100" LinkObjectIDznd="119@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1027.53 215.5 L 968.64 215.5 L 968.64 252.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv35" d="M 920.92 234.32 L 968.64 234.32" stroke-width="1" zvalue="196"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="163" MaxPinNum="2"/>
   </metadata>
  <path d="M 920.92 234.32 L 968.64 234.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="v6300" d="M 1058.79 738.29 L 1058.79 700.5 L 1004.14 700.5" stroke-width="1" zvalue="198"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@0" LinkObjectIDznd="118" MaxPinNum="2"/>
   </metadata>
  <path d="M 1058.79 738.29 L 1058.79 700.5 L 1004.14 700.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="120">
   <use class="kv35" height="30" transform="rotate(0,970.236,330) scale(1.43056,1.55556) translate(-285.554,-109.524)" width="30" x="948.7777777777778" xlink:href="#Accessory:避雷器PT带熔断器_0" y="306.6666666666666" zvalue="49"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449701740549" ObjectName="35kV勐弄河一级电站线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,970.236,330) scale(1.43056,1.55556) translate(-285.554,-109.524)" width="30" x="948.7777777777778" y="306.6666666666666"/></g>
  <g id="148">
   <use class="v6300" height="18" transform="rotate(0,1004.14,828.028) scale(2.38426,2.59259) translate(-570.529,-494.312)" width="18" x="982.6842592592592" xlink:href="#Accessory:四卷PT_0" y="804.6944444444445" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449701347333" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1004.14,828.028) scale(2.38426,2.59259) translate(-570.529,-494.312)" width="18" x="982.6842592592592" y="804.6944444444445"/></g>
  <g id="121">
   <use class="v6300" height="26" transform="rotate(0,1058.75,753.75) scale(1.25,1.25) translate(-210.25,-147.5)" width="12" x="1051.25" xlink:href="#Accessory:避雷器_0" y="737.5" zvalue="162"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449702330373" ObjectName="6.3kV母线电压互感器备雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1058.75,753.75) scale(1.25,1.25) translate(-210.25,-147.5)" width="12" x="1051.25" y="737.5"/></g>
  <g id="127">
   <use class="v6300" height="23" transform="rotate(0,1363.17,465.188) scale(2.875,-2.875) translate(-874.959,-605.429)" width="15" x="1341.604166666667" xlink:href="#Accessory:PT1515_0" y="432.125" zvalue="166"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449702395909" ObjectName="近区变"/>
   </metadata>
  <rect fill="white" height="23" opacity="0" stroke="white" transform="rotate(0,1363.17,465.188) scale(2.875,-2.875) translate(-874.959,-605.429)" width="15" x="1341.604166666667" y="432.125"/></g>
  <g id="129">
   <use class="v6300" height="20" transform="rotate(0,699.938,895.25) scale(1.725,1.725) translate(-285.114,-369.014)" width="25" x="678.375" xlink:href="#Accessory:4绕组母线PT_0" y="878" zvalue="167"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449702461445" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,699.938,895.25) scale(1.725,1.725) translate(-285.114,-369.014)" width="25" x="678.375" y="878"/></g>
  <g id="133">
   <use class="v6300" height="29" transform="rotate(0,767.75,878) scale(1.25,-1.25) translate(-149.8,-1576.78)" width="30" x="749" xlink:href="#Accessory:PT12321_0" y="859.875" zvalue="169"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449702526981" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,767.75,878) scale(1.25,-1.25) translate(-149.8,-1576.78)" width="30" x="749" y="859.875"/></g>
  <g id="136">
   <use class="v6300" height="29" transform="rotate(0,556.141,885.5) scale(1.25,-1.25) translate(-107.478,-1590.28)" width="30" x="537.3910256410256" xlink:href="#Accessory:PT12321_0" y="867.375" zvalue="173"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449702592517" ObjectName="#1发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,556.141,885.5) scale(1.25,-1.25) translate(-107.478,-1590.28)" width="30" x="537.3910256410256" y="867.375"/></g>
  <g id="155">
   <use class="v6300" height="20" transform="rotate(0,1375.94,895.25) scale(1.725,1.725) translate(-569.23,-369.014)" width="25" x="1354.375" xlink:href="#Accessory:4绕组母线PT_0" y="878" zvalue="187"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449702789125" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1375.94,895.25) scale(1.725,1.725) translate(-569.23,-369.014)" width="25" x="1354.375" y="878"/></g>
  <g id="153">
   <use class="v6300" height="29" transform="rotate(0,1443.75,878) scale(1.25,-1.25) translate(-285,-1576.78)" width="30" x="1425" xlink:href="#Accessory:PT12321_0" y="859.875" zvalue="189"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449702723589" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1443.75,878) scale(1.25,-1.25) translate(-285,-1576.78)" width="30" x="1425" y="859.875"/></g>
  <g id="150">
   <use class="v6300" height="29" transform="rotate(0,1232.14,885.5) scale(1.25,-1.25) translate(-242.678,-1590.28)" width="30" x="1213.391025641026" xlink:href="#Accessory:PT12321_0" y="867.375" zvalue="192"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449702658053" ObjectName="#2发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1232.14,885.5) scale(1.25,-1.25) translate(-242.678,-1590.28)" width="30" x="1213.391025641026" y="867.375"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124745707525" ObjectName="F"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124745773061" ObjectName="F"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="246" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,158.333,336.278) scale(1,1) translate(0,0)" writing-mode="lr" x="158.49" xml:space="preserve" y="341.19" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124741709829" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124745576453" ObjectName="F"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124745641989" ObjectName="F"/>
   </metadata>
  </g>
  <g id="183">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="183" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124745576453" ObjectName="F"/>
   </metadata>
  </g>
  <g id="181">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="181" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124745576453" ObjectName="F"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="78" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,494.506,667.036) scale(1,1) translate(0,-1.44226e-13)" writing-mode="lr" x="494.04" xml:space="preserve" y="671.8099999999999" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124741513221" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="79">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="79" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,627.725,964.44) scale(1,1) translate(0,-1.05619e-12)" writing-mode="lr" x="627.17" xml:space="preserve" y="969.15" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124740661253" ObjectName="P"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="80" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1303.73,964.44) scale(1,1) translate(0,-1.05619e-12)" writing-mode="lr" x="1303.17" xml:space="preserve" y="969.15" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124744921093" ObjectName="P"/>
   </metadata>
  </g>
  <g id="81">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="81" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,627.725,992.163) scale(1,1) translate(0,-1.08696e-12)" writing-mode="lr" x="627.17" xml:space="preserve" y="996.87" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124740726789" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="82">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="82" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1303.73,992.163) scale(1,1) translate(0,-1.08696e-12)" writing-mode="lr" x="1303.17" xml:space="preserve" y="996.87" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124744986631" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="83">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="83" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,627.725,1019.89) scale(1,1) translate(0,-1.11774e-12)" writing-mode="lr" x="627.17" xml:space="preserve" y="1024.6" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124740792325" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="84">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="84" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1303.73,1019.89) scale(1,1) translate(0,-1.11774e-12)" writing-mode="lr" x="1303.17" xml:space="preserve" y="1024.6" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124745052167" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="85" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1135.03,351.459) scale(1,1) translate(0,3.73868e-14)" writing-mode="lr" x="1134.48" xml:space="preserve" y="356.19" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124741840901" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="86" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1135.03,382.557) scale(1,1) translate(0,4.08394e-14)" writing-mode="lr" x="1134.48" xml:space="preserve" y="387.29" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124741906437" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="87" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1101.28,529.761) scale(1,1) translate(0,5.71822e-14)" writing-mode="lr" x="1100.73" xml:space="preserve" y="534.49" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124741971973" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="88" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1101.28,560.859) scale(1,1) translate(0,6.06349e-14)" writing-mode="lr" x="1100.73" xml:space="preserve" y="565.59" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124742037509" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="89" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1135.03,413.655) scale(1,1) translate(0,4.4292e-14)" writing-mode="lr" x="1134.48" xml:space="preserve" y="418.39" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124742103045" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="90">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="90" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1101.28,591.957) scale(1,1) translate(0,6.40875e-14)" writing-mode="lr" x="1100.73" xml:space="preserve" y="596.6900000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124742430725" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="92" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1025.03,54.9731) scale(1,1) translate(0,-4.8711e-14)" writing-mode="lr" x="1024.56" xml:space="preserve" y="59.65" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124737974279" ObjectName="P"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="94" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1025.03,78.4375) scale(1,1) translate(0,-7.47618e-14)" writing-mode="lr" x="1024.56" xml:space="preserve" y="83.11" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124738039816" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="96" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1025.03,101.902) scale(1,1) translate(0,-1.00813e-13)" writing-mode="lr" x="1024.56" xml:space="preserve" y="106.58" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124738105349" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="211">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="222"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374887288835" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="2">
   <use height="30" transform="rotate(0,238.149,188.016) scale(0.708333,0.665547) translate(93.6864,89.4657)" width="30" x="227.52" xlink:href="#State:红绿圆(方形)_0" y="178.03" zvalue="259"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,238.149,188.016) scale(0.708333,0.665547) translate(93.6864,89.4657)" width="30" x="227.52" y="178.03"/></g>
 </g>
</svg>