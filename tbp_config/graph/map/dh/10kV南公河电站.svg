<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549586427906" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:手车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.45181617405996" x2="1.714850492606708" y1="5.276370517142858" y2="14.05696281619048"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.583333333333333" x2="8.583333333333334" y1="5.166666666666667" y2="14.16666666666667"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV南公河电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="42.43" xlink:href="logo.png" y="45.71"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,177.054,75.7143) scale(1,1) translate(0,0)" writing-mode="lr" x="177.05" xml:space="preserve" y="79.20999999999999" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,178.762,75.4046) scale(1,1) translate(6.70892e-15,0)" writing-mode="lr" x="178.76" xml:space="preserve" y="84.40000000000001" zvalue="3">10kV南公河电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="6" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,75.4375,375) scale(1,1) translate(0,0)" width="72.88" x="39" y="363" zvalue="103"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,75.4375,375) scale(1,1) translate(0,0)" writing-mode="lr" x="75.44" xml:space="preserve" y="379.5" zvalue="103">信号一览</text>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="376.4285714285717" x2="376.4285714285717" y1="13.71428571428578" y2="1043.714285714286" zvalue="4"/>
  <line fill="none" id="28" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.4285714285719" x2="369.4285714285714" y1="149.5847783283681" y2="149.5847783283681" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="161.7142857142857" y2="161.7142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="187.7142857142857" y2="187.7142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="3.428571428571672" y1="161.7142857142857" y2="187.7142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="161.7142857142857" y2="187.7142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="161.7142857142857" y2="161.7142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="187.7142857142857" y2="187.7142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="161.7142857142857" y2="187.7142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285717" x2="365.4285714285717" y1="161.7142857142857" y2="187.7142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="187.7142857142857" y2="187.7142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="211.9642857142857" y2="211.9642857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="3.428571428571672" y1="187.7142857142857" y2="211.9642857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="187.7142857142857" y2="211.9642857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="187.7142857142857" y2="187.7142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="211.9642857142857" y2="211.9642857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="187.7142857142857" y2="211.9642857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285717" x2="365.4285714285717" y1="187.7142857142857" y2="211.9642857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="211.9642857142857" y2="211.9642857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="234.7142857142857" y2="234.7142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="3.428571428571672" y1="211.9642857142857" y2="234.7142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="211.9642857142857" y2="234.7142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="211.9642857142857" y2="211.9642857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="234.7142857142857" y2="234.7142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="211.9642857142857" y2="234.7142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285717" x2="365.4285714285717" y1="211.9642857142857" y2="234.7142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="234.7142857142857" y2="234.7142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="257.4642857142857" y2="257.4642857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="3.428571428571672" y1="234.7142857142857" y2="257.4642857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="234.7142857142857" y2="257.4642857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="234.7142857142857" y2="234.7142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="257.4642857142857" y2="257.4642857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="234.7142857142857" y2="257.4642857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285717" x2="365.4285714285717" y1="234.7142857142857" y2="257.4642857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="257.4642857142857" y2="257.4642857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="280.2142857142857" y2="280.2142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="3.428571428571672" y1="257.4642857142857" y2="280.2142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="257.4642857142857" y2="280.2142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="257.4642857142857" y2="257.4642857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="280.2142857142857" y2="280.2142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="257.4642857142857" y2="280.2142857142857"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285717" x2="365.4285714285717" y1="257.4642857142857" y2="280.2142857142857"/>
  <line fill="none" id="26" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.4285714285719" x2="369.4285714285714" y1="619.5847783283682" y2="619.5847783283682" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="92.42857142857167" y1="934.714285714286" y2="934.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="92.42857142857167" y1="973.8775857142859" y2="973.8775857142859"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="2.428571428571672" y1="934.714285714286" y2="973.8775857142859"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="92.42857142857167" y1="934.714285714286" y2="973.8775857142859"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="362.4285714285717" y1="934.714285714286" y2="934.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="362.4285714285717" y1="973.8775857142859" y2="973.8775857142859"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="92.42857142857167" y1="934.714285714286" y2="973.8775857142859"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362.4285714285717" x2="362.4285714285717" y1="934.714285714286" y2="973.8775857142859"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="92.42857142857167" y1="973.877555714286" y2="973.877555714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="92.42857142857167" y1="1001.795955714286" y2="1001.795955714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="2.428571428571672" y1="973.877555714286" y2="1001.795955714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="92.42857142857167" y1="973.877555714286" y2="1001.795955714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="182.4285714285717" y1="973.877555714286" y2="973.877555714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="182.4285714285717" y1="1001.795955714286" y2="1001.795955714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="92.42857142857167" y1="973.877555714286" y2="1001.795955714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285717" x2="182.4285714285717" y1="973.877555714286" y2="1001.795955714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285718" x2="272.4285714285718" y1="973.877555714286" y2="973.877555714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285718" x2="272.4285714285718" y1="1001.795955714286" y2="1001.795955714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285718" x2="182.4285714285718" y1="973.877555714286" y2="1001.795955714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285718" x2="272.4285714285718" y1="973.877555714286" y2="1001.795955714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285717" x2="362.4285714285717" y1="973.877555714286" y2="973.877555714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285717" x2="362.4285714285717" y1="1001.795955714286" y2="1001.795955714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285717" x2="272.4285714285717" y1="973.877555714286" y2="1001.795955714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362.4285714285717" x2="362.4285714285717" y1="973.877555714286" y2="1001.795955714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="92.42857142857167" y1="1001.795885714286" y2="1001.795885714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="92.42857142857167" y1="1029.714285714286" y2="1029.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="2.428571428571672" y1="1001.795885714286" y2="1029.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="92.42857142857167" y1="1001.795885714286" y2="1029.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="182.4285714285717" y1="1001.795885714286" y2="1001.795885714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="182.4285714285717" y1="1029.714285714286" y2="1029.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="92.42857142857167" y1="1001.795885714286" y2="1029.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285717" x2="182.4285714285717" y1="1001.795885714286" y2="1029.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285718" x2="272.4285714285718" y1="1001.795885714286" y2="1001.795885714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285718" x2="272.4285714285718" y1="1029.714285714286" y2="1029.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285718" x2="182.4285714285718" y1="1001.795885714286" y2="1029.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285718" x2="272.4285714285718" y1="1001.795885714286" y2="1029.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285717" x2="362.4285714285717" y1="1001.795885714286" y2="1001.795885714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285717" x2="362.4285714285717" y1="1029.714285714286" y2="1029.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285717" x2="272.4285714285717" y1="1001.795885714286" y2="1029.714285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362.4285714285717" x2="362.4285714285717" y1="1001.795885714286" y2="1029.714285714286"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.4286,954.714) scale(1,1) translate(0,0)" writing-mode="lr" x="47.43" xml:space="preserve" y="960.71" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.4286,988.714) scale(1,1) translate(0,0)" writing-mode="lr" x="44.43" xml:space="preserve" y="994.71" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.429,988.714) scale(1,1) translate(0,0)" writing-mode="lr" x="226.43" xml:space="preserve" y="994.71" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,43.4286,1016.71) scale(1,1) translate(0,0)" writing-mode="lr" x="43.43" xml:space="preserve" y="1022.71" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225.429,1016.71) scale(1,1) translate(0,0)" writing-mode="lr" x="225.43" xml:space="preserve" y="1022.71" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.9286,649.214) scale(1,1) translate(0,-2.79871e-13)" writing-mode="lr" x="67.92857142857167" xml:space="preserve" y="653.7142857142858" zvalue="18">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.483,956.714) scale(1,1) translate(0,0)" writing-mode="lr" x="227.48" xml:space="preserve" y="962.71" zvalue="26">NanGongHe-01-2022</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,137.483,1016.71) scale(1,1) translate(0,0)" writing-mode="lr" x="137.48" xml:space="preserve" y="1022.71" zvalue="27">李文杰</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,41.4286,175.714) scale(1,1) translate(0,0)" writing-mode="lr" x="41.43" xml:space="preserve" y="181.21" zvalue="29">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,221.429,175.714) scale(1,1) translate(0,0)" writing-mode="lr" x="221.43" xml:space="preserve" y="181.21" zvalue="30">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.6161,247.714) scale(1,1) translate(0,0)" writing-mode="lr" x="48.62" xml:space="preserve" y="252.21" zvalue="31">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.512,202.409) scale(1,1) translate(0,0)" writing-mode="lr" x="233.51" xml:space="preserve" y="206.91" zvalue="32">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1460,659) scale(1,1) translate(-1.26965e-12,0)" writing-mode="lr" x="1460" xml:space="preserve" y="663.5" zvalue="35">0.4kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1355.57,281.286) scale(1,1) translate(0,-4.2244e-13)" writing-mode="lr" x="1355.57" xml:space="preserve" y="285.79" zvalue="36">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,724.286,137.929) scale(1,1) translate(-1.4306e-13,0)" writing-mode="lr" x="724.29" xml:space="preserve" y="142.43" zvalue="37">10kV弄香线南公河电站支线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" x="902.7890625" xml:space="preserve" y="488.1785714285714" zvalue="41">#1主变           </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="902.7890625" xml:space="preserve" y="504.1785714285714" zvalue="41">1600KVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" x="765.71875" xml:space="preserve" y="997.8883940832956" zvalue="48">#1发电机      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="765.71875" xml:space="preserve" y="1013.888394083296" zvalue="48">630KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,865.571,601.857) scale(1,1) translate(0,0)" writing-mode="lr" x="865.5700000000001" xml:space="preserve" y="606.36" zvalue="50">401</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,872.786,386) scale(1,1) translate(9.56536e-14,0)" writing-mode="lr" x="872.79" xml:space="preserve" y="390.5" zvalue="53">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,868.179,325) scale(1,1) translate(-1.89182e-13,0)" writing-mode="lr" x="868.1799999999999" xml:space="preserve" y="329.5" zvalue="56">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1007.85,206.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1007.85" xml:space="preserve" y="210.64" zvalue="60">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1098.86,147.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1098.86" xml:space="preserve" y="152.21" zvalue="63">7</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1103.57,209.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1103.57" xml:space="preserve" y="213.64" zvalue="65">0</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,786.286,835.857) scale(1,1) translate(0,0)" writing-mode="lr" x="786.29" xml:space="preserve" y="840.36" zvalue="67">431</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,745.143,746.714) scale(1,1) translate(0,0)" writing-mode="lr" x="745.14" xml:space="preserve" y="751.21" zvalue="71">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1185.71,574.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1185.71" xml:space="preserve" y="578.64" zvalue="74">4901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" x="1337.5625" xml:space="preserve" y="1003.957590511867" zvalue="78">#2发电机             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1337.5625" xml:space="preserve" y="1019.957590511867" zvalue="78">630KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1357,833) scale(1,1) translate(0,0)" writing-mode="lr" x="1357" xml:space="preserve" y="837.5" zvalue="80">432</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1315.86,739.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1315.86" xml:space="preserve" y="744.36" zvalue="83">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.3571,200.643) scale(1,1) translate(0,0)" writing-mode="lr" x="52.36" xml:space="preserve" y="205.14" zvalue="87">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,191.211,375.591) scale(1,1) translate(0,0)" writing-mode="lr" x="191.21" xml:space="preserve" y="380.09" zvalue="99">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,296.211,375.591) scale(1,1) translate(0,0)" writing-mode="lr" x="296.21" xml:space="preserve" y="380.09" zvalue="100">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="10kV南公河电站.svg"><rect fill-opacity="0" height="24" width="72.88" x="39" y="363" zvalue="103"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="33">
   <path class="v400" d="M 511.43 678 L 1498.33 678" stroke-width="6" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674243051524" ObjectName="0.4kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674243051524"/></metadata>
  <path d="M 511.43 678 L 1498.33 678" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv10" d="M 515.71 282.29 L 1312.86 282.29" stroke-width="6" zvalue="35"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674243117060" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674243117060"/></metadata>
  <path d="M 515.71 282.29 L 1312.86 282.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="35">
   <use class="kv10" height="30" transform="rotate(0,722.857,177.714) scale(1.78571,1.7619) translate(-313.343,-65.4208)" width="12" x="712.1428571428571" xlink:href="#EnergyConsumer:负荷_0" y="151.2857142857143" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449795719174" ObjectName="10kV弄香线南公河电站支线"/>
   <cge:TPSR_Ref TObjectID="6192449795719174"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,722.857,177.714) scale(1.78571,1.7619) translate(-313.343,-65.4208)" width="12" x="712.1428571428571" y="151.2857142857143"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="36">
   <path class="kv10" d="M 722.86 201.5 L 722.86 282.29" stroke-width="1" zvalue="37"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="34@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 722.86 201.5 L 722.86 282.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="v400" d="M 838.43 536.22 L 838.5 584.41" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@1" LinkObjectIDznd="47@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 838.43 536.22 L 838.5 584.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="v400" d="M 838.71 621.28 L 838.71 678" stroke-width="1" zvalue="51"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@1" LinkObjectIDznd="33@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 838.71 621.28 L 838.71 678" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv10" d="M 1041.4 137.98 L 1041.4 191.95" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="56@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1041.4 137.98 L 1041.4 191.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv10" d="M 1041.37 222.59 L 1041.37 282.29" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@1" LinkObjectIDznd="34@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1041.37 222.59 L 1041.37 282.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv10" d="M 1086.36 170.79 L 1041.4 170.79" stroke-width="1" zvalue="63"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="57" MaxPinNum="2"/>
   </metadata>
  <path d="M 1086.36 170.79 L 1041.4 170.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv10" d="M 1090.36 229.36 L 1041.37 229.36" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="58" MaxPinNum="2"/>
   </metadata>
  <path d="M 1090.36 229.36 L 1041.37 229.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="v400" d="M 1155.26 492.27 L 1155.26 556.62" stroke-width="1" zvalue="74"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@0" LinkObjectIDznd="69@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1155.26 492.27 L 1155.26 556.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="v400" d="M 1155.14 593.61 L 1155.14 678" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@1" LinkObjectIDznd="33@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1155.14 593.61 L 1155.14 678" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="v400" d="M 1332.14 923.82 L 1332.14 859.71" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="88@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1332.14 923.82 L 1332.14 859.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="v400" d="M 760.84 732.52 L 760.84 678" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="33@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 760.84 732.52 L 760.84 678" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="v400" d="M 1332.76 807.57 L 1332.76 756.3" stroke-width="1" zvalue="93"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@0" LinkObjectIDznd="86@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1332.76 807.57 L 1332.76 756.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="v400" d="M 1331.55 725.66 L 1331.55 678" stroke-width="1" zvalue="94"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="33@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1331.55 725.66 L 1331.55 678" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="v400" d="M 760.8 763.16 L 760.8 810.43" stroke-width="1" zvalue="96"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@1" LinkObjectIDznd="63@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 760.8 763.16 L 760.8 810.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="v400" d="M 762.05 862.57 L 762.05 918.68" stroke-width="1" zvalue="97"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@1" LinkObjectIDznd="45@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 762.05 862.57 L 762.05 918.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="kv10" d="M 838.48 447.67 L 838.48 401.42" stroke-width="1" zvalue="106"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@0" LinkObjectIDznd="50@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 838.48 447.67 L 838.48 401.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv10" d="M 837.93 364.55 L 837.93 341.45" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="53@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 837.93 364.55 L 837.93 341.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv10" d="M 837.7 310.81 L 837.7 282.29" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="34@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 837.7 310.81 L 837.7 282.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="37">
   <use class="kv10" height="18" transform="rotate(0,1043.93,115.286) scale(2.71429,-2.71429) translate(-646.466,-142.331)" width="15" x="1023.571428571429" xlink:href="#Accessory:PT8_0" y="90.85714285714278" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449795784709" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1043.93,115.286) scale(2.71429,-2.71429) translate(-646.466,-142.331)" width="15" x="1023.571428571429" y="90.85714285714278"/></g>
  <g id="42">
   <use class="v400" height="18" transform="rotate(0,1157.79,469.571) scale(2.71429,-2.71429) translate(-718.376,-627.143)" width="15" x="1137.428571428571" xlink:href="#Accessory:PT8_0" y="445.1428571428571" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449795850245" ObjectName="0.4kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1157.79,469.571) scale(2.71429,-2.71429) translate(-718.376,-627.143)" width="15" x="1137.428571428571" y="445.1428571428571"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="39">
   <g id="390">
    <use class="kv10" height="60" transform="rotate(0,838.429,491.857) scale(1.35,1.5) translate(-210.37,-148.952)" width="40" x="811.4299999999999" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="446.86" zvalue="40"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874436247556" ObjectName="10"/>
    </metadata>
   </g>
   <g id="391">
    <use class="v400" height="60" transform="rotate(0,838.429,491.857) scale(1.35,1.5) translate(-210.37,-148.952)" width="40" x="811.4299999999999" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="446.86" zvalue="40"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874436313092" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399450361860" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399450361860"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,838.429,491.857) scale(1.35,1.5) translate(-210.37,-148.952)" width="40" x="811.4299999999999" y="446.86"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="45">
   <use class="v400" height="30" transform="rotate(0,761.429,950.286) scale(2.14286,2.14286) translate(-388.952,-489.676)" width="30" x="729.2857142857144" xlink:href="#Generator:发电机_0" y="918.1428571428571" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449795915781" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449795915781"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,761.429,950.286) scale(2.14286,2.14286) translate(-388.952,-489.676)" width="30" x="729.2857142857144" y="918.1428571428571"/></g>
  <g id="89">
   <use class="v400" height="30" transform="rotate(0,1332.14,955.429) scale(2.14286,2.14286) translate(-693.333,-492.419)" width="30" x="1300" xlink:href="#Generator:发电机_0" y="923.2857142857141" zvalue="77"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449796571142" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449796571142"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1332.14,955.429) scale(2.14286,2.14286) translate(-693.333,-492.419)" width="30" x="1300" y="923.2857142857141"/></g>
 </g>
 <g id="BreakerClass">
  <g id="47">
   <use class="v400" height="20" transform="rotate(0,838.571,602.857) scale(2.14286,1.92857) translate(-441.524,-280.979)" width="10" x="827.8571428571429" xlink:href="#Breaker:开关_0" y="583.5714285714286" zvalue="49"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924510351365" ObjectName="#1主变0.4kV侧401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924510351365"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,838.571,602.857) scale(2.14286,1.92857) translate(-441.524,-280.979)" width="10" x="827.8571428571429" y="583.5714285714286"/></g>
  <g id="50">
   <use class="kv10" height="20" transform="rotate(0,838,383) scale(2.14286,1.92857) translate(-441.219,-175.122)" width="10" x="827.2857142857142" xlink:href="#Breaker:开关_0" y="363.7142857142858" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924510416901" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924510416901"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,838,383) scale(2.14286,1.92857) translate(-441.219,-175.122)" width="10" x="827.2857142857142" y="363.7142857142858"/></g>
  <g id="63">
   <use class="v400" height="20" transform="rotate(0,762.049,836.857) scale(1.73302,2.85714) translate(-318.661,-525.386)" width="10" x="753.3840749414521" xlink:href="#Breaker:手车开关_0" y="808.2857142857142" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924510482437" ObjectName="#1发电机431断路器"/>
   <cge:TPSR_Ref TObjectID="6473924510482437"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,762.049,836.857) scale(1.73302,2.85714) translate(-318.661,-525.386)" width="10" x="753.3840749414521" y="808.2857142857142"/></g>
  <g id="88">
   <use class="v400" height="20" transform="rotate(0,1332.76,834) scale(1.73302,2.85714) translate(-560.058,-523.529)" width="10" x="1324.098360655738" xlink:href="#Breaker:手车开关_0" y="805.4285714285713" zvalue="79"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924510547973" ObjectName="#2发电机432断路器"/>
   <cge:TPSR_Ref TObjectID="6473924510547973"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1332.76,834) scale(1.73302,2.85714) translate(-560.058,-523.529)" width="10" x="1324.098360655738" y="805.4285714285713"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="53">
   <use class="kv10" height="30" transform="rotate(0,837.571,326) scale(1.42857,1.04762) translate(-248.057,-14.1039)" width="15" x="826.8571428571429" xlink:href="#Disconnector:刀闸_0" y="310.2857142857143" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449795981318" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449795981318"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,837.571,326) scale(1.42857,1.04762) translate(-248.057,-14.1039)" width="15" x="826.8571428571429" y="310.2857142857143"/></g>
  <g id="56">
   <use class="kv10" height="30" transform="rotate(0,1041.28,207.143) scale(1.42857,1.04762) translate(-309.169,-8.7013)" width="15" x="1030.56482997589" xlink:href="#Disconnector:刀闸_0" y="191.4285714285715" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449796046854" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449796046854"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1041.28,207.143) scale(1.42857,1.04762) translate(-309.169,-8.7013)" width="15" x="1030.56482997589" y="191.4285714285715"/></g>
  <g id="66">
   <use class="v400" height="30" transform="rotate(0,760.714,747.714) scale(1.42857,1.04762) translate(-225,-33.2727)" width="15" x="750.0000000000001" xlink:href="#Disconnector:刀闸_0" y="732" zvalue="70"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449796374534" ObjectName="#1发电机4311隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449796374534"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,760.714,747.714) scale(1.42857,1.04762) translate(-225,-33.2727)" width="15" x="750.0000000000001" y="732"/></g>
  <g id="69">
   <use class="v400" height="26" transform="rotate(0,1155.14,575.143) scale(1.42857,-1.42857) translate(-343.971,-972.171)" width="12" x="1146.570129728669" xlink:href="#Disconnector:单手车刀闸1212_0" y="556.5714285714286" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449796440070" ObjectName="0.4kV母线电压互感器4901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449796440070"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1155.14,575.143) scale(1.42857,-1.42857) translate(-343.971,-972.171)" width="12" x="1146.570129728669" y="556.5714285714286"/></g>
  <g id="86">
   <use class="v400" height="30" transform="rotate(0,1331.43,740.857) scale(1.42857,1.04762) translate(-396.214,-32.961)" width="15" x="1320.714285714286" xlink:href="#Disconnector:刀闸_0" y="725.1428571428571" zvalue="82"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449796505606" ObjectName="#2发电机4321隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449796505606"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1331.43,740.857) scale(1.42857,1.04762) translate(-396.214,-32.961)" width="15" x="1320.714285714286" y="725.1428571428571"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="59">
   <use class="kv10" height="20" transform="rotate(270,1100.29,170.857) scale(1.42857,1.42857) translate(-327.943,-46.9714)" width="10" x="1093.142857142857" xlink:href="#GroundDisconnector:地刀_0" y="156.5714285714286" zvalue="62"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449796177926" ObjectName="10kV母线电压互感器09017接地刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449796177926"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1100.29,170.857) scale(1.42857,1.42857) translate(-327.943,-46.9714)" width="10" x="1093.142857142857" y="156.5714285714286"/></g>
  <g id="61">
   <use class="kv10" height="20" transform="rotate(270,1104.29,229.429) scale(1.42857,1.42857) translate(-329.143,-64.5429)" width="10" x="1097.142857142857" xlink:href="#GroundDisconnector:地刀_0" y="215.1428571428571" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449796308998" ObjectName="10kV母线电压互感器09010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449796308998"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1104.29,229.429) scale(1.42857,1.42857) translate(-329.143,-64.5429)" width="10" x="1097.142857142857" y="215.1428571428571"/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,323.485,376.107) scale(0.708333,0.665547) translate(128.825,183.986)" width="30" x="312.86" xlink:href="#State:红绿圆(方形)_0" y="366.12" zvalue="101"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,323.485,376.107) scale(0.708333,0.665547) translate(128.825,183.986)" width="30" x="312.86" y="366.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,227.86,376.107) scale(0.708333,0.665547) translate(89.4498,183.986)" width="30" x="217.24" xlink:href="#State:红绿圆(方形)_0" y="366.12" zvalue="102"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,227.86,376.107) scale(0.708333,0.665547) translate(89.4498,183.986)" width="30" x="217.24" y="366.12"/></g>
 </g>
</svg>