<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549586296834" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV芒究电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="43.75" xlink:href="logo.png" y="45.25"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,178.375,75.25) scale(1,1) translate(0,0)" writing-mode="lr" x="178.37" xml:space="preserve" y="78.75" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,180.083,74.9403) scale(1,1) translate(6.85563e-15,0)" writing-mode="lr" x="180.08" xml:space="preserve" y="83.94" zvalue="3">10kV芒究电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="5" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,52.4375,330) scale(1,1) translate(0,0)" width="72.88" x="16" y="318" zvalue="83"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.4375,330) scale(1,1) translate(0,0)" writing-mode="lr" x="52.44" xml:space="preserve" y="334.5" zvalue="83">信号一览</text>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.75" x2="377.75" y1="13.25" y2="1043.25" zvalue="4"/>
  <line fill="none" id="28" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.750000000000227" x2="370.7499999999998" y1="149.1204926140824" y2="149.1204926140824" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="161.2500000000001" y2="161.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="187.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="4.75" y1="161.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="161.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="161.2500000000001" y2="161.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="187.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="161.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.75" x2="366.75" y1="161.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="187.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="211.5000000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="4.75" y1="187.2500000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="187.2500000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="187.2500000000001" y2="187.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="211.5000000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="187.2500000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.75" x2="366.75" y1="187.2500000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="211.5000000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="234.2500000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="4.75" y1="211.5000000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="211.5000000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="211.5000000000001" y2="211.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="234.2500000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="211.5000000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.75" x2="366.75" y1="211.5000000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="234.2500000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="257.0000000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="4.75" y1="234.2500000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="234.2500000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="234.2500000000001" y2="234.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="257.0000000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="234.2500000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.75" x2="366.75" y1="234.2500000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="257.0000000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="185.75" y1="279.7500000000001" y2="279.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.75" x2="4.75" y1="257.0000000000001" y2="279.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="257.0000000000001" y2="279.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="257.0000000000001" y2="257.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="366.75" y1="279.7500000000001" y2="279.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.75" x2="185.75" y1="257.0000000000001" y2="279.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.75" x2="366.75" y1="257.0000000000001" y2="279.7500000000001"/>
  <line fill="none" id="26" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.750000000000227" x2="370.7499999999998" y1="619.1204926140825" y2="619.1204926140825" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="934.2500000000002" y2="934.2500000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="973.4133000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="934.2500000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="934.2500000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="363.75" y1="934.2500000000002" y2="934.2500000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="363.75" y1="973.4133000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="934.2500000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="934.2500000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="973.4132700000002" y2="973.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1001.33167" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="973.4132700000002" y2="973.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1001.33167" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.75" x2="183.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="973.4132700000002" y2="973.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1001.33167" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="183.7500000000001" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.7500000000001" x2="273.7500000000001" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="973.4132700000002" y2="973.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1001.33167" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="273.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1001.3316" y2="1001.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="93.75" y1="1029.25" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.75" x2="3.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1001.3316" y2="1001.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="183.75" y1="1029.25" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.75" x2="93.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.75" x2="183.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1001.3316" y2="1001.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="273.7500000000001" y1="1029.25" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.7500000000001" x2="183.7500000000001" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.7500000000001" x2="273.7500000000001" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1001.3316" y2="1001.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="363.75" y1="1029.25" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.75" x2="273.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.75" x2="363.75" y1="1001.3316" y2="1029.25"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.75,954.25) scale(1,1) translate(0,0)" writing-mode="lr" x="48.75" xml:space="preserve" y="960.25" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.75,988.25) scale(1,1) translate(0,0)" writing-mode="lr" x="45.75" xml:space="preserve" y="994.25" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.75,988.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.75" xml:space="preserve" y="994.25" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.75,1016.25) scale(1,1) translate(0,0)" writing-mode="lr" x="44.75" xml:space="preserve" y="1022.25" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.75,1016.25) scale(1,1) translate(0,0)" writing-mode="lr" x="226.75" xml:space="preserve" y="1022.25" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.25,648.75) scale(1,1) translate(0,2.09749e-13)" writing-mode="lr" x="69.25" xml:space="preserve" y="653.2500000000001" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.804,956.25) scale(1,1) translate(0,0)" writing-mode="lr" x="228.8" xml:space="preserve" y="962.25" zvalue="27">MangJiu-01-2017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,134.804,1015.25) scale(1,1) translate(0,0)" writing-mode="lr" x="134.8" xml:space="preserve" y="1021.25" zvalue="28">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,42.75,175.25) scale(1,1) translate(0,0)" writing-mode="lr" x="42.75" xml:space="preserve" y="180.75" zvalue="30">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.75,175.25) scale(1,1) translate(0,0)" writing-mode="lr" x="222.75" xml:space="preserve" y="180.75" zvalue="31">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.7639,199.944) scale(1,1) translate(0,0)" writing-mode="lr" x="54.76" xml:space="preserve" y="204.44" zvalue="32">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.9375,247.25) scale(1,1) translate(0,0)" writing-mode="lr" x="49.94" xml:space="preserve" y="251.75" zvalue="33">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,529.857,688.429) scale(1,1) translate(0,0)" writing-mode="lr" x="529.86" xml:space="preserve" y="692.9299999999999" zvalue="35">0.4kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,505.571,315.571) scale(1,1) translate(0,0)" writing-mode="lr" x="505.57" xml:space="preserve" y="320.07" zvalue="36">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" x="723.8515625" xml:space="preserve" y="999.9618490134186" zvalue="39">#1发电机         </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="723.8515625" xml:space="preserve" y="1015.961849013419" zvalue="39">400KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,700.578,748.505) scale(1,1) translate(0,0)" writing-mode="lr" x="700.58" xml:space="preserve" y="753" zvalue="41">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,745.184,826.926) scale(1,1) translate(0,0)" writing-mode="lr" x="745.1799999999999" xml:space="preserve" y="831.4299999999999" zvalue="45">451</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" x="1299.8359375" xml:space="preserve" y="1003.961849013419" zvalue="49">#2发电机         </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1299.8359375" xml:space="preserve" y="1019.961849013419" zvalue="49">200KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1276.58,752.505) scale(1,1) translate(0,0)" writing-mode="lr" x="1276.58" xml:space="preserve" y="757" zvalue="51">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1321.18,830.926) scale(1,1) translate(0,0)" writing-mode="lr" x="1321.18" xml:space="preserve" y="835.4299999999999" zvalue="55">452</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" x="729" xml:space="preserve" y="510.75" zvalue="58">#1主变      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="729" xml:space="preserve" y="526.75" zvalue="58">500KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,825.75,404) scale(1,1) translate(0,0)" writing-mode="lr" x="825.75" xml:space="preserve" y="408.5" zvalue="60">0011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" x="1184" xml:space="preserve" y="508.25" zvalue="64">#2主变       </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1184" xml:space="preserve" y="524.25" zvalue="64">630KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1271.42,405.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1271.42" xml:space="preserve" y="410" zvalue="68">0021</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,985,65) scale(1,1) translate(0,0)" writing-mode="lr" x="985" xml:space="preserve" y="69.5" zvalue="71">10kV东勐线芒究T线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.833,201.944) scale(1,1) translate(0,0)" writing-mode="lr" x="234.83" xml:space="preserve" y="206.44" zvalue="73">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.222,246.389) scale(1,1) translate(0,0)" writing-mode="lr" x="233.22" xml:space="preserve" y="250.89" zvalue="75">10kV#2变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,168.211,330.591) scale(1,1) translate(0,0)" writing-mode="lr" x="168.21" xml:space="preserve" y="335.09" zvalue="79">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,273.211,330.591) scale(1,1) translate(0,0)" writing-mode="lr" x="273.21" xml:space="preserve" y="335.09" zvalue="80">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="10kV芒究电站.svg"><rect fill-opacity="0" height="24" width="72.88" x="16" y="318" zvalue="83"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="33">
   <path class="v400" d="M 615.71 692.29 L 1565.71 692.29" stroke-width="6" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674242854916" ObjectName="0.4kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674242854916"/></metadata>
  <path d="M 615.71 692.29 L 1565.71 692.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv10" d="M 552.86 318 L 1568.57 318" stroke-width="6" zvalue="35"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674242920452" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674242920452"/></metadata>
  <path d="M 552.86 318 L 1568.57 318" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v400" height="30" transform="rotate(0,719.062,945.148) scale(1.85899,1.85899) translate(-319.375,-423.843)" width="30" x="691.1770967546338" xlink:href="#Generator:发电机_0" y="917.2629266475365" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449794932741" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449794932741"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,719.062,945.148) scale(1.85899,1.85899) translate(-319.375,-423.843)" width="30" x="691.1770967546338" y="917.2629266475365"/></g>
  <g id="51">
   <use class="v400" height="30" transform="rotate(0,1295.06,949.148) scale(1.85899,1.85899) translate(-585.529,-425.691)" width="30" x="1267.177096754634" xlink:href="#Generator:发电机_0" y="921.2629266475365" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449795063814" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449795063814"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1295.06,949.148) scale(1.85899,1.85899) translate(-585.529,-425.691)" width="30" x="1267.177096754634" y="921.2629266475365"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="165">
   <use class="v400" height="30" transform="rotate(0,719.797,749.505) scale(1.9625,1.2338) translate(-345.802,-138.519)" width="15" x="705.077807004066" xlink:href="#Disconnector:刀闸_0" y="730.9980158730157" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449794867205" ObjectName="#1发电机4511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449794867205"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,719.797,749.505) scale(1.9625,1.2338) translate(-345.802,-138.519)" width="15" x="705.077807004066" y="730.9980158730157"/></g>
  <g id="50">
   <use class="v400" height="30" transform="rotate(0,1295.8,753.505) scale(1.9625,1.2338) translate(-628.299,-139.277)" width="15" x="1281.077807004066" xlink:href="#Disconnector:刀闸_0" y="734.9980158730157" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449794998278" ObjectName="#2发电机4521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449794998278"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1295.8,753.505) scale(1.9625,1.2338) translate(-628.299,-139.277)" width="15" x="1281.077807004066" y="734.9980158730157"/></g>
  <g id="55">
   <use class="kv10" height="30" transform="rotate(0,782,410) scale(1,1) translate(0,0)" width="15" x="774.5" xlink:href="#Disconnector:令克_0" y="395" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449795129350" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449795129350"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,782,410) scale(1,1) translate(0,0)" width="15" x="774.5" y="395"/></g>
  <g id="63">
   <use class="kv10" height="30" transform="rotate(0,1232.67,410) scale(1,1) translate(0,0)" width="15" x="1225.166541108969" xlink:href="#Disconnector:令克_0" y="395" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449795194886" ObjectName="#2主变10kV侧0021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449795194886"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1232.67,410) scale(1,1) translate(0,0)" width="15" x="1225.166541108969" y="395"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="43">
   <path class="v400" d="M 719.97 731.61 L 719.97 692.29" stroke-width="1" zvalue="42"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="33@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 719.97 731.61 L 719.97 692.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="v400" d="M 719.06 917.73 L 719.06 836.23" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="170@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 719.06 917.73 L 719.06 836.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="v400" d="M 720.69 814.99 L 720.69 767.7" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="165@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 720.69 814.99 L 720.69 767.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="v400" d="M 1295.97 735.61 L 1295.97 692.29" stroke-width="1" zvalue="52"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="33@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1295.97 735.61 L 1295.97 692.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="v400" d="M 1295.06 921.73 L 1295.06 840.23" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1295.06 921.73 L 1295.06 840.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="v400" d="M 1296.69 818.99 L 1296.69 771.7" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@0" LinkObjectIDznd="50@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1296.69 818.99 L 1296.69 771.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="v400" d="M 780.4 544.52 L 780.4 692.29" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@1" LinkObjectIDznd="33@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 780.4 544.52 L 780.4 692.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv10" d="M 781.92 422.25 L 781.92 477.61" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@1" LinkObjectIDznd="52@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.92 422.25 L 781.92 477.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv10" d="M 782.08 396.75 L 782.08 318" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="34@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 782.08 396.75 L 782.08 318" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="v400" d="M 1232.54 544.52 L 1232.54 692.29" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@1" LinkObjectIDznd="33@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1232.54 544.52 L 1232.54 692.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv10" d="M 982 125.32 L 982 318" stroke-width="1" zvalue="71"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="66@0" LinkObjectIDznd="34@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 982 125.32 L 982 318" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 1232.75 396.75 L 1232.75 318" stroke-width="1" zvalue="84"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="34@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1232.75 396.75 L 1232.75 318" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv10" d="M 1232.58 422.25 L 1232.58 477.61" stroke-width="1" zvalue="85"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@1" LinkObjectIDznd="65@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1232.58 422.25 L 1232.58 477.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="170">
   <use class="v400" height="20" transform="rotate(0,720.727,825.619) scale(1.22222,1.11111) translate(-129.93,-81.4508)" width="10" x="714.6154525852342" xlink:href="#Breaker:开关_0" y="814.507631257631" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924510023685" ObjectName="#1发电机451断路器"/>
   <cge:TPSR_Ref TObjectID="6473924510023685"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,720.727,825.619) scale(1.22222,1.11111) translate(-129.93,-81.4508)" width="10" x="714.6154525852342" y="814.507631257631"/></g>
  <g id="48">
   <use class="v400" height="20" transform="rotate(0,1296.73,829.619) scale(1.22222,1.11111) translate(-234.657,-81.8508)" width="10" x="1290.615452585234" xlink:href="#Breaker:开关_0" y="818.507631257631" zvalue="53"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924510089221" ObjectName="#2发电机452断路器"/>
   <cge:TPSR_Ref TObjectID="6473924510089221"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1296.73,829.619) scale(1.22222,1.11111) translate(-234.657,-81.8508)" width="10" x="1290.615452585234" y="818.507631257631"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="52">
   <g id="520">
    <use class="kv10" height="60" transform="rotate(0,780.4,511) scale(1.02,1.13333) translate(-14.902,-56.1176)" width="40" x="760" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="477" zvalue="57"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874435854340" ObjectName="10"/>
    </metadata>
   </g>
   <g id="521">
    <use class="v400" height="60" transform="rotate(0,780.4,511) scale(1.02,1.13333) translate(-14.902,-56.1176)" width="40" x="760" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="477" zvalue="57"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874435919876" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399450165252" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399450165252"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,780.4,511) scale(1.02,1.13333) translate(-14.902,-56.1176)" width="40" x="760" y="477"/></g>
  <g id="65">
   <g id="650">
    <use class="kv10" height="60" transform="rotate(0,1232.54,511) scale(1.02,1.13333) translate(-23.7675,-56.1176)" width="40" x="1212.14" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="477" zvalue="63"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874435985412" ObjectName="10"/>
    </metadata>
   </g>
   <g id="651">
    <use class="v400" height="60" transform="rotate(0,1232.54,511) scale(1.02,1.13333) translate(-23.7675,-56.1176)" width="40" x="1212.14" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="477" zvalue="63"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874436050948" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399450230788" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399450230788"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1232.54,511) scale(1.02,1.13333) translate(-23.7675,-56.1176)" width="40" x="1212.14" y="477"/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,300.485,331.107) scale(0.708333,0.665547) translate(119.354,161.373)" width="30" x="289.86" xlink:href="#State:红绿圆(方形)_0" y="321.12" zvalue="81"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,300.485,331.107) scale(0.708333,0.665547) translate(119.354,161.373)" width="30" x="289.86" y="321.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,204.86,331.107) scale(0.708333,0.665547) translate(79.9792,161.373)" width="30" x="194.24" xlink:href="#State:红绿圆(方形)_0" y="321.12" zvalue="82"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,204.86,331.107) scale(0.708333,0.665547) translate(79.9792,161.373)" width="30" x="194.24" y="321.12"/></g>
 </g>
</svg>