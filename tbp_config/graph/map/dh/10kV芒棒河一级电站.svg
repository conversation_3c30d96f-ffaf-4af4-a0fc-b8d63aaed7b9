<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549587279874" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV芒棒河一级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="42" xlink:href="logo.png" y="39.5"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,176.625,69.5) scale(1,1) translate(0,0)" writing-mode="lr" x="176.62" xml:space="preserve" y="73" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,178.333,69.1903) scale(1,1) translate(6.66134e-15,0)" writing-mode="lr" x="178.33" xml:space="preserve" y="78.19" zvalue="3">10kV芒棒河一级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="7" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,67.4375,363) scale(1,1) translate(0,0)" width="72.88" x="31" y="351" zvalue="112"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.4375,363) scale(1,1) translate(0,0)" writing-mode="lr" x="67.44" xml:space="preserve" y="367.5" zvalue="112">信号一览</text>
  <line fill="none" id="32" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="376" x2="376" y1="7.5" y2="1037.5" zvalue="4"/>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.000000000000227" x2="368.9999999999998" y1="143.3704926140824" y2="143.3704926140824" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="155.5000000000001" y2="155.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="181.5000000000001" y2="181.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="3" y1="155.5000000000001" y2="181.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="155.5000000000001" y2="181.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="155.5000000000001" y2="155.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="181.5000000000001" y2="181.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="155.5000000000001" y2="181.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365" x2="365" y1="155.5000000000001" y2="181.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="181.5000000000001" y2="181.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="205.7500000000001" y2="205.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="3" y1="181.5000000000001" y2="205.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="181.5000000000001" y2="205.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="181.5000000000001" y2="181.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="205.7500000000001" y2="205.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="181.5000000000001" y2="205.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365" x2="365" y1="181.5000000000001" y2="205.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="205.7500000000001" y2="205.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="228.5000000000001" y2="228.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="3" y1="205.7500000000001" y2="228.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="205.7500000000001" y2="228.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="205.7500000000001" y2="205.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="228.5000000000001" y2="228.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="205.7500000000001" y2="228.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365" x2="365" y1="205.7500000000001" y2="228.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="228.5000000000001" y2="228.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="251.2500000000001" y2="251.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="3" y1="228.5000000000001" y2="251.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="228.5000000000001" y2="251.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="228.5000000000001" y2="228.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="251.2500000000001" y2="251.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="228.5000000000001" y2="251.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365" x2="365" y1="228.5000000000001" y2="251.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="251.2500000000001" y2="251.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="184" y1="274.0000000000001" y2="274.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3" x2="3" y1="251.2500000000001" y2="274.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="251.2500000000001" y2="274.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="251.2500000000001" y2="251.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="365" y1="274.0000000000001" y2="274.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184" x2="184" y1="251.2500000000001" y2="274.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365" x2="365" y1="251.2500000000001" y2="274.0000000000001"/>
  <line fill="none" id="28" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.000000000000227" x2="368.9999999999998" y1="613.3704926140824" y2="613.3704926140824" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2" x2="92" y1="928.5000000000002" y2="928.5000000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2" x2="92" y1="967.6633000000002" y2="967.6633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2" x2="2" y1="928.5000000000002" y2="967.6633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="92" y1="928.5000000000002" y2="967.6633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="362" y1="928.5000000000002" y2="928.5000000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="362" y1="967.6633000000002" y2="967.6633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="92" y1="928.5000000000002" y2="967.6633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362" x2="362" y1="928.5000000000002" y2="967.6633000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2" x2="92" y1="967.6632700000002" y2="967.6632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2" x2="92" y1="995.5816700000003" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2" x2="2" y1="967.6632700000002" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="92" y1="967.6632700000002" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="182" y1="967.6632700000002" y2="967.6632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="182" y1="995.5816700000003" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="92" y1="967.6632700000002" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182" x2="182" y1="967.6632700000002" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.0000000000001" x2="272.0000000000001" y1="967.6632700000002" y2="967.6632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.0000000000001" x2="272.0000000000001" y1="995.5816700000003" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.0000000000001" x2="182.0000000000001" y1="967.6632700000002" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.0000000000001" x2="272.0000000000001" y1="967.6632700000002" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272" x2="362" y1="967.6632700000002" y2="967.6632700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272" x2="362" y1="995.5816700000003" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272" x2="272" y1="967.6632700000002" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362" x2="362" y1="967.6632700000002" y2="995.5816700000003"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2" x2="92" y1="995.5816000000002" y2="995.5816000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2" x2="92" y1="1023.5" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2" x2="2" y1="995.5816000000002" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="92" y1="995.5816000000002" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="182" y1="995.5816000000002" y2="995.5816000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="182" y1="1023.5" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92" x2="92" y1="995.5816000000002" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182" x2="182" y1="995.5816000000002" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.0000000000001" x2="272.0000000000001" y1="995.5816000000002" y2="995.5816000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.0000000000001" x2="272.0000000000001" y1="1023.5" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.0000000000001" x2="182.0000000000001" y1="995.5816000000002" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.0000000000001" x2="272.0000000000001" y1="995.5816000000002" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272" x2="362" y1="995.5816000000002" y2="995.5816000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272" x2="362" y1="1023.5" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272" x2="272" y1="995.5816000000002" y2="1023.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362" x2="362" y1="995.5816000000002" y2="1023.5"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47,948.5) scale(1,1) translate(0,0)" writing-mode="lr" x="47" xml:space="preserve" y="954.5" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44,982.5) scale(1,1) translate(0,0)" writing-mode="lr" x="44" xml:space="preserve" y="988.5" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226,982.5) scale(1,1) translate(0,0)" writing-mode="lr" x="226" xml:space="preserve" y="988.5" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,43,1010.5) scale(1,1) translate(0,0)" writing-mode="lr" x="43" xml:space="preserve" y="1016.5" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225,1010.5) scale(1,1) translate(0,0)" writing-mode="lr" x="225" xml:space="preserve" y="1016.5" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.5,643) scale(1,1) translate(0,2.07834e-13)" writing-mode="lr" x="67.5" xml:space="preserve" y="647.5000000000001" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.054,950.5) scale(1,1) translate(0,0)" writing-mode="lr" x="227.05" xml:space="preserve" y="956.5" zvalue="28">MangBangHeYiJi-01-2016</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,137.054,1008.5) scale(1,1) translate(0,0)" writing-mode="lr" x="137.05" xml:space="preserve" y="1014.5" zvalue="30">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,41,169.5) scale(1,1) translate(0,0)" writing-mode="lr" x="41" xml:space="preserve" y="175" zvalue="31">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,221,169.5) scale(1,1) translate(0,0)" writing-mode="lr" x="221" xml:space="preserve" y="175" zvalue="32">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.6875,193.75) scale(1,1) translate(0,0)" writing-mode="lr" x="48.69" xml:space="preserve" y="198.25" zvalue="33">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,230.792,193.083) scale(1,1) translate(0,0)" writing-mode="lr" x="230.79" xml:space="preserve" y="197.58" zvalue="34">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.1875,241.5) scale(1,1) translate(0,0)" writing-mode="lr" x="48.19" xml:space="preserve" y="246" zvalue="35">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,587.232,650.75) scale(1,1) translate(0,0)" writing-mode="lr" x="587.23" xml:space="preserve" y="655.25" zvalue="37">0.4kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,586.875,280.75) scale(1,1) translate(0,0)" writing-mode="lr" x="586.88" xml:space="preserve" y="285.25" zvalue="38">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" x="829.1328125" xml:space="preserve" y="983.1805990134186" zvalue="41">#1发电机     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="829.1328125" xml:space="preserve" y="999.1805990134186" zvalue="41">250KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,805.864,731.719) scale(1,1) translate(0,0)" writing-mode="lr" x="805.86" xml:space="preserve" y="736.22" zvalue="43">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,850.47,810.141) scale(1,1) translate(0,0)" writing-mode="lr" x="850.47" xml:space="preserve" y="814.64" zvalue="47">401</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" x="1158.8671875" xml:space="preserve" y="987.6805990134186" zvalue="51">#2发电机       </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1158.8671875" xml:space="preserve" y="1003.680599013419" zvalue="51">250KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1135.61,736.219) scale(1,1) translate(0,0)" writing-mode="lr" x="1135.61" xml:space="preserve" y="740.72" zvalue="53">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1180.22,814.641) scale(1,1) translate(0,0)" writing-mode="lr" x="1180.22" xml:space="preserve" y="819.14" zvalue="57">402</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" x="1477.6328125" xml:space="preserve" y="985.4305990134186" zvalue="61">#3发电机       </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1477.6328125" xml:space="preserve" y="1001.430599013419" zvalue="61">320KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1454.36,733.969) scale(1,1) translate(0,0)" writing-mode="lr" x="1454.36" xml:space="preserve" y="738.47" zvalue="63">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1498.97,812.391) scale(1,1) translate(0,0)" writing-mode="lr" x="1498.97" xml:space="preserve" y="816.89" zvalue="67">403</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,746,615.007) scale(1,1) translate(0,0)" writing-mode="lr" x="746" xml:space="preserve" y="619.51" zvalue="71">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,786.347,551.611) scale(1,1) translate(0,0)" writing-mode="lr" x="786.35" xml:space="preserve" y="556.11" zvalue="74">411</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" x="713.6875" xml:space="preserve" y="457.9079861111111" zvalue="76">#1主变        </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="713.6875" xml:space="preserve" y="473.9079861111111" zvalue="76">630KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,733.802,352.757) scale(1,1) translate(0,0)" writing-mode="lr" x="733.8" xml:space="preserve" y="357.26" zvalue="79">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1170.55,618.507) scale(1,1) translate(0,0)" writing-mode="lr" x="1170.55" xml:space="preserve" y="623.01" zvalue="84">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1210.9,555.111) scale(1,1) translate(0,-1.21483e-13)" writing-mode="lr" x="1210.9" xml:space="preserve" y="559.61" zvalue="87">412</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" x="1139.3984375" xml:space="preserve" y="461.875" zvalue="89">#2主变         </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1139.3984375" xml:space="preserve" y="477.875" zvalue="89">630KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1157.3,356.257) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.3" xml:space="preserve" y="360.76" zvalue="93">0021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,789.972,226.361) scale(1,1) translate(0,0)" writing-mode="lr" x="789.97" xml:space="preserve" y="230.86" zvalue="97">034</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,731.25,158.757) scale(1,1) translate(0,0)" writing-mode="lr" x="731.25" xml:space="preserve" y="163.26" zvalue="103">0346</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,768,50.25) scale(1,1) translate(0,0)" writing-mode="lr" x="768" xml:space="preserve" y="54.75" zvalue="104">10kV嘎棒线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229.75,241.25) scale(1,1) translate(0,0)" writing-mode="lr" x="229.75" xml:space="preserve" y="245.75" zvalue="106">10kV#2变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,183.211,363.591) scale(1,1) translate(0,0)" writing-mode="lr" x="183.21" xml:space="preserve" y="368.09" zvalue="108">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,288.211,363.591) scale(1,1) translate(0,0)" writing-mode="lr" x="288.21" xml:space="preserve" y="368.09" zvalue="109">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="10kV芒棒河一级电站.svg"><rect fill-opacity="0" height="24" width="72.88" x="31" y="351" zvalue="112"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="35">
   <path class="v400" d="M 558.71 674.43 L 1589.43 674.43" stroke-width="6" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674244493316" ObjectName="0.4kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674244493316"/></metadata>
  <path d="M 558.71 674.43 L 1589.43 674.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv10" d="M 621.75 284.25 L 1429.25 284.25" stroke-width="6" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674244558852" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674244558852"/></metadata>
  <path d="M 621.75 284.25 L 1429.25 284.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v400" height="30" transform="rotate(0,824.348,928.362) scale(1.85899,1.85899) translate(-368.024,-416.087)" width="30" x="796.462811040348" xlink:href="#Generator:发电机_0" y="900.4772123618222" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449812627462" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449812627462"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,824.348,928.362) scale(1.85899,1.85899) translate(-368.024,-416.087)" width="30" x="796.462811040348" y="900.4772123618222"/></g>
  <g id="53">
   <use class="v400" height="30" transform="rotate(0,1154.1,932.862) scale(1.85899,1.85899) translate(-520.393,-418.166)" width="30" x="1126.212811040348" xlink:href="#Generator:发电机_0" y="904.9772123618222" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449812758534" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449812758534"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1154.1,932.862) scale(1.85899,1.85899) translate(-520.393,-418.166)" width="30" x="1126.212811040348" y="904.9772123618222"/></g>
  <g id="62">
   <use class="v400" height="30" transform="rotate(0,1472.85,930.612) scale(1.85899,1.85899) translate(-667.679,-417.126)" width="30" x="1444.962811040348" xlink:href="#Generator:发电机_0" y="902.7272123618222" zvalue="60"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449812889606" ObjectName="#3发电机"/>
   <cge:TPSR_Ref TObjectID="6192449812889606"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1472.85,930.612) scale(1.85899,1.85899) translate(-667.679,-417.126)" width="30" x="1444.962811040348" y="902.7272123618222"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="165">
   <use class="v400" height="30" transform="rotate(0,825.082,732.719) scale(1.9625,1.2338) translate(-397.439,-135.339)" width="15" x="810.3635212897802" xlink:href="#Disconnector:刀闸_0" y="714.2123015873013" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449812561926" ObjectName="#1发电机4011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449812561926"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,825.082,732.719) scale(1.9625,1.2338) translate(-397.439,-135.339)" width="15" x="810.3635212897802" y="714.2123015873013"/></g>
  <g id="52">
   <use class="v400" height="30" transform="rotate(0,1154.83,737.219) scale(1.9625,1.2338) translate(-559.164,-136.191)" width="15" x="1140.11352128978" xlink:href="#Disconnector:刀闸_0" y="718.7123015873013" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449812692998" ObjectName="#2发电机4021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449812692998"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1154.83,737.219) scale(1.9625,1.2338) translate(-559.164,-136.191)" width="15" x="1140.11352128978" y="718.7123015873013"/></g>
  <g id="61">
   <use class="v400" height="30" transform="rotate(0,1473.58,734.969) scale(1.9625,1.2338) translate(-715.494,-135.765)" width="15" x="1458.86352128978" xlink:href="#Disconnector:刀闸_0" y="716.4623015873013" zvalue="62"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449812824070" ObjectName="#3发电机4031隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449812824070"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1473.58,734.969) scale(1.9625,1.2338) translate(-715.494,-135.765)" width="15" x="1458.86352128978" y="716.4623015873013"/></g>
  <g id="63">
   <use class="v400" height="30" transform="rotate(0,765.219,616.007) scale(1.9625,1.2338) translate(-368.08,-113.222)" width="15" x="750.5" xlink:href="#Disconnector:刀闸_0" y="597.5" zvalue="70"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449812955142" ObjectName="#1主变0.4kV侧4111隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449812955142"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,765.219,616.007) scale(1.9625,1.2338) translate(-368.08,-113.222)" width="15" x="750.5" y="597.5"/></g>
  <g id="70">
   <use class="kv10" height="30" transform="rotate(0,768.02,353.757) scale(1.9625,1.2338) translate(-369.454,-63.5277)" width="15" x="753.3015801302784" xlink:href="#Disconnector:刀闸_0" y="335.2500000000001" zvalue="78"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449813020678" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449813020678"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,768.02,353.757) scale(1.9625,1.2338) translate(-369.454,-63.5277)" width="15" x="753.3015801302784" y="335.2500000000001"/></g>
  <g id="88">
   <use class="v400" height="30" transform="rotate(0,1189.77,619.507) scale(1.9625,1.2338) translate(-576.301,-113.886)" width="15" x="1175.05462962963" xlink:href="#Disconnector:刀闸_0" y="601" zvalue="82"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449813151750" ObjectName="#2主变0.4kV侧4121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449813151750"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1189.77,619.507) scale(1.9625,1.2338) translate(-576.301,-113.886)" width="15" x="1175.05462962963" y="601"/></g>
  <g id="82">
   <use class="kv10" height="30" transform="rotate(0,1191.52,357.257) scale(1.9625,1.2338) translate(-577.159,-64.1909)" width="15" x="1176.80462962963" xlink:href="#Disconnector:刀闸_0" y="338.7500000000001" zvalue="91"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449813086214" ObjectName="#2主变10kV侧0021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449813086214"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1191.52,357.257) scale(1.9625,1.2338) translate(-577.159,-64.1909)" width="15" x="1176.80462962963" y="338.7500000000001"/></g>
  <g id="93">
   <use class="kv10" height="30" transform="rotate(0,766.719,159.757) scale(1.9625,1.2338) translate(-368.815,-26.7659)" width="15" x="752" xlink:href="#Disconnector:刀闸_0" y="141.2500000000001" zvalue="102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449813217286" ObjectName="10kV嘎棒线0346隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449813217286"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,766.719,159.757) scale(1.9625,1.2338) translate(-368.815,-26.7659)" width="15" x="752" y="141.2500000000001"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="44">
   <path class="v400" d="M 825.25 714.82 L 825.25 674.43" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.25 714.82 L 825.25 674.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="v400" d="M 824.35 900.94 L 824.35 819.44" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="170@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 824.35 900.94 L 824.35 819.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="v400" d="M 825.97 798.2 L 825.97 750.91" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="165@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.97 798.2 L 825.97 750.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="v400" d="M 1155 719.32 L 1155 674.43" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="35@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1155 719.32 L 1155 674.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="v400" d="M 1154.1 905.44 L 1154.1 823.94" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="50@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1154.1 905.44 L 1154.1 823.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="v400" d="M 1155.72 802.7 L 1155.72 755.41" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="52@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1155.72 802.7 L 1155.72 755.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="v400" d="M 1473.75 717.07 L 1473.75 674.43" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="35@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1473.75 717.07 L 1473.75 674.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="v400" d="M 1472.85 903.19 L 1472.85 821.69" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="59@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1472.85 903.19 L 1472.85 821.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="v400" d="M 1474.47 800.45 L 1474.47 753.16" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="61@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1474.47 800.45 L 1474.47 753.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="v400" d="M 765.34 634.2 L 765.34 674.43" stroke-width="1" zvalue="71"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@1" LinkObjectIDznd="35@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 765.34 634.2 L 765.34 674.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="v400" d="M 766.44 563.22 L 765.39 598.11" stroke-width="1" zvalue="74"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@1" LinkObjectIDznd="63@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 766.44 563.22 L 765.39 598.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="v400" d="M 766.32 489.56 L 766.32 541.98" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@1" LinkObjectIDznd="65@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 766.32 489.56 L 766.32 541.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv10" d="M 768.14 371.95 L 768.14 428.06" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@1" LinkObjectIDznd="67@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 768.14 371.95 L 768.14 428.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv10" d="M 768.19 335.86 L 768.19 284.25" stroke-width="1" zvalue="80"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 768.19 335.86 L 768.19 284.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="v400" d="M 1189.89 637.7 L 1189.89 674.43" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="88@1" LinkObjectIDznd="35@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1189.89 637.7 L 1189.89 674.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="v400" d="M 1191 566.72 L 1189.95 601.61" stroke-width="1" zvalue="86"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@1" LinkObjectIDznd="88@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1191 566.72 L 1189.95 601.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="v400" d="M 1190.88 493.06 L 1190.88 545.48" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="84@1" LinkObjectIDznd="86@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1190.88 493.06 L 1190.88 545.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv10" d="M 1191.64 375.45 L 1190.91 431.56" stroke-width="1" zvalue="92"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@1" LinkObjectIDznd="84@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1191.64 375.45 L 1190.91 431.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv10" d="M 1191.7 339.36 L 1191.7 284.25" stroke-width="1" zvalue="94"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="82@0" LinkObjectIDznd="36@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1191.7 339.36 L 1191.7 284.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv10" d="M 768.19 237.97 L 768.19 284.25" stroke-width="1" zvalue="97"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@1" LinkObjectIDznd="36@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 768.19 237.97 L 768.19 284.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv10" d="M 766.84 177.95 L 766.84 216.73" stroke-width="1" zvalue="100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@1" LinkObjectIDznd="89@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 766.84 177.95 L 766.84 216.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv10" d="M 768 84.78 L 768 141.86" stroke-width="1" zvalue="104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="93@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 768 84.78 L 768 141.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="170">
   <use class="v400" height="20" transform="rotate(0,826.012,808.833) scale(1.22222,1.11111) translate(-149.073,-79.7722)" width="10" x="819.9011668709484" xlink:href="#Breaker:开关_0" y="797.7219169719168" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924514021381" ObjectName="#1发电机401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924514021381"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,826.012,808.833) scale(1.22222,1.11111) translate(-149.073,-79.7722)" width="10" x="819.9011668709484" y="797.7219169719168"/></g>
  <g id="50">
   <use class="v400" height="20" transform="rotate(0,1155.76,813.333) scale(1.22222,1.11111) translate(-209.027,-80.2222)" width="10" x="1149.651166870948" xlink:href="#Breaker:开关_0" y="802.2219169719168" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924514086917" ObjectName="#2发电机402断路器"/>
   <cge:TPSR_Ref TObjectID="6473924514086917"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1155.76,813.333) scale(1.22222,1.11111) translate(-209.027,-80.2222)" width="10" x="1149.651166870948" y="802.2219169719168"/></g>
  <g id="59">
   <use class="v400" height="20" transform="rotate(0,1474.51,811.083) scale(1.22222,1.11111) translate(-266.982,-79.9972)" width="10" x="1468.401166870948" xlink:href="#Breaker:开关_0" y="799.9719169719168" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924514152453" ObjectName="#3发电机403断路器"/>
   <cge:TPSR_Ref TObjectID="6473924514152453"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1474.51,811.083) scale(1.22222,1.11111) translate(-266.982,-79.9972)" width="10" x="1468.401166870948" y="799.9719169719168"/></g>
  <g id="65">
   <use class="v400" height="20" transform="rotate(0,766.361,552.611) scale(1.22222,1.11111) translate(-138.227,-54.15)" width="10" x="760.25" xlink:href="#Breaker:开关_0" y="541.5" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924514217989" ObjectName="#1主变0.4kV侧411断路器"/>
   <cge:TPSR_Ref TObjectID="6473924514217989"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,766.361,552.611) scale(1.22222,1.11111) translate(-138.227,-54.15)" width="10" x="760.25" y="541.5"/></g>
  <g id="86">
   <use class="v400" height="20" transform="rotate(0,1190.92,556.111) scale(1.22222,1.11111) translate(-215.419,-54.5)" width="10" x="1184.80462962963" xlink:href="#Breaker:开关_0" y="545" zvalue="85"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924514283525" ObjectName="#2主变0.4kV侧412断路器"/>
   <cge:TPSR_Ref TObjectID="6473924514283525"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1190.92,556.111) scale(1.22222,1.11111) translate(-215.419,-54.5)" width="10" x="1184.80462962963" y="545"/></g>
  <g id="89">
   <use class="kv10" height="20" transform="rotate(0,768.111,227.361) scale(1.22222,1.11111) translate(-138.545,-21.625)" width="10" x="762" xlink:href="#Breaker:开关_0" y="216.25" zvalue="96"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924514349061" ObjectName="10kV嘎棒线034断路器"/>
   <cge:TPSR_Ref TObjectID="6473924514349061"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,768.111,227.361) scale(1.22222,1.11111) translate(-138.545,-21.625)" width="10" x="762" y="216.25"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="67">
   <g id="670">
    <use class="kv10" height="60" transform="rotate(0,766.32,458.75) scale(0.9375,1.04167) translate(49.838,-17.1)" width="40" x="747.5700000000001" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="427.5" zvalue="75"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874438737924" ObjectName="10"/>
    </metadata>
   </g>
   <g id="671">
    <use class="v400" height="60" transform="rotate(0,766.32,458.75) scale(0.9375,1.04167) translate(49.838,-17.1)" width="40" x="747.5700000000001" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="427.5" zvalue="75"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874438803460" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399451607043" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399451607043"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,766.32,458.75) scale(0.9375,1.04167) translate(49.838,-17.1)" width="40" x="747.5700000000001" y="427.5"/></g>
  <g id="84">
   <g id="840">
    <use class="kv10" height="60" transform="rotate(0,1190.88,462.25) scale(0.9375,1.04167) translate(78.1417,-17.24)" width="40" x="1172.13" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="431" zvalue="88"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874438868996" ObjectName="10"/>
    </metadata>
   </g>
   <g id="841">
    <use class="v400" height="60" transform="rotate(0,1190.88,462.25) scale(0.9375,1.04167) translate(78.1417,-17.24)" width="40" x="1172.13" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="431" zvalue="88"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874438934532" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399451672579" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399451672579"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1190.88,462.25) scale(0.9375,1.04167) translate(78.1417,-17.24)" width="40" x="1172.13" y="431"/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,315.485,364.107) scale(0.708333,0.665547) translate(125.531,177.956)" width="30" x="304.86" xlink:href="#State:红绿圆(方形)_0" y="354.12" zvalue="110"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374892138499" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,315.485,364.107) scale(0.708333,0.665547) translate(125.531,177.956)" width="30" x="304.86" y="354.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,219.86,364.107) scale(0.708333,0.665547) translate(86.1556,177.956)" width="30" x="209.24" xlink:href="#State:红绿圆(方形)_0" y="354.12" zvalue="111"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,219.86,364.107) scale(0.708333,0.665547) translate(86.1556,177.956)" width="30" x="209.24" y="354.12"/></g>
 </g>
</svg>