<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549682438145" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:带熔断器35kVPT11_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="1.1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="26.25" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="18.06481481481482" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="20.69444444444444" y1="24.96612466124661" y2="24.96612466124661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69444444444444" x2="19.37962962962963" y1="24.96612466124661" y2="22.5"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,5.71) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="13" y2="1"/>
   <ellipse cx="15.03" cy="18.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.67" cy="23.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="18.5" cy="23.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="20.5" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Accessory:PT7_0" viewBox="0,0,14,18">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1.416666666666664"/>
   <ellipse cx="7.15" cy="6.35" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.15" cy="12.03" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="PowerTransformer2:586_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.75" x2="10.08333333333333" y1="17.11381925541057" y2="17.11381925541057"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.98518194534241" x2="9.999999999999998" y1="9.386991192770131" y2="16.99800064973551"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00299079104462" x2="19.66666666666667" y1="9.301854138598824" y2="16.8218426122152"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:586_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="30.58333333333333" y2="36.5"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="21" y1="36.5" y2="40.5"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="10" y1="36.5" y2="40.5"/>
  </symbol>
  <symbol id="EnergyConsumer:站用897_0" viewBox="0,0,18,48">
   <use terminal-index="0" type="0" x="8.949999999999999" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="34" y2="47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="15" y1="34" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.050000000000001" x2="9.050000000000001" y1="41.58333333333334" y2="29.41666666666666"/>
   <ellipse cx="9.039999999999999" cy="8.720000000000001" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.970000000000001" cy="20.89" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.035256190670173" x2="9.035256190670173" y1="4.350000000000001" y2="8.458948332339872"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.035256190670172" x2="5.05" y1="8.483948332339875" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.063029030724982" x2="13.15" y1="8.439905009168513" y2="11.25"/>
   <path d="M 9 19.5833 L 4.08333 25.1667 L 14.0833 25.1667 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <rect fill-opacity="0" height="4.32" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,9.12,43.76) scale(1,1) translate(0,0)" width="2.83" x="7.7" y="41.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.720407223143537" x2="9.71105583195135" y1="47.65316518298714" y2="47.65316518298714"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.123714759535657" x2="9.123714759535657" y1="41.60000000000002" y2="44.27329376854603"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.450230329832317" x2="9.891173760825502" y1="47.28009787078974" y2="47.28009787078974"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.123714759535655" x2="8.277943615257049" y1="44.30029673590506" y2="43.49020771513355"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.089994472084024" x2="10.2514096185738" y1="46.90703055859233" y2="46.90703055859233"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.123714759535657" x2="9.969485903814263" y1="44.30029673590506" y2="43.49020771513355"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.123714759535657" x2="9.123714759535657" y1="45.92047477744808" y2="46.89258160237389"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV葫芦口电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="48" xlink:href="logo.png" y="31"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,176,61) scale(1,1) translate(0,0)" writing-mode="lr" x="176" xml:space="preserve" y="64.5" zvalue="54"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,176.5,60.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="176.5" xml:space="preserve" y="69.69" zvalue="55">110kV葫芦口电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="117" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,80.125,179) scale(1,1) translate(0,0)" width="72.88" x="43.69" y="167" zvalue="1763"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.125,179) scale(1,1) translate(0,0)" writing-mode="lr" x="80.13" xml:space="preserve" y="183.5" zvalue="1763">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,591.222,333.889) scale(1,1) translate(0,0)" writing-mode="lr" x="591.22" xml:space="preserve" y="338.39" zvalue="7">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,488.133,648.556) scale(1,1) translate(0,-4.2503e-13)" writing-mode="lr" x="488.13" xml:space="preserve" y="653.0599999999999" zvalue="16">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,793.333,232.111) scale(1,1) translate(0,0)" writing-mode="lr" x="793.33" xml:space="preserve" y="236.61" zvalue="17">131</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,784.556,279.667) scale(1,1) translate(0,0)" writing-mode="lr" x="784.5599999999999" xml:space="preserve" y="284.17" zvalue="18">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,786.222,183.667) scale(1,1) translate(0,0)" writing-mode="lr" x="786.22" xml:space="preserve" y="188.17" zvalue="20">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,759.932,77.4444) scale(1,1) translate(0,0)" writing-mode="lr" x="759.9299999999999" xml:space="preserve" y="81.94" zvalue="25">110kV南葫线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,725.056,270.778) scale(1,1) translate(0,-4.65899e-13)" writing-mode="lr" x="725.0599999999999" xml:space="preserve" y="275.28" zvalue="27">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,725.056,171.889) scale(1,1) translate(0,0)" writing-mode="lr" x="725.0599999999999" xml:space="preserve" y="176.39" zvalue="32">67</text>
  <line fill="none" id="67" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382" x2="382" y1="11" y2="1041" zvalue="58"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1256.56,267.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1256.56" xml:space="preserve" y="272.17" zvalue="94">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1189.33,258.944) scale(1,1) translate(0,0)" writing-mode="lr" x="1189.33" xml:space="preserve" y="263.44" zvalue="96">19017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="309" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,666.03,720.111) scale(1,1) translate(0,0)" writing-mode="lr" x="666.03" xml:space="preserve" y="724.61" zvalue="613">031</text>
  <line fill="none" id="666" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="146.8704926140824" y2="146.8704926140824" zvalue="1056"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1226.24,161) scale(1,1) translate(0,0)" writing-mode="lr" x="1226.24" xml:space="preserve" y="165.5" zvalue="1094">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="851" stroke="rgb(255,255,255)" text-anchor="middle" x="642" xml:space="preserve" y="947.75" zvalue="1261">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="851" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="642" xml:space="preserve" y="963.75" zvalue="1261">10MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,613,816.997) scale(1,1) translate(0,0)" writing-mode="lr" x="613" xml:space="preserve" y="821.5" zvalue="1264">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,894.778,353.222) scale(1,1) translate(0,0)" writing-mode="lr" x="894.78" xml:space="preserve" y="357.72" zvalue="1496">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,904.111,393.222) scale(1,1) translate(0,0)" writing-mode="lr" x="904.11" xml:space="preserve" y="397.72" zvalue="1498">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,784.556,548.667) scale(1,1) translate(0,0)" writing-mode="lr" x="784.5599999999999" xml:space="preserve" y="553.17" zvalue="1502">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,834.444,382.222) scale(1,1) translate(-7.34474e-13,0)" writing-mode="lr" x="834.4400000000001" xml:space="preserve" y="386.72" zvalue="1507">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,963.836,533.25) scale(1,1) translate(0,0)" writing-mode="lr" x="963.8360089465702" xml:space="preserve" y="537.75" zvalue="1523">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,962.25,564.75) scale(1,1) translate(0,0)" writing-mode="lr" x="962.2499999999999" xml:space="preserve" y="569.25" zvalue="1602">25MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1063.33,232.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1063.33" xml:space="preserve" y="236.61" zvalue="1608">132</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1054.56,279.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1054.56" xml:space="preserve" y="284.17" zvalue="1610">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1056.22,183.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1056.22" xml:space="preserve" y="188.17" zvalue="1613">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,995.056,270.778) scale(1,1) translate(0,-4.66787e-13)" writing-mode="lr" x="995.0599999999999" xml:space="preserve" y="275.28" zvalue="1620">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="170" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,995.056,171.889) scale(1,1) translate(0,0)" writing-mode="lr" x="995.0599999999999" xml:space="preserve" y="176.39" zvalue="1622">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,786.556,632.667) scale(1,1) translate(0,0)" writing-mode="lr" x="786.5599999999999" xml:space="preserve" y="637.17" zvalue="1632">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="280" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,692.111,865.111) scale(1,1) translate(0,0)" writing-mode="lr" x="692.11" xml:space="preserve" y="869.61" zvalue="1647">09127</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1000.03,720.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1000.03" xml:space="preserve" y="724.61" zvalue="1650">032</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" x="976" xml:space="preserve" y="947.75" zvalue="1653">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="976" xml:space="preserve" y="963.75" zvalue="1653">10MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="283" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,947,816.997) scale(1,1) translate(0,0)" writing-mode="lr" x="947" xml:space="preserve" y="821.5" zvalue="1655">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="282" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1026.11,865.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1026.11" xml:space="preserve" y="869.61" zvalue="1667">09227</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="307" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1280.45,830.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1280.45" xml:space="preserve" y="835" zvalue="1673">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="310" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1308.5,740.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1308.5" xml:space="preserve" y="744.72" zvalue="1675">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="316" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1707.5,291.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1707.5" xml:space="preserve" y="296.17" zvalue="1682">0.4kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1479.03,414) scale(1,1) translate(0,0)" writing-mode="lr" x="1479.03" xml:space="preserve" y="418.5" zvalue="1686">401</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1481,592) scale(1,1) translate(0,0)" writing-mode="lr" x="1481" xml:space="preserve" y="596.5" zvalue="1688">033</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1519.5,500) scale(1,1) translate(0,0)" writing-mode="lr" x="1519.5" xml:space="preserve" y="504.5" zvalue="1695">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1760.26,493) scale(1,1) translate(0,0)" writing-mode="lr" x="1760.26" xml:space="preserve" y="497.5" zvalue="1701">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1739.5,412) scale(1,1) translate(0,0)" writing-mode="lr" x="1739.5" xml:space="preserve" y="416.5" zvalue="1703">402</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1576.56,591.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1576.56" xml:space="preserve" y="596.17" zvalue="1707">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,905.819,625.722) scale(1,1) translate(0,0)" writing-mode="lr" x="905.8200000000001" xml:space="preserve" y="630.22" zvalue="1716">001</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="187.5" y1="240.75" y2="240.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="187.5" y1="266.75" y2="266.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="6.5" y1="240.75" y2="266.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="187.5" y1="240.75" y2="266.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="368.5" y1="240.75" y2="240.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="368.5" y1="266.75" y2="266.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="187.5" y1="240.75" y2="266.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.5" x2="368.5" y1="240.75" y2="266.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="187.5" y1="266.75" y2="266.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="187.5" y1="291" y2="291"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="6.5" y1="266.75" y2="291"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="187.5" y1="266.75" y2="291"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="368.5" y1="266.75" y2="266.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="368.5" y1="291" y2="291"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="187.5" y1="266.75" y2="291"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.5" x2="368.5" y1="266.75" y2="291"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="187.5" y1="291" y2="291"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="187.5" y1="313.75" y2="313.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="6.5" y1="291" y2="313.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="187.5" y1="291" y2="313.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="368.5" y1="291" y2="291"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="368.5" y1="313.75" y2="313.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="187.5" y1="291" y2="313.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.5" x2="368.5" y1="291" y2="313.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="187.5" y1="313.75" y2="313.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="187.5" y1="336.5" y2="336.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="6.5" y1="313.75" y2="336.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="187.5" y1="313.75" y2="336.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="368.5" y1="313.75" y2="313.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="368.5" y1="336.5" y2="336.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="187.5" y1="313.75" y2="336.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.5" x2="368.5" y1="313.75" y2="336.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="187.5" y1="336.5" y2="336.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="187.5" y1="359.25" y2="359.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="6.5" y1="336.5" y2="359.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="187.5" y1="336.5" y2="359.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="368.5" y1="336.5" y2="336.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="368.5" y1="359.25" y2="359.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="187.5" y1="336.5" y2="359.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.5" x2="368.5" y1="336.5" y2="359.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="187.5" y1="359.25" y2="359.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="187.5" y1="382" y2="382"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="6.5" y1="359.25" y2="382"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="187.5" y1="359.25" y2="382"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="368.5" y1="359.25" y2="359.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="368.5" y1="382" y2="382"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="187.5" y1="359.25" y2="382"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.5" x2="368.5" y1="359.25" y2="382"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="187.5" y1="382" y2="382"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="187.5" y1="404.75" y2="404.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.5" x2="6.5" y1="382" y2="404.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="187.5" y1="382" y2="404.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="368.5" y1="382" y2="382"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="368.5" y1="404.75" y2="404.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.5" x2="187.5" y1="382" y2="404.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.5" x2="368.5" y1="382" y2="404.75"/>
  <line fill="none" id="139" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.500000000000455" x2="372.5" y1="486.6204926140824" y2="486.6204926140824" zvalue="1741"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.5" x2="95.5" y1="925.75" y2="925.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.5" x2="95.5" y1="964.9132999999999" y2="964.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.5" x2="5.5" y1="925.75" y2="964.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.5" x2="95.5" y1="925.75" y2="964.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.5" x2="365.5" y1="925.75" y2="925.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.5" x2="365.5" y1="964.9132999999999" y2="964.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.5" x2="95.5" y1="925.75" y2="964.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="365.5" x2="365.5" y1="925.75" y2="964.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.5" x2="95.5" y1="964.91327" y2="964.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.5" x2="95.5" y1="992.83167" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.5" x2="5.5" y1="964.91327" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.5" x2="95.5" y1="964.91327" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.5" x2="185.5" y1="964.91327" y2="964.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.5" x2="185.5" y1="992.83167" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.5" x2="95.5" y1="964.91327" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.5" x2="185.5" y1="964.91327" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.5000000000001" x2="275.5000000000001" y1="964.91327" y2="964.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.5000000000001" x2="275.5000000000001" y1="992.83167" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.5000000000001" x2="185.5000000000001" y1="964.91327" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.5000000000001" x2="275.5000000000001" y1="964.91327" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.5" x2="365.5" y1="964.91327" y2="964.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.5" x2="365.5" y1="992.83167" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.5" x2="275.5" y1="964.91327" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="365.5" x2="365.5" y1="964.91327" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.5" x2="95.5" y1="992.8316" y2="992.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.5" x2="95.5" y1="1020.75" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.5" x2="5.5" y1="992.8316" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.5" x2="95.5" y1="992.8316" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.5" x2="185.5" y1="992.8316" y2="992.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.5" x2="185.5" y1="1020.75" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.5" x2="95.5" y1="992.8316" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.5" x2="185.5" y1="992.8316" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.5000000000001" x2="275.5000000000001" y1="992.8316" y2="992.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.5000000000001" x2="275.5000000000001" y1="1020.75" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.5000000000001" x2="185.5000000000001" y1="992.8316" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.5000000000001" x2="275.5000000000001" y1="992.8316" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.5" x2="365.5" y1="992.8316" y2="992.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.5" x2="365.5" y1="1020.75" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.5" x2="275.5" y1="992.8316" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="365.5" x2="365.5" y1="992.8316" y2="1020.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50.5,945.75) scale(1,1) translate(0,0)" writing-mode="lr" x="50.5" xml:space="preserve" y="951.75" zvalue="1743">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.5,979.75) scale(1,1) translate(0,0)" writing-mode="lr" x="47.5" xml:space="preserve" y="985.75" zvalue="1744">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229.5,979.75) scale(1,1) translate(0,0)" writing-mode="lr" x="229.5" xml:space="preserve" y="985.75" zvalue="1745">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46.5,1007.75) scale(1,1) translate(0,0)" writing-mode="lr" x="46.5" xml:space="preserve" y="1013.75" zvalue="1746">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.5,1007.75) scale(1,1) translate(0,0)" writing-mode="lr" x="228.5" xml:space="preserve" y="1013.75" zvalue="1747">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,71,560.25) scale(1,1) translate(0,0)" writing-mode="lr" x="71" xml:space="preserve" y="564.75" zvalue="1749">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,195.899,179.591) scale(1,1) translate(0,0)" writing-mode="lr" x="195.9" xml:space="preserve" y="184.09" zvalue="1750">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,300.899,179.591) scale(1,1) translate(0,0)" writing-mode="lr" x="300.9" xml:space="preserve" y="184.09" zvalue="1751">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,53,253.75) scale(1,1) translate(0,0)" writing-mode="lr" x="10.5" xml:space="preserve" y="258.25" zvalue="1752">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,233.5,253.75) scale(1,1) translate(0,0)" writing-mode="lr" x="191" xml:space="preserve" y="258.25" zvalue="1753">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.1875,327) scale(1,1) translate(0,0)" writing-mode="lr" x="56.19" xml:space="preserve" y="331.5" zvalue="1754">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,230.375,325.75) scale(1,1) translate(0,0)" writing-mode="lr" x="230.38" xml:space="preserve" y="330.25" zvalue="1755">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.1875,351) scale(1,1) translate(0,0)" writing-mode="lr" x="53.19" xml:space="preserve" y="355.5" zvalue="1756">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,52,279.75) scale(1,1) translate(0,0)" writing-mode="lr" x="9.5" xml:space="preserve" y="284.25" zvalue="1764">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,232.5,279.75) scale(1,1) translate(0,0)" writing-mode="lr" x="190" xml:space="preserve" y="284.25" zvalue="1765">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.1875,373) scale(1,1) translate(0,0)" writing-mode="lr" x="53.19" xml:space="preserve" y="377.5" zvalue="1768">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,221.188,372) scale(1,1) translate(0,0)" writing-mode="lr" x="221.19" xml:space="preserve" y="376.5" zvalue="1770">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.1875,396) scale(1,1) translate(0,0)" writing-mode="lr" x="53.19" xml:space="preserve" y="400.5" zvalue="1772">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,221.188,395) scale(1,1) translate(0,0)" writing-mode="lr" x="221.19" xml:space="preserve" y="399.5" zvalue="1773">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,52,302.75) scale(1,1) translate(0,0)" writing-mode="lr" x="9.5" xml:space="preserve" y="307.25" zvalue="1774">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,232,301.75) scale(1,1) translate(0,0)" writing-mode="lr" x="189.5" xml:space="preserve" y="306.25" zvalue="1776">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1031.93,77.4445) scale(1,1) translate(0,0)" writing-mode="lr" x="1031.93" xml:space="preserve" y="81.94" zvalue="1786">110kV梁大线葫芦口T线</text>
 </g>
 <g id="ButtonClass">
  <g href="户撒四级电站_110kV母线.svg"><rect fill-opacity="0" height="40" width="12" x="1225" y="242" zvalue="1053"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="43.69" y="167" zvalue="1763"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="89">
   <use class="kv110" height="30" transform="rotate(0,1231.22,268.667) scale(-1.11111,-0.814815) translate(-2338.49,-601.172)" width="15" x="1222.888888888889" xlink:href="#Disconnector:刀闸_0" y="256.4444581137762" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454479642627" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454479642627"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1231.22,268.667) scale(-1.11111,-0.814815) translate(-2338.49,-601.172)" width="15" x="1222.888888888889" y="256.4444581137762"/></g>
  <g id="21">
   <use class="kv110" height="30" transform="rotate(0,772,280.667) scale(-1.11111,-0.814815) translate(-1465.97,-627.899)" width="15" x="763.6666666666666" xlink:href="#Disconnector:刀闸_0" y="268.4444580078125" zvalue="17"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454479183875" ObjectName="110kV南葫线1311隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454479183875"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,772,280.667) scale(-1.11111,-0.814815) translate(-1465.97,-627.899)" width="15" x="763.6666666666666" y="268.4444580078125"/></g>
  <g id="22">
   <use class="kv110" height="30" transform="rotate(0,772,184.667) scale(-1.11111,-0.814815) translate(-1465.97,-414.081)" width="15" x="763.6666666931577" xlink:href="#Disconnector:刀闸_0" y="172.4444444444445" zvalue="19"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454479249411" ObjectName="110kV南葫线1316隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454479249411"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,772,184.667) scale(-1.11111,-0.814815) translate(-1465.97,-414.081)" width="15" x="763.6666666931577" y="172.4444444444445"/></g>
  <g id="9">
   <use class="kv10" height="26" transform="rotate(90,613,800.997) scale(1,1) translate(0,0)" width="12" x="607" xlink:href="#Disconnector:单手车刀闸1212_0" y="787.9969900066343" zvalue="1263"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454479970307" ObjectName="#1发电机0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454479970307"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,613,800.997) scale(1,1) translate(0,0)" width="12" x="607" y="787.9969900066343"/></g>
  <g id="186">
   <use class="kv110" height="30" transform="rotate(0,883.333,354.222) scale(1.11111,0.814815) translate(-87.5,77.7273)" width="15" x="875.0000101725263" xlink:href="#Disconnector:刀闸_0" y="342" zvalue="1495"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454480560131" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454480560131"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,883.333,354.222) scale(1.11111,0.814815) translate(-87.5,77.7273)" width="15" x="875.0000101725263" y="342"/></g>
  <g id="215">
   <use class="kv110" height="30" transform="rotate(0,1042,280.667) scale(-1.11111,-0.814815) translate(-1978.97,-627.899)" width="15" x="1033.666666666667" xlink:href="#Disconnector:刀闸_0" y="268.4444580078125" zvalue="1609"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450329640965" ObjectName="110kV梁大线葫芦口T线1321隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450329640965"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1042,280.667) scale(-1.11111,-0.814815) translate(-1978.97,-627.899)" width="15" x="1033.666666666667" y="268.4444580078125"/></g>
  <g id="214">
   <use class="kv110" height="30" transform="rotate(0,1042,184.667) scale(-1.11111,-0.814815) translate(-1978.97,-414.081)" width="15" x="1033.666666693158" xlink:href="#Disconnector:刀闸_0" y="172.4444444444445" zvalue="1611"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450329575429" ObjectName="110kV梁大线葫芦口T线1326隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450329575429"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1042,184.667) scale(-1.11111,-0.814815) translate(-1978.97,-414.081)" width="15" x="1033.666666693158" y="172.4444444444445"/></g>
  <g id="300">
   <use class="kv10" height="26" transform="rotate(90,947,800.997) scale(1,1) translate(0,0)" width="12" x="941" xlink:href="#Disconnector:单手车刀闸1212_0" y="787.9969900066343" zvalue="1654"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454482132995" ObjectName="#2发电机0921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454482132995"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,947,800.997) scale(1,1) translate(0,0)" width="12" x="941" y="787.9969900066343"/></g>
  <g id="308">
   <use class="kv10" height="30" transform="rotate(0,1284.33,740.222) scale(1.11111,0.814815) translate(-127.6,165.455)" width="15" x="1276" xlink:href="#Disconnector:刀闸_0" y="728" zvalue="1674"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454482329603" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454482329603"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1284.33,740.222) scale(1.11111,0.814815) translate(-127.6,165.455)" width="15" x="1276" y="728"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="6">
   <path class="kv110" d="M 599 313.67 L 1326 313.67" stroke-width="4" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674414100483" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674414100483"/></metadata>
  <path d="M 599 313.67 L 1326 313.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv10" d="M 505.13 672.89 L 1559 672.89" stroke-width="4" zvalue="15"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674414166019" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674414166019"/></metadata>
  <path d="M 505.13 672.89 L 1559 672.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="315">
   <path class="v400" d="M 1418 313.67 L 1767 313.67" stroke-width="4" zvalue="1681"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674414231555" ObjectName="0.4kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674414231555"/></metadata>
  <path d="M 1418 313.67 L 1767 313.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="19">
   <use class="kv110" height="20" transform="rotate(0,772,233.111) scale(1.22222,1.11111) translate(-139.253,-22.2)" width="10" x="765.8888888888889" xlink:href="#Breaker:开关_0" y="222" zvalue="16"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925176328195" ObjectName="110kV南葫线131断路器"/>
   <cge:TPSR_Ref TObjectID="6473925176328195"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,772,233.111) scale(1.22222,1.11111) translate(-139.253,-22.2)" width="10" x="765.8888888888889" y="222"/></g>
  <g id="341">
   <use class="kv10" height="20" transform="rotate(0,641.03,721.111) scale(2,2) translate(-315.515,-350.556)" width="10" x="631.0295695852833" xlink:href="#Breaker:小车断路器_0" y="701.1111128065321" zvalue="612"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925176393731" ObjectName="#1发电机031断路器"/>
   <cge:TPSR_Ref TObjectID="6473925176393731"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,641.03,721.111) scale(2,2) translate(-315.515,-350.556)" width="10" x="631.0295695852833" y="701.1111128065321"/></g>
  <g id="185">
   <use class="kv110" height="20" transform="rotate(0,883.333,394.222) scale(1.22222,1.11111) translate(-159.495,-38.3111)" width="10" x="877.2222324079938" xlink:href="#Breaker:开关_0" y="383.1111111111111" zvalue="1497"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925176459267" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925176459267"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,883.333,394.222) scale(1.22222,1.11111) translate(-159.495,-38.3111)" width="10" x="877.2222324079938" y="383.1111111111111"/></g>
  <g id="217">
   <use class="kv110" height="20" transform="rotate(0,1042,233.111) scale(1.22222,1.11111) translate(-188.343,-22.2)" width="10" x="1035.888888888889" xlink:href="#Breaker:开关_0" y="222" zvalue="1607"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924596006916" ObjectName="110kV梁大线葫芦口T线132断路器"/>
   <cge:TPSR_Ref TObjectID="6473924596006916"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1042,233.111) scale(1.22222,1.11111) translate(-188.343,-22.2)" width="10" x="1035.888888888889" y="222"/></g>
  <g id="303">
   <use class="kv10" height="20" transform="rotate(0,975.03,721.111) scale(2,2) translate(-482.515,-350.556)" width="10" x="965.0295695852833" xlink:href="#Breaker:小车断路器_0" y="701.1111128065321" zvalue="1649"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925176590341" ObjectName="#2发电机032断路器"/>
   <cge:TPSR_Ref TObjectID="6473925176590341"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,975.03,721.111) scale(2,2) translate(-482.515,-350.556)" width="10" x="965.0295695852833" y="701.1111128065321"/></g>
  <g id="3">
   <use class="v400" height="20" transform="rotate(0,1455.03,413) scale(2,2) translate(-722.516,-196.5)" width="10" x="1445.0329218107" xlink:href="#Breaker:小车断路器_0" y="393" zvalue="1685"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925176655877" ObjectName="#1站用变0.4kV侧401断路器"/>
   <cge:TPSR_Ref TObjectID="6473925176655877"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1455.03,413) scale(2,2) translate(-722.516,-196.5)" width="10" x="1445.0329218107" y="393"/></g>
  <g id="5">
   <use class="kv10" height="20" transform="rotate(0,1455,592) scale(2,2) translate(-722.5,-286)" width="10" x="1445" xlink:href="#Breaker:小车断路器_0" y="572" zvalue="1687"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925176721413" ObjectName="#1站用变10kV侧033断路器"/>
   <cge:TPSR_Ref TObjectID="6473925176721413"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1455,592) scale(2,2) translate(-722.5,-286)" width="10" x="1445" y="572"/></g>
  <g id="51">
   <use class="v400" height="20" transform="rotate(0,1710,413) scale(2,2) translate(-850,-196.5)" width="10" x="1700" xlink:href="#Breaker:小车断路器_0" y="393" zvalue="1702"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925176786949" ObjectName="#2站用变0.4kV侧402断路器"/>
   <cge:TPSR_Ref TObjectID="6473925176786949"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1710,413) scale(2,2) translate(-850,-196.5)" width="10" x="1700" y="393"/></g>
  <g id="12">
   <use class="kv10" height="20" transform="rotate(0,884.583,626.722) scale(1.22222,1.11111) translate(-159.722,-61.5611)" width="10" x="878.4722324079938" xlink:href="#Breaker:开关_0" y="615.6111111111111" zvalue="1715"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925219581955" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925219581955"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,884.583,626.722) scale(1.22222,1.11111) translate(-159.722,-61.5611)" width="10" x="878.4722324079938" y="615.6111111111111"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="23">
   <path class="kv110" d="M 771.9 292.48 L 771.9 313.67" stroke-width="1" zvalue="20"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@0" LinkObjectIDznd="6@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 771.9 292.48 L 771.9 313.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv110" d="M 771.93 268.65 L 771.93 243.72" stroke-width="1" zvalue="21"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@1" LinkObjectIDznd="19@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 771.93 268.65 L 771.93 243.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv110" d="M 771.96 222.48 L 771.9 196.48" stroke-width="1" zvalue="22"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="19@0" LinkObjectIDznd="22@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 771.96 222.48 L 771.9 196.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv110" d="M 771.93 172.65 L 771.93 136.22" stroke-width="1" zvalue="25"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@1" LinkObjectIDznd="27@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 771.93 172.65 L 771.93 136.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv110" d="M 734.44 256.28 L 771.93 256.28" stroke-width="1" zvalue="33"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="24" MaxPinNum="2"/>
   </metadata>
  <path d="M 734.44 256.28 L 771.93 256.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv110" d="M 734.44 157.39 L 771.93 157.39" stroke-width="1" zvalue="35"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 734.44 157.39 L 771.93 157.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv110" d="M 1231.12 280.48 L 1231.12 313.67" stroke-width="1" zvalue="98"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="6@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1231.12 280.48 L 1231.12 313.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv110" d="M 1231.15 256.65 L 1231.15 228.33" stroke-width="1" zvalue="99"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@1" LinkObjectIDznd="68@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1231.15 256.65 L 1231.15 228.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv110" d="M 1200.17 242.78 L 1231.15 242.78" stroke-width="1" zvalue="100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="97" MaxPinNum="2"/>
   </metadata>
  <path d="M 1200.17 242.78 L 1231.15 242.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="334">
   <path class="kv10" d="M 641.03 702.61 L 641.03 672.89" stroke-width="1" zvalue="623"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@0" LinkObjectIDznd="15@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 641.03 702.61 L 641.03 672.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv110" d="M 1254.03 224.37 L 1254.03 233 L 1231.15 233" stroke-width="1" zvalue="1430"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="97" MaxPinNum="2"/>
   </metadata>
  <path d="M 1254.03 224.37 L 1254.03 233 L 1231.15 233" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv110" d="M 883.4 366.24 L 883.4 383.59" stroke-width="1" zvalue="1503"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@1" LinkObjectIDznd="185@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 883.4 366.24 L 883.4 383.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv110" d="M 883.37 510.73 L 752.89 510.73 L 752.91 536.78" stroke-width="1" zvalue="1505"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@2" LinkObjectIDznd="181@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 883.37 510.73 L 752.89 510.73 L 752.91 536.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="kv110" d="M 845.28 370.06 L 883.4 370.06" stroke-width="1" zvalue="1508"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@0" LinkObjectIDznd="180" MaxPinNum="2"/>
   </metadata>
  <path d="M 845.28 370.06 L 883.4 370.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv110" d="M 883.43 342.4 L 883.43 313.67" stroke-width="1" zvalue="1511"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="6@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 883.43 342.4 L 883.43 313.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="kv10" d="M 641.03 739.11 L 641 881.88" stroke-width="1" zvalue="1572"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@1" LinkObjectIDznd="850@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 641.03 739.11 L 641 881.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv110" d="M 800.63 150.97 L 771.93 150.97" stroke-width="1" zvalue="1605"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 800.63 150.97 L 771.93 150.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv110" d="M 1041.9 292.48 L 1041.9 313.67" stroke-width="1" zvalue="1612"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@0" LinkObjectIDznd="6@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1041.9 292.48 L 1041.9 313.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="kv110" d="M 1041.93 268.65 L 1041.93 243.72" stroke-width="1" zvalue="1614"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@1" LinkObjectIDznd="217@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1041.93 268.65 L 1041.93 243.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="kv110" d="M 1041.96 222.48 L 1041.9 196.48" stroke-width="1" zvalue="1615"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217@0" LinkObjectIDznd="214@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1041.96 222.48 L 1041.9 196.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv110" d="M 1041.93 172.65 L 1041.93 136.22" stroke-width="1" zvalue="1618"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@1" LinkObjectIDznd="46@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1041.93 172.65 L 1041.93 136.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="206">
   <path class="kv110" d="M 1004.44 256.28 L 1041.93 256.28" stroke-width="1" zvalue="1623"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@0" LinkObjectIDznd="212" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004.44 256.28 L 1041.93 256.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="205">
   <path class="kv110" d="M 1004.44 157.39 L 1041.93 157.39" stroke-width="1" zvalue="1624"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="209" MaxPinNum="2"/>
   </metadata>
  <path d="M 1004.44 157.39 L 1041.93 157.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="203">
   <path class="kv110" d="M 1070.63 150.97 L 1041.93 150.97" stroke-width="1" zvalue="1626"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="204@0" LinkObjectIDznd="209" MaxPinNum="2"/>
   </metadata>
  <path d="M 1070.63 150.97 L 1041.93 150.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="kv110" d="M 818.03 536.63 L 818.03 510.73" stroke-width="1" zvalue="1629"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@0" LinkObjectIDznd="178" MaxPinNum="2"/>
   </metadata>
  <path d="M 818.03 536.63 L 818.03 510.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="273">
   <path class="kv10" d="M 625.93 801 L 641.02 801" stroke-width="1" zvalue="1639"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@1" LinkObjectIDznd="81" MaxPinNum="2"/>
   </metadata>
  <path d="M 625.93 801 L 641.02 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="274">
   <path class="kv10" d="M 600.03 801.08 L 555 801.08 L 555 870.5" stroke-width="1" zvalue="1640"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="10@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 600.03 801.08 L 555 801.08 L 555 870.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="275">
   <path class="kv10" d="M 584 877.75 L 584 801.08" stroke-width="1" zvalue="1641"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="274" MaxPinNum="2"/>
   </metadata>
  <path d="M 584 877.75 L 584 801.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="277">
   <path class="kv10" d="M 716.95 875.35 L 716.95 801 L 641.02 801" stroke-width="1" zvalue="1643"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="81" MaxPinNum="2"/>
   </metadata>
  <path d="M 716.95 875.35 L 716.95 801 L 641.02 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="kv10" d="M 745.5 885.72 L 745.5 801 L 712.95 801" stroke-width="1" zvalue="1644"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@0" LinkObjectIDznd="277" MaxPinNum="2"/>
   </metadata>
  <path d="M 745.5 885.72 L 745.5 801 L 712.95 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="281">
   <path class="kv10" d="M 668.17 854.72 L 668.17 801" stroke-width="1" zvalue="1647"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="279@0" LinkObjectIDznd="277" MaxPinNum="2"/>
   </metadata>
  <path d="M 668.17 854.72 L 668.17 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="302">
   <path class="kv10" d="M 975.03 702.61 L 975.03 672.89" stroke-width="1" zvalue="1651"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="303@0" LinkObjectIDznd="15@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 975.03 702.61 L 975.03 672.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="297">
   <path class="kv10" d="M 975.03 739.11 L 975 881.88" stroke-width="1" zvalue="1658"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="303@1" LinkObjectIDznd="301@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 975.03 739.11 L 975 881.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="293">
   <path class="kv10" d="M 959.93 801 L 975.02 801" stroke-width="1" zvalue="1660"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="300@1" LinkObjectIDznd="297" MaxPinNum="2"/>
   </metadata>
  <path d="M 959.93 801 L 975.02 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="292">
   <path class="kv10" d="M 934.03 801.08 L 889 801.08 L 889 870.5" stroke-width="1" zvalue="1661"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="300@0" LinkObjectIDznd="299@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 934.03 801.08 L 889 801.08 L 889 870.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="291">
   <path class="kv10" d="M 918 877.75 L 918 801.08" stroke-width="1" zvalue="1662"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="294@0" LinkObjectIDznd="292" MaxPinNum="2"/>
   </metadata>
  <path d="M 918 877.75 L 918 801.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="289">
   <path class="kv10" d="M 1050.95 875.35 L 1050.95 801 L 975.02 801" stroke-width="1" zvalue="1664"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="290@0" LinkObjectIDznd="297" MaxPinNum="2"/>
   </metadata>
  <path d="M 1050.95 875.35 L 1050.95 801 L 975.02 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="288">
   <path class="kv10" d="M 1079.5 885.72 L 1079.5 801 L 1046.95 801" stroke-width="1" zvalue="1665"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="298@0" LinkObjectIDznd="289" MaxPinNum="2"/>
   </metadata>
  <path d="M 1079.5 885.72 L 1079.5 801 L 1046.95 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="286">
   <path class="kv10" d="M 1002.17 854.72 L 1002.17 801" stroke-width="1" zvalue="1668"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="287@0" LinkObjectIDznd="289" MaxPinNum="2"/>
   </metadata>
  <path d="M 1002.17 854.72 L 1002.17 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="304">
   <path class="kv10" d="M 754.91 620.78 L 754.91 594 L 883.34 594" stroke-width="1" zvalue="1669"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="254@0" LinkObjectIDznd="14" MaxPinNum="2"/>
   </metadata>
  <path d="M 754.91 620.78 L 754.91 594 L 883.34 594" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="305">
   <path class="kv10" d="M 820.03 620.63 L 820.03 594" stroke-width="1" zvalue="1670"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@0" LinkObjectIDznd="304" MaxPinNum="2"/>
   </metadata>
  <path d="M 820.03 620.63 L 820.03 594" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv10" d="M 1284.43 728.4 L 1284.43 672.89" stroke-width="1" zvalue="1675"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="308@0" LinkObjectIDznd="15@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1284.43 728.4 L 1284.43 672.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="312">
   <path class="kv10" d="M 1284.4 752.24 L 1284.4 779.35" stroke-width="1" zvalue="1676"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="308@1" LinkObjectIDznd="306@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1284.4 752.24 L 1284.4 779.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="314">
   <path class="kv10" d="M 1306.63 704.97 L 1284.43 704.97" stroke-width="1" zvalue="1679"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="313@0" LinkObjectIDznd="311" MaxPinNum="2"/>
   </metadata>
  <path d="M 1306.63 704.97 L 1284.43 704.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="v400" d="M 1455.03 394.5 L 1455.03 313.67" stroke-width="1" zvalue="1689"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="3@0" LinkObjectIDznd="315@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1455.03 394.5 L 1455.03 313.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv10" d="M 1455 610 L 1455 672.89" stroke-width="1" zvalue="1691"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@1" LinkObjectIDznd="15@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1455 610 L 1455 672.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv10" d="M 1455.03 524.55 L 1455 573.5" stroke-width="1" zvalue="1698"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="5@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1455.03 524.55 L 1455 573.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="v400" d="M 1455 475.35 L 1455.03 431" stroke-width="1" zvalue="1699"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@1" LinkObjectIDznd="3@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1455 475.35 L 1455.03 431" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="v400" d="M 1710 394.5 L 1710 313.67" stroke-width="1" zvalue="1703"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="315@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1710 394.5 L 1710 313.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="v400" d="M 1710 431 L 1710 474.37" stroke-width="1" zvalue="1704"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@1" LinkObjectIDznd="47@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1710 431 L 1710 474.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv10" d="M 1522.03 579.63 L 1522.03 541 L 1455.02 541" stroke-width="1" zvalue="1712"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="44" MaxPinNum="2"/>
   </metadata>
  <path d="M 1522.03 579.63 L 1522.03 541 L 1455.02 541" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv10" d="M 1556.91 579.78 L 1556.91 541 L 1522.03 541" stroke-width="1" zvalue="1713"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@0" LinkObjectIDznd="83" MaxPinNum="2"/>
   </metadata>
  <path d="M 1556.91 579.78 L 1556.91 541 L 1522.03 541" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv10" d="M 883.34 579.47 L 883.34 616.09" stroke-width="1" zvalue="1716"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@1" LinkObjectIDznd="12@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 883.34 579.47 L 883.34 616.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv10" d="M 884.66 637.33 L 884.66 672.89" stroke-width="1" zvalue="1717"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="12@1" LinkObjectIDznd="15@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 884.66 637.33 L 884.66 672.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv110" d="M 883.41 404.83 L 883.4 482.89" stroke-width="1" zvalue="1784"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@1" LinkObjectIDznd="163@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 883.41 404.83 L 883.4 482.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="27">
   <use class="kv110" height="30" transform="rotate(0,771.932,114.222) scale(6.34921,1.48148) translate(-631.631,-29.9)" width="7" x="749.7098173330401" xlink:href="#ACLineSegment:线路_0" y="91.99999999999994" zvalue="24"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249331269634" ObjectName="110kV南葫线"/>
   <cge:TPSR_Ref TObjectID="8444249331269634_5066549682438145"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,771.932,114.222) scale(6.34921,1.48148) translate(-631.631,-29.9)" width="7" x="749.7098173330401" y="91.99999999999994"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="1">
   <use class="kv110" height="20" transform="rotate(90,723.611,256.222) scale(1.11111,1.11111) translate(-71.8056,-24.5111)" width="10" x="718.0555556615193" xlink:href="#GroundDisconnector:地刀_0" y="245.1111111111111" zvalue="26"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454479446019" ObjectName="110kV南葫线13117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454479446019"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,723.611,256.222) scale(1.11111,1.11111) translate(-71.8056,-24.5111)" width="10" x="718.0555556615193" y="245.1111111111111"/></g>
  <g id="32">
   <use class="kv110" height="20" transform="rotate(90,723.611,157.333) scale(1.11111,1.11111) translate(-71.8056,-14.6222)" width="10" x="718.0555555555555" xlink:href="#GroundDisconnector:地刀_0" y="146.2222222222221" zvalue="31"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454479577091" ObjectName="110kV南葫线13167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454479577091"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,723.611,157.333) scale(1.11111,1.11111) translate(-71.8056,-14.6222)" width="10" x="718.0555555555555" y="146.2222222222221"/></g>
  <g id="91">
   <use class="kv110" height="20" transform="rotate(90,1189.33,242.722) scale(1.11111,1.11111) translate(-118.378,-23.1611)" width="10" x="1183.777784559462" xlink:href="#GroundDisconnector:地刀_0" y="231.6111008326212" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454479773699" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454479773699"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1189.33,242.722) scale(1.11111,1.11111) translate(-118.378,-23.1611)" width="10" x="1183.777784559462" y="231.6111008326212"/></g>
  <g id="181">
   <use class="kv110" height="20" transform="rotate(0,752.778,549.667) scale(2.64444,1.32222) translate(-459.892,-130.73)" width="10" x="739.5555555555555" xlink:href="#GroundDisconnector:地刀_0" y="536.4444444444445" zvalue="1501"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454480429059" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454480429059"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,752.778,549.667) scale(2.64444,1.32222) translate(-459.892,-130.73)" width="10" x="739.5555555555555" y="536.4444444444445"/></g>
  <g id="177">
   <use class="kv110" height="20" transform="rotate(90,834.444,370) scale(1.11111,1.11111) translate(-82.8889,-35.8889)" width="10" x="828.8888990614149" xlink:href="#GroundDisconnector:地刀_0" y="358.8888888888889" zvalue="1506"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454480297987" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454480297987"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,834.444,370) scale(1.11111,1.11111) translate(-82.8889,-35.8889)" width="10" x="828.8888990614149" y="358.8888888888889"/></g>
  <g id="208">
   <use class="kv110" height="20" transform="rotate(90,993.611,256.222) scale(1.11111,1.11111) translate(-98.8056,-24.5111)" width="10" x="988.0555556615193" xlink:href="#GroundDisconnector:地刀_0" y="245.1111111111111" zvalue="1619"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450329444357" ObjectName="110kV梁大线葫芦口T线13217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450329444357"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,993.611,256.222) scale(1.11111,1.11111) translate(-98.8056,-24.5111)" width="10" x="988.0555556615193" y="245.1111111111111"/></g>
  <g id="207">
   <use class="kv110" height="20" transform="rotate(90,993.611,157.333) scale(1.11111,1.11111) translate(-98.8056,-14.6222)" width="10" x="988.0555555555555" xlink:href="#GroundDisconnector:地刀_0" y="146.2222222222221" zvalue="1621"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450329313285" ObjectName="110kV梁大线葫芦口T线13267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450329313285"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,993.611,157.333) scale(1.11111,1.11111) translate(-98.8056,-14.6222)" width="10" x="988.0555555555555" y="146.2222222222221"/></g>
  <g id="254">
   <use class="kv10" height="20" transform="rotate(0,754.778,633.667) scale(2.64444,1.32222) translate(-461.135,-151.201)" width="10" x="741.5555555555555" xlink:href="#GroundDisconnector:地刀_0" y="620.4444444444445" zvalue="1631"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454481412099" ObjectName="#1主变10kV侧00167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454481412099"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,754.778,633.667) scale(2.64444,1.32222) translate(-461.135,-151.201)" width="10" x="741.5555555555555" y="620.4444444444445"/></g>
  <g id="279">
   <use class="kv10" height="20" transform="rotate(0,668.111,865.556) scale(1.11111,1.11111) translate(-66.2556,-85.4444)" width="10" x="662.5555555555555" xlink:href="#GroundDisconnector:地刀_0" y="854.4444444444443" zvalue="1646"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454481674243" ObjectName="#1发电机09127接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454481674243"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,668.111,865.556) scale(1.11111,1.11111) translate(-66.2556,-85.4444)" width="10" x="662.5555555555555" y="854.4444444444443"/></g>
  <g id="287">
   <use class="kv10" height="20" transform="rotate(0,1002.11,865.556) scale(1.11111,1.11111) translate(-99.6556,-85.4444)" width="10" x="996.5555555555555" xlink:href="#GroundDisconnector:地刀_0" y="854.4444444444443" zvalue="1666"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454481805315" ObjectName="#2发电机09227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454481805315"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1002.11,865.556) scale(1.11111,1.11111) translate(-99.6556,-85.4444)" width="10" x="996.5555555555555" y="854.4444444444443"/></g>
  <g id="78">
   <use class="kv10" height="20" transform="rotate(0,1556.78,592.667) scale(2.64444,1.32222) translate(-959.858,-141.209)" width="10" x="1543.555555555556" xlink:href="#GroundDisconnector:地刀_0" y="579.4444444444445" zvalue="1706"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454482657283" ObjectName="#1站用变03367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454482657283"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1556.78,592.667) scale(2.64444,1.32222) translate(-959.858,-141.209)" width="10" x="1543.555555555556" y="579.4444444444445"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="AccessoryClass">
  <g id="68">
   <use class="kv110" height="30" transform="rotate(0,1231.24,207.25) scale(1.73333,-1.51667) translate(-509.91,-336.148)" width="30" x="1205.24092841766" xlink:href="#Accessory:带熔断器35kVPT11_0" y="184.5" zvalue="1093"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454479839235" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1231.24,207.25) scale(1.73333,-1.51667) translate(-509.91,-336.148)" width="30" x="1205.24092841766" y="184.5"/></g>
  <g id="52">
   <use class="kv110" height="26" transform="rotate(0,1254,212) scale(1,-1) translate(0,-424)" width="12" x="1248" xlink:href="#Accessory:避雷器1_0" y="199" zvalue="1429"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454480035843" ObjectName="110kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1254,212) scale(1,-1) translate(0,-424)" width="12" x="1248" y="199"/></g>
  <g id="10">
   <use class="kv10" height="40" transform="rotate(0,555,889) scale(1,-1) translate(0,-1778)" width="30" x="540" xlink:href="#Accessory:带熔断器的线路PT1_0" y="869" zvalue="1453"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454480101379" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,555,889) scale(1,-1) translate(0,-1778)" width="30" x="540" y="869"/></g>
  <g id="77">
   <use class="kv10" height="18" transform="rotate(0,745.5,894.929) scale(1.21429,1.21429) translate(-130.059,-156)" width="14" x="737" xlink:href="#Accessory:PT7_0" y="884" zvalue="1455"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454480166915" ObjectName="#1发电机励磁变"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,745.5,894.929) scale(1.21429,1.21429) translate(-130.059,-156)" width="14" x="737" y="884"/></g>
  <g id="166">
   <use class="kv110" height="26" transform="rotate(270,813,151) scale(1,1) translate(0,0)" width="12" x="807" xlink:href="#Accessory:避雷器1_0" y="138" zvalue="1604"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454480625667" ObjectName="110kV南葫线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,813,151) scale(1,1) translate(0,0)" width="12" x="807" y="138"/></g>
  <g id="204">
   <use class="kv110" height="26" transform="rotate(270,1083,151) scale(1,1) translate(0,0)" width="12" x="1077" xlink:href="#Accessory:避雷器1_0" y="138" zvalue="1625"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454480691203" ObjectName="110kV梁大线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1083,151) scale(1,1) translate(0,0)" width="12" x="1077" y="138"/></g>
  <g id="219">
   <use class="kv110" height="26" transform="rotate(0,818,549) scale(1,1) translate(0,0)" width="12" x="812" xlink:href="#Accessory:避雷器1_0" y="536" zvalue="1628"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454481215491" ObjectName="#1主变避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,818,549) scale(1,1) translate(0,0)" width="12" x="812" y="536"/></g>
  <g id="253">
   <use class="kv10" height="26" transform="rotate(0,820,633) scale(1,1) translate(0,0)" width="12" x="814" xlink:href="#Accessory:避雷器1_0" y="620" zvalue="1633"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454481281027" ObjectName="#1主变避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,820,633) scale(1,1) translate(0,0)" width="12" x="814" y="620"/></g>
  <g id="272">
   <use class="kv10" height="29" transform="rotate(0,584,892) scale(1,-1) translate(0,-1784)" width="30" x="569" xlink:href="#Accessory:PT12321_0" y="877.5" zvalue="1638"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454481477635" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,584,892) scale(1,-1) translate(0,-1784)" width="30" x="569" y="877.5"/></g>
  <g id="276">
   <use class="kv10" height="30" transform="rotate(0,717,890) scale(1,1) translate(0,0)" width="30" x="702" xlink:href="#Accessory:PT789_0" y="875" zvalue="1642"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454481543171" ObjectName="#1发电机PT3"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,717,890) scale(1,1) translate(0,0)" width="30" x="702" y="875"/></g>
  <g id="299">
   <use class="kv10" height="40" transform="rotate(0,889,889) scale(1,-1) translate(0,-1778)" width="30" x="874" xlink:href="#Accessory:带熔断器的线路PT1_0" y="869" zvalue="1656"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454482067459" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,889,889) scale(1,-1) translate(0,-1778)" width="30" x="874" y="869"/></g>
  <g id="298">
   <use class="kv10" height="18" transform="rotate(0,1079.5,894.929) scale(1.21429,1.21429) translate(-189,-156)" width="14" x="1071" xlink:href="#Accessory:PT7_0" y="884" zvalue="1657"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454482001923" ObjectName="#2发电机励磁变"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1079.5,894.929) scale(1.21429,1.21429) translate(-189,-156)" width="14" x="1071" y="884"/></g>
  <g id="294">
   <use class="kv10" height="29" transform="rotate(0,918,892) scale(1,-1) translate(0,-1784)" width="30" x="903" xlink:href="#Accessory:PT12321_0" y="877.5" zvalue="1659"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454481936387" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,918,892) scale(1,-1) translate(0,-1784)" width="30" x="903" y="877.5"/></g>
  <g id="290">
   <use class="kv10" height="30" transform="rotate(0,1051,890) scale(1,1) translate(0,0)" width="30" x="1036" xlink:href="#Accessory:PT789_0" y="875" zvalue="1663"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454481870851" ObjectName="#2发电机PT3"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1051,890) scale(1,1) translate(0,0)" width="30" x="1036" y="875"/></g>
  <g id="306">
   <use class="kv10" height="30" transform="rotate(0,1284.45,794) scale(1,1) translate(0,0)" width="30" x="1269.451293804562" xlink:href="#Accessory:PT789_0" y="779" zvalue="1672"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454482264067" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1284.45,794) scale(1,1) translate(0,0)" width="30" x="1269.451293804562" y="779"/></g>
  <g id="313">
   <use class="kv10" height="26" transform="rotate(270,1319,705) scale(1,1) translate(0,0)" width="12" x="1313" xlink:href="#Accessory:避雷器1_0" y="692" zvalue="1678"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454482395139" ObjectName="10kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1319,705) scale(1,1) translate(0,0)" width="12" x="1313" y="692"/></g>
  <g id="74">
   <use class="kv10" height="26" transform="rotate(0,1522,592) scale(1,1) translate(0,0)" width="12" x="1516" xlink:href="#Accessory:避雷器1_0" y="579" zvalue="1708"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454482526211" ObjectName="1号站用变避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1522,592) scale(1,1) translate(0,0)" width="12" x="1516" y="579"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="kv10" height="30" transform="rotate(0,641,904) scale(1.5,1.5) translate(-206.167,-293.833)" width="30" x="618.5" xlink:href="#Generator:发电机_0" y="881.5" zvalue="1260"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454479904771" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192454479904771"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,641,904) scale(1.5,1.5) translate(-206.167,-293.833)" width="30" x="618.5" y="881.5"/></g>
  <g id="301">
   <use class="kv10" height="30" transform="rotate(0,975,904) scale(1.5,1.5) translate(-317.5,-293.833)" width="30" x="952.5" xlink:href="#Generator:发电机_0" y="881.5" zvalue="1652"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454482198531" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192454482198531"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,975,904) scale(1.5,1.5) translate(-317.5,-293.833)" width="30" x="952.5" y="881.5"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="163">
   <g id="1630">
    <use class="kv110" height="50" transform="rotate(0,883.336,531.081) scale(1.98333,1.96323) translate(-423.207,-236.486)" width="30" x="853.59" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="482" zvalue="1522"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874583179266" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1631">
    <use class="kv10" height="50" transform="rotate(0,883.336,531.081) scale(1.98333,1.96323) translate(-423.207,-236.486)" width="30" x="853.59" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="482" zvalue="1522"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874588487682" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399529594882" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399529594882"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,883.336,531.081) scale(1.98333,1.96323) translate(-423.207,-236.486)" width="30" x="853.59" y="482"/></g>
  <g id="37">
   <g id="370">
    <use class="kv10" height="50" transform="rotate(0,1455,500) scale(1,-1) translate(0,-1000)" width="30" x="1440" xlink:href="#PowerTransformer2:586_0" y="475" zvalue="1694"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874583310338" ObjectName="10"/>
    </metadata>
   </g>
   <g id="371">
    <use class="v400" height="50" transform="rotate(0,1455,500) scale(1,-1) translate(0,-1000)" width="30" x="1440" xlink:href="#PowerTransformer2:586_1" y="475" zvalue="1694"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874583375874" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399529660418" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1455,500) scale(1,-1) translate(0,-1000)" width="30" x="1440" y="475"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="47">
   <use class="v400" height="48" transform="rotate(0,1710.07,509.5) scale(1.47917,1.47917) translate(-549.655,-153.549)" width="18" x="1696.761458333333" xlink:href="#EnergyConsumer:站用897_0" y="474" zvalue="1700"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454482460675" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,1710.07,509.5) scale(1.47917,1.47917) translate(-549.655,-153.549)" width="18" x="1696.761458333333" y="474"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="36">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="36" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,966.836,387.5) scale(1,1) translate(0,0)" writing-mode="lr" x="966.26" xml:space="preserve" y="392.28" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135387049986" ObjectName="P"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="39" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,969.836,404.5) scale(1,1) translate(0,0)" writing-mode="lr" x="969.26" xml:space="preserve" y="409.28" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135387115522" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="40">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="40" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,965.836,600.661) scale(1,1) translate(0,0)" writing-mode="lr" x="965.26" xml:space="preserve" y="605.4400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136107487234" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="41">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="41" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,965.836,616.661) scale(1,1) translate(0,-1.33041e-13)" writing-mode="lr" x="965.26" xml:space="preserve" y="621.4400000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136107552770" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="62" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,969.836,420.5) scale(1,1) translate(0,0)" writing-mode="lr" x="969.26" xml:space="preserve" y="425.28" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135387312130" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="63" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,968.836,630.661) scale(1,1) translate(0,0)" writing-mode="lr" x="968.26" xml:space="preserve" y="635.4400000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136107618306" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="64" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,637.5,982) scale(1,1) translate(0,0)" writing-mode="lr" x="636.92" xml:space="preserve" y="986.78" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135386394626" ObjectName="P"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="73" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,974.5,987) scale(1,1) translate(0,0)" writing-mode="lr" x="973.92" xml:space="preserve" y="991.78" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135391178754" ObjectName="P"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="80" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,639.5,1001) scale(1,1) translate(0,0)" writing-mode="lr" x="638.92" xml:space="preserve" y="1005.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135386460162" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="82">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="82" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,975.5,1002) scale(1,1) translate(0,0)" writing-mode="lr" x="974.92" xml:space="preserve" y="1006.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135391244290" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="85" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,641.5,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="640.92" xml:space="preserve" y="1024.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135386525698" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="87" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,975.5,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="974.92" xml:space="preserve" y="1024.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135391309826" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="88" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,574.133,656.889) scale(1,1) translate(0,0)" writing-mode="lr" x="573.66" xml:space="preserve" y="661.67" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135383969794" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="94" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,636,300.667) scale(1,1) translate(0,0)" writing-mode="lr" x="635.53" xml:space="preserve" y="305.44" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135383445506" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="95" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1457,293.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1456.53" xml:space="preserve" y="298.44" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135392555010" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="99" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,760.932,30.5) scale(1,1) translate(0,0)" writing-mode="lr" x="760.46" xml:space="preserve" y="35.28" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135384821762" ObjectName="P"/>
   </metadata>
  </g>
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="101" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,760.932,43.5) scale(1,1) translate(0,0)" writing-mode="lr" x="760.46" xml:space="preserve" y="48.28" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135384887298" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="103" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,763.932,59.5) scale(1,1) translate(0,0)" writing-mode="lr" x="763.46" xml:space="preserve" y="64.28" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135384952834" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="121">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,153.111,325.917) scale(1,1) translate(0,0)" writing-mode="lr" x="153.27" xml:space="preserve" y="330.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135383642114" ObjectName="F"/>
   </metadata>
  </g>
  <g id="120">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="120" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,153.111,253.917) scale(1,1) translate(0,0)" writing-mode="lr" x="153.27" xml:space="preserve" y="258.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127232143364" ObjectName="葫芦口电站上网有功"/>
   </metadata>
  </g>
  <g id="119">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,330.722,254.917) scale(1,1) translate(0,0)" writing-mode="lr" x="330.88" xml:space="preserve" y="259.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135397273602" ObjectName="F"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="246" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,330.722,326.917) scale(1,1) translate(0,0)" writing-mode="lr" x="330.88" xml:space="preserve" y="331.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135384166402" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,153.111,278.917) scale(1,1) translate(0,0)" writing-mode="lr" x="153.27" xml:space="preserve" y="283.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135397076994" ObjectName="F"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,330.722,279.917) scale(1,1) translate(0,0)" writing-mode="lr" x="330.88" xml:space="preserve" y="284.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135397142530" ObjectName="F"/>
   </metadata>
  </g>
  <g id="113">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="113" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,153.111,371.139) scale(1,1) translate(0,0)" writing-mode="lr" x="153.27" xml:space="preserve" y="376.05" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136556081154" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="111" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,331.111,371.139) scale(1,1) translate(0,0)" writing-mode="lr" x="331.27" xml:space="preserve" y="376.05" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136556408834" ObjectName="雨量采集实测值"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,153.111,302.917) scale(1,1) translate(0,0)" writing-mode="lr" x="153.27" xml:space="preserve" y="307.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127179190277" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,333.111,301.917) scale(1,1) translate(0,0)" writing-mode="lr" x="333.27" xml:space="preserve" y="306.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127179124744" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="143" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,148.111,348.417) scale(1,1) translate(0,0)" writing-mode="lr" x="148.27" xml:space="preserve" y="353.33" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481135392751618" ObjectName="F"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,149.111,392.75) scale(1,1) translate(0,0)" writing-mode="lr" x="149.27" xml:space="preserve" y="397.66" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136556146690" ObjectName="坝下水位"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="53" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1035.93,17.5) scale(1,1) translate(0,-1.55432e-15)" writing-mode="lr" x="992.8099999999999" xml:space="preserve" y="21.92" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128115372036" ObjectName="P"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="56" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1035.93,43.5) scale(1,1) translate(0,-7.32748e-15)" writing-mode="lr" x="992.8099999999999" xml:space="preserve" y="47.92" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128115437572" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="57" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1035.93,65.5) scale(1,1) translate(0,-1.22125e-14)" writing-mode="lr" x="992.8099999999999" xml:space="preserve" y="69.92" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128115503108" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="118">
   <use height="30" transform="rotate(0,328.173,180.107) scale(0.708333,0.665547) translate(130.755,85.4914)" width="30" x="317.55" xlink:href="#State:红绿圆(方形)_0" y="170.12" zvalue="1761"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374926217217" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,328.173,180.107) scale(0.708333,0.665547) translate(130.755,85.4914)" width="30" x="317.55" y="170.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,232.548,180.107) scale(0.708333,0.665547) translate(91.3799,85.4914)" width="30" x="221.92" xlink:href="#State:红绿圆(方形)_0" y="170.12" zvalue="1762"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950559629316" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,232.548,180.107) scale(0.708333,0.665547) translate(91.3799,85.4914)" width="30" x="221.92" y="170.12"/></g>
  <g id="42">
   <use height="30" transform="rotate(0,317,123.464) scale(1.22222,1.03092) translate(-47.6364,-3.23939)" width="90" x="262" xlink:href="#State:全站检修_0" y="108" zvalue="1783"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549682438145" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,317,123.464) scale(1.22222,1.03092) translate(-47.6364,-3.23939)" width="90" x="262" y="108"/></g>
 </g>
</svg>