<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549679554561" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.08333333333333" x2="9.833333333333332" y1="16.5" y2="18.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14" y1="9.75" y2="16.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="11" y1="11.58333333333333" y2="9.666666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="9.75" y1="11.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8" x2="15.08333333333333" y1="8.416666666666668" y2="25.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_1" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.96,13) scale(1,1) translate(0,0)" width="4.42" x="12.75" y="9"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="5.416666666666668" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_2" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="20" y1="6" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="10.08333333333333" y1="6.000000000000004" y2="25.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变11_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="9.25" y2="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="26.83333333333333" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0928507596068" x2="10.0928507596068" y1="10.97013412501683" y2="12.64189293057399"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.77292225201073" x2="10.0928507596068" y1="14.31365173613114" y2="12.64189293057398"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:母线电压互感器11_0" viewBox="0,0,45,48">
   <use terminal-index="0" type="0" x="25.25" xlink:href="#terminal" y="47.78457520218115"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.5,30.95) scale(1,-1) translate(0,-1330.05)" width="6" x="10.5" y="23.95"/>
   <path d="M 8.64167 11.4096 L 4.64167 11.4096 L 6.50709 7.52624 L 7.67376 9.52624 L 8.64167 11.4096" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="13.5" y1="19.78457520218114" y2="40.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.25" x2="25.25" y1="40.5" y2="47.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="37.25" x2="13.58333333333333" y1="40.5" y2="40.5"/>
   <ellipse cx="13.56" cy="5.42" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799663" x2="13.56620079799663" y1="3.155388266900378" y2="5.82957386674455"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799661" x2="10.88240159599323" y1="5.829573866744575" y2="7.166666666666657"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799662" x2="16.25" y1="5.829573866744575" y2="7.166666666666657"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,36.96,25.94) scale(1,-1) translate(0,-1129.72)" width="7.58" x="33.17" y="17.87"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="34.01790853551448" y2="24.11790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="40.43457520218114" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.7861111111111" x2="39.3" y1="11.60124186884782" y2="11.60124186884782"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.82777777777777" x2="39.93888888888888" y1="12.98282081621623" y2="12.98282081621623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.55" x2="41.21666666666666" y1="14.36439976358464" y2="14.36439976358464"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="34.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="18.01790853551448" y2="14.41790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="40.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <ellipse cx="13.59" cy="15.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.51" cy="10.09" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.67" cy="10.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490864" x2="10.91228003290526" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="13.59607923490865" y1="12.74178419246191" y2="15.41596979230608"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51274590157532" x2="23.19654510357869" y1="10.49930312563944" y2="11.83639592556152"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51274590157532" x2="20.51274590157532" y1="7.825117525795246" y2="10.49930312563942"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="16.27987843691203" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5127459015753" x2="17.82894669957193" y1="10.49930312563944" y2="11.83639592556152"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV弄岛变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="DisconnectorClass">
  <g id="89">
   <use class="kv35" height="30" transform="rotate(0,1107.22,260.667) scale(-1.11111,-0.814815) translate(-2102.89,-583.354)" width="15" x="1098.888888888889" xlink:href="#Disconnector:刀闸_0" y="248.4444581137762" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453855477762" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453855477762"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1107.22,260.667) scale(-1.11111,-0.814815) translate(-2102.89,-583.354)" width="15" x="1098.888888888889" y="248.4444581137762"/></g>
  <g id="21">
   <use class="kv35" height="30" transform="rotate(0,920,284.667) scale(-1.11111,-0.814815) translate(-1747.17,-636.808)" width="15" x="911.6666666666666" xlink:href="#Disconnector:刀闸_0" y="272.4444580078125" zvalue="17"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453855150082" ObjectName="35kV弄班线3621隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453855150082"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,920,284.667) scale(-1.11111,-0.814815) translate(-1747.17,-636.808)" width="15" x="911.6666666666666" y="272.4444580078125"/></g>
  <g id="22">
   <use class="kv35" height="30" transform="rotate(0,920,176.667) scale(-1.11111,-0.814815) translate(-1747.17,-396.263)" width="15" x="911.6666666931577" xlink:href="#Disconnector:刀闸_0" y="164.4444444444445" zvalue="19"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453855215618" ObjectName="35kV弄班线3626隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453855215618"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,920,176.667) scale(-1.11111,-0.814815) translate(-1747.17,-396.263)" width="15" x="911.6666666931577" y="164.4444444444445"/></g>
  <g id="99">
   <use class="kv35" height="30" transform="rotate(0,879.333,364.222) scale(1.11111,0.814815) translate(-87.1,80)" width="15" x="871.0000101725263" xlink:href="#Disconnector:刀闸_0" y="352" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453855739906" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453855739906"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,879.333,364.222) scale(1.11111,0.814815) translate(-87.1,80)" width="15" x="871.0000101725263" y="352"/></g>
  <g id="320">
   <use class="kv10" height="30" transform="rotate(0,682.962,825.444) scale(1.11111,0.814815) translate(-67.4628,184.823)" width="15" x="674.6282757807213" xlink:href="#Disconnector:刀闸_0" y="813.2222256130642" zvalue="641"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453855870978" ObjectName="10kV开发区线0616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453855870978"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,682.962,825.444) scale(1.11111,0.814815) translate(-67.4628,184.823)" width="15" x="674.6282757807213" y="813.2222256130642"/></g>
  <g id="51">
   <use class="kv35" height="30" transform="rotate(0,1338,363.556) scale(1.11111,0.814815) translate(-132.967,79.8485)" width="15" x="1329.666666666667" xlink:href="#Disconnector:刀闸_0" y="351.3333282470703" zvalue="1107"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453856198658" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453856198658"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1338,363.556) scale(1.11111,0.814815) translate(-132.967,79.8485)" width="15" x="1329.666666666667" y="351.3333282470703"/></g>
  <g id="313">
   <use class="kv35" height="30" transform="rotate(0,1336.15,285.722) scale(-1.11111,-0.814815) translate(-2537.85,-639.159)" width="15" x="1327.816108619377" xlink:href="#Disconnector:刀闸_0" y="273.5000133514404" zvalue="1369"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453856657411" ObjectName="35kV汉相弄线3611隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453856657411"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1336.15,285.722) scale(-1.11111,-0.814815) translate(-2537.85,-639.159)" width="15" x="1327.816108619377" y="273.5000133514404"/></g>
  <g id="309">
   <use class="kv35" height="30" transform="rotate(0,1336,177.722) scale(-1.11111,-0.814815) translate(-2537.57,-398.614)" width="15" x="1327.666666693158" xlink:href="#Disconnector:刀闸_0" y="165.4999997880724" zvalue="1372"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453856591874" ObjectName="35kV汉相弄线3616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453856591874"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1336,177.722) scale(-1.11111,-0.814815) translate(-2537.57,-398.614)" width="15" x="1327.666666693158" y="165.4999997880724"/></g>
  <g id="3">
   <use class="kv35" height="30" transform="rotate(0,1292.67,154) scale(1,1) translate(0,0)" width="30" x="1277.666666666667" xlink:href="#Disconnector:跌落刀闸_0" y="139" zvalue="1392"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453856919555" ObjectName="#1站用变跌落保险"/>
   <cge:TPSR_Ref TObjectID="6192453856919555"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1292.67,154) scale(1,1) translate(0,0)" width="30" x="1277.666666666667" y="139"/></g>
  <g id="33">
   <use class="kv10" height="30" transform="rotate(0,1338.51,655.222) scale(-1.11111,-0.814815) translate(-2542.34,-1462.14)" width="15" x="1330.177950298703" xlink:href="#Disconnector:刀闸_0" y="643" zvalue="1400"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453856985090" ObjectName="#2主变10kV侧0022隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453856985090"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1338.51,655.222) scale(-1.11111,-0.814815) translate(-2542.34,-1462.14)" width="15" x="1330.177950298703" y="643"/></g>
  <g id="240">
   <use class="kv10" height="30" transform="rotate(0,879.333,655.474) scale(-1.11111,-0.814815) translate(-1669.9,-1462.7)" width="15" x="871" xlink:href="#Disconnector:刀闸_0" y="643.2520251304314" zvalue="1410"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453857050627" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453857050627"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,879.333,655.474) scale(-1.11111,-0.814815) translate(-1669.9,-1462.7)" width="15" x="871" y="643.2520251304314"/></g>
  <g id="243">
   <use class="kv10" height="30" transform="rotate(0,570.333,649.222) scale(-1.11111,-0.814815) translate(-1082.8,-1448.77)" width="15" x="562" xlink:href="#Disconnector:刀闸_0" y="637" zvalue="1417"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453857116163" ObjectName="10kVⅠ段母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453857116163"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,570.333,649.222) scale(-1.11111,-0.814815) translate(-1082.8,-1448.77)" width="15" x="562" y="637"/></g>
  <g id="288">
   <use class="kv10" height="30" transform="rotate(0,1706.49,648.222) scale(-1.11111,-0.814815) translate(-3241.49,-1446.55)" width="15" x="1698.152556430824" xlink:href="#Disconnector:刀闸_0" y="636" zvalue="1425"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453857181699" ObjectName="10kVⅡ段母线电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453857181699"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1706.49,648.222) scale(-1.11111,-0.814815) translate(-3241.49,-1446.55)" width="15" x="1698.152556430824" y="636"/></g>
  <g id="332">
   <use class="kv10" height="30" transform="rotate(0,684.333,718.222) scale(1.11111,0.814815) translate(-67.6,160.455)" width="15" x="676" xlink:href="#Disconnector:刀闸_0" y="706" zvalue="1433"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453857312771" ObjectName="10kV开发区线0611隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453857312771"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,684.333,718.222) scale(1.11111,0.814815) translate(-67.6,160.455)" width="15" x="676" y="706"/></g>
  <g id="369">
   <use class="kv10" height="30" transform="rotate(0,849.446,825.556) scale(1.11111,0.814815) translate(-84.1113,184.848)" width="15" x="841.1129003582046" xlink:href="#Disconnector:刀闸_0" y="813.3333384195962" zvalue="1459"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453857509379" ObjectName="10kV芒滚线0626隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453857509379"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,849.446,825.556) scale(1.11111,0.814815) translate(-84.1113,184.848)" width="15" x="841.1129003582046" y="813.3333384195962"/></g>
  <g id="363">
   <use class="kv10" height="30" transform="rotate(0,850.304,719.333) scale(1.11111,0.814815) translate(-84.197,160.707)" width="15" x="841.9704304147167" xlink:href="#Disconnector:刀闸_0" y="707.1111145019531" zvalue="1468"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453857378307" ObjectName="10kV芒滚线0621隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453857378307"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,850.304,719.333) scale(1.11111,0.814815) translate(-84.197,160.707)" width="15" x="841.9704304147167" y="707.1111145019531"/></g>
  <g id="388">
   <use class="kv10" height="30" transform="rotate(0,1238.93,827.667) scale(1.11111,0.814815) translate(-123.06,185.328)" width="15" x="1230.598706195438" xlink:href="#Disconnector:刀闸_0" y="815.4444512261284" zvalue="1479"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453857902595" ObjectName="10kV南坎线0636隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453857902595"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1238.93,827.667) scale(1.11111,0.814815) translate(-123.06,185.328)" width="15" x="1230.598706195438" y="815.4444512261284"/></g>
  <g id="382">
   <use class="kv10" height="30" transform="rotate(0,1239.79,721.444) scale(1.11111,0.814815) translate(-123.146,161.187)" width="15" x="1231.45623625195" xlink:href="#Disconnector:刀闸_0" y="709.2222256130642" zvalue="1488"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453857771523" ObjectName="10kV南坎线0632隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453857771523"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1239.79,721.444) scale(1.11111,0.814815) translate(-123.146,161.187)" width="15" x="1231.45623625195" y="709.2222256130642"/></g>
  <g id="405">
   <use class="kv10" height="30" transform="rotate(0,1394.93,826.667) scale(1.11111,0.814815) translate(-138.66,185.101)" width="15" x="1386.598706195438" xlink:href="#Disconnector:刀闸_0" y="814.4444512261284" zvalue="1497"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453858295811" ObjectName="10kV四分厂线0646隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453858295811"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1394.93,826.667) scale(1.11111,0.814815) translate(-138.66,185.101)" width="15" x="1386.598706195438" y="814.4444512261284"/></g>
  <g id="399">
   <use class="kv10" height="30" transform="rotate(0,1395.79,720.444) scale(1.11111,0.814815) translate(-138.746,160.96)" width="15" x="1387.45623625195" xlink:href="#Disconnector:刀闸_0" y="708.2222256130642" zvalue="1506"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453858164739" ObjectName="10kV四分厂线0642隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453858164739"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1395.79,720.444) scale(1.11111,0.814815) translate(-138.746,160.96)" width="15" x="1387.45623625195" y="708.2222256130642"/></g>
  <g id="422">
   <use class="kv10" height="30" transform="rotate(0,1558.93,825.667) scale(1.11111,0.814815) translate(-155.06,184.874)" width="15" x="1550.598706195438" xlink:href="#Disconnector:刀闸_0" y="813.4444512261284" zvalue="1515"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453858689027" ObjectName="10kV大沙河线0656隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453858689027"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1558.93,825.667) scale(1.11111,0.814815) translate(-155.06,184.874)" width="15" x="1550.598706195438" y="813.4444512261284"/></g>
  <g id="416">
   <use class="kv10" height="30" transform="rotate(0,1559.79,719.444) scale(1.11111,0.814815) translate(-155.146,160.732)" width="15" x="1551.45623625195" xlink:href="#Disconnector:刀闸_0" y="707.2222256130642" zvalue="1524"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453858557955" ObjectName="10kV大沙河线0652隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453858557955"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1559.79,719.444) scale(1.11111,0.814815) translate(-155.146,160.732)" width="15" x="1551.45623625195" y="707.2222256130642"/></g>
  <g id="425">
   <use class="kv10" height="30" transform="rotate(0,1043.33,719.333) scale(1.11111,0.814815) translate(-103.5,160.707)" width="15" x="1035" xlink:href="#Disconnector:刀闸_0" y="707.1111145019531" zvalue="1530"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453858951171" ObjectName="10kV分段0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453858951171"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1043.33,719.333) scale(1.11111,0.814815) translate(-103.5,160.707)" width="15" x="1035" y="707.1111145019531"/></g>
 </g>
 <g id="ButtonClass">
  <g href="35kV弄岛变_110kV母线.svg"><rect fill-opacity="0" height="40" width="12" x="1101" y="266" zvalue="1053"/></g>
  <g href="小电流装置20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="270" y="388.25" zvalue="1698"/></g>
  <g href="全站公用20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="66.19" y="307.25" zvalue="1699"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="168.09" y="388.25" zvalue="1700"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="66.19" y="388.25" zvalue="1701"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="66.19" y="347.75" zvalue="1702"/></g>
 </g>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="64.81" xlink:href="logo.png" y="23.93"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,189.653,65.2136) scale(1,1) translate(-1.439e-14,0)" writing-mode="lr" x="189.65" xml:space="preserve" y="68.70999999999999" zvalue="1586"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,218.5,65.1903) scale(1,1) translate(0,0)" writing-mode="lr" x="218.5" xml:space="preserve" y="74.19" zvalue="1587">35kV弄岛变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="183" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,306.438,400.25) scale(1,1) translate(0,-2.58626e-13)" width="72.88" x="270" y="388.25" zvalue="1698"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,306.438,400.25) scale(1,1) translate(0,-2.58626e-13)" writing-mode="lr" x="306.44" xml:space="preserve" y="404.75" zvalue="1698">小电流接地</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="179" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,102.625,319.25) scale(1,1) translate(0,0)" width="72.88" x="66.19" y="307.25" zvalue="1699"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,102.625,319.25) scale(1,1) translate(0,0)" writing-mode="lr" x="102.63" xml:space="preserve" y="323.75" zvalue="1699">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="182" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,204.531,400.25) scale(1,1) translate(0,0)" width="72.88" x="168.09" y="388.25" zvalue="1700"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,204.531,400.25) scale(1,1) translate(0,0)" writing-mode="lr" x="204.53" xml:space="preserve" y="404.75" zvalue="1700">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="181" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,102.625,400.25) scale(1,1) translate(0,0)" width="72.88" x="66.19" y="388.25" zvalue="1701"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,102.625,400.25) scale(1,1) translate(0,0)" writing-mode="lr" x="102.63" xml:space="preserve" y="404.75" zvalue="1701">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="180" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,102.625,359.75) scale(1,1) translate(0,0)" width="72.88" x="66.19" y="347.75" zvalue="1702"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,102.625,359.75) scale(1,1) translate(0,0)" writing-mode="lr" x="102.63" xml:space="preserve" y="364.25" zvalue="1702">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,799.222,300.889) scale(1,1) translate(0,0)" writing-mode="lr" x="799.22" xml:space="preserve" y="305.39" zvalue="7">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1789.67,675.361) scale(1,1) translate(0,0)" writing-mode="lr" x="1789.67" xml:space="preserve" y="679.86" zvalue="14">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,483.133,672.556) scale(1,1) translate(0,0)" writing-mode="lr" x="483.13" xml:space="preserve" y="677.0599999999999" zvalue="16">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,941.333,236.111) scale(1,1) translate(0,0)" writing-mode="lr" x="941.33" xml:space="preserve" y="240.61" zvalue="17">362</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,932.556,283.667) scale(1,1) translate(0,0)" writing-mode="lr" x="932.5599999999999" xml:space="preserve" y="288.17" zvalue="18">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,934.222,175.667) scale(1,1) translate(0,0)" writing-mode="lr" x="934.22" xml:space="preserve" y="180.17" zvalue="20">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,918.932,77.4444) scale(1,1) translate(0,0)" writing-mode="lr" x="918.9299999999999" xml:space="preserve" y="81.94" zvalue="25">35kV弄班线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,973.056,167.889) scale(1,1) translate(0,0)" writing-mode="lr" x="973.0599999999999" xml:space="preserve" y="172.39" zvalue="32">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1132.56,259.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1132.56" xml:space="preserve" y="264.17" zvalue="94">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1157.33,230.944) scale(1,1) translate(0,0)" writing-mode="lr" x="1157.33" xml:space="preserve" y="235.44" zvalue="96">39017</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" x="1112.375" xml:space="preserve" y="124.4809003406101" zvalue="98">35kV母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1112.375" xml:space="preserve" y="140.4809003406101" zvalue="98">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,890.778,363.222) scale(1,1) translate(0,0)" writing-mode="lr" x="890.78" xml:space="preserve" y="367.72" zvalue="102">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,900.111,403.222) scale(1,1) translate(0,0)" writing-mode="lr" x="900.11" xml:space="preserve" y="407.72" zvalue="103">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,669.67,869.333) scale(1,1) translate(0,0)" writing-mode="lr" x="669.67" xml:space="preserve" y="873.83" zvalue="638">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="301" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,694.628,824.444) scale(1,1) translate(0,0)" writing-mode="lr" x="694.63" xml:space="preserve" y="828.9400000000001" zvalue="643">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="300" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,680.515,937.222) scale(1,1) translate(0,0)" writing-mode="lr" x="680.5147847926416" xml:space="preserve" y="941.7222290039062" zvalue="645">10kV开发区线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="681" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,941.886,506) scale(1,1) translate(0,0)" writing-mode="lr" x="941.89" xml:space="preserve" y="510.5" zvalue="1093">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1349.44,362.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1349.44" xml:space="preserve" y="367.06" zvalue="1108">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1358.78,402.556) scale(1,1) translate(0,3.49992e-13)" writing-mode="lr" x="1358.78" xml:space="preserve" y="407.06" zvalue="1110">302</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1397.17,505.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1397.17" xml:space="preserve" y="509.83" zvalue="1120">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" x="564.609375" xml:space="preserve" y="542.5" zvalue="1131">10kVⅠ段母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="564.609375" xml:space="preserve" y="558.5" zvalue="1131">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,948.5,530.5) scale(1,1) translate(0,0)" writing-mode="lr" x="948.5" xml:space="preserve" y="535" zvalue="1337">5000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="230" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1401.5,531) scale(1,1) translate(0,0)" writing-mode="lr" x="1401.5" xml:space="preserve" y="535.5" zvalue="1339">5000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="294" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1358.33,236.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1358.33" xml:space="preserve" y="240.67" zvalue="1370">361</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="293" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1348.7,284.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1348.7" xml:space="preserve" y="289.22" zvalue="1371">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="292" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1350.22,176.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1350.22" xml:space="preserve" y="181.22" zvalue="1374">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="291" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1334.93,78.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1334.93" xml:space="preserve" y="83" zvalue="1378">35kV汉相弄线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="290" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1389.06,168.944) scale(1,1) translate(0,0)" writing-mode="lr" x="1389.06" xml:space="preserve" y="173.44" zvalue="1381">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="330" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1388.68,278) scale(1,1) translate(0,0)" writing-mode="lr" x="1388.68" xml:space="preserve" y="282.5" zvalue="1389">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1294.5,255) scale(1,1) translate(0,0)" writing-mode="lr" x="1294.5" xml:space="preserve" y="259.5" zvalue="1392">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1294.5,275.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1294.5" xml:space="preserve" y="280" zvalue="1396">50kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1363.87,606) scale(1,1) translate(0,0)" writing-mode="lr" x="1363.87" xml:space="preserve" y="610.5" zvalue="1399">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1363.34,654.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1363.34" xml:space="preserve" y="658.72" zvalue="1401">0022</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,904.692,606.252) scale(1,1) translate(0,0)" writing-mode="lr" x="904.6900000000001" xml:space="preserve" y="610.75" zvalue="1409">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="231" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,904.167,654.474) scale(1,1) translate(0,0)" writing-mode="lr" x="904.17" xml:space="preserve" y="658.97" zvalue="1411">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="244" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,595.167,648.222) scale(1,1) translate(0,0)" writing-mode="lr" x="595.17" xml:space="preserve" y="652.72" zvalue="1418">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" x="1700.75" xml:space="preserve" y="541.5" zvalue="1424">10kVⅡ段母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1700.75" xml:space="preserve" y="557.5" zvalue="1424">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="248" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1731.32,647.222) scale(1,1) translate(0,-4.25808e-13)" writing-mode="lr" x="1731.32" xml:space="preserve" y="651.72" zvalue="1426">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="333" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,706.5,766) scale(1,1) translate(0,0)" writing-mode="lr" x="706.5" xml:space="preserve" y="770.5" zvalue="1432">061</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="334" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,696,717.222) scale(1,1) translate(0,0)" writing-mode="lr" x="696" xml:space="preserve" y="721.72" zvalue="1434">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="359" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,835.641,870.444) scale(1,1) translate(0,0)" writing-mode="lr" x="835.64" xml:space="preserve" y="874.9400000000001" zvalue="1457">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="358" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,861.113,824.556) scale(1,1) translate(0,0)" writing-mode="lr" x="861.11" xml:space="preserve" y="829.0599999999999" zvalue="1461">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="357" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,849.257,936.222) scale(1,1) translate(0,0)" writing-mode="lr" x="849.2570970813833" xml:space="preserve" y="940.7222290436425" zvalue="1462">10kV芒滚线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="356" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,872.47,767.111) scale(1,1) translate(0,0)" writing-mode="lr" x="872.47" xml:space="preserve" y="771.61" zvalue="1467">062</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="355" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,861.97,718.333) scale(1,1) translate(0,0)" writing-mode="lr" x="861.97" xml:space="preserve" y="722.83" zvalue="1469">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="378" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1225.13,872.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1225.13" xml:space="preserve" y="877.0599999999999" zvalue="1477">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="377" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1250.6,826.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1250.6" xml:space="preserve" y="831.17" zvalue="1481">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="376" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1238.5,940.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1238.5" xml:space="preserve" y="944.9444512261284" zvalue="1482">10kV南坎线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="375" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1261.96,769.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1261.96" xml:space="preserve" y="773.72" zvalue="1487">063</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="374" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1251.46,720.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1251.46" xml:space="preserve" y="724.9400000000001" zvalue="1489">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="395" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1381.13,871.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1381.13" xml:space="preserve" y="876.0599999999999" zvalue="1495">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="394" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1406.6,825.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1406.6" xml:space="preserve" y="830.17" zvalue="1499">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="393" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1393.5,938.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1393.5" xml:space="preserve" y="942.9444512261284" zvalue="1500">10kV四分厂线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="392" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1417.96,768.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1417.96" xml:space="preserve" y="772.72" zvalue="1505">064</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="391" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1407.46,719.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1407.46" xml:space="preserve" y="723.9400000000001" zvalue="1507">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="412" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1545.13,870.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1545.13" xml:space="preserve" y="875.0599999999999" zvalue="1513">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="411" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1570.6,824.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1570.6" xml:space="preserve" y="829.17" zvalue="1517">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="410" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1561,937.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1561" xml:space="preserve" y="941.9444512261284" zvalue="1518">10kV大沙河线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="409" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1581.96,767.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1581.96" xml:space="preserve" y="771.72" zvalue="1523">065</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="408" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1571.46,718.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1571.46" xml:space="preserve" y="722.9400000000001" zvalue="1525">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="426" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1069.5,718.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1069.5" xml:space="preserve" y="722.83" zvalue="1531">0121</text>
  <line fill="none" id="166" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50000000000045" x2="381.5" y1="150.3704926140824" y2="150.3704926140824" zvalue="1589"/>
  <line fill="none" id="165" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382.5" x2="382.5" y1="8.5" y2="1038.5" zvalue="1590"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.5" x2="196.5" y1="162.5" y2="162.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.5" x2="196.5" y1="188.5" y2="188.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.5" x2="15.5" y1="162.5" y2="188.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.5" x2="196.5" y1="162.5" y2="188.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.5" x2="377.5" y1="162.5" y2="162.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.5" x2="377.5" y1="188.5" y2="188.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.5" x2="196.5" y1="162.5" y2="188.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="377.5" x2="377.5" y1="162.5" y2="188.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.5" x2="196.5" y1="188.5" y2="188.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.5" x2="196.5" y1="212.75" y2="212.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.5" x2="15.5" y1="188.5" y2="212.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.5" x2="196.5" y1="188.5" y2="212.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.5" x2="377.5" y1="188.5" y2="188.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.5" x2="377.5" y1="212.75" y2="212.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.5" x2="196.5" y1="188.5" y2="212.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="377.5" x2="377.5" y1="188.5" y2="212.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.5" x2="196.5" y1="212.75" y2="212.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.5" x2="196.5" y1="235.5" y2="235.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.5" x2="15.5" y1="212.75" y2="235.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.5" x2="196.5" y1="212.75" y2="235.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.5" x2="377.5" y1="212.75" y2="212.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.5" x2="377.5" y1="235.5" y2="235.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.5" x2="196.5" y1="212.75" y2="235.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="377.5" x2="377.5" y1="212.75" y2="235.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.5" x2="196.5" y1="235.5" y2="235.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.5" x2="196.5" y1="258.25" y2="258.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.5" x2="15.5" y1="235.5" y2="258.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.5" x2="196.5" y1="235.5" y2="258.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.5" x2="377.5" y1="235.5" y2="235.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.5" x2="377.5" y1="258.25" y2="258.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.5" x2="196.5" y1="235.5" y2="258.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="377.5" x2="377.5" y1="235.5" y2="258.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.5" x2="196.5" y1="258.25" y2="258.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.5" x2="196.5" y1="281" y2="281"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.5" x2="15.5" y1="258.25" y2="281"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.5" x2="196.5" y1="258.25" y2="281"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.5" x2="377.5" y1="258.25" y2="258.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.5" x2="377.5" y1="281" y2="281"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.5" x2="196.5" y1="258.25" y2="281"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="377.5" x2="377.5" y1="258.25" y2="281"/>
  <line fill="none" id="163" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50000000000045" x2="381.5" y1="620.3704926140824" y2="620.3704926140824" zvalue="1592"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.5" x2="112.2745" y1="442.5" y2="442.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.5" x2="112.2745" y1="480.7823" y2="480.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.5" x2="66.5" y1="442.5" y2="480.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="112.2745" y1="442.5" y2="480.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="171.0809" y1="442.5" y2="442.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="171.0809" y1="480.7823" y2="480.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="112.2745" y1="442.5" y2="480.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="171.0809" y1="442.5" y2="480.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="229.8873" y1="442.5" y2="442.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="229.8873" y1="480.7823" y2="480.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="171.0809" y1="442.5" y2="480.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8873" x2="229.8873" y1="442.5" y2="480.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8872" x2="288.6936000000001" y1="442.5" y2="442.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8872" x2="288.6936000000001" y1="480.7823" y2="480.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8872" x2="229.8872" y1="442.5" y2="480.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="288.6936000000001" y1="442.5" y2="480.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="347.5" y1="442.5" y2="442.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="347.5" y1="480.7823" y2="480.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="288.6936000000001" y1="442.5" y2="480.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="347.5" x2="347.5" y1="442.5" y2="480.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.5" x2="112.2745" y1="480.7823" y2="480.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.5" x2="112.2745" y1="505.4617" y2="505.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.5" x2="66.5" y1="480.7823" y2="505.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="112.2745" y1="480.7823" y2="505.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="171.0809" y1="480.7823" y2="480.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="171.0809" y1="505.4617" y2="505.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="112.2745" y1="480.7823" y2="505.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="171.0809" y1="480.7823" y2="505.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="229.8873" y1="480.7823" y2="480.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="229.8873" y1="505.4617" y2="505.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="171.0809" y1="480.7823" y2="505.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8873" x2="229.8873" y1="480.7823" y2="505.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8872" x2="288.6936000000001" y1="480.7823" y2="480.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8872" x2="288.6936000000001" y1="505.4617" y2="505.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8872" x2="229.8872" y1="480.7823" y2="505.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="288.6936000000001" y1="480.7823" y2="505.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="347.5" y1="480.7823" y2="480.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="347.5" y1="505.4617" y2="505.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="288.6936000000001" y1="480.7823" y2="505.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="347.5" x2="347.5" y1="480.7823" y2="505.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.5" x2="112.2745" y1="505.4617" y2="505.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.5" x2="112.2745" y1="530.1411000000001" y2="530.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.5" x2="66.5" y1="505.4617" y2="530.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="112.2745" y1="505.4617" y2="530.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="171.0809" y1="505.4617" y2="505.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="171.0809" y1="530.1411000000001" y2="530.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="112.2745" y1="505.4617" y2="530.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="171.0809" y1="505.4617" y2="530.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="229.8873" y1="505.4617" y2="505.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="229.8873" y1="530.1411000000001" y2="530.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="171.0809" y1="505.4617" y2="530.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8873" x2="229.8873" y1="505.4617" y2="530.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8872" x2="288.6936000000001" y1="505.4617" y2="505.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8872" x2="288.6936000000001" y1="530.1411000000001" y2="530.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8872" x2="229.8872" y1="505.4617" y2="530.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="288.6936000000001" y1="505.4617" y2="530.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="347.5" y1="505.4617" y2="505.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="347.5" y1="530.1411000000001" y2="530.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="288.6936000000001" y1="505.4617" y2="530.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="347.5" x2="347.5" y1="505.4617" y2="530.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.5" x2="112.2745" y1="530.1410999999999" y2="530.1410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.5" x2="112.2745" y1="554.8205" y2="554.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.5" x2="66.5" y1="530.1410999999999" y2="554.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="112.2745" y1="530.1410999999999" y2="554.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="171.0809" y1="530.1410999999999" y2="530.1410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="171.0809" y1="554.8205" y2="554.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="112.2745" y1="530.1410999999999" y2="554.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="171.0809" y1="530.1410999999999" y2="554.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="229.8873" y1="530.1410999999999" y2="530.1410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="229.8873" y1="554.8205" y2="554.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="171.0809" y1="530.1410999999999" y2="554.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8873" x2="229.8873" y1="530.1410999999999" y2="554.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8872" x2="288.6936000000001" y1="530.1410999999999" y2="530.1410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8872" x2="288.6936000000001" y1="554.8205" y2="554.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8872" x2="229.8872" y1="530.1410999999999" y2="554.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="288.6936000000001" y1="530.1410999999999" y2="554.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="347.5" y1="530.1410999999999" y2="530.1410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="347.5" y1="554.8205" y2="554.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="288.6936000000001" y1="530.1410999999999" y2="554.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="347.5" x2="347.5" y1="530.1410999999999" y2="554.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.5" x2="112.2745" y1="554.8206" y2="554.8206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.5" x2="112.2745" y1="579.5" y2="579.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.5" x2="66.5" y1="554.8206" y2="579.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="112.2745" y1="554.8206" y2="579.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="171.0809" y1="554.8206" y2="554.8206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="171.0809" y1="579.5" y2="579.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="112.2745" y1="554.8206" y2="579.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="171.0809" y1="554.8206" y2="579.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="229.8873" y1="554.8206" y2="554.8206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="229.8873" y1="579.5" y2="579.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="171.0809" y1="554.8206" y2="579.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8873" x2="229.8873" y1="554.8206" y2="579.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8872" x2="288.6936000000001" y1="554.8206" y2="554.8206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8872" x2="288.6936000000001" y1="579.5" y2="579.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8872" x2="229.8872" y1="554.8206" y2="579.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="288.6936000000001" y1="554.8206" y2="579.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="347.5" y1="554.8206" y2="554.8206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="347.5" y1="579.5" y2="579.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="288.6936000000001" y1="554.8206" y2="579.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="347.5" x2="347.5" y1="554.8206" y2="579.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.5" x2="112.2745" y1="579.5" y2="579.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.5" x2="112.2745" y1="604.1794" y2="604.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="66.5" x2="66.5" y1="579.5" y2="604.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="112.2745" y1="579.5" y2="604.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="171.0809" y1="579.5" y2="579.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="171.0809" y1="604.1794" y2="604.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="112.2745" x2="112.2745" y1="579.5" y2="604.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="171.0809" y1="579.5" y2="604.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="229.8873" y1="579.5" y2="579.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="229.8873" y1="604.1794" y2="604.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.0809" x2="171.0809" y1="579.5" y2="604.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8873" x2="229.8873" y1="579.5" y2="604.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8872" x2="288.6936000000001" y1="579.5" y2="579.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8872" x2="288.6936000000001" y1="604.1794" y2="604.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="229.8872" x2="229.8872" y1="579.5" y2="604.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="288.6936000000001" y1="579.5" y2="604.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="347.5" y1="579.5" y2="579.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="347.5" y1="604.1794" y2="604.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="288.6936000000001" x2="288.6936000000001" y1="579.5" y2="604.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="347.5" x2="347.5" y1="579.5" y2="604.1794"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.5" x2="104.5" y1="935.5" y2="935.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.5" x2="104.5" y1="974.6632999999999" y2="974.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.5" x2="14.5" y1="935.5" y2="974.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="104.5" y1="935.5" y2="974.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="374.5" y1="935.5" y2="935.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="374.5" y1="974.6632999999999" y2="974.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="104.5" y1="935.5" y2="974.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="374.5" x2="374.5" y1="935.5" y2="974.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.5" x2="104.5" y1="974.66327" y2="974.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.5" x2="104.5" y1="1002.58167" y2="1002.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.5" x2="14.5" y1="974.66327" y2="1002.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="104.5" y1="974.66327" y2="1002.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="194.5" y1="974.66327" y2="974.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="194.5" y1="1002.58167" y2="1002.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="104.5" y1="974.66327" y2="1002.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.5" x2="194.5" y1="974.66327" y2="1002.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.5000000000001" x2="284.5000000000001" y1="974.66327" y2="974.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.5000000000001" x2="284.5000000000001" y1="1002.58167" y2="1002.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.5000000000001" x2="194.5000000000001" y1="974.66327" y2="1002.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.5000000000001" x2="284.5000000000001" y1="974.66327" y2="1002.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.5" x2="374.5" y1="974.66327" y2="974.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.5" x2="374.5" y1="1002.58167" y2="1002.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.5" x2="284.5" y1="974.66327" y2="1002.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="374.5" x2="374.5" y1="974.66327" y2="1002.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.5" x2="104.5" y1="1002.5816" y2="1002.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.5" x2="104.5" y1="1030.5" y2="1030.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.5" x2="14.5" y1="1002.5816" y2="1030.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="104.5" y1="1002.5816" y2="1030.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="194.5" y1="1002.5816" y2="1002.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="194.5" y1="1030.5" y2="1030.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.5" x2="104.5" y1="1002.5816" y2="1030.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.5" x2="194.5" y1="1002.5816" y2="1030.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.5000000000001" x2="284.5000000000001" y1="1002.5816" y2="1002.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.5000000000001" x2="284.5000000000001" y1="1030.5" y2="1030.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.5000000000001" x2="194.5000000000001" y1="1002.5816" y2="1030.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.5000000000001" x2="284.5000000000001" y1="1002.5816" y2="1030.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.5" x2="374.5" y1="1002.5816" y2="1002.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.5" x2="374.5" y1="1030.5" y2="1030.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.5" x2="284.5" y1="1002.5816" y2="1030.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="374.5" x2="374.5" y1="1002.5816" y2="1030.5"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.5,955.5) scale(1,1) translate(0,0)" writing-mode="lr" x="59.5" xml:space="preserve" y="961.5" zvalue="1596">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.5,989.5) scale(1,1) translate(0,0)" writing-mode="lr" x="56.5" xml:space="preserve" y="995.5" zvalue="1597">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,238.5,989.5) scale(1,1) translate(0,0)" writing-mode="lr" x="238.5" xml:space="preserve" y="995.5" zvalue="1598">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.5,1017.5) scale(1,1) translate(0,0)" writing-mode="lr" x="55.5" xml:space="preserve" y="1023.5" zvalue="1599">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.5,1017.5) scale(1,1) translate(0,0)" writing-mode="lr" x="237.5" xml:space="preserve" y="1023.5" zvalue="1600">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" x="140.515625" xml:space="preserve" y="458.8993055555555" zvalue="1601">35kV Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="140.515625" xml:space="preserve" y="475.8993055555555" zvalue="1601">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80,650) scale(1,1) translate(0,0)" writing-mode="lr" x="80" xml:space="preserve" y="654.5" zvalue="1603">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,212.899,317.341) scale(1,1) translate(0,0)" writing-mode="lr" x="212.9" xml:space="preserve" y="321.84" zvalue="1604">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,317.899,317.341) scale(1,1) translate(0,0)" writing-mode="lr" x="317.9" xml:space="preserve" y="321.84" zvalue="1605">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" x="260.28125" xml:space="preserve" y="459.3368055555555" zvalue="1606">10kV Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="260.28125" xml:space="preserve" y="476.3368055555555" zvalue="1606">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" x="318.25" xml:space="preserve" y="458.3368055555555" zvalue="1607">10kV Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="318.25" xml:space="preserve" y="475.3368055555555" zvalue="1607">母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,91.5,493.25) scale(1,1) translate(0,0)" writing-mode="lr" x="91.5" xml:space="preserve" y="497.75" zvalue="1608">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,91.5,518.75) scale(1,1) translate(0,0)" writing-mode="lr" x="91.5" xml:space="preserve" y="523.25" zvalue="1609">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,91.5,541.75) scale(1,1) translate(0,0)" writing-mode="lr" x="91.5" xml:space="preserve" y="546.25" zvalue="1610">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,91.5,564.75) scale(1,1) translate(0,0)" writing-mode="lr" x="91.5" xml:space="preserve" y="569.25" zvalue="1611">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,91.5,591.75) scale(1,1) translate(0,0)" writing-mode="lr" x="91.5" xml:space="preserve" y="596.25" zvalue="1612">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,239.554,957.5) scale(1,1) translate(0,0)" writing-mode="lr" x="239.55" xml:space="preserve" y="963.5" zvalue="1613">NongDao-01-2020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,149.554,989.5) scale(1,1) translate(0,0)" writing-mode="lr" x="149.55" xml:space="preserve" y="995.5" zvalue="1614">李文杰</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,329.554,989.5) scale(1,1) translate(0,0)" writing-mode="lr" x="329.55" xml:space="preserve" y="995.5" zvalue="1615">20200925</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.5,176.5) scale(1,1) translate(0,0)" writing-mode="lr" x="53.5" xml:space="preserve" y="182" zvalue="1616">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.5,176.5) scale(1,1) translate(0,0)" writing-mode="lr" x="233.5" xml:space="preserve" y="182" zvalue="1617">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.1875,200.75) scale(1,1) translate(0,0)" writing-mode="lr" x="57.19" xml:space="preserve" y="205.25" zvalue="1618">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.6875,248.5) scale(1,1) translate(0,0)" writing-mode="lr" x="60.69" xml:space="preserve" y="254" zvalue="1619">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="114" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,242.25,248) scale(1,1) translate(0,0)" writing-mode="lr" x="242.25" xml:space="preserve" y="253.5" zvalue="1620">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.6875,271.5) scale(1,1) translate(0,0)" writing-mode="lr" x="60.69" xml:space="preserve" y="277" zvalue="1621">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,242.25,271) scale(1,1) translate(0,0)" writing-mode="lr" x="242.25" xml:space="preserve" y="276.5" zvalue="1622">2号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.1875,224.75) scale(1,1) translate(0,0)" writing-mode="lr" x="58.19" xml:space="preserve" y="229.25" zvalue="1623">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241.25,224.5) scale(1,1) translate(0,0)" writing-mode="lr" x="241.25" xml:space="preserve" y="229" zvalue="1624">10kVⅡ母频率</text>
  <ellipse cx="860.66" cy="402.66" fill="rgb(255,0,0)" fill-opacity="1" id="55" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1706"/>
  <ellipse cx="1319.66" cy="402.66" fill="rgb(255,0,0)" fill-opacity="1" id="56" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1708"/>
 </g>
 <g id="BusbarSectionClass">
  <g id="6">
   <path class="kv35" d="M 775 316.67 L 1468 316.67" stroke-width="4" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674401124355" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674401124355"/></metadata>
  <path d="M 775 316.67 L 1468 316.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv10" d="M 1166 690 L 1820.44 690" stroke-width="4" zvalue="13"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674401189891" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674401189891"/></metadata>
  <path d="M 1166 690 L 1820.44 690" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv10" d="M 437.13 688.89 L 1054 688.89" stroke-width="4" zvalue="15"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674401255427" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674401255427"/></metadata>
  <path d="M 437.13 688.89 L 1054 688.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="19">
   <use class="kv35" height="20" transform="rotate(0,920,237.111) scale(1.22222,1.11111) translate(-166.162,-22.6)" width="10" x="913.8888888888889" xlink:href="#Breaker:开关_0" y="226" zvalue="16"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925102141444" ObjectName="35kV弄班线362断路器"/>
   <cge:TPSR_Ref TObjectID="6473925102141444"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,920,237.111) scale(1.22222,1.11111) translate(-166.162,-22.6)" width="10" x="913.8888888888889" y="226"/></g>
  <g id="101">
   <use class="kv35" height="20" transform="rotate(0,879.333,404.222) scale(1.22222,1.11111) translate(-158.768,-39.3111)" width="10" x="873.2222324079938" xlink:href="#Breaker:开关_0" y="393.1111111111111" zvalue="102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925102206980" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925102206980"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,879.333,404.222) scale(1.22222,1.11111) translate(-158.768,-39.3111)" width="10" x="873.2222324079938" y="393.1111111111111"/></g>
  <g id="50">
   <use class="kv35" height="20" transform="rotate(0,1338,403.556) scale(1.22222,1.11111) translate(-242.162,-39.2444)" width="10" x="1331.888888902134" xlink:href="#Breaker:开关_0" y="392.4444393581814" zvalue="1109"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925102272516" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473925102272516"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1338,403.556) scale(1.22222,1.11111) translate(-242.162,-39.2444)" width="10" x="1331.888888902134" y="392.4444393581814"/></g>
  <g id="322">
   <use class="kv35" height="20" transform="rotate(0,1337,237.167) scale(1.22222,1.11111) translate(-241.98,-22.6056)" width="10" x="1330.888888888889" xlink:href="#Breaker:开关_0" y="226.0555553436279" zvalue="1368"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925102338052" ObjectName="35kV汉相弄线361断路器"/>
   <cge:TPSR_Ref TObjectID="6473925102338052"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1337,237.167) scale(1.22222,1.11111) translate(-241.98,-22.6056)" width="10" x="1330.888888888889" y="226.0555553436279"/></g>
  <g id="10">
   <use class="kv10" height="20" transform="rotate(0,1338.37,607) scale(1.1,1) translate(-121.17,0)" width="10" x="1332.869989827474" xlink:href="#Breaker:开关_0" y="597" zvalue="1398"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925102403588" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473925102403588"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1338.37,607) scale(1.1,1) translate(-121.17,0)" width="10" x="1332.869989827474" y="597"/></g>
  <g id="241">
   <use class="kv10" height="20" transform="rotate(0,879.192,607.252) scale(1.1,1) translate(-79.4265,0)" width="10" x="873.6920395287714" xlink:href="#Breaker:开关_0" y="597.2520251304313" zvalue="1408"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925102469124" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925102469124"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,879.192,607.252) scale(1.1,1) translate(-79.4265,0)" width="10" x="873.6920395287714" y="597.2520251304313"/></g>
  <g id="308">
   <use class="kv10" height="20" transform="rotate(0,683.5,767) scale(1.1,1) translate(-61.6364,0)" width="10" x="678" xlink:href="#Breaker:开关_0" y="757" zvalue="1431"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925102534660" ObjectName="10kV开发区线061断路器"/>
   <cge:TPSR_Ref TObjectID="6473925102534660"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,683.5,767) scale(1.1,1) translate(-61.6364,0)" width="10" x="678" y="757"/></g>
  <g id="364">
   <use class="kv10" height="20" transform="rotate(0,849.47,768.111) scale(1.1,1) translate(-76.7246,0)" width="10" x="843.9704304147167" xlink:href="#Breaker:开关_0" y="758.1111128065321" zvalue="1466"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925102600196" ObjectName="10kV芒滚线062断路器"/>
   <cge:TPSR_Ref TObjectID="6473925102600196"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,849.47,768.111) scale(1.1,1) translate(-76.7246,0)" width="10" x="843.9704304147167" y="758.1111128065321"/></g>
  <g id="383">
   <use class="kv10" height="20" transform="rotate(0,1238.96,770.222) scale(1.1,1) translate(-112.132,0)" width="10" x="1233.45623625195" xlink:href="#Breaker:开关_0" y="760.2222256130642" zvalue="1486"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925102665732" ObjectName="10kV南坎线063断路器"/>
   <cge:TPSR_Ref TObjectID="6473925102665732"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1238.96,770.222) scale(1.1,1) translate(-112.132,0)" width="10" x="1233.45623625195" y="760.2222256130642"/></g>
  <g id="400">
   <use class="kv10" height="20" transform="rotate(0,1394.96,769.222) scale(1.1,1) translate(-126.314,0)" width="10" x="1389.45623625195" xlink:href="#Breaker:开关_0" y="759.2222256130642" zvalue="1504"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925102731268" ObjectName="10kV四分厂线064断路器"/>
   <cge:TPSR_Ref TObjectID="6473925102731268"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1394.96,769.222) scale(1.1,1) translate(-126.314,0)" width="10" x="1389.45623625195" y="759.2222256130642"/></g>
  <g id="417">
   <use class="kv10" height="20" transform="rotate(0,1558.96,768.222) scale(1.1,1) translate(-141.223,0)" width="10" x="1553.45623625195" xlink:href="#Breaker:开关_0" y="758.2222256130642" zvalue="1522"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925102796804" ObjectName="10kV大沙河线065断路器"/>
   <cge:TPSR_Ref TObjectID="6473925102796804"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1558.96,768.222) scale(1.1,1) translate(-141.223,0)" width="10" x="1553.45623625195" y="758.2222256130642"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="23">
   <path class="kv35" d="M 919.9 296.48 L 919.9 316.67" stroke-width="1" zvalue="20"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@0" LinkObjectIDznd="6@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.9 296.48 L 919.9 316.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv35" d="M 919.93 272.65 L 919.93 247.72" stroke-width="1" zvalue="21"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@1" LinkObjectIDznd="19@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.93 272.65 L 919.93 247.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv35" d="M 919.96 226.48 L 919.9 188.48" stroke-width="1" zvalue="22"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="19@0" LinkObjectIDznd="22@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.96 226.48 L 919.9 188.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv35" d="M 919.93 164.65 L 919.93 122.32" stroke-width="1" zvalue="25"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@1" LinkObjectIDznd="27@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.93 164.65 L 919.93 122.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv35" d="M 1107.12 272.48 L 1107.12 316.67" stroke-width="1" zvalue="98"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="6@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1107.12 272.48 L 1107.12 316.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv35" d="M 1107.15 248.65 L 1107.15 198.89" stroke-width="1" zvalue="99"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@1" LinkObjectIDznd="94@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1107.15 248.65 L 1107.15 198.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv35" d="M 879.4 376.24 L 879.4 393.59" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@1" LinkObjectIDznd="101@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.4 376.24 L 879.4 393.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="316">
   <path class="kv10" d="M 683.03 837.46 L 683.03 882.72" stroke-width="1" zvalue="647"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="320@1" LinkObjectIDznd="319@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 683.03 837.46 L 683.03 882.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="312">
   <path class="kv10" d="M 654.67 859.5 L 654.67 848.56 L 683.03 848.56" stroke-width="1" zvalue="651"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="323@0" LinkObjectIDznd="316" MaxPinNum="2"/>
   </metadata>
  <path d="M 654.67 859.5 L 654.67 848.56 L 683.03 848.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv10" d="M 708.09 858.86 L 708.09 847.89 L 683.03 847.89" stroke-width="1" zvalue="652"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="321@0" LinkObjectIDznd="316" MaxPinNum="2"/>
   </metadata>
  <path d="M 708.09 858.86 L 708.09 847.89 L 683.03 847.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="597">
   <path class="kv35" d="M 891.37 131 L 919.93 131" stroke-width="1" zvalue="1060"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="592@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 891.37 131 L 919.93 131" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="682">
   <path class="kv35" d="M 879.43 352.4 L 879.43 316.67" stroke-width="1" zvalue="1093"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="6@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.43 352.4 L 879.43 316.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="683">
   <path class="kv35" d="M 879.41 414.83 L 879.41 469.98" stroke-width="1" zvalue="1094"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@1" LinkObjectIDznd="680@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.41 414.83 L 879.41 469.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv35" d="M 1338.07 375.57 L 1338.07 392.93" stroke-width="1" zvalue="1111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@1" LinkObjectIDznd="50@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1338.07 375.57 L 1338.07 392.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv35" d="M 1338.1 351.74 L 1338.1 316.67" stroke-width="1" zvalue="1119"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="6@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1338.1 351.74 L 1338.1 316.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv35" d="M 1338.08 414.17 L 1338.08 467.08" stroke-width="1" zvalue="1121"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@1" LinkObjectIDznd="44@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1338.08 414.17 L 1338.08 467.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="235">
   <path class="kv35" d="M 960.78 153.39 L 919.93 153.39" stroke-width="1" zvalue="1347"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.78 153.39 L 919.93 153.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="307">
   <path class="kv35" d="M 1336.08 273.71 L 1336.08 247.78" stroke-width="1" zvalue="1375"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="313@1" LinkObjectIDznd="322@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1336.08 273.71 L 1336.08 247.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="306">
   <path class="kv35" d="M 1336.96 226.54 L 1336.96 189.54" stroke-width="1" zvalue="1376"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="322@0" LinkObjectIDznd="309@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1336.96 226.54 L 1336.96 189.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="299">
   <path class="kv35" d="M 1335.93 165.71 L 1335.93 123.38" stroke-width="1" zvalue="1379"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="309@1" LinkObjectIDznd="305@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1335.93 165.71 L 1335.93 123.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="295">
   <path class="kv35" d="M 1376.78 154.44 L 1335.93 154.44" stroke-width="1" zvalue="1384"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="298@0" LinkObjectIDznd="299" MaxPinNum="2"/>
   </metadata>
  <path d="M 1376.78 154.44 L 1335.93 154.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="326">
   <path class="kv35" d="M 1336.05 297.54 L 1336.05 316.67" stroke-width="1" zvalue="1385"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="313@0" LinkObjectIDznd="6@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1336.05 297.54 L 1336.05 316.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="327">
   <path class="kv35" d="M 1146.5 214.78 L 1107.15 214.78" stroke-width="1" zvalue="1386"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="97" MaxPinNum="2"/>
   </metadata>
  <path d="M 1146.5 214.78 L 1107.15 214.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="329">
   <path class="kv35" d="M 1377.85 261.61 L 1336.08 261.61" stroke-width="1" zvalue="1389"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="328@0" LinkObjectIDznd="307" MaxPinNum="2"/>
   </metadata>
  <path d="M 1377.85 261.61 L 1336.08 261.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="331">
   <path class="kv35" d="M 1366.63 132.06 L 1335.93 132.06" stroke-width="1" zvalue="1390"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="297@0" LinkObjectIDznd="299" MaxPinNum="2"/>
   </metadata>
  <path d="M 1366.63 132.06 L 1335.93 132.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv35" d="M 1292.67 168 L 1292.67 179.01" stroke-width="1" zvalue="1393"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="3@1" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1292.67 168 L 1292.67 179.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv35" d="M 1336 132.06 L 1292.67 132.06 L 1292.67 139" stroke-width="1" zvalue="1394"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="331" LinkObjectIDznd="3@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1336 132.06 L 1292.67 132.06 L 1292.67 139" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv10" d="M 1338.33 548.75 L 1338.33 597.43" stroke-width="1" zvalue="1401"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@1" LinkObjectIDznd="10@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1338.33 548.75 L 1338.33 597.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv10" d="M 1338.44 616.55 L 1338.44 643.21" stroke-width="1" zvalue="1402"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@1" LinkObjectIDznd="33@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1338.44 616.55 L 1338.44 643.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv10" d="M 1338.41 667.04 L 1338.41 690" stroke-width="1" zvalue="1405"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="13@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1338.41 667.04 L 1338.41 690" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="239">
   <path class="kv10" d="M 879.39 547.36 L 879.39 597.69" stroke-width="1" zvalue="1412"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="680@1" LinkObjectIDznd="241@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.39 547.36 L 879.39 597.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="238">
   <path class="kv10" d="M 879.27 616.8 L 879.27 643.46" stroke-width="1" zvalue="1413"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="241@1" LinkObjectIDznd="240@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.27 616.8 L 879.27 643.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="237">
   <path class="kv10" d="M 879.24 667.29 L 879.24 688.89" stroke-width="1" zvalue="1414"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="240@0" LinkObjectIDznd="15@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 879.24 667.29 L 879.24 688.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="246">
   <path class="kv10" d="M 570.24 661.04 L 570.24 688.89" stroke-width="1" zvalue="1420"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="243@0" LinkObjectIDznd="15@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 570.24 661.04 L 570.24 688.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="247">
   <path class="kv10" d="M 570.27 637.21 L 570.27 610.81" stroke-width="1" zvalue="1421"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="243@1" LinkObjectIDznd="75@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 570.27 637.21 L 570.27 610.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="287">
   <path class="kv10" d="M 1706.39 660.04 L 1706.39 690" stroke-width="1" zvalue="1427"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="288@0" LinkObjectIDznd="13@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1706.39 660.04 L 1706.39 690" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="286">
   <path class="kv10" d="M 1706.42 636.21 L 1706.42 609.81" stroke-width="1" zvalue="1428"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="288@1" LinkObjectIDznd="289@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1706.42 636.21 L 1706.42 609.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="335">
   <path class="kv10" d="M 684.43 706.4 L 684.43 688.89" stroke-width="1" zvalue="1434"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="332@0" LinkObjectIDznd="15@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 684.43 706.4 L 684.43 688.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="336">
   <path class="kv10" d="M 684.4 730.24 L 684.4 757.43" stroke-width="1" zvalue="1435"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="332@1" LinkObjectIDznd="308@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 684.4 730.24 L 684.4 757.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="337">
   <path class="kv10" d="M 683.57 776.55 L 683.57 813.63" stroke-width="1" zvalue="1436"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="308@1" LinkObjectIDznd="320@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 683.57 776.55 L 683.57 813.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="367">
   <path class="kv10" d="M 849.51 837.57 L 849.51 883.83" stroke-width="1" zvalue="1463"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="369@1" LinkObjectIDznd="368@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 849.51 837.57 L 849.51 883.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="366">
   <path class="kv10" d="M 820.64 860.61 L 820.64 849.67 L 849.51 849.67" stroke-width="1" zvalue="1464"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="371@0" LinkObjectIDznd="367" MaxPinNum="2"/>
   </metadata>
  <path d="M 820.64 860.61 L 820.64 849.67 L 849.51 849.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="365">
   <path class="kv10" d="M 874.06 859.97 L 874.06 849 L 849.51 849" stroke-width="1" zvalue="1465"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="370@0" LinkObjectIDznd="367" MaxPinNum="2"/>
   </metadata>
  <path d="M 874.06 859.97 L 874.06 849 L 849.51 849" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="362">
   <path class="kv10" d="M 850.4 707.52 L 850.4 688.89" stroke-width="1" zvalue="1470"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="363@0" LinkObjectIDznd="15@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 850.4 707.52 L 850.4 688.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="361">
   <path class="kv10" d="M 850.37 731.35 L 850.37 758.54" stroke-width="1" zvalue="1471"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="363@1" LinkObjectIDznd="364@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 850.37 731.35 L 850.37 758.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="360">
   <path class="kv10" d="M 849.54 777.66 L 849.54 813.74" stroke-width="1" zvalue="1472"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="364@1" LinkObjectIDznd="369@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 849.54 777.66 L 849.54 813.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="386">
   <path class="kv10" d="M 1239 839.68 L 1239 885.94" stroke-width="1" zvalue="1483"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="388@1" LinkObjectIDznd="387@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1239 839.68 L 1239 885.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="385">
   <path class="kv10" d="M 1210.13 862.72 L 1210.13 851.78 L 1239 851.78" stroke-width="1" zvalue="1484"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="390@0" LinkObjectIDznd="386" MaxPinNum="2"/>
   </metadata>
  <path d="M 1210.13 862.72 L 1210.13 851.78 L 1239 851.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="384">
   <path class="kv10" d="M 1263.55 862.08 L 1263.55 851.11 L 1239 851.11" stroke-width="1" zvalue="1485"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="389@0" LinkObjectIDznd="386" MaxPinNum="2"/>
   </metadata>
  <path d="M 1263.55 862.08 L 1263.55 851.11 L 1239 851.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="381">
   <path class="kv10" d="M 1239.89 709.63 L 1239.89 690" stroke-width="1" zvalue="1490"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="382@0" LinkObjectIDznd="13@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1239.89 709.63 L 1239.89 690" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="380">
   <path class="kv10" d="M 1239.86 733.46 L 1239.86 760.66" stroke-width="1" zvalue="1491"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="382@1" LinkObjectIDznd="383@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1239.86 733.46 L 1239.86 760.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="379">
   <path class="kv10" d="M 1239.03 779.77 L 1239.03 815.85" stroke-width="1" zvalue="1492"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="383@1" LinkObjectIDznd="388@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1239.03 779.77 L 1239.03 815.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="403">
   <path class="kv10" d="M 1395 838.68 L 1395 884.94" stroke-width="1" zvalue="1501"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="405@1" LinkObjectIDznd="404@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1395 838.68 L 1395 884.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="402">
   <path class="kv10" d="M 1366.13 861.72 L 1366.13 850.78 L 1395 850.78" stroke-width="1" zvalue="1502"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="407@0" LinkObjectIDznd="403" MaxPinNum="2"/>
   </metadata>
  <path d="M 1366.13 861.72 L 1366.13 850.78 L 1395 850.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="401">
   <path class="kv10" d="M 1419.55 861.08 L 1419.55 850.11 L 1395 850.11" stroke-width="1" zvalue="1503"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="406@0" LinkObjectIDznd="403" MaxPinNum="2"/>
   </metadata>
  <path d="M 1419.55 861.08 L 1419.55 850.11 L 1395 850.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="398">
   <path class="kv10" d="M 1395.89 708.63 L 1395.89 690" stroke-width="1" zvalue="1508"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="399@0" LinkObjectIDznd="13@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1395.89 708.63 L 1395.89 690" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="397">
   <path class="kv10" d="M 1395.86 732.46 L 1395.86 759.66" stroke-width="1" zvalue="1509"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="399@1" LinkObjectIDznd="400@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1395.86 732.46 L 1395.86 759.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="396">
   <path class="kv10" d="M 1395.03 778.77 L 1395.03 814.85" stroke-width="1" zvalue="1510"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="400@1" LinkObjectIDznd="405@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1395.03 778.77 L 1395.03 814.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="420">
   <path class="kv10" d="M 1559 837.68 L 1559 883.94" stroke-width="1" zvalue="1519"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="422@1" LinkObjectIDznd="421@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1559 837.68 L 1559 883.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="419">
   <path class="kv10" d="M 1530.13 860.72 L 1530.13 849.78 L 1559 849.78" stroke-width="1" zvalue="1520"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="424@0" LinkObjectIDznd="420" MaxPinNum="2"/>
   </metadata>
  <path d="M 1530.13 860.72 L 1530.13 849.78 L 1559 849.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="418">
   <path class="kv10" d="M 1583.55 860.08 L 1583.55 849.11 L 1559 849.11" stroke-width="1" zvalue="1521"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="423@0" LinkObjectIDznd="420" MaxPinNum="2"/>
   </metadata>
  <path d="M 1583.55 860.08 L 1583.55 849.11 L 1559 849.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="415">
   <path class="kv10" d="M 1559.89 707.63 L 1559.89 690" stroke-width="1" zvalue="1526"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="416@0" LinkObjectIDznd="13@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1559.89 707.63 L 1559.89 690" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="414">
   <path class="kv10" d="M 1559.86 731.46 L 1559.86 758.66" stroke-width="1" zvalue="1527"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="416@1" LinkObjectIDznd="417@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1559.86 731.46 L 1559.86 758.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="413">
   <path class="kv10" d="M 1559.03 777.77 L 1559.03 813.85" stroke-width="1" zvalue="1528"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="417@1" LinkObjectIDznd="422@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1559.03 777.77 L 1559.03 813.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="427">
   <path class="kv10" d="M 1043.43 707.52 L 1043.43 688.89" stroke-width="1" zvalue="1531"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="425@0" LinkObjectIDznd="15@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1043.43 707.52 L 1043.43 688.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="428">
   <path class="kv10" d="M 1043.4 731.35 L 1043.4 745 L 1177 745 L 1177 690" stroke-width="1" zvalue="1532"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="425@1" LinkObjectIDznd="13@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1043.4 731.35 L 1043.4 745 L 1177 745 L 1177 690" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="27">
   <use class="kv35" height="30" transform="rotate(0,919.932,110.222) scale(6.34921,0.814815) translate(-756.321,22.2727)" width="7" x="897.7098173330401" xlink:href="#ACLineSegment:线路_0" y="98" zvalue="24"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249321635844" ObjectName="35kV弄班线"/>
   <cge:TPSR_Ref TObjectID="8444249321635844_5066549679554561"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,919.932,110.222) scale(6.34921,0.814815) translate(-756.321,22.2727)" width="7" x="897.7098173330401" y="98"/></g>
  <g id="305">
   <use class="kv35" height="30" transform="rotate(0,1335.93,111.278) scale(6.34921,0.814815) translate(-1106.8,22.5126)" width="7" x="1313.70981733304" xlink:href="#ACLineSegment:线路_0" y="99.05555534362782" zvalue="1377"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249322487812" ObjectName="35kV汉相弄线弄岛变侧"/>
   <cge:TPSR_Ref TObjectID="8444249322487812_5066549679554561"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1335.93,111.278) scale(6.34921,0.814815) translate(-1106.8,22.5126)" width="7" x="1313.70981733304" y="99.05555534362782"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="32">
   <use class="kv35" height="20" transform="rotate(270,971.611,153.333) scale(-1.11111,1.11111) translate(-1845.51,-14.2222)" width="10" x="966.0555555555555" xlink:href="#GroundDisconnector:地刀_0" y="142.2222222222221" zvalue="31"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453855412226" ObjectName="35kV弄班线36267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453855412226"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,971.611,153.333) scale(-1.11111,1.11111) translate(-1845.51,-14.2222)" width="10" x="966.0555555555555" y="142.2222222222221"/></g>
  <g id="91">
   <use class="kv35" height="20" transform="rotate(270,1157.33,214.722) scale(-1.11111,1.11111) translate(-2198.38,-20.3611)" width="10" x="1151.777777777778" xlink:href="#GroundDisconnector:地刀_0" y="203.6111008326212" zvalue="95"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453855608835" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453855608835"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1157.33,214.722) scale(-1.11111,1.11111) translate(-2198.38,-20.3611)" width="10" x="1151.777777777778" y="203.6111008326212"/></g>
  <g id="323">
   <use class="kv10" height="20" transform="rotate(0,654.615,870.333) scale(1.11111,1.11111) translate(-64.9059,-85.9222)" width="10" x="649.0591391705665" xlink:href="#GroundDisconnector:地刀_0" y="859.2222256130642" zvalue="637"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453856067586" ObjectName="10kV开发区线06167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453856067586"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,654.615,870.333) scale(1.11111,1.11111) translate(-64.9059,-85.9222)" width="10" x="649.0591391705665" y="859.2222256130642"/></g>
  <g id="298">
   <use class="kv35" height="20" transform="rotate(270,1387.61,154.389) scale(-1.11111,1.11111) translate(-2635.91,-14.3278)" width="10" x="1382.055555555556" xlink:href="#GroundDisconnector:地刀_0" y="143.27777756585" zvalue="1380"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453856460802" ObjectName="35kV汉相弄线36167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453856460802"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1387.61,154.389) scale(-1.11111,1.11111) translate(-2635.91,-14.3278)" width="10" x="1382.055555555556" y="143.27777756585"/></g>
  <g id="328">
   <use class="kv35" height="20" transform="rotate(270,1388.68,261.556) scale(-1.11111,1.11111) translate(-2637.93,-25.0444)" width="10" x="1383.123516000293" xlink:href="#GroundDisconnector:地刀_0" y="250.4444444444444" zvalue="1388"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453856788483" ObjectName="35kV汉相弄线36117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453856788483"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1388.68,261.556) scale(-1.11111,1.11111) translate(-2637.93,-25.0444)" width="10" x="1383.123516000293" y="250.4444444444444"/></g>
  <g id="371">
   <use class="kv10" height="20" transform="rotate(0,820.585,871.444) scale(1.11111,1.11111) translate(-81.503,-86.0333)" width="10" x="815.0295695852832" xlink:href="#GroundDisconnector:地刀_0" y="860.3333384195963" zvalue="1456"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453857705987" ObjectName="10kV芒滚线06267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453857705987"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,820.585,871.444) scale(1.11111,1.11111) translate(-81.503,-86.0333)" width="10" x="815.0295695852832" y="860.3333384195963"/></g>
  <g id="390">
   <use class="kv10" height="20" transform="rotate(0,1210.07,873.556) scale(1.11111,1.11111) translate(-120.452,-86.2444)" width="10" x="1204.515375422516" xlink:href="#GroundDisconnector:地刀_0" y="862.4444512261284" zvalue="1476"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453858099203" ObjectName="10kV南坎线06367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453858099203"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1210.07,873.556) scale(1.11111,1.11111) translate(-120.452,-86.2444)" width="10" x="1204.515375422516" y="862.4444512261284"/></g>
  <g id="407">
   <use class="kv10" height="20" transform="rotate(0,1366.07,872.556) scale(1.11111,1.11111) translate(-136.052,-86.1444)" width="10" x="1360.515375422517" xlink:href="#GroundDisconnector:地刀_0" y="861.4444512261284" zvalue="1494"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453858492419" ObjectName="10kV四分厂线06467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453858492419"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1366.07,872.556) scale(1.11111,1.11111) translate(-136.052,-86.1444)" width="10" x="1360.515375422517" y="861.4444512261284"/></g>
  <g id="424">
   <use class="kv10" height="20" transform="rotate(0,1530.07,871.556) scale(1.11111,1.11111) translate(-152.452,-86.0444)" width="10" x="1524.515375422517" xlink:href="#GroundDisconnector:地刀_0" y="860.4444512261284" zvalue="1512"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453858885635" ObjectName="10kV大沙河线06567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453858885635"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1530.07,871.556) scale(1.11111,1.11111) translate(-152.452,-86.0444)" width="10" x="1524.515375422517" y="860.4444512261284"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="94">
   <use class="kv35" height="18" transform="rotate(0,1111.02,173.527) scale(4.16076,-3.03389) translate(-820.293,-212.419)" width="15" x="1079.817741421268" xlink:href="#Accessory:PT8_0" y="146.222222222222" zvalue="97"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453855674370" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1111.02,173.527) scale(4.16076,-3.03389) translate(-820.293,-212.419)" width="15" x="1079.817741421268" y="146.222222222222"/></g>
  <g id="321">
   <use class="kv10" height="26" transform="rotate(0,708.059,871.222) scale(1,1) translate(0,0)" width="12" x="702.0591391705666" xlink:href="#Accessory:避雷器1_0" y="858.2222256130642" zvalue="640"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453855936514" ObjectName="10kV开发区线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,708.059,871.222) scale(1,1) translate(0,0)" width="12" x="702.0591391705666" y="858.2222256130642"/></g>
  <g id="592">
   <use class="kv35" height="26" transform="rotate(90,879,131.033) scale(-1,1) translate(-1758,0)" width="12" x="873" xlink:href="#Accessory:避雷器1_0" y="118.0333333333334" zvalue="1055"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453856133122" ObjectName="35kV弄班线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,879,131.033) scale(-1,1) translate(-1758,0)" width="12" x="873" y="118.0333333333334"/></g>
  <g id="75">
   <use class="kv10" height="48" transform="rotate(0,567.279,589.5) scale(1.08586,0.895833) translate(-42.9228,66.0465)" width="45" x="542.8474435691754" xlink:href="#Accessory:母线电压互感器11_0" y="568" zvalue="1130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453856264194" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,567.279,589.5) scale(1.08586,0.895833) translate(-42.9228,66.0465)" width="45" x="542.8474435691754" y="568"/></g>
  <g id="297">
   <use class="kv35" height="26" transform="rotate(270,1379,132.089) scale(1,1) translate(0,0)" width="12" x="1373" xlink:href="#Accessory:避雷器1_0" y="119.0888886769613" zvalue="1382"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453856329730" ObjectName="35kV汉相弄避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1379,132.089) scale(1,1) translate(0,0)" width="12" x="1373" y="119.0888886769613"/></g>
  <g id="289">
   <use class="kv10" height="48" transform="rotate(0,1703.43,588.5) scale(1.08586,0.895833) translate(-132.758,65.9302)" width="45" x="1679" xlink:href="#Accessory:母线电压互感器11_0" y="567" zvalue="1423"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453857247235" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,1703.43,588.5) scale(1.08586,0.895833) translate(-132.758,65.9302)" width="45" x="1679" y="567"/></g>
  <g id="370">
   <use class="kv10" height="26" transform="rotate(0,874.03,872.333) scale(1,1) translate(0,0)" width="12" x="868.0295695852833" xlink:href="#Accessory:避雷器1_0" y="859.3333384195963" zvalue="1458"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453857574915" ObjectName="10kV芒滚线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,874.03,872.333) scale(1,1) translate(0,0)" width="12" x="868.0295695852833" y="859.3333384195963"/></g>
  <g id="389">
   <use class="kv10" height="26" transform="rotate(0,1263.52,874.444) scale(1,1) translate(0,0)" width="12" x="1257.515375422517" xlink:href="#Accessory:避雷器1_0" y="861.4444512261284" zvalue="1478"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453857968131" ObjectName="10kV南坎线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1263.52,874.444) scale(1,1) translate(0,0)" width="12" x="1257.515375422517" y="861.4444512261284"/></g>
  <g id="406">
   <use class="kv10" height="26" transform="rotate(0,1419.52,873.444) scale(1,1) translate(0,0)" width="12" x="1413.515375422517" xlink:href="#Accessory:避雷器1_0" y="860.4444512261284" zvalue="1496"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453858361347" ObjectName="10kV四分厂线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1419.52,873.444) scale(1,1) translate(0,0)" width="12" x="1413.515375422517" y="860.4444512261284"/></g>
  <g id="423">
   <use class="kv10" height="26" transform="rotate(0,1583.52,872.444) scale(1,1) translate(0,0)" width="12" x="1577.515375422517" xlink:href="#Accessory:避雷器1_0" y="859.4444512261284" zvalue="1514"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453858754563" ObjectName="10kV大沙河线避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1583.52,872.444) scale(1,1) translate(0,0)" width="12" x="1577.515375422517" y="859.4444512261284"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="319">
   <use class="kv10" height="30" transform="rotate(0,683.03,896.222) scale(1,-1) translate(0,-1792.44)" width="12" x="677.0295695852833" xlink:href="#EnergyConsumer:负荷_0" y="881.2222256130642" zvalue="642"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453855805442" ObjectName="10kV开发区线"/>
   <cge:TPSR_Ref TObjectID="6192453855805442"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,683.03,896.222) scale(1,-1) translate(0,-1792.44)" width="12" x="677.0295695852833" y="881.2222256130642"/></g>
  <g id="1">
   <use class="kv35" height="30" transform="rotate(0,1292.67,212.5) scale(2.36667,2.36667) translate(-732.803,-102.211)" width="20" x="1269" xlink:href="#EnergyConsumer:站用变11_0" y="177" zvalue="1391"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453856854019" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1292.67,212.5) scale(2.36667,2.36667) translate(-732.803,-102.211)" width="20" x="1269" y="177"/></g>
  <g id="368">
   <use class="kv10" height="30" transform="rotate(0,849.514,897.333) scale(1,-1) translate(0,-1794.67)" width="12" x="843.5141941627667" xlink:href="#EnergyConsumer:负荷_0" y="882.3333384195963" zvalue="1460"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453857443843" ObjectName="10kV芒滚线"/>
   <cge:TPSR_Ref TObjectID="6192453857443843"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,849.514,897.333) scale(1,-1) translate(0,-1794.67)" width="12" x="843.5141941627667" y="882.3333384195963"/></g>
  <g id="387">
   <use class="kv10" height="30" transform="rotate(0,1239,899.444) scale(1,-1) translate(0,-1798.89)" width="12" x="1233" xlink:href="#EnergyConsumer:负荷_0" y="884.4444512261284" zvalue="1480"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453857837059" ObjectName="10kV南坎线"/>
   <cge:TPSR_Ref TObjectID="6192453857837059"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1239,899.444) scale(1,-1) translate(0,-1798.89)" width="12" x="1233" y="884.4444512261284"/></g>
  <g id="404">
   <use class="kv10" height="30" transform="rotate(0,1395,898.444) scale(1,-1) translate(0,-1796.89)" width="12" x="1389" xlink:href="#EnergyConsumer:负荷_0" y="883.4444512261284" zvalue="1498"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453858230275" ObjectName="10kV四分厂线"/>
   <cge:TPSR_Ref TObjectID="6192453858230275"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1395,898.444) scale(1,-1) translate(0,-1796.89)" width="12" x="1389" y="883.4444512261284"/></g>
  <g id="421">
   <use class="kv10" height="30" transform="rotate(0,1559,897.444) scale(1,-1) translate(0,-1794.89)" width="12" x="1553" xlink:href="#EnergyConsumer:负荷_0" y="882.4444512261284" zvalue="1516"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453858623491" ObjectName="10kV大沙河线"/>
   <cge:TPSR_Ref TObjectID="6192453858623491"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1559,897.444) scale(1,-1) translate(0,-1794.89)" width="12" x="1553" y="882.4444512261284"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="680">
   <g id="6800">
    <use class="kv35" height="30" transform="rotate(0,879.386,508.5) scale(2.66667,2.76667) translate(-529.616,-298.205)" width="24" x="847.39" xlink:href="#PowerTransformer2:可调不带中性点_0" y="467" zvalue="1092"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874570334210" ObjectName="35"/>
    </metadata>
   </g>
   <g id="6801">
    <use class="kv10" height="30" transform="rotate(0,879.386,508.5) scale(2.66667,2.76667) translate(-529.616,-298.205)" width="24" x="847.39" xlink:href="#PowerTransformer2:可调不带中性点_1" y="467" zvalue="1092"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874570399746" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399524876290" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399524876290"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,879.386,508.5) scale(2.66667,2.76667) translate(-529.616,-298.205)" width="24" x="847.39" y="467"/></g>
  <g id="44">
   <g id="440">
    <use class="kv35" height="60" transform="rotate(0,1338.33,507.833) scale(1.38333,1.38333) translate(-363.197,-129.225)" width="40" x="1310.67" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="466.33" zvalue="1118"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874570465282" ObjectName="35"/>
    </metadata>
   </g>
   <g id="441">
    <use class="kv10" height="60" transform="rotate(0,1338.33,507.833) scale(1.38333,1.38333) translate(-363.197,-129.225)" width="40" x="1310.67" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="466.33" zvalue="1118"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874570530818" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399524941826" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399524941826"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1338.33,507.833) scale(1.38333,1.38333) translate(-363.197,-129.225)" width="40" x="1310.67" y="466.33"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="70">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="70" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,919.932,15.5) scale(1,1) translate(0,0)" writing-mode="lr" x="920.13" xml:space="preserve" y="20.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133394165763" ObjectName="P"/>
   </metadata>
  </g>
  <g id="72">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="72" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,919.932,30.9815) scale(1,1) translate(0,0)" writing-mode="lr" x="920.13" xml:space="preserve" y="35.89" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133394231299" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="74">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="74" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,919.932,46.463) scale(1,1) translate(0,0)" writing-mode="lr" x="920.13" xml:space="preserve" y="51.37" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133394296835" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="107" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,919.932,61.9445) scale(1,1) translate(0,0)" writing-mode="lr" x="920.13" xml:space="preserve" y="66.84999999999999" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133395017731" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="249">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="249" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,683.03,964.722) scale(1,1) translate(0,0)" writing-mode="lr" x="683.22" xml:space="preserve" y="969.63" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133395738627" ObjectName="P"/>
   </metadata>
  </g>
  <g id="254">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="254" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,683.03,987.722) scale(1,1) translate(0,0)" writing-mode="lr" x="683.22" xml:space="preserve" y="992.63" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133395804163" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="259">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="259" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,683.03,1010.72) scale(1,1) translate(0,0)" writing-mode="lr" x="683.22" xml:space="preserve" y="1015.63" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133395869699" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="429">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="429" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1335.93,15.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1336.13" xml:space="preserve" y="20.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133401243652" ObjectName="P"/>
   </metadata>
  </g>
  <g id="430">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="430" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1335.93,30.9815) scale(1,1) translate(0,0)" writing-mode="lr" x="1336.13" xml:space="preserve" y="35.89" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133401309188" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="431">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="431" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1335.93,46.463) scale(1,1) translate(0,0)" writing-mode="lr" x="1336.13" xml:space="preserve" y="51.37" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133401374724" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="432">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="432" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1335.93,61.9445) scale(1,1) translate(0,0)" writing-mode="lr" x="1336.13" xml:space="preserve" y="66.84999999999999" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133402095620" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="434">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="434" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,849.514,964.722) scale(1,1) translate(0,0)" writing-mode="lr" x="849.71" xml:space="preserve" y="969.63" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133405503492" ObjectName="P"/>
   </metadata>
  </g>
  <g id="435">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="435" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,849.514,987.722) scale(1,1) translate(0,0)" writing-mode="lr" x="849.71" xml:space="preserve" y="992.63" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133405569028" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="436">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="436" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,849.514,1010.72) scale(1,1) translate(0,0)" writing-mode="lr" x="849.71" xml:space="preserve" y="1015.63" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133405634564" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="437">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="437" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1239,964.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1239.2" xml:space="preserve" y="969.63" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133406945283" ObjectName="P"/>
   </metadata>
  </g>
  <g id="438">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="438" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1239,987.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1239.2" xml:space="preserve" y="992.63" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133407010819" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="439">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="439" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1239,1010.72) scale(1,1) translate(0,0)" writing-mode="lr" x="1239.2" xml:space="preserve" y="1015.63" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133407076355" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="440">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="440" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1395,964.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1395.2" xml:space="preserve" y="969.63" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133408387076" ObjectName="P"/>
   </metadata>
  </g>
  <g id="441">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="441" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1395,987.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1395.2" xml:space="preserve" y="992.63" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133408452612" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="442">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="442" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1395,1010.72) scale(1,1) translate(0,0)" writing-mode="lr" x="1395.2" xml:space="preserve" y="1015.63" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133408518148" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="443">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="443" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1559,964.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1559.2" xml:space="preserve" y="969.63" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133409828867" ObjectName="P"/>
   </metadata>
  </g>
  <g id="444">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="444" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1559,987.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1559.2" xml:space="preserve" y="992.63" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133409894403" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="445">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="445" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1559,1010.72) scale(1,1) translate(0,0)" writing-mode="lr" x="1559.2" xml:space="preserve" y="1015.63" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133409959939" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="9" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,803.886,396.5) scale(1,1) translate(0,0)" writing-mode="lr" x="804.08" xml:space="preserve" y="401.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133396656131" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="17" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,803.886,423.5) scale(1,1) translate(0,0)" writing-mode="lr" x="804.08" xml:space="preserve" y="428.41" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133396721667" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="18" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,803.886,450.5) scale(1,1) translate(0,0)" writing-mode="lr" x="804.08" xml:space="preserve" y="455.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133396918275" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="38" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,799.886,564.5) scale(1,1) translate(0,0)" writing-mode="lr" x="800.08" xml:space="preserve" y="569.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133396787203" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="39" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,799.886,591.5) scale(1,1) translate(0,0)" writing-mode="lr" x="800.08" xml:space="preserve" y="596.41" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133396852739" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="40">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="40" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,799.886,618.5) scale(1,1) translate(0,0)" writing-mode="lr" x="800.08" xml:space="preserve" y="623.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133397245955" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="41">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="41" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1438.83,395.833) scale(1,1) translate(9.2848e-13,0)" writing-mode="lr" x="1439.03" xml:space="preserve" y="400.74" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133398687748" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="45">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="45" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1438.83,422.833) scale(1,1) translate(9.2848e-13,0)" writing-mode="lr" x="1439.03" xml:space="preserve" y="427.74" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133398753284" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="46">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="46" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1438.83,449.833) scale(1,1) translate(9.2848e-13,0)" writing-mode="lr" x="1439.03" xml:space="preserve" y="454.74" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133398949892" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="47">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="47" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1434.83,563.833) scale(1,1) translate(9.25815e-13,0)" writing-mode="lr" x="1435.03" xml:space="preserve" y="568.74" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133398818820" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="48">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="48" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1434.83,590.833) scale(1,1) translate(9.25815e-13,0)" writing-mode="lr" x="1435.03" xml:space="preserve" y="595.74" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133398884356" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="68" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1434.83,617.833) scale(1,1) translate(9.25815e-13,0)" writing-mode="lr" x="1435.03" xml:space="preserve" y="622.74" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133399277572" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="111" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,140.139,518.75) scale(1,1) translate(1.27429e-14,0)" writing-mode="lr" x="140.25" xml:space="preserve" y="523.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133392068612" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="112">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="112" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,140.139,541.75) scale(1,1) translate(1.27429e-14,5.93692e-14)" writing-mode="lr" x="140.25" xml:space="preserve" y="546.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133392134148" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="113">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="113" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,140.139,564.75) scale(1,1) translate(1.27429e-14,-1.23845e-13)" writing-mode="lr" x="140.25" xml:space="preserve" y="569.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133392199684" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="116">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,140.139,493.25) scale(1,1) translate(1.27429e-14,-1.07969e-13)" writing-mode="lr" x="140.25" xml:space="preserve" y="498.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133392330756" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="118">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="118" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,144.5,200.611) scale(1,1) translate(0,0)" writing-mode="lr" x="144.65" xml:space="preserve" y="206.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133392461827" ObjectName="F"/>
   </metadata>
  </g>
  <g id="119">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,259.542,518.75) scale(1,1) translate(2.59993e-14,0)" writing-mode="lr" x="259.65" xml:space="preserve" y="523.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133393117187" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="120">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="120" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,259.542,541.75) scale(1,1) translate(2.59993e-14,0)" writing-mode="lr" x="259.65" xml:space="preserve" y="546.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133393182723" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="121">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,259.542,564.75) scale(1,1) translate(2.59993e-14,-1.23845e-13)" writing-mode="lr" x="259.65" xml:space="preserve" y="569.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133393248259" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="123">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,259.542,493.25) scale(1,1) translate(2.59993e-14,-1.07969e-13)" writing-mode="lr" x="259.65" xml:space="preserve" y="498.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133393379331" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="125">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="125" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,144.5,225.5) scale(1,1) translate(0,0)" writing-mode="lr" x="144.65" xml:space="preserve" y="231.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133393510404" ObjectName="F"/>
   </metadata>
  </g>
  <g id="126">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="126" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,317.5,518.75) scale(1,1) translate(3.24339e-14,0)" writing-mode="lr" x="317.61" xml:space="preserve" y="523.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133392592899" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="127">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="127" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,317.5,541.75) scale(1,1) translate(3.24339e-14,-1.18738e-13)" writing-mode="lr" x="317.61" xml:space="preserve" y="546.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133392658435" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="128">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="128" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,317.5,564.75) scale(1,1) translate(3.24339e-14,-1.23845e-13)" writing-mode="lr" x="317.61" xml:space="preserve" y="569.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133392723971" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="129">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="129" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,321.5,224.75) scale(1,1) translate(0,0)" writing-mode="lr" x="321.65" xml:space="preserve" y="231.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133392986116" ObjectName="F"/>
   </metadata>
  </g>
  <g id="131">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="131" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,317.5,493.25) scale(1,1) translate(3.24339e-14,-1.07969e-13)" writing-mode="lr" x="317.61" xml:space="preserve" y="498.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133392855043" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="139">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,144.5,247.694) scale(1,1) translate(0,0)" writing-mode="lr" x="144.65" xml:space="preserve" y="253.97" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133397114883" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,144.5,270.694) scale(1,1) translate(0,-1.75656e-13)" writing-mode="lr" x="144.65" xml:space="preserve" y="276.97" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133397180419" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="147">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,321.5,246.694) scale(1,1) translate(0,0)" writing-mode="lr" x="321.65" xml:space="preserve" y="252.97" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133399146500" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="149">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,140.139,591.75) scale(1,1) translate(1.27429e-14,1.29841e-13)" writing-mode="lr" x="140.25" xml:space="preserve" y="596.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133392527363" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="150" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,259.542,591.75) scale(1,1) translate(2.59993e-14,1.29841e-13)" writing-mode="lr" x="259.65" xml:space="preserve" y="596.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133393575939" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="151">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="151" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,317.5,591.75) scale(1,1) translate(3.24339e-14,1.29841e-13)" writing-mode="lr" x="317.61" xml:space="preserve" y="596.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133393051651" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="117" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,144.5,176) scale(1,1) translate(0,0)" writing-mode="lr" x="144.65" xml:space="preserve" y="182.33" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133469990916" ObjectName=""/>
   </metadata>
  </g>
  <g id="124">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="124" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,321.5,176) scale(1,1) translate(0,0)" writing-mode="lr" x="321.65" xml:space="preserve" y="182.35" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133470056452" ObjectName=""/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,572.633,507) scale(1,1) translate(0,0)" writing-mode="lr" x="572.83" xml:space="preserve" y="511.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133393379331" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1109.75,102.417) scale(1,1) translate(0,0)" writing-mode="lr" x="1109.95" xml:space="preserve" y="107.33" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133392330756" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1699,507) scale(1,1) translate(0,0)" writing-mode="lr" x="1698.53" xml:space="preserve" y="511.78" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133392855043" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,351.125,318) scale(0.708333,0.665547) translate(140.206,154.786)" width="30" x="340.5" xlink:href="#State:红绿圆(方形)_0" y="308.02" zvalue="1644"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374923464705" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,351.125,318) scale(0.708333,0.665547) translate(140.206,154.786)" width="30" x="340.5" y="308.02"/></g>
  <g id="1035">
   <use height="30" transform="rotate(0,255.5,318) scale(0.708333,0.665547) translate(100.831,154.786)" width="30" x="244.88" xlink:href="#State:红绿圆(方形)_0" y="308.02" zvalue="1645"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562957665239046" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,255.5,318) scale(0.708333,0.665547) translate(100.831,154.786)" width="30" x="244.88" y="308.02"/></g>
 </g>
</svg>