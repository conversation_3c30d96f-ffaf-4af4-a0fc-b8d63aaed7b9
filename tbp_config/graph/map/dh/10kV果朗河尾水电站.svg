<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549586165762" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV果朗河尾水电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="45" xlink:href="logo.png" y="46.67"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,179.625,76.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="179.63" xml:space="preserve" y="80.17" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,181.333,76.357) scale(1,1) translate(6.99441e-15,0)" writing-mode="lr" x="181.33" xml:space="preserve" y="85.36" zvalue="3">10kV果朗河尾水电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="6" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,66.4375,363) scale(1,1) translate(0,0)" width="72.88" x="30" y="351" zvalue="67"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,66.4375,363) scale(1,1) translate(0,0)" writing-mode="lr" x="66.44" xml:space="preserve" y="367.5" zvalue="67">信号一览</text>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="379.0000000000001" x2="379.0000000000001" y1="14.66666666666674" y2="1044.666666666667" zvalue="4"/>
  <line fill="none" id="28" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.000000000000341" x2="371.9999999999999" y1="150.5371592807491" y2="150.5371592807491" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="187.0000000000001" y1="162.6666666666668" y2="162.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="187.0000000000001" y1="188.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="6.000000000000114" y1="162.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="187.0000000000001" y1="162.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="368.0000000000001" y1="162.6666666666668" y2="162.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="368.0000000000001" y1="188.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="187.0000000000001" y1="162.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0000000000001" x2="368.0000000000001" y1="162.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="187.0000000000001" y1="188.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="187.0000000000001" y1="212.9166666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="6.000000000000114" y1="188.6666666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="187.0000000000001" y1="188.6666666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="368.0000000000001" y1="188.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="368.0000000000001" y1="212.9166666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="187.0000000000001" y1="188.6666666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0000000000001" x2="368.0000000000001" y1="188.6666666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="187.0000000000001" y1="212.9166666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="187.0000000000001" y1="235.6666666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="6.000000000000114" y1="212.9166666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="187.0000000000001" y1="212.9166666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="368.0000000000001" y1="212.9166666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="368.0000000000001" y1="235.6666666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="187.0000000000001" y1="212.9166666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0000000000001" x2="368.0000000000001" y1="212.9166666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="187.0000000000001" y1="235.6666666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="187.0000000000001" y1="258.4166666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="6.000000000000114" y1="235.6666666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="187.0000000000001" y1="235.6666666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="368.0000000000001" y1="235.6666666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="368.0000000000001" y1="258.4166666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="187.0000000000001" y1="235.6666666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0000000000001" x2="368.0000000000001" y1="235.6666666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="187.0000000000001" y1="258.4166666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="187.0000000000001" y1="281.1666666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="6.000000000000114" x2="6.000000000000114" y1="258.4166666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="187.0000000000001" y1="258.4166666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="368.0000000000001" y1="258.4166666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="368.0000000000001" y1="281.1666666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="187.0000000000001" x2="187.0000000000001" y1="258.4166666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="368.0000000000001" x2="368.0000000000001" y1="258.4166666666668" y2="281.1666666666668"/>
  <line fill="none" id="26" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.000000000000341" x2="371.9999999999999" y1="620.5371592807492" y2="620.5371592807492" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000114" x2="95.00000000000011" y1="935.666666666667" y2="935.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000114" x2="95.00000000000011" y1="974.829966666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000114" x2="5.000000000000114" y1="935.666666666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="95.00000000000011" y1="935.666666666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="365.0000000000001" y1="935.666666666667" y2="935.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="365.0000000000001" y1="974.829966666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="95.00000000000011" y1="935.666666666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="365.0000000000001" x2="365.0000000000001" y1="935.666666666667" y2="974.829966666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000114" x2="95.00000000000011" y1="974.829936666667" y2="974.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000114" x2="95.00000000000011" y1="1002.748336666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000114" x2="5.000000000000114" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="95.00000000000011" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="185.0000000000001" y1="974.829936666667" y2="974.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="185.0000000000001" y1="1002.748336666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="95.00000000000011" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000002" x2="275.0000000000002" y1="974.829936666667" y2="974.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000002" x2="275.0000000000002" y1="1002.748336666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000002" x2="185.0000000000002" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000002" x2="275.0000000000002" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000001" x2="365.0000000000001" y1="974.829936666667" y2="974.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000001" x2="365.0000000000001" y1="1002.748336666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000001" x2="275.0000000000001" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="365.0000000000001" x2="365.0000000000001" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000114" x2="95.00000000000011" y1="1002.748266666667" y2="1002.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000114" x2="95.00000000000011" y1="1030.666666666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="5.000000000000114" x2="5.000000000000114" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="95.00000000000011" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="185.0000000000001" y1="1002.748266666667" y2="1002.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="185.0000000000001" y1="1030.666666666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="95.00000000000011" x2="95.00000000000011" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000001" x2="185.0000000000001" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000002" x2="275.0000000000002" y1="1002.748266666667" y2="1002.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000002" x2="275.0000000000002" y1="1030.666666666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="185.0000000000002" x2="185.0000000000002" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000002" x2="275.0000000000002" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000001" x2="365.0000000000001" y1="1002.748266666667" y2="1002.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000001" x2="365.0000000000001" y1="1030.666666666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="275.0000000000001" x2="275.0000000000001" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="365.0000000000001" x2="365.0000000000001" y1="1002.748266666667" y2="1030.666666666667"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50,955.667) scale(1,1) translate(0,0)" writing-mode="lr" x="50" xml:space="preserve" y="961.67" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47,989.667) scale(1,1) translate(0,0)" writing-mode="lr" x="47" xml:space="preserve" y="995.67" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229,989.667) scale(1,1) translate(0,0)" writing-mode="lr" x="229" xml:space="preserve" y="995.67" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46,1017.67) scale(1,1) translate(0,0)" writing-mode="lr" x="46" xml:space="preserve" y="1023.67" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228,1017.67) scale(1,1) translate(0,0)" writing-mode="lr" x="228" xml:space="preserve" y="1023.67" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.5,650.167) scale(1,1) translate(0,0)" writing-mode="lr" x="70.50000000000011" xml:space="preserve" y="654.6666666666667" zvalue="18">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,230.054,957.667) scale(1,1) translate(0,0)" writing-mode="lr" x="230.05" xml:space="preserve" y="963.67" zvalue="26">GuoLangHe-01-2016</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,140.054,989.667) scale(1,1) translate(0,0)" writing-mode="lr" x="140.05" xml:space="preserve" y="995.67" zvalue="27">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44,176.667) scale(1,1) translate(0,0)" writing-mode="lr" x="44" xml:space="preserve" y="182.17" zvalue="29">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224,176.667) scale(1,1) translate(0,0)" writing-mode="lr" x="224" xml:space="preserve" y="182.17" zvalue="30">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.1875,248.667) scale(1,1) translate(0,0)" writing-mode="lr" x="51.19" xml:space="preserve" y="253.17" zvalue="31">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236.083,203.361) scale(1,1) translate(0,0)" writing-mode="lr" x="236.08" xml:space="preserve" y="207.86" zvalue="32">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,695.869,595.214) scale(1,1) translate(0,0)" writing-mode="lr" x="695.87" xml:space="preserve" y="599.71" zvalue="35">0.4kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1041.43,117.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1041.43" xml:space="preserve" y="122.21" zvalue="37">10kV尾水联络线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" x="1038.7734375" xml:space="preserve" y="963.3772309167044" zvalue="39">#1发电机       </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1038.7734375" xml:space="preserve" y="979.3772309167044" zvalue="39">500KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1065.64,791.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1065.64" xml:space="preserve" y="796.36" zvalue="41">401</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1069.3,702.429) scale(1,1) translate(0,0)" writing-mode="lr" x="1069.3" xml:space="preserve" y="706.9299999999999" zvalue="44">4011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1004.34,336.143) scale(1,1) translate(0,0)" writing-mode="lr" x="1004.34" xml:space="preserve" y="340.64" zvalue="48">0511</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1066.36,248.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1066.36" xml:space="preserve" y="253.07" zvalue="52">051</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" x="1101.5234375" xml:space="preserve" y="483.6547619047619" zvalue="59">#1主变       </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1101.5234375" xml:space="preserve" y="499.6547619047619" zvalue="59">630KVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,182.211,363.591) scale(1,1) translate(0,0)" writing-mode="lr" x="182.21" xml:space="preserve" y="368.09" zvalue="63">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,287.211,363.591) scale(1,1) translate(0,0)" writing-mode="lr" x="287.21" xml:space="preserve" y="368.09" zvalue="64">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="10kV果朗河尾水电站.svg"><rect fill-opacity="0" height="24" width="72.88" x="30" y="351" zvalue="67"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="33">
   <path class="v400" d="M 691.25 632.05 L 1436.25 632.05" stroke-width="6" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674242723845" ObjectName="0.4kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674242723845"/></metadata>
  <path d="M 691.25 632.05 L 1436.25 632.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="37">
   <use class="v400" height="30" transform="rotate(0,1038.57,916.286) scale(2.14286,2.14286) translate(-536.762,-471.543)" width="30" x="1006.428571428571" xlink:href="#Generator:发电机_0" y="884.1428571428571" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449793818630" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449793818630"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1038.57,916.286) scale(2.14286,2.14286) translate(-536.762,-471.543)" width="30" x="1006.428571428571" y="884.1428571428571"/></g>
 </g>
 <g id="BreakerClass">
  <g id="39">
   <use class="v400" height="20" transform="rotate(0,1038.64,792.857) scale(2.14286,1.92857) translate(-548.229,-372.46)" width="10" x="1027.928571428571" xlink:href="#Breaker:开关_0" y="773.5714285714287" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924509630469" ObjectName="#1 发电机401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924509630469"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1038.64,792.857) scale(2.14286,1.92857) translate(-548.229,-372.46)" width="10" x="1027.928571428571" y="773.5714285714287"/></g>
  <g id="48">
   <use class="kv10" height="20" transform="rotate(0,1038.64,249.571) scale(2.14286,1.92857) translate(-548.229,-110.878)" width="10" x="1027.928571428571" xlink:href="#Breaker:开关_0" y="230.2857142857143" zvalue="51"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924509696005" ObjectName="10kV尾水联络线051断路器"/>
   <cge:TPSR_Ref TObjectID="6473924509696005"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1038.64,249.571) scale(2.14286,1.92857) translate(-548.229,-110.878)" width="10" x="1027.928571428571" y="230.2857142857143"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="40">
   <path class="v400" d="M 1038.57 884.68 L 1038.57 811.28" stroke-width="1" zvalue="41"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="39@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1038.57 884.68 L 1038.57 811.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="v400" d="M 1038.57 774.41 L 1038.53 718.87" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@0" LinkObjectIDznd="42@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1038.57 774.41 L 1038.53 718.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="v400" d="M 1038.57 688.23 L 1038.57 632.05" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@0" LinkObjectIDznd="33@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1038.57 688.23 L 1038.57 632.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv10" d="M 1038.57 184.74 L 1038.57 231.12" stroke-width="1" zvalue="52"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1038.57 184.74 L 1038.57 231.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv10" d="M 1038.79 267.99 L 1038.79 321.95" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@1" LinkObjectIDznd="45@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1038.79 267.99 L 1038.79 321.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="v400" d="M 1038.57 543.01 L 1038.57 632.05" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@1" LinkObjectIDznd="33@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1038.57 543.01 L 1038.57 632.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv10" d="M 1037.14 352.59 L 1037.14 418.85" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@1" LinkObjectIDznd="55@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1037.14 352.59 L 1037.14 418.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="42">
   <use class="v400" height="30" transform="rotate(0,1038.45,703.429) scale(1.42857,1.04762) translate(-308.32,-31.2597)" width="15" x="1027.731747070199" xlink:href="#Disconnector:刀闸_0" y="687.7142857142857" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449793884166" ObjectName="#1发电机4011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449793884166"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1038.45,703.429) scale(1.42857,1.04762) translate(-308.32,-31.2597)" width="15" x="1027.731747070199" y="687.7142857142857"/></g>
  <g id="45">
   <use class="kv10" height="30" transform="rotate(0,1037.06,337.143) scale(1.42857,1.04762) translate(-307.902,-14.6104)" width="15" x="1026.341193679849" xlink:href="#Disconnector:刀闸_0" y="321.4285714285716" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449793949702" ObjectName="#1主变10kV侧0511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449793949702"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1037.06,337.143) scale(1.42857,1.04762) translate(-307.902,-14.6104)" width="15" x="1026.341193679849" y="321.4285714285716"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="55">
   <g id="550">
    <use class="kv10" height="60" transform="rotate(0,1038.57,480.81) scale(1.89286,2.10317) translate(-472.035,-219.103)" width="40" x="1000.71" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="417.71" zvalue="58"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874435592196" ObjectName="10"/>
    </metadata>
   </g>
   <g id="551">
    <use class="v400" height="60" transform="rotate(0,1038.57,480.81) scale(1.89286,2.10317) translate(-472.035,-219.103)" width="40" x="1000.71" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="417.71" zvalue="58"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874435657732" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399450034180" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399450034180"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1038.57,480.81) scale(1.89286,2.10317) translate(-472.035,-219.103)" width="40" x="1000.71" y="417.71"/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,314.485,364.107) scale(0.708333,0.665547) translate(125.119,177.956)" width="30" x="303.86" xlink:href="#State:红绿圆(方形)_0" y="354.12" zvalue="65"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,314.485,364.107) scale(0.708333,0.665547) translate(125.119,177.956)" width="30" x="303.86" y="354.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,218.86,364.107) scale(0.708333,0.665547) translate(85.7439,177.956)" width="30" x="208.24" xlink:href="#State:红绿圆(方形)_0" y="354.12" zvalue="66"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,218.86,364.107) scale(0.708333,0.665547) translate(85.7439,177.956)" width="30" x="208.24" y="354.12"/></g>
 </g>
</svg>