<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549684207617" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:带熔断器35kVPT11_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="1.1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="26.25" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="18.06481481481482" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="20.69444444444444" y1="24.96612466124661" y2="24.96612466124661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69444444444444" x2="19.37962962962963" y1="24.96612466124661" y2="22.5"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,5.71) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="13" y2="1"/>
   <ellipse cx="15.03" cy="18.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.67" cy="23.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="18.5" cy="23.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="20.5" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:2绕组线路PT_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.4166666666666714"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="17" y1="28" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="17.5" y1="9.16666666666667" y2="9.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="9.166666666666671" y2="0.3333333333333321"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.5) scale(1,1) translate(0,0)" width="4" x="13" y="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="23.41666666666666" y2="9.083333333333332"/>
   <ellipse cx="15.15" cy="28.4" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="34" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.16666666666667" x2="17.16666666666667" y1="34.50000000000001" y2="34.50000000000001"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_0" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.66666666666666" x2="27.83333333333334" y1="6.416666666666666" y2="22.08333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_1" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.5" x2="28.5" y1="3.833333333333332" y2="22"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_2" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.16666666666667" x2="35.5" y1="3.083333333333332" y2="24.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="35.5" x2="21.16666666666667" y1="3.166666666666664" y2="24.83333333333334"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="Accessory:空挂线路_0" viewBox="0,0,11,13">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="-0.0833333333333357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="12.91666666666667" y2="0.2500000000000009"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_0" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.05" x2="7.05" y1="1.166666666666664" y2="26.33333333333334"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="10.66666666666667" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="26.5417774506975" y2="23.30486537657277"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="26.5417774506975" y2="23.30486537657277"/>
   <rect fill-opacity="0" height="6.25" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,8.63) scale(1,1) translate(0,0)" width="2.83" x="5.58" y="5.5"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_1" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.05" x2="7.05" y1="2.833333333333334" y2="14.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666668" x2="3.416666666666668" y1="2.617622152595477" y2="5.500000000000003"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="10.66666666666666" y1="2.617622152595477" y2="5.500000000000003"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="10.66666666666667" y1="0.8161359979676668" y2="3.698513845372194"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="24.65357874079139" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125000000000003" x2="3.750000000000002" y1="26.5417774506975" y2="23.30486537657277"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="24.65357874079139" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.125" x2="10.5" y1="26.5417774506975" y2="23.30486537657277"/>
   <rect fill-opacity="0" height="6.25" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,8.63) scale(1,1) translate(0,0)" width="2.83" x="5.58" y="5.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="7.049999999999999" y1="16" y2="18.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.133333333333333" x2="7.133333333333333" y1="18.16666666666667" y2="24.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:带融断手车刀闸_2" viewBox="0,0,14,27">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.33333333333333" x2="3.333333333333333" y1="9.166666666666668" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.666666666666667" x2="10.83333333333333" y1="9.124593892873616" y2="18.41666666666667"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="3.923132183908033" y2="8.733477011494239"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666666" x2="12.5" y1="0.9166666666666661" y2="5.727011494252875"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666667" x2="1.583333333333334" y1="26.56537356321841" y2="21.7550287356322"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="23.75933908045977" y2="18.94899425287357"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.041666666666663" x2="12.5" y1="26.56537356321841" y2="21.7550287356322"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV勐乃电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="50.75" xlink:href="logo.png" y="38.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,178.75,68.75) scale(1,1) translate(0,0)" writing-mode="lr" x="178.75" xml:space="preserve" y="72.25" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,179.25,68.4403) scale(1,1) translate(0,0)" writing-mode="lr" x="179.25" xml:space="preserve" y="77.44" zvalue="3">110kV勐乃电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="84" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="437"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="437">信号一览</text>
  <line fill="none" id="35" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384.75" x2="384.75" y1="6.75" y2="1036.75" zvalue="4"/>
  <line fill="none" id="33" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.750000000000455" x2="377.75" y1="142.6204926140824" y2="142.6204926140824" zvalue="6"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" x="648.8515625" xml:space="preserve" y="952.4869357854257" zvalue="41">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="648.8515625" xml:space="preserve" y="968.4869357854257" zvalue="41">15MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="321" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,720.912,950.878) scale(1,1) translate(0,0)" writing-mode="lr" x="720.91" xml:space="preserve" y="955.38" zvalue="44">励磁变PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,468.03,671.686) scale(1,1) translate(0,0)" writing-mode="lr" x="468.03" xml:space="preserve" y="676.1900000000001" zvalue="48">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="318" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,514.615,951.462) scale(1,1) translate(0,0)" writing-mode="lr" x="514.62" xml:space="preserve" y="955.96" zvalue="51">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,622.122,782.419) scale(1,1) translate(0,-1.37476e-12)" writing-mode="lr" x="622.12" xml:space="preserve" y="786.92" zvalue="56">051</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,606.44,716.669) scale(1,1) translate(0,0)" writing-mode="lr" x="606.4400000000001" xml:space="preserve" y="721.17" zvalue="58">0511c</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,684.784,861.22) scale(1,1) translate(0,0)" writing-mode="lr" x="684.78" xml:space="preserve" y="865.72" zvalue="62">0912c</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,854.875,919.5) scale(1,1) translate(0,0)" writing-mode="lr" x="854.88" xml:space="preserve" y="924" zvalue="105">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,883.371,799.875) scale(1,1) translate(0,0)" writing-mode="lr" x="883.37" xml:space="preserve" y="804.37" zvalue="107">053</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,826.421,725.612) scale(1,1) translate(0,0)" writing-mode="lr" x="826.42" xml:space="preserve" y="730.11" zvalue="112">0531c</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,560.556,643.417) scale(1,1) translate(0,0)" writing-mode="lr" x="560.5599999999999" xml:space="preserve" y="647.92" zvalue="117">0901c</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,530.241,536.75) scale(1,1) translate(0,0)" writing-mode="lr" x="530.24" xml:space="preserve" y="541.25" zvalue="125">10kVⅠ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1142.21,652.643) scale(1,1) translate(0,4.27753e-13)" writing-mode="lr" x="1142.21" xml:space="preserve" y="657.14" zvalue="129">10kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1489.38,930.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1489.38" xml:space="preserve" y="935.25" zvalue="173">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1517.87,811.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1517.87" xml:space="preserve" y="815.62" zvalue="175">054</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1443.92,735.862) scale(1,1) translate(0,0)" writing-mode="lr" x="1443.92" xml:space="preserve" y="740.36" zvalue="180">0541c</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,493.25,328.5) scale(1,1) translate(0,0)" writing-mode="lr" x="493.25" xml:space="preserve" y="333" zvalue="187">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,828.528,397.972) scale(1,1) translate(0,0)" writing-mode="lr" x="828.53" xml:space="preserve" y="402.47" zvalue="189">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,837.861,453.972) scale(1,1) translate(-5.51133e-13,0)" writing-mode="lr" x="837.86" xml:space="preserve" y="458.47" zvalue="191">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,860.194,430.972) scale(1,1) translate(0,0)" writing-mode="lr" x="860.1900000000001" xml:space="preserve" y="435.47" zvalue="194">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,899.813,511.419) scale(1,1) translate(-1.93651e-13,-1.10154e-13)" writing-mode="lr" x="899.8099999999999" xml:space="preserve" y="515.92" zvalue="197">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,685.617,555.5) scale(1,1) translate(0,0)" writing-mode="lr" x="685.62" xml:space="preserve" y="560" zvalue="202">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,841.185,635.321) scale(1,1) translate(2.7351e-13,0)" writing-mode="lr" x="841.1900000000001" xml:space="preserve" y="639.8200000000001" zvalue="206">0011c</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,899.813,531.419) scale(1,1) translate(0,0)" writing-mode="lr" x="899.8099999999999" xml:space="preserve" y="535.92" zvalue="216">20MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1317.53,397.972) scale(1,1) translate(0,0)" writing-mode="lr" x="1317.53" xml:space="preserve" y="402.47" zvalue="218">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1326.86,453.972) scale(1,1) translate(0,0)" writing-mode="lr" x="1326.86" xml:space="preserve" y="458.47" zvalue="220">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1349.19,430.972) scale(1,1) translate(0,0)" writing-mode="lr" x="1349.19" xml:space="preserve" y="435.47" zvalue="223">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1388.81,511.419) scale(1,1) translate(-3.0223e-13,-1.10154e-13)" writing-mode="lr" x="1388.81" xml:space="preserve" y="515.92" zvalue="226">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1174.62,555.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1174.62" xml:space="preserve" y="560" zvalue="231">1020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1330.31,635.321) scale(1,1) translate(0,0)" writing-mode="lr" x="1330.31" xml:space="preserve" y="639.8200000000001" zvalue="235">0021c</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1388.81,531.419) scale(1,1) translate(0,0)" writing-mode="lr" x="1388.81" xml:space="preserve" y="535.92" zvalue="245">20MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1481.56,636.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1481.56" xml:space="preserve" y="641.25" zvalue="247">0902c</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1490.24,550.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1490.24" xml:space="preserve" y="555.25" zvalue="254">10kVⅡ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,650.833,264.917) scale(1,1) translate(0,0)" writing-mode="lr" x="650.83" xml:space="preserve" y="269.42" zvalue="262">151</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,642.056,332.472) scale(1,1) translate(0,0)" writing-mode="lr" x="642.0599999999999" xml:space="preserve" y="336.97" zvalue="263">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,643.722,204.472) scale(1,1) translate(0,0)" writing-mode="lr" x="643.72" xml:space="preserve" y="208.97" zvalue="266">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,625.432,94.25) scale(1,1) translate(0,0)" writing-mode="lr" x="625.4299999999999" xml:space="preserve" y="98.75" zvalue="270">110kV勐乃电站线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,678.556,299.917) scale(1,1) translate(0,0)" writing-mode="lr" x="678.5599999999999" xml:space="preserve" y="304.42" zvalue="273">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,678.556,251.139) scale(1,1) translate(0,0)" writing-mode="lr" x="678.5599999999999" xml:space="preserve" y="255.64" zvalue="275">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,678.556,196.694) scale(1,1) translate(0,0)" writing-mode="lr" x="678.5599999999999" xml:space="preserve" y="201.19" zvalue="277">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,999.556,274.524) scale(1,1) translate(0,-4.72553e-13)" writing-mode="lr" x="999.5599999999999" xml:space="preserve" y="279.02" zvalue="286">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1041.9,334.087) scale(1,1) translate(0,0)" writing-mode="lr" x="1041.9" xml:space="preserve" y="338.59" zvalue="288">10</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,987.365,141.286) scale(1,1) translate(0,0)" writing-mode="lr" x="987.37" xml:space="preserve" y="145.79" zvalue="290">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1040.15,222.952) scale(1,1) translate(0,0)" writing-mode="lr" x="1040.15" xml:space="preserve" y="227.45" zvalue="294">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="269" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1539.08,266.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1539.08" xml:space="preserve" y="270.67" zvalue="302">152</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1530.31,333.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1530.31" xml:space="preserve" y="338.22" zvalue="303">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1531.97,205.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1531.97" xml:space="preserve" y="210.22" zvalue="306">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1513.68,95.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1513.68" xml:space="preserve" y="100" zvalue="310">备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="261" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1566.81,301.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1566.81" xml:space="preserve" y="305.67" zvalue="313">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1566.81,252.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1566.81" xml:space="preserve" y="256.89" zvalue="315">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1566.81,197.944) scale(1,1) translate(0,0)" writing-mode="lr" x="1566.81" xml:space="preserve" y="202.44" zvalue="317">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="319" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,579.759,949.34) scale(1,1) translate(0,0)" writing-mode="lr" x="579.76" xml:space="preserve" y="953.84" zvalue="331">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="320" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,547.396,865.791) scale(1,1) translate(0,0)" writing-mode="lr" x="547.4" xml:space="preserve" y="870.29" zvalue="334">0911c</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="317" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,482.396,865.022) scale(1,1) translate(0,0)" writing-mode="lr" x="482.4" xml:space="preserve" y="869.52" zvalue="338">0913c</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="353" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1278.37,954.724) scale(1,1) translate(0,0)" writing-mode="lr" x="1278.37" xml:space="preserve" y="959.22" zvalue="346">励磁变PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="350" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1075.92,956.077) scale(1,1) translate(3.5136e-13,0)" writing-mode="lr" x="1075.92" xml:space="preserve" y="960.58" zvalue="347">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="325" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1183.43,787.035) scale(1,1) translate(0,0)" writing-mode="lr" x="1183.43" xml:space="preserve" y="791.53" zvalue="348">052</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="324" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1168.52,722.054) scale(1,1) translate(0,0)" writing-mode="lr" x="1168.52" xml:space="preserve" y="726.55" zvalue="350">0521c</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="323" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1246.09,865.836) scale(1,1) translate(0,0)" writing-mode="lr" x="1246.09" xml:space="preserve" y="870.34" zvalue="352">0922c</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="351" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1141.07,956.263) scale(1,1) translate(0,0)" writing-mode="lr" x="1141.07" xml:space="preserve" y="960.76" zvalue="359">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="322" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1108.7,870.406) scale(1,1) translate(0,0)" writing-mode="lr" x="1108.7" xml:space="preserve" y="874.91" zvalue="361">0921c</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="357" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,935.077,856.923) scale(1,1) translate(0,0)" writing-mode="lr" x="935.08" xml:space="preserve" y="861.42" zvalue="369">至0561c隔离开关</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="376" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1623.95,753.817) scale(1,1) translate(0,0)" writing-mode="lr" x="1623.95" xml:space="preserve" y="758.3200000000001" zvalue="372">0551c</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="377" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1691.95,753.817) scale(1,1) translate(0,0)" writing-mode="lr" x="1691.95" xml:space="preserve" y="758.3200000000001" zvalue="374">0561c</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="375" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1688.5,540.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1688.5" xml:space="preserve" y="545.25" zvalue="376">大坝变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="374" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1797,853) scale(1,1) translate(0,0)" writing-mode="lr" x="1797" xml:space="preserve" y="857.5" zvalue="381">至1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" x="1207.5859375" xml:space="preserve" y="958.7369357854257" zvalue="386">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1207.5859375" xml:space="preserve" y="974.7369357854257" zvalue="386">15MW</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="126" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="415"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="417">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="418">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="419">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="420">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="421">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="423">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="424">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="425">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="426">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="427">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="428">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,241.625,334) scale(1,1) translate(0,0)" writing-mode="lr" x="241.63" xml:space="preserve" y="338.5" zvalue="429">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,359.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="363.75" zvalue="430">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="438">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="439">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="442">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="444">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="445">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="446">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="447">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="449">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,194.5,955) scale(1,1) translate(0,0)" writing-mode="lr" x="194.5" xml:space="preserve" y="961" zvalue="481">MengNai-01-2011</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="437"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="kv10" height="30" transform="rotate(0,647.891,910.811) scale(1.43307,1.43307) translate(-189.293,-268.747)" width="30" x="626.3945868895569" xlink:href="#Generator:发电机_0" y="889.3152867370809" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454776061954" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192454776061954"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,647.891,910.811) scale(1.43307,1.43307) translate(-189.293,-268.747)" width="30" x="626.3945868895569" y="889.3152867370809"/></g>
  <g id="349">
   <use class="kv10" height="30" transform="rotate(0,1209.2,915.427) scale(1.43307,1.43307) translate(-358.918,-270.141)" width="30" x="1187.702279197249" xlink:href="#Generator:发电机_0" y="893.9306713524654" zvalue="344"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454777634818" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192454777634818"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1209.2,915.427) scale(1.43307,1.43307) translate(-358.918,-270.141)" width="30" x="1187.702279197249" y="893.9306713524654"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="276">
   <use class="kv10" height="30" transform="rotate(0,716.297,910.811) scale(1.3853,1.3853) translate(-193.446,-247.547)" width="30" x="695.5175622031613" xlink:href="#Accessory:PT789_0" y="890.0318193095626" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454775996418" ObjectName="#1发电机励磁变PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,716.297,910.811) scale(1.3853,1.3853) translate(-193.446,-247.547)" width="30" x="695.5175622031613" y="890.0318193095626"/></g>
  <g id="253">
   <use class="kv10" height="29" transform="rotate(0,514.615,920.511) scale(1.48718,-1.49258) translate(-161.273,-1530.09)" width="30" x="492.3076923076925" xlink:href="#Accessory:PT12321_0" y="898.8690543701709" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454775930882" ObjectName="#1发电机励磁变PT1"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,514.615,920.511) scale(1.48718,-1.49258) translate(-161.273,-1530.09)" width="30" x="492.3076923076925" y="898.8690543701709"/></g>
  <g id="182">
   <use class="kv10" height="30" transform="rotate(0,535.241,583) scale(1.73333,-1.51667) translate(-215.448,-959.646)" width="30" x="509.2409284176603" xlink:href="#Accessory:带熔断器35kVPT11_0" y="560.25" zvalue="124"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454775537666" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,535.241,583) scale(1.73333,-1.51667) translate(-215.448,-959.646)" width="30" x="509.2409284176603" y="560.25"/></g>
  <g id="181">
   <use class="kv10" height="26" transform="rotate(0,558,587.75) scale(1,-1) translate(0,-1175.5)" width="12" x="552" xlink:href="#Accessory:避雷器1_0" y="574.75" zvalue="126"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454775472130" ObjectName="10kVⅠ段母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,558,587.75) scale(1,-1) translate(0,-1175.5)" width="12" x="552" y="574.75"/></g>
  <g id="332">
   <use class="kv10" height="30" transform="rotate(0,1456.24,583) scale(1.73333,-1.51667) translate(-605.102,-959.646)" width="30" x="1430.24092841766" xlink:href="#Accessory:带熔断器35kVPT11_0" y="560.25" zvalue="253"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454774423554" ObjectName="10kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1456.24,583) scale(1.73333,-1.51667) translate(-605.102,-959.646)" width="30" x="1430.24092841766" y="560.25"/></g>
  <g id="331">
   <use class="kv10" height="26" transform="rotate(0,1479,587.75) scale(1,-1) translate(0,-1175.5)" width="12" x="1473" xlink:href="#Accessory:避雷器1_0" y="574.75" zvalue="255"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454774358018" ObjectName="10kVⅡ段母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1479,587.75) scale(1,-1) translate(0,-1175.5)" width="12" x="1473" y="574.75"/></g>
  <g id="356">
   <use class="kv110" height="40" transform="rotate(180,551.35,121.125) scale(-1.04167,1.36998) translate(-1080.02,-25.3116)" width="30" x="535.7254464285717" xlink:href="#Accessory:2绕组线路PT_0" y="93.7254464285715" zvalue="281"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454773702658" ObjectName="110kV勐乃电站线线路PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,551.35,121.125) scale(-1.04167,1.36998) translate(-1080.02,-25.3116)" width="30" x="535.7254464285717" y="93.7254464285715"/></g>
  <g id="354">
   <use class="kv110" height="26" transform="rotate(180,524.537,124.776) scale(-1.08054,1.08054) translate(-1009.49,-8.25358)" width="12" x="518.0535673143577" xlink:href="#Accessory:避雷器1_0" y="110.7286682615629" zvalue="283"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454773637122" ObjectName="避雷器4"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,524.537,124.776) scale(-1.08054,1.08054) translate(-1009.49,-8.25358)" width="12" x="518.0535673143577" y="110.7286682615629"/></g>
  <g id="379">
   <use class="kv110" height="30" transform="rotate(0,973.937,177.25) scale(1.73333,-1.51667) translate(-401.05,-286.368)" width="30" x="947.9365147243685" xlink:href="#Accessory:带熔断器35kVPT11_0" y="154.5000000000001" zvalue="289"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454773374978" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,973.937,177.25) scale(1.73333,-1.51667) translate(-401.05,-286.368)" width="30" x="947.9365147243685" y="154.5000000000001"/></g>
  <g id="378">
   <use class="kv110" height="26" transform="rotate(0,1021.57,181.429) scale(1,-1) translate(0,-362.857)" width="12" x="1015.571428571429" xlink:href="#Accessory:避雷器1_0" y="168.4285714285715" zvalue="291"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454773309442" ObjectName="110kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1021.57,181.429) scale(1,-1) translate(0,-362.857)" width="12" x="1015.571428571429" y="168.4285714285715"/></g>
  <g id="273">
   <use class="kv110" height="40" transform="rotate(180,1432.1,138.875) scale(-1.04167,1.36998) translate(-2806.29,-30.1052)" width="30" x="1416.475446428572" xlink:href="#Accessory:2绕组线路PT_0" y="111.4754464285715" zvalue="321"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454776193026" ObjectName="备用线线路PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(180,1432.1,138.875) scale(-1.04167,1.36998) translate(-2806.29,-30.1052)" width="30" x="1416.475446428572" y="111.4754464285715"/></g>
  <g id="271">
   <use class="kv110" height="26" transform="rotate(180,1405.54,149.026) scale(-1.08054,1.08054) translate(-2705.82,-10.0611)" width="12" x="1399.053567314358" xlink:href="#Accessory:避雷器1_0" y="134.9786682615629" zvalue="323"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454776127490" ObjectName="避雷器5"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1405.54,149.026) scale(-1.08054,1.08054) translate(-2705.82,-10.0611)" width="12" x="1399.053567314358" y="134.9786682615629"/></g>
  <g id="298">
   <use class="kv10" height="30" transform="rotate(0,579.759,908.504) scale(1.3853,1.3853) translate(-155.47,-246.905)" width="30" x="558.9791006646999" xlink:href="#Accessory:PT789_0" y="887.7241270018703" zvalue="330"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454776848386" ObjectName="#1发电机互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,579.759,908.504) scale(1.3853,1.3853) translate(-155.47,-246.905)" width="30" x="558.9791006646999" y="887.7241270018703"/></g>
  <g id="313">
   <use class="kv10" height="26" transform="rotate(90,691,818.519) scale(-1,-1) translate(-1382,-1637.04)" width="12" x="684.9999999999999" xlink:href="#Accessory:避雷器1_0" y="805.5192307692307" zvalue="341"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454777044994" ObjectName="#1发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,691,818.519) scale(-1,-1) translate(-1382,-1637.04)" width="12" x="684.9999999999999" y="805.5192307692307"/></g>
  <g id="348">
   <use class="kv10" height="30" transform="rotate(0,1277.6,915.427) scale(1.3853,1.3853) translate(-349.564,-248.831)" width="30" x="1256.825254510853" xlink:href="#Accessory:PT789_0" y="894.6472039249472" zvalue="345"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454777569282" ObjectName="#2发电机励磁变PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1277.6,915.427) scale(1.3853,1.3853) translate(-349.564,-248.831)" width="30" x="1256.825254510853" y="894.6472039249472"/></g>
  <g id="347">
   <use class="kv10" height="29" transform="rotate(0,1075.92,925.127) scale(1.48718,-1.49258) translate(-345.15,-1537.8)" width="30" x="1053.615384615385" xlink:href="#Accessory:PT12321_0" y="903.4844389855554" zvalue="346"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454777503746" ObjectName="#2发电机励磁变PT1"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1075.92,925.127) scale(1.48718,-1.49258) translate(-345.15,-1537.8)" width="30" x="1053.615384615385" y="903.4844389855554"/></g>
  <g id="336">
   <use class="kv10" height="30" transform="rotate(0,1141.07,913.119) scale(1.3853,1.3853) translate(-311.588,-248.189)" width="30" x="1120.286792972392" xlink:href="#Accessory:PT789_0" y="892.3395116172549" zvalue="358"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454777307138" ObjectName="#2发电机互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1141.07,913.119) scale(1.3853,1.3853) translate(-311.588,-248.189)" width="30" x="1120.286792972392" y="892.3395116172549"/></g>
  <g id="327">
   <use class="kv10" height="26" transform="rotate(90,1252.31,823.135) scale(-1,-1) translate(-2504.62,-1646.27)" width="12" x="1246.307692307692" xlink:href="#Accessory:避雷器1_0" y="810.1346153846152" zvalue="366"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454777110530" ObjectName="#2发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1252.31,823.135) scale(-1,-1) translate(-2504.62,-1646.27)" width="12" x="1246.307692307692" y="810.1346153846152"/></g>
  <g id="355">
   <use class="kv10" height="13" transform="rotate(270,945.654,832.923) scale(0.769231,4.52663) translate(282.427,-625.995)" width="11" x="941.4230769230769" xlink:href="#Accessory:空挂线路_0" y="803.5000000000001" zvalue="368"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454777700354" ObjectName="至0561c隔离开关"/>
   </metadata>
  <rect fill="white" height="13" opacity="0" stroke="white" transform="rotate(270,945.654,832.923) scale(0.769231,4.52663) translate(282.427,-625.995)" width="11" x="941.4230769230769" y="803.5000000000001"/></g>
  <g id="373">
   <use class="kv10" height="13" transform="rotate(270,1806,828.5) scale(1,4.23077) translate(0,-611.673)" width="11" x="1800.5" xlink:href="#Accessory:空挂线路_0" y="801" zvalue="380"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454777962498" ObjectName="至1号站用变"/>
   </metadata>
  <rect fill="white" height="13" opacity="0" stroke="white" transform="rotate(270,1806,828.5) scale(1,4.23077) translate(0,-611.673)" width="11" x="1800.5" y="801"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="255">
   <path class="kv10" d="M 492.5 688.69 L 950 688.69" stroke-width="6" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674420981763" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674420981763"/></metadata>
  <path d="M 492.5 688.69 L 950 688.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv10" d="M 1095 691.5 L 1574.29 691.5" stroke-width="6" zvalue="128"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674420916227" ObjectName="10kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674420916227"/></metadata>
  <path d="M 1095 691.5 L 1574.29 691.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv110" d="M 495 354.5 L 1607.5 354.5" stroke-width="6" zvalue="186"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674420850691" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674420850691"/></metadata>
  <path d="M 495 354.5 L 1607.5 354.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="249">
   <use class="kv10" height="20" transform="rotate(0,647.784,783.375) scale(1.59229,1.43307) translate(-237.998,-232.401)" width="10" x="639.8229380627333" xlink:href="#Breaker:开关_0" y="769.0439675338483" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925214601219" ObjectName="#1发电机051断路器"/>
   <cge:TPSR_Ref TObjectID="6473925214601219"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,647.784,783.375) scale(1.59229,1.43307) translate(-237.998,-232.401)" width="10" x="639.8229380627333" y="769.0439675338483"/></g>
  <g id="195">
   <use class="kv10" height="20" transform="rotate(0,859.034,800.875) scale(1.59229,1.43307) translate(-316.578,-237.69)" width="10" x="851.0729380627333" xlink:href="#Breaker:开关_0" y="786.5439675338483" zvalue="106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925214535683" ObjectName="#1站用变053断路器"/>
   <cge:TPSR_Ref TObjectID="6473925214535683"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,859.034,800.875) scale(1.59229,1.43307) translate(-316.578,-237.69)" width="10" x="851.0729380627333" y="786.5439675338483"/></g>
  <g id="247">
   <use class="kv10" height="20" transform="rotate(0,1493.53,812.125) scale(1.59229,1.43307) translate(-552.597,-241.089)" width="10" x="1485.572938062733" xlink:href="#Breaker:开关_0" y="797.7939675338483" zvalue="174"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925214470147" ObjectName="#2站用变054断路器"/>
   <cge:TPSR_Ref TObjectID="6473925214470147"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1493.53,812.125) scale(1.59229,1.43307) translate(-552.597,-241.089)" width="10" x="1485.572938062733" y="797.7939675338483"/></g>
  <g id="267">
   <use class="kv110" height="20" transform="rotate(0,817.083,454.972) scale(1.22222,1.11111) translate(-147.449,-44.3861)" width="10" x="810.9722324079938" xlink:href="#Breaker:开关_0" y="443.8611111111111" zvalue="190"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925214404611" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925214404611"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,817.083,454.972) scale(1.22222,1.11111) translate(-147.449,-44.3861)" width="10" x="810.9722324079938" y="443.8611111111111"/></g>
  <g id="314">
   <use class="kv110" height="20" transform="rotate(0,1306.08,454.972) scale(1.22222,1.11111) translate(-236.359,-44.3861)" width="10" x="1299.972232407994" xlink:href="#Breaker:开关_0" y="443.8611111111111" zvalue="219"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925214339075" ObjectName="#2主变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473925214339075"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1306.08,454.972) scale(1.22222,1.11111) translate(-236.359,-44.3861)" width="10" x="1299.972232407994" y="443.8611111111111"/></g>
  <g id="370">
   <use class="kv110" height="20" transform="rotate(0,629.5,265.917) scale(1.22222,1.11111) translate(-113.343,-25.4806)" width="10" x="623.3888888888889" xlink:href="#Breaker:开关_0" y="254.8055553436279" zvalue="260"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925214273539" ObjectName="110kV勐乃电站线151断路器"/>
   <cge:TPSR_Ref TObjectID="6473925214273539"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,629.5,265.917) scale(1.22222,1.11111) translate(-113.343,-25.4806)" width="10" x="623.3888888888889" y="254.8055553436279"/></g>
  <g id="293">
   <use class="kv110" height="20" transform="rotate(0,1517.75,267.167) scale(1.22222,1.11111) translate(-274.843,-25.6056)" width="10" x="1511.638888888889" xlink:href="#Breaker:开关_0" y="256.0555553436279" zvalue="300"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925214666755" ObjectName="备用线152断路器"/>
   <cge:TPSR_Ref TObjectID="6473925214666755"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1517.75,267.167) scale(1.22222,1.11111) translate(-274.843,-25.6056)" width="10" x="1511.638888888889" y="256.0555553436279"/></g>
  <g id="346">
   <use class="kv10" height="20" transform="rotate(0,1209.09,787.99) scale(1.59229,1.43307) translate(-446.791,-233.796)" width="10" x="1201.130630370425" xlink:href="#Breaker:开关_0" y="773.659352149233" zvalue="347"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925214732291" ObjectName="#2发电机052断路器"/>
   <cge:TPSR_Ref TObjectID="6473925214732291"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1209.09,787.99) scale(1.59229,1.43307) translate(-446.791,-233.796)" width="10" x="1201.130630370425" y="773.659352149233"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="246">
   <use class="kv10" height="25" transform="rotate(0,647.666,715.317) scale(0.353843,0.934146) translate(1168.17,49.604)" width="45" x="639.7049337208474" xlink:href="#Disconnector:特殊刀闸_0" y="703.6402229389662" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454775865346" ObjectName="#1发电机0511c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454775865346"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,647.666,715.317) scale(0.353843,0.934146) translate(1168.17,49.604)" width="45" x="639.7049337208474" y="703.6402229389662"/></g>
  <g id="242">
   <use class="kv10" height="25" transform="rotate(0,717.357,861.406) scale(0.353843,0.934146) translate(1295.43,59.9028)" width="45" x="709.3955620613175" xlink:href="#Disconnector:特殊刀闸_0" y="849.7296554901827" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454775799810" ObjectName="#1发电机励磁变0912c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454775799810"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,717.357,861.406) scale(0.353843,0.934146) translate(1295.43,59.9028)" width="45" x="709.3955620613175" y="849.7296554901827"/></g>
  <g id="192">
   <use class="kv10" height="25" transform="rotate(0,860.416,726.567) scale(0.353843,0.934146) translate(1556.68,50.3971)" width="45" x="852.4549337208474" xlink:href="#Disconnector:特殊刀闸_0" y="714.8902229389662" zvalue="110"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454775668738" ObjectName="#1站用变0531c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454775668738"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,860.416,726.567) scale(0.353843,0.934146) translate(1556.68,50.3971)" width="45" x="852.4549337208474" y="714.8902229389662"/></g>
  <g id="188">
   <use class="kv10" height="25" transform="rotate(0,535.222,644.417) scale(-0.37037,-0.977778) translate(-1994.49,-1303.76)" width="45" x="526.8888888888888" xlink:href="#Disconnector:特殊刀闸_0" y="632.1944581137762" zvalue="116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454775603202" ObjectName="10kVⅠ段母线电压互感器0901c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454775603202"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,535.222,644.417) scale(-0.37037,-0.977778) translate(-1994.49,-1303.76)" width="45" x="526.8888888888888" y="632.1944581137762"/></g>
  <g id="244">
   <use class="kv10" height="25" transform="rotate(0,1494.92,737.817) scale(0.353843,0.934146) translate(2715.34,51.1902)" width="45" x="1486.954933720847" xlink:href="#Disconnector:特殊刀闸_0" y="726.1402229389662" zvalue="178"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454775341058" ObjectName="#2站用变0541c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454775341058"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1494.92,737.817) scale(0.353843,0.934146) translate(2715.34,51.1902)" width="45" x="1486.954933720847" y="726.1402229389662"/></g>
  <g id="268">
   <use class="kv110" height="30" transform="rotate(0,817.083,398.972) scale(1.11111,0.814815) translate(-80.875,87.8977)" width="15" x="808.7500101725263" xlink:href="#Disconnector:刀闸_0" y="386.75" zvalue="188"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454775275522" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454775275522"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,817.083,398.972) scale(1.11111,0.814815) translate(-80.875,87.8977)" width="15" x="808.7500101725263" y="386.75"/></g>
  <g id="281">
   <use class="kv10" height="25" transform="rotate(0,815.09,637.75) scale(0.37037,0.977778) translate(1371.49,14.2165)" width="45" x="806.756491510451" xlink:href="#Disconnector:特殊刀闸_0" y="625.5277779669989" zvalue="205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454774947842" ObjectName="#1主变10kV侧0011c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454774947842"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,815.09,637.75) scale(0.37037,0.977778) translate(1371.49,14.2165)" width="45" x="806.756491510451" y="625.5277779669989"/></g>
  <g id="315">
   <use class="kv110" height="30" transform="rotate(0,1306.08,398.972) scale(1.11111,0.814815) translate(-129.775,87.8977)" width="15" x="1297.750010172526" xlink:href="#Disconnector:刀闸_0" y="386.75" zvalue="217"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454774882306" ObjectName="#2主变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454774882306"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1306.08,398.972) scale(1.11111,0.814815) translate(-129.775,87.8977)" width="15" x="1297.750010172526" y="386.75"/></g>
  <g id="303">
   <use class="kv10" height="25" transform="rotate(0,1304.22,637.75) scale(0.37037,0.977778) translate(2203,14.2165)" width="45" x="1295.884615384615" xlink:href="#Disconnector:特殊刀闸_0" y="625.5277779669988" zvalue="234"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454774554626" ObjectName="#2主变10kV侧0021c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454774554626"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1304.22,637.75) scale(0.37037,0.977778) translate(2203,14.2165)" width="45" x="1295.884615384615" y="625.5277779669988"/></g>
  <g id="338">
   <use class="kv10" height="25" transform="rotate(0,1456.22,637.75) scale(-0.37037,-0.977778) translate(-5402.19,-1290.27)" width="45" x="1447.888888888889" xlink:href="#Disconnector:特殊刀闸_0" y="625.5277777777778" zvalue="246"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454774489090" ObjectName="10kVⅡ段母线电压互感器0902c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454774489090"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1456.22,637.75) scale(-0.37037,-0.977778) translate(-5402.19,-1290.27)" width="45" x="1447.888888888889" y="625.5277777777778"/></g>
  <g id="369">
   <use class="kv110" height="30" transform="rotate(0,629.5,313.472) scale(-1.11111,-0.814815) translate(-1195.22,-700.966)" width="15" x="621.1666666666666" xlink:href="#Disconnector:刀闸_0" y="301.2500133514404" zvalue="261"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454774292482" ObjectName="110kV勐乃电站线1511隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454774292482"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,629.5,313.472) scale(-1.11111,-0.814815) translate(-1195.22,-700.966)" width="15" x="621.1666666666666" y="301.2500133514404"/></g>
  <g id="368">
   <use class="kv110" height="30" transform="rotate(0,629.5,205.472) scale(-1.11111,-0.814815) translate(-1195.22,-460.42)" width="15" x="621.1666666931577" xlink:href="#Disconnector:刀闸_0" y="193.2499997880724" zvalue="264"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454774226946" ObjectName="110kV勐乃电站线1516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454774226946"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,629.5,205.472) scale(-1.11111,-0.814815) translate(-1195.22,-460.42)" width="15" x="621.1666666931577" y="193.2499997880724"/></g>
  <g id="381">
   <use class="kv110" height="30" transform="rotate(0,974.222,275.524) scale(-1.11111,-0.814815) translate(-1850.19,-616.444)" width="15" x="965.8888956705728" xlink:href="#Disconnector:刀闸_0" y="263.3016009709192" zvalue="285"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454773571586" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454773571586"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,974.222,275.524) scale(-1.11111,-0.814815) translate(-1850.19,-616.444)" width="15" x="965.8888956705728" y="263.3016009709192"/></g>
  <g id="292">
   <use class="kv110" height="30" transform="rotate(0,1517.75,314.722) scale(-1.11111,-0.814815) translate(-2882.89,-703.75)" width="15" x="1509.416666666667" xlink:href="#Disconnector:刀闸_0" y="302.5000133514404" zvalue="301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454776782850" ObjectName="备用线1521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454776782850"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1517.75,314.722) scale(-1.11111,-0.814815) translate(-2882.89,-703.75)" width="15" x="1509.416666666667" y="302.5000133514404"/></g>
  <g id="291">
   <use class="kv110" height="30" transform="rotate(0,1517.75,206.722) scale(-1.11111,-0.814815) translate(-2882.89,-463.205)" width="15" x="1509.416666693158" xlink:href="#Disconnector:刀闸_0" y="194.4999997880724" zvalue="304"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454776717314" ObjectName="备用线1526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454776717314"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1517.75,206.722) scale(-1.11111,-0.814815) translate(-2882.89,-463.205)" width="15" x="1509.416666693158" y="194.4999997880724"/></g>
  <g id="301">
   <use class="kv10" height="25" transform="rotate(0,580.049,864.483) scale(0.353843,0.934146) translate(1044.7,60.1197)" width="45" x="572.0878697536251" xlink:href="#Disconnector:特殊刀闸_0" y="852.8065785671058" zvalue="333"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454776913922" ObjectName="#1发电站互感器0911c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454776913922"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,580.049,864.483) scale(0.353843,0.934146) translate(1044.7,60.1197)" width="45" x="572.0878697536251" y="852.8065785671058"/></g>
  <g id="308">
   <use class="kv10" height="25" transform="rotate(0,514.28,864.483) scale(0.353843,0.934146) translate(924.594,60.1197)" width="45" x="506.3186389843944" xlink:href="#Disconnector:特殊刀闸_0" y="852.8065785671058" zvalue="337"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454776979458" ObjectName="励磁变0913c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454776979458"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,514.28,864.483) scale(0.353843,0.934146) translate(924.594,60.1197)" width="45" x="506.3186389843944" y="852.8065785671058"/></g>
  <g id="345">
   <use class="kv10" height="25" transform="rotate(0,1208.97,719.932) scale(0.353843,0.934146) translate(2193.18,49.9294)" width="45" x="1201.01262602854" xlink:href="#Disconnector:特殊刀闸_0" y="708.2556075543508" zvalue="349"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454777438210" ObjectName="#2发电机0521c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454777438210"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1208.97,719.932) scale(0.353843,0.934146) translate(2193.18,49.9294)" width="45" x="1201.01262602854" y="708.2556075543508"/></g>
  <g id="344">
   <use class="kv10" height="25" transform="rotate(0,1278.66,866.022) scale(0.353843,0.934146) translate(2320.44,60.2281)" width="45" x="1270.70325436901" xlink:href="#Disconnector:特殊刀闸_0" y="854.3450401055675" zvalue="351"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454777372674" ObjectName="#2发电机励磁变0922c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454777372674"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1278.66,866.022) scale(0.353843,0.934146) translate(2320.44,60.2281)" width="45" x="1270.70325436901" y="854.3450401055675"/></g>
  <g id="335">
   <use class="kv10" height="25" transform="rotate(0,1141.36,869.099) scale(0.353843,0.934146) translate(2069.71,60.4451)" width="45" x="1133.395562061317" xlink:href="#Disconnector:特殊刀闸_0" y="857.4219631824903" zvalue="359"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454777241602" ObjectName="#2发电站互感器0921c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454777241602"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1141.36,869.099) scale(0.353843,0.934146) translate(2069.71,60.4451)" width="45" x="1133.395562061317" y="857.4219631824903"/></g>
  <g id="330">
   <use class="kv10" height="25" transform="rotate(0,1075.59,869.099) scale(0.353843,0.934146) translate(1949.6,60.4451)" width="45" x="1067.626331292087" xlink:href="#Disconnector:特殊刀闸_0" y="857.4219631824906" zvalue="363"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454777176066" ObjectName="励磁变0923c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454777176066"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1075.59,869.099) scale(0.353843,0.934146) translate(1949.6,60.4451)" width="45" x="1067.626331292087" y="857.4219631824906"/></g>
  <g id="359">
   <use class="kv10" height="25" transform="rotate(0,1657.92,754.817) scale(0.353843,0.934146) translate(3013,52.3886)" width="45" x="1649.954933720848" xlink:href="#Disconnector:特殊刀闸_0" y="743.1402229389662" zvalue="371"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454777765890" ObjectName="大坝变0551c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454777765890"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1657.92,754.817) scale(0.353843,0.934146) translate(3013,52.3886)" width="45" x="1649.954933720848" y="743.1402229389662"/></g>
  <g id="363">
   <use class="kv10" height="25" transform="rotate(0,1720.92,754.817) scale(0.353843,0.934146) translate(3128.04,52.3886)" width="45" x="1712.954933720848" xlink:href="#Disconnector:特殊刀闸_0" y="743.1402229389662" zvalue="373"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454777831426" ObjectName="大坝变0561c隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454777831426"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1720.92,754.817) scale(0.353843,0.934146) translate(3128.04,52.3886)" width="45" x="1712.954933720848" y="743.1402229389662"/></g>
  <g id="384">
   <use class="kv10" height="27" transform="rotate(0,1695,658.5) scale(2.41071,2.31481) translate(-982.014,-356.278)" width="14" x="1678.125" xlink:href="#Disconnector:带融断手车刀闸_0" y="627.25" zvalue="382"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454778028034" ObjectName="大坝变隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454778028034"/></metadata>
  <rect fill="white" height="27" opacity="0" stroke="white" transform="rotate(0,1695,658.5) scale(2.41071,2.31481) translate(-982.014,-356.278)" width="14" x="1678.125" y="627.25"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="241">
   <path class="kv10" d="M 647.73 769.66 L 647.7 726.57" stroke-width="1" zvalue="63"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@0" LinkObjectIDznd="246@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 647.73 769.66 L 647.7 726.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv10" d="M 647.89 797.06 L 647.89 889.67" stroke-width="1" zvalue="64"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@1" LinkObjectIDznd="850@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 647.89 797.06 L 647.89 889.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="239">
   <path class="kv10" d="M 716.23 890.52 L 716.23 872.66" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="242@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 716.23 890.52 L 716.23 872.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="219">
   <path class="kv10" d="M 717.37 850.43 L 717.37 833.11 L 647.89 833.11" stroke-width="1" zvalue="73"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="242@0" LinkObjectIDznd="240" MaxPinNum="2"/>
   </metadata>
  <path d="M 717.37 850.43 L 717.37 833.11 L 647.89 833.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv10" d="M 859.14 814.56 L 859.14 850.89" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@1" LinkObjectIDznd="205@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 859.14 814.56 L 859.14 850.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="kv10" d="M 860.45 688.69 L 860.45 715.28" stroke-width="1" zvalue="114"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="255@3" LinkObjectIDznd="192@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 860.45 688.69 L 860.45 715.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv10" d="M 860.45 737.82 L 860.45 787.16" stroke-width="1" zvalue="115"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@1" LinkObjectIDznd="195@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 860.45 737.82 L 860.45 787.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv10" d="M 535.19 656.23 L 535.19 688.69" stroke-width="1" zvalue="120"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@0" LinkObjectIDznd="255@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 535.19 656.23 L 535.19 688.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv10" d="M 535.19 632.64 L 535.15 604.08" stroke-width="1" zvalue="121"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@1" LinkObjectIDznd="182@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 535.19 632.64 L 535.15 604.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv10" d="M 558.03 600.12 L 558.03 608.75 L 535.16 608.75" stroke-width="1" zvalue="127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@0" LinkObjectIDznd="185" MaxPinNum="2"/>
   </metadata>
  <path d="M 558.03 600.12 L 558.03 608.75 L 535.16 608.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv10" d="M 1493.64 825.81 L 1493.64 862.14" stroke-width="1" zvalue="176"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="247@1" LinkObjectIDznd="248@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1493.64 825.81 L 1493.64 862.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv10" d="M 1494.95 691.5 L 1494.95 726.53" stroke-width="1" zvalue="182"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@1" LinkObjectIDznd="244@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1494.95 691.5 L 1494.95 726.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv10" d="M 1494.95 749.07 L 1494.95 798.41" stroke-width="1" zvalue="183"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="244@1" LinkObjectIDznd="247@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1494.95 749.07 L 1494.95 798.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="kv110" d="M 817.15 410.99 L 817.15 444.34" stroke-width="1" zvalue="192"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@1" LinkObjectIDznd="267@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 817.15 410.99 L 817.15 444.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv110" d="M 817.18 387.15 L 817.18 354.5" stroke-width="1" zvalue="195"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="251@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 817.18 387.15 L 817.18 354.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv110" d="M 817.16 465.58 L 817.16 483.59" stroke-width="1" zvalue="198"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@1" LinkObjectIDznd="263@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 817.16 465.58 L 817.16 483.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv110" d="M 849.36 418.81 L 817.15 418.81" stroke-width="1" zvalue="199"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265@0" LinkObjectIDznd="154" MaxPinNum="2"/>
   </metadata>
  <path d="M 849.36 418.81 L 817.15 418.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv110" d="M 815.29 509.87 L 725.45 509.87 L 725.45 533.9" stroke-width="1" zvalue="200"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@2" LinkObjectIDznd="259@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 815.29 509.87 L 725.45 509.87 L 725.45 533.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv10" d="M 815.12 649.52 L 815.12 688.69" stroke-width="1" zvalue="215"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="281@1" LinkObjectIDznd="255@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 815.12 649.52 L 815.12 688.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="kv110" d="M 1306.15 410.99 L 1306.15 444.34" stroke-width="1" zvalue="221"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="315@1" LinkObjectIDznd="314@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1306.15 410.99 L 1306.15 444.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv110" d="M 1306.18 387.15 L 1306.18 354.5" stroke-width="1" zvalue="224"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="315@0" LinkObjectIDznd="251@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1306.18 387.15 L 1306.18 354.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="kv110" d="M 1306.16 465.58 L 1306.16 483.59" stroke-width="1" zvalue="227"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="314@1" LinkObjectIDznd="310@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1306.16 465.58 L 1306.16 483.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="kv110" d="M 1338.36 418.81 L 1306.15 418.81" stroke-width="1" zvalue="228"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="312@0" LinkObjectIDznd="143" MaxPinNum="2"/>
   </metadata>
  <path d="M 1338.36 418.81 L 1306.15 418.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv110" d="M 1304.29 509.87 L 1214.45 509.87 L 1214.45 533.9" stroke-width="1" zvalue="229"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="310@2" LinkObjectIDznd="306@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1304.29 509.87 L 1214.45 509.87 L 1214.45 533.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="kv10" d="M 1304.25 649.52 L 1304.25 691.5" stroke-width="1" zvalue="244"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="303@1" LinkObjectIDznd="179@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1304.25 649.52 L 1304.25 691.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="132">
   <path class="kv10" d="M 1456.19 649.56 L 1456.19 691.5" stroke-width="1" zvalue="250"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="338@0" LinkObjectIDznd="179@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1456.19 649.56 L 1456.19 691.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="kv10" d="M 1456.19 625.98 L 1456.15 604.08" stroke-width="1" zvalue="251"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="338@1" LinkObjectIDznd="332@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1456.19 625.98 L 1456.15 604.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv10" d="M 1479.03 600.12 L 1479.03 608.75 L 1456.16 608.75" stroke-width="1" zvalue="256"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="331@0" LinkObjectIDznd="131" MaxPinNum="2"/>
   </metadata>
  <path d="M 1479.03 600.12 L 1479.03 608.75 L 1456.16 608.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv10" d="M 647.7 704.03 L 647.7 688.69" stroke-width="1" zvalue="257"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@0" LinkObjectIDznd="255@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 647.7 704.03 L 647.7 688.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv110" d="M 629.4 325.29 L 629.4 354.5" stroke-width="1" zvalue="265"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="369@0" LinkObjectIDznd="251@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 629.4 325.29 L 629.4 354.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv110" d="M 629.43 301.46 L 629.43 276.53" stroke-width="1" zvalue="267"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="369@1" LinkObjectIDznd="370@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 629.43 301.46 L 629.43 276.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv110" d="M 629.46 255.29 L 629.4 217.29" stroke-width="1" zvalue="268"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="370@0" LinkObjectIDznd="368@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 629.46 255.29 L 629.4 217.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv110" d="M 629.43 193.46 L 629.43 153.03" stroke-width="1" zvalue="271"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="368@1" LinkObjectIDznd="364@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 629.43 193.46 L 629.43 153.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv110" d="M 666.28 182.19 L 629.43 182.19" stroke-width="1" zvalue="278"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="360@0" LinkObjectIDznd="122" MaxPinNum="2"/>
   </metadata>
  <path d="M 666.28 182.19 L 629.43 182.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv110" d="M 666.28 236.64 L 629.43 236.64" stroke-width="1" zvalue="279"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="361@0" LinkObjectIDznd="123" MaxPinNum="2"/>
   </metadata>
  <path d="M 666.28 236.64 L 629.43 236.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv110" d="M 666.28 285.42 L 629.43 285.42" stroke-width="1" zvalue="280"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="362@0" LinkObjectIDznd="124" MaxPinNum="2"/>
   </metadata>
  <path d="M 666.28 285.42 L 629.43 285.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="kv110" d="M 973.85 198.33 L 973.85 263.51" stroke-width="1" zvalue="293"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="379@0" LinkObjectIDznd="381@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 973.85 198.33 L 973.85 263.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="114">
   <path class="kv110" d="M 1031.07 317.81 L 974.12 317.81" stroke-width="1" zvalue="296"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="380@0" LinkObjectIDznd="383" MaxPinNum="2"/>
   </metadata>
  <path d="M 1031.07 317.81 L 974.12 317.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv110" d="M 1028.07 241.24 L 973.85 241.24" stroke-width="1" zvalue="297"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="382@0" LinkObjectIDznd="116" MaxPinNum="2"/>
   </metadata>
  <path d="M 1028.07 241.24 L 973.85 241.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv110" d="M 1021.6 193.8 L 1021.6 206.29 L 973.85 206.29" stroke-width="1" zvalue="298"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="378@0" LinkObjectIDznd="116" MaxPinNum="2"/>
   </metadata>
  <path d="M 1021.6 193.8 L 1021.6 206.29 L 973.85 206.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="290">
   <path class="kv110" d="M 1517.65 326.54 L 1517.65 354.5" stroke-width="1" zvalue="305"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="292@0" LinkObjectIDznd="251@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517.65 326.54 L 1517.65 354.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="289">
   <path class="kv110" d="M 1517.68 302.71 L 1517.68 277.78" stroke-width="1" zvalue="307"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="292@1" LinkObjectIDznd="293@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517.68 302.71 L 1517.68 277.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="288">
   <path class="kv110" d="M 1517.71 256.54 L 1517.65 218.54" stroke-width="1" zvalue="308"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="293@0" LinkObjectIDznd="291@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517.71 256.54 L 1517.65 218.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="286">
   <path class="kv110" d="M 1517.68 194.71 L 1517.68 152.28" stroke-width="1" zvalue="311"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="291@1" LinkObjectIDznd="287@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517.68 194.71 L 1517.68 152.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="kv110" d="M 1554.53 183.44 L 1517.68 183.44" stroke-width="1" zvalue="318"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="283@0" LinkObjectIDznd="286" MaxPinNum="2"/>
   </metadata>
  <path d="M 1554.53 183.44 L 1517.68 183.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="277">
   <path class="kv110" d="M 1554.53 237.89 L 1517.68 237.89" stroke-width="1" zvalue="319"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="284@0" LinkObjectIDznd="288" MaxPinNum="2"/>
   </metadata>
  <path d="M 1554.53 237.89 L 1517.68 237.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="274">
   <path class="kv110" d="M 1554.53 286.67 L 1517.68 286.67" stroke-width="1" zvalue="320"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="285@0" LinkObjectIDznd="289" MaxPinNum="2"/>
   </metadata>
  <path d="M 1554.53 286.67 L 1517.68 286.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="295">
   <path class="kv10" d="M 815.25 574.77 L 815.25 626.26" stroke-width="1" zvalue="326"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@1" LinkObjectIDznd="281@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 815.25 574.77 L 815.25 626.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="297">
   <path class="kv10" d="M 1304.25 574.77 L 1304.24 626.26" stroke-width="1" zvalue="328"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="310@1" LinkObjectIDznd="303@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1304.25 574.77 L 1304.24 626.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="305">
   <path class="kv10" d="M 579.69 888.21 L 579.69 875.73" stroke-width="1" zvalue="334"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="298@0" LinkObjectIDznd="301@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 579.69 888.21 L 579.69 875.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="307">
   <path class="kv10" d="M 580.08 853.2 L 580.08 833.88" stroke-width="1" zvalue="335"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="301@0" LinkObjectIDznd="311" MaxPinNum="2"/>
   </metadata>
  <path d="M 580.08 853.2 L 580.08 833.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="309">
   <path class="kv10" d="M 514.62 899.24 L 514.62 875.73" stroke-width="1" zvalue="338"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@0" LinkObjectIDznd="308@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 514.62 899.24 L 514.62 875.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv10" d="M 514.31 853.2 L 514.31 833.11 L 648.66 833.11" stroke-width="1" zvalue="339"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="308@0" LinkObjectIDznd="219" MaxPinNum="2"/>
   </metadata>
  <path d="M 514.31 853.2 L 514.31 833.11 L 648.66 833.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="316">
   <path class="kv10" d="M 678.63 818.49 L 647.89 818.49" stroke-width="1" zvalue="342"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="313@0" LinkObjectIDznd="240" MaxPinNum="2"/>
   </metadata>
  <path d="M 678.63 818.49 L 647.89 818.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="343">
   <path class="kv10" d="M 1209.04 774.28 L 1209 731.18" stroke-width="1" zvalue="353"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="346@0" LinkObjectIDznd="345@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1209.04 774.28 L 1209 731.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="342">
   <path class="kv10" d="M 1209.2 801.68 L 1209.2 894.29" stroke-width="1" zvalue="354"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="346@1" LinkObjectIDznd="349@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1209.2 801.68 L 1209.2 894.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="341">
   <path class="kv10" d="M 1277.54 895.13 L 1277.54 877.27" stroke-width="1" zvalue="355"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="348@0" LinkObjectIDznd="344@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1277.54 895.13 L 1277.54 877.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="340">
   <path class="kv10" d="M 1278.7 854.73 L 1278.7 837.72 L 1209.2 837.72" stroke-width="1" zvalue="356"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="344@0" LinkObjectIDznd="342" MaxPinNum="2"/>
   </metadata>
  <path d="M 1278.7 854.73 L 1278.7 837.72 L 1209.2 837.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="339">
   <path class="kv10" d="M 1209 708.64 L 1209 691.5" stroke-width="1" zvalue="357"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="345@0" LinkObjectIDznd="179@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1209 708.64 L 1209 691.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="334">
   <path class="kv10" d="M 1141 892.82 L 1141 880.35" stroke-width="1" zvalue="360"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="336@0" LinkObjectIDznd="335@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1141 892.82 L 1141 880.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="333">
   <path class="kv10" d="M 1141.39 857.81 L 1141.39 837.72" stroke-width="1" zvalue="362"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="335@0" LinkObjectIDznd="328" MaxPinNum="2"/>
   </metadata>
  <path d="M 1141.39 857.81 L 1141.39 837.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="329">
   <path class="kv10" d="M 1075.92 903.86 L 1075.92 880.35" stroke-width="1" zvalue="364"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="347@0" LinkObjectIDznd="330@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1075.92 903.86 L 1075.92 880.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="328">
   <path class="kv10" d="M 1075.62 857.81 L 1075.62 837.72 L 1209.2 837.72" stroke-width="1" zvalue="365"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="330@0" LinkObjectIDznd="340" MaxPinNum="2"/>
   </metadata>
  <path d="M 1075.62 857.81 L 1075.62 837.72 L 1209.2 837.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="326">
   <path class="kv10" d="M 1239.94 823.1 L 1209.2 823.1" stroke-width="1" zvalue="367"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="327@0" LinkObjectIDznd="342" MaxPinNum="2"/>
   </metadata>
  <path d="M 1239.94 823.1 L 1209.2 823.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="358">
   <path class="kv10" d="M 915.85 832.92 L 859.14 832.92" stroke-width="1" zvalue="369"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="355@0" LinkObjectIDznd="194" MaxPinNum="2"/>
   </metadata>
  <path d="M 915.85 832.92 L 859.14 832.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="367">
   <path class="kv10" d="M 1695 713 L 1720.95 713 L 1720.95 743.53" stroke-width="1" zvalue="377"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="386" LinkObjectIDznd="363@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1695 713 L 1720.95 713 L 1720.95 743.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="371">
   <path class="kv10" d="M 1657.95 766.07 L 1657.95 836 L 1493.64 836" stroke-width="1" zvalue="378"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="359@1" LinkObjectIDznd="160" MaxPinNum="2"/>
   </metadata>
  <path d="M 1657.95 766.07 L 1657.95 836 L 1493.64 836" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="372">
   <path class="kv10" d="M 1720.95 766.07 L 1720.95 828.5 L 1778.15 828.5" stroke-width="1" zvalue="379"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="363@1" LinkObjectIDznd="373@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1720.95 766.07 L 1720.95 828.5 L 1778.15 828.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="383">
   <path class="kv110" d="M 974.12 287.34 L 974.12 354.5" stroke-width="1" zvalue="381"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="381@0" LinkObjectIDznd="251@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 974.12 287.34 L 974.12 354.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="385">
   <path class="kv10" d="M 1694.41 614.36 L 1694.41 628.99" stroke-width="1" zvalue="383"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="365@0" LinkObjectIDznd="384@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1694.41 614.36 L 1694.41 628.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="386">
   <path class="kv10" d="M 1695 688.59 L 1695 714 L 1657.95 714 L 1657.95 743.53" stroke-width="1" zvalue="384"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="384@1" LinkObjectIDznd="359@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1695 688.59 L 1695 714 L 1657.95 714 L 1657.95 743.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv110" d="M 1405.57 162.39 L 1405.57 182.5 L 1517.68 182.5" stroke-width="1" zvalue="389"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@0" LinkObjectIDznd="286" MaxPinNum="2"/>
   </metadata>
  <path d="M 1405.57 162.39 L 1405.57 182.5 L 1517.68 182.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv110" d="M 1432.1 165.7 L 1432.1 182.5" stroke-width="1" zvalue="390"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="273@0" LinkObjectIDznd="55" MaxPinNum="2"/>
   </metadata>
  <path d="M 1432.1 165.7 L 1432.1 182.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv110" d="M 551.35 147.95 L 551.35 159.29 L 629.43 159.29" stroke-width="1" zvalue="391"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="356@0" LinkObjectIDznd="122" MaxPinNum="2"/>
   </metadata>
  <path d="M 551.35 147.95 L 551.35 159.29 L 629.43 159.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv110" d="M 524.57 138.14 L 524.57 158.57 L 551.35 158.57" stroke-width="1" zvalue="392"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="354@0" LinkObjectIDznd="62" MaxPinNum="2"/>
   </metadata>
  <path d="M 524.57 138.14 L 524.57 158.57 L 551.35 158.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="205">
   <use class="kv10" height="30" transform="rotate(0,858.75,875.5) scale(1.69643,1.70833) translate(-342.789,-352.387)" width="28" x="835" xlink:href="#EnergyConsumer:站用变DY接地_0" y="849.875" zvalue="104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454775734274" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,858.75,875.5) scale(1.69643,1.70833) translate(-342.789,-352.387)" width="28" x="835" y="849.875"/></g>
  <g id="248">
   <use class="kv10" height="30" transform="rotate(0,1493.25,886.75) scale(1.69643,1.70833) translate(-603.268,-357.052)" width="28" x="1469.5" xlink:href="#EnergyConsumer:站用变DY接地_0" y="861.125" zvalue="172"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454775406594" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1493.25,886.75) scale(1.69643,1.70833) translate(-603.268,-357.052)" width="28" x="1469.5" y="861.125"/></g>
  <g id="287">
   <use class="kv110" height="30" transform="rotate(0,1517.68,132.278) scale(3.7037,1.48148) translate(-1091.69,-35.7681)" width="12" x="1495.45981733304" xlink:href="#EnergyConsumer:负荷_0" y="110.055555343628" zvalue="309"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454776651778" ObjectName="备用线"/>
   <cge:TPSR_Ref TObjectID="6192454776651778"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1517.68,132.278) scale(3.7037,1.48148) translate(-1091.69,-35.7681)" width="12" x="1495.45981733304" y="110.055555343628"/></g>
  <g id="365">
   <use class="kv10" height="30" transform="rotate(0,1694.25,589.75) scale(1.69643,-1.70833) translate(-685.784,-924.345)" width="28" x="1670.5" xlink:href="#EnergyConsumer:站用变DY接地_0" y="564.125" zvalue="375"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454777896962" ObjectName="大坝变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1694.25,589.75) scale(1.69643,-1.70833) translate(-685.784,-924.345)" width="28" x="1670.5" y="564.125"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="265">
   <use class="kv110" height="20" transform="rotate(270,860.194,418.75) scale(-1.11111,1.11111) translate(-1633.81,-40.7639)" width="10" x="854.6388990614149" xlink:href="#GroundDisconnector:地刀_0" y="407.6388888888889" zvalue="193"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454775209986" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454775209986"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,860.194,418.75) scale(-1.11111,1.11111) translate(-1633.81,-40.7639)" width="10" x="854.6388990614149" y="407.6388888888889"/></g>
  <g id="259">
   <use class="kv110" height="40" transform="rotate(0,722.809,549.386) scale(1.01543,-1.26928) translate(-10.6725,-976.831)" width="40" x="702.5" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="524" zvalue="201"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454775078914" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454775078914"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,722.809,549.386) scale(1.01543,-1.26928) translate(-10.6725,-976.831)" width="40" x="702.5" y="524"/></g>
  <g id="312">
   <use class="kv110" height="20" transform="rotate(270,1349.19,418.75) scale(-1.11111,1.11111) translate(-2562.91,-40.7639)" width="10" x="1343.638899061415" xlink:href="#GroundDisconnector:地刀_0" y="407.6388888888889" zvalue="222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454774816770" ObjectName="#2主变110kV侧10217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454774816770"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1349.19,418.75) scale(-1.11111,1.11111) translate(-2562.91,-40.7639)" width="10" x="1343.638899061415" y="407.6388888888889"/></g>
  <g id="306">
   <use class="kv110" height="40" transform="rotate(0,1211.81,549.386) scale(1.01543,-1.26928) translate(-18.1015,-976.831)" width="40" x="1191.5" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="524" zvalue="230"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454774685698" ObjectName="#2主变110kV侧1020中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454774685698"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1211.81,549.386) scale(1.01543,-1.26928) translate(-18.1015,-976.831)" width="40" x="1191.5" y="524"/></g>
  <g id="362">
   <use class="kv110" height="20" transform="rotate(270,677.111,285.361) scale(-1.11111,1.11111) translate(-1285.96,-27.425)" width="10" x="671.5555555555555" xlink:href="#GroundDisconnector:地刀_0" y="274.2500050862629" zvalue="272"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454774095874" ObjectName="110kV勐乃电站线15117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454774095874"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,677.111,285.361) scale(-1.11111,1.11111) translate(-1285.96,-27.425)" width="10" x="671.5555555555555" y="274.2500050862629"/></g>
  <g id="361">
   <use class="kv110" height="20" transform="rotate(270,677.111,236.583) scale(-1.11111,1.11111) translate(-1285.96,-22.5472)" width="10" x="671.5555555555557" xlink:href="#GroundDisconnector:地刀_0" y="225.4722220102947" zvalue="274"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454773964802" ObjectName="110kV勐乃电站线15160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454773964802"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,677.111,236.583) scale(-1.11111,1.11111) translate(-1285.96,-22.5472)" width="10" x="671.5555555555557" y="225.4722220102947"/></g>
  <g id="360">
   <use class="kv110" height="20" transform="rotate(270,677.111,182.139) scale(-1.11111,1.11111) translate(-1285.96,-17.1028)" width="10" x="671.5555556615193" xlink:href="#GroundDisconnector:地刀_0" y="171.0277775658501" zvalue="276"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454773833730" ObjectName="110kV勐乃电站线15167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454773833730"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,677.111,182.139) scale(-1.11111,1.11111) translate(-1285.96,-17.1028)" width="10" x="671.5555556615193" y="171.0277775658501"/></g>
  <g id="380">
   <use class="kv110" height="20" transform="rotate(270,1041.9,317.865) scale(1.11111,1.11111) translate(-103.635,-30.6754)" width="10" x="1036.34921313089" xlink:href="#GroundDisconnector:地刀_0" y="306.7539579754784" zvalue="287"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454773506050" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454773506050"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1041.9,317.865) scale(1.11111,1.11111) translate(-103.635,-30.6754)" width="10" x="1036.34921313089" y="306.7539579754784"/></g>
  <g id="382">
   <use class="kv110" height="20" transform="rotate(270,1038.9,241.294) scale(1.11111,1.11111) translate(-103.335,-23.0183)" width="10" x="1033.34921313089" xlink:href="#GroundDisconnector:地刀_0" y="230.1825294040499" zvalue="292"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454773243906" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454773243906"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1038.9,241.294) scale(1.11111,1.11111) translate(-103.335,-23.0183)" width="10" x="1033.34921313089" y="230.1825294040499"/></g>
  <g id="285">
   <use class="kv110" height="20" transform="rotate(270,1565.36,286.611) scale(-1.11111,1.11111) translate(-2973.63,-27.55)" width="10" x="1559.805555555556" xlink:href="#GroundDisconnector:地刀_0" y="275.5000050862629" zvalue="312"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454776586242" ObjectName="备用线15217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454776586242"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1565.36,286.611) scale(-1.11111,1.11111) translate(-2973.63,-27.55)" width="10" x="1559.805555555556" y="275.5000050862629"/></g>
  <g id="284">
   <use class="kv110" height="20" transform="rotate(270,1565.36,237.833) scale(-1.11111,1.11111) translate(-2973.63,-22.6722)" width="10" x="1559.805555555556" xlink:href="#GroundDisconnector:地刀_0" y="226.7222220102947" zvalue="314"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454776455170" ObjectName="备用线15260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454776455170"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1565.36,237.833) scale(-1.11111,1.11111) translate(-2973.63,-22.6722)" width="10" x="1559.805555555556" y="226.7222220102947"/></g>
  <g id="283">
   <use class="kv110" height="20" transform="rotate(270,1565.36,183.389) scale(-1.11111,1.11111) translate(-2973.63,-17.2278)" width="10" x="1559.80555566152" xlink:href="#GroundDisconnector:地刀_0" y="172.2777775658501" zvalue="316"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454776324098" ObjectName="备用线15267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454776324098"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1565.36,183.389) scale(-1.11111,1.11111) translate(-2973.63,-17.2278)" width="10" x="1559.80555566152" y="172.2777775658501"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="263">
   <g id="2630">
    <use class="kv110" height="50" transform="rotate(0,815.25,529.089) scale(2.15,1.85355) translate(-418.814,-222.304)" width="30" x="783" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="482.75" zvalue="196"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874591567874" ObjectName="110"/>
    </metadata>
   </g>
   <g id="2631">
    <use class="kv10" height="50" transform="rotate(0,815.25,529.089) scale(2.15,1.85355) translate(-418.814,-222.304)" width="30" x="783" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="482.75" zvalue="196"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874591633410" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399533068290" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399533068290"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,815.25,529.089) scale(2.15,1.85355) translate(-418.814,-222.304)" width="30" x="783" y="482.75"/></g>
  <g id="310">
   <g id="3100">
    <use class="kv110" height="50" transform="rotate(0,1304.25,529.089) scale(2.15,1.85355) translate(-680.372,-222.304)" width="30" x="1272" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="482.75" zvalue="225"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874591436802" ObjectName="110"/>
    </metadata>
   </g>
   <g id="3101">
    <use class="kv10" height="50" transform="rotate(0,1304.25,529.089) scale(2.15,1.85355) translate(-680.372,-222.304)" width="30" x="1272" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="482.75" zvalue="225"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874591502338" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399533002754" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399533002754"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1304.25,529.089) scale(2.15,1.85355) translate(-680.372,-222.304)" width="30" x="1272" y="482.75"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="168">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="168" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136344072194" ObjectName="F"/>
   </metadata>
  </g>
  <g id="167">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="167" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136352919554" ObjectName="F"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136352985090" ObjectName="F"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.222,335.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="340.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136347873282" ObjectName="F"/>
   </metadata>
  </g>
  <g id="81">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="81" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136352788482" ObjectName="F"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136352854018" ObjectName="F"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="78" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136556605442" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127180238856" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127180173319" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.972,353.917) scale(1,1) translate(0,0)" writing-mode="lr" x="156.13" xml:space="preserve" y="358.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136345710594" ObjectName="F"/>
   </metadata>
  </g>
  <g id="130">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,401.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="406.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136556670978" ObjectName="坝下水位"/>
   </metadata>
  </g>
  <g id="135">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,335.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="335.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136556736514" ObjectName="雨量采集"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="136" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,629.432,18.3166) scale(1,1) translate(-3.8931e-13,-1.72331e-14)" writing-mode="lr" x="628.96" xml:space="preserve" y="23" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136336994306" ObjectName="P"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="137" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,629.432,42.93) scale(1,1) translate(-3.8931e-13,8.0867e-14)" writing-mode="lr" x="628.96" xml:space="preserve" y="47.61" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136337059842" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="138" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,629.432,66.739) scale(1,1) translate(-3.8931e-13,-1.35504e-13)" writing-mode="lr" x="628.96" xml:space="preserve" y="71.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136337125378" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="146">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="146" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1234.25,418.408) scale(1,1) translate(0,1.35641e-13)" writing-mode="lr" x="1233.7" xml:space="preserve" y="423.09" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136338567170" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="148" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1234.25,442) scale(1,1) translate(0,1.43499e-13)" writing-mode="lr" x="1233.7" xml:space="preserve" y="446.68" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136338632706" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="155">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="155" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1233,600.811) scale(1,1) translate(0,6.53563e-14)" writing-mode="lr" x="1232.45" xml:space="preserve" y="605.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136338698242" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="156">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="156" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,740.25,603.511) scale(1,1) translate(0,-1.31268e-13)" writing-mode="lr" x="739.7" xml:space="preserve" y="608.21" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136341254146" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="159">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="159" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1233,626.464) scale(1,1) translate(0,6.82044e-14)" writing-mode="lr" x="1232.45" xml:space="preserve" y="631.16" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136338763778" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="161">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="161" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,740.25,629.589) scale(1,1) translate(0,-2.74116e-13)" writing-mode="lr" x="739.7" xml:space="preserve" y="634.28" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136341319682" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="162">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="162" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1234.25,465.592) scale(1,1) translate(0,-1.00905e-13)" writing-mode="lr" x="1233.7" xml:space="preserve" y="470.27" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136338829314" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="164">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="164" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1233,652.117) scale(1,1) translate(0,7.10525e-14)" writing-mode="lr" x="1232.45" xml:space="preserve" y="656.8099999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136339156994" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="165">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="165" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,740.25,655.666) scale(1,1) translate(0,7.14242e-14)" writing-mode="lr" x="739.7" xml:space="preserve" y="660.36" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136341712898" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="171">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="171" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,647.891,985.756) scale(1,1) translate(0,-8.64248e-13)" writing-mode="lr" x="647.34" xml:space="preserve" y="990.46" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136348004354" ObjectName="P"/>
   </metadata>
  </g>
  <g id="172">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="172" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1209.2,989.105) scale(1,1) translate(2.56728e-13,-8.67223e-13)" writing-mode="lr" x="1208.65" xml:space="preserve" y="993.8099999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136350756866" ObjectName="P"/>
   </metadata>
  </g>
  <g id="173">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="173" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,647.891,1012.6) scale(1,1) translate(0,-8.88095e-13)" writing-mode="lr" x="647.34" xml:space="preserve" y="1017.31" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136348069890" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="174">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="174" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1209.2,1015.95) scale(1,1) translate(2.56728e-13,-8.91069e-13)" writing-mode="lr" x="1208.65" xml:space="preserve" y="1020.65" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136350822402" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="175">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="175" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,647.891,1039.45) scale(1,1) translate(0,-1.5959e-12)" writing-mode="lr" x="647.34" xml:space="preserve" y="1044.15" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136348135426" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="176">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="176" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1209.2,1042.8) scale(1,1) translate(2.56728e-13,2.05856e-12)" writing-mode="lr" x="1208.65" xml:space="preserve" y="1047.5" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136350887938" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="2" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,984,57) scale(1,1) translate(0,0)" writing-mode="lr" x="983.53" xml:space="preserve" y="61.78" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136343678978" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1496,459) scale(1,1) translate(0,0)" writing-mode="lr" x="1495.53" xml:space="preserve" y="463.78" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136345317378" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="4" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,545.5,456.186) scale(1,1) translate(0,0)" writing-mode="lr" x="545.03" xml:space="preserve" y="460.96" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136347480066" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="5" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,984,82) scale(1,1) translate(0,0)" writing-mode="lr" x="983.53" xml:space="preserve" y="86.78" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136343744514" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="6" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1496,488) scale(1,1) translate(0,0)" writing-mode="lr" x="1495.53" xml:space="preserve" y="492.78" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136345382914" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="7" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,545.5,481.186) scale(1,1) translate(0,1.02959e-13)" writing-mode="lr" x="545.03" xml:space="preserve" y="485.96" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136347545602" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="11" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,984,107) scale(1,1) translate(0,0)" writing-mode="lr" x="983.53" xml:space="preserve" y="111.78" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136343810050" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="12" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1496,517) scale(1,1) translate(0,0)" writing-mode="lr" x="1495.53" xml:space="preserve" y="521.78" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136345448450" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="13" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,545.5,505.186) scale(1,1) translate(0,0)" writing-mode="lr" x="545.03" xml:space="preserve" y="509.96" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136347611138" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="14" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,482,374.5) scale(1,1) translate(0,0)" writing-mode="lr" x="481.53" xml:space="preserve" y="379.28" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136343941122" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="15" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1067,707.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1066.53" xml:space="preserve" y="712.28" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136345579522" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="16" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,456.5,707.686) scale(1,1) translate(-2.74114e-13,0)" writing-mode="lr" x="456.03" xml:space="preserve" y="712.46" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136347742210" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="17" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,724.375,416.704) scale(1,1) translate(0,0)" writing-mode="lr" x="723.8200000000001" xml:space="preserve" y="421.38" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136341123074" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="18" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,728.75,436.25) scale(1,1) translate(0,0)" writing-mode="lr" x="728.2" xml:space="preserve" y="440.93" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136341188610" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,728.75,457.25) scale(1,1) translate(0,0)" writing-mode="lr" x="728.2" xml:space="preserve" y="461.93" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136341385218" ObjectName="HIa"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="86">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="435"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374928642049" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="85">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="436"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562962186108929" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
 </g>
</svg>