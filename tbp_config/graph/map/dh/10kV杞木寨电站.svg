<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549592653826" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="PowerTransformer2:D-Y_0" viewBox="0,0,30,50">
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01666666666667" x2="15.01666666666667" y1="37.50000000000001" y2="32.91666666666667"/>
   <use terminal-index="1" type="1" x="15.00325153685922" xlink:href="#terminal" y="49.86055225321343"/>
   <use terminal-index="2" type="2" x="15" xlink:href="#terminal" y="37.75085860895189"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.25" x2="15" y1="43.08333333333334" y2="37.75000000000001"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15" x2="20" y1="37.74761215261902" y2="42.91666666666667"/>
   <ellipse cx="15" cy="35.25" fill-opacity="0" rx="14.75" ry="14.75" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:D-Y_1" viewBox="0,0,30,50">
   <path d="M 10 16 L 20.1667 16 L 15 7.83333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.75" ry="14.75" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="0" type="1" x="15.00731595793324" xlink:href="#terminal" y="0.3902606310013752"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV杞木寨电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="46" xlink:href="logo.png" y="46.67"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,180.625,76.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="180.63" xml:space="preserve" y="80.17" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,182.333,76.357) scale(1,1) translate(7.10543e-15,0)" writing-mode="lr" x="182.33" xml:space="preserve" y="85.36" zvalue="3">10kV杞木寨电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="38" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.5,317.667) scale(1,1) translate(0,0)" width="97" x="38" y="305.67" zvalue="9"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.5,317.667) scale(1,1) translate(0,0)" writing-mode="lr" x="86.5" xml:space="preserve" y="322.17" zvalue="9">全站公用</text>
  <line fill="none" id="46" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="380" x2="380" y1="14.66666666666674" y2="1044.666666666667" zvalue="4"/>
  <line fill="none" id="43" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.000000000000227" x2="372.9999999999998" y1="150.5371592807491" y2="150.5371592807491" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="162.6666666666668" y2="162.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="188.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="162.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="162.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="162.6666666666668" y2="162.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="188.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="162.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="162.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="188.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="212.9166666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="188.6666666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="188.6666666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="188.6666666666668" y2="188.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="212.9166666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="188.6666666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="188.6666666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="212.9166666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="235.6666666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="212.9166666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="212.9166666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="212.9166666666668" y2="212.9166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="235.6666666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="212.9166666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="212.9166666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="235.6666666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="258.4166666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="235.6666666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="235.6666666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="235.6666666666668" y2="235.6666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="258.4166666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="235.6666666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="235.6666666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="258.4166666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="281.1666666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="258.4166666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="258.4166666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="258.4166666666668" y2="258.4166666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="281.1666666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="258.4166666666668" y2="281.1666666666668"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="258.4166666666668" y2="281.1666666666668"/>
  <line fill="none" id="40" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.000000000000227" x2="372.9999999999998" y1="620.5371592807492" y2="620.5371592807492" zvalue="8"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.22236173734677" x2="107.5670617373469" y1="443.3333101725261" y2="443.3333101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.22236173734677" x2="107.5670617373469" y1="480.8233101725261" y2="480.8233101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.22236173734677" x2="59.22236173734677" y1="443.3333101725261" y2="480.8233101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5670617373469" x2="107.5670617373469" y1="443.3333101725261" y2="480.8233101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5673617373468" x2="169.6756617373469" y1="443.3333101725261" y2="443.3333101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5673617373468" x2="169.6756617373469" y1="480.8233101725261" y2="480.8233101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5673617373468" x2="107.5673617373468" y1="443.3333101725261" y2="480.8233101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6756617373469" x2="169.6756617373469" y1="443.3333101725261" y2="480.8233101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6751617373468" x2="232.9999617373469" y1="443.3333101725261" y2="443.3333101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6751617373468" x2="232.9999617373469" y1="480.8233101725261" y2="480.8233101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6751617373468" x2="169.6751617373468" y1="443.3333101725261" y2="480.8233101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9999617373469" x2="232.9999617373469" y1="443.3333101725261" y2="480.8233101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9998617373468" x2="295.1081617373468" y1="443.3333101725261" y2="443.3333101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9998617373468" x2="295.1081617373468" y1="480.8233101725261" y2="480.8233101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9998617373468" x2="232.9998617373468" y1="443.3333101725261" y2="480.8233101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="295.1081617373468" y1="443.3333101725261" y2="480.8233101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="357.2164617373469" y1="443.3333101725261" y2="443.3333101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="357.2164617373469" y1="480.8233101725261" y2="480.8233101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="295.1081617373468" y1="443.3333101725261" y2="480.8233101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="357.2164617373469" x2="357.2164617373469" y1="443.3333101725261" y2="480.8233101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.22236173734677" x2="107.5670617373469" y1="480.8234101725261" y2="480.8234101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.22236173734677" x2="107.5670617373469" y1="504.9920101725261" y2="504.9920101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.22236173734677" x2="59.22236173734677" y1="480.8234101725261" y2="504.9920101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5670617373469" x2="107.5670617373469" y1="480.8234101725261" y2="504.9920101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5673617373468" x2="169.6756617373469" y1="480.8234101725261" y2="480.8234101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5673617373468" x2="169.6756617373469" y1="504.9920101725261" y2="504.9920101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5673617373468" x2="107.5673617373468" y1="480.8234101725261" y2="504.9920101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6756617373469" x2="169.6756617373469" y1="480.8234101725261" y2="504.9920101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6751617373468" x2="232.9999617373469" y1="480.8234101725261" y2="480.8234101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6751617373468" x2="232.9999617373469" y1="504.9920101725261" y2="504.9920101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6751617373468" x2="169.6751617373468" y1="480.8234101725261" y2="504.9920101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9999617373469" x2="232.9999617373469" y1="480.8234101725261" y2="504.9920101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9998617373468" x2="295.1081617373468" y1="480.8234101725261" y2="480.8234101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9998617373468" x2="295.1081617373468" y1="504.9920101725261" y2="504.9920101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9998617373468" x2="232.9998617373468" y1="480.8234101725261" y2="504.9920101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="295.1081617373468" y1="480.8234101725261" y2="504.9920101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="357.2164617373469" y1="480.8234101725261" y2="480.8234101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="357.2164617373469" y1="504.9920101725261" y2="504.9920101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="295.1081617373468" y1="480.8234101725261" y2="504.9920101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="357.2164617373469" x2="357.2164617373469" y1="480.8234101725261" y2="504.9920101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.22236173734677" x2="107.5670617373469" y1="504.9920101725261" y2="504.9920101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.22236173734677" x2="107.5670617373469" y1="529.160610172526" y2="529.160610172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.22236173734677" x2="59.22236173734677" y1="504.9920101725261" y2="529.160610172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5670617373469" x2="107.5670617373469" y1="504.9920101725261" y2="529.160610172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5673617373468" x2="169.6756617373469" y1="504.9920101725261" y2="504.9920101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5673617373468" x2="169.6756617373469" y1="529.160610172526" y2="529.160610172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5673617373468" x2="107.5673617373468" y1="504.9920101725261" y2="529.160610172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6756617373469" x2="169.6756617373469" y1="504.9920101725261" y2="529.160610172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6751617373468" x2="232.9999617373469" y1="504.9920101725261" y2="504.9920101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6751617373468" x2="232.9999617373469" y1="529.160610172526" y2="529.160610172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6751617373468" x2="169.6751617373468" y1="504.9920101725261" y2="529.160610172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9999617373469" x2="232.9999617373469" y1="504.9920101725261" y2="529.160610172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9998617373468" x2="295.1081617373468" y1="504.9920101725261" y2="504.9920101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9998617373468" x2="295.1081617373468" y1="529.160610172526" y2="529.160610172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9998617373468" x2="232.9998617373468" y1="504.9920101725261" y2="529.160610172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="295.1081617373468" y1="504.9920101725261" y2="529.160610172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="357.2164617373469" y1="504.9920101725261" y2="504.9920101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="357.2164617373469" y1="529.160610172526" y2="529.160610172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="295.1081617373468" y1="504.9920101725261" y2="529.160610172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="357.2164617373469" x2="357.2164617373469" y1="504.9920101725261" y2="529.160610172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.22236173734677" x2="107.5670617373469" y1="529.1606501725261" y2="529.1606501725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.22236173734677" x2="107.5670617373469" y1="553.329250172526" y2="553.329250172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.22236173734677" x2="59.22236173734677" y1="529.1606501725261" y2="553.329250172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5670617373469" x2="107.5670617373469" y1="529.1606501725261" y2="553.329250172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5673617373468" x2="169.6756617373469" y1="529.1606501725261" y2="529.1606501725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5673617373468" x2="169.6756617373469" y1="553.329250172526" y2="553.329250172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5673617373468" x2="107.5673617373468" y1="529.1606501725261" y2="553.329250172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6756617373469" x2="169.6756617373469" y1="529.1606501725261" y2="553.329250172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6751617373468" x2="232.9999617373469" y1="529.1606501725261" y2="529.1606501725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6751617373468" x2="232.9999617373469" y1="553.329250172526" y2="553.329250172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6751617373468" x2="169.6751617373468" y1="529.1606501725261" y2="553.329250172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9999617373469" x2="232.9999617373469" y1="529.1606501725261" y2="553.329250172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9998617373468" x2="295.1081617373468" y1="529.1606501725261" y2="529.1606501725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9998617373468" x2="295.1081617373468" y1="553.329250172526" y2="553.329250172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9998617373468" x2="232.9998617373468" y1="529.1606501725261" y2="553.329250172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="295.1081617373468" y1="529.1606501725261" y2="553.329250172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="357.2164617373469" y1="529.1606501725261" y2="529.1606501725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="357.2164617373469" y1="553.329250172526" y2="553.329250172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="295.1081617373468" y1="529.1606501725261" y2="553.329250172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="357.2164617373469" x2="357.2164617373469" y1="529.1606501725261" y2="553.329250172526"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.22236173734677" x2="107.5670617373469" y1="553.3294101725261" y2="553.3294101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.22236173734677" x2="107.5670617373469" y1="577.4980101725261" y2="577.4980101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.22236173734677" x2="59.22236173734677" y1="553.3294101725261" y2="577.4980101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5670617373469" x2="107.5670617373469" y1="553.3294101725261" y2="577.4980101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5673617373468" x2="169.6756617373469" y1="553.3294101725261" y2="553.3294101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5673617373468" x2="169.6756617373469" y1="577.4980101725261" y2="577.4980101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5673617373468" x2="107.5673617373468" y1="553.3294101725261" y2="577.4980101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6756617373469" x2="169.6756617373469" y1="553.3294101725261" y2="577.4980101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6751617373468" x2="232.9999617373469" y1="553.3294101725261" y2="553.3294101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6751617373468" x2="232.9999617373469" y1="577.4980101725261" y2="577.4980101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6751617373468" x2="169.6751617373468" y1="553.3294101725261" y2="577.4980101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9999617373469" x2="232.9999617373469" y1="553.3294101725261" y2="577.4980101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9998617373468" x2="295.1081617373468" y1="553.3294101725261" y2="553.3294101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9998617373468" x2="295.1081617373468" y1="577.4980101725261" y2="577.4980101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9998617373468" x2="232.9998617373468" y1="553.3294101725261" y2="577.4980101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="295.1081617373468" y1="553.3294101725261" y2="577.4980101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="357.2164617373469" y1="553.3294101725261" y2="553.3294101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="357.2164617373469" y1="577.4980101725261" y2="577.4980101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="295.1081617373468" y1="553.3294101725261" y2="577.4980101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="357.2164617373469" x2="357.2164617373469" y1="553.3294101725261" y2="577.4980101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.22236173734677" x2="107.5670617373469" y1="577.4980101725261" y2="577.4980101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.22236173734677" x2="107.5670617373469" y1="601.6666101725261" y2="601.6666101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="59.22236173734677" x2="59.22236173734677" y1="577.4980101725261" y2="601.6666101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5670617373469" x2="107.5670617373469" y1="577.4980101725261" y2="601.6666101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5673617373468" x2="169.6756617373469" y1="577.4980101725261" y2="577.4980101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5673617373468" x2="169.6756617373469" y1="601.6666101725261" y2="601.6666101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="107.5673617373468" x2="107.5673617373468" y1="577.4980101725261" y2="601.6666101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6756617373469" x2="169.6756617373469" y1="577.4980101725261" y2="601.6666101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6751617373468" x2="232.9999617373469" y1="577.4980101725261" y2="577.4980101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6751617373468" x2="232.9999617373469" y1="601.6666101725261" y2="601.6666101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.6751617373468" x2="169.6751617373468" y1="577.4980101725261" y2="601.6666101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9999617373469" x2="232.9999617373469" y1="577.4980101725261" y2="601.6666101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9998617373468" x2="295.1081617373468" y1="577.4980101725261" y2="577.4980101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9998617373468" x2="295.1081617373468" y1="601.6666101725261" y2="601.6666101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="232.9998617373468" x2="232.9998617373468" y1="577.4980101725261" y2="601.6666101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="295.1081617373468" y1="577.4980101725261" y2="601.6666101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="357.2164617373469" y1="577.4980101725261" y2="577.4980101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="357.2164617373469" y1="601.6666101725261" y2="601.6666101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1081617373468" x2="295.1081617373468" y1="577.4980101725261" y2="601.6666101725261"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="357.2164617373469" x2="357.2164617373469" y1="577.4980101725261" y2="601.6666101725261"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="935.666666666667" y2="935.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="974.8299666666669" y2="974.8299666666669"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="6" y1="935.666666666667" y2="974.8299666666669"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="935.666666666667" y2="974.8299666666669"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="366" y1="935.666666666667" y2="935.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="366" y1="974.8299666666669" y2="974.8299666666669"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="935.666666666667" y2="974.8299666666669"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366" x2="366" y1="935.666666666667" y2="974.8299666666669"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="974.829936666667" y2="974.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="1002.748336666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="6" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="186" y1="974.829936666667" y2="974.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="186" y1="1002.748336666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186" x2="186" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="276.0000000000001" y1="974.829936666667" y2="974.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="276.0000000000001" y1="1002.748336666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="186.0000000000001" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.0000000000001" x2="276.0000000000001" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="366" y1="974.829936666667" y2="974.829936666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="366" y1="1002.748336666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="276" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366" x2="366" y1="974.829936666667" y2="1002.748336666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="1002.748266666667" y2="1002.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="1030.666666666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="6" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="186" y1="1002.748266666667" y2="1002.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="186" y1="1030.666666666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186" x2="186" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="276.0000000000001" y1="1002.748266666667" y2="1002.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="276.0000000000001" y1="1030.666666666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="186.0000000000001" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.0000000000001" x2="276.0000000000001" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="366" y1="1002.748266666667" y2="1002.748266666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="366" y1="1030.666666666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="276" y1="1002.748266666667" y2="1030.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366" x2="366" y1="1002.748266666667" y2="1030.666666666667"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51,955.667) scale(1,1) translate(0,0)" writing-mode="lr" x="51" xml:space="preserve" y="961.67" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48,989.667) scale(1,1) translate(0,0)" writing-mode="lr" x="48" xml:space="preserve" y="995.67" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,230,989.667) scale(1,1) translate(0,0)" writing-mode="lr" x="230" xml:space="preserve" y="995.67" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47,1017.67) scale(1,1) translate(0,0)" writing-mode="lr" x="47" xml:space="preserve" y="1023.67" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229,1017.67) scale(1,1) translate(0,0)" writing-mode="lr" x="229" xml:space="preserve" y="1023.67" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,71.5,650.167) scale(1,1) translate(0,0)" writing-mode="lr" x="71.50000000000011" xml:space="preserve" y="654.6666666666667" zvalue="18">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,204.399,317.508) scale(1,1) translate(0,0)" writing-mode="lr" x="204.4" xml:space="preserve" y="322.01" zvalue="19">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,309.399,317.508) scale(1,1) translate(0,0)" writing-mode="lr" x="309.4" xml:space="preserve" y="322.01" zvalue="20">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,493.167) scale(1,1) translate(0,0)" writing-mode="lr" x="83.00000000000011" xml:space="preserve" y="497.6666666666667" zvalue="21">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,518.667) scale(1,1) translate(0,0)" writing-mode="lr" x="83.00000000000011" xml:space="preserve" y="523.1666666666667" zvalue="22">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,544.167) scale(1,1) translate(0,0)" writing-mode="lr" x="83.00000000000011" xml:space="preserve" y="548.6666666666667" zvalue="23">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,82,568.667) scale(1,1) translate(0,0)" writing-mode="lr" x="82.00000000000011" xml:space="preserve" y="573.1666666666667" zvalue="24">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,595.167) scale(1,1) translate(0,0)" writing-mode="lr" x="83.00000000000011" xml:space="preserve" y="599.6666666666667" zvalue="25">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231.054,957.667) scale(1,1) translate(0,0)" writing-mode="lr" x="231.05" xml:space="preserve" y="963.67" zvalue="26">MGS4-01-2008</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,141.054,989.667) scale(1,1) translate(0,0)" writing-mode="lr" x="141.05" xml:space="preserve" y="995.67" zvalue="27">何成飞</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,321.054,989.667) scale(1,1) translate(0,0)" writing-mode="lr" x="321.05" xml:space="preserve" y="995.67" zvalue="28">20210510</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45,176.667) scale(1,1) translate(0,0)" writing-mode="lr" x="45" xml:space="preserve" y="182.17" zvalue="29">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225,176.667) scale(1,1) translate(0,0)" writing-mode="lr" x="225" xml:space="preserve" y="182.17" zvalue="30">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.1875,248.667) scale(1,1) translate(0,0)" writing-mode="lr" x="52.19" xml:space="preserve" y="253.17" zvalue="31">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.083,203.361) scale(1,1) translate(0,0)" writing-mode="lr" x="237.08" xml:space="preserve" y="207.86" zvalue="32">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" x="258.375" xml:space="preserve" y="461.421875" zvalue="33">0.4kV  母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="258.375" xml:space="preserve" y="477.421875" zvalue="33">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1168.88,507.354) scale(1,1) translate(0,0)" writing-mode="lr" x="1168.88" xml:space="preserve" y="511.85" zvalue="36">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1110.04,976.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1110.04" xml:space="preserve" y="980.67" zvalue="36">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1107.5,90.1667) scale(1,1) translate(0,0)" writing-mode="lr" x="1107.5" xml:space="preserve" y="94.67" zvalue="38">10kV杞大线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1076.12,263.417) scale(1,1) translate(0,0)" writing-mode="lr" x="1076.13" xml:space="preserve" y="267.92" zvalue="40">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1076.71,666.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1076.71" xml:space="preserve" y="671.17" zvalue="44">4016</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1130.12,776.167) scale(1,1) translate(0,1.36454e-12)" writing-mode="lr" x="1130.13" xml:space="preserve" y="780.67" zvalue="48">401</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1132.62,378.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1132.63" xml:space="preserve" y="383.38" zvalue="52">041</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1168.5,530) scale(1,1) translate(0,0)" writing-mode="lr" x="1168.5" xml:space="preserve" y="534.5" zvalue="55">240kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1109,998) scale(1,1) translate(0,0)" writing-mode="lr" x="1109" xml:space="preserve" y="1002.5" zvalue="56">200kW</text>
 </g>
 <g id="ButtonClass">
  <g href="500kV德宏变_全站公用.svg"><rect fill-opacity="0" height="24" width="97" x="38" y="305.67" zvalue="9"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="GeneratorClass">
  <g id="33">
   <use class="v400" height="30" transform="rotate(0,1106.29,911.979) scale(2.39583,2.39583) translate(-623.594,-510.389)" width="30" x="1070.348526204546" xlink:href="#Generator:发电机_0" y="876.0416666666665" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450021097477" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192450021097477"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1106.29,911.979) scale(2.39583,2.39583) translate(-623.594,-510.389)" width="30" x="1070.348526204546" y="876.0416666666665"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="34">
   <g id="340">
    <use class="v400" height="50" transform="rotate(0,1106.31,514.354) scale(1.7625,1.7625) translate(-467.18,-203.46)" width="30" x="1079.88" xlink:href="#PowerTransformer2:D-Y_0" y="470.29" zvalue="35"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874454859779" ObjectName="0.4"/>
    </metadata>
   </g>
   <g id="341">
    <use class="kv10" height="50" transform="rotate(0,1106.31,514.354) scale(1.7625,1.7625) translate(-467.18,-203.46)" width="30" x="1079.88" xlink:href="#PowerTransformer2:D-Y_1" y="470.29" zvalue="35"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874454794243" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399459274755" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399459274755"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1106.31,514.354) scale(1.7625,1.7625) translate(-467.18,-203.46)" width="30" x="1079.88" y="470.29"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="36">
   <use class="kv10" height="30" transform="rotate(0,1107.5,136.667) scale(1.5625,1.54167) translate(-395.325,-39.893)" width="12" x="1098.125" xlink:href="#EnergyConsumer:负荷_0" y="113.5416666666667" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450021031941" ObjectName="10kV杞大线"/>
   <cge:TPSR_Ref TObjectID="6192450021031941"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1107.5,136.667) scale(1.5625,1.54167) translate(-395.325,-39.893)" width="12" x="1098.125" y="113.5416666666667"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="39">
   <use class="kv10" height="30" transform="rotate(0,1106.25,264.417) scale(1.25,0.916667) translate(-219.375,22.7879)" width="15" x="1096.875" xlink:href="#Disconnector:刀闸_0" y="250.6666666666667" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450020966405" ObjectName="#1主变0416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450020966405"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1106.25,264.417) scale(1.25,0.916667) translate(-219.375,22.7879)" width="15" x="1096.875" y="250.6666666666667"/></g>
  <g id="42">
   <use class="v400" height="30" transform="rotate(0,1106.21,667.667) scale(1.25,0.916667) translate(-219.367,59.447)" width="15" x="1096.834570674413" xlink:href="#Disconnector:刀闸_0" y="653.9166666666667" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450020900869" ObjectName="#1发电机4016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450020900869"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1106.21,667.667) scale(1.25,0.916667) translate(-219.367,59.447)" width="15" x="1096.834570674413" y="653.9166666666667"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="10">
   <path class="kv10" d="M 1107.5 157.48 L 1107.5 251.12" stroke-width="1" zvalue="41"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="39@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1107.5 157.48 L 1107.5 251.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="v400" d="M 1106.32 654.37 L 1106.32 558.17" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@0" LinkObjectIDznd="34@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1106.32 654.37 L 1106.32 558.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="v400" d="M 1106.29 876.64 L 1106.38 793.28" stroke-width="1" zvalue="47"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="45@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1106.29 876.64 L 1106.38 793.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="v400" d="M 1106.19 761.02 L 1106.29 681.18" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="42@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1106.19 761.02 L 1106.29 681.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv10" d="M 1106.33 277.93 L 1106.33 363.73" stroke-width="1" zvalue="52"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="39@1" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1106.33 277.93 L 1106.33 363.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv10" d="M 1108.25 395.99 L 1108.25 470.98" stroke-width="1" zvalue="53"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@1" LinkObjectIDznd="34@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1108.25 395.99 L 1108.25 470.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="45">
   <use class="v400" height="20" transform="rotate(0,1106.25,777.167) scale(1.875,1.6875) translate(-511.875,-309.748)" width="10" x="1096.875" xlink:href="#Breaker:开关_0" y="760.2916666666667" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924557733893" ObjectName="#1发电机401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924557733893"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1106.25,777.167) scale(1.875,1.6875) translate(-511.875,-309.748)" width="10" x="1096.875" y="760.2916666666667"/></g>
  <g id="49">
   <use class="kv10" height="20" transform="rotate(0,1108.12,379.875) scale(1.875,1.6875) translate(-512.75,-147.889)" width="10" x="1098.75" xlink:href="#Breaker:开关_0" y="363" zvalue="51"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924557799429" ObjectName="#1主变041断路器"/>
   <cge:TPSR_Ref TObjectID="6473924557799429"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1108.12,379.875) scale(1.875,1.6875) translate(-512.75,-147.889)" width="10" x="1098.75" y="363"/></g>
 </g>
</svg>