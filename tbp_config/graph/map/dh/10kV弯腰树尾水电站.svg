<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549586755586" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="12.0194189818494"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="5.416666666666668" y2="12.02321291373541"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.92126160277786" x2="6.666666666666666" y1="12.10207526123773" y2="18.50000000000001"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.18646053143751" x2="24.5" y1="12.18904818695178" y2="19.00000000000001"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 7.33333 43.5833 L 23 43.5833 L 15 31.5 z" fill-opacity="0" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:20210316_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,18.75) scale(1,1) translate(0,0)" width="6" x="3" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="6.66666666666667" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="6.727157046327149" y2="14.02109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="6.734473004260392" y2="14.02109851866368"/>
  </symbol>
  <symbol id="Disconnector:20210316_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,18.75) scale(1,1) translate(0,0)" width="6" x="3" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="0.25" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="0.7271570463271502" y2="8.021098518663681"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="0.7344730042603906" y2="8.021098518663679"/>
  </symbol>
  <symbol id="Disconnector:20210316_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV弯腰树尾水电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="42.43" xlink:href="logo.png" y="40"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,177.054,70) scale(1,1) translate(0,0)" writing-mode="lr" x="177.05" xml:space="preserve" y="73.5" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,178.762,69.6903) scale(1,1) translate(6.70892e-15,0)" writing-mode="lr" x="178.76" xml:space="preserve" y="78.69" zvalue="3">10kV弯腰树尾水电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="6" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,67.4375,320) scale(1,1) translate(0,0)" width="72.88" x="31" y="308" zvalue="55"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.4375,320) scale(1,1) translate(0,0)" writing-mode="lr" x="67.44" xml:space="preserve" y="324.5" zvalue="55">信号一览</text>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="376.4285714285717" x2="376.4285714285717" y1="8" y2="1038" zvalue="4"/>
  <line fill="none" id="28" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.4285714285719" x2="369.4285714285714" y1="143.8704926140825" y2="143.8704926140825" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="156.0000000000001" y2="156.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="182.0000000000001" y2="182.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="3.428571428571672" y1="156.0000000000001" y2="182.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="156.0000000000001" y2="182.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="156.0000000000001" y2="156.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="182.0000000000001" y2="182.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="156.0000000000001" y2="182.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285717" x2="365.4285714285717" y1="156.0000000000001" y2="182.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="182.0000000000001" y2="182.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="206.2500000000001" y2="206.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="3.428571428571672" y1="182.0000000000001" y2="206.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="182.0000000000001" y2="206.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="182.0000000000001" y2="182.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="206.2500000000001" y2="206.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="182.0000000000001" y2="206.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285717" x2="365.4285714285717" y1="182.0000000000001" y2="206.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="206.2500000000001" y2="206.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="229.0000000000001" y2="229.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="3.428571428571672" y1="206.2500000000001" y2="229.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="206.2500000000001" y2="229.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="206.2500000000001" y2="206.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="229.0000000000001" y2="229.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="206.2500000000001" y2="229.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285717" x2="365.4285714285717" y1="206.2500000000001" y2="229.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="229.0000000000001" y2="229.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="251.7500000000001" y2="251.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="3.428571428571672" y1="229.0000000000001" y2="251.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="229.0000000000001" y2="251.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="229.0000000000001" y2="229.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="251.7500000000001" y2="251.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="229.0000000000001" y2="251.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285717" x2="365.4285714285717" y1="229.0000000000001" y2="251.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="251.7500000000001" y2="251.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="184.4285714285717" y1="274.5000000000001" y2="274.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="3.428571428571672" x2="3.428571428571672" y1="251.7500000000001" y2="274.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="251.7500000000001" y2="274.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="251.7500000000001" y2="251.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="365.4285714285717" y1="274.5000000000001" y2="274.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="184.4285714285717" x2="184.4285714285717" y1="251.7500000000001" y2="274.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="365.4285714285717" x2="365.4285714285717" y1="251.7500000000001" y2="274.5000000000001"/>
  <line fill="none" id="26" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.4285714285719" x2="369.4285714285714" y1="613.8704926140827" y2="613.8704926140827" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="92.42857142857167" y1="929.0000000000005" y2="929.0000000000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="92.42857142857167" y1="968.1633000000005" y2="968.1633000000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="2.428571428571672" y1="929.0000000000005" y2="968.1633000000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="92.42857142857167" y1="929.0000000000005" y2="968.1633000000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="362.4285714285717" y1="929.0000000000005" y2="929.0000000000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="362.4285714285717" y1="968.1633000000005" y2="968.1633000000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="92.42857142857167" y1="929.0000000000005" y2="968.1633000000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362.4285714285717" x2="362.4285714285717" y1="929.0000000000005" y2="968.1633000000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="92.42857142857167" y1="968.1632700000005" y2="968.1632700000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="92.42857142857167" y1="996.0816700000005" y2="996.0816700000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="2.428571428571672" y1="968.1632700000005" y2="996.0816700000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="92.42857142857167" y1="968.1632700000005" y2="996.0816700000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="182.4285714285717" y1="968.1632700000005" y2="968.1632700000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="182.4285714285717" y1="996.0816700000005" y2="996.0816700000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="92.42857142857167" y1="968.1632700000005" y2="996.0816700000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285717" x2="182.4285714285717" y1="968.1632700000005" y2="996.0816700000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285718" x2="272.4285714285718" y1="968.1632700000005" y2="968.1632700000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285718" x2="272.4285714285718" y1="996.0816700000005" y2="996.0816700000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285718" x2="182.4285714285718" y1="968.1632700000005" y2="996.0816700000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285718" x2="272.4285714285718" y1="968.1632700000005" y2="996.0816700000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285717" x2="362.4285714285717" y1="968.1632700000005" y2="968.1632700000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285717" x2="362.4285714285717" y1="996.0816700000005" y2="996.0816700000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285717" x2="272.4285714285717" y1="968.1632700000005" y2="996.0816700000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362.4285714285717" x2="362.4285714285717" y1="968.1632700000005" y2="996.0816700000005"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="92.42857142857167" y1="996.0816000000004" y2="996.0816000000004"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="92.42857142857167" y1="1024" y2="1024"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="2.428571428571672" x2="2.428571428571672" y1="996.0816000000004" y2="1024"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="92.42857142857167" y1="996.0816000000004" y2="1024"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="182.4285714285717" y1="996.0816000000004" y2="996.0816000000004"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="182.4285714285717" y1="1024" y2="1024"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="92.42857142857167" x2="92.42857142857167" y1="996.0816000000004" y2="1024"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285717" x2="182.4285714285717" y1="996.0816000000004" y2="1024"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285718" x2="272.4285714285718" y1="996.0816000000004" y2="996.0816000000004"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285718" x2="272.4285714285718" y1="1024" y2="1024"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="182.4285714285718" x2="182.4285714285718" y1="996.0816000000004" y2="1024"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285718" x2="272.4285714285718" y1="996.0816000000004" y2="1024"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285717" x2="362.4285714285717" y1="996.0816000000004" y2="996.0816000000004"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285717" x2="362.4285714285717" y1="1024" y2="1024"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="272.4285714285717" x2="272.4285714285717" y1="996.0816000000004" y2="1024"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="362.4285714285717" x2="362.4285714285717" y1="996.0816000000004" y2="1024"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.4286,949) scale(1,1) translate(0,0)" writing-mode="lr" x="47.43" xml:space="preserve" y="955" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.4286,983) scale(1,1) translate(0,0)" writing-mode="lr" x="44.43" xml:space="preserve" y="989" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.429,983) scale(1,1) translate(0,0)" writing-mode="lr" x="226.43" xml:space="preserve" y="989" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,43.4286,1011) scale(1,1) translate(0,0)" writing-mode="lr" x="43.43" xml:space="preserve" y="1017" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225.429,1011) scale(1,1) translate(0,0)" writing-mode="lr" x="225.43" xml:space="preserve" y="1017" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.9286,643.5) scale(1,1) translate(0,0)" writing-mode="lr" x="67.92857142857167" xml:space="preserve" y="648" zvalue="18">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.483,951) scale(1,1) translate(0,0)" writing-mode="lr" x="227.48" xml:space="preserve" y="957" zvalue="26">WanYaoShuWeiShui-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,135.483,1009) scale(1,1) translate(0,0)" writing-mode="lr" x="135.48" xml:space="preserve" y="1015" zvalue="27">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,41.4286,170) scale(1,1) translate(0,0)" writing-mode="lr" x="41.43" xml:space="preserve" y="175.5" zvalue="29">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,221.429,170) scale(1,1) translate(0,0)" writing-mode="lr" x="221.43" xml:space="preserve" y="175.5" zvalue="30">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.6161,242) scale(1,1) translate(0,0)" writing-mode="lr" x="48.62" xml:space="preserve" y="246.5" zvalue="31">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.512,196.694) scale(1,1) translate(0,0)" writing-mode="lr" x="233.51" xml:space="preserve" y="201.19" zvalue="32">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1622.71,597) scale(1,1) translate(0,0)" writing-mode="lr" x="1622.71" xml:space="preserve" y="601.5" zvalue="35">0.4kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1005.76,85.7143) scale(1,1) translate(0,0)" writing-mode="lr" x="1005.76" xml:space="preserve" y="90.20999999999999" zvalue="36">10kV州水泥厂线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" x="951.96875" xml:space="preserve" y="385.2656250000001" zvalue="37">#1主变            </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="951.96875" xml:space="preserve" y="401.2656250000001" zvalue="37">400KVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" x="1007.6328125" xml:space="preserve" y="999.5915166309901" zvalue="40">#1发电机      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1007.6328125" xml:space="preserve" y="1015.59151663099" zvalue="40">320KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1032.79,805.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1032.79" xml:space="preserve" y="810.0700000000001" zvalue="42">401</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,990.017,688.429) scale(1,1) translate(8.77536e-13,0)" writing-mode="lr" x="990.02" xml:space="preserve" y="692.9299999999999" zvalue="45">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,973.714,276.286) scale(1,1) translate(0,-4.76571e-13)" writing-mode="lr" x="973.71" xml:space="preserve" y="280.79" zvalue="48">0416</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,183.211,320.591) scale(1,1) translate(0,0)" writing-mode="lr" x="183.21" xml:space="preserve" y="325.09" zvalue="51">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,288.211,320.591) scale(1,1) translate(0,0)" writing-mode="lr" x="288.21" xml:space="preserve" y="325.09" zvalue="52">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="10kV弯腰树尾水电站.svg"><rect fill-opacity="0" height="24" width="72.88" x="31" y="308" zvalue="55"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="33">
   <path class="v400" d="M 628.57 598 L 1577.14 598" stroke-width="6" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674243641348" ObjectName="0.4kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674243641348"/></metadata>
  <path d="M 628.57 598 L 1577.14 598" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="35">
   <g id="350">
    <use class="kv10" height="50" transform="rotate(0,1005.71,388) scale(1.42857,1.42857) translate(-295.286,-105.686)" width="30" x="984.29" xlink:href="#PowerTransformer2:Y-D_0" y="352.29" zvalue="36"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874437296132" ObjectName="10"/>
    </metadata>
   </g>
   <g id="351">
    <use class="v400" height="50" transform="rotate(0,1005.71,388) scale(1.42857,1.42857) translate(-295.286,-105.686)" width="30" x="984.29" xlink:href="#PowerTransformer2:Y-D_1" y="352.29" zvalue="36"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874437361668" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399450886148" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399450886148"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1005.71,388) scale(1.42857,1.42857) translate(-295.286,-105.686)" width="30" x="984.29" y="352.29"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="36">
   <path class="v400" d="M 1005.71 423.21 L 1005.71 598" stroke-width="1" zvalue="37"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@1" LinkObjectIDznd="33@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.71 423.21 L 1005.71 598" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="v400" d="M 1005.71 914.96 L 1005.71 824.99" stroke-width="1" zvalue="42"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="40@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.71 914.96 L 1005.71 824.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="v400" d="M 1005.71 788.12 L 1005.68 704.87" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@0" LinkObjectIDznd="43@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.71 788.12 L 1005.68 704.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="v400" d="M 1005.71 674.23 L 1005.71 598" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="33@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.71 674.23 L 1005.71 598" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv10" d="M 1005.76 352.93 L 1005.76 297.95" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="46@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005.76 352.93 L 1005.76 297.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv10" d="M 1006.71 256.69 L 1006.71 152.74" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@1" LinkObjectIDznd="34@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1006.71 256.69 L 1006.71 152.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="38">
   <use class="v400" height="30" transform="rotate(0,1005.71,946.571) scale(2.14286,2.14286) translate(-519.238,-487.695)" width="30" x="973.5714285714286" xlink:href="#Generator:发电机_0" y="914.4285714285713" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449799061510" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449799061510"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1005.71,946.571) scale(2.14286,2.14286) translate(-519.238,-487.695)" width="30" x="973.5714285714286" y="914.4285714285713"/></g>
 </g>
 <g id="BreakerClass">
  <g id="40">
   <use class="v400" height="20" transform="rotate(0,1005.79,806.571) scale(2.14286,1.92857) translate(-530.705,-379.063)" width="10" x="995.0714285714286" xlink:href="#Breaker:开关_0" y="787.2857142857143" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924511465477" ObjectName="#1发电机401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924511465477"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1005.79,806.571) scale(2.14286,1.92857) translate(-530.705,-379.063)" width="10" x="995.0714285714286" y="787.2857142857143"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="43">
   <use class="v400" height="30" transform="rotate(0,1005.59,689.429) scale(1.42857,1.04762) translate(-298.462,-30.6234)" width="15" x="994.874604213056" xlink:href="#Disconnector:刀闸_0" y="673.7142857142857" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449799127045" ObjectName="#1发电机4011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449799127045"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1005.59,689.429) scale(1.42857,1.04762) translate(-298.462,-30.6234)" width="15" x="994.874604213056" y="673.7142857142857"/></g>
  <g id="46">
   <use class="kv10" height="26" transform="rotate(0,1006.7,277.286) scale(1.59341,1.59341) translate(-371.35,-95.5507)" width="12" x="997.142857142857" xlink:href="#Disconnector:20210316_0" y="256.5714285714286" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449799192581" ObjectName="#1主变10kV侧0416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449799192581"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1006.7,277.286) scale(1.59341,1.59341) translate(-371.35,-95.5507)" width="12" x="997.142857142857" y="256.5714285714286"/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,315.485,321.107) scale(0.708333,0.665547) translate(125.531,156.347)" width="30" x="304.86" xlink:href="#State:红绿圆(方形)_0" y="311.12" zvalue="53"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,315.485,321.107) scale(0.708333,0.665547) translate(125.531,156.347)" width="30" x="304.86" y="311.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,219.86,321.107) scale(0.708333,0.665547) translate(86.1556,156.347)" width="30" x="209.24" xlink:href="#State:红绿圆(方形)_0" y="311.12" zvalue="54"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,219.86,321.107) scale(0.708333,0.665547) translate(86.1556,156.347)" width="30" x="209.24" y="311.12"/></g>
 </g>
</svg>