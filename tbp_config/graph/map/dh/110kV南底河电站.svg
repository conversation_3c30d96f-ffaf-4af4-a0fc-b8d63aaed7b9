<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549683814401" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Accessory:带熔断器35kVPT11_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="1.1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="26.25" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="18.06481481481482" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="20.69444444444444" y1="24.96612466124661" y2="24.96612466124661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69444444444444" x2="19.37962962962963" y1="24.96612466124661" y2="22.5"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,5.71) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="13" y2="1"/>
   <ellipse cx="15.03" cy="18.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.67" cy="23.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="18.5" cy="23.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="20.5" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:kg123_0" viewBox="0,0,8,15">
   <use terminal-index="0" type="0" x="4" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="4" xlink:href="#terminal" y="14.5"/>
   <rect fill-opacity="0" height="14.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,4,7.53) scale(1,1) translate(0,0)" width="7" x="0.5" y="0.28"/>
  </symbol>
  <symbol id="Breaker:kg123_1" viewBox="0,0,8,15">
   <use terminal-index="0" type="0" x="4" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="4" xlink:href="#terminal" y="14.5"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="14.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,3.97,7.53) scale(1,1) translate(0,0)" width="7" x="0.47" y="0.28"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.750000000000004" y2="4.166666666666668"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="32"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="1" y1="27" y2="11"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="27" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="ACLineSegment:线路电压互感器_0" viewBox="0,0,35,40">
   <use terminal-index="0" type="0" x="14.20833333333333" xlink:href="#terminal" y="38.6908407585088"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.50545055364641" x2="21.50545055364641" y1="13.04138864447597" y2="19.16666666666667"/>
   <path d="M 14 13 L 5 13 L 5 25 L 5 25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.91094883543335" x2="24.38823024054981" y1="12.92541949757221" y2="12.92541949757221"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.04,24.92) scale(1,1) translate(0,0)" width="6.08" x="2" y="17.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="4" y1="25.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="5.5" y1="37" y2="37"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="6" y1="25.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="6.5" y1="36" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="32" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="7.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.46058610156545" x2="14.15436235204273" y1="12.92541949757221" y2="12.92541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16022336769755" x2="14.16022336769755" y1="38.83333333333334" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.46383161512025" x2="31.41666666666666" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.36598892707138" x2="31.36598892707138" y1="9.749999999999996" y2="16.24517624503405"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.35940244368076" x2="24.35940244368076" y1="12.99758812251703" y2="12.99758812251703"/>
   <ellipse cx="21.51" cy="23.82" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="28.55" cy="28.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.76" cy="32.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="24.35940244368077" x2="24.35940244368077" y1="10.48325293741726" y2="15.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.51910080183274" x2="32.51910080183274" y1="11.01295093653441" y2="15.34306843322376"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="33.38393470790376" x2="33.38393470790376" y1="11.91505874834466" y2="13.89969593432727"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.57589728904158" x2="16.57589728904158" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="26.52148720885832" x2="26.52148720885832" y1="10.48325293741726" y2="15.5840919325617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="16.49256395570825" x2="16.49256395570825" y1="10.33333333333333" y2="15.43417232847779"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="18.73798205421914" x2="18.73798205421914" y1="10.48325293741726" y2="15.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.91666666666667" x2="23.91666666666667" y1="24.08333333333334" y2="24.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.33333333333334" x2="31.33333333333334" y1="28.08333333333334" y2="28.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="24.58333333333334" y1="32" y2="32"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-Y_0" viewBox="0,0,30,50">
   <ellipse cx="14.99" cy="14.99" fill-opacity="0" rx="14.76" ry="14.76" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.99138374485597" x2="10.08333333333333" y1="11.06149193548387" y2="16.08333333333334"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01984739368999" x2="20" y1="11.06354954865259" y2="16"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00235768175583" x2="15.00235768175583" y1="6.416666666666664" y2="11.06149193548386"/>
   <use terminal-index="0" type="1" x="14.99138374485597" xlink:href="#terminal" y="0.2276829173857209"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-Y_1" viewBox="0,0,30,50">
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00235768175583" x2="15.00235768175583" y1="32" y2="37.04670698924731"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00546553497943" x2="20" y1="37.04651063100137" y2="41.75"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00235768175583" x2="10" y1="37.04670698924731" y2="41.91666666666666"/>
   <ellipse cx="15.01" cy="35.25" fill-opacity="0" rx="14.76" ry="14.76" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="1" x="15.01333161865569" xlink:href="#terminal" y="50.00705645161292"/>
  </symbol>
  <symbol id="Accessory:空挂线路_0" viewBox="0,0,11,13">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="-0.0833333333333357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="12.91666666666667" y2="0.2500000000000009"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.583333333333332" y2="1.050000000000001"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="26.41666666666667" y2="34.83333333333334"/>
   <rect fill-opacity="0" height="17" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,18) scale(1,1) translate(0,0)" width="8" x="3" y="9.5"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="35" y2="1.166666666666664"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <rect fill-opacity="0" height="17" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,18) scale(1,1) translate(0,0)" width="8" x="3" y="9.5"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="9.999999999999995" y2="1.299999999999994"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV南底河电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="48.25" xlink:href="logo.png" y="36.25"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,176.25,66.25) scale(1,1) translate(0,0)" writing-mode="lr" x="176.25" xml:space="preserve" y="69.75" zvalue="36"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,183.625,64.3153) scale(1,1) translate(0,0)" writing-mode="lr" x="183.63" xml:space="preserve" y="73.31999999999999" zvalue="37">110kV南底河电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="23" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,78.4375,196) scale(1,1) translate(0,0)" width="72.88" x="42" y="184" zvalue="387"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.4375,196) scale(1,1) translate(0,0)" writing-mode="lr" x="78.44" xml:space="preserve" y="200.5" zvalue="387">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,801.146,773.585) scale(1,1) translate(0,0)" writing-mode="lr" x="801.15" xml:space="preserve" y="778.09" zvalue="3">061</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,769.374,961) scale(1,1) translate(0,0)" writing-mode="lr" x="769.3740241165917" xml:space="preserve" y="965.5000000000005" zvalue="7">1号机 10MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,665.856,670.902) scale(1,1) translate(0,-5.85544e-13)" writing-mode="lr" x="665.86" xml:space="preserve" y="675.4" zvalue="19">10kV母线</text>
  <line fill="none" id="59" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382.25" x2="382.25" y1="4.25" y2="1034.25" zvalue="38"/>
  <line fill="none" id="57" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.250000000000455" x2="375.25" y1="140.1204926140824" y2="140.1204926140824" zvalue="40"/>
  <line fill="none" id="55" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.250000000000455" x2="376.25" y1="571.1204926140824" y2="571.1204926140824" zvalue="42"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.25" x2="98.25" y1="925.25" y2="925.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.25" x2="98.25" y1="964.4132999999999" y2="964.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.25" x2="8.25" y1="925.25" y2="964.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.25" x2="98.25" y1="925.25" y2="964.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.25" x2="368.25" y1="925.25" y2="925.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.25" x2="368.25" y1="964.4132999999999" y2="964.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.25" x2="98.25" y1="925.25" y2="964.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368.25" x2="368.25" y1="925.25" y2="964.4132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.25" x2="98.25" y1="964.41327" y2="964.41327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.25" x2="98.25" y1="992.33167" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.25" x2="8.25" y1="964.41327" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.25" x2="98.25" y1="964.41327" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.25" x2="188.25" y1="964.41327" y2="964.41327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.25" x2="188.25" y1="992.33167" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.25" x2="98.25" y1="964.41327" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.25" x2="188.25" y1="964.41327" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.2500000000001" x2="278.2500000000001" y1="964.41327" y2="964.41327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.2500000000001" x2="278.2500000000001" y1="992.33167" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.2500000000001" x2="188.2500000000001" y1="964.41327" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.2500000000001" x2="278.2500000000001" y1="964.41327" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.25" x2="368.25" y1="964.41327" y2="964.41327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.25" x2="368.25" y1="992.33167" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.25" x2="278.25" y1="964.41327" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368.25" x2="368.25" y1="964.41327" y2="992.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.25" x2="98.25" y1="992.3316" y2="992.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.25" x2="98.25" y1="1020.25" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8.25" x2="8.25" y1="992.3316" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.25" x2="98.25" y1="992.3316" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.25" x2="188.25" y1="992.3316" y2="992.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.25" x2="188.25" y1="1020.25" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98.25" x2="98.25" y1="992.3316" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.25" x2="188.25" y1="992.3316" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.2500000000001" x2="278.2500000000001" y1="992.3316" y2="992.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.2500000000001" x2="278.2500000000001" y1="1020.25" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.2500000000001" x2="188.2500000000001" y1="992.3316" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.2500000000001" x2="278.2500000000001" y1="992.3316" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.25" x2="368.25" y1="992.3316" y2="992.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.25" x2="368.25" y1="1020.25" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.25" x2="278.25" y1="992.3316" y2="1020.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368.25" x2="368.25" y1="992.3316" y2="1020.25"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.25,945.25) scale(1,1) translate(0,0)" writing-mode="lr" x="53.25" xml:space="preserve" y="951.25" zvalue="46">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50.25,979.25) scale(1,1) translate(0,0)" writing-mode="lr" x="50.25" xml:space="preserve" y="985.25" zvalue="47">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.25,979.25) scale(1,1) translate(0,0)" writing-mode="lr" x="232.25" xml:space="preserve" y="985.25" zvalue="48">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.25,1007.25) scale(1,1) translate(0,0)" writing-mode="lr" x="49.25" xml:space="preserve" y="1013.25" zvalue="49">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231.25,1007.25) scale(1,1) translate(0,0)" writing-mode="lr" x="231.25" xml:space="preserve" y="1013.25" zvalue="50">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.75,639.75) scale(1,1) translate(0,0)" writing-mode="lr" x="73.75" xml:space="preserve" y="644.25" zvalue="53">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,169.649,197.091) scale(1,1) translate(0,0)" writing-mode="lr" x="169.65" xml:space="preserve" y="201.59" zvalue="54">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,274.649,197.091) scale(1,1) translate(0,0)" writing-mode="lr" x="274.65" xml:space="preserve" y="201.59" zvalue="55">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,323.304,979.25) scale(1,1) translate(0,0)" writing-mode="lr" x="323.3" xml:space="preserve" y="985.25" zvalue="66">20200911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1115.71,773.585) scale(1,1) translate(0,0)" writing-mode="lr" x="1115.71" xml:space="preserve" y="778.09" zvalue="94">062</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1086.64,960.105) scale(1,1) translate(0,0)" writing-mode="lr" x="1086.637402995043" xml:space="preserve" y="964.6047537717054" zvalue="98">2号机  10MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,671,848.504) scale(1,1) translate(0,0)" writing-mode="lr" x="671" xml:space="preserve" y="853" zvalue="111">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,804.93,849.504) scale(1,1) translate(0,0)" writing-mode="lr" x="804.9299999999999" xml:space="preserve" y="854" zvalue="125">0912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,883.549,849.504) scale(1,1) translate(0,0)" writing-mode="lr" x="883.55" xml:space="preserve" y="854" zvalue="129">0913</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,995.273,849.504) scale(1,1) translate(0,0)" writing-mode="lr" x="995.27" xml:space="preserve" y="854" zvalue="133">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1119.94,849.504) scale(1,1) translate(0,0)" writing-mode="lr" x="1119.94" xml:space="preserve" y="854" zvalue="141">0922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1205.75,847.713) scale(1,1) translate(1.05671e-12,0)" writing-mode="lr" x="1205.75" xml:space="preserve" y="852.21" zvalue="145">0923</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1376.63,748.341) scale(1,1) translate(0,0)" writing-mode="lr" x="1376.63" xml:space="preserve" y="752.84" zvalue="153">063</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1433.89,896.888) scale(1,1) translate(0,0)" writing-mode="lr" x="1433.89" xml:space="preserve" y="901.39" zvalue="155">461</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="291" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1570.48,898.299) scale(1,1) translate(0,0)" writing-mode="lr" x="1570.48" xml:space="preserve" y="902.8" zvalue="176">462</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1653.56,894.173) scale(1,1) translate(0,0)" writing-mode="lr" x="1653.56" xml:space="preserve" y="898.67" zvalue="180">463</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1083.65,409.07) scale(1,1) translate(0,0)" writing-mode="lr" x="1083.65" xml:space="preserve" y="413.57" zvalue="191">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1092.01,464.88) scale(1,1) translate(0,0)" writing-mode="lr" x="1092.01" xml:space="preserve" y="469.38" zvalue="193">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1029.64,455.032) scale(1,1) translate(0,0)" writing-mode="lr" x="1029.64" xml:space="preserve" y="459.53" zvalue="202">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="168" stroke="rgb(255,255,255)" text-anchor="middle" x="1147.3203125" xml:space="preserve" y="554.8419842371193" zvalue="206">#1主变 </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="168" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1147.3203125" xml:space="preserve" y="570.8419842371193" zvalue="206">31.5MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1052.74,643.959) scale(1,1) translate(0,0)" writing-mode="lr" x="1052.74" xml:space="preserve" y="648.46" zvalue="218">0011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="196" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,673.532,324.318) scale(1,1) translate(0,0)" writing-mode="lr" x="673.53" xml:space="preserve" y="328.82" zvalue="221">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="205" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1195.95,559.965) scale(1,1) translate(0,-1.2245e-13)" writing-mode="lr" x="1195.95" xml:space="preserve" y="564.46" zvalue="227">1010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="209" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,719.451,555.5) scale(1,1) translate(0,0)" writing-mode="lr" x="719.45" xml:space="preserve" y="560" zvalue="233">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,759.5,638.222) scale(1,1) translate(0,0)" writing-mode="lr" x="759.5" xml:space="preserve" y="642.72" zvalue="236">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="218" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,823.556,387.667) scale(1,1) translate(0,0)" writing-mode="lr" x="823.5599999999999" xml:space="preserve" y="392.17" zvalue="245">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="217" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,855.333,422.944) scale(1,1) translate(0,0)" writing-mode="lr" x="855.33" xml:space="preserve" y="427.44" zvalue="247">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="216" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,800.222,511) scale(1,1) translate(0,0)" writing-mode="lr" x="800.22" xml:space="preserve" y="515.5" zvalue="253">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="261" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,892.333,271.111) scale(1,1) translate(0,0)" writing-mode="lr" x="892.33" xml:space="preserve" y="275.61" zvalue="264">161</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,883.556,318.667) scale(1,1) translate(0,0)" writing-mode="lr" x="883.5599999999999" xml:space="preserve" y="323.17" zvalue="266">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,885.222,210.667) scale(1,1) translate(0,0)" writing-mode="lr" x="885.22" xml:space="preserve" y="215.17" zvalue="269">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,862.086,90.4444) scale(1,1) translate(1.82318e-13,0)" writing-mode="lr" x="862.09" xml:space="preserve" y="94.94" zvalue="274">110kV备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,945.056,308.778) scale(1,1) translate(0,0)" writing-mode="lr" x="945.0599999999999" xml:space="preserve" y="313.28" zvalue="276">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="256" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,944.056,258.333) scale(1,1) translate(0,0)" writing-mode="lr" x="944.0599999999999" xml:space="preserve" y="262.83" zvalue="278">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,949.056,203.889) scale(1,1) translate(0,0)" writing-mode="lr" x="949.0599999999999" xml:space="preserve" y="208.39" zvalue="280">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1336.45,843.361) scale(1,1) translate(0,0)" writing-mode="lr" x="1336.446188184859" xml:space="preserve" y="847.8611111111111" zvalue="288">1号站用变 0.315MVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="290" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1541.94,814.293) scale(1,1) translate(0,0)" writing-mode="lr" x="1541.94" xml:space="preserve" y="818.79" zvalue="289">后端无出线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="301" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1693.08,837.966) scale(1,1) translate(0,0)" writing-mode="lr" x="1693.078993015819" xml:space="preserve" y="842.4664351851852" zvalue="298">2号站用变  0.05MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="310" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1256.22,270) scale(1,1) translate(0,0)" writing-mode="lr" x="1256.22" xml:space="preserve" y="274.5" zvalue="307">162</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="309" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1247.44,317.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1247.44" xml:space="preserve" y="322.06" zvalue="309">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="307" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1249.11,187.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1249.11" xml:space="preserve" y="191.83" zvalue="312">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="305" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1230,89.3333) scale(1,1) translate(0,0)" writing-mode="lr" x="1230" xml:space="preserve" y="93.83" zvalue="316">110kV南葫线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="304" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1308.94,307.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1308.94" xml:space="preserve" y="312.17" zvalue="318">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1317.39,245) scale(1,1) translate(0,0)" writing-mode="lr" x="1317.39" xml:space="preserve" y="249.5" zvalue="322">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1628,725) scale(1,1) translate(0,0)" writing-mode="lr" x="1628" xml:space="preserve" y="729.5" zvalue="343">T接35kV旧城变10kV浑水沟线</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="262" y2="262"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="288" y2="288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="262" y2="288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="262" y2="288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="262" y2="262"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="288" y2="288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="262" y2="288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="262" y2="288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="288" y2="288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="312.25" y2="312.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="288" y2="312.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="288" y2="312.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="288" y2="288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="312.25" y2="312.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="288" y2="312.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="288" y2="312.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="312.25" y2="312.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="335" y2="335"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="312.25" y2="335"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="312.25" y2="335"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="312.25" y2="312.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="335" y2="335"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="312.25" y2="335"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="312.25" y2="335"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="335" y2="335"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="357.75" y2="357.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="335" y2="357.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="335" y2="357.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="335" y2="335"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="357.75" y2="357.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="335" y2="357.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="335" y2="357.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="357.75" y2="357.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="380.5" y2="380.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="357.75" y2="380.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="357.75" y2="380.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="357.75" y2="357.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="380.5" y2="380.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="357.75" y2="380.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="357.75" y2="380.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="380.5" y2="380.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="403.25" y2="403.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="380.5" y2="403.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="380.5" y2="403.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="380.5" y2="380.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="403.25" y2="403.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="380.5" y2="403.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="380.5" y2="403.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="403.25" y2="403.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="426" y2="426"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="403.25" y2="426"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="403.25" y2="426"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="403.25" y2="403.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="426" y2="426"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="403.25" y2="426"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="403.25" y2="426"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,54.5,275) scale(1,1) translate(0,0)" writing-mode="lr" x="12" xml:space="preserve" y="279.5" zvalue="390">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235,275) scale(1,1) translate(0,0)" writing-mode="lr" x="192.5" xml:space="preserve" y="279.5" zvalue="391">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.6875,348.25) scale(1,1) translate(0,0)" writing-mode="lr" x="57.69" xml:space="preserve" y="352.75" zvalue="392">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236.625,347) scale(1,1) translate(0,0)" writing-mode="lr" x="236.63" xml:space="preserve" y="351.5" zvalue="393">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,53.5,301) scale(1,1) translate(0,0)" writing-mode="lr" x="11" xml:space="preserve" y="305.5" zvalue="399">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,234,301) scale(1,1) translate(0,0)" writing-mode="lr" x="191.5" xml:space="preserve" y="305.5" zvalue="400">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.6875,394.25) scale(1,1) translate(0,0)" writing-mode="lr" x="54.69" xml:space="preserve" y="398.75" zvalue="403">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.688,393.25) scale(1,1) translate(0,0)" writing-mode="lr" x="222.69" xml:space="preserve" y="397.75" zvalue="405">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.6875,417.25) scale(1,1) translate(0,0)" writing-mode="lr" x="54.69" xml:space="preserve" y="421.75" zvalue="406">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.688,416.25) scale(1,1) translate(0,0)" writing-mode="lr" x="222.69" xml:space="preserve" y="420.75" zvalue="407">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,53.5,324) scale(1,1) translate(0,0)" writing-mode="lr" x="11" xml:space="preserve" y="328.5" zvalue="408">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,233.5,323) scale(1,1) translate(0,0)" writing-mode="lr" x="191" xml:space="preserve" y="327.5" zvalue="410">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.5,945) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="951" zvalue="416">NanDiHe-01-2011</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="42" y="184" zvalue="387"/></g>
 </g>
 <g id="BreakerClass">
  <g id="157">
   <use class="kv10" height="20" transform="rotate(0,772.498,774.673) scale(2.17559,2.17559) translate(-411.544,-406.842)" width="10" x="761.6201573688402" xlink:href="#Breaker:小车断路器_0" y="752.9173635942258" zvalue="2"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925211062275" ObjectName="#1发电机061断路器"/>
   <cge:TPSR_Ref TObjectID="6473925211062275"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,772.498,774.673) scale(2.17559,2.17559) translate(-411.544,-406.842)" width="10" x="761.6201573688402" y="752.9173635942258"/></g>
  <g id="78">
   <use class="kv10" height="20" transform="rotate(0,1088.52,774.673) scale(2.17559,2.17559) translate(-582.308,-406.842)" width="10" x="1077.642075956942" xlink:href="#Breaker:小车断路器_0" y="752.9173635942258" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925211127811" ObjectName="#2发电机062断路器"/>
   <cge:TPSR_Ref TObjectID="6473925211127811"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1088.52,774.673) scale(2.17559,2.17559) translate(-582.308,-406.842)" width="10" x="1077.642075956942" y="752.9173635942258"/></g>
  <g id="145">
   <use class="kv10" height="20" transform="rotate(0,1409.75,747.445) scale(1.79049,1.79049) translate(-618.445,-322.088)" width="10" x="1400.798900966311" xlink:href="#Breaker:小车断路器_0" y="729.5405636208372" zvalue="152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925211258883" ObjectName="#1站用变10kV侧063断路器"/>
   <cge:TPSR_Ref TObjectID="6473925211258883"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1409.75,747.445) scale(1.79049,1.79049) translate(-618.445,-322.088)" width="10" x="1400.798900966311" y="729.5405636208372"/></g>
  <g id="144">
   <use class="v400" height="20" transform="rotate(0,1410.62,895.609) scale(1.79049,1.60534) translate(-618.827,-331.662)" width="10" x="1401.664674067747" xlink:href="#Breaker:小车断路器_0" y="879.5555632615849" zvalue="154"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925211193347" ObjectName="#1站用变0.4kV侧461断路器"/>
   <cge:TPSR_Ref TObjectID="6473925211193347"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1410.62,895.609) scale(1.79049,1.60534) translate(-618.827,-331.662)" width="10" x="1401.664674067747" y="879.5555632615849"/></g>
  <g id="148">
   <use class="v400" height="20" transform="rotate(0,1547.87,895.609) scale(1.79049,1.60534) translate(-679.421,-331.662)" width="10" x="1538.912894961572" xlink:href="#Breaker:小车断路器_0" y="879.5555632582827" zvalue="175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925211324419" ObjectName="0.4kV侧462断路器"/>
   <cge:TPSR_Ref TObjectID="6473925211324419"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1547.87,895.609) scale(1.79049,1.60534) translate(-679.421,-331.662)" width="10" x="1538.912894961572" y="879.5555632582827"/></g>
  <g id="154">
   <use class="v400" height="15" transform="rotate(0,1634.34,895.07) scale(1.67859,1.50501) translate(-657.986,-296.555)" width="8" x="1627.627668659265" xlink:href="#Breaker:kg123_0" y="883.7822518478623" zvalue="179"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925211389955" ObjectName="#2站用变0.4kV侧463断路器"/>
   <cge:TPSR_Ref TObjectID="6473925211389955"/></metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,1634.34,895.07) scale(1.67859,1.50501) translate(-657.986,-296.555)" width="8" x="1627.627668659265" y="883.7822518478623"/></g>
  <g id="187">
   <use class="kv110" height="20" transform="rotate(0,1073.41,465.775) scale(1.09419,0.994718) translate(-91.9299,2.42045)" width="10" x="1067.93678363881" xlink:href="#Breaker:开关_0" y="455.8276243792908" zvalue="192"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925211455491" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925211455491"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1073.41,465.775) scale(1.09419,0.994718) translate(-91.9299,2.42045)" width="10" x="1067.93678363881" y="455.8276243792908"/></g>
  <g id="275">
   <use class="kv110" height="20" transform="rotate(0,871,272.111) scale(1.22222,1.11111) translate(-157.253,-26.1)" width="10" x="864.8888888888889" xlink:href="#Breaker:开关_0" y="261" zvalue="263"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925211521027" ObjectName="110kV备用线161断路器"/>
   <cge:TPSR_Ref TObjectID="6473925211521027"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,871,272.111) scale(1.22222,1.11111) translate(-157.253,-26.1)" width="10" x="864.8888888888889" y="261"/></g>
  <g id="326">
   <use class="kv110" height="20" transform="rotate(0,1234.89,271) scale(1.22222,1.11111) translate(-223.414,-25.9889)" width="10" x="1228.777777777778" xlink:href="#Breaker:开关_0" y="259.8888888888888" zvalue="306"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925211717635" ObjectName="110kV南葫线162断路器"/>
   <cge:TPSR_Ref TObjectID="6473925211717635"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1234.89,271) scale(1.22222,1.11111) translate(-223.414,-25.9889)" width="10" x="1228.777777777778" y="259.8888888888888"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="9">
   <path class="kv10" d="M 772.5 754.55 L 772.5 692.28" stroke-width="1" zvalue="4"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="10@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 772.5 754.55 L 772.5 692.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv10" d="M 1088.52 754.55 L 1088.52 692.28" stroke-width="1" zvalue="95"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@0" LinkObjectIDznd="10@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1088.52 754.55 L 1088.52 692.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv10" d="M 772.49 811.1 L 700.68 811.1 L 700.69 835.18" stroke-width="1" zvalue="112"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112" LinkObjectIDznd="80@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 772.49 811.1 L 700.68 811.1 L 700.69 835.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv10" d="M 700.69 865.62 L 700.69 901.33" stroke-width="1" zvalue="113"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@1" LinkObjectIDznd="124@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 700.69 865.62 L 700.69 901.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv10" d="M 772.49 813.69 L 826.91 813.69 L 826.92 835.18" stroke-width="1" zvalue="125"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112" LinkObjectIDznd="92@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 772.49 813.69 L 826.91 813.69 L 826.92 835.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv10" d="M 826.92 865.62 L 826.92 906.39" stroke-width="1" zvalue="126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@1" LinkObjectIDznd="122@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 826.92 865.62 L 826.92 906.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv10" d="M 826.91 813.69 L 857.34 813.69 L 857.35 835.18" stroke-width="1" zvalue="129"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93" LinkObjectIDznd="95@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 826.91 813.69 L 857.34 813.69 L 857.35 835.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv10" d="M 857.35 865.62 L 857.35 903.97" stroke-width="1" zvalue="130"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@1" LinkObjectIDznd="123@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 857.35 865.62 L 857.35 903.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv10" d="M 1017.6 865.62 L 1017.6 901.33" stroke-width="1" zvalue="134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@1" LinkObjectIDznd="74@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1017.6 865.62 L 1017.6 901.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv10" d="M 1142.94 865.62 L 1143 906.39" stroke-width="1" zvalue="142"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@1" LinkObjectIDznd="72@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1142.94 865.62 L 1143 906.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv10" d="M 1174.27 865.62 L 1174.27 903.97" stroke-width="1" zvalue="146"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@1" LinkObjectIDznd="73@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1174.27 865.62 L 1174.27 903.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv10" d="M 772.47 886.88 L 772.5 794.25" stroke-width="1" zvalue="147"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@0" LinkObjectIDznd="157@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 772.47 886.88 L 772.5 794.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv10" d="M 1088.49 886.88 L 1088.52 794.25" stroke-width="1" zvalue="148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75@0" LinkObjectIDznd="78@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1088.49 886.88 L 1088.52 794.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv10" d="M 1409.75 730.88 L 1409.75 692.28" stroke-width="1" zvalue="173"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="10@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1409.75 730.88 L 1409.75 692.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="184">
   <path class="kv110" d="M 1073.47 420.72 L 1073.37 456.26" stroke-width="1" zvalue="198"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@1" LinkObjectIDznd="187@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1073.47 420.72 L 1073.37 456.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv110" d="M 1039.34 444.14 L 1073.4 444.14" stroke-width="1" zvalue="203"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@0" LinkObjectIDznd="184" MaxPinNum="2"/>
   </metadata>
  <path d="M 1039.34 444.14 L 1073.4 444.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="kv110" d="M 1073.48 475.27 L 1073.48 508.06" stroke-width="1" zvalue="216"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@1" LinkObjectIDznd="178@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1073.48 475.27 L 1073.48 508.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="kv10" d="M 1073.33 629.63 L 1073.43 587.67" stroke-width="1" zvalue="218"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="178@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1073.33 629.63 L 1073.43 587.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv10" d="M 1073.33 660.07 L 1073.33 692.28" stroke-width="1" zvalue="219"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@1" LinkObjectIDznd="10@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1073.33 660.07 L 1073.33 692.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv110" d="M 1073.5 351.88 L 1073.5 399.38" stroke-width="1" zvalue="221"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@0" LinkObjectIDznd="188@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1073.5 351.88 L 1073.5 399.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv10" d="M 734.4 608.65 L 734.4 626.21" stroke-width="1" zvalue="240"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="306@0" LinkObjectIDznd="308@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 734.4 608.65 L 734.4 626.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv10" d="M 735.43 650.04 L 735.43 692.28" stroke-width="1" zvalue="241"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="308@0" LinkObjectIDznd="10@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 735.43 650.04 L 735.43 692.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv10" d="M 704.97 641.37 L 704.97 670 L 735.43 670" stroke-width="1" zvalue="242"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="313@0" LinkObjectIDznd="214" MaxPinNum="2"/>
   </metadata>
  <path d="M 704.97 641.37 L 704.97 670 L 735.43 670" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="229">
   <path class="kv110" d="M 798.15 376.65 L 798.15 351.88" stroke-width="1" zvalue="256"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="228@1" LinkObjectIDznd="195@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 798.15 376.65 L 798.15 351.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="230">
   <path class="kv110" d="M 798.14 450.17 L 798.12 400.48" stroke-width="1" zvalue="257"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="222@0" LinkObjectIDznd="228@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 798.14 450.17 L 798.12 400.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="249">
   <path class="kv110" d="M 844.5 406.67 L 798.13 406.67" stroke-width="1" zvalue="258"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="227@0" LinkObjectIDznd="230" MaxPinNum="2"/>
   </metadata>
  <path d="M 844.5 406.67 L 798.13 406.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv110" d="M 843.03 451.63 L 843.03 434 L 798.13 434" stroke-width="1" zvalue="259"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="230" MaxPinNum="2"/>
   </metadata>
  <path d="M 843.03 451.63 L 843.03 434 L 798.13 434" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="272">
   <path class="kv110" d="M 870.9 331.48 L 870.9 351.88" stroke-width="1" zvalue="268"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="195@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 870.9 331.48 L 870.9 351.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="271">
   <path class="kv110" d="M 870.93 307.65 L 870.93 282.72" stroke-width="1" zvalue="270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@1" LinkObjectIDznd="275@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 870.93 307.65 L 870.93 282.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="kv110" d="M 870.96 261.48 L 870.9 223.48" stroke-width="1" zvalue="271"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="275@0" LinkObjectIDznd="273@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 870.96 261.48 L 870.9 223.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="276">
   <path class="kv110" d="M 936.78 189.39 L 870.93 189.39" stroke-width="1" zvalue="284"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265@0" LinkObjectIDznd="282" MaxPinNum="2"/>
   </metadata>
  <path d="M 936.78 189.39 L 870.93 189.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="277">
   <path class="kv110" d="M 933.22 243.83 L 870.93 243.83" stroke-width="1" zvalue="285"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="266@0" LinkObjectIDznd="270" MaxPinNum="2"/>
   </metadata>
  <path d="M 933.22 243.83 L 870.93 243.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="kv110" d="M 937.78 294.28 L 870.93 294.28" stroke-width="1" zvalue="286"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@0" LinkObjectIDznd="271" MaxPinNum="2"/>
   </metadata>
  <path d="M 937.78 294.28 L 870.93 294.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="282">
   <path class="kv110" d="M 870.93 147.22 L 870.93 199.65" stroke-width="1" zvalue="289"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="269@0" LinkObjectIDznd="273@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 870.93 147.22 L 870.93 199.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="286">
   <path class="kv10" d="M 1410.6 822.22 L 1410.6 763.56" stroke-width="1" zvalue="293"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@0" LinkObjectIDznd="145@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1410.6 822.22 L 1410.6 763.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="287">
   <path class="v400" d="M 1410.62 861.34 L 1410.62 880.76" stroke-width="1" zvalue="294"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@1" LinkObjectIDznd="144@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1410.62 861.34 L 1410.62 880.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="289">
   <path class="v400" d="M 1546.39 853 L 1546.39 880.76" stroke-width="1" zvalue="295"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="281@0" LinkObjectIDznd="148@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1546.39 853 L 1546.39 880.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="293">
   <path class="v400" d="M 1633.62 861.34 L 1633.62 884.53" stroke-width="1" zvalue="298"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="292@1" LinkObjectIDznd="154@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1633.62 861.34 L 1633.62 884.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="297">
   <path class="kv10" d="M 1634.46 805.06 L 1634.46 821.7" stroke-width="1" zvalue="302"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="296@0" LinkObjectIDznd="292@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1634.46 805.06 L 1634.46 821.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="323">
   <path class="kv110" d="M 1234.79 330.37 L 1234.79 351.88" stroke-width="1" zvalue="311"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="325@0" LinkObjectIDznd="195@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.79 330.37 L 1234.79 351.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="322">
   <path class="kv110" d="M 1234.82 306.54 L 1234.82 281.61" stroke-width="1" zvalue="313"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="325@1" LinkObjectIDznd="326@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.82 306.54 L 1234.82 281.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="321">
   <path class="kv110" d="M 1234.85 260.37 L 1234.79 200.15" stroke-width="1" zvalue="314"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="326@0" LinkObjectIDznd="324@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.85 260.37 L 1234.79 200.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="312">
   <path class="kv110" d="M 1303.11 293.17 L 1234.82 293.17" stroke-width="1" zvalue="325"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="319@0" LinkObjectIDznd="322" MaxPinNum="2"/>
   </metadata>
  <path d="M 1303.11 293.17 L 1234.82 293.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv110" d="M 1234.82 146.88 L 1234.82 176.32" stroke-width="1" zvalue="326"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="320@0" LinkObjectIDznd="324@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1234.82 146.88 L 1234.82 176.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="327">
   <path class="kv110" d="M 1305.11 230.5 L 1234.82 230.5" stroke-width="1" zvalue="327"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="317@0" LinkObjectIDznd="321" MaxPinNum="2"/>
   </metadata>
  <path d="M 1305.11 230.5 L 1234.82 230.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="329">
   <path class="kv10" d="M 1174.27 835.18 L 1174.26 815.78 L 1088.51 815.78" stroke-width="1" zvalue="329"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="113" MaxPinNum="2"/>
   </metadata>
  <path d="M 1174.27 835.18 L 1174.26 815.78 L 1088.51 815.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="330">
   <path class="kv10" d="M 1142.94 835.18 L 1142.94 815.78" stroke-width="1" zvalue="330"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="329" MaxPinNum="2"/>
   </metadata>
  <path d="M 1142.94 835.18 L 1142.94 815.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="331">
   <path class="kv10" d="M 1017.6 835.18 L 1017.59 813.56 L 1088.51 813.56" stroke-width="1" zvalue="331"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="113" MaxPinNum="2"/>
   </metadata>
  <path d="M 1017.6 835.18 L 1017.59 813.56 L 1088.51 813.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv10" d="M 1634.65 776.72 L 1634.65 756.58" stroke-width="1" zvalue="343"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="296@1" LinkObjectIDznd="6@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1634.65 776.72 L 1634.65 756.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="v400" d="M 1410.62 910.06 L 1410.62 958 L 1634.34 958 L 1634.34 905.6" stroke-width="1" zvalue="344"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="144@1" LinkObjectIDznd="154@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1410.62 910.06 L 1410.62 958 L 1634.34 958 L 1634.34 905.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="v400" d="M 1547.87 910.06 L 1547.87 958" stroke-width="1" zvalue="345"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="148@1" LinkObjectIDznd="12" MaxPinNum="2"/>
   </metadata>
  <path d="M 1547.87 910.06 L 1547.87 958" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv110" d="M 1219.47 571.72 L 1220.57 599.22" stroke-width="1" zvalue="346"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@1" LinkObjectIDznd="219@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1219.47 571.72 L 1220.57 599.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv110" d="M 1073.45 531.01 L 1219.5 531.01 L 1219.5 550.38" stroke-width="1" zvalue="347"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@2" LinkObjectIDznd="202@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1073.45 531.01 L 1219.5 531.01 L 1219.5 550.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="151">
   <use class="kv10" height="30" transform="rotate(0,772.466,910.951) scale(1.63169,1.63169) translate(-289.576,-343.189)" width="30" x="747.9905779681756" xlink:href="#Generator:发电机_0" y="886.4759152805962" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454749323266" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192454749323266"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,772.466,910.951) scale(1.63169,1.63169) translate(-289.576,-343.189)" width="30" x="747.9905779681756" y="886.4759152805962"/></g>
  <g id="75">
   <use class="kv10" height="30" transform="rotate(0,1088.49,910.951) scale(1.63169,1.63169) translate(-411.92,-343.189)" width="30" x="1064.012496556277" xlink:href="#Generator:发电机_0" y="886.4759152805962" zvalue="97"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454749585410" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192454749585410"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1088.49,910.951) scale(1.63169,1.63169) translate(-411.92,-343.189)" width="30" x="1064.012496556277" y="886.4759152805962"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="124">
   <use class="kv10" height="30" transform="rotate(0,700.547,916.453) scale(1.08779,1.08779) translate(-55.2225,-72.6478)" width="30" x="684.2296226164864" xlink:href="#Accessory:带熔断器35kVPT11_0" y="900.1358584514667" zvalue="14"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454749257730" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,700.547,916.453) scale(1.08779,1.08779) translate(-55.2225,-72.6478)" width="30" x="684.2296226164864" y="900.1358584514667"/></g>
  <g id="123">
   <use class="kv10" height="40" transform="rotate(0,857.252,924.099) scale(1.08779,-1.08779) translate(-67.8698,-1771.86)" width="30" x="840.9350780336603" xlink:href="#Accessory:带熔断器的线路PT1_0" y="902.3427093416093" zvalue="15"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454749192194" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,857.252,924.099) scale(1.08779,-1.08779) translate(-67.8698,-1771.86)" width="30" x="840.9350780336603" y="902.3427093416093"/></g>
  <g id="122">
   <use class="kv10" height="29" transform="rotate(0,826.917,921.891) scale(1.08779,-1.08779) translate(-65.4215,-1768.11)" width="30" x="810.5996989327974" xlink:href="#Accessory:PT12321_0" y="906.1180015592805" zvalue="16"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454749126658" ObjectName="#1发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,826.917,921.891) scale(1.08779,-1.08779) translate(-65.4215,-1768.11)" width="30" x="810.5996989327974" y="906.1180015592805"/></g>
  <g id="74">
   <use class="kv10" height="30" transform="rotate(0,1016.57,916.453) scale(1.08779,1.08779) translate(-80.7278,-72.6478)" width="30" x="1000.251541204588" xlink:href="#Accessory:带熔断器35kVPT11_0" y="900.1358584514667" zvalue="98"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454749519874" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1016.57,916.453) scale(1.08779,1.08779) translate(-80.7278,-72.6478)" width="30" x="1000.251541204588" y="900.1358584514667"/></g>
  <g id="73">
   <use class="kv10" height="40" transform="rotate(0,1173.27,924.099) scale(1.08779,-1.08779) translate(-93.3752,-1771.86)" width="30" x="1156.956996621762" xlink:href="#Accessory:带熔断器的线路PT1_0" y="902.3427093416093" zvalue="99"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454749454338" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1173.27,924.099) scale(1.08779,-1.08779) translate(-93.3752,-1771.86)" width="30" x="1156.956996621762" y="902.3427093416093"/></g>
  <g id="72">
   <use class="kv10" height="29" transform="rotate(0,1143,921.891) scale(1.08779,-1.08779) translate(-90.932,-1768.11)" width="30" x="1126.684939834746" xlink:href="#Accessory:PT12321_0" y="906.1180015592805" zvalue="100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454749388802" ObjectName="#2发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1143,921.891) scale(1.08779,-1.08779) translate(-90.932,-1768.11)" width="30" x="1126.684939834746" y="906.1180015592805"/></g>
  <g id="219">
   <use class="kv110" height="26" transform="rotate(0,1220.54,610.291) scale(0.895246,0.895246) translate(142.188,70.0491)" width="12" x="1215.165670367207" xlink:href="#Accessory:避雷器1_0" y="598.6532877882158" zvalue="208"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454750044162" ObjectName="#1主变避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1220.54,610.291) scale(0.895246,0.895246) translate(142.188,70.0491)" width="12" x="1215.165670367207" y="598.6532877882158"/></g>
  <g id="306">
   <use class="kv10" height="30" transform="rotate(0,734.451,594) scale(1,-1) translate(0,-1188)" width="30" x="719.4512938045621" xlink:href="#Accessory:PT789_0" y="579" zvalue="232"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454750568450" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,734.451,594) scale(1,-1) translate(0,-1188)" width="30" x="719.4512938045621" y="579"/></g>
  <g id="313">
   <use class="kv10" height="26" transform="rotate(180,705,629) scale(1,1) translate(0,0)" width="12" x="699" xlink:href="#Accessory:避雷器1_0" y="616" zvalue="238"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454750437378" ObjectName="10kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,705,629) scale(1,1) translate(0,0)" width="12" x="699" y="616"/></g>
  <g id="222">
   <use class="kv110" height="30" transform="rotate(0,798.222,471.25) scale(1.73333,1.51667) translate(-326.709,-152.786)" width="30" x="772.2222290100829" xlink:href="#Accessory:带熔断器35kVPT11_0" y="448.5" zvalue="252"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454750699522" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,798.222,471.25) scale(1.73333,1.51667) translate(-326.709,-152.786)" width="30" x="772.2222290100829" y="448.5"/></g>
  <g id="221">
   <use class="kv110" height="26" transform="rotate(0,843,464) scale(1,1) translate(0,0)" width="12" x="837" xlink:href="#Accessory:避雷器1_0" y="451" zvalue="254"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454750633986" ObjectName="110kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,843,464) scale(1,1) translate(0,0)" width="12" x="837" y="451"/></g>
  <g id="281">
   <use class="v400" height="13" transform="rotate(0,1546.39,840.702) scale(1.86869,-1.86869) translate(-714.084,-1284.94)" width="11" x="1536.111111111111" xlink:href="#Accessory:空挂线路_0" y="828.5555555555554" zvalue="288"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454751551490" ObjectName="后端无出线"/>
   </metadata>
  <rect fill="white" height="13" opacity="0" stroke="white" transform="rotate(0,1546.39,840.702) scale(1.86869,-1.86869) translate(-714.084,-1284.94)" width="11" x="1536.111111111111" y="828.5555555555554"/></g>
  <g id="6">
   <use class="kv10" height="13" transform="rotate(0,1635,750) scale(1,-1) translate(0,-1500)" width="11" x="1629.5" xlink:href="#Accessory:空挂线路_0" y="743.5" zvalue="342"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454752141314" ObjectName="T接35kV旧城变10kV浑水沟线"/>
   </metadata>
  <rect fill="white" height="13" opacity="0" stroke="white" transform="rotate(0,1635,750) scale(1,-1) translate(0,-1500)" width="11" x="1629.5" y="743.5"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="10">
   <path class="kv10" d="M 661.07 692.28 L 1617.42 692.28" stroke-width="6" zvalue="18"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674420064259" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674420064259"/></metadata>
  <path d="M 661.07 692.28 L 1617.42 692.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv110" d="M 663.09 351.88 L 1446.43 351.88" stroke-width="6" zvalue="220"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674420129795" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674420129795"/></metadata>
  <path d="M 663.09 351.88 L 1446.43 351.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="DisconnectorClass">
  <g id="80">
   <use class="kv10" height="36" transform="rotate(0,700.687,850.399) scale(0.895246,0.895246) translate(81.2549,97.6206)" width="14" x="694.4201537147731" xlink:href="#Disconnector:联体小车刀闸2_0" y="834.2843723313406" zvalue="110"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454749650946" ObjectName="#1发电机0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454749650946"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,700.687,850.399) scale(0.895246,0.895246) translate(81.2549,97.6206)" width="14" x="694.4201537147731" y="834.2843723313406"/></g>
  <g id="92">
   <use class="kv10" height="36" transform="rotate(0,826.917,850.399) scale(0.895246,0.895246) translate(96.0252,97.6206)" width="14" x="820.6498719043561" xlink:href="#Disconnector:联体小车刀闸2_0" y="834.2843723313406" zvalue="124"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454749716482" ObjectName="#1发电机0912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454749716482"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,826.917,850.399) scale(0.895246,0.895246) translate(96.0252,97.6206)" width="14" x="820.6498719043561" y="834.2843723313406"/></g>
  <g id="95">
   <use class="kv10" height="36" transform="rotate(0,857.355,850.399) scale(0.895246,0.895246) translate(99.5868,97.6206)" width="14" x="851.0882436663813" xlink:href="#Disconnector:联体小车刀闸2_0" y="834.2843723313406" zvalue="128"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454749782018" ObjectName="#1发电机0913隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454749782018"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,857.355,850.399) scale(0.895246,0.895246) translate(99.5868,97.6206)" width="14" x="851.0882436663813" y="834.2843723313406"/></g>
  <g id="98">
   <use class="kv10" height="36" transform="rotate(0,1017.6,850.399) scale(0.895246,0.895246) translate(118.338,97.6206)" width="14" x="1011.33731853117" xlink:href="#Disconnector:联体小车刀闸2_0" y="834.2843723313406" zvalue="132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454749847554" ObjectName="#2发电机0921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454749847554"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1017.6,850.399) scale(0.895246,0.895246) translate(118.338,97.6206)" width="14" x="1011.33731853117" y="834.2843723313406"/></g>
  <g id="104">
   <use class="kv10" height="36" transform="rotate(0,1142.94,850.399) scale(0.895246,0.895246) translate(133.003,97.6206)" width="14" x="1136.671790492456" xlink:href="#Disconnector:联体小车刀闸2_0" y="834.2843723313406" zvalue="140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454749913090" ObjectName="#2发电机0922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454749913090"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1142.94,850.399) scale(0.895246,0.895246) translate(133.003,97.6206)" width="14" x="1136.671790492456" y="834.2843723313406"/></g>
  <g id="107">
   <use class="kv10" height="36" transform="rotate(0,1174.27,850.399) scale(0.895246,0.895246) translate(136.67,97.6206)" width="14" x="1168.005408482778" xlink:href="#Disconnector:联体小车刀闸2_0" y="834.2843723313406" zvalue="144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454749978626" ObjectName="#2发电机0923隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454749978626"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1174.27,850.399) scale(0.895246,0.895246) translate(136.67,97.6206)" width="14" x="1168.005408482778" y="834.2843723313406"/></g>
  <g id="188">
   <use class="kv110" height="30" transform="rotate(0,1073.41,409.965) scale(0.994718,0.72946) translate(5.6602,147.989)" width="15" x="1065.947347564075" xlink:href="#Disconnector:刀闸_0" y="399.0230572160561" zvalue="190"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454750240770" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454750240770"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1073.41,409.965) scale(0.994718,0.72946) translate(5.6602,147.989)" width="15" x="1065.947347564075" y="399.0230572160561"/></g>
  <g id="191">
   <use class="kv10" height="36" transform="rotate(0,1073.33,644.854) scale(0.895246,0.895246) translate(124.859,73.5695)" width="14" x="1067.066396242528" xlink:href="#Disconnector:手车刀闸_0" y="628.7396608515319" zvalue="217"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454752206850" ObjectName="#1主变10kV侧0011刀闸"/>
   <cge:TPSR_Ref TObjectID="6192454752206850"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1073.33,644.854) scale(0.895246,0.895246) translate(124.859,73.5695)" width="14" x="1067.066396242528" y="628.7396608515319"/></g>
  <g id="202">
   <use class="kv110" height="30" transform="rotate(0,1219.41,560.965) scale(0.994718,0.72946) translate(6.43546,203.991)" width="15" x="1211.947347564075" xlink:href="#Disconnector:刀闸_0" y="550.023057216056" zvalue="226"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454750371842" ObjectName="#1主变110kV侧1010隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454750371842"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1219.41,560.965) scale(0.994718,0.72946) translate(6.43546,203.991)" width="15" x="1211.947347564075" y="550.023057216056"/></g>
  <g id="308">
   <use class="kv10" height="30" transform="rotate(0,735.333,638.222) scale(1.11111,-0.814815) translate(-72.7,-1424.27)" width="15" x="727.0000000000001" xlink:href="#Disconnector:刀闸_0" y="626" zvalue="234"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449669496838" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449669496838"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,735.333,638.222) scale(1.11111,-0.814815) translate(-72.7,-1424.27)" width="15" x="727.0000000000001" y="626"/></g>
  <g id="228">
   <use class="kv110" height="30" transform="rotate(0,798.222,388.667) scale(-1.11111,-0.814815) translate(-1515.79,-868.444)" width="15" x="789.8888956705728" xlink:href="#Disconnector:刀闸_0" y="376.4444581137762" zvalue="244"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449669300230" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449669300230"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,798.222,388.667) scale(-1.11111,-0.814815) translate(-1515.79,-868.444)" width="15" x="789.8888956705728" y="376.4444581137762"/></g>
  <g id="274">
   <use class="kv110" height="30" transform="rotate(0,871,319.667) scale(-1.11111,-0.814815) translate(-1654.07,-714.763)" width="15" x="862.6666666666666" xlink:href="#Disconnector:刀闸_0" y="307.4444580078125" zvalue="265"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454751485954" ObjectName="110kV备用线1611隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454751485954"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,871,319.667) scale(-1.11111,-0.814815) translate(-1654.07,-714.763)" width="15" x="862.6666666666666" y="307.4444580078125"/></g>
  <g id="273">
   <use class="kv110" height="30" transform="rotate(0,871,211.667) scale(-1.11111,-0.814815) translate(-1654.07,-474.217)" width="15" x="862.6666666931577" xlink:href="#Disconnector:刀闸_0" y="199.4444444444445" zvalue="267"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454751420418" ObjectName="110kV备用线1616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454751420418"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,871,211.667) scale(-1.11111,-0.814815) translate(-1654.07,-474.217)" width="15" x="862.6666666931577" y="199.4444444444445"/></g>
  <g id="296">
   <use class="kv10" height="30" transform="rotate(0,1634.56,790.333) scale(-1.11111,-1.11111) translate(-3104.82,-1499.97)" width="15" x="1626.222222222222" xlink:href="#Disconnector:令克_0" y="773.6666666666667" zvalue="301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454751617026" ObjectName="2号站用变刀闸"/>
   <cge:TPSR_Ref TObjectID="6192454751617026"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1634.56,790.333) scale(-1.11111,-1.11111) translate(-3104.82,-1499.97)" width="15" x="1626.222222222222" y="773.6666666666667"/></g>
  <g id="325">
   <use class="kv110" height="30" transform="rotate(0,1234.89,318.556) scale(-1.11111,-0.814815) translate(-2345.46,-712.288)" width="15" x="1226.555555555556" xlink:href="#Disconnector:刀闸_0" y="306.3333468967013" zvalue="308"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454752665602" ObjectName="110kV南葫线1621隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454752665602"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1234.89,318.556) scale(-1.11111,-0.814815) translate(-2345.46,-712.288)" width="15" x="1226.555555555556" y="306.3333468967013"/></g>
  <g id="324">
   <use class="kv110" height="30" transform="rotate(0,1234.89,188.333) scale(-1.11111,-0.814815) translate(-2345.46,-422.247)" width="15" x="1226.555555582047" xlink:href="#Disconnector:刀闸_0" y="176.1111111111111" zvalue="310"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454752600066" ObjectName="110kV南葫线1626隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454752600066"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1234.89,188.333) scale(-1.11111,-0.814815) translate(-2345.46,-422.247)" width="15" x="1226.555555582047" y="176.1111111111111"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="181">
   <use class="kv110" height="20" transform="rotate(90,1029.64,444.09) scale(0.994718,0.994718) translate(5.441,2.30531)" width="10" x="1024.666549259365" xlink:href="#GroundDisconnector:地刀_0" y="434.1427712939253" zvalue="201"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454750175234" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454750175234"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1029.64,444.09) scale(0.994718,0.994718) translate(5.441,2.30531)" width="10" x="1024.666549259365" y="434.1427712939253"/></g>
  <g id="227">
   <use class="kv110" height="20" transform="rotate(270,855.333,406.722) scale(1.11111,1.11111) translate(-84.9778,-39.5611)" width="10" x="849.7777845594618" xlink:href="#GroundDisconnector:地刀_0" y="395.6111008326212" zvalue="246"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449669431302" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449669431302"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,855.333,406.722) scale(1.11111,1.11111) translate(-84.9778,-39.5611)" width="10" x="849.7777845594618" y="395.6111008326212"/></g>
  <g id="267">
   <use class="kv110" height="20" transform="rotate(270,948.611,294.222) scale(-1.11111,1.11111) translate(-1801.81,-28.3111)" width="10" x="943.0555551846821" xlink:href="#GroundDisconnector:地刀_0" y="283.1111111111111" zvalue="275"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454751289346" ObjectName="110kV备用线16117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454751289346"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,948.611,294.222) scale(-1.11111,1.11111) translate(-1801.81,-28.3111)" width="10" x="943.0555551846821" y="283.1111111111111"/></g>
  <g id="266">
   <use class="kv110" height="20" transform="rotate(270,944.056,243.778) scale(-1.11111,1.11111) translate(-1793.15,-23.2667)" width="10" x="938.4999998410542" xlink:href="#GroundDisconnector:地刀_0" y="232.6666666666667" zvalue="277"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454751158274" ObjectName="110kV备用线16160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454751158274"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,944.056,243.778) scale(-1.11111,1.11111) translate(-1793.15,-23.2667)" width="10" x="938.4999998410542" y="232.6666666666667"/></g>
  <g id="265">
   <use class="kv110" height="20" transform="rotate(270,947.611,189.333) scale(-1.11111,1.11111) translate(-1799.91,-17.8222)" width="10" x="942.0555551317002" xlink:href="#GroundDisconnector:地刀_0" y="178.2222222222222" zvalue="279"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454751027202" ObjectName="110kV备用线16167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454751027202"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,947.611,189.333) scale(-1.11111,1.11111) translate(-1799.91,-17.8222)" width="10" x="942.0555551317002" y="178.2222222222222"/></g>
  <g id="319">
   <use class="kv110" height="20" transform="rotate(270,1313.94,293.111) scale(-1.11111,1.11111) translate(-2495.94,-28.2)" width="10" x="1308.388885511292" xlink:href="#GroundDisconnector:地刀_0" y="281.9999999999999" zvalue="317"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454752468994" ObjectName="110kV南葫线16217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454752468994"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1313.94,293.111) scale(-1.11111,1.11111) translate(-2495.94,-28.2)" width="10" x="1308.388885511292" y="281.9999999999999"/></g>
  <g id="317">
   <use class="kv110" height="20" transform="rotate(270,1315.94,230.444) scale(-1.11111,1.11111) translate(-2499.74,-21.9333)" width="10" x="1310.388884862264" xlink:href="#GroundDisconnector:地刀_0" y="219.3333333333331" zvalue="321"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454752337922" ObjectName="110kV南葫线16267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454752337922"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1315.94,230.444) scale(-1.11111,1.11111) translate(-2499.74,-21.9333)" width="10" x="1310.388884862264" y="219.3333333333331"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="178">
   <g id="1780">
    <use class="kv110" height="50" transform="rotate(0,1073.43,547.783) scale(1.65239,1.61812) translate(-414.02,-193.8)" width="30" x="1048.64" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="507.33" zvalue="205"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874589995010" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1781">
    <use class="kv10" height="50" transform="rotate(0,1073.43,547.783) scale(1.65239,1.61812) translate(-414.02,-193.8)" width="30" x="1048.64" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="507.33" zvalue="205"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874590060546" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399532281858" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399532281858"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1073.43,547.783) scale(1.65239,1.61812) translate(-414.02,-193.8)" width="30" x="1048.64" y="507.33"/></g>
  <g id="280">
   <g id="2800">
    <use class="kv10" height="50" transform="rotate(0,1410.61,841.688) scale(0.78581,0.78581) translate(381.279,224.066)" width="30" x="1398.82" xlink:href="#PowerTransformer2:Y-Y_0" y="822.04" zvalue="287"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874590388226" ObjectName="10"/>
    </metadata>
   </g>
   <g id="2801">
    <use class="v400" height="50" transform="rotate(0,1410.61,841.688) scale(0.78581,0.78581) translate(381.279,224.066)" width="30" x="1398.82" xlink:href="#PowerTransformer2:Y-Y_1" y="822.04" zvalue="287"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874590453762" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399532478466" ObjectName="1号站用变"/>
   </metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1410.61,841.688) scale(0.78581,0.78581) translate(381.279,224.066)" width="30" x="1398.82" y="822.04"/></g>
  <g id="292">
   <g id="2920">
    <use class="kv10" height="50" transform="rotate(0,1633.61,841.426) scale(0.796296,0.796296) translate(414.845,210.156)" width="30" x="1621.67" xlink:href="#PowerTransformer2:Y-Y_0" y="821.52" zvalue="297"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874590257154" ObjectName="10"/>
    </metadata>
   </g>
   <g id="2921">
    <use class="v400" height="50" transform="rotate(0,1633.61,841.426) scale(0.796296,0.796296) translate(414.845,210.156)" width="30" x="1621.67" xlink:href="#PowerTransformer2:Y-Y_1" y="821.52" zvalue="297"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874590322690" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399532412930" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1633.61,841.426) scale(0.796296,0.796296) translate(414.845,210.156)" width="30" x="1621.67" y="821.52"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="269">
   <use class="kv110" height="30" transform="rotate(0,870.932,127.222) scale(1.85185,1.48148) translate(-395.518,-34.125)" width="12" x="859.8209284441513" xlink:href="#EnergyConsumer:负荷_0" y="104.9999999999999" zvalue="272"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454751354882" ObjectName="110kV备用线"/>
   <cge:TPSR_Ref TObjectID="6192454751354882"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,870.932,127.222) scale(1.85185,1.48148) translate(-395.518,-34.125)" width="12" x="859.8209284441513" y="104.9999999999999"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="320">
   <use class="kv110" height="40" transform="rotate(0,1239,126.111) scale(1.26984,1.11111) translate(-258.565,-10.3889)" width="35" x="1216.778600401823" xlink:href="#ACLineSegment:线路电压互感器_0" y="103.8888888888887" zvalue="315"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249331269634" ObjectName="110kV南葫线"/>
   <cge:TPSR_Ref TObjectID="8444249331269634_5066549683814401"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1239,126.111) scale(1.26984,1.11111) translate(-258.565,-10.3889)" width="35" x="1216.778600401823" y="103.8888888888887"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,864.932,26.9445) scale(1,1) translate(0,0)" writing-mode="lr" x="864.46" xml:space="preserve" y="31.61" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136262742018" ObjectName="P"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="5" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,864.932,41.6481) scale(1,1) translate(0,0)" writing-mode="lr" x="864.46" xml:space="preserve" y="46.31" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136262807554" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="14" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,865.932,56.3518) scale(1,1) translate(0,0)" writing-mode="lr" x="865.46" xml:space="preserve" y="61.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136262873090" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="15" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1225,26.9445) scale(1,1) translate(0,0)" writing-mode="lr" x="1224.53" xml:space="preserve" y="31.61" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136273096706" ObjectName="P"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="16" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1225,41.6481) scale(1,1) translate(0,0)" writing-mode="lr" x="1224.53" xml:space="preserve" y="46.31" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136273162242" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="17" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1225,56.3518) scale(1,1) translate(0,0)" writing-mode="lr" x="1224.53" xml:space="preserve" y="61.02" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136273227778" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="64" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1148.93,429.83) scale(1,1) translate(0,0)" writing-mode="lr" x="1148.35" xml:space="preserve" y="434.11" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136259661826" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="65" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1148.93,451.83) scale(1,1) translate(0,0)" writing-mode="lr" x="1148.35" xml:space="preserve" y="456.11" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136259727362" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="66" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1148.93,473.83) scale(1,1) translate(0,0)" writing-mode="lr" x="1148.35" xml:space="preserve" y="478.11" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136259923970" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="67">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="67" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,977.926,608.736) scale(1,1) translate(0,0)" writing-mode="lr" x="977.35" xml:space="preserve" y="613.01" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136259792898" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="68" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,977.926,631.736) scale(1,1) translate(0,-1.36388e-13)" writing-mode="lr" x="977.35" xml:space="preserve" y="636.01" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136259858434" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="69">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="69" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,977.926,656.736) scale(1,1) translate(0,0)" writing-mode="lr" x="977.35" xml:space="preserve" y="661.01" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136260251650" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="70">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="70" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,759.966,977.927) scale(1,1) translate(0,0)" writing-mode="lr" x="759.39" xml:space="preserve" y="982.7" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136254681090" ObjectName="P"/>
   </metadata>
  </g>
  <g id="71">
   <text Format="f5.2" Plane="0" fill="rgb(255,25,0)" font-family="SimSun" font-size="13" id="71" prefix="Q:" stroke="rgb(255,25,0)" text-anchor="middle" transform="rotate(0,761.966,999.927) scale(1,1) translate(0,0)" writing-mode="lr" x="761.39" xml:space="preserve" y="1004.7" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136254746626" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="76">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="76" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,761.966,1015.93) scale(1,1) translate(0,-1.55187e-12)" writing-mode="lr" x="761.39" xml:space="preserve" y="1020.7" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136254812162" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="79">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="79" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1084.99,977.927) scale(1,1) translate(-2.28592e-13,0)" writing-mode="lr" x="1084.41" xml:space="preserve" y="982.7" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136256385026" ObjectName="P"/>
   </metadata>
  </g>
  <g id="81">
   <text Format="f5.2" Plane="0" fill="rgb(255,25,0)" font-family="SimSun" font-size="13" id="81" prefix="Q:" stroke="rgb(255,25,0)" text-anchor="middle" transform="rotate(0,1082.99,996.927) scale(1,1) translate(1.14074e-13,0)" writing-mode="lr" x="1082.41" xml:space="preserve" y="1001.7" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136256450562" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="84">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="84" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1083.99,1015.93) scale(1,1) translate(1.14185e-13,-1.55187e-12)" writing-mode="lr" x="1083.41" xml:space="preserve" y="1020.7" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136256516098" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="105" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,154.611,347.167) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="352.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136262610946" ObjectName="F"/>
   </metadata>
  </g>
  <g id="167">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="167" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,154.611,275.167) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="280.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136270737410" ObjectName="F"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="103" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,332.222,276.167) scale(1,1) translate(0,0)" writing-mode="lr" x="332.38" xml:space="preserve" y="281.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136270802946" ObjectName="F"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="102" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,332.222,348.167) scale(1,1) translate(0,0)" writing-mode="lr" x="332.38" xml:space="preserve" y="353.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136256253954" ObjectName="F"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="91" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,154.611,300.167) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="305.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136270606338" ObjectName="F"/>
   </metadata>
  </g>
  <g id="90">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,332.222,301.167) scale(1,1) translate(0,0)" writing-mode="lr" x="332.38" xml:space="preserve" y="306.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136270671874" ObjectName="F"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,154.611,392.389) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="397.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136270868482" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="33" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,154.611,324.167) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="329.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127179452424" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="26">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,334.611,323.167) scale(1,1) translate(0,0)" writing-mode="lr" x="334.77" xml:space="preserve" y="328.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136270802946" ObjectName="GEN_QSUM"/>
   </metadata>
  </g>
  <g id="130">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,154.611,414.389) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="419.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136270934018" ObjectName="坝下水位"/>
   </metadata>
  </g>
  <g id="135">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,330.611,392.389) scale(1,1) translate(0,0)" writing-mode="lr" x="330.77" xml:space="preserve" y="397.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136270999554" ObjectName="雨量采集"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,618.072,546.776) scale(1,1) translate(0,0)" writing-mode="lr" x="617.6" xml:space="preserve" y="551.55" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136255860738" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="20" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,926.087,466.38) scale(1,1) translate(0,9.96713e-14)" writing-mode="lr" x="925.62" xml:space="preserve" y="471.16" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136262217730" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="21" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,618.072,569.776) scale(1,1) translate(0,0)" writing-mode="lr" x="617.6" xml:space="preserve" y="574.55" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136255926274" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="22" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,926.087,490.88) scale(1,1) translate(0,0)" writing-mode="lr" x="925.62" xml:space="preserve" y="495.66" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136262283266" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="25">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="25" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,618.072,592.776) scale(1,1) translate(0,0)" writing-mode="lr" x="617.6" xml:space="preserve" y="597.55" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136255991810" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="27">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="27" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,926.087,515.38) scale(1,1) translate(0,0)" writing-mode="lr" x="925.62" xml:space="preserve" y="520.16" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136262348802" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="28">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="28" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,629.072,718.276) scale(1,1) translate(0,0)" writing-mode="lr" x="628.6" xml:space="preserve" y="723.05" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136256122882" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="29">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="29" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,627.087,289.88) scale(1,1) translate(0,0)" writing-mode="lr" x="626.62" xml:space="preserve" y="294.66" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136262479874" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="653">
   <use height="30" transform="rotate(0,209,199.5) scale(0.708333,0.665547) translate(81.6838,95.2368)" width="30" x="198.38" xlink:href="#State:红绿圆(方形)_0" y="189.52" zvalue="357"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562962127388673" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,209,199.5) scale(0.708333,0.665547) translate(81.6838,95.2368)" width="30" x="198.38" y="189.52"/></g>
  <g id="654">
   <use height="30" transform="rotate(0,312.625,199.5) scale(0.708333,0.665547) translate(124.353,95.2368)" width="30" x="302" xlink:href="#State:红绿圆(方形)_0" y="189.52" zvalue="359"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374927986689" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,312.625,199.5) scale(0.708333,0.665547) translate(124.353,95.2368)" width="30" x="302" y="189.52"/></g>
  <g id="24">
   <use height="30" transform="rotate(0,318.812,125.464) scale(1.22222,1.03092) translate(-47.9659,-3.29938)" width="90" x="263.81" xlink:href="#State:全站检修_0" y="110" zvalue="426"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549683814401" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,318.812,125.464) scale(1.22222,1.03092) translate(-47.9659,-3.29938)" width="90" x="263.81" y="110"/></g>
 </g>
</svg>