<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549597437954" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷带壁雷器_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="28.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11.75" y1="17.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="14.75" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="12" y1="11.75" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="9" y1="12.75" y2="12.75"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.04,18.25) scale(1,1) translate(0,0)" width="3.25" x="9.42" y="14.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.5" y1="10.83333333333333" y2="10.83333333333333"/>
   <path d="M 5.025 2.775 L 4.16667 9.5 L 5.025 7.60833 L 5.91667 9.5 L 5.025 2.775" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 5 23.75 L 11 23.75 L 11 17.75 L 10.25 20.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.000411522633746" x2="5.000411522633746" y1="7.627914951989029" y2="28.23902606310013"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="GroundDisconnector:支那变双联地刀_0" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="20.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="13" y1="28.83333333333334" y2="28.83333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.927164662603633" x2="5.927164662603633" y1="35.73783949080951" y2="33.38156647584609"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.940012628342391" x2="9.914316696864876" y1="35.81657844392075" y2="35.81657844392075"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.461425904573654" x2="8.392903420633612" y1="37.61600124906283" y2="37.61600124906283"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.48434686602373" x2="7.369982459183538" y1="39.33429284309519" y2="39.33429284309519"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.6833333333333336" x2="5.927164662603635" y1="23.72291938246423" y2="33.38156647584608"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.83108493932905" x2="8.028087080656674" y1="23.60113107469012" y2="23.60113107469012"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.97259166003632" x2="5.97259166003632" y1="23.55945108167042" y2="20.52711096774605"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.57716466260364" x2="15.57716466260364" y1="35.62739518973012" y2="33.27112217476669"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.47836350249219" x2="19.45266757101467" y1="35.70613414284136" y2="35.70613414284136"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="12.99977677872345" x2="17.93125429478341" y1="37.50555694798344" y2="37.50555694798344"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="14.18103107350686" x2="16.75" y1="39.2238485420158" y2="39.2238485420158"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.33333333333333" x2="15.57716466260364" y1="23.61247508138485" y2="33.2711221747667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.62259166003632" x2="15.62259166003632" y1="23.44900678059104" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.48108493932905" x2="17.67808708065667" y1="23.49068677361073" y2="23.49068677361073"/>
  </symbol>
  <symbol id="GroundDisconnector:支那变双联地刀_1" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="20.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.916666666666667" x2="15.75" y1="28.83333333333333" y2="28.83333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.927164662603633" x2="5.927164662603633" y1="35.73783949080951" y2="23.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.070030169158852" x2="10.04433423768134" y1="35.73324511058742" y2="35.73324511058742"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.591443445390117" x2="8.522920961450074" y1="37.61600124906283" y2="37.61600124906283"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.781031073506858" x2="7.333333333333333" y1="39.33429284309519" y2="39.33429284309519"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.83108493932905" x2="8.028087080656674" y1="23.60113107469012" y2="23.60113107469012"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.97259166003632" x2="5.97259166003632" y1="23.55945108167042" y2="20.52711096774605"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.66049799593697" x2="15.66049799593697" y1="35.66666666666666" y2="23.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.64503016915885" x2="19.61933423768134" y1="35.70613414284136" y2="35.70613414284136"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.16644344539012" x2="18.09792096145007" y1="37.50555694798344" y2="37.50555694798344"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="14.43103107350686" x2="16.83333333333333" y1="39.2238485420158" y2="39.2238485420158"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.62259166003632" x2="15.62259166003632" y1="23.44900678059104" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.48108493932905" x2="17.67808708065667" y1="23.49068677361073" y2="23.49068677361073"/>
  </symbol>
  <symbol id="GroundDisconnector:支那变双联地刀_2" viewBox="0,0,20,40">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="20.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.177164662603633" x2="15.66666666666667" y1="33.98783949080951" y2="24.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.070030169158852" x2="10.04433423768134" y1="35.73324511058742" y2="35.73324511058742"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.591443445390117" x2="8.522920961450074" y1="37.61600124906283" y2="37.61600124906283"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.781031073506858" x2="7.333333333333333" y1="39.33429284309519" y2="39.33429284309519"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.97259166003632" x2="5.97259166003632" y1="23.55945108167042" y2="20.52711096774605"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.83108493932905" x2="8.028087080656674" y1="23.60113107469012" y2="23.60113107469012"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.41049799593697" x2="6.333333333333334" y1="34.00000000000001" y2="25.00000000000001"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.64503016915885" x2="19.61933423768134" y1="35.70613414284136" y2="35.70613414284136"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="13.16644344539012" x2="18.09792096145007" y1="37.50555694798344" y2="37.50555694798344"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="14.43103107350686" x2="16.83333333333333" y1="39.2238485420158" y2="39.2238485420158"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.62259166003632" x2="15.62259166003632" y1="23.44900678059104" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.48108493932905" x2="17.67808708065667" y1="23.49068677361073" y2="23.49068677361073"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Compensator:10kV电容器_0" viewBox="0,0,24,40">
   <use terminal-index="0" type="0" x="12" xlink:href="#terminal" y="4.699999999999999"/>
   <path d="M 12 20.1 L 17.85 20.1 L 17.85 24.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="4.749074074074073" y1="35.35833333333333" y2="35.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.72129629629629" x2="4.72129629629629" y1="33.13611111111111" y2="35.37685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="24.85833333333333" y2="30.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.258333333333324" x2="0.258333333333324" y1="22.10833333333333" y2="33.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.75833333333333" x2="4.75833333333333" y1="22.35833333333333" y2="20.15462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="31.85833333333333" y2="35.37685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="20.04351851851851" y2="24.775"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.024239280774554" x2="4.024239280774554" y1="39.35833333333333" y2="37.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.786111111111114" x2="12.00833333333334" y1="20.10833333333333" y2="20.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="35.35833333333333" y2="39.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.84166666666666" x2="4.024239280774546" y1="39.35833333333333" y2="39.35833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.84166666666667" x2="19.84166666666667" y1="37.35833333333333" y2="39.35833333333333"/>
   <path d="M 4.75833 25.9572 A 2.96392 1.81747 180 0 1 4.75833 22.3223" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 4.75833 29.5921 A 2.96392 1.81747 180 0 1 4.75833 25.9572" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 4.75833 33.1271 A 2.96392 1.81747 180 0 1 4.75833 29.4921" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="30.85833333333333" y2="30.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="31.85833333333333" y2="31.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="12" y1="4.5" y2="7.166666666666668"/>
   <path d="M 7.26667 12.1 A 4.91667 4.75 -450 1 0 12.0167 7.18333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 7.33333 12.0833 L 12 12.1667 L 12 20.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510778" x2="17.14262023217246" y1="27.75922956383932" y2="26.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="29.36667381975841" y2="30.33114037330986"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.50700202690252" x2="18.32283029297954" y1="31.0857461490052" y2="31.0857461490052"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.28450340888154" x2="18.47116270499357" y1="30.7156109584975" y2="30.7156109584975"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.98783858485353" x2="18.76782752902158" y1="30.34547576798984" y2="30.34547576798984"/>
   <rect fill-opacity="0" height="4.29" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,17.83,27.22) scale(1,1) translate(0,0)" width="2.33" x="16.67" y="25.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="24.56666666666668" y2="25.08015580397416"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510781" x2="18.53565505804314" y1="27.75922956383932" y2="26.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="25.08015580397418" y2="27.73243882624067"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变无融断_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <path d="M 10 9 L 10 0" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="26.83333333333333" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="10.8868007916835" y2="12.55855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="14.23031840279781" y2="12.55855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:放电间隙2_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15.08333333333333" xlink:href="#terminal" y="11.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.01785714285714" x2="15.01785714285714" y1="8.75" y2="14.16666666666667"/>
   <path d="M 15.0179 14.1078 L 11.5 18.1667 L 18.5802 18.1667 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 15.0333 9.11336 L 11.5154 5.05453 L 18.4321 5.05453 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:接地变X_0" viewBox="0,0,22,19">
   <use terminal-index="0" type="0" x="11.25" xlink:href="#terminal" y="9.5"/>
   <ellipse cx="11.21" cy="9.720000000000001" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.20192285733684" x2="11.20192285733684" y1="5.35" y2="9.458948332339871"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.20192285733684" x2="7.216666666666667" y1="9.483948332339875" y2="12.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.22969569739165" x2="15.31666666666667" y1="9.439905009168513" y2="12.25"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="12.00516117969822" xlink:href="#terminal" y="7.908504801097395"/>
  </symbol>
  <symbol id="PowerTransformer2:可调两卷变_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="DollyBreaker:手车_0" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="0.3833333333333346" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="21.8" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.0246913580247" x2="0.3833333333333346" y1="11.2962962962963" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.07407407407407" x2="21.53229166666667" y1="11.2962962962963" y2="21.41666666666666"/>
  </symbol>
  <symbol id="DollyBreaker:手车_1" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="21" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="1" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="11" y1="1" y2="11"/>
  </symbol>
  <symbol id="DollyBreaker:手车_2" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <path d="M 3.6066 1.05 L 18.5833 9.95" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 18.4234 1 L 3.5 10" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:芒章接地变中性点_0" viewBox="0,0,8,25">
   <use terminal-index="0" type="0" x="4.05" xlink:href="#terminal" y="0.5999999999999996"/>
   <ellipse cx="4.08" cy="14.92" fill-opacity="0" rx="2.24" ry="2.24" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="8.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,3.96,4.59) scale(1,1) translate(0,0)" width="4.08" x="1.92" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.924443536244111" x2="5.031267419466841" y1="24.50147998261393" y2="24.50147998261393"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.851981351981355" x2="6.192307692307693" y1="23.03094803979166" y2="23.03094803979166"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.833333333333333" x2="7.166666666666667" y1="21.59999999999999" y2="21.59999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.083333333333335" x2="4.083333333333335" y1="12.91666666666667" y2="8.600000000000003"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.083333333333335" x2="4.083333333333335" y1="21.6" y2="17.25"/>
  </symbol>
  <symbol id="Accessory:四绕组PT带保险_0" viewBox="0,0,50,45">
   <use terminal-index="0" type="0" x="28.72373692455959" xlink:href="#terminal" y="2.537541475206687"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(90,12.17,28.24) scale(1,1) translate(0,0)" width="11" x="6.67" y="25.74"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.17373692455959" x2="8.173736924559588" y1="24.48754147520669" y2="31.48754147520669"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.36818136900403" x2="11.92373692455959" y1="19.88568962335484" y2="19.88568962335484"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.92373692455959" x2="11.92373692455959" y1="19.89494888261409" y2="22.73754147520669"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.17373692455959" x2="16.17373692455959" y1="22.48754147520669" y2="24.48754147520669"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.173736924559588" x2="8.173736924559588" y1="31.48754147520669" y2="33.48754147520669"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.72373692455959" x2="28.72373692455959" y1="12.43754147520669" y2="17.93754147520669"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.13847434068691" x2="12.13847434068691" y1="33.74119684783661" y2="40.42249505571176"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.07180767402025" x2="14.07180767402025" y1="40.81181738078094" y2="40.81181738078094"/>
   <ellipse cx="32.24" cy="19.94" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.23570975531306" x2="24.23570975531306" y1="8.047098059599875" y2="8.047098059599875"/>
   <ellipse cx="32.41" cy="26.94" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.02535717173132" x2="14.30217301641517" y1="40.57373692455958" y2="40.57373692455958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.77535717173132" x2="13.3855063497485" y1="41.82373692455958" y2="41.82373692455958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.27535717173132" x2="12.71883968308184" y1="43.32373692455958" y2="43.32373692455958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.25707025789292" x2="30.90659044566773" y1="27.95420814187334" y2="27.95420814187334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.02120709929452" x2="30.78366711494242" y1="25.40420814187335" y2="27.80420814187334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.13997709147048" x2="34.3775170758226" y1="25.50420814187336" y2="27.90420814187335"/>
   <ellipse cx="25.24" cy="19.94" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="23.92179506068867" x2="23.92179506068867" y1="7.635814161432609" y2="7.635814161432609"/>
   <ellipse cx="25.24" cy="26.94" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.09399162376016" x2="29.09399162376016" y1="19.50717613281176" y2="19.50717613281176"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.84399162376016" x2="29.84399162376016" y1="25.75717613281177" y2="25.75717613281177"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.41316287352088" x2="22.96038051705155" y1="27.10182765481038" y2="25.97250753869496"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.41316287352088" x2="25.9155555986147" y1="27.10182765481038" y2="29.75495915600721"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.3635525048964" x2="25.41316287352089" y1="25.57801626972896" y2="27.10182765481037"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="29.34399162376016" x2="29.34399162376016" y1="26.25717613281177" y2="26.25717613281177"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.41316287352088" x2="25.9155555986147" y1="19.85182765481038" y2="22.50495915600721"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.3635525048964" x2="25.41316287352089" y1="18.32801626972896" y2="19.85182765481037"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.41316287352088" x2="22.96038051705155" y1="19.85182765481038" y2="18.72250753869496"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.66316287352087" x2="33.1655555986147" y1="19.85182765481038" y2="22.50495915600721"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.6135525048964" x2="32.6631628735209" y1="18.32801626972896" y2="19.85182765481037"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.66316287352088" x2="30.21038051705155" y1="19.85182765481038" y2="18.72250753869496"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.69516181091775" x2="28.69516181091775" y1="12.66972348513988" y2="2.415176794076061"/>
   <rect fill-opacity="0" height="10.1" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(180,28.66,7.56) scale(1,1) translate(0,0)" width="5.26" x="26.03" y="2.51"/>
  </symbol>
  <symbol id="Disconnector:小车带电壁雷_0" viewBox="0,0,30,33">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="8" y1="2.333333333333329" y2="2.333333333333329"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="7" y1="8.5" y2="8.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.5" x2="7.5" y1="1.583333333333329" y2="1.583333333333329"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="8.5" y2="5.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="6" y1="4.5" y2="4.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.625" x2="8.375" y1="3.083333333333329" y2="3.083333333333329"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26" x2="27" y1="28.5" y2="26.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="4.499999999999998" y2="3.083333333333329"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26" x2="26" y1="24.5" y2="28.5"/>
   <rect fill-opacity="0" height="4.25" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,26,27.63) scale(1,1) translate(0,0)" width="2" x="25" y="25.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="26" y1="24.5" y2="24.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.08333333333334" x2="26.08333333333334" y1="31.25" y2="29.91666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="6" y1="5.5" y2="5.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26" x2="25" y1="28.5" y2="26.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.333333333333332" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="15" y1="19.25" y2="24.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="24.08333333333333" y2="29.75"/>
   <rect fill-opacity="0" height="7" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,13.5) scale(1,1) translate(0,0)" width="4" x="13" y="10"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.91800411522634" x2="9.459670781893006" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="9.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="20.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="20.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="9.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="9.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="20.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="20.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.625" x2="26.625" y1="32.8" y2="32.8"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.125" x2="27.125" y1="32.00833333333333" y2="32.00833333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.83333333333334" x2="27.58333333333334" y1="31.21666666666667" y2="31.21666666666667"/>
  </symbol>
  <symbol id="Disconnector:小车带电壁雷_1" viewBox="0,0,30,33">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.25" y2="32.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.91800411522634" x2="9.459670781893006" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="20.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="9.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="20.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="26" y1="24.5" y2="24.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26" x2="25" y1="28.5" y2="26.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26" x2="27" y1="28.5" y2="26.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.08333333333334" x2="26.08333333333334" y1="31.25" y2="29.91666666666667"/>
   <rect fill-opacity="0" height="4.25" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,26,27.63) scale(1,1) translate(0,0)" width="2" x="25" y="25.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,13.5) scale(1,1) translate(0,0)" width="4" x="13" y="10"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26" x2="26" y1="24.5" y2="28.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="8.5" y2="5.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="8" y1="2.333333333333329" y2="2.333333333333329"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="6" y1="4.5" y2="4.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="6" y1="5.5" y2="5.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.5" x2="7.5" y1="1.583333333333329" y2="1.583333333333329"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="4.499999999999998" y2="3.083333333333329"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="7" y1="8.5" y2="8.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.625" x2="8.375" y1="3.083333333333329" y2="3.083333333333329"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.625" x2="26.625" y1="32.8" y2="32.8"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.125" x2="27.125" y1="32.00833333333333" y2="32.00833333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.83333333333334" x2="27.58333333333334" y1="31.21666666666667" y2="31.21666666666667"/>
  </symbol>
  <symbol id="Disconnector:小车带电壁雷_2" viewBox="0,0,30,33">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.333333333333332" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26" x2="25" y1="28.5" y2="26.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,13.5) scale(1,1) translate(0,0)" width="4" x="13" y="10"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="16.5" y1="19" y2="24"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26" x2="27" y1="28.5" y2="26.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26" x2="26" y1="24.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.08333333333334" x2="26.08333333333334" y1="31.25" y2="29.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="24.08333333333333" y2="29.75"/>
   <rect fill-opacity="0" height="4.25" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,26,27.63) scale(1,1) translate(0,0)" width="2" x="25" y="25.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="26" y1="24.5" y2="24.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.625" x2="8.375" y1="3.083333333333329" y2="3.083333333333329"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="6" y1="5.5" y2="5.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="4.499999999999998" y2="3.083333333333329"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="8.5" y2="5.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="6" y1="4.5" y2="4.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="7" y1="8.5" y2="8.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.5" x2="7.5" y1="1.583333333333329" y2="1.583333333333329"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="8" y1="2.333333333333329" y2="2.333333333333329"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.91800411522634" x2="9.459670781893006" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="9.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="20.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="20.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="9.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="9.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="20.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00133744855967" x2="20.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.83333333333334" x2="27.58333333333334" y1="31.21666666666667" y2="31.21666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.125" x2="27.125" y1="32.00833333333333" y2="32.00833333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.625" x2="26.625" y1="32.8" y2="32.8"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.33333333333334" x2="13.33333333333333" y1="19.16666666666666" y2="24.16666666666666"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV芒章变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="63.81" xlink:href="logo.png" y="23.68"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,188.653,64.9636) scale(1,1) translate(-1.41679e-14,0)" writing-mode="lr" x="188.65" xml:space="preserve" y="68.45999999999999" zvalue="1"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="334" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,190.153,63.7136) scale(1,1) translate(-1.4501e-14,0)" writing-mode="lr" x="190.15" xml:space="preserve" y="68.20999999999999" zvalue="3"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,218,64.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="218" xml:space="preserve" y="73.69" zvalue="4">35kV芒章变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="205" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,288.438,387.25) scale(1,1) translate(0,0)" width="72.88" x="252" y="375.25" zvalue="815"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,288.438,387.25) scale(1,1) translate(0,0)" writing-mode="lr" x="288.44" xml:space="preserve" y="391.75" zvalue="815">小电流接地</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="22" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,85.625,314.25) scale(1,1) translate(0,0)" width="72.88" x="49.19" y="302.25" zvalue="816"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.625,314.25) scale(1,1) translate(0,0)" writing-mode="lr" x="85.63" xml:space="preserve" y="318.75" zvalue="816">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="157" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,187.531,387.25) scale(1,1) translate(0,0)" width="72.88" x="151.09" y="375.25" zvalue="817"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,187.531,387.25) scale(1,1) translate(0,0)" writing-mode="lr" x="187.53" xml:space="preserve" y="391.75" zvalue="817">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="57" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,85.625,387.25) scale(1,1) translate(0,0)" width="72.88" x="49.19" y="375.25" zvalue="818"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.625,387.25) scale(1,1) translate(0,0)" writing-mode="lr" x="85.63" xml:space="preserve" y="391.75" zvalue="818">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="53" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,85.625,350.75) scale(1,1) translate(0,0)" width="72.88" x="49.19" y="338.75" zvalue="819"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.625,350.75) scale(1,1) translate(0,0)" writing-mode="lr" x="85.63" xml:space="preserve" y="355.25" zvalue="819">信号一览</text>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="8" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,453,52) scale(1,1) translate(0,0)" writing-mode="lr" x="453" xml:space="preserve" y="59.5" zvalue="1102">程序化操作</text>
  <line fill="none" id="331" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.000000000000455" x2="373" y1="152.8704926140824" y2="152.8704926140824" zvalue="6"/>
  <line fill="none" id="330" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="374" x2="374" y1="11" y2="1041" zvalue="7"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="260.75" y2="283.5"/>
  <line fill="none" id="328" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.000000000000455" x2="373" y1="622.8704926140824" y2="622.8704926140824" zvalue="9"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="58" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3873" x2="221.3873" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="221.3872" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339" x2="339" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="58" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3873" x2="221.3873" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="221.3872" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339" x2="339" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="58" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3873" x2="221.3873" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="221.3872" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339" x2="339" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="58" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3873" x2="221.3873" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="221.3872" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339" x2="339" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="58" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3873" x2="221.3873" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="221.3872" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339" x2="339" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="103.7745" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="58" x2="58" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="162.5809" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="103.7745" x2="103.7745" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="221.3873" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="162.5809" x2="162.5809" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3873" x2="221.3873" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="280.1936000000001" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="221.3872" x2="221.3872" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="339" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="280.1936000000001" x2="280.1936000000001" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="339" x2="339" y1="582" y2="606.6794"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="938" y2="938"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="6" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="366" y1="938" y2="938"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="366" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366" x2="366" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="6" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="186" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="186" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186" x2="186" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="276.0000000000001" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="276.0000000000001" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="186.0000000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.0000000000001" x2="276.0000000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="366" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="366" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="276" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366" x2="366" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="96" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="6" x2="6" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="186" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="186" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="96" x2="96" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186" x2="186" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="276.0000000000001" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="276.0000000000001" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="186.0000000000001" x2="186.0000000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276.0000000000001" x2="276.0000000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="366" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="366" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="276" x2="276" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="366" x2="366" y1="1005.0816" y2="1033"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="324" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52,958) scale(1,1) translate(0,0)" writing-mode="lr" x="52" xml:space="preserve" y="964" zvalue="13">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="323" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49,992) scale(1,1) translate(0,0)" writing-mode="lr" x="49" xml:space="preserve" y="998" zvalue="14">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="322" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231,992) scale(1,1) translate(0,0)" writing-mode="lr" x="231" xml:space="preserve" y="998" zvalue="15">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="321" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="47" xml:space="preserve" y="1026" zvalue="16">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="320" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,229,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="229" xml:space="preserve" y="1026" zvalue="17">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="319" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,130.528,465.389) scale(1,1) translate(0,9.9772e-14)" writing-mode="lr" x="130.527804904514" xml:space="preserve" y="469.8888914320204" zvalue="18">35kV母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1714.64,647.917) scale(1,1) translate(0,-4.24938e-13)" writing-mode="lr" x="1714.64" xml:space="preserve" y="652.42" zvalue="40">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="303" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,71.5,652.5) scale(1,1) translate(0,0)" writing-mode="lr" x="71.5" xml:space="preserve" y="657" zvalue="47">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,204.399,319.841) scale(1,1) translate(0,0)" writing-mode="lr" x="204.4" xml:space="preserve" y="324.34" zvalue="48">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="301" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,309.399,319.841) scale(1,1) translate(0,0)" writing-mode="lr" x="309.4" xml:space="preserve" y="324.34" zvalue="49">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="296" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,249.931,463.833) scale(1,1) translate(0,9.94266e-14)" writing-mode="lr" x="249.9305758608714" xml:space="preserve" y="468.3333358830876" zvalue="51">10kV母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="294" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,495.75) scale(1,1) translate(0,0)" writing-mode="lr" x="83" xml:space="preserve" y="500.25" zvalue="53">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="293" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,521.25) scale(1,1) translate(0,0)" writing-mode="lr" x="83" xml:space="preserve" y="525.75" zvalue="54">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="292" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,544.25) scale(1,1) translate(0,0)" writing-mode="lr" x="83" xml:space="preserve" y="548.75" zvalue="55">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="291" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,567.25) scale(1,1) translate(0,0)" writing-mode="lr" x="83" xml:space="preserve" y="571.75" zvalue="56">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="290" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83,594.25) scale(1,1) translate(0,0)" writing-mode="lr" x="83" xml:space="preserve" y="598.75" zvalue="57">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="289" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224.054,960) scale(1,1) translate(0,0)" writing-mode="lr" x="224.05" xml:space="preserve" y="966" zvalue="58">MangZhang-01-2024</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,142.054,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="142.05" xml:space="preserve" y="1026" zvalue="59">唐涛</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,321.054,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="321.05" xml:space="preserve" y="1026" zvalue="60">20240923</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="286" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45,179) scale(1,1) translate(0,0)" writing-mode="lr" x="45" xml:space="preserve" y="183.5" zvalue="61">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,225,179) scale(1,1) translate(0,0)" writing-mode="lr" x="225" xml:space="preserve" y="183.5" zvalue="62">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.6875,203.25) scale(1,1) translate(0,0)" writing-mode="lr" x="52.69" xml:space="preserve" y="207.75" zvalue="63">35kV母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="282" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46.6875,251) scale(1,1) translate(0,0)" writing-mode="lr" x="46.69" xml:space="preserve" y="255.5" zvalue="65">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="280" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46.6875,274) scale(1,1) translate(0,0)" writing-mode="lr" x="46.69" xml:space="preserve" y="278.5" zvalue="67">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="276" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.1875,227.25) scale(1,1) translate(0,0)" writing-mode="lr" x="52.19" xml:space="preserve" y="231.75" zvalue="73">10kV母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" x="797.3671875" xml:space="preserve" y="470.6961811913385" zvalue="82">#1主变   </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="797.3671875" xml:space="preserve" y="487.6961811913385" zvalue="82">8000KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,901.675,388.667) scale(1,1) translate(-1.97548e-13,0)" writing-mode="lr" x="901.67" xml:space="preserve" y="393.17" zvalue="132">341</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,853.391,324.694) scale(1,1) translate(0,0)" writing-mode="lr" x="853.39" xml:space="preserve" y="329.19" zvalue="135">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,808.722,229.694) scale(1,1) translate(0,0)" writing-mode="lr" x="808.72" xml:space="preserve" y="234.19" zvalue="154">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="392" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,657.767,717.64) scale(1,1) translate(0,0)" writing-mode="lr" x="657.77" xml:space="preserve" y="722.14" zvalue="443">042</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="395" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,706.972,777.111) scale(1,1) translate(-1.24162e-12,0)" writing-mode="lr" x="706.97" xml:space="preserve" y="781.61" zvalue="450">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="407" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,897.392,625.62) scale(1,1) translate(0,0)" writing-mode="lr" x="897.39" xml:space="preserve" y="630.12" zvalue="457">001</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="414" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,725.683,177.868) scale(1,1) translate(0,0)" writing-mode="lr" x="725.6799999999999" xml:space="preserve" y="182.37" zvalue="464">35kV线路电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="427" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,811.25,349.278) scale(1,1) translate(0,0)" writing-mode="lr" x="811.25" xml:space="preserve" y="353.78" zvalue="476">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="428" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,782.896,274.565) scale(1,1) translate(0,-3.52471e-13)" writing-mode="lr" x="782.9" xml:space="preserve" y="279.07" zvalue="479">97</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="435" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(180,1006.71,337.25) scale(-1,-1) translate(-2013.42,-674.5)" writing-mode="lr" x="1006.71" xml:space="preserve" y="341.75" zvalue="484">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(180,1030.58,229.5) scale(-1,-1) translate(-2061.17,-459)" writing-mode="lr" x="1030.58" xml:space="preserve" y="234" zvalue="486">3418</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="467" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,924.861,289.667) scale(1,1) translate(2.03584e-13,0)" writing-mode="lr" x="924.86" xml:space="preserve" y="294.17" zvalue="513">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="476" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,579.725,479.375) scale(1,1) translate(0,0)" writing-mode="lr" x="579.72" xml:space="preserve" y="483.88" zvalue="522">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="482" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,543.657,624.679) scale(1,1) translate(0,0)" writing-mode="lr" x="543.66" xml:space="preserve" y="629.1799999999999" zvalue="526">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="509" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,936.267,717.64) scale(1,1) translate(0,0)" writing-mode="lr" x="936.27" xml:space="preserve" y="722.14" zvalue="552">043</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="507" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,981.472,777.111) scale(1,1) translate(-2.16154e-13,0)" writing-mode="lr" x="981.47" xml:space="preserve" y="781.61" zvalue="555">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,884.125,837.75) scale(1,1) translate(0,0)" writing-mode="lr" x="884.13" xml:space="preserve" y="842.25" zvalue="563">8</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="521" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,864.969,934.25) scale(1,1) translate(0,0)" writing-mode="lr" x="864.97" xml:space="preserve" y="938.75" zvalue="565">10kV2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,525.262,725.126) scale(1,1) translate(0,0)" writing-mode="lr" x="525.26" xml:space="preserve" y="729.63" zvalue="601">041</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,574.972,775.111) scale(1,1) translate(0,0)" writing-mode="lr" x="574.97" xml:space="preserve" y="779.61" zvalue="603">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,564.683,797.015) scale(1,1) translate(0,0)" writing-mode="lr" x="564.6799999999999" xml:space="preserve" y="801.52" zvalue="610">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,552.688,937.25) scale(1,1) translate(0,0)" writing-mode="lr" x="552.6900000000001" xml:space="preserve" y="941.75" zvalue="618">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,522.75,815.625) scale(1,1) translate(0,0)" writing-mode="lr" x="522.75" xml:space="preserve" y="820.13" zvalue="620">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1229.52,944.5) scale(1,1) translate(-2.61701e-13,0)" writing-mode="lr" x="1229.52" xml:space="preserve" y="949" zvalue="677">10kV备用Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1205.29,717.64) scale(1,1) translate(-1.32315e-12,0)" writing-mode="lr" x="1205.29" xml:space="preserve" y="722.14" zvalue="679">045</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1250.49,781.361) scale(1,1) translate(0,0)" writing-mode="lr" x="1250.49" xml:space="preserve" y="785.86" zvalue="683">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="175" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,922.113,584.444) scale(1,1) translate(0,0)" writing-mode="lr" x="922.11" xml:space="preserve" y="588.9400000000001" zvalue="838">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="216" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,870.304,90.5) scale(1,1) translate(0,0)" writing-mode="lr" x="870.3" xml:space="preserve" y="95" zvalue="848">35kV槟芒线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1384.65,945.25) scale(1,1) translate(-2.96178e-13,0)" writing-mode="lr" x="1384.65" xml:space="preserve" y="949.75" zvalue="878">10kV备用Ⅱ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1360.57,717.64) scale(1,1) translate(0,0)" writing-mode="lr" x="1360.57" xml:space="preserve" y="722.14" zvalue="880">046</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1405.77,782.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1405.77" xml:space="preserve" y="786.61" zvalue="883">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1538.28,947.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1538.28" xml:space="preserve" y="952" zvalue="890">10kV备用Ⅲ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="186" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1515.85,717.64) scale(1,1) translate(0,0)" writing-mode="lr" x="1515.85" xml:space="preserve" y="722.14" zvalue="892">047</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1561.05,784.361) scale(1,1) translate(0,0)" writing-mode="lr" x="1561.05" xml:space="preserve" y="788.86" zvalue="895">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="220" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1694.48,947.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1694.48" xml:space="preserve" y="952.25" zvalue="902">10kV备用Ⅳ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="217" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1671.13,717.64) scale(1,1) translate(0,0)" writing-mode="lr" x="1671.13" xml:space="preserve" y="722.14" zvalue="904">048</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="206" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1716.33,784.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1716.33" xml:space="preserve" y="789.11" zvalue="907">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="232" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,963.583,917.5) scale(1,1) translate(0,0)" writing-mode="lr" x="963.58" xml:space="preserve" y="922" zvalue="914">10kV相帕线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="235" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1086.56,944.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1086.56" xml:space="preserve" y="949.25" zvalue="917">10kV芒章线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1062.01,717.64) scale(1,1) translate(0,0)" writing-mode="lr" x="1062.01" xml:space="preserve" y="722.14" zvalue="919">044</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="233" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1107.21,781.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1107.21" xml:space="preserve" y="786.11" zvalue="922">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" x="686.0703125" xml:space="preserve" y="936.484375" zvalue="934">10kV1号接地变及可投切小</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="686.0703125" xml:space="preserve" y="952.484375" zvalue="934">电阻成套装置</text>
  <ellipse cx="845.66" cy="390.66" fill="rgb(255,0,0)" fill-opacity="1" id="91" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1051"/>
  <ellipse cx="702.66" cy="718.66" fill="rgb(255,0,0)" fill-opacity="1" id="98" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1053"/>
  <ellipse cx="617.66" cy="627.66" fill="rgb(255,0,0)" fill-opacity="1" id="105" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1055"/>
  <ellipse cx="574.66" cy="717.66" fill="rgb(255,0,0)" fill-opacity="1" id="163" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1057"/>
  <ellipse cx="982.66" cy="716.66" fill="rgb(255,0,0)" fill-opacity="1" id="165" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1059"/>
  <ellipse cx="845.66" cy="626.66" fill="rgb(255,0,0)" fill-opacity="1" id="169" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1061"/>
  <ellipse cx="1103.66" cy="716.66" fill="rgb(255,0,0)" fill-opacity="1" id="170" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1063"/>
  <ellipse cx="1248.66" cy="716.66" fill="rgb(255,0,0)" fill-opacity="1" id="171" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1065"/>
  <ellipse cx="1405.66" cy="716.66" fill="rgb(255,0,0)" fill-opacity="1" id="172" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1067"/>
  <ellipse cx="1558.66" cy="716.66" fill="rgb(255,0,0)" fill-opacity="1" id="174" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1069"/>
  <ellipse cx="1715.66" cy="716.66" fill="rgb(255,0,0)" fill-opacity="1" id="176" rx="5.66" ry="5.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" zvalue="1071"/>
 </g>
 <g id="ButtonClass">
  <g href="小电流装置20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="252" y="375.25" zvalue="815"/></g>
  <g href="全站公用_遥控.svg"><rect fill-opacity="0" height="24" width="72.88" x="49.19" y="302.25" zvalue="816"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="151.09" y="375.25" zvalue="817"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="49.19" y="375.25" zvalue="818"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="49.19" y="338.75" zvalue="819"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="429">
   <path class="kv10" d="M 462.33 674.5 L 1745 674.5" stroke-width="6" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674265071621" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674265071621"/></metadata>
  <path d="M 462.33 674.5 L 1745 674.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="549">
   <g id="5490">
    <use class="kv35" height="30" transform="rotate(0,872.063,472.194) scale(2.58333,2.6) translate(-515.49,-266.581)" width="24" x="841.0599999999999" xlink:href="#PowerTransformer2:可调两卷变_0" y="433.19" zvalue="81"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874468950019" ObjectName="35"/>
    </metadata>
   </g>
   <g id="5491">
    <use class="kv10" height="30" transform="rotate(0,872.063,472.194) scale(2.58333,2.6) translate(-515.49,-266.581)" width="24" x="841.0599999999999" xlink:href="#PowerTransformer2:可调两卷变_1" y="433.19" zvalue="81"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874469015555" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399465762819" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399465762819"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,872.063,472.194) scale(2.58333,2.6) translate(-515.49,-266.581)" width="24" x="841.0599999999999" y="433.19"/></g>
 </g>
 <g id="BreakerClass">
  <g id="630">
   <use class="kv35" height="20" transform="rotate(0,871.97,384.648) scale(1.68868,1.53516) translate(-352.165,-128.738)" width="10" x="863.5269620796487" xlink:href="#Breaker:开关_0" y="369.2967224121094" zvalue="130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924603609092" ObjectName="35kV槟芒线341断路器"/>
   <cge:TPSR_Ref TObjectID="6473924603609092"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,871.97,384.648) scale(1.68868,1.53516) translate(-352.165,-128.738)" width="10" x="863.5269620796487" y="369.2967224121094"/></g>
  <g id="389">
   <use class="kv10" height="20" transform="rotate(0,682.063,718.64) scale(1.25288,-1.13898) translate(-136.401,-1348.2)" width="10" x="675.7989244017909" xlink:href="#Breaker:开关_0" y="707.2499999725464" zvalue="442"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924604264452" ObjectName="10kV1号接地变042断路器"/>
   <cge:TPSR_Ref TObjectID="6473924604264452"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,682.063,718.64) scale(1.25288,-1.13898) translate(-136.401,-1348.2)" width="10" x="675.7989244017909" y="707.2499999725464"/></g>
  <g id="403">
   <use class="kv10" height="20" transform="rotate(0,871.063,626.62) scale(1.25288,-1.13898) translate(-174.549,-1175.39)" width="10" x="864.7989216703091" xlink:href="#Breaker:开关_0" y="615.230151148999" zvalue="456"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924603740164" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924603740164"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,871.063,626.62) scale(1.25288,-1.13898) translate(-174.549,-1175.39)" width="10" x="864.7989216703091" y="615.230151148999"/></g>
  <g id="515">
   <use class="kv10" height="20" transform="rotate(0,960.563,718.64) scale(1.25288,-1.13898) translate(-192.613,-1348.2)" width="10" x="954.2989244017909" xlink:href="#Breaker:开关_0" y="707.2499999725464" zvalue="551"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924604198916" ObjectName="10kV相帕线043断路器"/>
   <cge:TPSR_Ref TObjectID="6473924604198916"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,960.563,718.64) scale(1.25288,-1.13898) translate(-192.613,-1348.2)" width="10" x="954.2989244017909" y="707.2499999725464"/></g>
  <g id="15">
   <use class="kv10" height="20" transform="rotate(0,551.554,718.64) scale(1.25288,-1.13898) translate(-110.06,-1348.2)" width="10" x="545.2894077271594" xlink:href="#Breaker:开关_0" y="707.25" zvalue="600"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924603805700" ObjectName="10kV1号电容器041断路器"/>
   <cge:TPSR_Ref TObjectID="6473924603805700"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,551.554,718.64) scale(1.25288,-1.13898) translate(-110.06,-1348.2)" width="10" x="545.2894077271594" y="707.25"/></g>
  <g id="34">
   <use class="kv10" height="20" transform="rotate(0,1229.58,718.64) scale(1.25288,-1.13898) translate(-246.912,-1348.2)" width="10" x="1223.319890010256" xlink:href="#Breaker:开关_0" y="707.2499999725464" zvalue="678"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924604067844" ObjectName="10kV备用Ⅰ回线045断路器"/>
   <cge:TPSR_Ref TObjectID="6473924604067844"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1229.58,718.64) scale(1.25288,-1.13898) translate(-246.912,-1348.2)" width="10" x="1223.319890010256" y="707.2499999725464"/></g>
  <g id="180">
   <use class="kv10" height="20" transform="rotate(0,1384.86,718.64) scale(1.25288,-1.13898) translate(-278.253,-1348.2)" width="10" x="1378.599056676923" xlink:href="#Breaker:开关_0" y="707.2499999725464" zvalue="879"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924604002308" ObjectName="10kV备用Ⅱ回线046断路器"/>
   <cge:TPSR_Ref TObjectID="6473924604002308"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1384.86,718.64) scale(1.25288,-1.13898) translate(-278.253,-1348.2)" width="10" x="1378.599056676923" y="707.2499999725464"/></g>
  <g id="203">
   <use class="kv10" height="20" transform="rotate(0,1540.14,718.64) scale(1.25288,-1.13898) translate(-309.594,-1348.2)" width="10" x="1533.87822334359" xlink:href="#Breaker:开关_0" y="707.2499999725464" zvalue="891"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924603871236" ObjectName="10kV备用Ⅲ回线047断路器"/>
   <cge:TPSR_Ref TObjectID="6473924603871236"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1540.14,718.64) scale(1.25288,-1.13898) translate(-309.594,-1348.2)" width="10" x="1533.87822334359" y="707.2499999725464"/></g>
  <g id="228">
   <use class="kv10" height="20" transform="rotate(0,1695.42,718.64) scale(1.25288,-1.13898) translate(-340.935,-1348.2)" width="10" x="1689.157390010256" xlink:href="#Breaker:开关_0" y="707.2499999725464" zvalue="903"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924603936772" ObjectName="10kV备用Ⅳ回线048断路器"/>
   <cge:TPSR_Ref TObjectID="6473924603936772"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1695.42,718.64) scale(1.25288,-1.13898) translate(-340.935,-1348.2)" width="10" x="1689.157390010256" y="707.2499999725464"/></g>
  <g id="242">
   <use class="kv10" height="20" transform="rotate(0,1086.31,718.64) scale(1.25288,-1.13898) translate(-217.993,-1348.2)" width="10" x="1080.04072334359" xlink:href="#Breaker:开关_0" y="707.2499999725464" zvalue="918"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924604133380" ObjectName="10kV芒章线044断路器"/>
   <cge:TPSR_Ref TObjectID="6473924604133380"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1086.31,718.64) scale(1.25288,-1.13898) translate(-217.993,-1348.2)" width="10" x="1080.04072334359" y="707.2499999725464"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="252">
   <path class="kv35" d="M 872.09 435.99 L 872.09 399.31" stroke-width="1" zvalue="131"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="549@0" LinkObjectIDznd="630@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 872.09 435.99 L 872.09 399.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv35" d="M 871.91 369.96 L 871.91 335.98" stroke-width="1" zvalue="134"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="630@0" LinkObjectIDznd="633@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 871.91 369.96 L 871.91 335.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="244">
   <path class="kv35" d="M 762.61 215.79 L 803.33 215.79" stroke-width="1" zvalue="153"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="413@0" LinkObjectIDznd="664@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 762.61 215.79 L 803.33 215.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="243">
   <path class="kv35" d="M 827.71 216.05 L 871.94 216.05" stroke-width="1" zvalue="155"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="664@0" LinkObjectIDznd="219" MaxPinNum="2"/>
   </metadata>
  <path d="M 827.71 216.05 L 871.94 216.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="431">
   <path class="kv35" d="M 782.76 235.48 L 782.76 216.07" stroke-width="1" zvalue="480"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="430@0" LinkObjectIDznd="244" MaxPinNum="2"/>
   </metadata>
  <path d="M 782.76 235.48 L 782.76 216.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="433">
   <path class="kv10" d="M 890.63 542.09 L 872.06 542.09" stroke-width="1" zvalue="482"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="432@0" LinkObjectIDznd="50" MaxPinNum="2"/>
   </metadata>
  <path d="M 890.63 542.09 L 872.06 542.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="439">
   <path class="kv35" d="M 1001.6 257.85 L 1001.6 244.06" stroke-width="1" zvalue="486"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="434@0" LinkObjectIDznd="438@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1001.6 257.85 L 1001.6 244.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="468">
   <path class="kv35" d="M 890.53 288.67 L 871.94 288.67" stroke-width="1" zvalue="513"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="466@0" LinkObjectIDznd="219" MaxPinNum="2"/>
   </metadata>
  <path d="M 890.53 288.67 L 871.94 288.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="480">
   <path class="kv10" d="M 581.32 552.41 L 581.32 595.92" stroke-width="1" zvalue="526"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="475@0" LinkObjectIDznd="479@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 581.32 552.41 L 581.32 595.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="481">
   <path class="kv10" d="M 581.32 652.99 L 581.32 674.5" stroke-width="1" zvalue="527"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="479@1" LinkObjectIDznd="429@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 581.32 652.99 L 581.32 674.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="512">
   <path class="kv10" d="M 971.42 759.17 L 960.5 759.17" stroke-width="1" zvalue="556"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="513@0" LinkObjectIDznd="67" MaxPinNum="2"/>
   </metadata>
  <path d="M 971.42 759.17 L 960.5 759.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="520">
   <path class="kv10" d="M 865.35 863.16 L 865.35 859.31" stroke-width="1" zvalue="565"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="519@0" LinkObjectIDznd="518@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 865.35 863.16 L 865.35 859.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv10" d="M 564.92 757.17 L 552.11 757.17" stroke-width="1" zvalue="642"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="14@0" LinkObjectIDznd="46" MaxPinNum="2"/>
   </metadata>
  <path d="M 564.92 757.17 L 552.11 757.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv10" d="M 1240.44 763.42 L 1230.35 763.42" stroke-width="1" zvalue="684"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="16@0" LinkObjectIDznd="79" MaxPinNum="2"/>
   </metadata>
  <path d="M 1240.44 763.42 L 1230.35 763.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="kv10" d="M 530.75 843.63 L 530.75 922.25 L 552.13 922.25 L 552.13 810.1" stroke-width="1" zvalue="769"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@0" LinkObjectIDznd="9@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 530.75 843.63 L 530.75 922.25 L 552.13 922.25 L 552.13 810.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv10" d="M 551.69 849.89 L 552.13 849.89" stroke-width="1" zvalue="770"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="17@0" LinkObjectIDznd="159" MaxPinNum="2"/>
   </metadata>
  <path d="M 551.69 849.89 L 552.13 849.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv10" d="M 883.06 586.5 L 872.06 586.5" stroke-width="1" zvalue="839"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="210@0" LinkObjectIDznd="50" MaxPinNum="2"/>
   </metadata>
  <path d="M 883.06 586.5 L 872.06 586.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv35" d="M 853.33 348.42 L 871.91 348.42" stroke-width="1" zvalue="843"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="424@0" LinkObjectIDznd="251" MaxPinNum="2"/>
   </metadata>
  <path d="M 853.33 348.42 L 871.91 348.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="218">
   <path class="kv35" d="M 1001.81 212.19 L 1001.81 195 L 871.94 195" stroke-width="1" zvalue="849"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="438@1" LinkObjectIDznd="219" MaxPinNum="2"/>
   </metadata>
  <path d="M 1001.81 212.19 L 1001.81 195 L 871.94 195" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="219">
   <path class="kv35" d="M 871.94 311.61 L 871.94 139.07" stroke-width="1" zvalue="850"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="633@0" LinkObjectIDznd="215@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 871.94 311.61 L 871.94 139.07" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="kv35" d="M 848.59 203.71 L 848.59 216.05" stroke-width="1" zvalue="852"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@0" LinkObjectIDznd="243" MaxPinNum="2"/>
   </metadata>
  <path d="M 848.59 203.71 L 848.59 216.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv10" d="M 1230.6 787.92 L 1230.35 787.92" stroke-width="1" zvalue="875"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@0" LinkObjectIDznd="79" MaxPinNum="2"/>
   </metadata>
  <path d="M 1230.6 787.92 L 1230.35 787.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv10" d="M 1395.72 764.17 L 1385.62 764.17" stroke-width="1" zvalue="884"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@0" LinkObjectIDznd="90" MaxPinNum="2"/>
   </metadata>
  <path d="M 1395.72 764.17 L 1385.62 764.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv10" d="M 1385.88 788.67 L 1385.62 788.67" stroke-width="1" zvalue="887"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="147@0" LinkObjectIDznd="90" MaxPinNum="2"/>
   </metadata>
  <path d="M 1385.88 788.67 L 1385.62 788.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv10" d="M 1551 766.42 L 1540.9 766.42" stroke-width="1" zvalue="896"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="106" MaxPinNum="2"/>
   </metadata>
  <path d="M 1551 766.42 L 1540.9 766.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="kv10" d="M 1541.16 790.92 L 1540.9 790.92" stroke-width="1" zvalue="899"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@0" LinkObjectIDznd="106" MaxPinNum="2"/>
   </metadata>
  <path d="M 1541.16 790.92 L 1540.9 790.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv10" d="M 1706.28 766.67 L 1696.23 766.73" stroke-width="1" zvalue="908"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@0" LinkObjectIDznd="112" MaxPinNum="2"/>
   </metadata>
  <path d="M 1706.28 766.67 L 1696.23 766.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="kv10" d="M 1696.44 793.17 L 1696.23 793.17" stroke-width="1" zvalue="911"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@0" LinkObjectIDznd="112" MaxPinNum="2"/>
   </metadata>
  <path d="M 1696.44 793.17 L 1696.23 793.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="kv10" d="M 960.5 796.88 L 960.5 865.51" stroke-width="1" zvalue="914"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67" LinkObjectIDznd="230@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.5 796.88 L 960.5 865.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="239">
   <path class="kv10" d="M 1097.16 763.67 L 1087.05 763.67" stroke-width="1" zvalue="923"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="240@0" LinkObjectIDznd="72" MaxPinNum="2"/>
   </metadata>
  <path d="M 1097.16 763.67 L 1087.05 763.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="236">
   <path class="kv10" d="M 1087.33 788.17 L 1087.05 788.17" stroke-width="1" zvalue="926"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@0" LinkObjectIDznd="72" MaxPinNum="2"/>
   </metadata>
  <path d="M 1087.33 788.17 L 1087.05 788.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="246">
   <path class="kv10" d="M 960.5 779.67 L 960.08 779.67" stroke-width="1" zvalue="929"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67" LinkObjectIDznd="247@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.5 779.67 L 960.08 779.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="256">
   <path class="kv10" d="M 758.59 870.79 L 758.59 825 L 681.84 825" stroke-width="1" zvalue="937"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="255@0" LinkObjectIDznd="250@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 758.59 870.79 L 758.59 825 L 681.84 825" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="257">
   <path class="kv10" d="M 718.03 835.73 L 718.03 825" stroke-width="1" zvalue="938"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162@1" LinkObjectIDznd="256" MaxPinNum="2"/>
   </metadata>
  <path d="M 718.03 835.73 L 718.03 825" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="259">
   <path class="kv10" d="M 718.05 870.54 L 718.05 860.1" stroke-width="1" zvalue="940"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="258@0" LinkObjectIDznd="162@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 718.05 870.54 L 718.05 860.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="7">
   <path class="kv10" d="M 865.22 793.83 L 865.15 807" stroke-width="1" zvalue="941"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="522@0" LinkObjectIDznd="67" MaxPinNum="2"/>
   </metadata>
  <path d="M 865.22 793.83 L 865.15 807" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv10" d="M 872.08 567.67 L 872.06 567.67" stroke-width="1" zvalue="944"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="11@0" LinkObjectIDznd="50" MaxPinNum="2"/>
   </metadata>
  <path d="M 872.08 567.67 L 872.06 567.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="25">
   <path class="kv10" d="M 552.08 769.67 L 552.11 769.67" stroke-width="1" zvalue="947"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@0" LinkObjectIDznd="46" MaxPinNum="2"/>
   </metadata>
  <path d="M 552.08 769.67 L 552.11 769.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv10" d="M 914.67 808.08 L 914.67 807" stroke-width="1" zvalue="953"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="67" MaxPinNum="2"/>
   </metadata>
  <path d="M 914.67 808.08 L 914.67 807" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv35" d="M 872.08 453.76 L 924.59 453.76 L 924.59 485.79" stroke-width="1" zvalue="954"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="549@2" LinkObjectIDznd="211@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 872.08 453.76 L 924.59 453.76 L 924.59 485.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv10" d="M 551.64 707.76 L 551.64 695.36" stroke-width="1" zvalue="966"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15@1" LinkObjectIDznd="41@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 551.64 707.76 L 551.64 695.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv10" d="M 551.84 686.86 L 551.84 674.5" stroke-width="1" zvalue="967"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="429@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 551.84 686.86 L 551.84 674.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv10" d="M 552.11 785.73 L 552.11 748.99" stroke-width="1" zvalue="968"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@1" LinkObjectIDznd="42@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 552.11 785.73 L 552.11 748.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv10" d="M 552.29 740.49 L 552.29 729.54" stroke-width="1" zvalue="969"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@1" LinkObjectIDznd="15@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 552.29 740.49 L 552.29 729.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv10" d="M 872.06 508.71 L 872.07 593.81" stroke-width="1" zvalue="973"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="549@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 872.06 508.71 L 872.07 593.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv10" d="M 872.09 602.31 L 872.09 615.74" stroke-width="1" zvalue="974"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="48@1" LinkObjectIDznd="403@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 872.09 602.31 L 872.09 615.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv10" d="M 871.02 637.52 L 871.09 647.45" stroke-width="1" zvalue="975"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="403@0" LinkObjectIDznd="49@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 871.02 637.52 L 871.09 647.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv10" d="M 871.07 655.94 L 871.07 674.5" stroke-width="1" zvalue="976"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="429@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 871.07 655.94 L 871.07 674.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv10" d="M 682.15 707.76 L 682.18 695.33" stroke-width="1" zvalue="980"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="389@1" LinkObjectIDznd="55@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 682.15 707.76 L 682.18 695.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv10" d="M 682.15 686.83 L 682.15 674.5" stroke-width="1" zvalue="981"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="429@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 682.15 686.83 L 682.15 674.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv10" d="M 682.02 729.54 L 682.05 741.16" stroke-width="1" zvalue="982"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="389@0" LinkObjectIDznd="56@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 682.02 729.54 L 682.05 741.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv10" d="M 960.52 729.54 L 960.52 740.47" stroke-width="1" zvalue="987"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="515@0" LinkObjectIDznd="63@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.52 729.54 L 960.52 740.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv10" d="M 960.5 748.96 L 960.5 807 L 865.15 807 L 865.15 827.44" stroke-width="1" zvalue="988"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="518@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.5 748.96 L 960.5 807 L 865.15 807 L 865.15 827.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv10" d="M 960.65 707.76 L 960.68 695.33" stroke-width="1" zvalue="989"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="515@1" LinkObjectIDznd="62@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.65 707.76 L 960.68 695.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv10" d="M 960.65 686.83 L 960.65 674.5" stroke-width="1" zvalue="990"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="429@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 960.65 686.83 L 960.65 674.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv10" d="M 1087.05 883.51 L 1087.05 749.55" stroke-width="1" zvalue="994"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="245@0" LinkObjectIDznd="71@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1087.05 883.51 L 1087.05 749.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv10" d="M 1087.43 741.05 L 1087.43 729.54" stroke-width="1" zvalue="995"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@1" LinkObjectIDznd="242@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1087.43 741.05 L 1087.43 729.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 1086.39 707.76 L 1086.42 695.92" stroke-width="1" zvalue="996"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="242@1" LinkObjectIDznd="70@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1086.39 707.76 L 1086.42 695.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv10" d="M 1086.4 687.42 L 1086.4 674.5" stroke-width="1" zvalue="997"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="429@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1086.4 687.42 L 1086.4 674.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv10" d="M 1230.35 882.9 L 1230.35 749.55" stroke-width="1" zvalue="1001"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@0" LinkObjectIDznd="78@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1230.35 882.9 L 1230.35 749.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv10" d="M 1230.37 741.05 L 1230.37 729.54" stroke-width="1" zvalue="1002"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="78@1" LinkObjectIDznd="34@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1230.37 741.05 L 1230.37 729.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv10" d="M 1229.67 707.76 L 1229.67 695.92" stroke-width="1" zvalue="1003"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@1" LinkObjectIDznd="77@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1229.67 707.76 L 1229.67 695.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv10" d="M 1229.92 687.42 L 1229.92 674.5" stroke-width="1" zvalue="1004"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@0" LinkObjectIDznd="429@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1229.92 687.42 L 1229.92 674.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv10" d="M 1385.62 883.65 L 1385.62 749.55" stroke-width="1" zvalue="1008"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="86@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1385.62 883.65 L 1385.62 749.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv10" d="M 1385.64 741.05 L 1385.64 729.54" stroke-width="1" zvalue="1009"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@1" LinkObjectIDznd="180@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1385.64 741.05 L 1385.64 729.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv10" d="M 1384.95 707.76 L 1384.98 695.92" stroke-width="1" zvalue="1010"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@1" LinkObjectIDznd="85@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1384.95 707.76 L 1384.98 695.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv10" d="M 1384.95 687.42 L 1384.95 674.5" stroke-width="1" zvalue="1011"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85@0" LinkObjectIDznd="429@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1384.95 687.42 L 1384.95 674.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv10" d="M 1540.23 707.76 L 1540.26 695.33" stroke-width="1" zvalue="1015"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@1" LinkObjectIDznd="97@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1540.23 707.76 L 1540.26 695.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv10" d="M 1540.23 686.83 L 1540.23 674.5" stroke-width="1" zvalue="1016"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="429@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1540.23 686.83 L 1540.23 674.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv10" d="M 1540.9 885.9 L 1540.9 749.55" stroke-width="1" zvalue="1017"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="204@0" LinkObjectIDznd="99@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1540.9 885.9 L 1540.9 749.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="kv10" d="M 1540.92 741.05 L 1540.92 729.54" stroke-width="1" zvalue="1018"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@1" LinkObjectIDznd="203@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1540.92 741.05 L 1540.92 729.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv10" d="M 1696.23 886.15 L 1696.23 750.14" stroke-width="1" zvalue="1022"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@0" LinkObjectIDznd="111@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1696.23 886.15 L 1696.23 750.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv10" d="M 1696.25 741.64 L 1696.25 729.54" stroke-width="1" zvalue="1023"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@1" LinkObjectIDznd="228@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1696.25 741.64 L 1696.25 729.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv10" d="M 1695.51 707.76 L 1695.51 696.51" stroke-width="1" zvalue="1024"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="228@1" LinkObjectIDznd="109@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1695.51 707.76 L 1695.51 696.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="116">
   <path class="kv10" d="M 1695.8 688.01 L 1695.8 674.5" stroke-width="1" zvalue="1025"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="429@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1695.8 688.01 L 1695.8 674.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv10" d="M 682.03 749.66 L 682.03 825" stroke-width="1" zvalue="1026"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@0" LinkObjectIDznd="256" MaxPinNum="2"/>
   </metadata>
  <path d="M 682.03 749.66 L 682.03 825" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv10" d="M 696.92 759.17 L 682.02 759.17" stroke-width="1" zvalue="1027"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="396@0" LinkObjectIDznd="8" MaxPinNum="2"/>
   </metadata>
  <path d="M 696.92 759.17 L 682.02 759.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv10" d="M 682.25 790.67 L 682.02 790.67" stroke-width="1" zvalue="1028"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@0" LinkObjectIDznd="8" MaxPinNum="2"/>
   </metadata>
  <path d="M 682.25 790.67 L 682.02 790.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="633">
   <use class="kv35" height="30" transform="rotate(0,871.863,323.694) scale(0.833333,0.833333) translate(173.123,62.2389)" width="15" x="865.6131037206526" xlink:href="#Disconnector:刀闸_0" y="311.1944428814782" zvalue="133"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450392424454" ObjectName="35kV槟芒线3416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450392424454"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,871.863,323.694) scale(0.833333,0.833333) translate(173.123,62.2389)" width="15" x="865.6131037206526" y="311.1944428814782"/></g>
  <g id="664">
   <use class="kv35" height="30" transform="rotate(270,815.621,216.121) scale(0.833333,-0.833333) translate(161.874,-477.965)" width="15" x="809.3705691885017" xlink:href="#Disconnector:刀闸_0" y="203.6205674930806" zvalue="152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450393997317" ObjectName="35kV槟芒线3419隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450393997317"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,815.621,216.121) scale(0.833333,-0.833333) translate(161.874,-477.965)" width="15" x="809.3705691885017" y="203.6205674930806"/></g>
  <g id="162">
   <use class="kv10" height="30" transform="rotate(0,717.977,848.015) scale(0.833333,-0.833333) translate(142.345,-1868.13)" width="15" x="711.726852457616" xlink:href="#Disconnector:刀闸_0" y="835.5153504566865" zvalue="284"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450396422149" ObjectName="10kV1号接地变0426隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450396422149"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,717.977,848.015) scale(0.833333,-0.833333) translate(142.345,-1868.13)" width="15" x="711.726852457616" y="835.5153504566865"/></g>
  <g id="438">
   <use class="kv35" height="30" transform="rotate(180,1001.71,227.5) scale(1.25,1.25) translate(-198.467,-41.75)" width="15" x="992.3333333333335" xlink:href="#Disconnector:令克_0" y="208.75" zvalue="485"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450393866245" ObjectName="35kV槟芒线3418跌落熔断器"/>
   <cge:TPSR_Ref TObjectID="6192450393866245"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1001.71,227.5) scale(1.25,1.25) translate(-198.467,-41.75)" width="15" x="992.3333333333335" y="208.75"/></g>
  <g id="479">
   <use class="kv10" height="33" transform="rotate(0,581.315,624.454) scale(1.8439,1.78325) translate(-253.392,-261.353)" width="30" x="553.6569707157805" xlink:href="#Disconnector:小车带电壁雷_0" y="595.0303765175738" zvalue="525"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450393014277" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450393014277"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,581.315,624.454) scale(1.8439,1.78325) translate(-253.392,-261.353)" width="30" x="553.6569707157805" y="595.0303765175738"/></g>
  <g id="518">
   <use class="kv10" height="30" transform="rotate(0,865.25,842.75) scale(1.25,-1.25) translate(-171.175,-1513.2)" width="15" x="855.875" xlink:href="#Disconnector:令克_0" y="824" zvalue="562"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450395963397" ObjectName="10kV相帕线0438跌落熔断器"/>
   <cge:TPSR_Ref TObjectID="6192450395963397"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,865.25,842.75) scale(1.25,-1.25) translate(-171.175,-1513.2)" width="15" x="855.875" y="824"/></g>
  <g id="9">
   <use class="kv10" height="30" transform="rotate(0,552.058,798.015) scale(0.833333,-0.833333) translate(109.162,-1758.13)" width="15" x="545.8082010582013" xlink:href="#Disconnector:刀闸_0" y="785.5153504566865" zvalue="609"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450393079813" ObjectName="10kV1号电容器0416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450393079813"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,552.058,798.015) scale(0.833333,-0.833333) translate(109.162,-1758.13)" width="15" x="545.8082010582013" y="785.5153504566865"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="396">
   <use class="kv10" height="20" transform="rotate(270,707.75,759.222) scale(1.11111,1.11111) translate(-70.2194,-74.8111)" width="10" x="702.1944444444445" xlink:href="#GroundDisconnector:地刀_0" y="748.1111111111111" zvalue="449"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450396553221" ObjectName="10kV1号接地变04267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450396553221"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,707.75,759.222) scale(1.11111,1.11111) translate(-70.2194,-74.8111)" width="10" x="702.1944444444445" y="748.1111111111111"/></g>
  <g id="424">
   <use class="kv35" height="20" transform="rotate(90,842.5,348.472) scale(-1.11111,1.11111) translate(-1600.19,-33.7361)" width="10" x="836.9444444444445" xlink:href="#GroundDisconnector:地刀_0" y="337.3611111111111" zvalue="475"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450392752133" ObjectName="35kV槟芒线34160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450392752133"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,842.5,348.472) scale(-1.11111,1.11111) translate(-1600.19,-33.7361)" width="10" x="836.9444444444445" y="337.3611111111111"/></g>
  <g id="430">
   <use class="kv35" height="20" transform="rotate(180,782.702,246.315) scale(-1.11111,-1.11111) translate(-1486.58,-466.887)" width="10" x="777.1464585228999" xlink:href="#GroundDisconnector:地刀_0" y="235.203900826414" zvalue="478"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450394128389" ObjectName="35kV槟芒线34197接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450394128389"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,782.702,246.315) scale(-1.11111,-1.11111) translate(-1486.58,-466.887)" width="10" x="777.1464585228999" y="235.203900826414"/></g>
  <g id="466">
   <use class="kv35" height="20" transform="rotate(270,901.361,288.722) scale(1.11111,1.11111) translate(-89.5806,-27.7611)" width="10" x="895.8055555555555" xlink:href="#GroundDisconnector:地刀_0" y="277.6111111111111" zvalue="512"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450392883205" ObjectName="35kV槟芒线34167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450392883205"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,901.361,288.722) scale(1.11111,1.11111) translate(-89.5806,-27.7611)" width="10" x="895.8055555555555" y="277.6111111111111"/></g>
  <g id="513">
   <use class="kv10" height="20" transform="rotate(270,982.25,759.222) scale(1.11111,1.11111) translate(-97.6694,-74.8111)" width="10" x="976.6944444444445" xlink:href="#GroundDisconnector:地刀_0" y="748.1111111111111" zvalue="554"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450396094469" ObjectName="10kV相帕线04367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450396094469"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,982.25,759.222) scale(1.11111,1.11111) translate(-97.6694,-74.8111)" width="10" x="976.6944444444445" y="748.1111111111111"/></g>
  <g id="14">
   <use class="kv10" height="20" transform="rotate(270,575.75,757.222) scale(1.11111,1.11111) translate(-57.0194,-74.6111)" width="10" x="570.1944444444445" xlink:href="#GroundDisconnector:地刀_0" y="746.1111111111111" zvalue="602"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450393210885" ObjectName="10kV1号电容器04160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450393210885"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,575.75,757.222) scale(1.11111,1.11111) translate(-57.0194,-74.6111)" width="10" x="570.1944444444445" y="746.1111111111111"/></g>
  <g id="20">
   <use class="kv10" height="40" transform="rotate(90,531.25,839.625) scale(-1,1) translate(-1062.5,0)" width="20" x="521.25" xlink:href="#GroundDisconnector:支那变双联地刀_0" y="819.625" zvalue="619"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450393407493" ObjectName="10kV1号电容器04167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450393407493"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,531.25,839.625) scale(-1,1) translate(-1062.5,0)" width="20" x="521.25" y="819.625"/></g>
  <g id="16">
   <use class="kv10" height="20" transform="rotate(270,1251.27,763.472) scale(1.11111,1.11111) translate(-124.572,-75.2361)" width="10" x="1245.71541005291" xlink:href="#GroundDisconnector:地刀_0" y="752.3611111111111" zvalue="682"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450395308037" ObjectName="10kV备用Ⅰ回线04567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450395308037"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1251.27,763.472) scale(1.11111,1.11111) translate(-124.572,-75.2361)" width="10" x="1245.71541005291" y="752.3611111111111"/></g>
  <g id="210">
   <use class="kv10" height="20" transform="rotate(270,893.891,586.556) scale(1.11111,1.11111) translate(-88.8335,-57.5444)" width="10" x="888.3352730328216" xlink:href="#GroundDisconnector:地刀_0" y="575.4444444444445" zvalue="837"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450393604101" ObjectName="#1主变10kV侧00167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450393604101"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,893.891,586.556) scale(1.11111,1.11111) translate(-88.8335,-57.5444)" width="10" x="888.3352730328216" y="575.4444444444445"/></g>
  <g id="151">
   <use class="kv10" height="20" transform="rotate(270,1406.55,764.222) scale(1.11111,1.11111) translate(-140.099,-75.3111)" width="10" x="1400.994576719577" xlink:href="#GroundDisconnector:地刀_0" y="753.1111111111111" zvalue="882"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450395045893" ObjectName="10kV备用Ⅱ回线04667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450395045893"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1406.55,764.222) scale(1.11111,1.11111) translate(-140.099,-75.3111)" width="10" x="1400.994576719577" y="753.1111111111111"/></g>
  <g id="197">
   <use class="kv10" height="20" transform="rotate(270,1561.83,766.472) scale(1.11111,1.11111) translate(-155.627,-75.5361)" width="10" x="1556.273743386243" xlink:href="#GroundDisconnector:地刀_0" y="755.3611111111111" zvalue="894"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450394521605" ObjectName="10kV备用Ⅲ回线04767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450394521605"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1561.83,766.472) scale(1.11111,1.11111) translate(-155.627,-75.5361)" width="10" x="1556.273743386243" y="755.3611111111111"/></g>
  <g id="226">
   <use class="kv10" height="20" transform="rotate(270,1717.11,766.722) scale(1.11111,1.11111) translate(-171.155,-75.5611)" width="10" x="1711.55291005291" xlink:href="#GroundDisconnector:地刀_0" y="755.6111111111111" zvalue="906"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450394783749" ObjectName="10kV备用Ⅳ回线04867接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450394783749"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1717.11,766.722) scale(1.11111,1.11111) translate(-171.155,-75.5611)" width="10" x="1711.55291005291" y="755.6111111111111"/></g>
  <g id="240">
   <use class="kv10" height="20" transform="rotate(270,1107.99,763.722) scale(1.11111,1.11111) translate(-110.244,-75.2611)" width="10" x="1102.436243386243" xlink:href="#GroundDisconnector:地刀_0" y="752.6111111111111" zvalue="921"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450395570181" ObjectName="10kV芒章线04467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450395570181"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1107.99,763.722) scale(1.11111,1.11111) translate(-110.244,-75.2611)" width="10" x="1102.436243386243" y="752.6111111111111"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="413">
   <use class="kv35" height="45" transform="rotate(90,728.628,222.425) scale(-1.78299,1.70231) translate(-1117.71,-75.9626)" width="50" x="684.0535952710159" xlink:href="#Accessory:四绕组PT带保险_0" y="184.1231447257861" zvalue="463"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450392621062" ObjectName="35kV线路电压互感器"/>
   </metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(90,728.628,222.425) scale(-1.78299,1.70231) translate(-1117.71,-75.9626)" width="50" x="684.0535952710159" y="184.1231447257861"/></g>
  <g id="432">
   <use class="kv10" height="26" transform="rotate(270,906.083,542.125) scale(0.9375,1.25) translate(60.0306,-105.175)" width="12" x="900.4583333333335" xlink:href="#Accessory:避雷器1_0" y="525.875" zvalue="481"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450393473029" ObjectName="2号主变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,906.083,542.125) scale(0.9375,1.25) translate(60.0306,-105.175)" width="12" x="900.4583333333335" y="525.875"/></g>
  <g id="475">
   <use class="kv10" height="45" transform="rotate(0,576.083,524.125) scale(1.40506,-1.41667) translate(-155.952,-884.721)" width="50" x="540.9567090049761" xlink:href="#Accessory:四绕组PT带保险_0" y="492.25" zvalue="521"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450392948741" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,576.083,524.125) scale(1.40506,-1.41667) translate(-155.952,-884.721)" width="50" x="540.9567090049761" y="492.25"/></g>
  <g id="522">
   <use class="kv10" height="26" transform="rotate(0,865.25,778.375) scale(-0.9375,-1.25) translate(-1788.56,-1397.83)" width="12" x="859.625" xlink:href="#Accessory:避雷器1_0" y="762.125" zvalue="567"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450395832325" ObjectName="2号站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,865.25,778.375) scale(-0.9375,-1.25) translate(-1788.56,-1397.83)" width="12" x="859.625" y="762.125"/></g>
  <g id="211">
   <use class="kv35" height="26" transform="rotate(0,924.625,501.25) scale(-0.9375,1.25) translate(-1911.27,-97)" width="12" x="919" xlink:href="#Accessory:避雷器1_0" y="485" zvalue="841"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450393669637" ObjectName="1号主变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,924.625,501.25) scale(-0.9375,1.25) translate(-1911.27,-97)" width="12" x="919" y="485"/></g>
  <g id="214">
   <use class="kv35" height="26" transform="rotate(180,848.625,188.25) scale(0.9375,1.25) translate(56.2,-34.4)" width="12" x="843" xlink:href="#Accessory:避雷器1_0" y="172" zvalue="845"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450393735173" ObjectName="35kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,848.625,188.25) scale(0.9375,1.25) translate(56.2,-34.4)" width="12" x="843" y="172"/></g>
  <g id="141">
   <use class="kv10" height="30" transform="rotate(0,1230.52,791.25) scale(1,1) translate(0,0)" width="30" x="1215.520965608465" xlink:href="#Accessory:放电间隙2_0" y="776.25" zvalue="874"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450395176965" ObjectName="045"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1230.52,791.25) scale(1,1) translate(0,0)" width="30" x="1215.520965608465" y="776.25"/></g>
  <g id="147">
   <use class="kv10" height="30" transform="rotate(0,1385.8,792) scale(1,1) translate(0,0)" width="30" x="1370.800132275132" xlink:href="#Accessory:放电间隙2_0" y="777" zvalue="886"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450394914821" ObjectName="046"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1385.8,792) scale(1,1) translate(0,0)" width="30" x="1370.800132275132" y="777"/></g>
  <g id="192">
   <use class="kv10" height="30" transform="rotate(0,1541.08,794.25) scale(1,1) translate(0,0)" width="30" x="1526.079298941799" xlink:href="#Accessory:放电间隙2_0" y="779.25" zvalue="898"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450394390533" ObjectName="047"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1541.08,794.25) scale(1,1) translate(0,0)" width="30" x="1526.079298941799" y="779.25"/></g>
  <g id="223">
   <use class="kv10" height="30" transform="rotate(0,1696.36,796.5) scale(1,1) translate(0,0)" width="30" x="1681.358465608465" xlink:href="#Accessory:放电间隙2_0" y="781.5" zvalue="910"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450394652677" ObjectName="048"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1696.36,796.5) scale(1,1) translate(0,0)" width="30" x="1681.358465608465" y="781.5"/></g>
  <g id="237">
   <use class="kv10" height="30" transform="rotate(0,1087.24,791.5) scale(1,1) translate(0,0)" width="30" x="1072.241798941799" xlink:href="#Accessory:放电间隙2_0" y="776.5" zvalue="925"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450395439109" ObjectName="044"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1087.24,791.5) scale(1,1) translate(0,0)" width="30" x="1072.241798941799" y="776.5"/></g>
  <g id="247">
   <use class="kv10" height="30" transform="rotate(0,960,783) scale(1,1) translate(0,0)" width="30" x="945" xlink:href="#Accessory:放电间隙2_0" y="768" zvalue="928"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450395701253" ObjectName="043"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,960,783) scale(1,1) translate(0,0)" width="30" x="945" y="768"/></g>
  <g id="255">
   <use class="kv10" height="26" transform="rotate(0,758.625,886.25) scale(-0.9375,1.25) translate(-1568.2,-174)" width="12" x="753" xlink:href="#Accessory:避雷器1_0" y="870" zvalue="936"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450396684293" ObjectName="10kV1号接地变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,758.625,886.25) scale(-0.9375,1.25) translate(-1568.2,-174)" width="12" x="753" y="870"/></g>
  <g id="258">
   <use class="kv10" height="25" transform="rotate(0,717.963,891.25) scale(1.74,1.74) translate(-302.381,-369.787)" width="8" x="711.0029999999999" xlink:href="#Accessory:芒章接地变中性点_0" y="869.5" zvalue="939"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450396749829" ObjectName="10kV1号接地变附属"/>
   </metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,717.963,891.25) scale(1.74,1.74) translate(-302.381,-369.787)" width="8" x="711.0029999999999" y="869.5"/></g>
  <g id="11">
   <use class="kv10" height="30" transform="rotate(0,872,571) scale(1,1) translate(0,0)" width="30" x="857" xlink:href="#Accessory:放电间隙2_0" y="556" zvalue="943"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450396160005" ObjectName="#1主变10"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,872,571) scale(1,1) translate(0,0)" width="30" x="857" y="556"/></g>
  <g id="24">
   <use class="kv10" height="30" transform="rotate(0,552,773) scale(1,1) translate(0,0)" width="30" x="537" xlink:href="#Accessory:放电间隙2_0" y="758" zvalue="946"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450396225541" ObjectName="041"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,552,773) scale(1,1) translate(0,0)" width="30" x="537" y="758"/></g>
  <g id="26">
   <use class="kv10" height="30" transform="rotate(0,682.167,794) scale(1,1) translate(0,0)" width="30" x="667.1666666666667" xlink:href="#Accessory:放电间隙2_0" y="779" zvalue="949"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450396291077" ObjectName="042"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,682.167,794) scale(1,1) translate(0,0)" width="30" x="667.1666666666667" y="779"/></g>
  <g id="28">
   <use class="kv10" height="30" transform="rotate(270,918,808) scale(-1,1) translate(-1836,0)" width="30" x="903" xlink:href="#Accessory:放电间隙2_0" y="793" zvalue="952"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450396356613" ObjectName="043-1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,918,808) scale(-1,1) translate(-1836,0)" width="30" x="903" y="793"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="434">
   <use class="kv35" height="30" transform="rotate(180,1001.6,286.594) scale(-2.13854,-2.03125) translate(-1458.58,-412.217)" width="20" x="980.2187500000002" xlink:href="#EnergyConsumer:站用变无融断_0" y="256.125" zvalue="483"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450393931781" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1001.6,286.594) scale(-2.13854,-2.03125) translate(-1458.58,-412.217)" width="20" x="980.2187500000002" y="256.125"/></g>
  <g id="519">
   <use class="kv10" height="30" transform="rotate(0,865.354,891.906) scale(2.10104,2.03125) translate(-442.475,-437.345)" width="20" x="844.34375" xlink:href="#EnergyConsumer:站用变无融断_0" y="861.4375" zvalue="564"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450395897861" ObjectName="10kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,865.354,891.906) scale(2.10104,2.03125) translate(-442.475,-437.345)" width="20" x="844.34375" y="861.4375"/></g>
  <g id="37">
   <use class="kv10" height="30" transform="rotate(0,1230.35,902.25) scale(1.79167,-1.43333) translate(-538.892,-1525.23)" width="12" x="1219.597001064808" xlink:href="#EnergyConsumer:负荷_0" y="880.75" zvalue="676"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450395373573" ObjectName="10kV备用Ⅰ回线"/>
   <cge:TPSR_Ref TObjectID="6192450395373573"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1230.35,902.25) scale(1.79167,-1.43333) translate(-538.892,-1525.23)" width="12" x="1219.597001064808" y="880.75"/></g>
  <g id="182">
   <use class="kv10" height="30" transform="rotate(0,1385.62,903) scale(1.79167,-1.43333) translate(-607.499,-1526.5)" width="12" x="1374.86608104247" xlink:href="#EnergyConsumer:负荷_0" y="881.5" zvalue="877"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450395111429" ObjectName="10kV备用Ⅱ回线"/>
   <cge:TPSR_Ref TObjectID="6192450395111429"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1385.62,903) scale(1.79167,-1.43333) translate(-607.499,-1526.5)" width="12" x="1374.86608104247" y="881.5"/></g>
  <g id="204">
   <use class="kv10" height="30" transform="rotate(0,1540.9,905.25) scale(1.79167,-1.43333) translate(-676.111,-1530.32)" width="12" x="1530.145247709137" xlink:href="#EnergyConsumer:负荷_0" y="883.75" zvalue="889"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450394587141" ObjectName="10kV备用Ⅲ回线"/>
   <cge:TPSR_Ref TObjectID="6192450394587141"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1540.9,905.25) scale(1.79167,-1.43333) translate(-676.111,-1530.32)" width="12" x="1530.145247709137" y="883.75"/></g>
  <g id="229">
   <use class="kv10" height="30" transform="rotate(0,1696.23,905.5) scale(1.79167,-1.43333) translate(-744.747,-1530.74)" width="12" x="1685.479354005985" xlink:href="#EnergyConsumer:负荷_0" y="884" zvalue="901"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450394849285" ObjectName="10kV备用Ⅳ回线"/>
   <cge:TPSR_Ref TObjectID="6192450394849285"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1696.23,905.5) scale(1.79167,-1.43333) translate(-744.747,-1530.74)" width="12" x="1685.479354005985" y="884"/></g>
  <g id="230">
   <use class="kv10" height="30" transform="rotate(0,963.583,884.5) scale(1.43333,-1.43333) translate(-288.066,-1495.09)" width="15" x="952.8333333333334" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="863" zvalue="913"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450395766789" ObjectName="10kV相帕线"/>
   <cge:TPSR_Ref TObjectID="6192450395766789"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,963.583,884.5) scale(1.43333,-1.43333) translate(-288.066,-1495.09)" width="15" x="952.8333333333334" y="863"/></g>
  <g id="245">
   <use class="kv10" height="30" transform="rotate(0,1090.63,902.5) scale(1.43333,-1.43333) translate(-326.476,-1525.65)" width="15" x="1079.883333333333" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="881" zvalue="916"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450395635717" ObjectName="10kV芒章线"/>
   <cge:TPSR_Ref TObjectID="6192450395635717"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1090.63,902.5) scale(1.43333,-1.43333) translate(-326.476,-1525.65)" width="15" x="1079.883333333333" y="881"/></g>
  <g id="250">
   <use class="kv10" height="19" transform="rotate(0,681.443,825) scale(1.59091,1.57895) translate(-246.607,-297)" width="22" x="663.9429227780005" xlink:href="#EnergyConsumer:接地变X_0" y="809.9999999999999" zvalue="933"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450396618757" ObjectName="10kV1号接地变"/>
   </metadata>
  <rect fill="white" height="19" opacity="0" stroke="white" transform="rotate(0,681.443,825) scale(1.59091,1.57895) translate(-246.607,-297)" width="22" x="663.9429227780005" y="809.9999999999999"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="17">
   <use class="kv10" height="40" transform="rotate(0,551.688,874.75) scale(1.49479,1.625) translate(-176.677,-323.942)" width="24" x="533.75" xlink:href="#Compensator:10kV电容器_0" y="842.25" zvalue="617"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450393276421" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192450393276421"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,551.688,874.75) scale(1.49479,1.625) translate(-176.677,-323.942)" width="24" x="533.75" y="842.25"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="76">
   <text Format="f4.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="76" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,551.188,976.125) scale(1,1) translate(0,0)" writing-mode="lr" x="551.38" xml:space="preserve" y="981.01" zvalue="1">Q:dd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128413495299" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f4.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="80" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,548.188,1004.25) scale(1,1) translate(0,0)" writing-mode="lr" x="548.38" xml:space="preserve" y="1009.14" zvalue="1">Ia:dd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128413560835" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="89" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1085.33,266.938) scale(1,1) translate(0,0)" writing-mode="lr" x="1085.53" xml:space="preserve" y="271.85" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128415264771" ObjectName="P"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="93" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1223.83,967) scale(1,1) translate(0,0)" writing-mode="lr" x="1224.02" xml:space="preserve" y="971.89" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128421294083" ObjectName="P"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="95" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1085.33,285.938) scale(1,1) translate(0,0)" writing-mode="lr" x="1085.53" xml:space="preserve" y="290.85" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128415330307" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="100" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1223.83,990) scale(1,1) translate(0,0)" writing-mode="lr" x="1224.02" xml:space="preserve" y="994.89" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128421359619" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="102" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1085.33,304.938) scale(1,1) translate(0,0)" writing-mode="lr" x="1085.53" xml:space="preserve" y="309.85" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128415395843" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="110">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="110" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1223.83,1013) scale(1,1) translate(0,0)" writing-mode="lr" x="1224.02" xml:space="preserve" y="1017.89" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128421425155" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="119">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,251.042,521.25) scale(1,1) translate(2.50556e-14,0)" writing-mode="lr" x="251.15" xml:space="preserve" y="526.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128411398147" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="120">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="120" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,251.042,544.25) scale(1,1) translate(2.50556e-14,5.96467e-14)" writing-mode="lr" x="251.15" xml:space="preserve" y="549.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128411463683" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="121">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,251.042,567.25) scale(1,1) translate(2.50556e-14,-1.244e-13)" writing-mode="lr" x="251.15" xml:space="preserve" y="572.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128411529219" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="123">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,251.042,495.75) scale(1,1) translate(2.50556e-14,-1.08524e-13)" writing-mode="lr" x="251.15" xml:space="preserve" y="500.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128411660291" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="125">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="125" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,136,204) scale(1,1) translate(0,0)" writing-mode="lr" x="136.15" xml:space="preserve" y="210.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128410480643" ObjectName="F"/>
   </metadata>
  </g>
  <g id="133">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="133" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,772.563,375.597) scale(1,1) translate(0,8.12683e-14)" writing-mode="lr" x="772.8" xml:space="preserve" y="380.47" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128409366531" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="134">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="134" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,772.563,395.694) scale(1,1) translate(0,8.57308e-14)" writing-mode="lr" x="772.8" xml:space="preserve" y="400.57" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128409432067" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="135">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="135" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,772.563,419.694) scale(1,1) translate(0,9.10599e-14)" writing-mode="lr" x="772.8" xml:space="preserve" y="424.57" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128409628675" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="136" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,993.563,566.694) scale(1,1) translate(0,0)" writing-mode="lr" x="993.8" xml:space="preserve" y="571.6799999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128409497603" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="137" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,993.563,589.694) scale(1,1) translate(0,0)" writing-mode="lr" x="993.8" xml:space="preserve" y="594.6799999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128409563139" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="138" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,993.563,612.694) scale(1,1) translate(0,0)" writing-mode="lr" x="993.8" xml:space="preserve" y="617.6799999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128409956355" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="139">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,124,252.194) scale(1,1) translate(0,0)" writing-mode="lr" x="124.15" xml:space="preserve" y="258.47" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128409825283" ObjectName="YW1"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="140" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,123,272.597) scale(1,1) translate(0,2.59986e-13)" writing-mode="lr" x="123.15" xml:space="preserve" y="279.08" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128409890819" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="150" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,251.042,594.25) scale(1,1) translate(2.50556e-14,1.30396e-13)" writing-mode="lr" x="251.15" xml:space="preserve" y="599.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128411856899" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="117">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="117" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,136,178.5) scale(1,1) translate(0,0)" writing-mode="lr" x="136.15" xml:space="preserve" y="184.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128426799107" ObjectName=""/>
   </metadata>
  </g>
  <g id="124">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="124" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,313,178.5) scale(1,1) translate(0,0)" writing-mode="lr" x="313.15" xml:space="preserve" y="184.85" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128426864643" ObjectName=""/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="32" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,136,228) scale(1,1) translate(0,0)" writing-mode="lr" x="136.15" xml:space="preserve" y="234.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128411791363" ObjectName="F"/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,134.361,521.5) scale(1,1) translate(1.21014e-14,0)" writing-mode="lr" x="134.47" xml:space="preserve" y="526.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128414740483" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="36">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="36" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,134.361,544.5) scale(1,1) translate(1.21014e-14,5.96745e-14)" writing-mode="lr" x="134.47" xml:space="preserve" y="549.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128414806019" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,134.361,567.5) scale(1,1) translate(1.21014e-14,-1.24456e-13)" writing-mode="lr" x="134.47" xml:space="preserve" y="572.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128414871555" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="33" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,134.361,496) scale(1,1) translate(1.21014e-14,-1.0858e-13)" writing-mode="lr" x="134.47" xml:space="preserve" y="500.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128414543875" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="40">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,134.361,594.5) scale(1,1) translate(1.21014e-14,1.30451e-13)" writing-mode="lr" x="134.47" xml:space="preserve" y="599.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128414937091" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="118">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="118" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,682.443,967) scale(1,1) translate(0,0)" writing-mode="lr" x="639.8" xml:space="preserve" y="971.42" zvalue="1">P:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128426209283" ObjectName="P"/>
   </metadata>
  </g>
  <g id="126">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="126" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,682.443,990) scale(1,1) translate(0,0)" writing-mode="lr" x="639.8" xml:space="preserve" y="994.42" zvalue="1">Q:sdd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128426274819" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="127">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="127" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,682.443,1013) scale(1,1) translate(0,0)" writing-mode="lr" x="639.8" xml:space="preserve" y="1017.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128426340355" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="128">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="128" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,963.583,967) scale(1,1) translate(0,0)" writing-mode="lr" x="920.9400000000001" xml:space="preserve" y="971.42" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128423653379" ObjectName="P"/>
   </metadata>
  </g>
  <g id="129">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="129" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,963.583,990) scale(1,1) translate(0,0)" writing-mode="lr" x="920.9400000000001" xml:space="preserve" y="994.42" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128423718915" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="131">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="131" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,963.583,1013) scale(1,1) translate(0,0)" writing-mode="lr" x="920.9400000000001" xml:space="preserve" y="1017.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128423784451" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="132">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="132" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1090.63,967) scale(1,1) translate(0,0)" writing-mode="lr" x="1047.99" xml:space="preserve" y="971.42" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128422735875" ObjectName="P"/>
   </metadata>
  </g>
  <g id="148">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="148" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1090.63,990) scale(1,1) translate(0,0)" writing-mode="lr" x="1047.99" xml:space="preserve" y="994.42" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128422801411" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="152">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="152" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1090.63,1013) scale(1,1) translate(0,0)" writing-mode="lr" x="1047.99" xml:space="preserve" y="1017.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128422866947" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="153">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="153" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1385.62,967) scale(1,1) translate(0,0)" writing-mode="lr" x="1342.97" xml:space="preserve" y="971.42" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128419852291" ObjectName="P"/>
   </metadata>
  </g>
  <g id="154">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="154" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1385.62,990) scale(1,1) translate(0,0)" writing-mode="lr" x="1342.97" xml:space="preserve" y="994.42" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128419917827" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="155">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="155" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1385.62,1013) scale(1,1) translate(0,0)" writing-mode="lr" x="1342.97" xml:space="preserve" y="1017.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128419983363" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="156">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="156" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1540.9,967) scale(1,1) translate(0,0)" writing-mode="lr" x="1498.25" xml:space="preserve" y="971.42" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128416968707" ObjectName="P"/>
   </metadata>
  </g>
  <g id="158">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="158" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1540.9,990) scale(1,1) translate(0,0)" writing-mode="lr" x="1498.25" xml:space="preserve" y="994.42" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128417034243" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="161">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="161" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1540.9,1013) scale(1,1) translate(0,0)" writing-mode="lr" x="1498.25" xml:space="preserve" y="1017.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128417099779" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="164">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="164" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1696.23,967) scale(1,1) translate(0,0)" writing-mode="lr" x="1653.58" xml:space="preserve" y="971.42" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128418410499" ObjectName="P"/>
   </metadata>
  </g>
  <g id="166">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="166" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1696.23,990) scale(1,1) translate(0,0)" writing-mode="lr" x="1653.58" xml:space="preserve" y="994.42" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128418476035" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="168">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="168" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1696.23,1013) scale(1,1) translate(0,0)" writing-mode="lr" x="1653.58" xml:space="preserve" y="1017.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128418541571" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,583.361,456) scale(1,1) translate(6.19504e-14,9.9698e-14)" writing-mode="lr" x="583.47" xml:space="preserve" y="460.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128411660291" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="262">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="262" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,723.361,157) scale(1,1) translate(-3.09974e-13,1.16573e-13)" writing-mode="lr" x="723.47" xml:space="preserve" y="161.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128414543875" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,342.625,320.5) scale(0.708333,0.665547) translate(136.706,156.042)" width="30" x="332" xlink:href="#State:红绿圆(方形)_0" y="310.52" zvalue="752"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374898626563" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,342.625,320.5) scale(0.708333,0.665547) translate(136.706,156.042)" width="30" x="332" y="310.52"/></g>
  <g id="1035">
   <use height="30" transform="rotate(0,247,320.5) scale(0.708333,0.665547) translate(97.3309,156.042)" width="30" x="236.38" xlink:href="#State:红绿圆(方形)_0" y="310.52" zvalue="753"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562961576689666" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,247,320.5) scale(0.708333,0.665547) translate(97.3309,156.042)" width="30" x="236.38" y="310.52"/></g>
  <g id="264">
   <use height="30" transform="rotate(0,305.812,112.464) scale(1.27778,1.03333) translate(-53.981,-3.12787)" width="90" x="248.31" xlink:href="#State:全站检修_0" y="96.95999999999999" zvalue="1104"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549597437954" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,305.812,112.464) scale(1.27778,1.03333) translate(-53.981,-3.12787)" width="90" x="248.31" y="96.95999999999999"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="215">
   <use class="kv35" height="30" transform="rotate(0,870.304,121.625) scale(4.08692,1.175) translate(-646.552,-15.4894)" width="7" x="856" xlink:href="#ACLineSegment:线路_0" y="104" zvalue="847"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249386385412" ObjectName="35kV槟芒线"/>
   <cge:TPSR_Ref TObjectID="8444249386385412_5066549597437954"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,870.304,121.625) scale(4.08692,1.175) translate(-646.552,-15.4894)" width="7" x="856" y="104"/></g>
 </g>
 <g id="DollyBreakerClass">
  <g id="41">
   <use class="kv10" height="22" transform="rotate(0,551.829,695.119) scale(0.56949,0.826446) translate(412.424,144.066)" width="22" x="545.5644899598362" xlink:href="#DollyBreaker:手车_0" y="686.0285204991087" zvalue="963"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450398060549" ObjectName="10kV1号电容器041断路器手车工作位"/>
   <cge:TPSR_Ref TObjectID="6192450398060549"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,551.829,695.119) scale(0.56949,0.826446) translate(412.424,144.066)" width="22" x="545.5644899598362" y="686.0285204991087"/></g>
  <g id="42">
   <use class="kv10" height="22" transform="rotate(0,552.257,740.734) scale(0.56949,-0.826446) translate(412.747,-1638.93)" width="22" x="545.9922974464674" xlink:href="#DollyBreaker:手车_0" y="731.6434987386067" zvalue="964"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450397995013" ObjectName="10kV1号电容器041断路器手车下"/>
   <cge:TPSR_Ref TObjectID="6192450397995013"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,552.257,740.734) scale(0.56949,-0.826446) translate(412.747,-1638.93)" width="22" x="545.9922974464674" y="731.6434987386067"/></g>
  <g id="48">
   <use class="kv10" height="22" transform="rotate(0,872.063,602.071) scale(0.56949,0.826446) translate(654.508,124.526)" width="22" x="865.7989244017909" xlink:href="#DollyBreaker:手车_0" y="592.9803921568628" zvalue="971"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450398126085" ObjectName="#1主变10kV侧001断路器手车工作位"/>
   <cge:TPSR_Ref TObjectID="6192450398126085"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,872.063,602.071) scale(0.56949,0.826446) translate(654.508,124.526)" width="22" x="865.7989244017909" y="592.9803921568628"/></g>
  <g id="49">
   <use class="kv10" height="22" transform="rotate(0,871.063,647.686) scale(0.56949,-0.826446) translate(653.752,-1433.3)" width="22" x="864.7989216705059" xlink:href="#DollyBreaker:手车_0" y="638.595365418895" zvalue="972"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450398191621" ObjectName="#1主变10kV侧001断路器手车下"/>
   <cge:TPSR_Ref TObjectID="6192450398191621"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,871.063,647.686) scale(0.56949,-0.826446) translate(653.752,-1433.3)" width="22" x="864.7989216705059" y="638.595365418895"/></g>
  <g id="55">
   <use class="kv10" height="22" transform="rotate(0,682.147,695.091) scale(0.56949,0.826446) translate(510.939,144.06)" width="22" x="675.8824495623977" xlink:href="#DollyBreaker:手车_0" y="686" zvalue="978"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450398257157" ObjectName="10kV1号接地变042断路器手车工作位"/>
   <cge:TPSR_Ref TObjectID="6192450398257157"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,682.147,695.091) scale(0.56949,0.826446) translate(510.939,144.06)" width="22" x="675.8824495623977" y="686"/></g>
  <g id="56">
   <use class="kv10" height="22" transform="rotate(0,682.022,741.401) scale(0.56949,-0.826446) translate(510.844,-1640.41)" width="22" x="675.7571618214876" xlink:href="#DollyBreaker:手车_0" y="732.3101654097359" zvalue="979"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450398322693" ObjectName="10kV1号接地变042断路器手车下"/>
   <cge:TPSR_Ref TObjectID="6192450398322693"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,682.022,741.401) scale(0.56949,-0.826446) translate(510.844,-1640.41)" width="22" x="675.7571618214876" y="732.3101654097359"/></g>
  <g id="62">
   <use class="kv10" height="22" transform="rotate(0,960.647,695.091) scale(0.56949,0.826446) translate(721.473,144.06)" width="22" x="954.3824495623977" xlink:href="#DollyBreaker:手车_0" y="686" zvalue="985"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450398388229" ObjectName="10kV相帕线043断路器手车工作位"/>
   <cge:TPSR_Ref TObjectID="6192450398388229"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,960.647,695.091) scale(0.56949,0.826446) translate(721.473,144.06)" width="22" x="954.3824495623977" y="686"/></g>
  <g id="63">
   <use class="kv10" height="22" transform="rotate(0,960.492,740.706) scale(0.56949,-0.826446) translate(721.356,-1638.87)" width="22" x="954.2276546953577" xlink:href="#DollyBreaker:手车_0" y="731.614973262032" zvalue="986"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450398453765" ObjectName="10kV相帕线043断路器手车下"/>
   <cge:TPSR_Ref TObjectID="6192450398453765"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,960.492,740.706) scale(0.56949,-0.826446) translate(721.356,-1638.87)" width="22" x="954.2276546953577" y="731.614973262032"/></g>
  <g id="70">
   <use class="kv10" height="22" transform="rotate(0,1086.39,695.679) scale(0.56949,0.826446) translate(816.528,144.184)" width="22" x="1080.124248504196" xlink:href="#DollyBreaker:手车_0" y="686.5882352941176" zvalue="992"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450398519301" ObjectName="10kV芒章线044断路器手车工作位"/>
   <cge:TPSR_Ref TObjectID="6192450398519301"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1086.39,695.679) scale(0.56949,0.826446) translate(816.528,144.184)" width="22" x="1080.124248504196" y="686.5882352941176"/></g>
  <g id="71">
   <use class="kv10" height="22" transform="rotate(0,1087.4,741.294) scale(0.56949,-0.826446) translate(817.292,-1640.17)" width="22" x="1081.133689839572" xlink:href="#DollyBreaker:手车_0" y="732.2032085561498" zvalue="993"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450398584837" ObjectName="10kV芒章线044断路器手车下"/>
   <cge:TPSR_Ref TObjectID="6192450398584837"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1087.4,741.294) scale(0.56949,-0.826446) translate(817.292,-1640.17)" width="22" x="1081.133689839572" y="732.2032085561498"/></g>
  <g id="77">
   <use class="kv10" height="22" transform="rotate(0,1229.91,695.679) scale(0.56949,0.826446) translate(925.026,144.184)" width="22" x="1223.64705882353" xlink:href="#DollyBreaker:手车_0" y="686.5882352941176" zvalue="999"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450398650373" ObjectName="10kV备用Ⅰ回线045断路器手车工作位"/>
   <cge:TPSR_Ref TObjectID="6192450398650373"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1229.91,695.679) scale(0.56949,0.826446) translate(925.026,144.184)" width="22" x="1223.64705882353" y="686.5882352941176"/></g>
  <g id="78">
   <use class="kv10" height="22" transform="rotate(0,1230.34,741.294) scale(0.56949,-0.826446) translate(925.349,-1640.17)" width="22" x="1224.074866310161" xlink:href="#DollyBreaker:手车_0" y="732.2032085561498" zvalue="1000"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450398715909" ObjectName="10kV备用Ⅰ回线045断路器手车下"/>
   <cge:TPSR_Ref TObjectID="6192450398715909"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1230.34,741.294) scale(0.56949,-0.826446) translate(925.349,-1640.17)" width="22" x="1224.074866310161" y="732.2032085561498"/></g>
  <g id="85">
   <use class="kv10" height="22" transform="rotate(0,1384.95,695.679) scale(0.56949,0.826446) translate(1042.23,144.184)" width="22" x="1378.68258183753" xlink:href="#DollyBreaker:手车_0" y="686.5882352941176" zvalue="1006"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450398781445" ObjectName="10kV备用Ⅱ回线046断路器手车工作位"/>
   <cge:TPSR_Ref TObjectID="6192450398781445"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1384.95,695.679) scale(0.56949,0.826446) translate(1042.23,144.184)" width="22" x="1378.68258183753" y="686.5882352941176"/></g>
  <g id="86">
   <use class="kv10" height="22" transform="rotate(0,1385.61,741.294) scale(0.56949,-0.826446) translate(1042.73,-1640.17)" width="22" x="1379.343946287822" xlink:href="#DollyBreaker:手车_0" y="732.2032085561498" zvalue="1007"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450398846981" ObjectName="10kV备用Ⅱ回线046断路器手车下"/>
   <cge:TPSR_Ref TObjectID="6192450398846981"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1385.61,741.294) scale(0.56949,-0.826446) translate(1042.73,-1640.17)" width="22" x="1379.343946287822" y="732.2032085561498"/></g>
  <g id="97">
   <use class="kv10" height="22" transform="rotate(0,1540.23,695.091) scale(0.56949,0.826446) translate(1159.61,144.06)" width="22" x="1533.961748504196" xlink:href="#DollyBreaker:手车_0" y="686" zvalue="1013"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450398912518" ObjectName="10kV备用Ⅲ回线047断路器手车工作位"/>
   <cge:TPSR_Ref TObjectID="6192450398912518"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1540.23,695.091) scale(0.56949,0.826446) translate(1159.61,144.06)" width="22" x="1533.961748504196" y="686"/></g>
  <g id="99">
   <use class="kv10" height="22" transform="rotate(0,1540.89,741.294) scale(0.56949,-0.826446) translate(1160.11,-1640.17)" width="22" x="1534.623112954489" xlink:href="#DollyBreaker:手车_0" y="732.2032085561498" zvalue="1014"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450398978054" ObjectName="10kV备用Ⅲ回线047断路器手车下"/>
   <cge:TPSR_Ref TObjectID="6192450398978054"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1540.89,741.294) scale(0.56949,-0.826446) translate(1160.11,-1640.17)" width="22" x="1534.623112954489" y="732.2032085561498"/></g>
  <g id="109">
   <use class="kv10" height="22" transform="rotate(0,1695.79,696.267) scale(0.56949,0.826446) translate(1277.21,144.307)" width="22" x="1689.529411764706" xlink:href="#DollyBreaker:手车_0" y="687.1764705882352" zvalue="1020"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450399043590" ObjectName="10kV备用Ⅳ回线048断路器手车工作位"/>
   <cge:TPSR_Ref TObjectID="6192450399043590"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1695.79,696.267) scale(0.56949,0.826446) translate(1277.21,144.307)" width="22" x="1689.529411764706" y="687.1764705882352"/></g>
  <g id="111">
   <use class="kv10" height="22" transform="rotate(0,1696.22,741.882) scale(0.56949,-0.826446) translate(1277.54,-1641.47)" width="22" x="1689.957219251337" xlink:href="#DollyBreaker:手车_0" y="732.7914438502675" zvalue="1021"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450399109126" ObjectName="10kV备用Ⅳ回线048断路器手车下"/>
   <cge:TPSR_Ref TObjectID="6192450399109126"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1696.22,741.882) scale(0.56949,-0.826446) translate(1277.54,-1641.47)" width="22" x="1689.957219251337" y="732.7914438502675"/></g>
 </g>
</svg>