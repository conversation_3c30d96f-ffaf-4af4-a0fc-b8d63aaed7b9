<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549581774850" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:四卷带壁雷器母线PT_0" viewBox="0,0,40,35">
   <use terminal-index="0" type="0" x="22.10905664884498" xlink:href="#terminal" y="34.51612485684674"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="28.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <rect fill-opacity="0" height="15.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,30,14.36) scale(1,-1) translate(0,-930.43)" width="7" x="26.5" y="6.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="16.27740325661302" y2="15.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="7.166666666666671" y1="16.27740325661302" y2="12.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="12.27740325661302" y2="13.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="30.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="22" y1="28.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="14" y1="21.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="30" y1="28.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="30" y1="28.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.92943360505483" x2="29.92943360505483" y1="6.610736589946352" y2="3.462520268614096"/>
   <ellipse cx="13.81" cy="17.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.95921744067709" x2="27.68240159599324" y1="3.3946117330996" y2="3.3946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.12588410734375" x2="28.51573492932657" y1="2.144611733099605" y2="2.144611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="30.54255077401042" x2="29.09906826265991" y1="0.8946117330996053" y2="0.8946117330996053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599325" x2="16.18240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="13.78240159599324" y1="15.23154965466559" y2="17.7066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="11.38240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <ellipse cx="20.23" cy="13.99" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.48" cy="10.41" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="17.79906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="20.19906826265991" y1="11.73154965466559" y2="14.2066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="22.59906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="8.148216321332258" y2="10.62329629003646"/>
   <ellipse cx="6.64" cy="14.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:三卷PT带容断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,6.5) scale(1,1) translate(0,0)" width="4" x="13" y="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_0" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.66666666666666" x2="27.83333333333334" y1="6.416666666666666" y2="22.08333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_1" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.5" x2="28.5" y1="3.833333333333332" y2="22"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:特殊刀闸_2" viewBox="0,0,45,25">
   <use terminal-index="0" type="0" x="28.55" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="28.55" xlink:href="#terminal" y="24.45"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6" x2="11.6" y1="4.383333333333335" y2="8.133333333333336"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="5.583333333333332" y1="6.333333333333335" y2="6.333333333333335"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="3.5" y1="3.25" y2="9.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="2.35" y2="10.35"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.699999999999999" x2="1.699999999999999" y1="4.25" y2="8.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.58333333333334" x2="28.58333333333334" y1="22.66666666666667" y2="24.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.59150672768254" x2="28.59150672768254" y1="3.000000000000002" y2="0.6887951642647483"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.96666666666667" x2="29.93333333333333" y1="3.005662181544974" y2="3.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.16666666666667" x2="35.5" y1="3.083333333333332" y2="24.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="35.5" x2="21.16666666666667" y1="3.166666666666664" y2="24.83333333333334"/>
  </symbol>
  <symbol id="Accessory:母线PT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="12.99001161381492" xlink:href="#terminal" y="37.28457520218116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27" x2="27" y1="22.75" y2="26.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27" x2="13" y1="26.75" y2="26.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.93916361389983" x2="12.93916361389983" y1="33.96630546659506" y2="37.26790853551449"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.93916361389983" x2="12.93916361389983" y1="30.79012345679012" y2="22.71704491718448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.91963465760507" x2="12.91963465760507" y1="19.74560215515698" y2="13.06430394728183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.01963465760507" x2="13.01963465760507" y1="22.75831495554599" y2="22.75831495554599"/>
   <ellipse cx="23.37" cy="20.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.556517488649495" x2="16.39655620359946" y1="22.67498162221265" y2="22.67498162221265"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.01963465760507" x2="13.01963465760507" y1="33.90122531314907" y2="33.90122531314907"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982824" x2="16.47988953693279" y1="33.90122531314905" y2="33.90122531314905"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982819" x2="16.47988953693279" y1="19.66306207843401" y2="19.66306207843401"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982824" x2="16.47988953693279" y1="30.80597243603708" y2="30.80597243603708"/>
   <ellipse cx="26.96" cy="14.43" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="10.72318415531615" x2="15" y1="12.99639541176734" y2="12.99639541176734"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.47318415531615" x2="14.08333333333333" y1="11.74639541176734" y2="11.74639541176734"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.97318415531615" x2="13.41666666666667" y1="10.24639541176734" y2="10.24639541176734"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.19789092865613" x2="26.19789092865613" y1="15.85976741889724" y2="12.69310075223057"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.4" x2="23.4" y1="18.33333333333334" y2="20.80841330203754"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.39999999999999" x2="21" y1="20.80841330203756" y2="22.04595328638966"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.73863166939686" x2="26.49789092865613" y1="14.59124890037872" y2="15.89688090349358"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.4" x2="25.79999999999999" y1="20.80841330203756" y2="22.04595328638966"/>
   <ellipse cx="30.62" cy="20.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.64999999999999" x2="28.25" y1="20.80841330203756" y2="22.04595328638966"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.5256687064339" x2="26.2293724101376" y1="13.81347112260094" y2="12.66532297445279"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.65" x2="30.65" y1="18.33333333333334" y2="20.80841330203754"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.65" x2="33.04999999999999" y1="20.80841330203756" y2="22.04595328638966"/>
  </symbol>
  <symbol id="Accessory:带电容电阻接地_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.75"/>
   <rect fill-opacity="0" height="11.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.94,6.29) scale(1,1) translate(0,0)" width="6.08" x="2.9" y="0.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666663" x2="10.5" y1="20.49453511141348" y2="20.49453511141348"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.603429610654176" x2="7.693139016796801" y1="25.38116790988687" y2="25.38116790988687"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.965686274509802" x2="9.330882352941176" y1="22.90451817731685" y2="22.90451817731685"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.916666666666667" x2="5.916666666666667" y1="14.58333333333333" y2="11.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.083333333333333" x2="6.083333333333333" y1="20.58333333333334" y2="16.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.833333333333333" x2="9" y1="16.16120177808014" y2="16.16120177808014"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.833333333333333" x2="9.25" y1="14.66120177808014" y2="14.66120177808014"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV高河三级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="51.75" xlink:href="logo.png" y="45.25"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="171" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,186.375,75.25) scale(1,1) translate(0,0)" writing-mode="lr" x="186.37" xml:space="preserve" y="78.75" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,186.972,73.8292) scale(1,1) translate(7.62045e-15,0)" writing-mode="lr" x="186.97" xml:space="preserve" y="82.83" zvalue="3">110kV高河三级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="270" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="458"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="458">信号一览</text>
  <line fill="none" id="168" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="385.75" x2="385.75" y1="13.25" y2="1043.25" zvalue="4"/>
  <line fill="none" id="163" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75000000000023" x2="378.7499999999998" y1="149.1204926140824" y2="149.1204926140824" zvalue="6"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="11.75" x2="101.75" y1="934.2500000000002" y2="934.2500000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="11.75" x2="101.75" y1="973.4133000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="11.75" x2="11.75" y1="934.2500000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="101.75" x2="101.75" y1="934.2500000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="101.75" x2="371.75" y1="934.2500000000002" y2="934.2500000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="101.75" x2="371.75" y1="973.4133000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="101.75" x2="101.75" y1="934.2500000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="371.75" x2="371.75" y1="934.2500000000002" y2="973.4133000000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="11.75" x2="101.75" y1="973.4132700000002" y2="973.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="11.75" x2="101.75" y1="1001.33167" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="11.75" x2="11.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="101.75" x2="101.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="101.75" x2="191.75" y1="973.4132700000002" y2="973.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="101.75" x2="191.75" y1="1001.33167" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="101.75" x2="101.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="191.75" x2="191.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="191.7500000000001" x2="281.7500000000001" y1="973.4132700000002" y2="973.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="191.7500000000001" x2="281.7500000000001" y1="1001.33167" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="191.7500000000001" x2="191.7500000000001" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="281.7500000000001" x2="281.7500000000001" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="281.75" x2="371.75" y1="973.4132700000002" y2="973.4132700000002"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="281.75" x2="371.75" y1="1001.33167" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="281.75" x2="281.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="371.75" x2="371.75" y1="973.4132700000002" y2="1001.33167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="11.75" x2="101.75" y1="1001.3316" y2="1001.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="11.75" x2="101.75" y1="1029.25" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="11.75" x2="11.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="101.75" x2="101.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="101.75" x2="191.75" y1="1001.3316" y2="1001.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="101.75" x2="191.75" y1="1029.25" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="101.75" x2="101.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="191.75" x2="191.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="191.7500000000001" x2="281.7500000000001" y1="1001.3316" y2="1001.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="191.7500000000001" x2="281.7500000000001" y1="1029.25" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="191.7500000000001" x2="191.7500000000001" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="281.7500000000001" x2="281.7500000000001" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="281.75" x2="371.75" y1="1001.3316" y2="1001.3316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="281.75" x2="371.75" y1="1029.25" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="281.75" x2="281.75" y1="1001.3316" y2="1029.25"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="371.75" x2="371.75" y1="1001.3316" y2="1029.25"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.75,954.25) scale(1,1) translate(0,0)" writing-mode="lr" x="56.75" xml:space="preserve" y="960.25" zvalue="11">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.75,988.25) scale(1,1) translate(0,0)" writing-mode="lr" x="53.75" xml:space="preserve" y="994.25" zvalue="12">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.75,988.25) scale(1,1) translate(0,0)" writing-mode="lr" x="235.75" xml:space="preserve" y="994.25" zvalue="13">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.75,1016.25) scale(1,1) translate(0,0)" writing-mode="lr" x="52.75" xml:space="preserve" y="1022.25" zvalue="14">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.75,1016.25) scale(1,1) translate(0,0)" writing-mode="lr" x="234.75" xml:space="preserve" y="1022.25" zvalue="15">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236.804,956.25) scale(1,1) translate(0,0)" writing-mode="lr" x="236.8" xml:space="preserve" y="962.25" zvalue="25">GaoHe3-01-2012</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,146.804,988.25) scale(1,1) translate(0,0)" writing-mode="lr" x="146.8" xml:space="preserve" y="994.25" zvalue="26">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,326.804,988.25) scale(1,1) translate(0,0)" writing-mode="lr" x="326.8" xml:space="preserve" y="994.25" zvalue="27">20210213</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,468.533,707.449) scale(1,1) translate(0,0)" writing-mode="lr" x="468.53" xml:space="preserve" y="711.95" zvalue="33">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,522.379,570.17) scale(1,1) translate(0,0)" writing-mode="lr" x="522.38" xml:space="preserve" y="574.67" zvalue="79">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,525.13,318.681) scale(1,1) translate(0,0)" writing-mode="lr" x="525.13" xml:space="preserve" y="323.18" zvalue="89">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,927.556,400) scale(1,1) translate(0,3.47722e-13)" writing-mode="lr" x="927.5599999999999" xml:space="preserve" y="404.5" zvalue="91">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,954.056,376.071) scale(1,1) translate(0,0)" writing-mode="lr" x="954.0599999999999" xml:space="preserve" y="380.57" zvalue="93">10</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,906.134,514.34) scale(1,1) translate(0,0)" writing-mode="lr" x="906.13" xml:space="preserve" y="518.84" zvalue="95">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,960.111,439.25) scale(1,1) translate(0,0)" writing-mode="lr" x="960.11" xml:space="preserve" y="443.75" zvalue="97">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1332.95,948.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1332.95" xml:space="preserve" y="952.61" zvalue="154">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,616.032,286.151) scale(1,1) translate(0,0)" writing-mode="lr" x="616.03" xml:space="preserve" y="290.65" zvalue="162">1711</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,519.476,277.429) scale(1,1) translate(0,4.47856e-13)" writing-mode="lr" x="519.48" xml:space="preserve" y="281.93" zvalue="166">17117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,610.337,228.54) scale(1,1) translate(0,0)" writing-mode="lr" x="610.34" xml:space="preserve" y="233.04" zvalue="169">171</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,523.476,233.962) scale(1,1) translate(0,0)" writing-mode="lr" x="523.48" xml:space="preserve" y="238.46" zvalue="171">17160</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,564.087,170.901) scale(1,1) translate(0,0)" writing-mode="lr" x="564.09" xml:space="preserve" y="175.4" zvalue="176">1716</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,524.976,155.679) scale(1,1) translate(0,0)" writing-mode="lr" x="524.98" xml:space="preserve" y="160.18" zvalue="178">17167</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,590.63,86.5714) scale(1,1) translate(0,0)" writing-mode="lr" x="590.63" xml:space="preserve" y="91.06999999999999" zvalue="183">110kV高河三级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,616.032,474.151) scale(1,1) translate(0,0)" writing-mode="lr" x="616.03" xml:space="preserve" y="478.65" zvalue="212">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,519.476,533.429) scale(1,1) translate(0,0)" writing-mode="lr" x="519.48" xml:space="preserve" y="537.9299999999999" zvalue="216">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="206" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,562.337,416.54) scale(1,1) translate(0,0)" writing-mode="lr" x="562.34" xml:space="preserve" y="421.04" zvalue="218">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="205" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,523.476,467.179) scale(1,1) translate(0,-2.03694e-13)" writing-mode="lr" x="523.48" xml:space="preserve" y="471.68" zvalue="220">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,617.252,358.901) scale(1,1) translate(0,0)" writing-mode="lr" x="617.25" xml:space="preserve" y="363.4" zvalue="224">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,524.976,403.679) scale(1,1) translate(0,3.50989e-13)" writing-mode="lr" x="524.98" xml:space="preserve" y="408.18" zvalue="226">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="233" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,551.17,649.651) scale(1,1) translate(-1.0747e-12,5.69456e-13)" writing-mode="lr" x="551.17" xml:space="preserve" y="654.15" zvalue="237">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="231" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,718.667,543.684) scale(1,1) translate(-6.24537e-13,0)" writing-mode="lr" x="718.67" xml:space="preserve" y="548.1799999999999" zvalue="239">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,526.963,691.444) scale(1,1) translate(8.5178e-13,0)" writing-mode="lr" x="526.96" xml:space="preserve" y="695.9400000000001" zvalue="242">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,925.613,286.151) scale(1,1) translate(0,0)" writing-mode="lr" x="925.61" xml:space="preserve" y="290.65" zvalue="248">1721</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,827.476,277.429) scale(1,1) translate(0,4.47856e-13)" writing-mode="lr" x="827.48" xml:space="preserve" y="281.93" zvalue="252">17217</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,918.337,228.54) scale(1,1) translate(0,0)" writing-mode="lr" x="918.34" xml:space="preserve" y="233.04" zvalue="254">172</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,831.476,233.962) scale(1,1) translate(0,0)" writing-mode="lr" x="831.48" xml:space="preserve" y="238.46" zvalue="256">17260</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,872.087,170.901) scale(1,1) translate(0,0)" writing-mode="lr" x="872.09" xml:space="preserve" y="175.4" zvalue="260">1726</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,832.976,155.679) scale(1,1) translate(2.70775e-13,0)" writing-mode="lr" x="832.98" xml:space="preserve" y="160.18" zvalue="262">17267</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,898.63,86.5714) scale(1,1) translate(0,0)" writing-mode="lr" x="898.63" xml:space="preserve" y="91.06999999999999" zvalue="264">110kV高河三四级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,920.111,839.25) scale(1,1) translate(0,0)" writing-mode="lr" x="920.11" xml:space="preserve" y="843.75" zvalue="280">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,863.333,909) scale(1,1) translate(0,0)" writing-mode="lr" x="863.33" xml:space="preserve" y="913.5" zvalue="286">10kVⅠ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,826.15,783.667) scale(1,1) translate(0,1.29091e-12)" writing-mode="lr" x="826.15" xml:space="preserve" y="788.17" zvalue="288">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" x="636.6484375" xml:space="preserve" y="939.5963107854257" zvalue="292">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="636.6484375" xml:space="preserve" y="955.5963107854257" zvalue="292">5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,613.376,821.753) scale(1,1) translate(0,0)" writing-mode="lr" x="613.38" xml:space="preserve" y="826.25" zvalue="295">071</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,591.375,769.889) scale(1,1) translate(0,0)" writing-mode="lr" x="591.37" xml:space="preserve" y="774.39" zvalue="299">0711</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,457.506,892.111) scale(1,1) translate(-7.8252e-13,0)" writing-mode="lr" x="457.51" xml:space="preserve" y="896.61" zvalue="305">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,557.349,935.643) scale(1,1) translate(0,0)" writing-mode="lr" x="557.35" xml:space="preserve" y="940.14" zvalue="308">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" x="1152.8671875" xml:space="preserve" y="932.9400607854257" zvalue="317">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1152.8671875" xml:space="preserve" y="948.9400607854257" zvalue="317">9MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1125.15,822.864) scale(1,1) translate(0,0)" writing-mode="lr" x="1125.15" xml:space="preserve" y="827.36" zvalue="320">072</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1103.15,771) scale(1,1) translate(0,0)" writing-mode="lr" x="1103.15" xml:space="preserve" y="775.5" zvalue="324">0721</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,965.596,893.222) scale(1,1) translate(0,0)" writing-mode="lr" x="965.6" xml:space="preserve" y="897.72" zvalue="330">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1069.13,936.754) scale(1,1) translate(0,0)" writing-mode="lr" x="1069.13" xml:space="preserve" y="941.25" zvalue="333">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1343.89,822.819) scale(1,1) translate(0,0)" writing-mode="lr" x="1343.89" xml:space="preserve" y="827.3200000000001" zvalue="342">073</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1262.6,771) scale(1,1) translate(-2.21255e-12,0)" writing-mode="lr" x="1262.6" xml:space="preserve" y="775.5" zvalue="343">0731</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1389.73,901.206) scale(1,1) translate(0,0)" writing-mode="lr" x="1389.73" xml:space="preserve" y="905.71" zvalue="348">077</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="177" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1458.14,908.373) scale(1,1) translate(0,0)" writing-mode="lr" x="1458.14" xml:space="preserve" y="912.87" zvalue="350">0776</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1529.69,843.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1529.69" xml:space="preserve" y="848.17" zvalue="354">10kV大坝线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1283.17,653.651) scale(1,1) translate(0,5.73009e-13)" writing-mode="lr" x="1283.17" xml:space="preserve" y="658.15" zvalue="358">074</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="182" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1271.23,691.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1271.23" xml:space="preserve" y="695.9400000000001" zvalue="361">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="194" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1210.78,307.983) scale(1,1) translate(0,0)" writing-mode="lr" x="1210.78" xml:space="preserve" y="312.48" zvalue="367">10kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="243" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1287.35,373.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1287.35" xml:space="preserve" y="378.17" zvalue="383">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="242" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1294.01,422.984) scale(1,1) translate(0,0)" writing-mode="lr" x="1294.01" xml:space="preserve" y="427.48" zvalue="385">075</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="249" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1643.44,445.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1643.44" xml:space="preserve" y="449.97" zvalue="397">27</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="248" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1586.67,511.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1586.67" xml:space="preserve" y="515.72" zvalue="400">10kVⅡ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1549.48,385.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1549.48" xml:space="preserve" y="390.39" zvalue="402">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1287.24,221.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1287.24" xml:space="preserve" y="226.17" zvalue="407">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1293.88,270.984) scale(1,1) translate(0,0)" writing-mode="lr" x="1293.88" xml:space="preserve" y="275.48" zvalue="409">076</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1377.78,273.761) scale(1,1) translate(0,0)" writing-mode="lr" x="1377.78" xml:space="preserve" y="278.26" zvalue="414">(死开关)</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="269" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1322.63,85.3889) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.63" xml:space="preserve" y="89.89" zvalue="418">10kV高河二三级线</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="283" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="445"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="281" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="447">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="280" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="448">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="279" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="449">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="278" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="450">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="277" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="451">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="276" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="452">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="275" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.9375,358) scale(1,1) translate(0,0)" writing-mode="lr" x="60.94" xml:space="preserve" y="362.5" zvalue="453">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="262" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="459">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="261" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="460">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="230" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="463">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="226" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="464">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="220" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="465">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="466">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="210" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="467">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="469">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="195" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,242.188,333) scale(1,1) translate(0,0)" writing-mode="lr" x="242.19" xml:space="preserve" y="337.5" zvalue="472">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,148,1015.5) scale(1,1) translate(0,0)" writing-mode="lr" x="148" xml:space="preserve" y="1021.5" zvalue="483">李宏梅</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,328,1015.5) scale(1,1) translate(0,0)" writing-mode="lr" x="328" xml:space="preserve" y="1021.5" zvalue="484">20211213</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="458"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="253">
   <path class="kv10" d="M 476.25 731.43 L 1609.11 731.43" stroke-width="6" zvalue="32"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674233614341" ObjectName="10kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674233614341"/></metadata>
  <path d="M 476.25 731.43 L 1609.11 731.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv110" d="M 518.54 333.65 L 994.44 333.65" stroke-width="6" zvalue="88"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674233548805" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674233548805"/></metadata>
  <path d="M 518.54 333.65 L 994.44 333.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv10" d="M 1226.67 333.65 L 1676.67 333.65" stroke-width="6" zvalue="366"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674233679876" ObjectName="10kVⅡ母"/>
   <cge:TPSR_Ref TObjectID="9288674233679876"/></metadata>
  <path d="M 1226.67 333.65 L 1676.67 333.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="104">
   <g id="1040">
    <use class="kv110" height="50" transform="rotate(0,589.41,572.17) scale(1.34375,1.34375) translate(-145.623,-137.775)" width="30" x="569.25" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="538.58" zvalue="78"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874420453380" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1041">
    <use class="kv10" height="50" transform="rotate(0,589.41,572.17) scale(1.34375,1.34375) translate(-145.623,-137.775)" width="30" x="569.25" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="538.58" zvalue="78"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874420518916" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399442432004" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399442432004"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,589.41,572.17) scale(1.34375,1.34375) translate(-145.623,-137.775)" width="30" x="569.25" y="538.58"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="358">
   <use class="kv110" height="30" transform="rotate(0,899,393) scale(-1.11111,-0.814815) translate(-1707.27,-878.096)" width="15" x="890.6666666666667" xlink:href="#Disconnector:刀闸_0" y="380.7777913411458" zvalue="90"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449543667717" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449543667717"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,899,393) scale(-1.11111,-0.814815) translate(-1707.27,-878.096)" width="15" x="890.6666666666667" y="380.7777913411458"/></g>
  <g id="132">
   <use class="kv110" height="30" transform="rotate(0,589.448,287.151) scale(-1.11111,-0.814815) translate(-1119.12,-642.341)" width="15" x="581.1150793650793" xlink:href="#Disconnector:刀闸_0" y="274.9285711560931" zvalue="161"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449543208965" ObjectName="110kV高河三级线1711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449543208965"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,589.448,287.151) scale(-1.11111,-0.814815) translate(-1119.12,-642.341)" width="15" x="581.1150793650793" y="274.9285711560931"/></g>
  <g id="105">
   <use class="kv110" height="30" transform="rotate(0,590.698,171.901) scale(-1.11111,-0.814815) translate(-1121.49,-385.648)" width="15" x="582.3650793650793" xlink:href="#Disconnector:刀闸_0" y="159.6785711560931" zvalue="174"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449542881286" ObjectName="110kV高河三级线1716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449542881286"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,590.698,171.901) scale(-1.11111,-0.814815) translate(-1121.49,-385.648)" width="15" x="582.3650793650793" y="159.6785711560931"/></g>
  <g id="221">
   <use class="kv110" height="30" transform="rotate(0,589.448,475.151) scale(-1.11111,-0.814815) translate(-1119.12,-1061.07)" width="15" x="581.1150793650793" xlink:href="#Disconnector:刀闸_0" y="462.9285711560931" zvalue="211"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449544192005" ObjectName="#1主变110kV侧1016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449544192005"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,589.448,475.151) scale(-1.11111,-0.814815) translate(-1119.12,-1061.07)" width="15" x="581.1150793650793" y="462.9285711560931"/></g>
  <g id="214">
   <use class="kv110" height="30" transform="rotate(0,589.419,359.901) scale(-1.11111,-0.814815) translate(-1119.06,-804.375)" width="15" x="581.085509779796" xlink:href="#Disconnector:刀闸_0" y="347.6785711560931" zvalue="222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449543864325" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449543864325"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,589.419,359.901) scale(-1.11111,-0.814815) translate(-1119.06,-804.375)" width="15" x="581.085509779796" y="347.6785711560931"/></g>
  <g id="353">
   <use class="kv10" height="25" transform="rotate(0,582.81,692.444) scale(1.11111,1.11111) translate(-55.781,-67.8556)" width="45" x="557.8104455621603" xlink:href="#Disconnector:特殊刀闸_0" y="678.5555555555555" zvalue="241"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449544388614" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449544388614"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,582.81,692.444) scale(1.11111,1.11111) translate(-55.781,-67.8556)" width="45" x="557.8104455621603" y="678.5555555555555"/></g>
  <g id="59">
   <use class="kv110" height="30" transform="rotate(0,899.03,287.151) scale(-1.11111,-0.814815) translate(-1707.32,-642.341)" width="15" x="890.69623625195" xlink:href="#Disconnector:刀闸_0" y="274.9285711560931" zvalue="247"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449545109509" ObjectName="110kV高河三四级线1721隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449545109509"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,899.03,287.151) scale(-1.11111,-0.814815) translate(-1707.32,-642.341)" width="15" x="890.69623625195" y="274.9285711560931"/></g>
  <g id="34">
   <use class="kv110" height="30" transform="rotate(0,898.698,171.901) scale(-1.11111,-0.814815) translate(-1706.69,-385.648)" width="15" x="890.3650793650793" xlink:href="#Disconnector:刀闸_0" y="159.6785711560931" zvalue="258"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449544781829" ObjectName="110kV高河三四级线1726隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449544781829"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,898.698,171.901) scale(-1.11111,-0.814815) translate(-1706.69,-385.648)" width="15" x="890.3650793650793" y="159.6785711560931"/></g>
  <g id="77">
   <use class="kv10" height="25" transform="rotate(0,867.134,788.667) scale(-1.11111,1.11111) translate(-1645.05,-77.4778)" width="45" x="842.133528437017" xlink:href="#Disconnector:特殊刀闸_0" y="774.7777777777777" zvalue="287"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449545437189" ObjectName="10kVⅠ母电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449545437189"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,867.134,788.667) scale(-1.11111,1.11111) translate(-1645.05,-77.4778)" width="45" x="842.133528437017" y="774.7777777777777"/></g>
  <g id="311">
   <use class="kv10" height="25" transform="rotate(0,632.263,770.889) scale(1.11111,1.11111) translate(-60.7263,-75.7)" width="45" x="607.2634325375019" xlink:href="#Disconnector:特殊刀闸_0" y="757" zvalue="297"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449545895941" ObjectName="#1发电机0711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449545895941"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,632.263,770.889) scale(1.11111,1.11111) translate(-60.7263,-75.7)" width="45" x="607.2634325375019" y="757"/></g>
  <g id="320">
   <use class="kv10" height="25" transform="rotate(0,505.186,893.111) scale(1.11111,1.11111) translate(-48.0186,-87.9222)" width="45" x="480.1864784179249" xlink:href="#Disconnector:特殊刀闸_0" y="879.2222222222222" zvalue="303"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449545764870" ObjectName="#1发电机0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449545764870"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,505.186,893.111) scale(1.11111,1.11111) translate(-48.0186,-87.9222)" width="45" x="480.1864784179249" y="879.2222222222222"/></g>
  <g id="125">
   <use class="kv10" height="25" transform="rotate(0,1144.04,772) scale(1.11111,1.11111) translate(-111.904,-75.8111)" width="45" x="1119.04121031528" xlink:href="#Disconnector:特殊刀闸_0" y="758.1111111111111" zvalue="322"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449546485766" ObjectName="#2发电机0721隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449546485766"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1144.04,772) scale(1.11111,1.11111) translate(-111.904,-75.8111)" width="45" x="1119.04121031528" y="758.1111111111111"/></g>
  <g id="108">
   <use class="kv10" height="25" transform="rotate(0,1016.96,894.222) scale(1.11111,1.11111) translate(-99.1964,-88.0333)" width="45" x="991.9642561957027" xlink:href="#Disconnector:特殊刀闸_0" y="880.3333333333333" zvalue="328"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449546354694" ObjectName="#2发电机0921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449546354694"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1016.96,894.222) scale(1.11111,1.11111) translate(-99.1964,-88.0333)" width="45" x="991.9642561957027" y="880.3333333333333"/></g>
  <g id="155">
   <use class="kv10" height="25" transform="rotate(0,1316.04,772) scale(1.11111,1.11111) translate(-129.104,-75.8111)" width="45" x="1291.041210315279" xlink:href="#Disconnector:特殊刀闸_0" y="758.1111111111111" zvalue="342"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449546682374" ObjectName="#1站用变0731隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449546682374"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1316.04,772) scale(1.11111,1.11111) translate(-129.104,-75.8111)" width="45" x="1291.041210315279" y="758.1111111111111"/></g>
  <g id="172">
   <use class="kv10" height="30" transform="rotate(270,1458.14,869.373) scale(-1.11111,0.814815) translate(-2769.63,194.807)" width="15" x="1449.807347363061" xlink:href="#Disconnector:刀闸_0" y="857.1507933783154" zvalue="349"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449546747910" ObjectName="10kV大坝线0776隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449546747910"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1458.14,869.373) scale(-1.11111,0.814815) translate(-2769.63,194.807)" width="15" x="1449.807347363061" y="857.1507933783154"/></g>
  <g id="190">
   <use class="kv10" height="25" transform="rotate(0,1314.81,692.444) scale(1.11111,1.11111) translate(-128.981,-67.8556)" width="45" x="1289.81044556216" xlink:href="#Disconnector:特殊刀闸_0" y="678.5555555555555" zvalue="359"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449546944518" ObjectName="10kVⅠ母侧0741隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449546944518"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1314.81,692.444) scale(1.11111,1.11111) translate(-128.981,-67.8556)" width="45" x="1289.81044556216" y="678.5555555555555"/></g>
  <g id="235">
   <use class="kv10" height="25" transform="rotate(0,1316.02,374.667) scale(1.11111,1.11111) translate(-129.102,-36.0778)" width="45" x="1291.021556673271" xlink:href="#Disconnector:特殊刀闸_0" y="360.7777777777777" zvalue="382"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449547075590" ObjectName="10kVⅡ母侧0751隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449547075590"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1316.02,374.667) scale(1.11111,1.11111) translate(-129.102,-36.0778)" width="45" x="1291.021556673271" y="360.7777777777777"/></g>
  <g id="252">
   <use class="kv10" height="25" transform="rotate(0,1590.47,390.889) scale(-1.11111,1.11111) translate(-3019.39,-37.7)" width="45" x="1565.46686177035" xlink:href="#Disconnector:特殊刀闸_0" y="376.9999999999999" zvalue="401"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449547206662" ObjectName="10kVⅡ母电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449547206662"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1590.47,390.889) scale(-1.11111,1.11111) translate(-3019.39,-37.7)" width="45" x="1565.46686177035" y="376.9999999999999"/></g>
  <g id="265">
   <use class="kv10" height="25" transform="rotate(0,1315.91,222.667) scale(1.11111,1.11111) translate(-129.091,-20.8778)" width="45" x="1290.908230004962" xlink:href="#Disconnector:特殊刀闸_0" y="208.7777777777777" zvalue="406"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449547468806" ObjectName="10kV高河二三级线0766隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449547468806"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1315.91,222.667) scale(1.11111,1.11111) translate(-129.091,-20.8778)" width="45" x="1290.908230004962" y="208.7777777777777"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="351">
   <use class="kv110" height="20" transform="rotate(270,952.611,361.516) scale(-1.11111,1.11111) translate(-1809.41,-35.0405)" width="10" x="947.0555555555554" xlink:href="#GroundDisconnector:地刀_0" y="350.4047619047619" zvalue="92"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449543602181" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449543602181"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,952.611,361.516) scale(-1.11111,1.11111) translate(-1809.41,-35.0405)" width="10" x="947.0555555555554" y="350.4047619047619"/></g>
  <g id="118">
   <use class="kv110" height="20" transform="rotate(270,957.611,424.556) scale(-1.11111,1.11111) translate(-1818.91,-41.3444)" width="10" x="952.0555555555555" xlink:href="#GroundDisconnector:地刀_0" y="413.4444444444444" zvalue="96"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449543405573" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449543405573"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,957.611,424.556) scale(-1.11111,1.11111) translate(-1818.91,-41.3444)" width="10" x="952.0555555555555" y="413.4444444444444"/></g>
  <g id="113">
   <use class="kv110" height="20" transform="rotate(90,519.476,258.734) scale(1.11111,1.11111) translate(-51.3921,-24.7623)" width="10" x="513.9206349206349" xlink:href="#GroundDisconnector:地刀_0" y="247.6230156005374" zvalue="165"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449543143429" ObjectName="110kV高河三级线17117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449543143429"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,519.476,258.734) scale(1.11111,1.11111) translate(-51.3921,-24.7623)" width="10" x="513.9206349206349" y="247.6230156005374"/></g>
  <g id="109">
   <use class="kv110" height="20" transform="rotate(90,520.976,203.859) scale(1.23611,1.11111) translate(-98.3318,-19.2748)" width="10" x="514.7956349206349" xlink:href="#GroundDisconnector:地刀_0" y="192.7480156005374" zvalue="170"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449543012357" ObjectName="110kV高河三级线17160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449543012357"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,520.976,203.859) scale(1.23611,1.11111) translate(-98.3318,-19.2748)" width="10" x="514.7956349206349" y="192.7480156005374"/></g>
  <g id="96">
   <use class="kv110" height="20" transform="rotate(90,524.976,138.859) scale(1.23611,1.11111) translate(-99.0958,-12.7748)" width="10" x="518.7956349206349" xlink:href="#GroundDisconnector:地刀_0" y="127.7480156005374" zvalue="177"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449542815750" ObjectName="110kV高河三级线17167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449542815750"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,524.976,138.859) scale(1.23611,1.11111) translate(-99.0958,-12.7748)" width="10" x="518.7956349206349" y="127.7480156005374"/></g>
  <g id="218">
   <use class="kv110" height="20" transform="rotate(90,519.476,514.734) scale(1.11111,1.11111) translate(-51.3921,-50.3623)" width="10" x="513.9206349206349" xlink:href="#GroundDisconnector:地刀_0" y="503.6230156005374" zvalue="215"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449544126469" ObjectName="#1主变110kV侧10167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449544126469"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,519.476,514.734) scale(1.11111,1.11111) translate(-51.3921,-50.3623)" width="10" x="513.9206349206349" y="503.6230156005374"/></g>
  <g id="216">
   <use class="kv110" height="20" transform="rotate(90,520.976,447.859) scale(1.23611,1.11111) translate(-98.3318,-43.6748)" width="10" x="514.7956349206349" xlink:href="#GroundDisconnector:地刀_0" y="436.7480156005374" zvalue="219"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449543995397" ObjectName="#1主变110kV侧10160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449543995397"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,520.976,447.859) scale(1.23611,1.11111) translate(-98.3318,-43.6748)" width="10" x="514.7956349206349" y="436.7480156005374"/></g>
  <g id="212">
   <use class="kv110" height="20" transform="rotate(90,524.976,386.859) scale(1.23611,1.11111) translate(-99.0958,-37.5748)" width="10" x="518.7956349206349" xlink:href="#GroundDisconnector:地刀_0" y="375.7480156005374" zvalue="225"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449543798789" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449543798789"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,524.976,386.859) scale(1.23611,1.11111) translate(-99.0958,-37.5748)" width="10" x="518.7956349206349" y="375.7480156005374"/></g>
  <g id="229">
   <use class="kv110" height="40" transform="rotate(0,680,544.684) scale(1.11111,1.11111) translate(-65.7778,-52.2462)" width="40" x="657.7777777777778" xlink:href="#GroundDisconnector:中性点地刀12_0" y="522.4615997942387" zvalue="238"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449544323078" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449544323078"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,680,544.684) scale(1.11111,1.11111) translate(-65.7778,-52.2462)" width="40" x="657.7777777777778" y="522.4615997942387"/></g>
  <g id="47">
   <use class="kv110" height="20" transform="rotate(90,827.476,258.734) scale(1.11111,1.11111) translate(-82.1921,-24.7623)" width="10" x="821.9206349206349" xlink:href="#GroundDisconnector:地刀_0" y="247.6230156005374" zvalue="251"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449545043973" ObjectName="110kV高河三四级线17217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449545043973"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,827.476,258.734) scale(1.11111,1.11111) translate(-82.1921,-24.7623)" width="10" x="821.9206349206349" y="247.6230156005374"/></g>
  <g id="43">
   <use class="kv110" height="20" transform="rotate(90,828.976,203.859) scale(1.23611,1.11111) translate(-157.163,-19.2748)" width="10" x="822.7956349206349" xlink:href="#GroundDisconnector:地刀_0" y="192.7480156005374" zvalue="255"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449544912902" ObjectName="110kV高河三四级线17260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449544912902"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,828.976,203.859) scale(1.23611,1.11111) translate(-157.163,-19.2748)" width="10" x="822.7956349206349" y="192.7480156005374"/></g>
  <g id="32">
   <use class="kv110" height="20" transform="rotate(90,832.976,138.859) scale(1.23611,1.11111) translate(-157.927,-12.7748)" width="10" x="826.7956349206349" xlink:href="#GroundDisconnector:地刀_0" y="127.7480156005374" zvalue="261"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449544716293" ObjectName="110kV高河三四级线17267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449544716293"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,832.976,138.859) scale(1.23611,1.11111) translate(-157.927,-12.7748)" width="10" x="826.7956349206349" y="127.7480156005374"/></g>
  <g id="68">
   <use class="kv10" height="20" transform="rotate(270,917.611,824.556) scale(-1.11111,1.11111) translate(-1742.91,-81.3444)" width="10" x="912.0555555555555" xlink:href="#GroundDisconnector:地刀_0" y="813.4444444444443" zvalue="279"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449545306118" ObjectName="10kVⅠ母电压互感器09017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449545306118"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,917.611,824.556) scale(-1.11111,1.11111) translate(-1742.91,-81.3444)" width="10" x="912.0555555555555" y="813.4444444444443"/></g>
  <g id="325">
   <use class="kv10" height="20" transform="rotate(270,557.349,919.421) scale(1.11111,1.11111) translate(-55.1794,-90.831)" width="10" x="551.7936575753347" xlink:href="#GroundDisconnector:地刀_0" y="908.3095135310341" zvalue="307"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449545699334" ObjectName="#1发电机09117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449545699334"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,557.349,919.421) scale(1.11111,1.11111) translate(-55.1794,-90.831)" width="10" x="551.7936575753347" y="908.3095135310341"/></g>
  <g id="100">
   <use class="kv10" height="20" transform="rotate(270,1069.13,920.532) scale(1.11111,1.11111) translate(-106.357,-90.9421)" width="10" x="1063.571435353113" xlink:href="#GroundDisconnector:地刀_0" y="909.4206246421452" zvalue="332"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449546289158" ObjectName="#2发电机09217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449546289158"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1069.13,920.532) scale(1.11111,1.11111) translate(-106.357,-90.9421)" width="10" x="1063.571435353113" y="909.4206246421452"/></g>
  <g id="256">
   <use class="kv10" height="20" transform="rotate(270,1640.94,426.778) scale(-1.11111,1.11111) translate(-3117.24,-41.5667)" width="10" x="1635.388888888889" xlink:href="#GroundDisconnector:地刀_0" y="415.6666666666666" zvalue="396"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449547403270" ObjectName="10kVⅡ母电压互感器09027接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449547403270"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1640.94,426.778) scale(-1.11111,1.11111) translate(-3117.24,-41.5667)" width="10" x="1635.388888888889" y="415.6666666666666"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="116">
   <use class="kv110" height="35" transform="rotate(0,896.134,466.594) scale(1.3125,-1.3125) translate(-207.115,-816.625)" width="40" x="869.8843330918792" xlink:href="#Accessory:四卷带壁雷器母线PT_0" y="443.625" zvalue="94"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449543471109" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,896.134,466.594) scale(1.3125,-1.3125) translate(-207.115,-816.625)" width="40" x="869.8843330918792" y="443.625"/></g>
  <g id="12">
   <use class="kv110" height="40" transform="rotate(90,640,148) scale(1.11111,1.11111) translate(-61.7778,-12.5778)" width="40" x="617.7777777777779" xlink:href="#Accessory:母线PT_0" y="125.7777777777778" zvalue="244"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449544454150" ObjectName="高河三级线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,640,148) scale(1.11111,1.11111) translate(-61.7778,-12.5778)" width="40" x="617.7777777777779" y="125.7777777777778"/></g>
  <g id="22">
   <use class="kv110" height="40" transform="rotate(90,948,148) scale(1.11111,1.11111) translate(-92.5778,-12.5778)" width="40" x="925.7777777777778" xlink:href="#Accessory:母线PT_0" y="125.7777777777778" zvalue="268"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449544519686" ObjectName="高河三四级线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,948,148) scale(1.11111,1.11111) translate(-92.5778,-12.5778)" width="40" x="925.7777777777778" y="125.7777777777778"/></g>
  <g id="60">
   <use class="kv10" height="26" transform="rotate(90,524.496,625.111) scale(-1.45299,1.45299) translate(-882.754,-188.999)" width="12" x="515.7777777777777" xlink:href="#Accessory:带电容电阻接地_0" y="606.2222222222223" zvalue="270"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449545175045" ObjectName="#1主变10kV侧电阻接地"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,524.496,625.111) scale(-1.45299,1.45299) translate(-882.754,-188.999)" width="12" x="515.7777777777777" y="606.2222222222223"/></g>
  <g id="74">
   <use class="kv10" height="18" transform="rotate(0,862.593,868.667) scale(2.34568,2.34568) translate(-484.763,-486.229)" width="15" x="845" xlink:href="#Accessory:PT8_0" y="847.5555555555554" zvalue="285"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449545371654" ObjectName="10kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,862.593,868.667) scale(2.34568,2.34568) translate(-484.763,-486.229)" width="15" x="845" y="847.5555555555554"/></g>
  <g id="188">
   <use class="kv10" height="40" transform="rotate(0,511.909,993.478) scale(1.15242,-1.15242) translate(-65.4204,-1852.51)" width="30" x="494.622352329025" xlink:href="#Accessory:带熔断器的线路PT1_0" y="970.4294661633703" zvalue="293"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449545961477" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,511.909,993.478) scale(1.15242,-1.15242) translate(-65.4204,-1852.51)" width="30" x="494.622352329025" y="970.4294661633703"/></g>
  <g id="318">
   <use class="kv10" height="26" transform="rotate(270,681.197,853.111) scale(1.49573,1.49573) translate(-222.794,-276.301)" width="12" x="672.2222222222222" xlink:href="#Accessory:带电容电阻接地_0" y="833.6666666666665" zvalue="301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449545830405" ObjectName="#1发电机设备1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,681.197,853.111) scale(1.49573,1.49573) translate(-222.794,-276.301)" width="12" x="672.2222222222222" y="833.6666666666665"/></g>
  <g id="326">
   <use class="kv10" height="40" transform="rotate(0,450.798,993.478) scale(1.15242,-1.15242) translate(-57.3376,-1852.51)" width="30" x="433.5112412179138" xlink:href="#Accessory:带熔断器的线路PT1_0" y="970.4294661633703" zvalue="310"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449545568262" ObjectName="#1发电机PT3"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,450.798,993.478) scale(1.15242,-1.15242) translate(-57.3376,-1852.51)" width="30" x="433.5112412179138" y="970.4294661633703"/></g>
  <g id="328">
   <use class="kv10" height="30" transform="rotate(0,568.889,990.889) scale(1.11111,1.11111) translate(-55.2222,-97.4222)" width="30" x="552.2222222222224" xlink:href="#Accessory:三卷PT带容断器_0" y="974.2222222222222" zvalue="312"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449545502725" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,568.889,990.889) scale(1.11111,1.11111) translate(-55.2222,-97.4222)" width="30" x="552.2222222222224" y="974.2222222222222"/></g>
  <g id="135">
   <use class="kv10" height="40" transform="rotate(0,1023.69,994.589) scale(1.15242,-1.15242) translate(-133.11,-1854.58)" width="30" x="1006.400130106803" xlink:href="#Accessory:带熔断器的线路PT1_0" y="971.5405772744814" zvalue="318"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449546551302" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1023.69,994.589) scale(1.15242,-1.15242) translate(-133.11,-1854.58)" width="30" x="1006.400130106803" y="971.5405772744814"/></g>
  <g id="119">
   <use class="kv10" height="26" transform="rotate(270,1192.97,854.222) scale(1.49573,1.49573) translate(-392.411,-276.669)" width="12" x="1184" xlink:href="#Accessory:带电容电阻接地_0" y="834.7777777777777" zvalue="326"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449546420230" ObjectName="#2发电机设备1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1192.97,854.222) scale(1.49573,1.49573) translate(-392.411,-276.669)" width="12" x="1184" y="834.7777777777777"/></g>
  <g id="92">
   <use class="kv10" height="40" transform="rotate(0,962.575,994.589) scale(1.15242,-1.15242) translate(-125.027,-1854.58)" width="30" x="945.2890189956917" xlink:href="#Accessory:带熔断器的线路PT1_0" y="971.5405772744814" zvalue="335"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449546158085" ObjectName="#2发电机PT3"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,962.575,994.589) scale(1.15242,-1.15242) translate(-125.027,-1854.58)" width="30" x="945.2890189956917" y="971.5405772744814"/></g>
  <g id="90">
   <use class="kv10" height="30" transform="rotate(0,1080.67,992) scale(1.11111,1.11111) translate(-106.4,-97.5333)" width="30" x="1064" xlink:href="#Accessory:三卷PT带容断器_0" y="975.3333333333333" zvalue="337"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449546092549" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1080.67,992) scale(1.11111,1.11111) translate(-106.4,-97.5333)" width="30" x="1064" y="975.3333333333333"/></g>
  <g id="185">
   <use class="kv10" height="26" transform="rotate(270,1374.5,622.889) scale(-1.45299,1.45299) translate(-2317.75,-188.306)" width="12" x="1365.777777777778" xlink:href="#Accessory:带电容电阻接地_0" y="604" zvalue="363"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449546878982" ObjectName="10kVⅠ母侧电阻接地"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1374.5,622.889) scale(-1.45299,1.45299) translate(-2317.75,-188.306)" width="12" x="1365.777777777778" y="604"/></g>
  <g id="196">
   <use class="kv10" height="40" transform="rotate(0,1221.46,670.145) scale(1.15242,-1.15242) translate(-159.268,-1248.61)" width="30" x="1204.177907884581" xlink:href="#Accessory:带熔断器的线路PT1_0" y="647.0961328300369" zvalue="369"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449547010054" ObjectName="10kVⅠ母侧PT1"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1221.46,670.145) scale(1.15242,-1.15242) translate(-159.268,-1248.61)" width="30" x="1204.177907884581" y="647.0961328300369"/></g>
  <g id="245">
   <use class="kv10" height="26" transform="rotate(270,1378.5,458.889) scale(-1.45299,1.45299) translate(-2324.51,-137.176)" width="12" x="1369.777777777778" xlink:href="#Accessory:带电容电阻接地_0" y="440" zvalue="391"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449547141126" ObjectName="10kVⅡ母侧电阻接地"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1378.5,458.889) scale(-1.45299,1.45299) translate(-2324.51,-137.176)" width="12" x="1369.777777777778" y="440"/></g>
  <g id="254">
   <use class="kv10" height="18" transform="rotate(0,1585.93,470.889) scale(2.34568,2.34568) translate(-899.728,-258.03)" width="15" x="1568.333333333333" xlink:href="#Accessory:PT8_0" y="449.7777777777777" zvalue="399"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449547272198" ObjectName="10kVⅡ母电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1585.93,470.889) scale(2.34568,2.34568) translate(-899.728,-258.03)" width="15" x="1568.333333333333" y="449.7777777777777"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="81">
   <path class="kv110" d="M 898.9 404.82 L 898.9 444.26" stroke-width="1" zvalue="95"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="358@0" LinkObjectIDznd="116@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.9 404.82 L 898.9 444.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="80">
   <path class="kv110" d="M 946.78 424.61 L 898.9 424.61" stroke-width="1" zvalue="98"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="118@0" LinkObjectIDznd="81" MaxPinNum="2"/>
   </metadata>
  <path d="M 946.78 424.61 L 898.9 424.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv110" d="M 898.93 380.99 L 898.93 333.65" stroke-width="1" zvalue="151"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="358@1" LinkObjectIDznd="82@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.93 380.99 L 898.93 333.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv110" d="M 941.78 361.57 L 898.93 361.57" stroke-width="1" zvalue="152"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="351@0" LinkObjectIDznd="57" MaxPinNum="2"/>
   </metadata>
  <path d="M 941.78 361.57 L 898.93 361.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv110" d="M 589.35 298.97 L 589.35 333.65" stroke-width="1" zvalue="163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@0" LinkObjectIDznd="82@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.35 298.97 L 589.35 333.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv110" d="M 589.38 275.14 L 589.38 240.15" stroke-width="1" zvalue="164"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@1" LinkObjectIDznd="110@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.38 275.14 L 589.38 240.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv110" d="M 589.69 218.91 L 589.69 183.72" stroke-width="1" zvalue="172"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="105@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.69 218.91 L 589.69 183.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv110" d="M 590.63 159.89 L 590.63 130.98" stroke-width="1" zvalue="175"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@1" LinkObjectIDznd="173@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 590.63 159.89 L 590.63 130.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="200">
   <path class="kv110" d="M 535.81 138.92 L 590.63 138.92" stroke-width="1" zvalue="207"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="44" MaxPinNum="2"/>
   </metadata>
  <path d="M 535.81 138.92 L 590.63 138.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv110" d="M 531.81 203.92 L 589.69 203.92" stroke-width="1" zvalue="208"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="46" MaxPinNum="2"/>
   </metadata>
  <path d="M 531.81 203.92 L 589.69 203.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="kv110" d="M 530.31 258.79 L 589.38 258.79" stroke-width="1" zvalue="209"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="48" MaxPinNum="2"/>
   </metadata>
  <path d="M 530.31 258.79 L 589.38 258.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="219">
   <path class="kv110" d="M 589.38 463.14 L 589.38 428.15" stroke-width="1" zvalue="214"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@1" LinkObjectIDznd="217@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.38 463.14 L 589.38 428.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv110" d="M 589.69 406.91 L 589.69 371.72" stroke-width="1" zvalue="221"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217@0" LinkObjectIDznd="214@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.69 406.91 L 589.69 371.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv110" d="M 589.35 347.89 L 589.35 333.65" stroke-width="1" zvalue="223"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@1" LinkObjectIDznd="82@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.35 347.89 L 589.35 333.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="kv110" d="M 589.45 539.18 L 589.45 486.97" stroke-width="1" zvalue="230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="221@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.45 539.18 L 589.45 486.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="223">
   <path class="kv110" d="M 535.81 386.92 L 589.69 386.92" stroke-width="1" zvalue="231"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@0" LinkObjectIDznd="215" MaxPinNum="2"/>
   </metadata>
  <path d="M 535.81 386.92 L 589.69 386.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="kv110" d="M 531.81 447.92 L 589.38 447.92" stroke-width="1" zvalue="232"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="216@0" LinkObjectIDznd="219" MaxPinNum="2"/>
   </metadata>
  <path d="M 531.81 447.92 L 589.38 447.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv110" d="M 530.31 514.79 L 589.45 514.79" stroke-width="1" zvalue="233"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@0" LinkObjectIDznd="222" MaxPinNum="2"/>
   </metadata>
  <path d="M 530.31 514.79 L 589.45 514.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="kv10" d="M 589.41 605.29 L 589.41 640.02" stroke-width="1" zvalue="237"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@1" LinkObjectIDznd="227@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.41 605.29 L 589.41 640.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="kv110" d="M 589.43 558.24 L 665.11 558.24" stroke-width="1" zvalue="239"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@2" LinkObjectIDznd="229@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.43 558.24 L 665.11 558.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv10" d="M 589.53 661.26 L 589.53 679.39" stroke-width="1" zvalue="242"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="227@1" LinkObjectIDznd="353@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.53 661.26 L 589.53 679.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv10" d="M 589.53 705.72 L 589.53 731.43" stroke-width="1" zvalue="243"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="353@1" LinkObjectIDznd="253@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.53 705.72 L 589.53 731.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv110" d="M 620.79 140.21 L 605.71 140.21 L 605.71 146.25 L 590.63 146.25" stroke-width="1" zvalue="245"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="12@0" LinkObjectIDznd="44" MaxPinNum="2"/>
   </metadata>
  <path d="M 620.79 140.21 L 605.71 140.21 L 605.71 146.25 L 590.63 146.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv110" d="M 898.93 298.97 L 898.93 333.65" stroke-width="1" zvalue="249"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="82@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.93 298.97 L 898.93 333.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv110" d="M 898.96 275.14 L 898.96 240.15" stroke-width="1" zvalue="250"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@1" LinkObjectIDznd="45@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.96 275.14 L 898.96 240.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv110" d="M 897.69 218.91 L 897.69 183.72" stroke-width="1" zvalue="257"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="34@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 897.69 218.91 L 897.69 183.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv110" d="M 898.63 159.89 L 898.63 130.98" stroke-width="1" zvalue="259"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@1" LinkObjectIDznd="30@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.63 159.89 L 898.63 130.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv110" d="M 843.81 138.92 L 898.63 138.92" stroke-width="1" zvalue="265"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="33" MaxPinNum="2"/>
   </metadata>
  <path d="M 843.81 138.92 L 898.63 138.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv110" d="M 839.81 203.92 L 897.69 203.92" stroke-width="1" zvalue="266"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="42" MaxPinNum="2"/>
   </metadata>
  <path d="M 839.81 203.92 L 897.69 203.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv110" d="M 838.31 258.79 L 898.96 258.79" stroke-width="1" zvalue="267"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@0" LinkObjectIDznd="50" MaxPinNum="2"/>
   </metadata>
  <path d="M 838.31 258.79 L 898.96 258.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv110" d="M 928.79 140.21 L 913.71 140.21 L 913.71 146.25 L 898.63 146.25" stroke-width="1" zvalue="269"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@0" LinkObjectIDznd="33" MaxPinNum="2"/>
   </metadata>
  <path d="M 928.79 140.21 L 913.71 140.21 L 913.71 146.25 L 898.63 146.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv10" d="M 542.29 625.11 L 589.41 625.11" stroke-width="1" zvalue="271"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="228" MaxPinNum="2"/>
   </metadata>
  <path d="M 542.29 625.11 L 589.41 625.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv10" d="M 906.78 824.61 L 860.41 824.61" stroke-width="1" zvalue="281"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="79" MaxPinNum="2"/>
   </metadata>
  <path d="M 906.78 824.61 L 860.41 824.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv10" d="M 860.41 731.43 L 860.41 775.61" stroke-width="1" zvalue="288"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@1" LinkObjectIDznd="77@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 860.41 731.43 L 860.41 775.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv10" d="M 860.41 801.94 L 860.41 849.05" stroke-width="1" zvalue="289"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@1" LinkObjectIDznd="74@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 860.41 801.94 L 860.41 849.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv10" d="M 639.14 836.39 L 639.14 869.01" stroke-width="1" zvalue="296"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@1" LinkObjectIDznd="189@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 639.14 836.39 L 639.14 869.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv10" d="M 638.99 809 L 638.99 784.17" stroke-width="1" zvalue="298"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@0" LinkObjectIDznd="311@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 638.99 809 L 638.99 784.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv10" d="M 638.99 757.83 L 638.99 731.43" stroke-width="1" zvalue="300"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="311@0" LinkObjectIDznd="253@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 638.99 757.83 L 638.99 731.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv10" d="M 662.87 853.11 L 639.14 853.11" stroke-width="1" zvalue="302"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="318@0" LinkObjectIDznd="73" MaxPinNum="2"/>
   </metadata>
  <path d="M 662.87 853.11 L 639.14 853.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv10" d="M 639.14 853.11 L 511.91 853.11 L 511.91 880.06" stroke-width="1" zvalue="304"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73" LinkObjectIDznd="320@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 639.14 853.11 L 511.91 853.11 L 511.91 880.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv10" d="M 511.91 972.16 L 511.91 906.39" stroke-width="1" zvalue="306"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@0" LinkObjectIDznd="320@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 511.91 972.16 L 511.91 906.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv10" d="M 546.52 919.37 L 511.91 919.37" stroke-width="1" zvalue="309"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="325@0" LinkObjectIDznd="66" MaxPinNum="2"/>
   </metadata>
  <path d="M 546.52 919.37 L 511.91 919.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv10" d="M 450.8 972.16 L 450.8 946.44 L 511.91 946.44" stroke-width="1" zvalue="311"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="326@0" LinkObjectIDznd="66" MaxPinNum="2"/>
   </metadata>
  <path d="M 450.8 972.16 L 450.8 946.44 L 511.91 946.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv10" d="M 568.83 974.61 L 568.83 948.67 L 511.91 948.67" stroke-width="1" zvalue="313"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="328@0" LinkObjectIDznd="66" MaxPinNum="2"/>
   </metadata>
  <path d="M 568.83 974.61 L 568.83 948.67 L 511.91 948.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv10" d="M 1150.92 837.5 L 1150.92 870.12" stroke-width="1" zvalue="321"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@1" LinkObjectIDznd="138@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1150.92 837.5 L 1150.92 870.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv10" d="M 1150.76 810.11 L 1150.76 785.28" stroke-width="1" zvalue="323"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@0" LinkObjectIDznd="125@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1150.76 810.11 L 1150.76 785.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv10" d="M 1150.76 758.94 L 1150.76 731.43" stroke-width="1" zvalue="325"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@0" LinkObjectIDznd="253@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1150.76 758.94 L 1150.76 731.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv10" d="M 1174.65 854.22 L 1150.92 854.22" stroke-width="1" zvalue="327"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="119@0" LinkObjectIDznd="128" MaxPinNum="2"/>
   </metadata>
  <path d="M 1174.65 854.22 L 1150.92 854.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv10" d="M 1150.92 854.22 L 1023.69 854.22 L 1023.69 881.17" stroke-width="1" zvalue="329"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128" LinkObjectIDznd="108@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1150.92 854.22 L 1023.69 854.22 L 1023.69 881.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv10" d="M 1023.69 973.27 L 1023.69 907.5" stroke-width="1" zvalue="331"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="135@0" LinkObjectIDznd="108@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023.69 973.27 L 1023.69 907.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv10" d="M 1058.29 920.48 L 1023.69 920.48" stroke-width="1" zvalue="334"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@0" LinkObjectIDznd="101" MaxPinNum="2"/>
   </metadata>
  <path d="M 1058.29 920.48 L 1023.69 920.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv10" d="M 962.58 973.27 L 962.58 947.56 L 1023.69 947.56" stroke-width="1" zvalue="336"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="101" MaxPinNum="2"/>
   </metadata>
  <path d="M 962.58 973.27 L 962.58 947.56 L 1023.69 947.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv10" d="M 1080.61 975.72 L 1080.61 949.78 L 1023.69 949.78" stroke-width="1" zvalue="338"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="101" MaxPinNum="2"/>
   </metadata>
  <path d="M 1080.61 975.72 L 1080.61 949.78 L 1023.69 949.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv10" d="M 1322.76 758.94 L 1322.76 731.43" stroke-width="1" zvalue="343"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="253@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.76 758.94 L 1322.76 731.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv10" d="M 1322.76 810.11 L 1322.76 785.28" stroke-width="1" zvalue="344"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@0" LinkObjectIDznd="155@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.76 810.11 L 1322.76 785.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv10" d="M 1322.92 837.5 L 1322.92 888.55" stroke-width="1" zvalue="345"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@1" LinkObjectIDznd="55@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.92 837.5 L 1322.92 888.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="174">
   <path class="kv10" d="M 1375.12 868.51 L 1322.92 868.51" stroke-width="1" zvalue="350"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@1" LinkObjectIDznd="164" MaxPinNum="2"/>
   </metadata>
  <path d="M 1375.12 868.51 L 1322.92 868.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="kv10" d="M 1446.32 869.47 L 1396.36 869.47" stroke-width="1" zvalue="351"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@0" LinkObjectIDznd="169@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1446.32 869.47 L 1396.36 869.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv10" d="M 1515.31 871.11 L 1470.15 871.11" stroke-width="1" zvalue="354"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="172@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1515.31 871.11 L 1470.15 871.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv10" d="M 1321.53 661.26 L 1321.53 679.39" stroke-width="1" zvalue="360"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@1" LinkObjectIDznd="190@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1321.53 661.26 L 1321.53 679.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv10" d="M 1321.53 705.72 L 1321.53 731.43" stroke-width="1" zvalue="362"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@1" LinkObjectIDznd="157" MaxPinNum="2"/>
   </metadata>
  <path d="M 1321.53 705.72 L 1321.53 731.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="237">
   <path class="kv10" d="M 1321.41 640.02 L 1321.41 434.6" stroke-width="1" zvalue="385"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@0" LinkObjectIDznd="236@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1321.41 640.02 L 1321.41 434.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="238">
   <path class="kv10" d="M 1322.74 413.35 L 1322.74 387.94" stroke-width="1" zvalue="386"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="236@0" LinkObjectIDznd="235@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.74 413.35 L 1322.74 387.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="239">
   <path class="kv10" d="M 1322.74 361.61 L 1322.74 333.65" stroke-width="1" zvalue="387"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@0" LinkObjectIDznd="193@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.74 361.61 L 1322.74 333.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv10" d="M 1356.7 622.89 L 1321.41 622.89" stroke-width="1" zvalue="388"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@0" LinkObjectIDznd="237" MaxPinNum="2"/>
   </metadata>
  <path d="M 1356.7 622.89 L 1321.41 622.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="241">
   <path class="kv10" d="M 1221.46 648.82 L 1221.46 622.89 L 1322.22 622.89" stroke-width="1" zvalue="389"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="240" MaxPinNum="2"/>
   </metadata>
  <path d="M 1221.46 648.82 L 1221.46 622.89 L 1322.22 622.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="244">
   <path class="kv10" d="M 1360.7 458.89 L 1321.41 458.89" stroke-width="1" zvalue="392"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="245@0" LinkObjectIDznd="237" MaxPinNum="2"/>
   </metadata>
  <path d="M 1360.7 458.89 L 1321.41 458.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="255">
   <path class="kv10" d="M 1630.11 426.83 L 1583.74 426.83" stroke-width="1" zvalue="398"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="256@0" LinkObjectIDznd="250" MaxPinNum="2"/>
   </metadata>
  <path d="M 1630.11 426.83 L 1583.74 426.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="251">
   <path class="kv10" d="M 1583.74 333.65 L 1583.74 377.83" stroke-width="1" zvalue="403"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@1" LinkObjectIDznd="252@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1583.74 333.65 L 1583.74 377.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="250">
   <path class="kv10" d="M 1583.74 404.17 L 1583.74 451.28" stroke-width="1" zvalue="404"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="252@1" LinkObjectIDznd="254@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1583.74 404.17 L 1583.74 451.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="263">
   <path class="kv10" d="M 1322.62 261.35 L 1322.63 235.94" stroke-width="1" zvalue="410"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="264@0" LinkObjectIDznd="265@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.62 261.35 L 1322.63 235.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="kv10" d="M 1322.74 282.6 L 1322.74 333.65" stroke-width="1" zvalue="415"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="264@1" LinkObjectIDznd="193@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.74 282.6 L 1322.74 333.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="268">
   <path class="kv10" d="M 1322.63 209.61 L 1322.63 138.98" stroke-width="1" zvalue="418"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265@0" LinkObjectIDznd="267@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.63 209.61 L 1322.63 138.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="55">
   <use class="kv10" height="30" transform="rotate(0,1321.83,909.833) scale(1.46748,1.47778) translate(-414.537,-286.99)" width="28" x="1301.284749097668" xlink:href="#EnergyConsumer:站用变DY接地_0" y="887.6666666666667" zvalue="153"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449543274501" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1321.83,909.833) scale(1.46748,1.47778) translate(-414.537,-286.99)" width="28" x="1301.284749097668" y="887.6666666666667"/></g>
  <g id="178">
   <use class="kv10" height="30" transform="rotate(90,1531.96,871.111) scale(-1.25,1.23333) translate(-2756.03,-161.305)" width="12" x="1524.463785560517" xlink:href="#EnergyConsumer:负荷_0" y="852.611111111111" zvalue="353"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449546813446" ObjectName="10kV大坝线"/>
   <cge:TPSR_Ref TObjectID="6192449546813446"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1531.96,871.111) scale(-1.25,1.23333) translate(-2756.03,-161.305)" width="12" x="1524.463785560517" y="852.611111111111"/></g>
 </g>
 <g id="BreakerClass">
  <g id="110">
   <use class="kv110" height="20" transform="rotate(0,589.726,229.54) scale(1.22222,1.11111) translate(-106.112,-21.8429)" width="10" x="583.6150793650793" xlink:href="#Breaker:开关_0" y="218.4285711560931" zvalue="168"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924471750661" ObjectName="110kV高河三级线171断路器"/>
   <cge:TPSR_Ref TObjectID="6473924471750661"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,589.726,229.54) scale(1.22222,1.11111) translate(-106.112,-21.8429)" width="10" x="583.6150793650793" y="218.4285711560931"/></g>
  <g id="217">
   <use class="kv110" height="20" transform="rotate(0,589.726,417.54) scale(1.22222,1.11111) translate(-106.112,-40.6429)" width="10" x="583.6150793650793" xlink:href="#Breaker:开关_0" y="406.4285711560931" zvalue="217"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924471816197" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473924471816197"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,589.726,417.54) scale(1.22222,1.11111) translate(-106.112,-40.6429)" width="10" x="583.6150793650793" y="406.4285711560931"/></g>
  <g id="227">
   <use class="kv10" height="20" transform="rotate(0,589.451,650.651) scale(1.22222,1.11111) translate(-106.062,-63.954)" width="10" x="583.34007519179" xlink:href="#Breaker:开关_0" y="639.5396822672042" zvalue="236"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924471881733" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924471881733"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,589.451,650.651) scale(1.22222,1.11111) translate(-106.062,-63.954)" width="10" x="583.34007519179" y="639.5396822672042"/></g>
  <g id="45">
   <use class="kv110" height="20" transform="rotate(0,897.726,229.54) scale(1.22222,1.11111) translate(-162.112,-21.8429)" width="10" x="891.6150793650793" xlink:href="#Breaker:开关_0" y="218.4285711560931" zvalue="253"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924471947269" ObjectName="110kV高河三四级线172断路器"/>
   <cge:TPSR_Ref TObjectID="6473924471947269"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,897.726,229.54) scale(1.22222,1.11111) translate(-162.112,-21.8429)" width="10" x="891.6150793650793" y="218.4285711560931"/></g>
  <g id="181">
   <use class="kv10" height="20" transform="rotate(0,639.039,822.708) scale(1.59229,1.43307) translate(-234.745,-244.288)" width="10" x="631.0772582189995" xlink:href="#Breaker:开关_0" y="808.3773008671817" zvalue="294"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924472012805" ObjectName="#1发电机071断路器"/>
   <cge:TPSR_Ref TObjectID="6473924472012805"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,639.039,822.708) scale(1.59229,1.43307) translate(-234.745,-244.288)" width="10" x="631.0772582189995" y="808.3773008671817"/></g>
  <g id="130">
   <use class="kv10" height="20" transform="rotate(0,1150.82,823.819) scale(1.59229,1.43307) translate(-425.114,-244.623)" width="10" x="1142.855035996777" xlink:href="#Breaker:开关_0" y="809.4884119782926" zvalue="319"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924472078341" ObjectName="#2发电机072断路器"/>
   <cge:TPSR_Ref TObjectID="6473924472078341"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1150.82,823.819) scale(1.59229,1.43307) translate(-425.114,-244.623)" width="10" x="1142.855035996777" y="809.4884119782926"/></g>
  <g id="153">
   <use class="kv10" height="20" transform="rotate(0,1322.82,823.819) scale(1.59229,1.43307) translate(-489.094,-244.623)" width="10" x="1314.855035996777" xlink:href="#Breaker:开关_0" y="809.4884119782926" zvalue="341"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924472143877" ObjectName="#1站用变073断路器"/>
   <cge:TPSR_Ref TObjectID="6473924472143877"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1322.82,823.819) scale(1.59229,1.43307) translate(-489.094,-244.623)" width="10" x="1314.855035996777" y="809.4884119782926"/></g>
  <g id="169">
   <use class="kv10" height="20" transform="rotate(90,1385.73,868.429) scale(1.22222,1.11111) translate(-250.839,-85.7317)" width="10" x="1379.615079365079" xlink:href="#Breaker:开关_0" y="857.317460044982" zvalue="347"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924472209413" ObjectName="10kV大坝线077断路器"/>
   <cge:TPSR_Ref TObjectID="6473924472209413"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1385.73,868.429) scale(1.22222,1.11111) translate(-250.839,-85.7317)" width="10" x="1379.615079365079" y="857.317460044982"/></g>
  <g id="192">
   <use class="kv10" height="20" transform="rotate(0,1321.45,650.651) scale(1.22222,1.11111) translate(-239.153,-63.954)" width="10" x="1315.34007519179" xlink:href="#Breaker:开关_0" y="639.5396822672042" zvalue="356"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924472274949" ObjectName="10kVⅠ母侧074断路器"/>
   <cge:TPSR_Ref TObjectID="6473924472274949"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1321.45,650.651) scale(1.22222,1.11111) translate(-239.153,-63.954)" width="10" x="1315.34007519179" y="639.5396822672042"/></g>
  <g id="236">
   <use class="kv10" height="20" transform="rotate(0,1322.78,423.984) scale(1.22222,1.11111) translate(-239.395,-41.2873)" width="10" x="1316.673408525123" xlink:href="#Breaker:开关_0" y="412.8730156005375" zvalue="384"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924472340485" ObjectName="10kVⅡ母侧075断路器"/>
   <cge:TPSR_Ref TObjectID="6473924472340485"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1322.78,423.984) scale(1.22222,1.11111) translate(-239.395,-41.2873)" width="10" x="1316.673408525123" y="412.8730156005375"/></g>
  <g id="264">
   <use class="kv10" height="20" transform="rotate(0,1322.66,271.984) scale(1.22222,1.11111) translate(-239.373,-26.0873)" width="10" x="1316.551186302901" xlink:href="#Breaker:开关_0" y="260.8730156005375" zvalue="408"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924472406021" ObjectName="10kV高河二三级线076断路器"/>
   <cge:TPSR_Ref TObjectID="6473924472406021"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1322.66,271.984) scale(1.22222,1.11111) translate(-239.373,-26.0873)" width="10" x="1316.551186302901" y="260.8730156005375"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="30">
   <use class="kv110" height="30" transform="rotate(0,898.63,112.667) scale(2.14286,1.23333) translate(-475.27,-17.8153)" width="7" x="891.1304522271839" xlink:href="#ACLineSegment:线路_0" y="94.16666666666657" zvalue="263"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249320849413" ObjectName="110kV高河三四级线"/>
   <cge:TPSR_Ref TObjectID="8444249320849413_5066549581774850"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,898.63,112.667) scale(2.14286,1.23333) translate(-475.27,-17.8153)" width="7" x="891.1304522271839" y="94.16666666666657"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="189">
   <use class="kv10" height="30" transform="rotate(0,640.141,890.145) scale(1.43307,1.43307) translate(-186.952,-262.501)" width="30" x="618.6455089277976" xlink:href="#Generator:发电机_0" y="868.6486200704142" zvalue="291"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449546027013" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449546027013"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,640.141,890.145) scale(1.43307,1.43307) translate(-186.952,-262.501)" width="30" x="618.6455089277976" y="868.6486200704142"/></g>
  <g id="138">
   <use class="kv10" height="30" transform="rotate(0,1151.92,891.256) scale(1.43307,1.43307) translate(-341.608,-262.837)" width="30" x="1130.423286705575" xlink:href="#Generator:发电机_0" y="869.7597311815252" zvalue="316"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449546616838" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449546616838"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1151.92,891.256) scale(1.43307,1.43307) translate(-341.608,-262.837)" width="30" x="1130.423286705575" y="869.7597311815252"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="35" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,594.408,30.0278) scale(1,1) translate(0,-3.91045e-14)" writing-mode="lr" x="593.9400000000001" xml:space="preserve" y="34.72" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124055416836" ObjectName="P"/>
   </metadata>
  </g>
  <g id="36">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="36" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,900.853,31.1389) scale(1,1) translate(0,3.71493e-14)" writing-mode="lr" x="900.38" xml:space="preserve" y="35.85" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124061577220" ObjectName="P"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="37" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1600.24,852.583) scale(1,1) translate(0,0)" writing-mode="lr" x="1599.77" xml:space="preserve" y="857.28" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124066426884" ObjectName="P"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="38" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1322.63,25.6944) scale(1,1) translate(0,-2.97293e-14)" writing-mode="lr" x="1322.16" xml:space="preserve" y="30.39" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124069441540" ObjectName="P"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="54" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,594.408,47.0833) scale(1,1) translate(0,8.6437e-14)" writing-mode="lr" x="593.9400000000001" xml:space="preserve" y="51.77" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124055482372" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="93" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,900.853,50.5278) scale(1,1) translate(0,1.24734e-13)" writing-mode="lr" x="900.38" xml:space="preserve" y="55.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124061642756" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="94" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1600.24,872.194) scale(1,1) translate(0,1.90971e-12)" writing-mode="lr" x="1599.77" xml:space="preserve" y="876.89" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124066492420" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="97" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1322.63,45.3056) scale(1,1) translate(0,3.74392e-15)" writing-mode="lr" x="1322.16" xml:space="preserve" y="49.99" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124069507076" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="98" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,594.408,63.5833) scale(1,1) translate(0,1.35762e-13)" writing-mode="lr" x="593.9400000000001" xml:space="preserve" y="68.25" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124055547908" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="99" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,900.853,65.8056) scale(1,1) translate(0,-1.27922e-14)" writing-mode="lr" x="900.38" xml:space="preserve" y="70.44" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124061708292" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="103" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1600.24,889.028) scale(1,1) translate(0,-2.53971e-12)" writing-mode="lr" x="1599.77" xml:space="preserve" y="893.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124066557956" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="106">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="106" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1322.63,63.25) scale(1,1) translate(0,-1.20027e-14)" writing-mode="lr" x="1322.16" xml:space="preserve" y="67.90000000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124069572612" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="107" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,660.522,397.344) scale(1,1) translate(0,-2.56845e-13)" writing-mode="lr" x="659.97" xml:space="preserve" y="402.03" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124058497028" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="111" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,660.522,417.51) scale(1,1) translate(0,-9.06235e-14)" writing-mode="lr" x="659.97" xml:space="preserve" y="422.16" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124058562564" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="112">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="112" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,662.299,629.549) scale(1,1) translate(6.766e-14,0)" writing-mode="lr" x="661.75" xml:space="preserve" y="634.24" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124058628100" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="114">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="114" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,662.299,648.382) scale(1,1) translate(0,-2.8278e-13)" writing-mode="lr" x="661.75" xml:space="preserve" y="653.0700000000001" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124058693636" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="115" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,660.522,442.122) scale(1,1) translate(0,4.78159e-14)" writing-mode="lr" x="659.97" xml:space="preserve" y="446.8" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124058759172" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="141" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,662.299,668.882) scale(1,1) translate(0,-2.91218e-13)" writing-mode="lr" x="661.75" xml:space="preserve" y="673.59" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124059086852" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="143">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="143" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,717.919,887.487) scale(1,1) translate(-1.47642e-13,-1.94481e-12)" writing-mode="lr" x="717.37" xml:space="preserve" y="892.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124063543300" ObjectName="P"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="150" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1231.25,889.709) scale(1,1) translate(0,9.73637e-14)" writing-mode="lr" x="1230.7" xml:space="preserve" y="894.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124064722948" ObjectName="P"/>
   </metadata>
  </g>
  <g id="151">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="151" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,717.919,910.987) scale(1,1) translate(0,2.09722e-12)" writing-mode="lr" x="717.37" xml:space="preserve" y="915.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124063608836" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="159">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="159" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1231.25,907.876) scale(1,1) translate(0,-2.39002e-12)" writing-mode="lr" x="1230.7" xml:space="preserve" y="912.55" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124064788484" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="161">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="161" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,717.919,934.487) scale(1,1) translate(-1.47642e-13,2.1524e-12)" writing-mode="lr" x="717.37" xml:space="preserve" y="939.17" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124063674372" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="184">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="184" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1231.25,928.82) scale(1,1) translate(0,-2.2397e-12)" writing-mode="lr" x="1230.7" xml:space="preserve" y="933.51" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124064854020" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="274">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="274" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124057841668" ObjectName="F"/>
   </metadata>
  </g>
  <g id="273">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="273" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127231815684" ObjectName="高河三级上网有功"/>
   </metadata>
  </g>
  <g id="272">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="272" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124071211012" ObjectName="F"/>
   </metadata>
  </g>
  <g id="260">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="260" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124071014404" ObjectName="F"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124071079940" ObjectName="F"/>
   </metadata>
  </g>
  <g id="209">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="209" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127183515653" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="198">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127183450117" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="197">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="197" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,358.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="363.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124068261892" ObjectName="F"/>
   </metadata>
  </g>
  <g id="191">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="191" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.111,333.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.27" xml:space="preserve" y="338.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124058365956" ObjectName="F"/>
   </metadata>
  </g>
  <g id="287">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="287" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,378.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="383.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124070490116" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="288">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="288" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,335.611,378.167) scale(1,1) translate(0,0)" writing-mode="lr" x="335.77" xml:space="preserve" y="383.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124070555652" ObjectName="雨量采集"/>
   </metadata>
  </g>
  <g id="124">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="124" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,907.543,555.15) scale(1,1) translate(0,0)" writing-mode="lr" x="907.0700000000001" xml:space="preserve" y="559.9299999999999" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124057448452" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="126">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="126" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,867.254,944.927) scale(1,1) translate(0,0)" writing-mode="lr" x="866.79" xml:space="preserve" y="949.71" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124057972740" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="133">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="133" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1584.67,551.15) scale(1,1) translate(0,0)" writing-mode="lr" x="1584.2" xml:space="preserve" y="555.9299999999999" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124067868676" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="134">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="134" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,907.543,584.15) scale(1,1) translate(0,0)" writing-mode="lr" x="907.0700000000001" xml:space="preserve" y="588.9299999999999" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124057513988" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="136" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,867.254,973.927) scale(1,1) translate(0,0)" writing-mode="lr" x="866.79" xml:space="preserve" y="978.71" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124058038276" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="137" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1584.67,580.15) scale(1,1) translate(0,1.24933e-13)" writing-mode="lr" x="1584.2" xml:space="preserve" y="584.9299999999999" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124067934212" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="139">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="139" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,907.543,613.15) scale(1,1) translate(0,0)" writing-mode="lr" x="907.0700000000001" xml:space="preserve" y="617.9299999999999" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124057579524" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="140" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,867.254,1002.93) scale(1,1) translate(0,0)" writing-mode="lr" x="866.79" xml:space="preserve" y="1007.71" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124058103812" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="142" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1584.67,613.15) scale(1,1) translate(0,0)" writing-mode="lr" x="1584.2" xml:space="preserve" y="617.9299999999999" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124067999748" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="144">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="144" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,480.543,345.65) scale(1,1) translate(2.9013e-13,0)" writing-mode="lr" x="480.07" xml:space="preserve" y="350.43" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124057710596" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="145" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,473.254,754.427) scale(1,1) translate(0,0)" writing-mode="lr" x="472.79" xml:space="preserve" y="759.21" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124058234884" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="156">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="156" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1209.67,349.65) scale(1,1) translate(3.87912e-13,0)" writing-mode="lr" x="1209.2" xml:space="preserve" y="354.43" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124068130820" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="271">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="457"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374884470788" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="286">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="477"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950045892616" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
 </g>
</svg>