<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549683748865" height="1200" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1200" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:带电显示器_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.41666666666667" x2="8.750000000000002" y1="11.08333333333333" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.08333333333333" x2="10.08333333333333" y1="1.5" y2="6.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.08333333333333" x2="10.08333333333333" y1="8.5" y2="10.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333334" x2="12.58333333333333" y1="6.5" y2="6.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333334" x2="12.58333333333333" y1="8.5" y2="8.5"/>
   <ellipse cx="10.08" cy="12.75" fill-opacity="0" rx="2.24" ry="2.24" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.083333333333334" x2="11.08333333333333" y1="19.5" y2="19.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.666666666666668" x2="11.66666666666667" y1="11.08333333333333" y2="14.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.583333333333334" x2="11.58333333333333" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.08333333333333" x2="10.08333333333333" y1="17.5" y2="15.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.083333333333334" x2="12.08333333333333" y1="17.5" y2="17.5"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_0" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3" x2="7" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.25" x2="5.083333333333332" y1="7.083333333333336" y2="19.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_1" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.833333333333333" x2="6.916666666666667" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.05" x2="5.05" y1="5.000000000000004" y2="22.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_2" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.166666666666668" x2="1.333333333333333" y1="5.083333333333332" y2="24.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1" x2="8.833333333333334" y1="4.999999999999998" y2="24.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="3" y1="23" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="12" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="12" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0.3333333333333357" y2="12.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="12" y2="12"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸2020_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="Accessory:厂用变2020_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <path d="M 13 13.5 L 17 13.5 L 15 10.5 L 13 13.5 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="20.66666666666666" y2="22.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="17.91666666666666" y2="20.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="25.41666666666667" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="20.66666666666666" y2="22.66666666666666"/>
   <ellipse cx="14.95" cy="20.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.95" cy="12.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0833 3.5 L 14.0833 4.5 L 16.0833 4.5 L 15.0833 3.5 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="1" y2="7.333333333333331"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.750000000000004" y2="4.166666666666668"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="32"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="1" y1="27" y2="11"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="27" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Accessory:PT2525_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,4.96) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.25" y2="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="8.166666666666663" y1="24.91666666666666" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="10.75" y1="21.91666666666666" y2="24.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="13.75" y1="24.91666666666666" y2="26.91666666666666"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_0" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.416666666666625" x2="14.8889734851558" y1="30.74450652239035" y2="19.51308960898588"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2340.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_1" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.8889734851558" x2="14.8889734851558" y1="32.25" y2="19.51308960898588"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2340.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_2" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.88620689655174" x2="9.700000000000015" y1="19.49501474926254" y2="32.31404129793511"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.699999999999994" x2="20.24999999999999" y1="19.33333333333333" y2="32.38333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2340.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆_0" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="15" cy="15" fill="rgb(255,0,0)" fill-opacity="1" rx="14.63" ry="14.63" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆_1" viewBox="0,0,30,30">
   <ellipse Plane="0" cx="14.75" cy="15" fill="rgb(0,170,0)" fill-opacity="1" rx="14.36" ry="14.36" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV测试变" InitShowingPlane="" fill="rgb(0,0,0)" height="1200" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="500kV德宏变_全站公用.svg"><rect fill-opacity="0" height="24" width="97" x="19.71" y="858.5700000000001" zvalue="704"/></g>
  <g href="单厂站信息.svg"><rect fill-opacity="0" height="53.5" width="270.75" x="911.25" y="42.5" zvalue="739"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="53.5" width="270.75" x="1191.25" y="42.5" zvalue="743"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="52.29" id="3" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,238.048,58.1905) scale(1,1) translate(0,0)" width="383.9" x="46.1" y="32.05" zvalue="495"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="37" id="1" stroke="rgb(255,255,255)" text-anchor="middle" text-weight="bolder" transform="rotate(0,238.048,58.1905) scale(1,1) translate(0,0)" writing-mode="lr" x="238.05" xml:space="preserve" y="71.69" zvalue="495">110kV测试变</text>
  <image height="64" id="2" preserveAspectRatio="xMidYMid slice" width="298" x="27.71" xlink:href="logo.png" y="599.5700000000001"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,176.714,631.571) scale(1,1) translate(0,0)" writing-mode="lr" x="176.71" xml:space="preserve" y="635.0700000000001" zvalue="698"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,179.214,629.262) scale(1,1) translate(0,0)" writing-mode="lr" x="179.21" xml:space="preserve" y="638.26" zvalue="699">110kV测试变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="37" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,68.2143,870.571) scale(1,1) translate(0,0)" width="97" x="19.71" y="858.5700000000001" zvalue="704"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,68.2143,870.571) scale(1,1) translate(0,0)" writing-mode="lr" x="68.20999999999999" xml:space="preserve" y="875.0700000000001" zvalue="704">全站公用</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="53.5" id="54" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1046.62,69.25) scale(1,1) translate(0,0)" width="270.75" x="911.25" y="42.5" zvalue="739"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1046.62,69.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1046.63" xml:space="preserve" y="73.75" zvalue="739">Button</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="53.5" id="55" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1326.62,69.25) scale(1,1) translate(0,0)" width="270.75" x="1191.25" y="42.5" zvalue="743"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1326.62,69.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1326.63" xml:space="preserve" y="73.75" zvalue="743">AVC功能</text>
  <rect fill="none" fill-opacity="0" height="79" id="29" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1205.75,319.25) scale(1,1) translate(0,0)" width="205" x="1103.25" y="279.75" zvalue="572"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,655.186,551.602) scale(1,1) translate(-1.39333e-13,5.95381e-14)" writing-mode="lr" x="655.1900000000001" xml:space="preserve" y="556.1" zvalue="579">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,595.837,702.702) scale(1,1) translate(0,0)" writing-mode="lr" x="595.84" xml:space="preserve" y="707.2" zvalue="582">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,736.265,1063.74) scale(1,1) translate(0,3.47383e-13)" writing-mode="lr" x="736.264661569014" xml:space="preserve" y="1068.24045413687" zvalue="584">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,763.554,675) scale(1,1) translate(0,0)" writing-mode="lr" x="763.55" xml:space="preserve" y="679.5" zvalue="591">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,936.375,548.111) scale(1,1) translate(0,0)" writing-mode="lr" x="936.38" xml:space="preserve" y="552.61" zvalue="594">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,703.225,801) scale(1,1) translate(1.00052e-12,0)" writing-mode="lr" x="703.23" xml:space="preserve" y="805.5" zvalue="612">631</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,889.945,826.277) scale(1,1) translate(1.94499e-13,0)" writing-mode="lr" x="889.95" xml:space="preserve" y="830.78" zvalue="619">6911</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,905.778,762.889) scale(1,1) translate(0,0)" writing-mode="lr" x="905.78" xml:space="preserve" y="767.39" zvalue="621">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,805.083,989.917) scale(1,1) translate(0,0)" writing-mode="lr" x="805.08" xml:space="preserve" y="994.42" zvalue="625">励磁PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,891.75,956.667) scale(1,1) translate(0,0)" writing-mode="lr" x="891.75" xml:space="preserve" y="961.17" zvalue="627">6912</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,911.25,1038.25) scale(1,1) translate(0,0)" writing-mode="lr" x="911.25" xml:space="preserve" y="1042.75" zvalue="630">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1196.51,1064.99) scale(1,1) translate(3.84485e-13,3.47799e-13)" writing-mode="lr" x="1196.514661569014" xml:space="preserve" y="1069.49045413687" zvalue="633">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1163.48,802.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1163.48" xml:space="preserve" y="806.75" zvalue="635">632</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1347.2,830.527) scale(1,1) translate(0,0)" writing-mode="lr" x="1347.2" xml:space="preserve" y="835.03" zvalue="641">6921</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1364.03,764.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1364.03" xml:space="preserve" y="768.64" zvalue="643">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1264.56,993.705) scale(1,1) translate(0,0)" writing-mode="lr" x="1264.56" xml:space="preserve" y="998.21" zvalue="647">励磁PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1350,957.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1350" xml:space="preserve" y="962.42" zvalue="649">6922</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1369.5,1039.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1369.5" xml:space="preserve" y="1044" zvalue="651">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1304.07,470.674) scale(1,1) translate(0,0)" writing-mode="lr" x="1304.07" xml:space="preserve" y="475.17" zvalue="653">母线PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1335.04,583.739) scale(1,1) translate(0,0)" writing-mode="lr" x="1335.04" xml:space="preserve" y="588.24" zvalue="655">6901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1674.36,794.139) scale(1,1) translate(0,0)" writing-mode="lr" x="1674.36" xml:space="preserve" y="798.64" zvalue="661">633</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1511.33,893.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1511.33" xml:space="preserve" y="897.72" zvalue="663">-67</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.93664745163255" x2="88.28134745163254" y1="997.2380720772878" y2="997.2380720772878"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.93664745163255" x2="88.28134745163254" y1="1034.728072077288" y2="1034.728072077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.93664745163255" x2="39.93664745163255" y1="997.2380720772878" y2="1034.728072077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28134745163254" x2="88.28134745163254" y1="997.2380720772878" y2="1034.728072077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28164745163247" x2="150.3899474516325" y1="997.2380720772878" y2="997.2380720772878"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28164745163247" x2="150.3899474516325" y1="1034.728072077288" y2="1034.728072077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28164745163247" x2="88.28164745163247" y1="997.2380720772878" y2="1034.728072077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3899474516325" x2="150.3899474516325" y1="997.2380720772878" y2="1034.728072077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3894474516325" x2="213.7142474516326" y1="997.2380720772878" y2="997.2380720772878"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3894474516325" x2="213.7142474516326" y1="1034.728072077288" y2="1034.728072077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3894474516325" x2="150.3894474516325" y1="997.2380720772878" y2="1034.728072077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7142474516326" x2="213.7142474516326" y1="997.2380720772878" y2="1034.728072077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7141474516325" x2="275.8224474516326" y1="997.2380720772878" y2="997.2380720772878"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7141474516325" x2="275.8224474516326" y1="1034.728072077288" y2="1034.728072077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7141474516325" x2="213.7141474516325" y1="997.2380720772878" y2="1034.728072077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="275.8224474516326" y1="997.2380720772878" y2="1034.728072077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="337.9307474516324" y1="997.2380720772878" y2="997.2380720772878"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="337.9307474516324" y1="1034.728072077288" y2="1034.728072077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="275.8224474516326" y1="997.2380720772878" y2="1034.728072077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="337.9307474516324" x2="337.9307474516324" y1="997.2380720772878" y2="1034.728072077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.93664745163255" x2="88.28134745163254" y1="1034.728172077288" y2="1034.728172077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.93664745163255" x2="88.28134745163254" y1="1058.896772077288" y2="1058.896772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.93664745163255" x2="39.93664745163255" y1="1034.728172077288" y2="1058.896772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28134745163254" x2="88.28134745163254" y1="1034.728172077288" y2="1058.896772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28164745163247" x2="150.3899474516325" y1="1034.728172077288" y2="1034.728172077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28164745163247" x2="150.3899474516325" y1="1058.896772077288" y2="1058.896772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28164745163247" x2="88.28164745163247" y1="1034.728172077288" y2="1058.896772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3899474516325" x2="150.3899474516325" y1="1034.728172077288" y2="1058.896772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3894474516325" x2="213.7142474516326" y1="1034.728172077288" y2="1034.728172077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3894474516325" x2="213.7142474516326" y1="1058.896772077288" y2="1058.896772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3894474516325" x2="150.3894474516325" y1="1034.728172077288" y2="1058.896772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7142474516326" x2="213.7142474516326" y1="1034.728172077288" y2="1058.896772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7141474516325" x2="275.8224474516326" y1="1034.728172077288" y2="1034.728172077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7141474516325" x2="275.8224474516326" y1="1058.896772077288" y2="1058.896772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7141474516325" x2="213.7141474516325" y1="1034.728172077288" y2="1058.896772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="275.8224474516326" y1="1034.728172077288" y2="1058.896772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="337.9307474516324" y1="1034.728172077288" y2="1034.728172077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="337.9307474516324" y1="1058.896772077288" y2="1058.896772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="275.8224474516326" y1="1034.728172077288" y2="1058.896772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="337.9307474516324" x2="337.9307474516324" y1="1034.728172077288" y2="1058.896772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.93664745163255" x2="88.28134745163254" y1="1058.896772077288" y2="1058.896772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.93664745163255" x2="88.28134745163254" y1="1083.065372077288" y2="1083.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.93664745163255" x2="39.93664745163255" y1="1058.896772077288" y2="1083.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28134745163254" x2="88.28134745163254" y1="1058.896772077288" y2="1083.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28164745163247" x2="150.3899474516325" y1="1058.896772077288" y2="1058.896772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28164745163247" x2="150.3899474516325" y1="1083.065372077288" y2="1083.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28164745163247" x2="88.28164745163247" y1="1058.896772077288" y2="1083.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3899474516325" x2="150.3899474516325" y1="1058.896772077288" y2="1083.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3894474516325" x2="213.7142474516326" y1="1058.896772077288" y2="1058.896772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3894474516325" x2="213.7142474516326" y1="1083.065372077288" y2="1083.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3894474516325" x2="150.3894474516325" y1="1058.896772077288" y2="1083.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7142474516326" x2="213.7142474516326" y1="1058.896772077288" y2="1083.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7141474516325" x2="275.8224474516326" y1="1058.896772077288" y2="1058.896772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7141474516325" x2="275.8224474516326" y1="1083.065372077288" y2="1083.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7141474516325" x2="213.7141474516325" y1="1058.896772077288" y2="1083.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="275.8224474516326" y1="1058.896772077288" y2="1083.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="337.9307474516324" y1="1058.896772077288" y2="1058.896772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="337.9307474516324" y1="1083.065372077288" y2="1083.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="275.8224474516326" y1="1058.896772077288" y2="1083.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="337.9307474516324" x2="337.9307474516324" y1="1058.896772077288" y2="1083.065372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.93664745163255" x2="88.28134745163254" y1="1083.065412077288" y2="1083.065412077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.93664745163255" x2="88.28134745163254" y1="1107.234012077288" y2="1107.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.93664745163255" x2="39.93664745163255" y1="1083.065412077288" y2="1107.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28134745163254" x2="88.28134745163254" y1="1083.065412077288" y2="1107.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28164745163247" x2="150.3899474516325" y1="1083.065412077288" y2="1083.065412077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28164745163247" x2="150.3899474516325" y1="1107.234012077288" y2="1107.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28164745163247" x2="88.28164745163247" y1="1083.065412077288" y2="1107.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3899474516325" x2="150.3899474516325" y1="1083.065412077288" y2="1107.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3894474516325" x2="213.7142474516326" y1="1083.065412077288" y2="1083.065412077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3894474516325" x2="213.7142474516326" y1="1107.234012077288" y2="1107.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3894474516325" x2="150.3894474516325" y1="1083.065412077288" y2="1107.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7142474516326" x2="213.7142474516326" y1="1083.065412077288" y2="1107.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7141474516325" x2="275.8224474516326" y1="1083.065412077288" y2="1083.065412077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7141474516325" x2="275.8224474516326" y1="1107.234012077288" y2="1107.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7141474516325" x2="213.7141474516325" y1="1083.065412077288" y2="1107.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="275.8224474516326" y1="1083.065412077288" y2="1107.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="337.9307474516324" y1="1083.065412077288" y2="1083.065412077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="337.9307474516324" y1="1107.234012077288" y2="1107.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="275.8224474516326" y1="1083.065412077288" y2="1107.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="337.9307474516324" x2="337.9307474516324" y1="1083.065412077288" y2="1107.234012077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.93664745163255" x2="88.28134745163254" y1="1107.234172077288" y2="1107.234172077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.93664745163255" x2="88.28134745163254" y1="1131.402772077288" y2="1131.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.93664745163255" x2="39.93664745163255" y1="1107.234172077288" y2="1131.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28134745163254" x2="88.28134745163254" y1="1107.234172077288" y2="1131.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28164745163247" x2="150.3899474516325" y1="1107.234172077288" y2="1107.234172077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28164745163247" x2="150.3899474516325" y1="1131.402772077288" y2="1131.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28164745163247" x2="88.28164745163247" y1="1107.234172077288" y2="1131.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3899474516325" x2="150.3899474516325" y1="1107.234172077288" y2="1131.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3894474516325" x2="213.7142474516326" y1="1107.234172077288" y2="1107.234172077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3894474516325" x2="213.7142474516326" y1="1131.402772077288" y2="1131.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3894474516325" x2="150.3894474516325" y1="1107.234172077288" y2="1131.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7142474516326" x2="213.7142474516326" y1="1107.234172077288" y2="1131.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7141474516325" x2="275.8224474516326" y1="1107.234172077288" y2="1107.234172077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7141474516325" x2="275.8224474516326" y1="1131.402772077288" y2="1131.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7141474516325" x2="213.7141474516325" y1="1107.234172077288" y2="1131.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="275.8224474516326" y1="1107.234172077288" y2="1131.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="337.9307474516324" y1="1107.234172077288" y2="1107.234172077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="337.9307474516324" y1="1131.402772077288" y2="1131.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="275.8224474516326" y1="1107.234172077288" y2="1131.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="337.9307474516324" x2="337.9307474516324" y1="1107.234172077288" y2="1131.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.93664745163255" x2="88.28134745163254" y1="1131.402772077288" y2="1131.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.93664745163255" x2="88.28134745163254" y1="1155.571372077288" y2="1155.571372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="39.93664745163255" x2="39.93664745163255" y1="1131.402772077288" y2="1155.571372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28134745163254" x2="88.28134745163254" y1="1131.402772077288" y2="1155.571372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28164745163247" x2="150.3899474516325" y1="1131.402772077288" y2="1131.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28164745163247" x2="150.3899474516325" y1="1155.571372077288" y2="1155.571372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="88.28164745163247" x2="88.28164745163247" y1="1131.402772077288" y2="1155.571372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3899474516325" x2="150.3899474516325" y1="1131.402772077288" y2="1155.571372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3894474516325" x2="213.7142474516326" y1="1131.402772077288" y2="1131.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3894474516325" x2="213.7142474516326" y1="1155.571372077288" y2="1155.571372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="150.3894474516325" x2="150.3894474516325" y1="1131.402772077288" y2="1155.571372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7142474516326" x2="213.7142474516326" y1="1131.402772077288" y2="1155.571372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7141474516325" x2="275.8224474516326" y1="1131.402772077288" y2="1131.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7141474516325" x2="275.8224474516326" y1="1155.571372077288" y2="1155.571372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="213.7141474516325" x2="213.7141474516325" y1="1131.402772077288" y2="1155.571372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="275.8224474516326" y1="1131.402772077288" y2="1155.571372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="337.9307474516324" y1="1131.402772077288" y2="1131.402772077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="337.9307474516324" y1="1155.571372077288" y2="1155.571372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.8224474516326" x2="275.8224474516326" y1="1131.402772077288" y2="1155.571372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="337.9307474516324" x2="337.9307474516324" y1="1131.402772077288" y2="1155.571372077288"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-11.28571428571433" x2="169.7142857142857" y1="715.5714285714284" y2="715.5714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-11.28571428571433" x2="169.7142857142857" y1="741.5714285714284" y2="741.5714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-11.28571428571433" x2="-11.28571428571433" y1="715.5714285714284" y2="741.5714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.7142857142857" x2="169.7142857142857" y1="715.5714285714284" y2="741.5714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.7142857142857" x2="350.7142857142857" y1="715.5714285714284" y2="715.5714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.7142857142857" x2="350.7142857142857" y1="741.5714285714284" y2="741.5714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.7142857142857" x2="169.7142857142857" y1="715.5714285714284" y2="741.5714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="350.7142857142857" x2="350.7142857142857" y1="715.5714285714284" y2="741.5714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-11.28571428571433" x2="169.7142857142857" y1="741.5714285714284" y2="741.5714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-11.28571428571433" x2="169.7142857142857" y1="765.8214285714284" y2="765.8214285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-11.28571428571433" x2="-11.28571428571433" y1="741.5714285714284" y2="765.8214285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.7142857142857" x2="169.7142857142857" y1="741.5714285714284" y2="765.8214285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.7142857142857" x2="350.7142857142857" y1="741.5714285714284" y2="741.5714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.7142857142857" x2="350.7142857142857" y1="765.8214285714284" y2="765.8214285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.7142857142857" x2="169.7142857142857" y1="741.5714285714284" y2="765.8214285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="350.7142857142857" x2="350.7142857142857" y1="741.5714285714284" y2="765.8214285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-11.28571428571433" x2="169.7142857142857" y1="765.8214285714284" y2="765.8214285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-11.28571428571433" x2="169.7142857142857" y1="788.5714285714284" y2="788.5714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-11.28571428571433" x2="-11.28571428571433" y1="765.8214285714284" y2="788.5714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.7142857142857" x2="169.7142857142857" y1="765.8214285714284" y2="788.5714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.7142857142857" x2="350.7142857142857" y1="765.8214285714284" y2="765.8214285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.7142857142857" x2="350.7142857142857" y1="788.5714285714284" y2="788.5714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.7142857142857" x2="169.7142857142857" y1="765.8214285714284" y2="788.5714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="350.7142857142857" x2="350.7142857142857" y1="765.8214285714284" y2="788.5714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-11.28571428571433" x2="169.7142857142857" y1="788.5714285714284" y2="788.5714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-11.28571428571433" x2="169.7142857142857" y1="811.3214285714284" y2="811.3214285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-11.28571428571433" x2="-11.28571428571433" y1="788.5714285714284" y2="811.3214285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.7142857142857" x2="169.7142857142857" y1="788.5714285714284" y2="811.3214285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.7142857142857" x2="350.7142857142857" y1="788.5714285714284" y2="788.5714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.7142857142857" x2="350.7142857142857" y1="811.3214285714284" y2="811.3214285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.7142857142857" x2="169.7142857142857" y1="788.5714285714284" y2="811.3214285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="350.7142857142857" x2="350.7142857142857" y1="788.5714285714284" y2="811.3214285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-11.28571428571433" x2="169.7142857142857" y1="811.3214285714284" y2="811.3214285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-11.28571428571433" x2="169.7142857142857" y1="834.0714285714284" y2="834.0714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="-11.28571428571433" x2="-11.28571428571433" y1="811.3214285714284" y2="834.0714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.7142857142857" x2="169.7142857142857" y1="811.3214285714284" y2="834.0714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.7142857142857" x2="350.7142857142857" y1="811.3214285714284" y2="811.3214285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.7142857142857" x2="350.7142857142857" y1="834.0714285714284" y2="834.0714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="169.7142857142857" x2="169.7142857142857" y1="811.3214285714284" y2="834.0714285714284"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="350.7142857142857" x2="350.7142857142857" y1="811.3214285714284" y2="834.0714285714284"/>
  <line fill="none" id="41" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="361.7142857142857" x2="361.7142857142857" y1="567.5714285714284" y2="1597.571428571428" zvalue="700"/>
  <line fill="none" id="39" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="-13.28571428571365" x2="354.7142857142859" y1="703.4419211855108" y2="703.4419211855108" zvalue="702"/>
  <line fill="none" id="38" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="-13.28571428571365" x2="354.7142857142859" y1="1173.441921185511" y2="1173.441921185511" zvalue="703"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="-12.28571428571433" x2="77.71428571428567" y1="1488.571428571428" y2="1488.571428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="-12.28571428571433" x2="77.71428571428567" y1="1527.734728571429" y2="1527.734728571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="-12.28571428571433" x2="-12.28571428571433" y1="1488.571428571428" y2="1527.734728571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="77.71428571428567" x2="77.71428571428567" y1="1488.571428571428" y2="1527.734728571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="77.71428571428567" x2="347.7142857142857" y1="1488.571428571428" y2="1488.571428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="77.71428571428567" x2="347.7142857142857" y1="1527.734728571429" y2="1527.734728571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="77.71428571428567" x2="77.71428571428567" y1="1488.571428571428" y2="1527.734728571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.7142857142857" x2="347.7142857142857" y1="1488.571428571428" y2="1527.734728571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="-12.28571428571433" x2="77.71428571428567" y1="1527.734698571428" y2="1527.734698571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="-12.28571428571433" x2="77.71428571428567" y1="1555.653098571428" y2="1555.653098571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="-12.28571428571433" x2="-12.28571428571433" y1="1527.734698571428" y2="1555.653098571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="77.71428571428567" x2="77.71428571428567" y1="1527.734698571428" y2="1555.653098571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="77.71428571428567" x2="167.7142857142857" y1="1527.734698571428" y2="1527.734698571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="77.71428571428567" x2="167.7142857142857" y1="1555.653098571428" y2="1555.653098571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="77.71428571428567" x2="77.71428571428567" y1="1527.734698571428" y2="1555.653098571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="167.7142857142857" x2="167.7142857142857" y1="1527.734698571428" y2="1555.653098571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="167.7142857142858" x2="257.7142857142858" y1="1527.734698571428" y2="1527.734698571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="167.7142857142858" x2="257.7142857142858" y1="1555.653098571428" y2="1555.653098571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="167.7142857142858" x2="167.7142857142858" y1="1527.734698571428" y2="1555.653098571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="257.7142857142858" x2="257.7142857142858" y1="1527.734698571428" y2="1555.653098571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="257.7142857142857" x2="347.7142857142857" y1="1527.734698571428" y2="1527.734698571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="257.7142857142857" x2="347.7142857142857" y1="1555.653098571428" y2="1555.653098571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="257.7142857142857" x2="257.7142857142857" y1="1527.734698571428" y2="1555.653098571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.7142857142857" x2="347.7142857142857" y1="1527.734698571428" y2="1555.653098571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="-12.28571428571433" x2="77.71428571428567" y1="1555.653028571428" y2="1555.653028571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="-12.28571428571433" x2="77.71428571428567" y1="1583.571428571428" y2="1583.571428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="-12.28571428571433" x2="-12.28571428571433" y1="1555.653028571428" y2="1583.571428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="77.71428571428567" x2="77.71428571428567" y1="1555.653028571428" y2="1583.571428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="77.71428571428567" x2="167.7142857142857" y1="1555.653028571428" y2="1555.653028571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="77.71428571428567" x2="167.7142857142857" y1="1583.571428571428" y2="1583.571428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="77.71428571428567" x2="77.71428571428567" y1="1555.653028571428" y2="1583.571428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="167.7142857142857" x2="167.7142857142857" y1="1555.653028571428" y2="1583.571428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="167.7142857142858" x2="257.7142857142858" y1="1555.653028571428" y2="1555.653028571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="167.7142857142858" x2="257.7142857142858" y1="1583.571428571428" y2="1583.571428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="167.7142857142858" x2="167.7142857142858" y1="1555.653028571428" y2="1583.571428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="257.7142857142858" x2="257.7142857142858" y1="1555.653028571428" y2="1583.571428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="257.7142857142857" x2="347.7142857142857" y1="1555.653028571428" y2="1555.653028571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="257.7142857142857" x2="347.7142857142857" y1="1583.571428571428" y2="1583.571428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="257.7142857142857" x2="257.7142857142857" y1="1555.653028571428" y2="1583.571428571428"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.7142857142857" x2="347.7142857142857" y1="1555.653028571428" y2="1583.571428571428"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,32.7143,1508.57) scale(1,1) translate(0,0)" writing-mode="lr" x="32.71" xml:space="preserve" y="1514.57" zvalue="706">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,29.7143,1542.57) scale(1,1) translate(0,0)" writing-mode="lr" x="29.71" xml:space="preserve" y="1548.57" zvalue="707">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,211.714,1542.57) scale(1,1) translate(0,0)" writing-mode="lr" x="211.71" xml:space="preserve" y="1548.57" zvalue="708">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,28.7143,1570.57) scale(1,1) translate(0,0)" writing-mode="lr" x="28.71" xml:space="preserve" y="1576.57" zvalue="709">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,210.714,1570.57) scale(1,1) translate(0,0)" writing-mode="lr" x="210.71" xml:space="preserve" y="1576.57" zvalue="710">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" x="115.71875" xml:space="preserve" y="1010.5625" zvalue="711">110kV  母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="115.71875" xml:space="preserve" y="1026.5625" zvalue="711">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.2143,1203.07) scale(1,1) translate(0,0)" writing-mode="lr" x="53.21428571428567" xml:space="preserve" y="1207.571428571428" zvalue="713">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,186.113,870.413) scale(1,1) translate(0,0)" writing-mode="lr" x="186.11" xml:space="preserve" y="874.91" zvalue="714">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,291.113,870.413) scale(1,1) translate(0,0)" writing-mode="lr" x="291.11" xml:space="preserve" y="874.91" zvalue="715">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,64.7143,1046.07) scale(1,1) translate(0,0)" writing-mode="lr" x="64.71428571428567" xml:space="preserve" y="1050.571428571428" zvalue="716">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,64.7143,1071.57) scale(1,1) translate(0,0)" writing-mode="lr" x="64.71428571428567" xml:space="preserve" y="1076.071428571428" zvalue="717">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,64.7143,1097.07) scale(1,1) translate(0,0)" writing-mode="lr" x="64.71428571428567" xml:space="preserve" y="1101.571428571428" zvalue="718">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63.7143,1121.57) scale(1,1) translate(0,0)" writing-mode="lr" x="63.71428571428567" xml:space="preserve" y="1126.071428571428" zvalue="719">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,64.7143,1148.07) scale(1,1) translate(0,0)" writing-mode="lr" x="64.71428571428567" xml:space="preserve" y="1152.571428571428" zvalue="720">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,212.768,1510.57) scale(1,1) translate(0,0)" writing-mode="lr" x="212.77" xml:space="preserve" y="1516.57" zvalue="721">MengNongHe2-01-2012</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,122.768,1542.57) scale(1,1) translate(0,0)" writing-mode="lr" x="122.77" xml:space="preserve" y="1548.57" zvalue="722"> 杨立超</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,302.768,1542.57) scale(1,1) translate(0,0)" writing-mode="lr" x="302.77" xml:space="preserve" y="1548.57" zvalue="723">20210222</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,26.7143,729.571) scale(1,1) translate(0,0)" writing-mode="lr" x="26.71" xml:space="preserve" y="735.0700000000001" zvalue="724">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,206.714,729.571) scale(1,1) translate(0,0)" writing-mode="lr" x="206.71" xml:space="preserve" y="735.0700000000001" zvalue="725">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,394.5,394.5) scale(1,1) translate(0,0)" writing-mode="lr" x="394.5" xml:space="preserve" y="399" zvalue="726">11</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,555,210) scale(1,1) translate(0,0)" writing-mode="lr" x="555" xml:space="preserve" y="214.5" zvalue="744">DA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,492.804,411.827) scale(1,1) translate(0,0)" writing-mode="lr" x="492.8" xml:space="preserve" y="416.33" zvalue="747">110</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,675,164) scale(1,1) translate(0,0)" writing-mode="lr" x="675" xml:space="preserve" y="168.5" zvalue="749">DA</text>
 </g>
 <g id="ClockClass">
  
  
 </g>
 <g id="PowerTransformer2Class">
  <g id="414">
   <g id="4140">
    <use class="kv110" height="50" transform="rotate(0,734.679,548.327) scale(2.15,1.85355) translate(-375.718,-231.163)" width="30" x="702.4299999999999" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="501.99" zvalue="578"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874589863938" ObjectName="110"/>
    </metadata>
   </g>
   <g id="4141">
    <use class="kv6" height="50" transform="rotate(0,734.679,548.327) scale(2.15,1.85355) translate(-375.718,-231.163)" width="30" x="702.4299999999999" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="501.99" zvalue="578"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874423730180" ObjectName="6"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399532216322" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399532216322"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,734.679,548.327) scale(2.15,1.85355) translate(-375.718,-231.163)" width="30" x="702.4299999999999" y="501.99"/></g>
  <g id="47">
   <g id="470">
    <use class="kv110" height="50" transform="rotate(0,572.679,410.327) scale(2.15,1.85355) translate(-289.066,-167.615)" width="30" x="540.4299999999999" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="363.99" zvalue="746"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="110"/>
    </metadata>
   </g>
   <g id="471">
    <use class="kv6" height="50" transform="rotate(0,572.679,410.327) scale(2.15,1.85355) translate(-289.066,-167.615)" width="30" x="540.4299999999999" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="363.99" zvalue="746"/>
    <metadata>
     <cge:PSR_Ref ObjectID="" ObjectName="6"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变"/>
   </metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,572.679,410.327) scale(2.15,1.85355) translate(-289.066,-167.615)" width="30" x="540.4299999999999" y="363.99"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="213">
   <path class="kv110" d="M 734.75 455.12 L 734.75 502.83" stroke-width="1" zvalue="580"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="414@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 734.75 455.12 L 734.75 502.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="kv110" d="M 878.75 529.11 L 734.71 529.11" stroke-width="1" zvalue="589"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="457@0" LinkObjectIDznd="414@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 878.75 529.11 L 734.71 529.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv6" d="M 734.68 700.75 L 734.68 727.04" stroke-width="1" zvalue="592"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="453@1" LinkObjectIDznd="410@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 734.68 700.75 L 734.68 727.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv6" d="M 731.48 727.04 L 731.48 774.56" stroke-width="1" zvalue="611"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="410@3" LinkObjectIDznd="198@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 731.48 727.04 L 731.48 774.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv6" d="M 734.68 650.56 L 734.68 594.01" stroke-width="1" zvalue="613"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="453@0" LinkObjectIDznd="414@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 734.68 650.56 L 734.68 594.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv6" d="M 857.81 797.69 L 857.81 805.67" stroke-width="1" zvalue="623"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@0" LinkObjectIDznd="191@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 857.81 797.69 L 857.81 805.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv6" d="M 911.67 971.83 L 911.67 987.6" stroke-width="1" zvalue="628"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@1" LinkObjectIDznd="184@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 911.67 971.83 L 911.67 987.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv6" d="M 1191.73 727.04 L 1191.73 775.81" stroke-width="1" zvalue="636"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="410@2" LinkObjectIDznd="181@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1191.73 727.04 L 1191.73 775.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv6" d="M 1191.73 826 L 1191.73 980.25" stroke-width="1" zvalue="637"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@1" LinkObjectIDznd="182@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1191.73 826 L 1191.73 980.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv6" d="M 1316.06 798.94 L 1316.06 809.92" stroke-width="1" zvalue="645"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@0" LinkObjectIDznd="176@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1316.06 798.94 L 1316.06 809.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv6" d="M 1300.31 542.69 L 1300.31 569.17" stroke-width="1" zvalue="656"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@0" LinkObjectIDznd="168@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1300.31 542.69 L 1300.31 569.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv6" d="M 1300.21 600.25 L 1300.21 727.04" stroke-width="1" zvalue="657"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="168@1" LinkObjectIDznd="410@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1300.21 600.25 L 1300.21 727.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="164">
   <path class="kv6" d="M 1230.06 612.5 L 1230.06 643.5 L 1300.21 643.5" stroke-width="1" zvalue="659"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="166" MaxPinNum="2"/>
   </metadata>
  <path d="M 1230.06 612.5 L 1230.06 643.5 L 1300.21 643.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv6" d="M 1642.95 727.04 L 1642.95 769.7" stroke-width="1" zvalue="667"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="410@0" LinkObjectIDznd="163@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1642.95 727.04 L 1642.95 769.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv6" d="M 1642.95 819.89 L 1642.95 936.32" stroke-width="1" zvalue="668"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="163@1" LinkObjectIDznd="159@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1642.95 819.89 L 1642.95 936.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv6" d="M 731.48 824.75 L 731.48 979" stroke-width="1" zvalue="676"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@1" LinkObjectIDznd="212@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 731.48 824.75 L 731.48 979" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv6" d="M 621.66 865.96 L 731.48 865.96" stroke-width="1" zvalue="677"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@0" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 621.66 865.96 L 731.48 865.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv6" d="M 653.06 892.5 L 653.06 865.96" stroke-width="1" zvalue="678"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@0" LinkObjectIDznd="126" MaxPinNum="2"/>
   </metadata>
  <path d="M 653.06 892.5 L 653.06 865.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv6" d="M 858.06 934.5 L 858.06 868 L 731.48 868" stroke-width="1" zvalue="679"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189@0" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 858.06 934.5 L 858.06 868 L 731.48 868" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv6" d="M 911.67 943.5 L 911.67 910 L 858.06 910" stroke-width="1" zvalue="680"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="122" MaxPinNum="2"/>
   </metadata>
  <path d="M 911.67 943.5 L 911.67 910 L 858.06 910" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv6" d="M 807.02 928.96 L 807.02 910 L 858.06 910" stroke-width="1" zvalue="681"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@0" LinkObjectIDznd="124" MaxPinNum="2"/>
   </metadata>
  <path d="M 807.02 928.96 L 807.02 910 L 858.06 910" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv6" d="M 858.71 836.75 L 858.71 868" stroke-width="1" zvalue="682"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@1" LinkObjectIDznd="124" MaxPinNum="2"/>
   </metadata>
  <path d="M 858.71 836.75 L 858.71 868" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv6" d="M 1369.5 988.85 L 1369.5 973.08" stroke-width="1" zvalue="683"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="171@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1369.5 988.85 L 1369.5 973.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv6" d="M 1079.91 867.21 L 1191.73 867.21" stroke-width="1" zvalue="684"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 1079.91 867.21 L 1191.73 867.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv6" d="M 1111.31 893.75 L 1111.31 867.21" stroke-width="1" zvalue="685"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="177@0" LinkObjectIDznd="119" MaxPinNum="2"/>
   </metadata>
  <path d="M 1111.31 893.75 L 1111.31 867.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv6" d="M 1315.96 935.75 L 1315.96 867.69 L 1191.73 867.69" stroke-width="1" zvalue="686"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 1315.96 935.75 L 1315.96 867.69 L 1191.73 867.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv6" d="M 1369.92 944.75 L 1369.92 905.38 L 1315.96 905.38" stroke-width="1" zvalue="687"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="109" MaxPinNum="2"/>
   </metadata>
  <path d="M 1369.92 944.75 L 1369.92 905.38 L 1315.96 905.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="kv6" d="M 1265.27 930.21 L 1265.27 906.15 L 1315.96 906.15" stroke-width="1" zvalue="688"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@0" LinkObjectIDznd="109" MaxPinNum="2"/>
   </metadata>
  <path d="M 1265.27 930.21 L 1265.27 906.15 L 1315.96 906.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv6" d="M 1315.96 841 L 1315.96 867.69" stroke-width="1" zvalue="689"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@1" LinkObjectIDznd="109" MaxPinNum="2"/>
   </metadata>
  <path d="M 1315.96 841 L 1315.96 867.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv6" d="M 1536.68 878.31 L 1536.68 854.62 L 1642.95 854.62" stroke-width="1" zvalue="690"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="162@0" LinkObjectIDznd="157" MaxPinNum="2"/>
   </metadata>
  <path d="M 1536.68 878.31 L 1536.68 854.62 L 1642.95 854.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv6" d="M 1581.31 878.19 L 1581.31 854.62" stroke-width="1" zvalue="691"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="105" MaxPinNum="2"/>
   </metadata>
  <path d="M 1581.31 878.19 L 1581.31 854.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv6" d="M 1737.95 888.93 L 1737.95 860.77 L 1642.95 860.77" stroke-width="1" zvalue="692"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160@0" LinkObjectIDznd="157" MaxPinNum="2"/>
   </metadata>
  <path d="M 1737.95 888.93 L 1737.95 860.77 L 1642.95 860.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv6" d="M 926.7 632.26 L 926.7 603.08 L 734.68 603.08" stroke-width="1" zvalue="693"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@0" LinkObjectIDznd="196" MaxPinNum="2"/>
   </metadata>
  <path d="M 926.7 632.26 L 926.7 603.08 L 734.68 603.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv6" d="M 893.72 625.17 L 893.72 603.08" stroke-width="1" zvalue="694"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@0" LinkObjectIDznd="102" MaxPinNum="2"/>
   </metadata>
  <path d="M 893.72 625.17 L 893.72 603.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="410">
   <path class="kv6" d="M 568.62 727.04 L 1801.62 727.04" stroke-width="6" zvalue="581"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674235645957" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674235645957"/></metadata>
  <path d="M 568.62 727.04 L 1801.62 727.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="212">
   <use class="kv6" height="30" transform="rotate(0,731.475,1006.42) scale(1.85899,1.85899) translate(-325.11,-452.156)" width="30" x="703.5903611791394" xlink:href="#Generator:发电机_0" y="978.5367361713461" zvalue="583"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449608613894" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449608613894"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,731.475,1006.42) scale(1.85899,1.85899) translate(-325.11,-452.156)" width="30" x="703.5903611791394" y="978.5367361713461"/></g>
  <g id="182">
   <use class="kv6" height="30" transform="rotate(0,1191.73,1007.67) scale(1.85899,1.85899) translate(-537.78,-452.734)" width="30" x="1163.84036117914" xlink:href="#Generator:发电机_0" y="979.7867361713461" zvalue="632"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449606975494" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449606975494"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1191.73,1007.67) scale(1.85899,1.85899) translate(-537.78,-452.734)" width="30" x="1163.84036117914" y="979.7867361713461"/></g>
 </g>
 <g id="BreakerClass">
  <g id="453">
   <use class="kv6" height="20" transform="rotate(0,734.679,676) scale(2.75,2.75) translate(-458.773,-412.682)" width="10" x="720.9285714285716" xlink:href="#Breaker:小车断路器_0" y="648.5" zvalue="590"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924481056773" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924481056773"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,734.679,676) scale(2.75,2.75) translate(-458.773,-412.682)" width="10" x="720.9285714285716" y="648.5"/></g>
  <g id="198">
   <use class="kv6" height="20" transform="rotate(0,731.475,800) scale(2.75,2.75) translate(-456.734,-491.591)" width="10" x="717.7252143314927" xlink:href="#Breaker:小车断路器_0" y="772.5" zvalue="610"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924480991237" ObjectName="#1发电机631断路器"/>
   <cge:TPSR_Ref TObjectID="6473924480991237"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,731.475,800) scale(2.75,2.75) translate(-456.734,-491.591)" width="10" x="717.7252143314927" y="772.5"/></g>
  <g id="181">
   <use class="kv6" height="20" transform="rotate(0,1191.73,801.25) scale(2.75,2.75) translate(-749.621,-492.386)" width="10" x="1177.975214331493" xlink:href="#Breaker:小车断路器_0" y="773.75" zvalue="634"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924480925701" ObjectName="#2发电机632断路器"/>
   <cge:TPSR_Ref TObjectID="6473924480925701"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1191.73,801.25) scale(2.75,2.75) translate(-749.621,-492.386)" width="10" x="1177.975214331493" y="773.75"/></g>
  <g id="163">
   <use class="kv6" height="20" transform="rotate(0,1642.95,795.139) scale(2.75,2.75) translate(-1036.76,-488.497)" width="10" x="1629.197436553715" xlink:href="#Breaker:小车断路器_0" y="767.6388888888889" zvalue="660"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924480860165" ObjectName="#1站用变633断路器"/>
   <cge:TPSR_Ref TObjectID="6473924480860165"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1642.95,795.139) scale(2.75,2.75) translate(-1036.76,-488.497)" width="10" x="1629.197436553715" y="767.6388888888889"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="457">
   <use class="kv110" height="45" transform="rotate(0,888.75,549.111) scale(1.25,-1.25) translate(-172.125,-982.775)" width="45" x="860.625" xlink:href="#GroundDisconnector:12547_0" y="520.9863244035436" zvalue="593"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   </metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,888.75,549.111) scale(1.25,-1.25) translate(-172.125,-982.775)" width="45" x="860.625" y="520.9863244035436"/></g>
  <g id="162">
   <use class="kv6" height="30" transform="rotate(180,1536.67,894.222) scale(1.11111,-1.11111) translate(-153.111,-1697.36)" width="10" x="1531.111111111111" xlink:href="#GroundDisconnector:配网地刀_0" y="877.5555555555554" zvalue="662"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449606189062" ObjectName="#1站用变63367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449606189062"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1536.67,894.222) scale(1.11111,-1.11111) translate(-153.111,-1697.36)" width="10" x="1531.111111111111" y="877.5555555555554"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="195">
   <use class="kv6" height="20" transform="rotate(0,893.722,642.667) scale(1.94444,1.94444) translate(-424.649,-302.708)" width="20" x="874.2777777777778" xlink:href="#Accessory:带电显示器_0" y="623.2222222222223" zvalue="614"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449607696390" ObjectName="#1主变显示器1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,893.722,642.667) scale(1.94444,1.94444) translate(-424.649,-302.708)" width="20" x="874.2777777777778" y="623.2222222222223"/></g>
  <g id="194">
   <use class="kv6" height="26" transform="rotate(0,926.667,646) scale(1.11111,1.11111) translate(-92,-63.1556)" width="12" x="920.0000000000001" xlink:href="#Accessory:避雷器1_0" y="631.5555555555555" zvalue="615"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449607630854" ObjectName="#1主变6kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,926.667,646) scale(1.11111,1.11111) translate(-92,-63.1556)" width="12" x="920.0000000000001" y="631.5555555555555"/></g>
  <g id="193">
   <use class="kv6" height="26" transform="rotate(90,607.917,866) scale(-1.11111,1.11111) translate(-1154.38,-85.1556)" width="12" x="601.25" xlink:href="#Accessory:避雷器1_0" y="851.5555555555555" zvalue="616"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449607565318" ObjectName="#1发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,607.917,866) scale(-1.11111,1.11111) translate(-1154.38,-85.1556)" width="12" x="601.25" y="851.5555555555555"/></g>
  <g id="192">
   <use class="kv6" height="20" transform="rotate(0,653.056,910) scale(1.94444,1.94444) translate(-307.754,-432.556)" width="20" x="633.6111111111111" xlink:href="#Accessory:带电显示器_0" y="890.5555555555557" zvalue="617"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449607499782" ObjectName="#1发电机显示器1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,653.056,910) scale(1.94444,1.94444) translate(-307.754,-432.556)" width="20" x="633.6111111111111" y="890.5555555555557"/></g>
  <g id="190">
   <use class="kv6" height="30" transform="rotate(0,857.889,775.444) scale(1.51852,-1.51852) translate(-285.16,-1278.33)" width="30" x="835.1111111111112" xlink:href="#Accessory:PT789_0" y="752.6666666666665" zvalue="620"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449607368710" ObjectName="#1发电机机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,857.889,775.444) scale(1.51852,-1.51852) translate(-285.16,-1278.33)" width="30" x="835.1111111111112" y="752.6666666666665"/></g>
  <g id="189">
   <use class="kv6" height="20" transform="rotate(0,858.056,952) scale(1.94444,1.94444) translate(-407.325,-452.956)" width="20" x="838.6111111111111" xlink:href="#Accessory:带电显示器_0" y="932.5555555555557" zvalue="622"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449607303174" ObjectName="#1发电机显示器2"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,858.056,952) scale(1.94444,1.94444) translate(-407.325,-452.956)" width="20" x="838.6111111111111" y="932.5555555555557"/></g>
  <g id="187">
   <use class="kv6" height="30" transform="rotate(0,807.083,948.083) scale(1.30556,1.30556) translate(-184.309,-217.309)" width="30" x="787.5" xlink:href="#Accessory:PT2525_0" y="928.5000000000001" zvalue="624"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449607237638" ObjectName="#1发电机励磁PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,807.083,948.083) scale(1.30556,1.30556) translate(-184.309,-217.309)" width="30" x="787.5" y="928.5000000000001"/></g>
  <g id="184">
   <use class="kv6" height="29" transform="rotate(0,911.25,1007.79) scale(1.41667,-1.41667) translate(-261.765,-1713.13)" width="30" x="890" xlink:href="#Accessory:厂用变2020_0" y="987.2499999999999" zvalue="629"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449607106566" ObjectName="#1发电机励磁变"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,911.25,1007.79) scale(1.41667,-1.41667) translate(-261.765,-1713.13)" width="30" x="890" y="987.2499999999999"/></g>
  <g id="178">
   <use class="kv6" height="26" transform="rotate(90,1066.17,867.25) scale(-1.11111,1.11111) translate(-2025.05,-85.2806)" width="12" x="1059.5" xlink:href="#Accessory:避雷器1_0" y="852.8055555555555" zvalue="638"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449606909958" ObjectName="#2发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1066.17,867.25) scale(-1.11111,1.11111) translate(-2025.05,-85.2806)" width="12" x="1059.5" y="852.8055555555555"/></g>
  <g id="177">
   <use class="kv6" height="20" transform="rotate(0,1111.31,911.25) scale(1.94444,1.94444) translate(-530.333,-433.163)" width="20" x="1091.861111111111" xlink:href="#Accessory:带电显示器_0" y="891.8055555555557" zvalue="639"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449606844422" ObjectName="#2发电机显示器1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1111.31,911.25) scale(1.94444,1.94444) translate(-530.333,-433.163)" width="20" x="1091.861111111111" y="891.8055555555557"/></g>
  <g id="175">
   <use class="kv6" height="30" transform="rotate(0,1316.14,776.694) scale(1.51852,-1.51852) translate(-441.636,-1280.4)" width="30" x="1293.361111111111" xlink:href="#Accessory:PT789_0" y="753.9166666666665" zvalue="642"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449606713350" ObjectName="#2发电机机组PT电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1316.14,776.694) scale(1.51852,-1.51852) translate(-441.636,-1280.4)" width="30" x="1293.361111111111" y="753.9166666666665"/></g>
  <g id="174">
   <use class="kv6" height="20" transform="rotate(0,1315.96,953.25) scale(1.94444,1.94444) translate(-629.739,-453.563)" width="20" x="1296.520479919691" xlink:href="#Accessory:带电显示器_0" y="933.8055555555557" zvalue="644"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449606647814" ObjectName="#2发电机显示器2"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1315.96,953.25) scale(1.94444,1.94444) translate(-629.739,-453.563)" width="20" x="1296.520479919691" y="933.8055555555557"/></g>
  <g id="172">
   <use class="kv6" height="30" transform="rotate(0,1265.33,949.333) scale(1.30556,1.30556) translate(-291.559,-217.601)" width="30" x="1245.75" xlink:href="#Accessory:PT2525_0" y="929.7500000000001" zvalue="646"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449606582278" ObjectName="#2发电机励磁PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1265.33,949.333) scale(1.30556,1.30556) translate(-291.559,-217.601)" width="30" x="1245.75" y="929.7500000000001"/></g>
  <g id="170">
   <use class="kv6" height="29" transform="rotate(0,1369.5,1009.04) scale(1.41667,-1.41667) translate(-396.544,-1715.26)" width="30" x="1348.25" xlink:href="#Accessory:厂用变2020_0" y="988.4999999999999" zvalue="650"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449606451206" ObjectName="#2发电机励磁变"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1369.5,1009.04) scale(1.41667,-1.41667) translate(-396.544,-1715.26)" width="30" x="1348.25" y="988.4999999999999"/></g>
  <g id="169">
   <use class="kv6" height="30" transform="rotate(0,1300.39,520.444) scale(1.51852,-1.51852) translate(-436.257,-855.398)" width="30" x="1277.611111111111" xlink:href="#Accessory:PT789_0" y="497.6666666666665" zvalue="652"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449606385670" ObjectName="6.3kV母线PT电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1300.39,520.444) scale(1.51852,-1.51852) translate(-436.257,-855.398)" width="30" x="1277.611111111111" y="497.6666666666665"/></g>
  <g id="165">
   <use class="kv6" height="20" transform="rotate(0,1230.06,595) scale(1.94444,-1.94444) translate(-588.011,-891.556)" width="20" x="1210.611111111111" xlink:href="#Accessory:带电显示器_0" y="575.5555555555557" zvalue="658"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449606254598" ObjectName="母线PT电压互感器显示器1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1230.06,595) scale(1.94444,-1.94444) translate(-588.011,-891.556)" width="20" x="1210.611111111111" y="575.5555555555557"/></g>
  <g id="161">
   <use class="kv6" height="20" transform="rotate(0,1581.31,895.694) scale(1.94444,1.94444) translate(-758.618,-425.607)" width="20" x="1561.861111111111" xlink:href="#Accessory:带电显示器_0" y="876.2500000000002" zvalue="664"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449606057990" ObjectName="#1站用变显示器1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1581.31,895.694) scale(1.94444,1.94444) translate(-758.618,-425.607)" width="20" x="1561.861111111111" y="876.2500000000002"/></g>
  <g id="160">
   <use class="kv6" height="26" transform="rotate(0,1737.92,902.667) scale(1.11111,1.11111) translate(-173.125,-88.8222)" width="12" x="1731.25" xlink:href="#Accessory:避雷器1_0" y="888.2222222222222" zvalue="665"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449605992454" ObjectName="#1站用变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1737.92,902.667) scale(1.11111,1.11111) translate(-173.125,-88.8222)" width="12" x="1731.25" y="888.2222222222222"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="191">
   <use class="kv6" height="26" transform="rotate(0,858.712,821.239) scale(1.2003,-1.2003) translate(-142.094,-1502.83)" width="12" x="851.5102735786276" xlink:href="#Disconnector:手车刀闸2020_0" y="805.6349206349206" zvalue="618"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449607434246" ObjectName="#1发电机6911刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449607434246"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,858.712,821.239) scale(1.2003,-1.2003) translate(-142.094,-1502.83)" width="12" x="851.5102735786276" y="805.6349206349206"/></g>
  <g id="186">
   <use class="kv6" height="36" transform="rotate(0,911.667,957.667) scale(0.833333,0.833333) translate(181.167,188.533)" width="14" x="905.8333333333333" xlink:href="#Disconnector:手车刀闸_0" y="942.6666666666665" zvalue="626"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449607172102" ObjectName="#1发电机励磁变6912刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449607172102"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,911.667,957.667) scale(0.833333,0.833333) translate(181.167,188.533)" width="14" x="905.8333333333333" y="942.6666666666665"/></g>
  <g id="176">
   <use class="kv6" height="26" transform="rotate(0,1315.96,825.489) scale(1.2003,-1.2003) translate(-218.397,-1510.62)" width="12" x="1308.760273578627" xlink:href="#Disconnector:手车刀闸2020_0" y="809.8849206349206" zvalue="640"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449606778886" ObjectName="#2发电机6921刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449606778886"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1315.96,825.489) scale(1.2003,-1.2003) translate(-218.397,-1510.62)" width="12" x="1308.760273578627" y="809.8849206349206"/></g>
  <g id="171">
   <use class="kv6" height="36" transform="rotate(0,1369.92,958.917) scale(0.833333,0.833333) translate(272.817,188.783)" width="14" x="1364.083333333333" xlink:href="#Disconnector:手车刀闸_0" y="943.9166666666665" zvalue="648"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449606516742" ObjectName="#2发电机励磁变6922刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449606516742"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1369.92,958.917) scale(0.833333,0.833333) translate(272.817,188.783)" width="14" x="1364.083333333333" y="943.9166666666665"/></g>
  <g id="168">
   <use class="kv6" height="26" transform="rotate(0,1300.21,584.739) scale(1.2003,-1.2003) translate(-215.769,-1069.3)" width="12" x="1293.010273578627" xlink:href="#Disconnector:手车刀闸2020_0" y="569.1349206349206" zvalue="654"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449606320134" ObjectName="母线PT6901刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449606320134"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1300.21,584.739) scale(1.2003,-1.2003) translate(-215.769,-1069.3)" width="12" x="1293.010273578627" y="569.1349206349206"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="159">
   <use class="kv6" height="30" transform="rotate(0,1643.79,960.925) scale(1.69643,1.70833) translate(-665.071,-387.807)" width="28" x="1620.044243907063" xlink:href="#EnergyConsumer:站用变DY接地_0" y="935.2996031746031" zvalue="666"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449605926918" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1643.79,960.925) scale(1.69643,1.70833) translate(-665.071,-387.807)" width="28" x="1620.044243907063" y="935.2996031746031"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="49">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="16" id="49" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1059,203) scale(1,1) translate(0,0)" writing-mode="lr" x="1059.15" xml:space="preserve" y="209.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123731079175" ObjectName=""/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="16" id="52" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1139,203) scale(1,1) translate(0,0)" writing-mode="lr" x="1139.15" xml:space="preserve" y="209.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123731144711" ObjectName=""/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,255)" font-family="SimSun" font-size="16" id="53" stroke="rgb(85,255,255)" text-anchor="middle" transform="rotate(0,1219,203) scale(1,1) translate(0,0)" writing-mode="lr" x="1219.15" xml:space="preserve" y="209.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123731275783" ObjectName=""/>
   </metadata>
  </g>
  <g id="51">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="21" id="51" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1399.54,174.054) scale(1,1) translate(0,0)" writing-mode="lr" x="1399.99" xml:space="preserve" y="181" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123897802756" ObjectName="P"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="57">
   <use height="30" transform="rotate(0,452,276) scale(1,1) translate(0,0)" width="30" x="437" xlink:href="#State:红绿圆_0" y="261" zvalue="750"/>
   <metadata>
    <cge:Meas_Ref ObjectID="15762599129972741" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,452,276) scale(1,1) translate(0,0)" width="30" x="437" y="261"/></g>
  <g id="58">
   <use height="30" transform="rotate(0,451,334) scale(1,1) translate(0,0)" width="30" x="436" xlink:href="#State:红绿圆_0" y="319" zvalue="752"/>
   <metadata>
    <cge:Meas_Ref ObjectID="15762599130038278" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,451,334) scale(1,1) translate(0,0)" width="30" x="436" y="319"/></g>
 </g>
</svg>