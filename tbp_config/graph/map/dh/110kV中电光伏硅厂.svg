<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549590556674" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="PowerTransformer2:接地可调两卷变_0" viewBox="0,0,22,30">
   <use terminal-index="0" type="1" x="11.00731595793324" xlink:href="#terminal" y="1.08736282578875"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.02194787379973" x2="6.955871323769093" y1="7.640146319158664" y2="3.944602328551218"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.23333333333333" x2="11.01463191586648" y1="3.694602328551216" y2="7.640146319158664"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.00935070873343" x2="11.00935070873343" y1="7.654778235025146" y2="11.68728637061797"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="21.53783721993598" x2="20.53783721993598" y1="2.175582990397805" y2="5.175582990397805"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.5394375857338822" x2="21.53943758573388" y1="13.09064929126657" y2="2.173982624599901"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="18.53052126200274" x2="21.53052126200274" y1="1.175582990397805" y2="2.175582990397805"/>
   <ellipse cx="11.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="11" xlink:href="#terminal" y="7.7"/>
  </symbol>
  <symbol id="PowerTransformer2:接地可调两卷变_1" viewBox="0,0,22,30">
   <use terminal-index="1" type="1" x="11" xlink:href="#terminal" y="29.05121170553269"/>
   <path d="M 6.75 25.8333 L 15.8333 25.8333 L 11.0833 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:电炉_0" viewBox="0,0,27,14">
   <use terminal-index="0" type="0" x="13.5" xlink:href="#terminal" y="0.25"/>
   <rect fill-opacity="0" height="10.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.54,5.54) scale(1,1) translate(0,0)" width="3.08" x="12" y="0.25"/>
   <path d="M 0.833333 8.25 A 12.5 5 0 0 0 25.8333 8.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1" x2="25.75" y1="8.25" y2="8.25"/>
  </symbol>
  <symbol id="Compensator:无功补偿20210816_0" viewBox="0,0,12,13">
   <use terminal-index="0" type="0" x="6.1" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.4" x2="4" y1="3.141666666666663" y2="3.141666666666663"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.141666666666663" x2="6.141666666666663" y1="0.04999999999999893" y2="3.141666666666661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.141666666666663" x2="6.141666666666663" y1="5.15" y2="8.09166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.629535048371164" x2="2.363367518766924" y1="11.72804518360739" y2="7.956509060518093"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.02953748863437" x2="7.38155138676536" y1="8.002747606646338" y2="11.51674385085443"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.116482967203315" x2="3.466407395865956" y1="8.199949647924862" y2="9.792275696188446"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.4" x2="4" y1="5.141666666666663" y2="5.141666666666663"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.201323776119781" x2="8.670438561349325" y1="7.945721353591398" y2="9.806332800169811"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.915200446966938" x2="0.6490329173626979" y1="12.7581213334275" y2="8.986585210338204"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.62680850872896" x2="8.978822406859944" y1="9.206377652950431" y2="12.72037389715852"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:熔断器12_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1.916666666666668" y2="18.25"/>
   <rect fill-opacity="0" height="16.08" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.06,10.06) scale(1,1) translate(0,0)" width="9.58" x="0.27" y="2.02"/>
  </symbol>
  <symbol id="Compensator:遮放35kV电容_0" viewBox="0,0,60,40">
   <use terminal-index="0" type="0" x="0.6873530692755132" xlink:href="#terminal" y="23.15315325549626"/>
   <path d="M 20.4238 13.4601 L 20.4238 23.2699 L 30.2336 23.2699" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 22.0587 7.73774 L 3.84051 7.73774 L 3.84051 23.1532" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.96429701311517" x2="1.154486884904586" y1="23.15315325549626" y2="23.15315325549626"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.05839172695363" x2="27.25558883317917" y1="7.796129066690407" y2="7.796129066690407"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.45979317384086" x2="31.45979317384086" y1="9.197530513577634" y2="6.394727619803181"/>
   <rect fill-opacity="0" height="12.38" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,20.95,7.8) scale(1,1) translate(0,0)" width="5.61" x="18.15" y="1.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.11711686125934" x2="17.91291252059766" y1="7.796129066690407" y2="9.197530513577634"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.05839172695363" x2="30.05839172695363" y1="9.898231237021246" y2="5.694026896359567"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.86119462072809" x2="32.86119462072809" y1="8.49682979013402" y2="7.095428343246795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38.11645004655519" x2="56.33466885608912" y1="37.4591263591367" y2="37.4591263591367"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.11711686125934" x2="17.91291252059766" y1="7.796129066690407" y2="6.394727619803181"/>
   <rect fill-opacity="0" height="5.61" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,40.34,23.33) scale(1,1) translate(0,0)" width="2.8" x="38.93" y="20.53"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="56.13613698444676" x2="56.13613698444676" y1="23.44511189026444" y2="31.73673711768053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="59.17250678603577" x2="59.17250678603577" y1="12.43243218680899" y2="34.59899781143184"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.90257007663224" x2="59.37103865767813" y1="23.29329340018499" y2="23.29329340018499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="57.26893648734728" x2="59.28929023994304" y1="12.31564873290172" y2="12.31564873290172"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="59.28929023994304" x2="57.38571994125455" y1="34.48221435752456" y2="34.48221435752456"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="53.0219115469196" x2="56.16208886309282" y1="31.75360583880046" y2="31.75360583880046"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.76476491941656" x2="34.76476491941656" y1="31.73673711768053" y2="23.29329340018499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.23356690781454" x2="49.82983047345427" y1="23.29329340018499" y2="23.29329340018499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="37.91791817491283" x2="34.82964461603171" y1="31.70170208150834" y2="31.70170208150834"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="51.23123192034149" x2="56.16208886309283" y1="23.29329340018499" y2="23.29329340018499"/>
   <path d="M 42.9614 31.7017 A 4.15364 2.547 -90 0 1 37.8674 31.7017" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 48.0554 31.7017 A 4.15364 2.547 -90 0 1 42.9614 31.7017" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 53.0092 31.7017 A 4.15364 2.547 -90 0 1 47.9152 31.7017" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="51.23123192034147" x2="51.23123192034147" y1="21.19119122985414" y2="25.39539557051582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="49.82983047345425" x2="49.82983047345425" y1="21.19119122985414" y2="25.39539557051582"/>
   <path d="M 20.5873 13.1682 A 9.58792 9.95831 -360 1 1 10.9993 23.1265" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:四卷不带避雷器_0" viewBox="0,0,25,25">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="23.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5" x2="20.5" y1="19.5" y2="13.83333333333333"/>
   <rect fill-opacity="0" height="7.92" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,20.5,10.04) scale(1,1) translate(0,0)" width="3" x="19" y="6.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.16666666666667" x2="20.58333333333334" y1="19.58333333333334" y2="19.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.58333333333334" x2="20.58333333333334" y1="6" y2="3"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.08333333333334" x2="22.08333333333334" y1="2.916666666666664" y2="2.916666666666664"/>
   <path d="M 17.5 4.5 L 17.5 7.5 L 23.5 11.5 L 23.5 14.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="21.58333333333334" y1="1.916666666666664" y2="1.916666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.08333333333334" x2="21.08333333333334" y1="0.9166666666666643" y2="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.45024559666368" x2="13.45024559666368" y1="17.52740325661302" y2="18.52740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.45024559666368" x2="11.45024559666368" y1="21.52740325661302" y2="17.52740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.45024559666368" x2="13.45024559666368" y1="21.52740325661302" y2="20.52740325661302"/>
   <ellipse cx="12.06" cy="12.91" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.03240159599324" x2="9.632401595993242" y1="13.12329629003648" y2="14.36083627438858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.03240159599324" x2="12.03240159599324" y1="10.64821632133226" y2="13.12329629003646"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.03240159599325" x2="14.43240159599324" y1="13.12329629003648" y2="14.36083627438858"/>
   <ellipse cx="5.56" cy="19.58" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.64" cy="12.91" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.615734929326575" x2="5.615734929326575" y1="10.64821632133226" y2="13.12329629003647"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.532401595993235" x2="3.132401595993244" y1="19.78996295670315" y2="21.02750294105526"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.615734929326578" x2="8.015734929326571" y1="13.12329629003649" y2="14.36083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.53240159599324" x2="5.53240159599324" y1="17.31488298799893" y2="19.78996295670313"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.532401595993244" x2="7.932401595993237" y1="19.78996295670315" y2="21.02750294105526"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.615734929326567" x2="3.215734929326572" y1="13.12329629003649" y2="14.36083627438859"/>
   <ellipse cx="11.98" cy="19.58" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷带壁雷器_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="28.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11.75" y1="17.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="14.75" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="12" y1="11.75" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="9" y1="12.75" y2="12.75"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.04,18.25) scale(1,1) translate(0,0)" width="3.25" x="9.42" y="14.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.5" y1="10.83333333333333" y2="10.83333333333333"/>
   <path d="M 5.025 2.775 L 4.16667 9.5 L 5.025 7.60833 L 5.91667 9.5 L 5.025 2.775" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 5 23.75 L 11 23.75 L 11 17.75 L 10.25 20.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.000411522633746" x2="5.000411522633746" y1="7.627914951989029" y2="28.23902606310013"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变D-Y_0" viewBox="0,0,23,39">
   <use terminal-index="0" type="0" x="7.666666666666666" xlink:href="#terminal" y="0.4166666666666714"/>
   <path d="M 7.5 4.25 L 4.58333 10.1667 L 10.8333 10.1667 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.42034 32.6694 L 7.64848 37.3333 L 7.05673 32.6694 L 7.72567 32.7212 z" fill="rgb(170,170,127)" fill-opacity="1" stroke="rgb(170,170,127)" stroke-dasharray="6 2 2 2" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 6.20768 29.4824 L 9.29511 29.4824 L 7.7514 31.037 L 6.20768 29.4824" fill="none" stroke="rgb(170,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.597025948103791" x2="7.597025948103791" y1="2.04293756914176" y2="0.4882945839350796"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.597025948103783" x2="19.17487025948103" y1="29.63785055656028" y2="21.32051058570456"/>
   <ellipse cx="7.42" cy="8.6" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.5" cy="17.92" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.611718118722353" x2="7.611718118722353" y1="15.85874008086165" y2="18.45772818087859"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.2052656081938" x2="7.61171811872235" y1="21.05671628089551" y2="18.45772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.018170629250884" x2="7.611718118722337" y1="21.05671628089551" y2="18.45772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.41666666666666" x2="15.93279794298801" y1="26.63147854022231" y2="26.63147854022231"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.11989292193093" x2="17.22957168772375" y1="27.93097259023087" y2="27.93097259023087"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8231191771952" x2="18.52634543245948" y1="29.23046664023932" y2="29.23046664023932"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.611718118722351" x2="19.17487025948103" y1="18.45772818087858" y2="18.45772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.17473230482734" x2="19.17473230482734" y1="18.49624249591244" y2="26.39901100404639"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.756205920707614" x2="7.756205920707614" y1="32.74713652697363" y2="24.41980688231738"/>
  </symbol>
  <symbol id=":单相三绕组PT带避雷器_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="39"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.79,20.83) scale(1,1) translate(0,0)" width="6.08" x="8.75" y="13.67"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666666" x2="31.16666666666666" y1="19.66666666666667" y2="19.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.25" x2="14.25" y1="30.91666666666666" y2="30.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.25" x2="13.25" y1="31.91666666666666" y2="31.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.25" x2="12.25" y1="32.91666666666666" y2="32.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="27.91666666666666" y2="30.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.5" x2="31.5" y1="27.25" y2="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.91022336769755" x2="19.91022336769755" y1="39.83333333333334" y2="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="12.75" y1="21.66666666666667" y2="15.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="10.75" y1="21.66666666666667" y2="15.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="21.66666666666667" y2="8.999999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.33333333333333" x2="11.75" y1="9.008752830905545" y2="9.008752830905545"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.25545055364641" x2="28.25545055364641" y1="9.041388644475965" y2="15.16666666666666"/>
   <ellipse cx="28.43" cy="19.74" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="28.51" cy="27.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="35.07589728904158" x2="35.07589728904158" y1="8.997588122517026" y2="8.997588122517026"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.25000000000001" x2="38.25000000000001" y1="24.41666666666667" y2="24.41666666666667"/>
   <ellipse cx="34.78" cy="24.19" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV中电光伏硅厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="66.95999999999999" xlink:href="logo.png" y="45.64"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.589,75.6429) scale(1,1) translate(0,0)" writing-mode="lr" x="201.59" xml:space="preserve" y="79.14" zvalue="10493"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,203.298,75.3332) scale(1,1) translate(9.43293e-15,0)" writing-mode="lr" x="203.3" xml:space="preserve" y="84.33" zvalue="10494">110kV中电光伏硅厂</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="16" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" width="72.88" x="71.94" y="327" zvalue="10496"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" writing-mode="lr" x="108.38" xml:space="preserve" y="343.5" zvalue="10496">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,853.219,382.662) scale(1,1) translate(-1.81792e-13,0)" writing-mode="lr" x="853.22" xml:space="preserve" y="387.16" zvalue="7577">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1047.3,318.216) scale(1,1) translate(0,0)" writing-mode="lr" x="1047.3" xml:space="preserve" y="322.72" zvalue="9968">141</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,990.669,187.201) scale(1,1) translate(0,0)" writing-mode="lr" x="990.67" xml:space="preserve" y="191.7" zvalue="9972">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1046.57,373.121) scale(1,1) translate(0,0)" writing-mode="lr" x="1046.57" xml:space="preserve" y="377.62" zvalue="9975">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,991.419,274.636) scale(1,1) translate(0,0)" writing-mode="lr" x="991.42" xml:space="preserve" y="279.14" zvalue="10028">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1046.07,224.659) scale(1,1) translate(0,0)" writing-mode="lr" x="1046.07" xml:space="preserve" y="229.16" zvalue="10032">6</text>
  <line fill="none" id="174" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="152.8704926140824" y2="152.8704926140824" zvalue="10076"/>
  <line fill="none" id="173" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="380" x2="380" y1="11" y2="1041" zvalue="10077"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="260.75" y2="283.5"/>
  <line fill="none" id="171" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="622.8704926140824" y2="622.8704926140824" zvalue="10079"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="582" y2="606.6794"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="938" y2="938"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="938" y2="938"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1005.0816" y2="1033"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,958) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="964" zvalue="10083">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,992) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="998" zvalue="10084">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237,992) scale(1,1) translate(0,0)" writing-mode="lr" x="237" xml:space="preserve" y="998" zvalue="10085">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1026" zvalue="10086">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="1026" zvalue="10087">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" x="137.53125" xml:space="preserve" y="462.3993055555555" zvalue="10088">110kV母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="137.53125" xml:space="preserve" y="479.3993055555555" zvalue="10088">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5,652.5) scale(1,1) translate(0,0)" writing-mode="lr" x="78.5" xml:space="preserve" y="657" zvalue="10090">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,212.399,338.071) scale(1,1) translate(0,0)" writing-mode="lr" x="212.4" xml:space="preserve" y="342.57" zvalue="10091">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,317.399,338.071) scale(1,1) translate(0,0)" writing-mode="lr" x="317.4" xml:space="preserve" y="342.57" zvalue="10092">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" x="256.9375" xml:space="preserve" y="460.8368055555555" zvalue="10093">10kV    母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="256.9375" xml:space="preserve" y="477.8368055555555" zvalue="10093">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90,495.75) scale(1,1) translate(0,0)" writing-mode="lr" x="90" xml:space="preserve" y="500.25" zvalue="10095">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90,521.25) scale(1,1) translate(0,0)" writing-mode="lr" x="90" xml:space="preserve" y="525.75" zvalue="10096">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90,544.25) scale(1,1) translate(0,0)" writing-mode="lr" x="90" xml:space="preserve" y="548.75" zvalue="10097">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90,567.25) scale(1,1) translate(0,0)" writing-mode="lr" x="90" xml:space="preserve" y="571.75" zvalue="10098">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90,594.25) scale(1,1) translate(0,0)" writing-mode="lr" x="90" xml:space="preserve" y="598.75" zvalue="10099">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.054,960) scale(1,1) translate(0,0)" writing-mode="lr" x="237.05" xml:space="preserve" y="966" zvalue="10100">ZhongDianGuangFu-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52,179) scale(1,1) translate(0,0)" writing-mode="lr" x="52" xml:space="preserve" y="183.5" zvalue="10103">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232,179) scale(1,1) translate(0,0)" writing-mode="lr" x="232" xml:space="preserve" y="183.5" zvalue="10104">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,203.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="207.75" zvalue="10105">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.1875,227.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.19" xml:space="preserve" y="231.75" zvalue="10110">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1008.27,99.9464) scale(1,1) translate(0,0)" writing-mode="lr" x="1008.27" xml:space="preserve" y="104.45" zvalue="10140">110kV支盏T线光伏支线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,991.339,367.117) scale(1,1) translate(0,0)" writing-mode="lr" x="991.34" xml:space="preserve" y="371.62" zvalue="10259">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1316.3,488.393) scale(1,1) translate(0,0)" writing-mode="lr" x="1316.3" xml:space="preserve" y="492.89" zvalue="10435">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1307.57,437.488) scale(1,1) translate(0,0)" writing-mode="lr" x="1307.57" xml:space="preserve" y="441.99" zvalue="10442">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1241.34,455.492) scale(1,1) translate(0,0)" writing-mode="lr" x="1241.34" xml:space="preserve" y="459.99" zvalue="10458">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" x="1359.765625" xml:space="preserve" y="672.8125" zvalue="10504">#1炉变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1359.765625" xml:space="preserve" y="688.8125" zvalue="10504">12.5MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1182.14,691.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1182.14" xml:space="preserve" y="696.0700000000001" zvalue="10506">1010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1288.52,898.071) scale(1,1) translate(0,0)" writing-mode="lr" x="1288.52" xml:space="preserve" y="902.5700000000001" zvalue="10508">#1电炉</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" x="1357.890625" xml:space="preserve" y="857.8125" zvalue="10510">#1电容器组</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1357.890625" xml:space="preserve" y="873.8125" zvalue="10510">3MVar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1302.64,552.181) scale(1,1) translate(0,1.20721e-13)" writing-mode="lr" x="1302.64" xml:space="preserve" y="556.6799999999999" zvalue="10543">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1576.3,488.393) scale(1,1) translate(0,0)" writing-mode="lr" x="1576.3" xml:space="preserve" y="492.89" zvalue="10554">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1567.57,437.488) scale(1,1) translate(0,0)" writing-mode="lr" x="1567.57" xml:space="preserve" y="441.99" zvalue="10556">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1501.34,455.492) scale(1,1) translate(0,0)" writing-mode="lr" x="1501.34" xml:space="preserve" y="459.99" zvalue="10560">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" x="1619.765625" xml:space="preserve" y="672.8125" zvalue="10563">#2炉变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1619.765625" xml:space="preserve" y="688.8125" zvalue="10563">12.5MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1442.14,691.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1442.14" xml:space="preserve" y="696.0700000000001" zvalue="10565">1020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1548.52,898.071) scale(1,1) translate(0,0)" writing-mode="lr" x="1548.52" xml:space="preserve" y="902.5700000000001" zvalue="10569">#2电炉</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1562.64,552.181) scale(1,1) translate(0,1.20721e-13)" writing-mode="lr" x="1562.64" xml:space="preserve" y="556.6799999999999" zvalue="10577">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1495.77,581.382) scale(1,1) translate(0,0)" writing-mode="lr" x="1495.77" xml:space="preserve" y="585.88" zvalue="10580">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" x="1630.890625" xml:space="preserve" y="941.5625" zvalue="10589">#2电容器组</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1630.890625" xml:space="preserve" y="957.5625" zvalue="10589">9MVar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1611.27,817.017) scale(1,1) translate(0,0)" writing-mode="lr" x="1611.27" xml:space="preserve" y="821.52" zvalue="10592">043</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1645.14,767.181) scale(1,1) translate(0,0)" writing-mode="lr" x="1645.14" xml:space="preserve" y="771.6799999999999" zvalue="10593">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,840.298,488.393) scale(1,1) translate(0,0)" writing-mode="lr" x="840.3" xml:space="preserve" y="492.89" zvalue="10607">104</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,831.568,437.488) scale(1,1) translate(0,0)" writing-mode="lr" x="831.5700000000001" xml:space="preserve" y="441.99" zvalue="10609">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="147" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,765.339,455.492) scale(1,1) translate(0,0)" writing-mode="lr" x="765.34" xml:space="preserve" y="459.99" zvalue="10613">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" x="883.78125" xml:space="preserve" y="568.8125" zvalue="10616">#1环保动力变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="883.78125" xml:space="preserve" y="584.8125" zvalue="10616">3.15MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,706.143,587.571) scale(1,1) translate(0,2.57159e-13)" writing-mode="lr" x="706.14" xml:space="preserve" y="592.0700000000001" zvalue="10619">1040</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="192" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,660.643,695.571) scale(1,1) translate(0,0)" writing-mode="lr" x="660.64" xml:space="preserve" y="700.0700000000001" zvalue="10629">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,791.077,652.017) scale(1,1) translate(0,-5.71558e-13)" writing-mode="lr" x="791.08" xml:space="preserve" y="656.52" zvalue="10640">004</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,823.642,687.181) scale(1,1) translate(0,0)" writing-mode="lr" x="823.64" xml:space="preserve" y="691.6799999999999" zvalue="10644">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,675,966.5) scale(1,1) translate(0,0)" writing-mode="lr" x="675" xml:space="preserve" y="971" zvalue="10649">10kV环保用电Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,695.619,815.592) scale(1,1) translate(0,0)" writing-mode="lr" x="695.62" xml:space="preserve" y="820.09" zvalue="10652">041</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="233" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,662.927,869.428) scale(1,1) translate(0,0)" writing-mode="lr" x="662.9299999999999" xml:space="preserve" y="873.9299999999999" zvalue="10653">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="232" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,664.246,763.659) scale(1,1) translate(0,0)" writing-mode="lr" x="664.25" xml:space="preserve" y="768.16" zvalue="10654">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="223" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,987,966.5) scale(1,1) translate(0,0)" writing-mode="lr" x="987" xml:space="preserve" y="971" zvalue="10662">10kV环保用电Ⅱ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1003.62,815.592) scale(1,1) translate(0,0)" writing-mode="lr" x="1003.62" xml:space="preserve" y="820.09" zvalue="10664">42</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="237" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,974.927,869.428) scale(1,1) translate(0,0)" writing-mode="lr" x="974.9299999999999" xml:space="preserve" y="873.9299999999999" zvalue="10665">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="235" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,976.246,763.659) scale(1,1) translate(0,0)" writing-mode="lr" x="976.25" xml:space="preserve" y="768.16" zvalue="10666">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="239" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,831,884) scale(1,1) translate(0,0)" writing-mode="lr" x="831" xml:space="preserve" y="888.5" zvalue="10671">#1站用变</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="71.94" y="327" zvalue="10496"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="48">
   <path class="kv110" d="M 609.14 401.66 L 1679.39 401.66" stroke-width="6" zvalue="7576"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674250326020" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674250326020"/></metadata>
  <path d="M 609.14 401.66 L 1679.39 401.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="kv10" d="M 631.14 718.57 L 1040.14 718.57" stroke-width="6" zvalue="10628"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674250391556" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674250391556"/></metadata>
  <path d="M 631.14 718.57 L 1040.14 718.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="100">
   <use class="kv110" height="20" transform="rotate(0,1021.35,318.592) scale(1.5542,1.35421) translate(-361.423,-79.7894)" width="10" x="1013.576891677422" xlink:href="#Breaker:开关_0" y="305.0494044238865" zvalue="9966"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924540956677" ObjectName="110kV支盏T线光伏支线141断路器"/>
   <cge:TPSR_Ref TObjectID="6473924540956677"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1021.35,318.592) scale(1.5542,1.35421) translate(-361.423,-79.7894)" width="10" x="1013.576891677422" y="305.0494044238865"/></g>
  <g id="61">
   <use class="kv110" height="20" transform="rotate(0,1288.35,487.017) scale(1.5542,-1.35421) translate(-456.631,-843.107)" width="10" x="1280.576891677422" xlink:href="#Breaker:开关_0" y="473.4751523863273" zvalue="10434"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924541022213" ObjectName="#1炉变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473924541022213"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1288.35,487.017) scale(1.5542,-1.35421) translate(-456.631,-843.107)" width="10" x="1280.576891677422" y="473.4751523863273"/></g>
  <g id="133">
   <use class="kv110" height="20" transform="rotate(0,1548.35,487.017) scale(1.5542,-1.35421) translate(-549.342,-843.107)" width="10" x="1540.576891677422" xlink:href="#Breaker:开关_0" y="473.4751523863273" zvalue="10553"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924541087749" ObjectName="#2炉变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473924541087749"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1548.35,487.017) scale(1.5542,-1.35421) translate(-549.342,-843.107)" width="10" x="1540.576891677422" y="473.4751523863273"/></g>
  <g id="120">
   <use class="kv10" height="20" transform="rotate(0,1632.04,818.017) scale(1.5542,-1.35421) translate(-579.185,-1418.53)" width="10" x="1624.270513736041" xlink:href="#Breaker:开关_0" y="804.4751523863274" zvalue="10591"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924541153285" ObjectName="#2电容器组043断路器"/>
   <cge:TPSR_Ref TObjectID="6473924541153285"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1632.04,818.017) scale(1.5542,-1.35421) translate(-579.185,-1418.53)" width="10" x="1624.270513736041" y="804.4751523863274"/></g>
  <g id="187">
   <use class="kv110" height="20" transform="rotate(0,812.348,487.017) scale(1.5542,-1.35421) translate(-286.898,-843.107)" width="10" x="804.5768916774223" xlink:href="#Breaker:开关_0" y="473.4751523863273" zvalue="10606"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924541218821" ObjectName="#1环保动力变110kV侧104断路器"/>
   <cge:TPSR_Ref TObjectID="6473924541218821"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,812.348,487.017) scale(1.5542,-1.35421) translate(-286.898,-843.107)" width="10" x="804.5768916774223" y="473.4751523863273"/></g>
  <g id="200">
   <use class="kv10" height="20" transform="rotate(0,812.348,653.017) scale(1.5542,-1.35421) translate(-286.898,-1131.69)" width="10" x="804.5768916774223" xlink:href="#Breaker:开关_0" y="639.4751523863274" zvalue="10639"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924541284357" ObjectName="#1环保动力变10kV侧004断路器"/>
   <cge:TPSR_Ref TObjectID="6473924541284357"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,812.348,653.017) scale(1.5542,-1.35421) translate(-286.898,-1131.69)" width="10" x="804.5768916774223" y="639.4751523863274"/></g>
  <g id="214">
   <use class="kv10" height="20" transform="rotate(0,675.348,816.592) scale(1.5542,1.35421) translate(-238.046,-210.047)" width="10" x="667.5768916774224" xlink:href="#Breaker:开关_0" y="803.0494044238866" zvalue="10651"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924541349893" ObjectName="10kV环保用电Ⅰ回线041断路器"/>
   <cge:TPSR_Ref TObjectID="6473924541349893"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,675.348,816.592) scale(1.5542,1.35421) translate(-238.046,-210.047)" width="10" x="667.5768916774224" y="803.0494044238866"/></g>
  <g id="230">
   <use class="kv10" height="20" transform="rotate(0,987.348,816.592) scale(1.5542,1.35421) translate(-349.299,-210.047)" width="10" x="979.5768916774224" xlink:href="#Breaker:开关_0" y="803.0494044238866" zvalue="10663"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924541415429" ObjectName="10kV环保用电Ⅱ回线042断路器"/>
   <cge:TPSR_Ref TObjectID="6473924541415429"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,987.348,816.592) scale(1.5542,1.35421) translate(-349.299,-210.047)" width="10" x="979.5768916774224" y="803.0494044238866"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="98">
   <use class="kv110" height="20" transform="rotate(270,991.339,205.886) scale(-1.24619,-1.0068) translate(-1785.61,-410.313)" width="10" x="985.1079399930696" xlink:href="#GroundDisconnector:地刀_0" y="195.8179363847547" zvalue="9971"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449938391046" ObjectName="110kV支盏T线光伏支线14167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449938391046"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,991.339,205.886) scale(-1.24619,-1.0068) translate(-1785.61,-410.313)" width="10" x="985.1079399930696" y="195.8179363847547"/></g>
  <g id="29">
   <use class="kv110" height="20" transform="rotate(270,991.339,257.886) scale(-1.24619,-1.0068) translate(-1785.61,-513.962)" width="10" x="985.1079399930696" xlink:href="#GroundDisconnector:地刀_0" y="247.8179363847547" zvalue="10027"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449938522118" ObjectName="110kV支盏T线光伏支线14160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449938522118"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,991.339,257.886) scale(-1.24619,-1.0068) translate(-1785.61,-513.962)" width="10" x="985.1079399930696" y="247.8179363847547"/></g>
  <g id="114">
   <use class="kv110" height="20" transform="rotate(270,991.339,349.886) scale(-1.24619,-1.0068) translate(-1785.61,-697.341)" width="10" x="985.1079401555987" xlink:href="#GroundDisconnector:地刀_0" y="339.8179363847547" zvalue="10258"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449938784262" ObjectName="110kV支盏T线光伏支线14117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449938784262"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,991.339,349.886) scale(-1.24619,-1.0068) translate(-1785.61,-697.341)" width="10" x="985.1079401555987" y="339.8179363847547"/></g>
  <g id="28">
   <use class="kv110" height="20" transform="rotate(90,1264.34,455.723) scale(-1.24619,1.0068) translate(-2277.67,-3.01026)" width="10" x="1258.107933956715" xlink:href="#GroundDisconnector:地刀_0" y="445.6548171461969" zvalue="10456"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449938915334" ObjectName="#1炉变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449938915334"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1264.34,455.723) scale(-1.24619,1.0068) translate(-2277.67,-3.01026)" width="10" x="1258.107933956715" y="445.6548171461969"/></g>
  <g id="104">
   <use class="kv110" height="40" transform="rotate(0,1218.14,692.571) scale(1,-1) translate(0,-1385.14)" width="40" x="1198.142857142857" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="672.5714285714287" zvalue="10505"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449939111942" ObjectName="#1炉变110kV侧中性点1010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449939111942"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1218.14,692.571) scale(1,-1) translate(0,-1385.14)" width="40" x="1198.142857142857" y="672.5714285714287"/></g>
  <g id="128">
   <use class="kv110" height="20" transform="rotate(90,1524.34,455.723) scale(-1.24619,1.0068) translate(-2746.31,-3.01026)" width="10" x="1518.107933956715" xlink:href="#GroundDisconnector:地刀_0" y="445.6548171461969" zvalue="10558"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449940029445" ObjectName="#2炉变110kV侧10217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449940029445"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1524.34,455.723) scale(-1.24619,1.0068) translate(-2746.31,-3.01026)" width="10" x="1518.107933956715" y="445.6548171461969"/></g>
  <g id="101">
   <use class="kv110" height="40" transform="rotate(0,1478.14,692.571) scale(1,-1) translate(0,-1385.14)" width="40" x="1458.142857142857" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="672.5714285714287" zvalue="10564"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449939898374" ObjectName="#2炉变110kV侧中性点1020接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449939898374"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1478.14,692.571) scale(1,-1) translate(0,-1385.14)" width="40" x="1458.142857142857" y="672.5714285714287"/></g>
  <g id="62">
   <use class="kv110" height="20" transform="rotate(90,1524.34,579.723) scale(-1.24619,1.0068) translate(-2746.31,-3.84785)" width="10" x="1518.107933956715" xlink:href="#GroundDisconnector:地刀_0" y="569.6548171461969" zvalue="10578"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449939570694" ObjectName="#2炉变110kV侧10267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449939570694"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1524.34,579.723) scale(-1.24619,1.0068) translate(-2746.31,-3.84785)" width="10" x="1518.107933956715" y="569.6548171461969"/></g>
  <g id="180">
   <use class="kv110" height="20" transform="rotate(90,788.339,455.723) scale(-1.24619,1.0068) translate(-1419.71,-3.01026)" width="10" x="782.1079339567156" xlink:href="#GroundDisconnector:地刀_0" y="445.6548171461969" zvalue="10611"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449940619269" ObjectName="#1环保动力变110kV侧10417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449940619269"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,788.339,455.723) scale(-1.24619,1.0068) translate(-1419.71,-3.01026)" width="10" x="782.1079339567156" y="445.6548171461969"/></g>
  <g id="169">
   <use class="kv110" height="40" transform="rotate(0,742.143,588.571) scale(1,-1) translate(0,-1177.14)" width="40" x="722.1428571428571" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="568.5714285714286" zvalue="10617"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449940488197" ObjectName="#1环保动力变110kV侧中性点1040接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449940488197"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,742.143,588.571) scale(1,-1) translate(0,-1177.14)" width="40" x="722.1428571428571" y="568.5714285714286"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="96">
   <use class="kv110" height="30" transform="rotate(180,1021.53,372.428) scale(0.947693,-0.6712) translate(55.9904,-932.228)" width="15" x="1014.426990568853" xlink:href="#Disconnector:刀闸_0" y="362.3599590791467" zvalue="9974"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449938259974" ObjectName="110kV支盏T线光伏支线1411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449938259974"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1021.53,372.428) scale(0.947693,-0.6712) translate(55.9904,-932.228)" width="15" x="1014.426990568853" y="362.3599590791467"/></g>
  <g id="32">
   <use class="kv110" height="30" transform="rotate(0,1021.35,230.659) scale(-0.947693,0.6712) translate(-2099.47,108.061)" width="15" x="1014.246350035014" xlink:href="#Disconnector:刀闸_0" y="220.5912212922868" zvalue="10031"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449938587654" ObjectName="110kV支盏T线光伏支线1416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449938587654"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1021.35,230.659) scale(-0.947693,0.6712) translate(-2099.47,108.061)" width="15" x="1014.246350035014" y="220.5912212922868"/></g>
  <g id="53">
   <use class="kv110" height="30" transform="rotate(180,1288.53,433.181) scale(0.947693,0.6712) translate(70.7272,207.269)" width="15" x="1281.426990568852" xlink:href="#Disconnector:刀闸_0" y="423.1127944518049" zvalue="10441"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449938980870" ObjectName="#1炉变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449938980870"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1288.53,433.181) scale(0.947693,0.6712) translate(70.7272,207.269)" width="15" x="1281.426990568852" y="423.1127944518049"/></g>
  <g id="5">
   <use class="kv110" height="30" transform="rotate(180,1288.53,553.181) scale(0.947693,0.6712) translate(70.7272,266.054)" width="15" x="1281.426990568852" xlink:href="#Disconnector:刀闸_0" y="543.1127944518049" zvalue="10542"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449939439622" ObjectName="#1炉变110kV侧1016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449939439622"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1288.53,553.181) scale(0.947693,0.6712) translate(70.7272,266.054)" width="15" x="1281.426990568852" y="543.1127944518049"/></g>
  <g id="132">
   <use class="kv110" height="30" transform="rotate(180,1548.53,433.181) scale(0.947693,0.6712) translate(85.0777,207.269)" width="15" x="1541.426990568852" xlink:href="#Disconnector:刀闸_0" y="423.1127944518049" zvalue="10555"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449940094981" ObjectName="#2炉变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449940094981"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1548.53,433.181) scale(0.947693,0.6712) translate(85.0777,207.269)" width="15" x="1541.426990568852" y="423.1127944518049"/></g>
  <g id="69">
   <use class="kv110" height="30" transform="rotate(180,1548.53,553.181) scale(0.947693,0.6712) translate(85.0777,266.054)" width="15" x="1541.426990568852" xlink:href="#Disconnector:刀闸_0" y="543.1127944518049" zvalue="10576"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449939636230" ObjectName="#2炉变110kV侧1026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449939636230"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1548.53,553.181) scale(0.947693,0.6712) translate(85.0777,266.054)" width="15" x="1541.426990568852" y="543.1127944518049"/></g>
  <g id="121">
   <use class="kv10" height="30" transform="rotate(180,1632.53,768.181) scale(0.947693,0.6712) translate(89.714,371.375)" width="15" x="1625.426990568853" xlink:href="#Disconnector:刀闸_0" y="758.112794451805" zvalue="10592"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449940226054" ObjectName="#2电容器组0436隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449940226054"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1632.53,768.181) scale(0.947693,0.6712) translate(89.714,371.375)" width="15" x="1625.426990568853" y="758.112794451805"/></g>
  <g id="186">
   <use class="kv110" height="30" transform="rotate(180,812.535,433.181) scale(0.947693,0.6712) translate(44.4548,207.269)" width="15" x="805.4269905688527" xlink:href="#Disconnector:刀闸_0" y="423.1127944518049" zvalue="10608"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449940684805" ObjectName="#1环保动力变110kV侧1041隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449940684805"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,812.535,433.181) scale(0.947693,0.6712) translate(44.4548,207.269)" width="15" x="805.4269905688527" y="423.1127944518049"/></g>
  <g id="195">
   <use class="kv10" height="30" transform="rotate(0,976.69,670.571) scale(1,1) translate(0,0)" width="15" x="969.1895238095238" xlink:href="#Disconnector:令克_0" y="655.5714285714287" zvalue="10632"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449940815877" ObjectName="110kV母线电压互感器高压熔断器"/>
   <cge:TPSR_Ref TObjectID="6192449940815877"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,976.69,670.571) scale(1,1) translate(0,0)" width="15" x="969.1895238095238" y="655.5714285714287"/></g>
  <g id="204">
   <use class="kv10" height="30" transform="rotate(180,812.535,688.181) scale(0.947693,0.6712) translate(44.4548,332.186)" width="15" x="805.4269905688527" xlink:href="#Disconnector:刀闸_0" y="678.112794451805" zvalue="10643"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449940946949" ObjectName="#1环保动力变10kV侧0041隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449940946949"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,812.535,688.181) scale(0.947693,0.6712) translate(44.4548,332.186)" width="15" x="805.4269905688527" y="678.112794451805"/></g>
  <g id="213">
   <use class="kv10" height="30" transform="rotate(180,675.535,870.428) scale(0.947693,-0.6712) translate(36.8932,-2172.18)" width="15" x="668.4269905688527" xlink:href="#Disconnector:刀闸_0" y="860.3599590791466" zvalue="10652"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449941078021" ObjectName="10kV环保用电Ⅰ回线0416隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449941078021"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,675.535,870.428) scale(0.947693,-0.6712) translate(36.8932,-2172.18)" width="15" x="668.4269905688527" y="860.3599590791466"/></g>
  <g id="215">
   <use class="kv10" height="30" transform="rotate(0,675.354,764.659) scale(-0.947693,0.6712) translate(-1388.38,369.65)" width="15" x="668.2463500350141" xlink:href="#Disconnector:刀闸_0" y="754.5912212922868" zvalue="10653"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449941143557" ObjectName="10kV环保用电Ⅰ回线0411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449941143557"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,675.354,764.659) scale(-0.947693,0.6712) translate(-1388.38,369.65)" width="15" x="668.2463500350141" y="754.5912212922868"/></g>
  <g id="229">
   <use class="kv10" height="30" transform="rotate(180,987.535,870.428) scale(0.947693,-0.6712) translate(54.1138,-2172.18)" width="15" x="980.4269905688528" xlink:href="#Disconnector:刀闸_0" y="860.3599590791466" zvalue="10664"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449941274629" ObjectName="10kV环保用电Ⅱ回线0426隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449941274629"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,987.535,870.428) scale(0.947693,-0.6712) translate(54.1138,-2172.18)" width="15" x="980.4269905688528" y="860.3599590791466"/></g>
  <g id="228">
   <use class="kv10" height="30" transform="rotate(0,987.354,764.659) scale(-0.947693,0.6712) translate(-2029.6,369.65)" width="15" x="980.2463500350141" xlink:href="#Disconnector:刀闸_0" y="754.5912212922868" zvalue="10665"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449941209093" ObjectName="10kV环保用电Ⅱ回线0421隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449941209093"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,987.354,764.659) scale(-0.947693,0.6712) translate(-2029.6,369.65)" width="15" x="980.2463500350141" y="754.5912212922868"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="93">
   <path class="kv110" d="M 1021.45 331.52 L 1021.45 362.69" stroke-width="1" zvalue="9978"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@1" LinkObjectIDznd="96@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1021.45 331.52 L 1021.45 362.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv110" d="M 1021.48 382.32 L 1021.48 401.66" stroke-width="1" zvalue="9981"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@1" LinkObjectIDznd="48@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1021.48 382.32 L 1021.48 401.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv110" d="M 1021.27 167.07 L 1021.27 220.92" stroke-width="1" zvalue="10040"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="32@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1021.27 167.07 L 1021.27 220.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv110" d="M 1001.16 257.95 L 1021.3 257.95" stroke-width="1" zvalue="10141"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="29@0" LinkObjectIDznd="73" MaxPinNum="2"/>
   </metadata>
  <path d="M 1001.16 257.95 L 1021.3 257.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv110" d="M 1001.16 205.95 L 1021.27 205.95" stroke-width="1" zvalue="10255"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="41" MaxPinNum="2"/>
   </metadata>
  <path d="M 1001.16 205.95 L 1021.27 205.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv110" d="M 1001.16 349.95 L 1021.45 349.95" stroke-width="1" zvalue="10259"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="93" MaxPinNum="2"/>
   </metadata>
  <path d="M 1001.16 349.95 L 1021.45 349.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv110" d="M 1288.45 474.08 L 1288.45 442.92" stroke-width="1" zvalue="10443"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@1" LinkObjectIDznd="53@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1288.45 474.08 L 1288.45 442.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv110" d="M 1274.16 455.66 L 1288.45 455.66" stroke-width="1" zvalue="10457"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="52" MaxPinNum="2"/>
   </metadata>
  <path d="M 1274.16 455.66 L 1288.45 455.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv110" d="M 1288.48 423.28 L 1288.48 401.66" stroke-width="1" zvalue="10459"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@1" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1288.48 423.28 L 1288.48 401.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv110" d="M 1021.3 240.56 L 1021.3 305.64" stroke-width="1" zvalue="10463"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@1" LinkObjectIDznd="100@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1021.3 240.56 L 1021.3 305.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv110" d="M 1288.27 652.47 L 1220.14 652.47 L 1220.74 680.37" stroke-width="1" zvalue="10506"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@2" LinkObjectIDznd="104@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1288.27 652.47 L 1220.14 652.47 L 1220.74 680.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv10" d="M 1288.27 864.95 L 1288.27 724.29" stroke-width="1" zvalue="10508"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="97@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1288.27 864.95 L 1288.27 724.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv10" d="M 1357.43 798.78 L 1357.43 780.57 L 1288.27 780.57" stroke-width="1" zvalue="10510"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="109" MaxPinNum="2"/>
   </metadata>
  <path d="M 1357.43 798.78 L 1357.43 780.57 L 1288.27 780.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv10" d="M 1320.89 781.32 L 1320.89 780.57" stroke-width="1" zvalue="10540"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="7@0" LinkObjectIDznd="112" MaxPinNum="2"/>
   </metadata>
  <path d="M 1320.89 781.32 L 1320.89 780.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv110" d="M 1288.3 630.23 L 1288.3 562.92" stroke-width="1" zvalue="10544"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="5@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1288.3 630.23 L 1288.3 562.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv110" d="M 1288.48 543.28 L 1288.48 499.97" stroke-width="1" zvalue="10545"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@1" LinkObjectIDznd="61@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1288.48 543.28 L 1288.48 499.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv110" d="M 1317.78 612.61 L 1288.3 612.61" stroke-width="1" zvalue="10547"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@0" LinkObjectIDznd="10" MaxPinNum="2"/>
   </metadata>
  <path d="M 1317.78 612.61 L 1288.3 612.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv110" d="M 1548.45 474.08 L 1548.45 442.92" stroke-width="1" zvalue="10557"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@1" LinkObjectIDznd="132@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1548.45 474.08 L 1548.45 442.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv110" d="M 1534.16 455.66 L 1548.45 455.66" stroke-width="1" zvalue="10559"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@0" LinkObjectIDznd="129" MaxPinNum="2"/>
   </metadata>
  <path d="M 1534.16 455.66 L 1548.45 455.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv110" d="M 1548.48 423.28 L 1548.48 401.66" stroke-width="1" zvalue="10561"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1548.48 423.28 L 1548.48 401.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv110" d="M 1548.27 652.47 L 1480.14 652.47 L 1480.74 680.37" stroke-width="1" zvalue="10566"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@2" LinkObjectIDznd="101@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1548.27 652.47 L 1480.14 652.47 L 1480.74 680.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv10" d="M 1548.27 864.95 L 1548.27 724.29" stroke-width="1" zvalue="10568"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="113@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1548.27 864.95 L 1548.27 724.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv110" d="M 1548.3 630.23 L 1548.3 562.92" stroke-width="1" zvalue="10579"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="69@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1548.3 630.23 L 1548.3 562.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv110" d="M 1548.48 543.28 L 1548.48 499.97" stroke-width="1" zvalue="10581"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@1" LinkObjectIDznd="133@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1548.48 543.28 L 1548.48 499.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv110" d="M 1534.16 579.66 L 1548.3 579.66" stroke-width="1" zvalue="10582"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="60" MaxPinNum="2"/>
   </metadata>
  <path d="M 1534.16 579.66 L 1548.3 579.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv110" d="M 1577.78 612.61 L 1548.3 612.61" stroke-width="1" zvalue="10583"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="79@0" LinkObjectIDznd="60" MaxPinNum="2"/>
   </metadata>
  <path d="M 1577.78 612.61 L 1548.3 612.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv10" d="M 1631.99 858.26 L 1631.99 830.97" stroke-width="1" zvalue="10593"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="120@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1631.99 858.26 L 1631.99 830.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv10" d="M 1632.15 805.08 L 1632.15 777.92" stroke-width="1" zvalue="10595"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@1" LinkObjectIDznd="121@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1632.15 805.08 L 1632.15 777.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv10" d="M 1632.48 758.28 L 1632.48 748.57 L 1548.27 748.57" stroke-width="1" zvalue="10596"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="121@1" LinkObjectIDznd="91" MaxPinNum="2"/>
   </metadata>
  <path d="M 1632.48 758.28 L 1632.48 748.57 L 1548.27 748.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv10" d="M 1614.51 788.61 L 1632.15 788.61" stroke-width="1" zvalue="10603"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 1614.51 788.61 L 1632.15 788.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="kv10" d="M 1613.51 845.61 L 1631.99 845.61" stroke-width="1" zvalue="10604"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@0" LinkObjectIDznd="124" MaxPinNum="2"/>
   </metadata>
  <path d="M 1613.51 845.61 L 1631.99 845.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="181">
   <path class="kv110" d="M 812.45 474.08 L 812.45 442.92" stroke-width="1" zvalue="10610"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@1" LinkObjectIDznd="186@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 812.45 474.08 L 812.45 442.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv110" d="M 798.16 455.66 L 812.45 455.66" stroke-width="1" zvalue="10612"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@0" LinkObjectIDznd="181" MaxPinNum="2"/>
   </metadata>
  <path d="M 798.16 455.66 L 812.45 455.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv110" d="M 812.48 423.28 L 812.48 401.66" stroke-width="1" zvalue="10614"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@1" LinkObjectIDznd="48@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 812.48 423.28 L 812.48 401.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv110" d="M 812.27 548.47 L 744.14 548.47 L 744.74 576.37" stroke-width="1" zvalue="10618"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@2" LinkObjectIDznd="169@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 812.27 548.47 L 744.14 548.47 L 744.74 576.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv110" d="M 812.3 499.97 L 812.3 526.23" stroke-width="1" zvalue="10627"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="187@0" LinkObjectIDznd="176@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 812.3 499.97 L 812.3 526.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv10" d="M 976.77 643.5 L 976.77 657.32" stroke-width="1" zvalue="10633"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@0" LinkObjectIDznd="195@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 976.77 643.5 L 976.77 657.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv10" d="M 976.61 682.82 L 976.61 718.57" stroke-width="1" zvalue="10634"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@1" LinkObjectIDznd="190@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 976.61 682.82 L 976.61 718.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="kv10" d="M 994.78 693.61 L 976.61 693.61" stroke-width="1" zvalue="10637"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@0" LinkObjectIDznd="197" MaxPinNum="2"/>
   </metadata>
  <path d="M 994.78 693.61 L 976.61 693.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv10" d="M 812.27 620.29 L 812.27 640.08" stroke-width="1" zvalue="10640"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="176@1" LinkObjectIDznd="200@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 812.27 620.29 L 812.27 640.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv10" d="M 812.3 665.97 L 812.3 678.28" stroke-width="1" zvalue="10646"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="200@0" LinkObjectIDznd="204@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 812.3 665.97 L 812.3 678.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv10" d="M 812.3 697.92 L 812.3 718.57" stroke-width="1" zvalue="10647"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="204@0" LinkObjectIDznd="190@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 812.3 697.92 L 812.3 718.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="219">
   <path class="kv10" d="M 675.3 803.64 L 675.3 774.56" stroke-width="1" zvalue="10656"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@0" LinkObjectIDznd="215@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 675.3 803.64 L 675.3 774.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="220">
   <path class="kv10" d="M 675.27 754.92 L 675.27 718.57" stroke-width="1" zvalue="10657"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@0" LinkObjectIDznd="190@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 675.27 754.92 L 675.27 718.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="kv10" d="M 676.83 907.39 L 676.83 880.32" stroke-width="1" zvalue="10658"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="210@0" LinkObjectIDznd="213@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 676.83 907.39 L 676.83 880.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="kv10" d="M 675.45 860.69 L 675.45 829.52" stroke-width="1" zvalue="10659"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="213@0" LinkObjectIDznd="214@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 675.45 860.69 L 675.45 829.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="227">
   <path class="kv10" d="M 987.3 803.64 L 987.3 774.56" stroke-width="1" zvalue="10666"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="228@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 987.3 803.64 L 987.3 774.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="226">
   <path class="kv10" d="M 987.27 754.92 L 987.27 718.57" stroke-width="1" zvalue="10667"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="228@0" LinkObjectIDznd="190@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 987.27 754.92 L 987.27 718.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv10" d="M 988.83 907.39 L 988.83 880.32" stroke-width="1" zvalue="10668"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@0" LinkObjectIDznd="229@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 988.83 907.39 L 988.83 880.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="kv10" d="M 987.45 860.69 L 987.45 829.52" stroke-width="1" zvalue="10669"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@0" LinkObjectIDznd="230@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 987.45 860.69 L 987.45 829.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv10" d="M 831.33 808.95 L 831.33 718.57" stroke-width="1" zvalue="10671"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="238@0" LinkObjectIDznd="190@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 831.33 808.95 L 831.33 718.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,521.25) scale(1,1) translate(1.24653e-14,0)" writing-mode="lr" x="137.75" xml:space="preserve" y="526.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126127796228" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="67">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,544.25) scale(1,1) translate(1.24653e-14,5.96467e-14)" writing-mode="lr" x="137.75" xml:space="preserve" y="549.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126127861764" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,567.25) scale(1,1) translate(1.24653e-14,-1.244e-13)" writing-mode="lr" x="137.75" xml:space="preserve" y="572.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126127927300" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,495.75) scale(1,1) translate(1.24653e-14,-1.08524e-13)" writing-mode="lr" x="137.75" xml:space="preserve" y="500.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126128058372" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,142,203.111) scale(1,1) translate(0,0)" writing-mode="lr" x="142.15" xml:space="preserve" y="209.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126128189444" ObjectName="F"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,521.25) scale(1,1) translate(2.57217e-14,0)" writing-mode="lr" x="257.15" xml:space="preserve" y="526.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126141362182" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="58">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="58" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,544.25) scale(1,1) translate(2.57217e-14,5.96467e-14)" writing-mode="lr" x="257.15" xml:space="preserve" y="549.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126141427718" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,567.25) scale(1,1) translate(2.57217e-14,-1.244e-13)" writing-mode="lr" x="257.15" xml:space="preserve" y="572.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126141493254" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,495.75) scale(1,1) translate(2.57217e-14,-1.08524e-13)" writing-mode="lr" x="257.15" xml:space="preserve" y="500.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126141624326" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="54" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,142,228) scale(1,1) translate(0,0)" writing-mode="lr" x="142.15" xml:space="preserve" y="234.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126141755398" ObjectName="F"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,594.25) scale(1,1) translate(1.24653e-14,1.30396e-13)" writing-mode="lr" x="137.75" xml:space="preserve" y="599.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126128254980" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="20" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,257.042,594.25) scale(1,1) translate(2.57217e-14,1.30396e-13)" writing-mode="lr" x="257.15" xml:space="preserve" y="599.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126141820934" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1021.77,26.8214) scale(1,1) translate(0,0)" writing-mode="lr" x="1021.95" xml:space="preserve" y="31.69" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126128844807" ObjectName="P"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="18" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1021.77,47.8214) scale(1,1) translate(0,0)" writing-mode="lr" x="1021.95" xml:space="preserve" y="52.69" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126128910340" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1021.77,68.8214) scale(1,1) translate(0,0)" writing-mode="lr" x="1021.95" xml:space="preserve" y="73.69" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126128975876" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="2" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,667.271,445.821) scale(1,1) translate(0,0)" writing-mode="lr" x="666.72" xml:space="preserve" y="452.1" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126138806278" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="6" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,667.271,482.821) scale(1,1) translate(0,0)" writing-mode="lr" x="666.72" xml:space="preserve" y="489.1" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126138871814" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="9" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,667.271,519.821) scale(1,1) translate(0,0)" writing-mode="lr" x="666.72" xml:space="preserve" y="526.1" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126139068422" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="17" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1664.52,466.071) scale(1,1) translate(0,9.96029e-14)" writing-mode="lr" x="1663.97" xml:space="preserve" y="472.35" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126135005188" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="21" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1664.52,503.071) scale(1,1) translate(0,0)" writing-mode="lr" x="1663.97" xml:space="preserve" y="509.35" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126135070724" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="24">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="24" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1664.52,540.071) scale(1,1) translate(0,0)" writing-mode="lr" x="1663.97" xml:space="preserve" y="546.35" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126135267332" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="30">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="30" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1164.52,459.821) scale(1,1) translate(2.46807e-13,0)" writing-mode="lr" x="1163.97" xml:space="preserve" y="466.1" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126130417668" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="31" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1164.52,496.821) scale(1,1) translate(2.46807e-13,0)" writing-mode="lr" x="1163.97" xml:space="preserve" y="503.1" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126130483204" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="33" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1164.52,533.821) scale(1,1) translate(2.46807e-13,0)" writing-mode="lr" x="1163.97" xml:space="preserve" y="540.1" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126130679814" ObjectName="HIa"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="1056">
   <use height="30" transform="rotate(0,265.286,338.071) scale(0.708333,0.665547) translate(104.86,164.872)" width="30" x="254.66" xlink:href="#State:红绿圆(方形)_0" y="328.09" zvalue="10247"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562951572094980" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,265.286,338.071) scale(0.708333,0.665547) translate(104.86,164.872)" width="30" x="254.66" y="328.09"/></g>
  <g id="1057">
   <use height="30" transform="rotate(0,352.911,338.071) scale(0.708333,0.665547) translate(140.941,164.872)" width="30" x="342.29" xlink:href="#State:红绿圆(方形)_0" y="328.09" zvalue="10249"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374892728323" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,352.911,338.071) scale(0.708333,0.665547) translate(140.941,164.872)" width="30" x="342.29" y="328.09"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="97">
   <g id="970">
    <use class="kv110" height="30" transform="rotate(0,1288.27,677.026) scale(3.36364,3.36364) translate(-879.272,-440.293)" width="22" x="1251.27" xlink:href="#PowerTransformer2:接地可调两卷变_0" y="626.5700000000001" zvalue="10503"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874447716355" ObjectName="110"/>
    </metadata>
   </g>
   <g id="971">
    <use class="kv10" height="30" transform="rotate(0,1288.27,677.026) scale(3.36364,3.36364) translate(-879.272,-440.293)" width="22" x="1251.27" xlink:href="#PowerTransformer2:接地可调两卷变_1" y="626.5700000000001" zvalue="10503"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874447781891" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399455932419" ObjectName="#1炉变"/>
   <cge:TPSR_Ref TObjectID="6755399455932419"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1288.27,677.026) scale(3.36364,3.36364) translate(-879.272,-440.293)" width="22" x="1251.27" y="626.5700000000001"/></g>
  <g id="113">
   <g id="1130">
    <use class="kv110" height="30" transform="rotate(0,1548.27,677.026) scale(3.36364,3.36364) translate(-1061.97,-440.293)" width="22" x="1511.27" xlink:href="#PowerTransformer2:接地可调两卷变_0" y="626.5700000000001" zvalue="10562"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874447847427" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1131">
    <use class="kv10" height="30" transform="rotate(0,1548.27,677.026) scale(3.36364,3.36364) translate(-1061.97,-440.293)" width="22" x="1511.27" xlink:href="#PowerTransformer2:接地可调两卷变_1" y="626.5700000000001" zvalue="10562"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874447912963" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399455997955" ObjectName="#2炉变"/>
   <cge:TPSR_Ref TObjectID="6755399455997955"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1548.27,677.026) scale(3.36364,3.36364) translate(-1061.97,-440.293)" width="22" x="1511.27" y="626.5700000000001"/></g>
  <g id="176">
   <g id="1760">
    <use class="kv110" height="30" transform="rotate(0,812.271,573.026) scale(3.36364,3.36364) translate(-544.785,-367.212)" width="22" x="775.27" xlink:href="#PowerTransformer2:接地可调两卷变_0" y="522.5700000000001" zvalue="10615"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874447978499" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1761">
    <use class="kv10" height="30" transform="rotate(0,812.271,573.026) scale(3.36364,3.36364) translate(-544.785,-367.212)" width="22" x="775.27" xlink:href="#PowerTransformer2:接地可调两卷变_1" y="522.5700000000001" zvalue="10615"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874448044035" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399456063491" ObjectName="#1环保动力变"/>
   <cge:TPSR_Ref TObjectID="6755399456063491"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,812.271,573.026) scale(3.36364,3.36364) translate(-544.785,-367.212)" width="22" x="775.27" y="522.5700000000001"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="107">
   <use class="kv10" height="14" transform="rotate(0,1288.27,875.071) scale(1.5,1.5) translate(-422.674,-288.19)" width="27" x="1268.021473606603" xlink:href="#EnergyConsumer:电炉_0" y="864.5714285714287" zvalue="10507"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449939177478" ObjectName="#1电炉"/>
   <cge:TPSR_Ref TObjectID="6192449939177478"/></metadata>
  <rect fill="white" height="14" opacity="0" stroke="white" transform="rotate(0,1288.27,875.071) scale(1.5,1.5) translate(-422.674,-288.19)" width="27" x="1268.021473606603" y="864.5714285714287"/></g>
  <g id="92">
   <use class="kv10" height="14" transform="rotate(0,1548.27,875.071) scale(1.5,1.5) translate(-509.34,-288.19)" width="27" x="1528.021473606603" xlink:href="#EnergyConsumer:电炉_0" y="864.5714285714287" zvalue="10567"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449939767302" ObjectName="#2电炉"/>
   <cge:TPSR_Ref TObjectID="6192449939767302"/></metadata>
  <rect fill="white" height="14" opacity="0" stroke="white" transform="rotate(0,1548.27,875.071) scale(1.5,1.5) translate(-509.34,-288.19)" width="27" x="1528.021473606603" y="864.5714285714287"/></g>
  <g id="210">
   <use class="kv10" height="30" transform="rotate(0,680.25,925.5) scale(1.36667,-1.36667) translate(-179.756,-1597.2)" width="15" x="670" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="905" zvalue="10648"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449941012485" ObjectName="10kV环保用电Ⅰ回线"/>
   <cge:TPSR_Ref TObjectID="6192449941012485"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,680.25,925.5) scale(1.36667,-1.36667) translate(-179.756,-1597.2)" width="15" x="670" y="905"/></g>
  <g id="231">
   <use class="kv10" height="30" transform="rotate(0,992.25,925.5) scale(1.36667,-1.36667) translate(-263.463,-1597.2)" width="15" x="982" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="905" zvalue="10661"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449941340165" ObjectName="10kV环保用电Ⅱ回线"/>
   <cge:TPSR_Ref TObjectID="6192449941340165"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,992.25,925.5) scale(1.36667,-1.36667) translate(-263.463,-1597.2)" width="15" x="982" y="905"/></g>
  <g id="238">
   <use class="kv10" height="39" transform="rotate(0,837.25,838.402) scale(1.54348,1.54348) translate(-288.556,-284.614)" width="23" x="819.5" xlink:href="#EnergyConsumer:站用变D-Y_0" y="808.304347826087" zvalue="10670"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449941405701" ObjectName="#1站用变"/>
   <cge:TPSR_Ref TObjectID="6192449941405701"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,837.25,838.402) scale(1.54348,1.54348) translate(-288.556,-284.614)" width="23" x="819.5" y="808.304347826087"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="110">
   <use class="kv10" height="13" transform="rotate(0,1357.14,816.488) scale(2.83333,2.83333) translate(-867.151,-516.399)" width="12" x="1340.142857142857" xlink:href="#Compensator:无功补偿20210816_0" y="798.0714285714284" zvalue="10509"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449939243014" ObjectName="#1电容器组"/>
   <cge:TPSR_Ref TObjectID="6192449939243014"/></metadata>
  <rect fill="white" height="13" opacity="0" stroke="white" transform="rotate(0,1357.14,816.488) scale(2.83333,2.83333) translate(-867.151,-516.399)" width="12" x="1340.142857142857" y="798.0714285714284"/></g>
  <g id="115">
   <use class="kv10" height="40" transform="rotate(90,1635.14,887.571) scale(1,1) translate(0,0)" width="60" x="1605.142857142857" xlink:href="#Compensator:遮放35kV电容_0" y="867.5714285714287" zvalue="10588"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449940160517" ObjectName="#2电容器组"/>
   <cge:TPSR_Ref TObjectID="6192449940160517"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(90,1635.14,887.571) scale(1,1) translate(0,0)" width="60" x="1605.142857142857" y="867.5714285714287"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="184">
   <use class="kv110" height="26" transform="rotate(270,1330.14,612.648) scale(1,1) translate(0,0)" width="12" x="1324.142857142857" xlink:href="#Accessory:避雷器1_0" y="599.6478544176694" zvalue="10533"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449939308550" ObjectName="#1炉变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1330.14,612.648) scale(1,1) translate(0,0)" width="12" x="1324.142857142857" y="599.6478544176694"/></g>
  <g id="7">
   <use class="kv10" height="20" transform="rotate(270,1320.89,781.321) scale(1.25,1.25) translate(-262.929,-153.764)" width="10" x="1314.642857142857" xlink:href="#Accessory:熔断器12_0" y="768.8214285714287" zvalue="10539"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449939374086" ObjectName="#1电容器组熔断器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1320.89,781.321) scale(1.25,1.25) translate(-262.929,-153.764)" width="10" x="1314.642857142857" y="768.8214285714287"/></g>
  <g id="79">
   <use class="kv110" height="26" transform="rotate(270,1590.14,612.648) scale(1,1) translate(0,0)" width="12" x="1584.142857142857" xlink:href="#Accessory:避雷器1_0" y="599.6478544176694" zvalue="10573"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449939701766" ObjectName="#2炉变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1590.14,612.648) scale(1,1) translate(0,0)" width="12" x="1584.142857142857" y="599.6478544176694"/></g>
  <g id="140">
   <use class="kv10" height="26" transform="rotate(90,1602.14,788.648) scale(-1,1) translate(-3204.29,0)" width="12" x="1596.142857142857" xlink:href="#Accessory:避雷器1_0" y="775.6478544176694" zvalue="10600"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449940291590" ObjectName="#2电容器组避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1602.14,788.648) scale(-1,1) translate(-3204.29,0)" width="12" x="1596.142857142857" y="775.6478544176694"/></g>
  <g id="141">
   <use class="kv10" height="26" transform="rotate(90,1601.14,845.648) scale(-1,1) translate(-3202.29,0)" width="12" x="1595.142857142857" xlink:href="#Accessory:避雷器1_0" y="832.6478544176694" zvalue="10602"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449940357126" ObjectName="#2电容器组避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1601.14,845.648) scale(-1,1) translate(-3202.29,0)" width="12" x="1595.142857142857" y="832.6478544176694"/></g>
  <g id="193">
   <use class="kv10" height="25" transform="rotate(0,988.393,624.821) scale(1.66,1.66) translate(-384.725,-240.173)" width="25" x="967.6428571428571" xlink:href="#Accessory:四卷不带避雷器_0" y="604.0714285714286" zvalue="10630"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449940750341" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,988.393,624.821) scale(1.66,1.66) translate(-384.725,-240.173)" width="25" x="967.6428571428571" y="604.0714285714286"/></g>
  <g id="198">
   <use class="kv10" height="26" transform="rotate(270,1007.14,693.648) scale(1,1) translate(0,0)" width="12" x="1001.142857142857" xlink:href="#Accessory:避雷器1_0" y="680.6478544176694" zvalue="10636"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449940881413" ObjectName="110kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1007.14,693.648) scale(1,1) translate(0,0)" width="12" x="1001.142857142857" y="680.6478544176694"/></g>
 </g>
</svg>