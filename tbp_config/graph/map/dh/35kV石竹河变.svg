<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549598420994" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_0" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="3" y1="19" y2="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="4.75" y2="8.75"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="18.75" y2="21.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_1" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="2" y2="24"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="8.5" y1="8.75" y2="8.75"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸_2" viewBox="0,0,14,26">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="1.700383258460111"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="24.31751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.08333333333333" x2="3.995614035087721" y1="9.083333333333332" y2="17.19627192982456"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.717927631578948" x2="10.16666666666667" y1="9.172423245614038" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="2" y2="5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="21.25" y2="24.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="1.763815276003976" y2="6.574160103590185"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="4.686947459912011" y2="9.497292287498219"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="1.680481942670642" y2="6.490826770256848"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="24.32918883922238" y2="19.51884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="21.52315435646374" y2="16.71280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="24.32918883922238" y2="19.51884401163617"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_0" viewBox="0,0,24,30">
   <use terminal-index="0" type="1" x="12.01097393689986" xlink:href="#terminal" y="1.076388888888891"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01097393689986" x2="7.955871323769093" y1="7.932784636488341" y2="3.94460232855122"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.23333333333333" x2="12.01097393689986" y1="3.694602328551216" y2="7.910836762688615"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.01179698216735" x2="12.01179698216735" y1="7.936028940348194" y2="11.93602894034819"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.02194787379972" x2="22.02194787379972" y1="2.021947873799723" y2="5.021947873799725"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.9386145404663946" x2="23.02194787379972" y1="13.84430727023319" y2="2.010973936899859"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20" x2="23" y1="1.021947873799727" y2="2.021947873799727"/>
   <ellipse cx="12.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:可调不带中性点_1" viewBox="0,0,24,30">
   <use terminal-index="1" type="1" x="12" xlink:href="#terminal" y="29.04621056241427"/>
   <path d="M 7.66708 25.8333 L 16.7504 25.8333 L 12.0004 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Compensator:石竹河电容_0" viewBox="0,0,25,50">
   <use terminal-index="0" type="0" x="15.53806584362139" xlink:href="#terminal" y="3.916666666666671"/>
   <path d="M 15.5 8 L 15.5 4" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.41666666666667" x2="15.41666666666667" y1="21.58333333333334" y2="14.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.25" x2="15.25" y1="39.75" y2="46"/>
   <path d="M 15.3333 34.0833 L 15.3333 39.0833" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="15.5" y1="44.49999999999999" y2="44.49999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.333333333333332" x2="15.33333333333333" y1="14.66666666666666" y2="14.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="15.5" y1="34" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.33333333333333" x2="8.583333333333332" y1="46.02500000000001" y2="46.02500000000001"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.6669449648394" x2="12.6669449648394" y1="35.23218852495381" y2="33.86887908453677"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.64403220113491" x2="12.64403220113491" y1="42.92153126609119" y2="44.30775347021272"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.35833333333333" x2="15.35833333333333" y1="21.5" y2="34.25"/>
   <path d="M 12.6669 37.828 A 1.83361 1.26721 -180 0 1 12.6669 35.2936" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 12.6669 40.3625 A 1.83361 1.26721 -180 0 1 12.6669 37.828" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 12.6669 42.8272 A 1.83361 1.26721 -180 0 1 12.6669 40.2927" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.47622508369464" x2="14.38450772290849" y1="39.69411948214003" y2="39.69411948214003"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.42467136535954" x2="14.33295400457339" y1="38.97395102168146" y2="38.97395102168146"/>
   <path d="M 8.3714 14.575 A 6.84167 7.10597 -270 1 0 15.4774 7.73333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.916666666666666" x2="8.916666666666664" y1="44.63112091546323" y2="44.63112091546323"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.666666666666666" x2="8.666666666666666" y1="39.88112091546323" y2="46.13112091546323"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.41666666666666" x2="22.41666666666666" y1="44.54778758212989" y2="44.54778758212989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.41666666666666" x2="22.41666666666666" y1="34.04778758212989" y2="34.04778758212989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.916666666666666" x2="8.916666666666664" y1="34.13112091546323" y2="34.13112091546323"/>
   <path d="M 22.25 34.1311 L 22.25 39.1311" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.16666666666666" x2="22.16666666666666" y1="39.79778758212989" y2="46.04778758212989"/>
   <path d="M 8.75 34.2145 L 8.75 39.2145" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58361163150607" x2="19.58361163150607" y1="35.27997610708371" y2="33.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.083611631506071" x2="6.083611631506071" y1="35.36330944041704" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.56069886780158" x2="19.56069886780158" y1="42.96931884822108" y2="44.35554105234261"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.060698867801575" x2="6.060698867801575" y1="43.05265218155442" y2="44.43887438567594"/>
   <path d="M 19.5836 37.8758 A 1.83361 1.26721 -180 0 1 19.5836 35.3414" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.08361 37.9592 A 1.83361 1.26721 -180 0 1 6.08361 35.4248" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 19.5836 40.4103 A 1.83361 1.26721 -180 0 1 19.5836 37.8758" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.08361 40.4936 A 1.83361 1.26721 -180 0 1 6.08361 37.9592" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.08361 42.9583 A 1.83361 1.26721 -180 0 1 6.08361 40.4239" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 19.5836 42.8749 A 1.83361 1.26721 -180 0 1 19.5836 40.3405" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.34133803202621" x2="21.24962067124006" y1="39.02173860381135" y2="39.02173860381135"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.39289175036131" x2="21.30117438957516" y1="39.74190706426992" y2="39.74190706426992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.841338032026206" x2="7.749620671240055" y1="39.10507193714469" y2="39.10507193714469"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.892891750361306" x2="7.801174389575155" y1="39.82524039760325" y2="39.82524039760325"/>
  </symbol>
  <symbol id="EnergyConsumer:石竹河接地变_0" viewBox="0,0,22,37">
   <use terminal-index="0" type="0" x="11.08333333333333" xlink:href="#terminal" y="1.916666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="34.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="12" y1="34.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.41666666666667" x2="10.41666666666667" y1="34.91666666666666" y2="34.91666666666666"/>
   <path d="M 11 14.75 L 11 19.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.41666666666667" x2="11.41666666666667" y1="35.41666666666666" y2="35.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="31.5" y2="34.5"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11,29) scale(1,1) translate(0,0)" width="1" x="10.5" y="26.5"/>
   <path d="M 13 19.5 L 11 23.5 L 11 26.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="5.25" y2="2.5"/>
   <ellipse cx="11.03" cy="9.960000000000001" fill-opacity="0" rx="4.72" ry="4.62" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.02566015658475" x2="11.02566015658475" y1="5.510389533882023" y2="9.38903557397801"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.02566015658475" x2="7.269557345940605" y1="9.443137645577355" y2="12.72399908045799"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.0869541465795" x2="15.17788782448493" y1="9.347824244629766" y2="12.36331860312917"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变YD2022_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="24" y1="24" y2="18"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.98980556533989" x2="12.83661970177291" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.9898055653399" x2="15.14299142890688" y1="29.68333333333333" y2="27.75505857643124"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.83661970177294" x2="15.1429914289069" y1="27.75505857643125" y2="27.75505857643125"/>
  </symbol>
  <symbol id="Accessory:ptblq_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="18" xlink:href="#terminal" y="1.066666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.24166666666667" x2="10.86666666666667" y1="1" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.26666666666667" x2="25.26666666666667" y1="6.583333333333332" y2="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="12" y2="1"/>
   <ellipse cx="10.62" cy="16.75" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.62" cy="20.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.78" cy="24.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.05" cy="20.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.86666666666667" x2="14.86666666666667" y1="23.25" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="14.86666666666667" y1="18.63888888888889" y2="20.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.61666666666667" x2="10.61666666666667" y1="19" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.616666666666671" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.61666666666667" x2="10.61666666666667" y1="14.38888888888889" y2="16.38888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.86666666666667" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.866666666666671" x2="10.86666666666667" y1="22.13888888888889" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.86666666666667" x2="10.86666666666667" y1="26.75" y2="24.13888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067752" x2="5.636382113821139" y1="22.23028455284553" y2="20.91546973803071"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.10250677506775" x2="5.63638211382114" y1="18.28584010840109" y2="19.60065492321591"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.102506775067754" x2="8.102506775067756" y1="18.28584010840108" y2="22.23028455284553"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,10.86,6.21) scale(-1,1) translate(-1746.33,0)" width="4.92" x="8.4" y="2.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.53333333333333" x2="27.36666666666667" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="6.583333333333337" y2="12.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="25.28333333333333" y1="14.83333333333334" y2="18.43333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.28333333333333" x2="26.95" y1="12.5" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.23333333333333" x2="23.65000000000001" y1="12.53333333333333" y2="9.283333333333333"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-360,25.36,10.96) scale(-1,1) translate(-2326.33,0)" width="4.92" x="22.9" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.93333333333334" x2="27.01666666666667" y1="19.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.28333333333333" x2="26.41666666666666" y1="21" y2="21"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸1_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="35.06751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="9" y1="9" y2="9"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.084670781893005" x2="1.626337448559672" y1="4.853614126578682" y2="9.663958954164888"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.084670781893004" x2="12.54300411522634" y1="4.853614126578682" y2="9.663958954164888"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.27315435646374" y2="26.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="31.27315435646374" y2="26.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="7.00133744855967" y1="4.916666666666663" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="1.833333333333333" x2="7.001337448559672" y1="10.75" y2="27"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="7.00133744855967" x2="7.00133744855967" y1="26.75" y2="31.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸1_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="35.06751120764343"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="7.00133744855967" y1="0.7499999999999964" y2="35.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="9" y1="9" y2="9"/>
  </symbol>
  <symbol id="Disconnector:联体手车刀闸1_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.9503832584601106"/>
   <use terminal-index="1" type="0" x="7.021825533811279" xlink:href="#terminal" y="35.06751120764343"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="2.083333333333333" y1="11.16666666666666" y2="25.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.133446357018218" x2="11.79254048508705" y1="11.12564285751284" y2="24.83570582669768"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.27315435646374" y2="27.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="35.07918883922238" y2="30.26884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.27315435646374" y2="27.46280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="35.07918883922238" y2="30.26884401163617"/>
  </symbol>
  <symbol id="Disconnector:石竹河变隔刀_0" viewBox="0,0,20,15">
   <use terminal-index="0" type="0" x="6.75" xlink:href="#terminal" y="7.333333333333333"/>
   <use terminal-index="1" type="0" x="18.95833333333333" xlink:href="#terminal" y="8.833333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.875" x2="18.125" y1="8.791666666666666" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="13" y1="6.166666666666666" y2="9.166666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.41633830629834" x2="15.59762883546839" y1="5.454459356264215" y2="7.347490124786891"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="5.25" y2="12"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.56325225887448" x2="18.20833333333334" y1="7.352403488288314" y2="7.352403488288314"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.27050656226104" x2="6.958333333333333" y1="7.354679508488497" y2="7.354679508488497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.26850526417405" x2="11.26850526417405" y1="6.904348370330451" y2="7.726200752762247"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.30389157810095" x2="15.485182107271" y1="8.39001437360243" y2="10.28304514212511"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.45080553067709" x2="18.08333333333334" y1="10.28795850562653" y2="10.28795850562653"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.32472650073032" x2="10.02341123021651" y1="10.29023452582672" y2="10.29023452582672"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.32272520264332" x2="11.32272520264332" y1="9.83990338766867" y2="10.66175577010046"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.91666666666666" x2="18.16666666666666" y1="8.791666666666666" y2="7.333333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.125" x2="3.125" y1="5.5" y2="12.25"/>
  </symbol>
  <symbol id="Disconnector:石竹河变隔刀_1" viewBox="0,0,20,15">
   <use terminal-index="0" type="0" x="6.75" xlink:href="#terminal" y="7.333333333333333"/>
   <use terminal-index="1" type="0" x="18.95833333333333" xlink:href="#terminal" y="8.833333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.56325225887448" x2="18.20833333333334" y1="7.352403488288314" y2="7.352403488288314"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="5.25" y2="12"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.29166666666667" x2="15.59762883546839" y1="7.347490124786891" y2="7.347490124786891"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="13" y1="7.375" y2="10.375"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.875" x2="18.125" y1="8.791666666666666" y2="10.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.27050656226104" x2="6.958333333333333" y1="7.354679508488497" y2="7.354679508488497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.26850526417405" x2="11.26850526417405" y1="6.904348370330451" y2="7.726200752762247"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.375" x2="15.485182107271" y1="10.2830451421251" y2="10.2830451421251"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.45080553067709" x2="18.08333333333334" y1="10.28795850562653" y2="10.28795850562653"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.32272520264332" x2="11.32272520264332" y1="9.83990338766867" y2="10.66175577010046"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.32472650073032" x2="10.02341123021651" y1="10.29023452582672" y2="10.29023452582672"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.91666666666666" x2="18.16666666666666" y1="8.791666666666666" y2="7.333333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.125" x2="3.125" y1="5.5" y2="12.25"/>
  </symbol>
  <symbol id="Disconnector:石竹河变隔刀_2" viewBox="0,0,20,15">
   <use terminal-index="0" type="0" x="6.75" xlink:href="#terminal" y="7.333333333333333"/>
   <use terminal-index="1" type="0" x="18.95833333333333" xlink:href="#terminal" y="8.833333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.625" x2="15.125" y1="7.625" y2="10.125"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="15.25" y1="10.125" y2="7.625"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.56325225887448" x2="18.20833333333334" y1="7.352403488288314" y2="7.352403488288314"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.875" x2="18.125" y1="8.791666666666666" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="5.25" y2="12"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.27050656226104" x2="6.958333333333333" y1="7.354679508488497" y2="7.354679508488497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.26850526417405" x2="11.26850526417405" y1="6.904348370330451" y2="7.726200752762247"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.45080553067709" x2="18.08333333333334" y1="10.28795850562653" y2="10.28795850562653"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.32272520264332" x2="11.32272520264332" y1="9.83990338766867" y2="10.66175577010046"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.32472650073032" x2="10.02341123021651" y1="10.29023452582672" y2="10.29023452582672"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.91666666666666" x2="18.16666666666666" y1="8.791666666666666" y2="7.333333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.125" x2="3.125" y1="5.5" y2="12.25"/>
  </symbol>
  <symbol id="Accessory:石竹河附属_0" viewBox="0,0,20,15">
   <use terminal-index="0" type="0" x="4.25" xlink:href="#terminal" y="7.5"/>
   <ellipse cx="7.25" cy="7.5" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="12.83" cy="7.5" fill-opacity="0" rx="2.83" ry="2.83" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:回贤变站用变_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="9" y1="24" y2="25.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="25.75" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.16666666666667" x2="11.25" y1="24" y2="25.83333333333333"/>
   <rect fill-opacity="0" height="4.32" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.06,5.25) scale(1,1) translate(0,0)" width="3.43" x="8.34" y="3.09"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0235180220435" x2="10.0235180220435" y1="0.5833333333333091" y2="9.290410445610181"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="29.16666666666667" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="10.8868007916835" y2="12.55855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="14.23031840279781" y2="12.55855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="ACLineSegment:线路带壁雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="14.875" xlink:href="#terminal" y="39.83880854456296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.25" x2="6.25" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="5.75" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.25" x2="7.25" y1="32.25" y2="32.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="8.25" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="6.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="4.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.91022336769755" x2="14.91022336769755" y1="39.75" y2="0.8333333333333321"/>
   <path d="M 14.75 9.25 L 5.75 9.25 L 5.75 21.25 L 5.75 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.79,21.17) scale(1,1) translate(0,0)" width="6.08" x="2.75" y="14"/>
  </symbol>
  <symbol id="DollyBreaker:手车_0" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="0.3833333333333346" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.09166666666667" x2="21.8" y1="1.007668711656441" y2="11.21216768916155"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.0246913580247" x2="0.3833333333333346" y1="11.2962962962963" y2="21.41666666666666"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.07407407407407" x2="21.53229166666667" y1="11.2962962962963" y2="21.41666666666666"/>
  </symbol>
  <symbol id="DollyBreaker:手车_1" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="21" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="1" y1="1" y2="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11" x2="11" y1="1" y2="11"/>
  </symbol>
  <symbol id="DollyBreaker:手车_2" viewBox="0,0,22,22">
   <use terminal-index="0" type="0" x="11.0136046511628" xlink:href="#terminal" y="1.007668711656439"/>
   <use terminal-index="1" type="0" x="11.05181327160494" xlink:href="#terminal" y="11.29135423767326"/>
   <path d="M 3.6066 1.05 L 18.5833 9.95" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 18.4234 1 L 3.5 10" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷带壁雷器_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="28.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11.75" y1="17.75" y2="20.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="11" y1="14.75" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="12" y1="11.75" y2="11.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="9" y1="12.75" y2="12.75"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.04,18.25) scale(1,1) translate(0,0)" width="3.25" x="9.42" y="14.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.5" y1="10.83333333333333" y2="10.83333333333333"/>
   <path d="M 5.025 2.775 L 4.16667 9.5 L 5.025 7.60833 L 5.91667 9.5 L 5.025 2.775" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 5 23.75 L 11 23.75 L 11 17.75 L 10.25 20.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.000411522633746" x2="5.000411522633746" y1="7.627914951989029" y2="28.23902606310013"/>
  </symbol>
  <symbol id="Accessory:传输线_0" viewBox="0,0,12,22">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="1"/>
   <path d="M 1.16667 1 L 10.8333 1 L 6 7.63889 L 1.16667 1 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="14.27777777777778" y2="7.638888888888888"/>
   <path d="M 1.16667 20.9167 L 10.8333 20.9167 L 6 14.2778 L 1.16667 20.9167 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV石竹河变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,172.939,51.3772) scale(1,1) translate(-1.37318e-14,0)" writing-mode="lr" x="172.94" xml:space="preserve" y="55.88" zvalue="101"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="1" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,190.911,48.3894) scale(1,1) translate(0,2.92884e-15)" writing-mode="lr" x="190.91" xml:space="preserve" y="55.89" zvalue="102"> 35kV石竹河变</text>
  <image height="82.56999999999999" id="2" preserveAspectRatio="xMidYMid slice" width="249.69" x="74.73999999999999" xlink:href="logo.png" y="14"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.582,55.2864) scale(1,1) translate(-1.65946e-14,0)" writing-mode="lr" x="199.58" xml:space="preserve" y="58.79" zvalue="131"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,228.429,55.2631) scale(1,1) translate(0,0)" writing-mode="lr" x="228.43" xml:space="preserve" y="64.26000000000001" zvalue="132">35kV石竹河变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="81" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,191.344,406.461) scale(1,1) translate(0,-2.62764e-13)" width="72.88" x="154.91" y="394.46" zvalue="481"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,191.344,406.461) scale(1,1) translate(0,-2.62764e-13)" writing-mode="lr" x="191.34" xml:space="preserve" y="410.96" zvalue="481">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="80" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,89.4375,406.461) scale(1,1) translate(0,-2.62764e-13)" width="72.88" x="53" y="394.46" zvalue="482"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89.4375,406.461) scale(1,1) translate(0,-2.62764e-13)" writing-mode="lr" x="89.44" xml:space="preserve" y="410.96" zvalue="482">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="79" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,89.4375,363.211) scale(1,1) translate(0,0)" width="72.88" x="53" y="351.21" zvalue="483"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89.4375,363.211) scale(1,1) translate(0,0)" writing-mode="lr" x="89.44" xml:space="preserve" y="367.71" zvalue="483">信号一览</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="47" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,294.25,406.461) scale(1,1) translate(0,0)" width="72.88" x="257.81" y="394.46" zvalue="596"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,294.25,406.461) scale(1,1) translate(0,0)" writing-mode="lr" x="294.25" xml:space="preserve" y="410.96" zvalue="596">小电流接地</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="18" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,89.4375,319) scale(1,1) translate(0,0)" width="72.88" x="53" y="307" zvalue="597"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,89.4375,319) scale(1,1) translate(0,0)" writing-mode="lr" x="89.44" xml:space="preserve" y="323.5" zvalue="597">全站公用</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,573.154,380.163) scale(1,1) translate(0,0)" writing-mode="lr" x="573.15" xml:space="preserve" y="384.66" zvalue="3">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,560.54,697.829) scale(1,1) translate(0,0)" writing-mode="lr" x="560.54" xml:space="preserve" y="702.33" zvalue="5">10kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,910.019,306.773) scale(1,1) translate(0,0)" writing-mode="lr" x="910.02" xml:space="preserve" y="311.27" zvalue="21">331</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,910.179,237) scale(1,1) translate(0,0)" writing-mode="lr" x="910.1799999999999" xml:space="preserve" y="241.5" zvalue="23">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,966.053,342.634) scale(1,1) translate(1.06421e-13,0)" writing-mode="lr" x="966.05" xml:space="preserve" y="347.13" zvalue="25">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,914.679,369) scale(1,1) translate(0,0)" writing-mode="lr" x="914.6799999999999" xml:space="preserve" y="373.5" zvalue="27">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,966.053,198.634) scale(1,1) translate(0,0)" writing-mode="lr" x="966.05" xml:space="preserve" y="203.13" zvalue="29">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,966.053,274.634) scale(1,1) translate(0,-3.52563e-13)" writing-mode="lr" x="966.05" xml:space="preserve" y="279.13" zvalue="31">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1459.43,451.889) scale(1,1) translate(2.72524e-12,0)" writing-mode="lr" x="1459.43" xml:space="preserve" y="456.39" zvalue="39">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1376.86,476.412) scale(1,1) translate(0,0)" writing-mode="lr" x="1376.86" xml:space="preserve" y="480.91" zvalue="43">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1498.47,422.753) scale(1,1) translate(0,0)" writing-mode="lr" x="1498.47" xml:space="preserve" y="427.25" zvalue="46">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1089.7,677.393) scale(1,1) translate(0,0)" writing-mode="lr" x="1089.7" xml:space="preserve" y="681.89" zvalue="57">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,621.256,770.361) scale(1,1) translate(0,0)" writing-mode="lr" x="621.26" xml:space="preserve" y="774.86" zvalue="60">032</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,664.012,813.211) scale(1,1) translate(0,0)" writing-mode="lr" x="664.01" xml:space="preserve" y="817.71" zvalue="67">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,947.867,772.917) scale(1,1) translate(0,0)" writing-mode="lr" x="947.87" xml:space="preserve" y="777.42" zvalue="80">034</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,986.317,813.389) scale(1,1) translate(0,0)" writing-mode="lr" x="986.3200000000001" xml:space="preserve" y="817.89" zvalue="86">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,917.913,977.833) scale(1,1) translate(0,0)" writing-mode="lr" x="917.91" xml:space="preserve" y="982.33" zvalue="88">10kV腾拉拱线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="325" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,957.96,938.333) scale(1,1) translate(0,0)" writing-mode="lr" x="957.96" xml:space="preserve" y="942.83" zvalue="91">0348</text>
  <line fill="none" id="191" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="387.2857142857144" x2="387.2857142857144" y1="6.000000000000227" y2="996.0000000000002" zvalue="103"/>
  <line fill="none" id="190" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="-3" x2="387" y1="159.770803025626" y2="159.770803025626" zvalue="104"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="51.28585714285725" x2="121.9587571428572" y1="918.7056480819299" y2="918.7056480819299"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="51.28585714285725" x2="121.9587571428572" y1="970.0451480819299" y2="970.0451480819299"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="51.28585714285725" x2="51.28585714285725" y1="918.7056480819299" y2="970.0451480819299"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="121.9587571428572" x2="121.9587571428572" y1="918.7056480819299" y2="970.0451480819299"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="121.9590571428572" x2="354.0000571428573" y1="918.7056480819299" y2="918.7056480819299"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="121.9590571428572" x2="354.0000571428573" y1="970.0451480819299" y2="970.0451480819299"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="121.9590571428572" x2="121.9590571428572" y1="918.7056480819299" y2="970.0451480819299"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="354.0000571428573" x2="354.0000571428573" y1="918.7056480819299" y2="970.0451480819299"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="51.28585714285725" x2="121.9587571428572" y1="970.0451280819299" y2="970.0451280819299"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="51.28585714285725" x2="121.9587571428572" y1="997.52262808193" y2="997.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="51.28585714285725" x2="51.28585714285725" y1="970.0451280819299" y2="997.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="121.9587571428572" x2="121.9587571428572" y1="970.0451280819299" y2="997.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="121.9590571428572" x2="192.2315571428572" y1="970.0451280819299" y2="970.0451280819299"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="121.9590571428572" x2="192.2315571428572" y1="997.52262808193" y2="997.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="121.9590571428572" x2="121.9590571428572" y1="970.0451280819299" y2="997.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.2315571428572" x2="192.2315571428572" y1="970.0451280819299" y2="997.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.2316571428572" x2="273.1157571428572" y1="970.0451280819299" y2="970.0451280819299"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.2316571428572" x2="273.1157571428572" y1="997.52262808193" y2="997.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.2316571428572" x2="192.2316571428572" y1="970.0451280819299" y2="997.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.1157571428572" x2="273.1157571428572" y1="970.0451280819299" y2="997.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.1157571428572" x2="353.9998571428572" y1="970.0451280819299" y2="970.0451280819299"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.1157571428572" x2="353.9998571428572" y1="997.52262808193" y2="997.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.1157571428572" x2="273.1157571428572" y1="970.0451280819299" y2="997.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="353.9998571428572" x2="353.9998571428572" y1="970.0451280819299" y2="997.52262808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="51.28585714285725" x2="121.9587571428572" y1="997.5225480819299" y2="997.5225480819299"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="51.28585714285725" x2="121.9587571428572" y1="1025.00004808193" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="51.28585714285725" x2="51.28585714285725" y1="997.5225480819299" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="121.9587571428572" x2="121.9587571428572" y1="997.5225480819299" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="121.9590571428572" x2="192.2315571428572" y1="997.5225480819299" y2="997.5225480819299"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="121.9590571428572" x2="192.2315571428572" y1="1025.00004808193" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="121.9590571428572" x2="121.9590571428572" y1="997.5225480819299" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.2315571428572" x2="192.2315571428572" y1="997.5225480819299" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.2316571428572" x2="273.1157571428572" y1="997.5225480819299" y2="997.5225480819299"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.2316571428572" x2="273.1157571428572" y1="1025.00004808193" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.2316571428572" x2="192.2316571428572" y1="997.5225480819299" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.1157571428572" x2="273.1157571428572" y1="997.5225480819299" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.1157571428572" x2="353.9998571428572" y1="997.5225480819299" y2="997.5225480819299"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.1157571428572" x2="353.9998571428572" y1="1025.00004808193" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.1157571428572" x2="273.1157571428572" y1="997.5225480819299" y2="1025.00004808193"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="353.9998571428572" x2="353.9998571428572" y1="997.5225480819299" y2="1025.00004808193"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="188" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,198.338,948.279) scale(1,1) translate(-1.25768e-14,1.04078e-13)" writing-mode="lr" x="56.64" xml:space="preserve" y="953.78" zvalue="106">参考图号            ShiZhuHe-02-2024</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="187" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,128.863,984.648) scale(1,1) translate(-5.89468e-14,-1.51362e-12)" writing-mode="lr" x="66.37" xml:space="preserve" y="990.15" zvalue="107">制图                       </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="186" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,276.732,984.648) scale(1,1) translate(-4.45118e-14,1.08116e-13)" writing-mode="lr" x="200.46" xml:space="preserve" y="990.15" zvalue="108">绘制日期    20241031    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,118.707,1013.21) scale(1,1) translate(0,1.11286e-13)" writing-mode="lr" x="118.71" xml:space="preserve" y="1018.71" zvalue="109">更新           许方杰</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="184" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,277.461,1013.21) scale(1,1) translate(0,1.11286e-13)" writing-mode="lr" x="200.64" xml:space="preserve" y="1018.71" zvalue="110">更新日期    20241120</text>
  <line fill="none" id="183" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="-1" x2="386" y1="610.5373878122268" y2="610.5373878122268" zvalue="111"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="181" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,93.635,631.579) scale(1,1) translate(5.10353e-15,-1.36087e-13)" writing-mode="lr" x="93.63499377633093" xml:space="preserve" y="636.0792383117246" zvalue="113">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.28571428571445" x2="193.2857142857144" y1="163.0000000000001" y2="163.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.28571428571445" x2="193.2857142857144" y1="189.0000000000001" y2="189.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.28571428571445" x2="12.28571428571445" y1="163.0000000000001" y2="189.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.2857142857144" x2="193.2857142857144" y1="163.0000000000001" y2="189.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.2857142857144" x2="374.2857142857144" y1="163.0000000000001" y2="163.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.2857142857144" x2="374.2857142857144" y1="189.0000000000001" y2="189.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.2857142857144" x2="193.2857142857144" y1="163.0000000000001" y2="189.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="374.2857142857144" x2="374.2857142857144" y1="163.0000000000001" y2="189.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.28571428571445" x2="193.2857142857144" y1="189.0000000000001" y2="189.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.28571428571445" x2="193.2857142857144" y1="213.2500000000001" y2="213.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.28571428571445" x2="12.28571428571445" y1="189.0000000000001" y2="213.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.2857142857144" x2="193.2857142857144" y1="189.0000000000001" y2="213.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.2857142857144" x2="374.2857142857144" y1="189.0000000000001" y2="189.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.2857142857144" x2="374.2857142857144" y1="213.2500000000001" y2="213.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.2857142857144" x2="193.2857142857144" y1="189.0000000000001" y2="213.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="374.2857142857144" x2="374.2857142857144" y1="189.0000000000001" y2="213.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.28571428571445" x2="193.2857142857144" y1="213.2500000000001" y2="213.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.28571428571445" x2="193.2857142857144" y1="236.0000000000001" y2="236.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.28571428571445" x2="12.28571428571445" y1="213.2500000000001" y2="236.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.2857142857144" x2="193.2857142857144" y1="213.2500000000001" y2="236.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.2857142857144" x2="374.2857142857144" y1="213.2500000000001" y2="213.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.2857142857144" x2="374.2857142857144" y1="236.0000000000001" y2="236.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.2857142857144" x2="193.2857142857144" y1="213.2500000000001" y2="236.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="374.2857142857144" x2="374.2857142857144" y1="213.2500000000001" y2="236.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.28571428571445" x2="193.2857142857144" y1="236.0000000000001" y2="236.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.28571428571445" x2="193.2857142857144" y1="258.7500000000001" y2="258.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.28571428571445" x2="12.28571428571445" y1="236.0000000000001" y2="258.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.2857142857144" x2="193.2857142857144" y1="236.0000000000001" y2="258.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.2857142857144" x2="374.2857142857144" y1="236.0000000000001" y2="236.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.2857142857144" x2="374.2857142857144" y1="258.7500000000001" y2="258.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.2857142857144" x2="193.2857142857144" y1="236.0000000000001" y2="258.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="374.2857142857144" x2="374.2857142857144" y1="236.0000000000001" y2="258.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.28571428571445" x2="193.2857142857144" y1="258.7500000000001" y2="258.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.28571428571445" x2="193.2857142857144" y1="281.5000000000001" y2="281.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="12.28571428571445" x2="12.28571428571445" y1="258.7500000000001" y2="281.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.2857142857144" x2="193.2857142857144" y1="258.7500000000001" y2="281.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.2857142857144" x2="374.2857142857144" y1="258.7500000000001" y2="258.7500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.2857142857144" x2="374.2857142857144" y1="281.5000000000001" y2="281.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="193.2857142857144" x2="193.2857142857144" y1="258.7500000000001" y2="281.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="374.2857142857144" x2="374.2857142857144" y1="258.7500000000001" y2="281.5000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="29" x2="116.1396999999999" y1="443.0000000000002" y2="443.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="29" x2="116.1396999999999" y1="481.2823000000002" y2="481.2823000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="29" x2="29" y1="443.0000000000002" y2="481.2823000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="116.1396999999999" y1="443.0000000000002" y2="481.2823000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="183.0997" y1="443.0000000000002" y2="443.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="183.0997" y1="481.2823000000002" y2="481.2823000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="116.1396999999999" y1="443.0000000000002" y2="481.2823000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="183.0997" y1="443.0000000000002" y2="481.2823000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="237.0405" y1="443.0000000000002" y2="443.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="237.0405" y1="481.2823000000002" y2="481.2823000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="183.0997" y1="443.0000000000002" y2="481.2823000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0405" x2="237.0405" y1="443.0000000000002" y2="481.2823000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0403" x2="318.0592" y1="443.0000000000002" y2="443.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0403" x2="318.0592" y1="481.2823000000002" y2="481.2823000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0403" x2="237.0403" y1="443.0000000000002" y2="481.2823000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.0592" x2="318.0592" y1="443.0000000000002" y2="481.2823000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.059" x2="371.9998000000001" y1="443.0000000000002" y2="443.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.059" x2="371.9998000000001" y1="481.2823000000002" y2="481.2823000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.059" x2="318.059" y1="443.0000000000002" y2="481.2823000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.9998000000001" x2="371.9998000000001" y1="443.0000000000002" y2="481.2823000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="29" x2="116.1396999999999" y1="481.2823000000002" y2="481.2823000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="29" x2="116.1396999999999" y1="505.9617000000002" y2="505.9617000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="29" x2="29" y1="481.2823000000002" y2="505.9617000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="116.1396999999999" y1="481.2823000000002" y2="505.9617000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="183.0997" y1="481.2823000000002" y2="481.2823000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="183.0997" y1="505.9617000000002" y2="505.9617000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="116.1396999999999" y1="481.2823000000002" y2="505.9617000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="183.0997" y1="481.2823000000002" y2="505.9617000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="237.0405" y1="481.2823000000002" y2="481.2823000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="237.0405" y1="505.9617000000002" y2="505.9617000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="183.0997" y1="481.2823000000002" y2="505.9617000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0405" x2="237.0405" y1="481.2823000000002" y2="505.9617000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0403" x2="318.0592" y1="481.2823000000002" y2="481.2823000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0403" x2="318.0592" y1="505.9617000000002" y2="505.9617000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0403" x2="237.0403" y1="481.2823000000002" y2="505.9617000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.0592" x2="318.0592" y1="481.2823000000002" y2="505.9617000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.059" x2="371.9998000000001" y1="481.2823000000002" y2="481.2823000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.059" x2="371.9998000000001" y1="505.9617000000002" y2="505.9617000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.059" x2="318.059" y1="481.2823000000002" y2="505.9617000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.9998000000001" x2="371.9998000000001" y1="481.2823000000002" y2="505.9617000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="29" x2="116.1396999999999" y1="505.9617000000002" y2="505.9617000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="29" x2="116.1396999999999" y1="530.6411000000002" y2="530.6411000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="29" x2="29" y1="505.9617000000002" y2="530.6411000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="116.1396999999999" y1="505.9617000000002" y2="530.6411000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="183.0997" y1="505.9617000000002" y2="505.9617000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="183.0997" y1="530.6411000000002" y2="530.6411000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="116.1396999999999" y1="505.9617000000002" y2="530.6411000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="183.0997" y1="505.9617000000002" y2="530.6411000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="237.0405" y1="505.9617000000002" y2="505.9617000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="237.0405" y1="530.6411000000002" y2="530.6411000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="183.0997" y1="505.9617000000002" y2="530.6411000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0405" x2="237.0405" y1="505.9617000000002" y2="530.6411000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0403" x2="318.0592" y1="505.9617000000002" y2="505.9617000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0403" x2="318.0592" y1="530.6411000000002" y2="530.6411000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0403" x2="237.0403" y1="505.9617000000002" y2="530.6411000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.0592" x2="318.0592" y1="505.9617000000002" y2="530.6411000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.059" x2="371.9998000000001" y1="505.9617000000002" y2="505.9617000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.059" x2="371.9998000000001" y1="530.6411000000002" y2="530.6411000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.059" x2="318.059" y1="505.9617000000002" y2="530.6411000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.9998000000001" x2="371.9998000000001" y1="505.9617000000002" y2="530.6411000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="29" x2="116.1396999999999" y1="530.6411000000002" y2="530.6411000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="29" x2="116.1396999999999" y1="555.3205000000002" y2="555.3205000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="29" x2="29" y1="530.6411000000002" y2="555.3205000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="116.1396999999999" y1="530.6411000000002" y2="555.3205000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="183.0997" y1="530.6411000000002" y2="530.6411000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="183.0997" y1="555.3205000000002" y2="555.3205000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="116.1396999999999" y1="530.6411000000002" y2="555.3205000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="183.0997" y1="530.6411000000002" y2="555.3205000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="237.0405" y1="530.6411000000002" y2="530.6411000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="237.0405" y1="555.3205000000002" y2="555.3205000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="183.0997" y1="530.6411000000002" y2="555.3205000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0405" x2="237.0405" y1="530.6411000000002" y2="555.3205000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0403" x2="318.0592" y1="530.6411000000002" y2="530.6411000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0403" x2="318.0592" y1="555.3205000000002" y2="555.3205000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0403" x2="237.0403" y1="530.6411000000002" y2="555.3205000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.0592" x2="318.0592" y1="530.6411000000002" y2="555.3205000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.059" x2="371.9998000000001" y1="530.6411000000002" y2="530.6411000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.059" x2="371.9998000000001" y1="555.3205000000002" y2="555.3205000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.059" x2="318.059" y1="530.6411000000002" y2="555.3205000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.9998000000001" x2="371.9998000000001" y1="530.6411000000002" y2="555.3205000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="29" x2="116.1396999999999" y1="555.3206000000002" y2="555.3206000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="29" x2="116.1396999999999" y1="580.0000000000002" y2="580.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="29" x2="29" y1="555.3206000000002" y2="580.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="116.1396999999999" y1="555.3206000000002" y2="580.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="183.0997" y1="555.3206000000002" y2="555.3206000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="183.0997" y1="580.0000000000002" y2="580.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="116.1396999999999" y1="555.3206000000002" y2="580.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="183.0997" y1="555.3206000000002" y2="580.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="237.0405" y1="555.3206000000002" y2="555.3206000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="237.0405" y1="580.0000000000002" y2="580.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="183.0997" y1="555.3206000000002" y2="580.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0405" x2="237.0405" y1="555.3206000000002" y2="580.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0403" x2="318.0592" y1="555.3206000000002" y2="555.3206000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0403" x2="318.0592" y1="580.0000000000002" y2="580.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0403" x2="237.0403" y1="555.3206000000002" y2="580.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.0592" x2="318.0592" y1="555.3206000000002" y2="580.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.059" x2="371.9998000000001" y1="555.3206000000002" y2="555.3206000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.059" x2="371.9998000000001" y1="580.0000000000002" y2="580.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.059" x2="318.059" y1="555.3206000000002" y2="580.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.9998000000001" x2="371.9998000000001" y1="555.3206000000002" y2="580.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="29" x2="116.1396999999999" y1="580.0000000000002" y2="580.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="29" x2="116.1396999999999" y1="604.6794000000002" y2="604.6794000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="29" x2="29" y1="580.0000000000002" y2="604.6794000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="116.1396999999999" y1="580.0000000000002" y2="604.6794000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="183.0997" y1="580.0000000000002" y2="580.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="183.0997" y1="604.6794000000002" y2="604.6794000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="116.1396999999999" x2="116.1396999999999" y1="580.0000000000002" y2="604.6794000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="183.0997" y1="580.0000000000002" y2="604.6794000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="237.0405" y1="580.0000000000002" y2="580.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="237.0405" y1="604.6794000000002" y2="604.6794000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.0997" x2="183.0997" y1="580.0000000000002" y2="604.6794000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0405" x2="237.0405" y1="580.0000000000002" y2="604.6794000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0403" x2="318.0592" y1="580.0000000000002" y2="580.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0403" x2="318.0592" y1="604.6794000000002" y2="604.6794000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="237.0403" x2="237.0403" y1="580.0000000000002" y2="604.6794000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.0592" x2="318.0592" y1="580.0000000000002" y2="604.6794000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.059" x2="371.9998000000001" y1="580.0000000000002" y2="580.0000000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.059" x2="371.9998000000001" y1="604.6794000000002" y2="604.6794000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.059" x2="318.059" y1="580.0000000000002" y2="604.6794000000002"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="371.9998000000001" x2="371.9998000000001" y1="580.0000000000002" y2="604.6794000000002"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="177" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,150.643,460.5) scale(1,1) translate(0,0)" writing-mode="lr" x="150.6428833007816" xml:space="preserve" y="465.0000000000002" zvalue="116">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,277.31,460.5) scale(1,1) translate(0,0)" writing-mode="lr" x="277.3095092773439" xml:space="preserve" y="465.0000000000002" zvalue="119">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,72.2857,493.5) scale(1,1) translate(0,0)" writing-mode="lr" x="72.28571428571445" xml:space="preserve" y="498.0000000000002" zvalue="120">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,72.2857,519) scale(1,1) translate(0,0)" writing-mode="lr" x="72.28571428571445" xml:space="preserve" y="523.5000000000002" zvalue="121">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="171" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,72.2857,544.5) scale(1,1) translate(0,0)" writing-mode="lr" x="72.28571428571445" xml:space="preserve" y="549.0000000000002" zvalue="122">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="170" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,72.2857,570) scale(1,1) translate(0,0)" writing-mode="lr" x="72.28571428571445" xml:space="preserve" y="574.5000000000002" zvalue="123">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,72.2857,595.5) scale(1,1) translate(0,0)" writing-mode="lr" x="72.28571428571445" xml:space="preserve" y="600.0000000000002" zvalue="124">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.2857,177) scale(1,1) translate(0,0)" writing-mode="lr" x="54.29" xml:space="preserve" y="181.5" zvalue="125">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,230.286,177) scale(1,1) translate(0,0)" writing-mode="lr" x="230.29" xml:space="preserve" y="181.5" zvalue="126">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="166" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.7857,201.25) scale(1,1) translate(0,0)" writing-mode="lr" x="60.79" xml:space="preserve" y="205.75" zvalue="127">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.7857,249) scale(1,1) translate(0,0)" writing-mode="lr" x="54.79" xml:space="preserve" y="253.5" zvalue="128">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.7857,272) scale(1,1) translate(0,0)" writing-mode="lr" x="54.79" xml:space="preserve" y="276.5" zvalue="129">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.2857,225.25) scale(1,1) translate(0,0)" writing-mode="lr" x="60.29" xml:space="preserve" y="229.75" zvalue="130">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="348" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1337.37,766.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1337.37" xml:space="preserve" y="770.72" zvalue="178">036</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="349" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1255.91,808.47) scale(1,1) translate(0,0)" writing-mode="lr" x="1255.91" xml:space="preserve" y="812.97" zvalue="182">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1311.61,977.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1311.61" xml:space="preserve" y="982.33" zvalue="183">10kV备用Ⅰ回线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,772.358,770.417) scale(1,1) translate(0,0)" writing-mode="lr" x="772.36" xml:space="preserve" y="774.92" zvalue="195">033</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,690.179,817.833) scale(1,1) translate(0,0)" writing-mode="lr" x="690.1799999999999" xml:space="preserve" y="822.33" zvalue="201">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,611.236,873.275) scale(1,1) translate(0,0)" writing-mode="lr" x="611.24" xml:space="preserve" y="877.77" zvalue="211">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,876.38,81.5) scale(1,1) translate(0,0)" writing-mode="lr" x="876.38" xml:space="preserve" y="86" zvalue="331">35kV勐石线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,940.951,583.429) scale(1,1) translate(0,0)" writing-mode="lr" x="940.95" xml:space="preserve" y="587.9299999999999" zvalue="337">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="265" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,912.446,509.643) scale(1,1) translate(0,0)" writing-mode="lr" x="912.45" xml:space="preserve" y="514.14" zvalue="339">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,905.333,453.143) scale(1,1) translate(0,0)" writing-mode="lr" x="905.33" xml:space="preserve" y="457.64" zvalue="343">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="263" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,921.923,682.282) scale(1,1) translate(0,0)" writing-mode="lr" x="921.92" xml:space="preserve" y="686.78" zvalue="346">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,848.952,498.857) scale(1,1) translate(0,-1.08548e-13)" writing-mode="lr" x="848.95" xml:space="preserve" y="503.36" zvalue="349">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="273" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,942.81,600.357) scale(1,1) translate(0,-1.31038e-13)" writing-mode="lr" x="942.8099999999999" xml:space="preserve" y="604.86" zvalue="350">5000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="300" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1109,359.778) scale(1,1) translate(0,0)" writing-mode="lr" x="1109" xml:space="preserve" y="364.28" zvalue="357">3321</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1181.85,335.032) scale(1,1) translate(0,0)" writing-mode="lr" x="1181.85" xml:space="preserve" y="339.53" zvalue="363">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="344" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1177.3,765.696) scale(1,1) translate(0,0)" writing-mode="lr" x="1177.3" xml:space="preserve" y="770.2" zvalue="398">035</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="345" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1093.77,808.097) scale(1,1) translate(0,0)" writing-mode="lr" x="1093.77" xml:space="preserve" y="812.6" zvalue="402">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="327" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1149.94,977.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1149.94" xml:space="preserve" y="982.33" zvalue="403">10kV石竹河线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="11" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,595,997) scale(1,1) translate(0,0)" writing-mode="lr" x="595" xml:space="preserve" y="1000.5" zvalue="416">10kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,563.893,921.643) scale(1,1) translate(0,0)" writing-mode="lr" x="563.89" xml:space="preserve" y="926.14" zvalue="418">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,745.545,994) scale(1,1) translate(0,0)" writing-mode="lr" x="745.55" xml:space="preserve" y="998.5" zvalue="422">10kV接地变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,988.5,1017) scale(1,1) translate(0,0)" writing-mode="lr" x="988.5" xml:space="preserve" y="1021.5" zvalue="423">10kV2号站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1125,558.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1125" xml:space="preserve" y="563" zvalue="425">10kVⅠ段母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1410.89,780.643) scale(1,1) translate(-2.1676e-12,-1.71387e-13)" writing-mode="lr" x="1410.89" xml:space="preserve" y="785.14" zvalue="429">0121</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1442.32,591.909) scale(1,1) translate(0,0)" writing-mode="lr" x="1442.32" xml:space="preserve" y="596.41" zvalue="433">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,896.692,129.548) scale(1,1) translate(0,0)" writing-mode="lr" x="896.6900000000001" xml:space="preserve" y="134.05" zvalue="439">9</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1132.5,183.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1132.5" xml:space="preserve" y="188.25" zvalue="444">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.068,320.303) scale(1,1) translate(0,0)" writing-mode="lr" x="201.07" xml:space="preserve" y="324.8" zvalue="477">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,306.068,320.303) scale(1,1) translate(0,0)" writing-mode="lr" x="306.07" xml:space="preserve" y="324.8" zvalue="478">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="154.91" y="394.46" zvalue="481"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="53" y="394.46" zvalue="482"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="53" y="351.21" zvalue="483"/></g>
  <g href="小电流装置20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="257.81" y="394.46" zvalue="596"/></g>
  <g href="全站公用_遥控.svg"><rect fill-opacity="0" height="24" width="72.88" x="53" y="307" zvalue="597"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="261">
   <path class="kv35" d="M 543.61 402.86 L 1658.69 402.86" stroke-width="4" zvalue="2"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674266644485" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674266644485"/></metadata>
  <path d="M 543.61 402.86 L 1658.69 402.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="260">
   <path class="kv10" d="M 521.07 721.83 L 1496 721.83" stroke-width="4" zvalue="4"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674266578949" ObjectName="10kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674266578949"/></metadata>
  <path d="M 521.07 721.83 L 1496 721.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="250">
   <use class="kv35" height="20" transform="rotate(0,885.179,308) scale(1.5542,1.35421) translate(-312.868,-77.0191)" width="10" x="877.4075746319403" xlink:href="#Breaker:开关_0" y="294.457895288224" zvalue="20"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924611145732" ObjectName="35kV勐石线331断路器"/>
   <cge:TPSR_Ref TObjectID="6473924611145732"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,885.179,308) scale(1.5542,1.35421) translate(-312.868,-77.0191)" width="10" x="877.4075746319403" y="294.457895288224"/></g>
  <g id="223">
   <use class="kv10" height="20" transform="rotate(0,596.513,767.222) scale(1.5542,1.35421) translate(-209.935,-197.134)" width="10" x="588.7416593572968" xlink:href="#Breaker:开关_0" y="753.6801089273775" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924611080196" ObjectName="10kV1号电容器032断路器"/>
   <cge:TPSR_Ref TObjectID="6473924611080196"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,596.513,767.222) scale(1.5542,1.35421) translate(-209.935,-197.134)" width="10" x="588.7416593572968" y="753.6801089273775"/></g>
  <g id="208">
   <use class="kv10" height="20" transform="rotate(0,916.984,770.333) scale(1.5542,1.35421) translate(-324.209,-197.948)" width="10" x="909.2131301874962" xlink:href="#Breaker:开关_0" y="756.7912201444523" zvalue="79"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924611014660" ObjectName="10kV腾拉拱线034断路器"/>
   <cge:TPSR_Ref TObjectID="6473924611014660"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,916.984,770.333) scale(1.5542,1.35421) translate(-324.209,-197.948)" width="10" x="909.2131301874962" y="756.7912201444523"/></g>
  <g id="943">
   <use class="kv10" height="20" transform="rotate(0,1309.51,765.444) scale(1.5542,1.35421) translate(-464.177,-196.669)" width="10" x="1301.740907965274" xlink:href="#Breaker:开关_0" y="751.9023312555634" zvalue="177"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924610949124" ObjectName="10kV备用Ⅰ回线036断路器"/>
   <cge:TPSR_Ref TObjectID="6473924610949124"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1309.51,765.444) scale(1.5542,1.35421) translate(-464.177,-196.669)" width="10" x="1301.740907965274" y="751.9023312555634"/></g>
  <g id="927">
   <use class="kv10" height="20" transform="rotate(0,745.215,768.333) scale(1.5542,1.35421) translate(-262.959,-197.425)" width="10" x="737.4442412986073" xlink:href="#Breaker:开关_0" y="754.7912201444524" zvalue="194"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924610883588" ObjectName="10kV接地变033断路器"/>
   <cge:TPSR_Ref TObjectID="6473924610883588"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,745.215,768.333) scale(1.5542,1.35421) translate(-262.959,-197.425)" width="10" x="737.4442412986073" y="754.7912201444524"/></g>
  <g id="281">
   <use class="kv35" height="20" transform="rotate(0,884.446,508.143) scale(1.5542,1.35421) translate(-312.606,-129.369)" width="10" x="876.6748542801128" xlink:href="#Breaker:开关_0" y="494.6007524310807" zvalue="338"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924611211268" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924611211268"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,884.446,508.143) scale(1.5542,1.35421) translate(-312.606,-129.369)" width="10" x="876.6748542801128" y="494.6007524310807"/></g>
  <g id="276">
   <use class="kv10" height="20" transform="rotate(0,884.829,683.282) scale(1.5542,1.35421) translate(-312.743,-175.179)" width="10" x="877.0577102515635" xlink:href="#Breaker:开关_0" y="669.7396413199697" zvalue="345"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924611276804" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924611276804"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,884.829,683.282) scale(1.5542,1.35421) translate(-312.743,-175.179)" width="10" x="877.0577102515635" y="669.7396413199697"/></g>
  <g id="343">
   <use class="kv10" height="20" transform="rotate(0,1151.37,767.393) scale(1.5542,1.35421) translate(-407.786,-197.179)" width="10" x="1143.598052309694" xlink:href="#Breaker:开关_0" y="753.8507439539759" zvalue="397"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924611342340" ObjectName="10kV石竹河线035断路器"/>
   <cge:TPSR_Ref TObjectID="6473924611342340"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1151.37,767.393) scale(1.5542,1.35421) translate(-407.786,-197.179)" width="10" x="1143.598052309694" y="753.8507439539759"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="247">
   <use class="kv35" height="30" transform="rotate(0,885.179,238) scale(1,0.733333) translate(0,82.5455)" width="15" x="877.6785714285714" xlink:href="#Disconnector:刀闸_0" y="227.0000000000002" zvalue="22"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450448719877" ObjectName="35kV勐石线3316隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450448719877"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,885.179,238) scale(1,0.733333) translate(0,82.5455)" width="15" x="877.6785714285714" y="227.0000000000002"/></g>
  <g id="245">
   <use class="kv35" height="30" transform="rotate(0,885.179,370) scale(1,0.733333) translate(0,130.545)" width="15" x="877.6785714285714" xlink:href="#Disconnector:刀闸_0" y="359.0000000000001" zvalue="26"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450448523269" ObjectName="35kV勐石线3311隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450448523269"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,885.179,370) scale(1,0.733333) translate(0,130.545)" width="15" x="877.6785714285714" y="359.0000000000001"/></g>
  <g id="238">
   <use class="kv35" height="30" transform="rotate(0,1434.43,452.889) scale(1,0.733333) translate(0,160.687)" width="15" x="1426.928571428571" xlink:href="#Disconnector:刀闸_0" y="441.888888888889" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450448195589" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450448195589"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1434.43,452.889) scale(1,0.733333) translate(0,160.687)" width="15" x="1426.928571428571" y="441.888888888889"/></g>
  <g id="225">
   <use class="kv10" height="26" transform="rotate(0,1125.41,678.393) scale(1.02041,1.0989) translate(-22.3654,-59.7696)" width="14" x="1118.269036706625" xlink:href="#Disconnector:联体手车刀闸_0" y="664.1070620294603" zvalue="56"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450447867909" ObjectName="10kVⅠ段母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450447867909"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1125.41,678.393) scale(1.02041,1.0989) translate(-22.3654,-59.7696)" width="14" x="1118.269036706625" y="664.1070620294603"/></g>
  <g id="248">
   <use class="kv10" height="30" transform="rotate(0,985.675,939.333) scale(1.42857,1.42857) translate(-292.488,-275.371)" width="15" x="974.9603174603175" xlink:href="#Disconnector:令克_0" y="917.904761904762" zvalue="90"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450447278085" ObjectName="10kV2号站用变0348跌落刀闸"/>
   <cge:TPSR_Ref TObjectID="6192450447278085"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,985.675,939.333) scale(1.42857,1.42857) translate(-292.488,-275.371)" width="15" x="974.9603174603175" y="917.904761904762"/></g>
  <g id="947">
   <use class="kv10" height="30" transform="rotate(0,596.75,869.275) scale(1,0.733333) translate(0,312.1)" width="15" x="589.2496145923617" xlink:href="#Disconnector:刀闸_0" y="858.2749011753539" zvalue="209"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450446688261" ObjectName="10kV1号电容器0326隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450446688261"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,596.75,869.275) scale(1,0.733333) translate(0,312.1)" width="15" x="589.2496145923617" y="858.2749011753539"/></g>
  <g id="279">
   <use class="kv35" height="30" transform="rotate(0,884.333,454.143) scale(1,0.733333) translate(0,161.143)" width="15" x="876.832880007327" xlink:href="#Disconnector:刀闸_0" y="443.1428571428569" zvalue="341"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450448982021" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450448982021"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,884.333,454.143) scale(1,0.733333) translate(0,161.143)" width="15" x="876.832880007327" y="443.1428571428569"/></g>
  <g id="283">
   <use class="kv35" height="30" transform="rotate(0,1132.39,360.778) scale(1,0.733333) translate(0,127.192)" width="15" x="1124.888888888889" xlink:href="#Disconnector:刀闸_0" y="349.7777777777778" zvalue="356"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450449047557" ObjectName="35kV1号站用变3321隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450449047557"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1132.39,360.778) scale(1,0.733333) translate(0,127.192)" width="15" x="1124.888888888889" y="349.7777777777778"/></g>
  <g id="24">
   <use class="kv10" height="36" transform="rotate(0,1444.57,780.857) scale(1.42857,1.42857) translate(-430.371,-226.543)" width="14" x="1434.571428571428" xlink:href="#Disconnector:联体手车刀闸1_0" y="755.1428571428571" zvalue="428"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450450423813" ObjectName="0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450450423813"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1444.57,780.857) scale(1.42857,1.42857) translate(-430.371,-226.543)" width="14" x="1434.571428571428" y="755.1428571428571"/></g>
  <g id="9">
   <use class="kv35" height="15" transform="rotate(0,900.359,150.423) scale(4.56667,4.56667) translate(-667.533,-90.7339)" width="20" x="854.6923076923077" xlink:href="#Disconnector:石竹河变隔刀_0" y="116.1733062330622" zvalue="438"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450450620421" ObjectName="35kV勐石线3319隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450450620421"/></metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,900.359,150.423) scale(4.56667,4.56667) translate(-667.533,-90.7339)" width="20" x="854.6923076923077" y="116.1733062330622"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="246">
   <use class="kv35" height="20" transform="rotate(270,922.857,341.531) scale(-1.42857,1.42857) translate(-1566.71,-98.1737)" width="10" x="915.7141710911687" xlink:href="#GroundDisconnector:地刀_0" y="327.2455360959888" zvalue="24"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450448654341" ObjectName="35kV勐石线33117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450448654341"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,922.857,341.531) scale(-1.42857,1.42857) translate(-1566.71,-98.1737)" width="10" x="915.7141710911687" y="327.2455360959888"/></g>
  <g id="244">
   <use class="kv35" height="20" transform="rotate(270,922.857,197.531) scale(-1.42857,1.42857) translate(-1566.71,-54.9737)" width="10" x="915.7141710911687" xlink:href="#GroundDisconnector:地刀_0" y="183.2455360959889" zvalue="28"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450448457733" ObjectName="35kV勐石线33167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450448457733"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,922.857,197.531) scale(-1.42857,1.42857) translate(-1566.71,-54.9737)" width="10" x="915.7141710911687" y="183.2455360959889"/></g>
  <g id="243">
   <use class="kv35" height="20" transform="rotate(270,922.857,273.531) scale(-1.42857,1.42857) translate(-1566.71,-77.7737)" width="10" x="915.7141710911687" xlink:href="#GroundDisconnector:地刀_0" y="259.2455360959889" zvalue="30"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450448326661" ObjectName="35kV勐石线33160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450448326661"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,922.857,273.531) scale(-1.42857,1.42857) translate(-1566.71,-77.7737)" width="10" x="915.7141710911687" y="259.2455360959889"/></g>
  <g id="235">
   <use class="kv35" height="20" transform="rotate(90,1401.22,476.42) scale(-1.42857,1.42857) translate(-2379.93,-138.64)" width="10" x="1394.07528220228" xlink:href="#GroundDisconnector:地刀_0" y="462.1344249848778" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450448130053" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450448130053"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1401.22,476.42) scale(-1.42857,1.42857) translate(-2379.93,-138.64)" width="10" x="1394.07528220228" y="462.1344249848778"/></g>
  <g id="233">
   <use class="kv35" height="20" transform="rotate(270,1469.88,425.753) scale(-1.42857,1.42857) translate(-2496.66,-123.44)" width="10" x="1462.741948868947" xlink:href="#GroundDisconnector:地刀_0" y="411.467758318211" zvalue="44"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450447998981" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450447998981"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1469.88,425.753) scale(-1.42857,1.42857) translate(-2496.66,-123.44)" width="10" x="1462.741948868947" y="411.467758318211"/></g>
  <g id="219">
   <use class="kv10" height="20" transform="rotate(270,635.123,818.148) scale(-1.42857,1.42857) translate(-1077.57,-241.159)" width="10" x="627.9801587301583" xlink:href="#GroundDisconnector:地刀_0" y="803.8618914104643" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450447736837" ObjectName="10kV1号电容器03260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450447736837"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,635.123,818.148) scale(-1.42857,1.42857) translate(-1077.57,-241.159)" width="10" x="627.9801587301583" y="803.8618914104643"/></g>
  <g id="204">
   <use class="kv10" height="20" transform="rotate(270,958.984,815.325) scale(-1.42857,1.42857) translate(-1628.13,-240.312)" width="10" x="951.8412698412702" xlink:href="#GroundDisconnector:地刀_0" y="801.0396691882422" zvalue="85"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450447474693" ObjectName="10kV腾拉拱线03467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450447474693"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,958.984,815.325) scale(-1.42857,1.42857) translate(-1628.13,-240.312)" width="10" x="951.8412698412702" y="801.0396691882422"/></g>
  <g id="938">
   <use class="kv10" height="20" transform="rotate(90,1283.58,808.839) scale(1.42857,1.42857) translate(-382.932,-238.366)" width="10" x="1276.440476190476" xlink:href="#GroundDisconnector:地刀_0" y="794.5532071825028" zvalue="181"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450447081477" ObjectName="10kV备用Ⅰ回线03667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450447081477"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1283.58,808.839) scale(1.42857,1.42857) translate(-382.932,-238.366)" width="10" x="1276.440476190476" y="794.5532071825028"/></g>
  <g id="922">
   <use class="kv10" height="20" transform="rotate(90,718.179,817.548) scale(1.42857,1.42857) translate(-213.311,-240.979)" width="10" x="711.0357142857146" xlink:href="#GroundDisconnector:地刀_0" y="803.2618918547407" zvalue="200"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450446819333" ObjectName="10kV接地变03367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450446819333"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,718.179,817.548) scale(1.42857,1.42857) translate(-213.311,-240.979)" width="10" x="711.0357142857146" y="803.2618918547407"/></g>
  <g id="275">
   <use class="kv35" height="20" transform="rotate(90,848.952,482.286) scale(1.42857,1.42857) translate(-252.543,-140.4)" width="10" x="841.8095238095239" xlink:href="#GroundDisconnector:地刀_0" y="467.9999999999999" zvalue="347"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450448916485" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450448916485"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,848.952,482.286) scale(1.42857,1.42857) translate(-252.543,-140.4)" width="10" x="841.8095238095239" y="467.9999999999999"/></g>
  <g id="288">
   <use class="kv35" height="20" transform="rotate(0,1164.73,336.032) scale(-1.42857,1.42857) translate(-1977.9,-96.5238)" width="10" x="1157.587301587302" xlink:href="#GroundDisconnector:地刀_0" y="321.7460317460317" zvalue="362"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450449178629" ObjectName="35kV1号站用变33217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450449178629"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1164.73,336.032) scale(-1.42857,1.42857) translate(-1977.9,-96.5238)" width="10" x="1157.587301587302" y="321.7460317460317"/></g>
  <g id="339">
   <use class="kv10" height="20" transform="rotate(90,1123.37,807.607) scale(1.42857,1.42857) translate(-334.868,-237.996)" width="10" x="1116.226191963468" xlink:href="#GroundDisconnector:地刀_0" y="793.3214152199882" zvalue="401"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450449768453" ObjectName="10kV石竹河线03567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450449768453"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1123.37,807.607) scale(1.42857,1.42857) translate(-334.868,-237.996)" width="10" x="1116.226191963468" y="793.3214152199882"/></g>
  <g id="4">
   <use class="kv10" height="20" transform="rotate(90,564.286,904.143) scale(1.42857,1.42857) translate(-167.143,-266.957)" width="10" x="557.1428571428571" xlink:href="#GroundDisconnector:地刀_0" y="889.8571428571428" zvalue="417"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450450096133" ObjectName="10kV1号电容器03267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450450096133"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,564.286,904.143) scale(1.42857,1.42857) translate(-167.143,-266.957)" width="10" x="557.1428571428571" y="889.8571428571428"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="220">
   <use class="kv10" height="26" transform="rotate(90,566.961,814.252) scale(-1,1) translate(-1133.92,0)" width="12" x="560.9607929238764" xlink:href="#Accessory:避雷器1_0" y="801.2523676009407" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450447802373" ObjectName="10kV1号电容器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,566.961,814.252) scale(-1,1) translate(-1133.92,0)" width="12" x="560.9607929238764" y="801.2523676009407"/></g>
  <g id="205">
   <use class="kv10" height="26" transform="rotate(90,890.739,815.363) scale(1,1) translate(0,0)" width="12" x="884.7385707016547" xlink:href="#Accessory:避雷器1_0" y="802.3634787120518" zvalue="84"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450447540229" ObjectName="10kV腾拉拱线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,890.739,815.363) scale(1,1) translate(0,0)" width="12" x="884.7385707016547" y="802.3634787120518"/></g>
  <g id="940">
   <use class="kv10" height="26" transform="rotate(270,1329.71,817.583) scale(-1,1) translate(-2659.42,0)" width="12" x="1323.710792923877" xlink:href="#Accessory:避雷器1_0" y="804.5826587869897" zvalue="180"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450447147013" ObjectName="10kV备用Ⅰ回线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1329.71,817.583) scale(-1,1) translate(-2659.42,0)" width="12" x="1323.710792923877" y="804.5826587869897"/></g>
  <g id="924">
   <use class="kv10" height="26" transform="rotate(270,764.516,817.472) scale(-1,1) translate(-1529.03,0)" width="12" x="758.5163484794324" xlink:href="#Accessory:避雷器1_0" y="804.4715474325258" zvalue="199"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450446884869" ObjectName="10kV接地变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,764.516,817.472) scale(-1,1) translate(-1529.03,0)" width="12" x="758.5163484794324" y="804.4715474325258"/></g>
  <g id="1075">
   <use class="kv10" height="26" transform="rotate(0,1017.38,937.968) scale(-1,1) translate(-2034.75,0)" width="12" x="1011.377459590544" xlink:href="#Accessory:避雷器1_0" y="924.9676767676767" zvalue="277"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450446491653" ObjectName="10kV腾拉拱线避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1017.38,937.968) scale(-1,1) translate(-2034.75,0)" width="12" x="1011.377459590544" y="924.9676767676767"/></g>
  <g id="268">
   <use class="kv10" height="26" transform="rotate(90,848.076,617.346) scale(-1,1) translate(-1696.15,0)" width="12" x="842.0758722889557" xlink:href="#Accessory:避雷器1_0" y="604.3463787875651" zvalue="353"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450448785413" ObjectName="#1主变10kV侧避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,848.076,617.346) scale(-1,1) translate(-1696.15,0)" width="12" x="842.0758722889557" y="604.3463787875651"/></g>
  <g id="308">
   <use class="kv10" height="26" transform="rotate(90,847.889,645.333) scale(-1,1) translate(-1695.78,0)" width="12" x="841.8888888888889" xlink:href="#Accessory:避雷器1_0" y="632.3333333333333" zvalue="378"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450449244165" ObjectName="#1主变10kV侧避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,847.889,645.333) scale(-1,1) translate(-1695.78,0)" width="12" x="841.8888888888889" y="632.3333333333333"/></g>
  <g id="340">
   <use class="kv10" height="26" transform="rotate(270,1171.57,819.531) scale(-1,1) translate(-2343.14,0)" width="12" x="1165.567937268298" xlink:href="#Accessory:避雷器1_0" y="806.5310714854022" zvalue="400"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450449833989" ObjectName="10kV石竹河线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1171.57,819.531) scale(-1,1) translate(-2343.14,0)" width="12" x="1165.567937268298" y="806.5310714854022"/></g>
  <g id="19">
   <use class="kv10" height="30" transform="rotate(0,1130.5,607.5) scale(-1.7,-1.7) translate(-1785,-954.353)" width="30" x="1105" xlink:href="#Accessory:ptblq_0" y="582" zvalue="424"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450450292741" ObjectName="10kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1130.5,607.5) scale(-1.7,-1.7) translate(-1785,-954.353)" width="30" x="1105" y="582"/></g>
  <g id="22">
   <use class="kv10" height="26" transform="rotate(270,1152,646) scale(1,1) translate(0,0)" width="12" x="1146" xlink:href="#Accessory:避雷器1_0" y="633" zvalue="426"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450450358277" ObjectName="10kVⅠ段母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1152,646) scale(1,1) translate(0,0)" width="12" x="1146" y="633"/></g>
  <g id="28">
   <use class="kv35" height="30" transform="rotate(0,1439.59,555.864) scale(-1.7,1.7) translate(-2275.91,-218.385)" width="30" x="1414.089735852677" xlink:href="#Accessory:ptblq_0" y="530.3636363636364" zvalue="432"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450450489349" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1439.59,555.864) scale(-1.7,1.7) translate(-2275.91,-218.385)" width="30" x="1414.089735852677" y="530.3636363636364"/></g>
  <g id="30">
   <use class="kv35" height="26" transform="rotate(90,1401.55,516.909) scale(-1,1) translate(-2803.09,0)" width="12" x="1395.545454545455" xlink:href="#Accessory:避雷器1_0" y="503.9090909090909" zvalue="434"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450450554885" ObjectName="35kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1401.55,516.909) scale(-1,1) translate(-2803.09,0)" width="12" x="1395.545454545455" y="503.9090909090909"/></g>
  <g id="34">
   <use class="kv35" height="15" transform="rotate(0,958.138,156.199) scale(2.95285,2.95285) translate(-614.13,-88.655)" width="20" x="928.609756097561" xlink:href="#Accessory:石竹河附属_0" y="134.0528455284553" zvalue="441"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450450685957" ObjectName="35kV勐石线附属"/>
   </metadata>
  <rect fill="white" height="15" opacity="0" stroke="white" transform="rotate(0,958.138,156.199) scale(2.95285,2.95285) translate(-614.13,-88.655)" width="20" x="928.609756097561" y="134.0528455284553"/></g>
  <g id="137">
   <use class="kv10" height="22" transform="rotate(0,884.636,632.5) scale(0.772727,0.772727) translate(258.824,183.529)" width="12" x="880" xlink:href="#Accessory:传输线_0" y="624" zvalue="610"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450457501701" ObjectName="#1主变10kV侧电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,884.636,632.5) scale(0.772727,0.772727) translate(258.824,183.529)" width="12" x="880" y="624"/></g>
  <g id="197">
   <use class="kv10" height="22" transform="rotate(0,596.636,841.833) scale(0.772727,0.772727) translate(174.118,245.098)" width="12" x="592" xlink:href="#Accessory:传输线_0" y="833.3333435058597" zvalue="613"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450457567237" ObjectName="1号电容器电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,596.636,841.833) scale(0.772727,0.772727) translate(174.118,245.098)" width="12" x="592" y="833.3333435058597"/></g>
  <g id="201">
   <use class="kv10" height="22" transform="rotate(0,745.303,841.833) scale(0.772727,0.772727) translate(217.843,245.098)" width="12" x="740.6666666666667" xlink:href="#Accessory:传输线_0" y="833.333343823751" zvalue="616"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450457632773" ObjectName="接地变电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,745.303,841.833) scale(0.772727,0.772727) translate(217.843,245.098)" width="12" x="740.6666666666667" y="833.333343823751"/></g>
  <g id="210">
   <use class="kv10" height="22" transform="rotate(0,917.303,841.833) scale(0.772727,0.772727) translate(268.431,245.098)" width="12" x="912.6666666666666" xlink:href="#Accessory:传输线_0" y="833.3333433469136" zvalue="619"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450457698309" ObjectName="腾拉拱线电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,917.303,841.833) scale(0.772727,0.772727) translate(268.431,245.098)" width="12" x="912.6666666666666" y="833.3333433469136"/></g>
  <g id="231">
   <use class="kv10" height="22" transform="rotate(0,1151.3,841.833) scale(0.772727,0.772727) translate(337.255,245.098)" width="12" x="1146.666666666667" xlink:href="#Accessory:传输线_0" y="833.3333435058594" zvalue="622"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450457763845" ObjectName="石竹河线电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1151.3,841.833) scale(0.772727,0.772727) translate(337.255,245.098)" width="12" x="1146.666666666667" y="833.3333435058594"/></g>
  <g id="234">
   <use class="kv10" height="22" transform="rotate(0,1309.97,841.833) scale(0.772727,0.772727) translate(383.922,245.098)" width="12" x="1305.333333333333" xlink:href="#Accessory:传输线_0" y="833.3333433469136" zvalue="625"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450457829381" ObjectName="备用Ⅰ回线电缆"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1309.97,841.833) scale(0.772727,0.772727) translate(383.922,245.098)" width="12" x="1305.333333333333" y="833.3333433469136"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="217">
   <path class="kv35" d="M 885.27 138.1 L 885.27 227.36" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@0" LinkObjectIDznd="247@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 885.27 138.1 L 885.27 227.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv35" d="M 885.24 248.81 L 885.13 295.04" stroke-width="1" zvalue="71"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="247@1" LinkObjectIDznd="250@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 885.24 248.81 L 885.13 295.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv35" d="M 885.28 320.93 L 885.27 359.36" stroke-width="1" zvalue="72"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@1" LinkObjectIDznd="245@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 885.28 320.93 L 885.27 359.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv35" d="M 908.93 341.6 L 885.27 341.6" stroke-width="1" zvalue="74"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@0" LinkObjectIDznd="215" MaxPinNum="2"/>
   </metadata>
  <path d="M 908.93 341.6 L 885.27 341.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="kv35" d="M 908.93 273.6 L 885.18 273.6" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="243@0" LinkObjectIDznd="216" MaxPinNum="2"/>
   </metadata>
  <path d="M 908.93 273.6 L 885.18 273.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="kv35" d="M 908.93 197.6 L 885.27 197.6" stroke-width="1" zvalue="76"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="244@0" LinkObjectIDznd="217" MaxPinNum="2"/>
   </metadata>
  <path d="M 908.93 197.6 L 885.27 197.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="kv10" d="M 985.56 956.83 L 985.56 968.78" stroke-width="1" zvalue="92"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@1" LinkObjectIDznd="16@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 985.56 956.83 L 985.56 968.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv10" d="M 1125.4 631.19 L 1125.4 665.98" stroke-width="1" zvalue="100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="19@0" LinkObjectIDznd="225@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1125.4 631.19 L 1125.4 665.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv10" d="M 732.11 817.62 L 745.2 817.62" stroke-width="1" zvalue="301"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="922@0" LinkObjectIDznd="152" MaxPinNum="2"/>
   </metadata>
  <path d="M 732.11 817.62 L 745.2 817.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv10" d="M 752.15 817.5 L 745.2 817.5" stroke-width="1" zvalue="302"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="924@0" LinkObjectIDznd="94" MaxPinNum="2"/>
   </metadata>
  <path d="M 752.15 817.5 L 745.2 817.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv10" d="M 596.81 880.09 L 596.81 916.95" stroke-width="1" zvalue="321"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="947@1" LinkObjectIDznd="2@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 596.81 880.09 L 596.81 916.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="280">
   <path class="kv35" d="M 884.55 536.8 L 884.55 521.08" stroke-width="1" zvalue="340"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="282@0" LinkObjectIDznd="281@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 884.55 536.8 L 884.55 521.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="278">
   <path class="kv35" d="M 884.39 495.19 L 884.39 464.95" stroke-width="1" zvalue="342"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="281@0" LinkObjectIDznd="279@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 884.39 495.19 L 884.39 464.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="274">
   <path class="kv35" d="M 862.88 482.36 L 884.39 482.36" stroke-width="1" zvalue="348"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="275@0" LinkObjectIDznd="278" MaxPinNum="2"/>
   </metadata>
  <path d="M 862.88 482.36 L 884.39 482.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="303">
   <path class="kv35" d="M 1434.52 402.86 L 1434.52 442.25" stroke-width="1" zvalue="372"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@2" LinkObjectIDznd="238@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1434.52 402.86 L 1434.52 442.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="304">
   <path class="kv35" d="M 1434.49 463.7 L 1434.49 532.18" stroke-width="1" zvalue="373"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="238@1" LinkObjectIDznd="28@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1434.49 463.7 L 1434.49 532.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="305">
   <path class="kv35" d="M 1455.96 425.82 L 1434.52 425.82" stroke-width="1" zvalue="374"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="233@0" LinkObjectIDznd="303" MaxPinNum="2"/>
   </metadata>
  <path d="M 1455.96 425.82 L 1434.52 425.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="306">
   <path class="kv35" d="M 1415.15 476.35 L 1434.49 476.35" stroke-width="1" zvalue="375"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@0" LinkObjectIDznd="304" MaxPinNum="2"/>
   </metadata>
  <path d="M 1415.15 476.35 L 1434.49 476.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="307">
   <path class="kv10" d="M 860.44 617.31 L 884.76 617.31" stroke-width="1" zvalue="376"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="139" MaxPinNum="2"/>
   </metadata>
  <path d="M 860.44 617.31 L 884.76 617.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="309">
   <path class="kv10" d="M 860.26 645.3 L 884.76 645.3" stroke-width="1" zvalue="379"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="308@0" LinkObjectIDznd="139" MaxPinNum="2"/>
   </metadata>
  <path d="M 860.26 645.3 L 884.76 645.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="310">
   <path class="kv10" d="M 621.19 818.22 L 596.26 818.22" stroke-width="1" zvalue="380"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@0" LinkObjectIDznd="147" MaxPinNum="2"/>
   </metadata>
  <path d="M 621.19 818.22 L 596.26 818.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv10" d="M 579.33 814.22 L 596.26 814.22" stroke-width="1" zvalue="381"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@0" LinkObjectIDznd="147" MaxPinNum="2"/>
   </metadata>
  <path d="M 579.33 814.22 L 596.26 814.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="317">
   <path class="kv10" d="M 1125.43 690.83 L 1125.43 721.83" stroke-width="1" zvalue="387"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="225@1" LinkObjectIDznd="260@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1125.43 690.83 L 1125.43 721.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="318">
   <path class="kv10" d="M 945.06 815.4 L 917.76 815.4" stroke-width="1" zvalue="388"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="204@0" LinkObjectIDznd="89" MaxPinNum="2"/>
   </metadata>
  <path d="M 945.06 815.4 L 917.76 815.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="319">
   <path class="kv10" d="M 903.11 815.4 L 917.76 815.4" stroke-width="1" zvalue="389"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="318" MaxPinNum="2"/>
   </metadata>
  <path d="M 903.11 815.4 L 917.76 815.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="324">
   <path class="kv10" d="M 985.79 920.4 L 985.79 899.33 L 917.76 899.33" stroke-width="1" zvalue="394"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@0" LinkObjectIDznd="89" MaxPinNum="2"/>
   </metadata>
  <path d="M 985.79 920.4 L 985.79 899.33 L 917.76 899.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="326">
   <path class="kv10" d="M 1017.34 925.6 L 1017.34 911.56 L 985.79 911.56" stroke-width="1" zvalue="395"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1075@0" LinkObjectIDznd="324" MaxPinNum="2"/>
   </metadata>
  <path d="M 1017.34 925.6 L 1017.34 911.56 L 985.79 911.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="332">
   <path class="kv10" d="M 1159.2 819.56 L 1151.26 819.56" stroke-width="1" zvalue="409"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="340@0" LinkObjectIDznd="90" MaxPinNum="2"/>
   </metadata>
  <path d="M 1159.2 819.56 L 1151.26 819.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv10" d="M 578.21 904.21 L 596.81 904.21" stroke-width="1" zvalue="418"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="4@0" LinkObjectIDznd="74" MaxPinNum="2"/>
   </metadata>
  <path d="M 578.21 904.21 L 596.81 904.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv10" d="M 1139.63 645.97 L 1125.4 645.97" stroke-width="1" zvalue="427"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@0" LinkObjectIDznd="194" MaxPinNum="2"/>
   </metadata>
  <path d="M 1139.63 645.97 L 1125.4 645.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv10" d="M 1444.55 756.5 L 1444.55 721.83" stroke-width="1" zvalue="429"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@0" LinkObjectIDznd="260@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1444.55 756.5 L 1444.55 721.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv10" d="M 1444.6 805.24 L 1444.6 828.86" stroke-width="1" zvalue="430"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@1" LinkObjectIDznd="" MaxPinNum="2"/>
   </metadata>
  <path d="M 1444.6 805.24 L 1444.6 828.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv35" d="M 1413.91 516.88 L 1434.49 516.88" stroke-width="1" zvalue="435"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="30@0" LinkObjectIDznd="304" MaxPinNum="2"/>
   </metadata>
  <path d="M 1413.91 516.88 L 1434.49 516.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv35" d="M 885.52 149.66 L 885.27 149.66" stroke-width="1" zvalue="439"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="217" MaxPinNum="2"/>
   </metadata>
  <path d="M 885.52 149.66 L 885.27 149.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv35" d="M 941.16 156.2 L 941.16 156.51" stroke-width="1" zvalue="442"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@0" LinkObjectIDznd="9@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 941.16 156.2 L 941.16 156.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv35" d="M 1131.5 295.74 L 1132.48 350.14" stroke-width="1" zvalue="444"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="283@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1131.5 295.74 L 1132.48 350.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv35" d="M 1164.66 322.1 L 1164.66 310.02 L 1131.76 310.02" stroke-width="1" zvalue="445"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="288@0" LinkObjectIDznd="42" MaxPinNum="2"/>
   </metadata>
  <path d="M 1164.66 322.1 L 1164.66 310.02 L 1131.76 310.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv35" d="M 884.42 443.51 L 884.42 402.86" stroke-width="1" zvalue="448"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="279@0" LinkObjectIDznd="261@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 884.42 443.51 L 884.42 402.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv35" d="M 885.24 380.81 L 885.24 402.86" stroke-width="1" zvalue="449"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="245@1" LinkObjectIDznd="261@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 885.24 380.81 L 885.24 402.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv35" d="M 1132.45 371.59 L 1132.45 402.86" stroke-width="1" zvalue="453"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="283@1" LinkObjectIDznd="261@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.45 371.59 L 1132.45 402.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv10" d="M 884.78 670.33 L 884.78 659.94" stroke-width="1" zvalue="544"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="134@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 884.78 670.33 L 884.78 659.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv10" d="M 884.76 653.16 L 884.76 605.79" stroke-width="1" zvalue="545"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="134@0" LinkObjectIDznd="282@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 884.76 653.16 L 884.76 605.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv10" d="M 884.93 696.21 L 884.96 707.06" stroke-width="1" zvalue="548"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@1" LinkObjectIDznd="140@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 884.93 696.21 L 884.96 707.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="kv10" d="M 884.93 713.84 L 884.93 721.83" stroke-width="1" zvalue="549"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="260@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 884.93 713.84 L 884.93 721.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv10" d="M 596.62 780.15 L 596.62 790.06" stroke-width="1" zvalue="552"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@1" LinkObjectIDznd="145@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 596.62 780.15 L 596.62 790.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv10" d="M 596.26 796.84 L 596.26 858.64" stroke-width="1" zvalue="553"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="947@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 596.26 796.84 L 596.26 858.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv10" d="M 596.46 754.27 L 596.46 741.44" stroke-width="1" zvalue="556"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@0" LinkObjectIDznd="148@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 596.46 754.27 L 596.46 741.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv10" d="M 596.26 734.66 L 596.26 721.83" stroke-width="1" zvalue="557"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="148@0" LinkObjectIDznd="260@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 596.26 734.66 L 596.26 721.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv10" d="M 745.2 925.29 L 745.2 797.84" stroke-width="1" zvalue="560"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@0" LinkObjectIDznd="151@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 745.2 925.29 L 745.2 797.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv10" d="M 745.22 791.06 L 745.32 781.27" stroke-width="1" zvalue="561"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@1" LinkObjectIDznd="927@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 745.22 791.06 L 745.32 781.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv10" d="M 745.16 755.38 L 745.16 742.44" stroke-width="1" zvalue="564"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="927@0" LinkObjectIDznd="154@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 745.16 755.38 L 745.16 742.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv10" d="M 744.76 735.66 L 744.76 721.83" stroke-width="1" zvalue="565"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@0" LinkObjectIDznd="260@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 744.76 735.66 L 744.76 721.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="158">
   <path class="kv10" d="M 916.93 757.38 L 916.96 745.44" stroke-width="1" zvalue="568"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@0" LinkObjectIDznd="157@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 916.93 757.38 L 916.96 745.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="159">
   <path class="kv10" d="M 916.93 738.66 L 916.93 721.83" stroke-width="1" zvalue="569"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="260@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 916.93 738.66 L 916.93 721.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv10" d="M 917.78 794.56 L 917.78 783.27" stroke-width="1" zvalue="573"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@1" LinkObjectIDznd="208@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 917.78 794.56 L 917.78 783.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv10" d="M 1137.3 807.68 L 1151.26 807.68" stroke-width="1" zvalue="574"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="339@0" LinkObjectIDznd="90" MaxPinNum="2"/>
   </metadata>
  <path d="M 1137.3 807.68 L 1151.26 807.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv10" d="M 1151.43 721.83 L 1151.43 736.16" stroke-width="1" zvalue="580"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="260@6" LinkObjectIDznd="198@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1151.43 721.83 L 1151.43 736.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="218">
   <path class="kv10" d="M 1151.46 742.94 L 1151.46 754.44" stroke-width="1" zvalue="581"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@1" LinkObjectIDznd="343@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1151.46 742.94 L 1151.46 754.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="kv10" d="M 1151.28 790.06 L 1151.28 780.33" stroke-width="1" zvalue="585"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@1" LinkObjectIDznd="343@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1151.28 790.06 L 1151.28 780.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="227">
   <path class="kv10" d="M 1309.46 752.49 L 1309.46 739.94" stroke-width="1" zvalue="588"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="943@0" LinkObjectIDznd="226@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1309.46 752.49 L 1309.46 739.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="kv10" d="M 1309.26 733.16 L 1309.26 721.83" stroke-width="1" zvalue="589"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@0" LinkObjectIDznd="260@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1309.26 733.16 L 1309.26 721.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="kv10" d="M 1309.62 788.22 L 1309.62 778.38" stroke-width="1" zvalue="603"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@1" LinkObjectIDznd="943@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1309.62 788.22 L 1309.62 778.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv10" d="M 1297.51 808.91 L 1309.59 808.91" stroke-width="1" zvalue="605"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="938@0" LinkObjectIDznd="136" MaxPinNum="2"/>
   </metadata>
  <path d="M 1297.51 808.91 L 1309.59 808.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv10" d="M 1317.34 817.62 L 1309.59 817.62" stroke-width="1" zvalue="606"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="940@0" LinkObjectIDznd="136" MaxPinNum="2"/>
   </metadata>
  <path d="M 1317.34 817.62 L 1309.59 817.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv10" d="M 917.76 917.48 L 917.76 801.34" stroke-width="1" zvalue="607"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@0" LinkObjectIDznd="175@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 917.76 917.48 L 917.76 801.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv10" d="M 1151.26 917.48 L 1151.26 796.84" stroke-width="1" zvalue="608"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="338@0" LinkObjectIDznd="221@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1151.26 917.48 L 1151.26 796.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv10" d="M 1309.59 795 L 1309.59 917.48" stroke-width="1" zvalue="609"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@0" LinkObjectIDznd="937@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1309.59 795 L 1309.59 917.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="kv10" d="M 884.64 624.77 L 884.76 624.77" stroke-width="1" zvalue="611"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="139" MaxPinNum="2"/>
   </metadata>
  <path d="M 884.64 624.77 L 884.76 624.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="kv10" d="M 596.64 834.11 L 596.26 834.11" stroke-width="1" zvalue="614"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="147" MaxPinNum="2"/>
   </metadata>
  <path d="M 596.64 834.11 L 596.26 834.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv10" d="M 745.3 834.11 L 745.2 834.11" stroke-width="1" zvalue="617"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201@0" LinkObjectIDznd="152" MaxPinNum="2"/>
   </metadata>
  <path d="M 745.3 834.11 L 745.2 834.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="230">
   <path class="kv10" d="M 917.3 834.11 L 917.76 834.11" stroke-width="1" zvalue="620"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="210@0" LinkObjectIDznd="89" MaxPinNum="2"/>
   </metadata>
  <path d="M 917.3 834.11 L 917.76 834.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="kv10" d="M 1151.3 834.11 L 1151.26 834.11" stroke-width="1" zvalue="623"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@0" LinkObjectIDznd="90" MaxPinNum="2"/>
   </metadata>
  <path d="M 1151.3 834.11 L 1151.26 834.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="236">
   <path class="kv10" d="M 1309.97 834.11 L 1309.59 834.11" stroke-width="1" zvalue="626"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="234@0" LinkObjectIDznd="136" MaxPinNum="2"/>
   </metadata>
  <path d="M 1309.97 834.11 L 1309.59 834.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="203">
   <use class="kv10" height="30" transform="rotate(180,912.774,942.247) scale(1.99385,1.86923) translate(-447.525,-425.126)" width="15" x="897.8205000587269" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="914.2083435058589" zvalue="87"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450447343621" ObjectName="10kV腾拉拱线"/>
   <cge:TPSR_Ref TObjectID="6192450447343621"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,912.774,942.247) scale(1.99385,1.86923) translate(-447.525,-425.126)" width="15" x="897.8205000587269" y="914.2083435058589"/></g>
  <g id="937">
   <use class="kv10" height="30" transform="rotate(180,1304.61,942.247) scale(1.99385,1.86923) translate(-642.837,-425.126)" width="15" x="1289.65383339206" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="914.2083433882121" zvalue="182"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450446950405" ObjectName="10kV备用Ⅰ回线"/>
   <cge:TPSR_Ref TObjectID="6192450446950405"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1304.61,942.247) scale(1.99385,1.86923) translate(-642.837,-425.126)" width="15" x="1289.65383339206" y="914.2083433882121"/></g>
  <g id="338">
   <use class="kv10" height="30" transform="rotate(180,1146.27,942.247) scale(1.99385,1.86923) translate(-563.915,-425.126)" width="15" x="1131.320500058727" xlink:href="#EnergyConsumer:负荷带壁雷器_0" y="914.2083434563324" zvalue="402"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450449637381" ObjectName="10kV石竹河线"/>
   <cge:TPSR_Ref TObjectID="6192450449637381"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1146.27,942.247) scale(1.99385,1.86923) translate(-563.915,-425.126)" width="15" x="1131.320500058727" y="914.2083434563324"/></g>
  <g id="10">
   <use class="kv10" height="37" transform="rotate(0,745.045,953.778) scale(1.81818,1.71772) translate(-326.27,-385.241)" width="22" x="725.0454545454545" xlink:href="#EnergyConsumer:石竹河接地变_0" y="922" zvalue="421"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450450161669" ObjectName="10kV接地变"/>
   </metadata>
  <rect fill="white" height="37" opacity="0" stroke="white" transform="rotate(0,745.045,953.778) scale(1.81818,1.71772) translate(-326.27,-385.241)" width="22" x="725.0454545454545" y="922"/></g>
  <g id="16">
   <use class="kv10" height="30" transform="rotate(0,985.2,987.5) scale(1.3,1.3) translate(-223.154,-223.385)" width="28" x="967" xlink:href="#EnergyConsumer:站用变YD2022_0" y="968" zvalue="422"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450450227205" ObjectName="10kV2号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,985.2,987.5) scale(1.3,1.3) translate(-223.154,-223.385)" width="28" x="967" y="968"/></g>
  <g id="41">
   <use class="kv35" height="30" transform="rotate(0,1131.5,249.75) scale(3.25,-3.25) translate(-760.846,-292.846)" width="20" x="1099" xlink:href="#EnergyConsumer:回贤变站用变_0" y="201" zvalue="443"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450450751493" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1131.5,249.75) scale(3.25,-3.25) translate(-760.846,-292.846)" width="20" x="1099" y="201"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="ACLineSegmentClass">
  <g id="179">
   <use class="kv35" height="40" transform="rotate(0,885.37,116.636) scale(0.832684,1.08182) translate(175.393,-7.18487)" width="30" x="872.880174237008" xlink:href="#ACLineSegment:线路带壁雷器_0" y="95.00000000000011" zvalue="330"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249406242819" ObjectName="35kV勐石线"/>
   <cge:TPSR_Ref TObjectID="8444249406242819_5066549598420994"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,885.37,116.636) scale(0.832684,1.08182) translate(175.393,-7.18487)" width="30" x="872.880174237008" y="95.00000000000011"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="282">
   <g id="2820">
    <use class="kv35" height="30" transform="rotate(0,884.522,571.143) scale(2.5,2.46667) translate(-512.713,-317.598)" width="24" x="854.52" xlink:href="#PowerTransformer2:可调不带中性点_0" y="534.14" zvalue="336"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874470457347" ObjectName="35"/>
    </metadata>
   </g>
   <g id="2821">
    <use class="kv10" height="30" transform="rotate(0,884.522,571.143) scale(2.5,2.46667) translate(-512.713,-317.598)" width="24" x="854.52" xlink:href="#PowerTransformer2:可调不带中性点_1" y="534.14" zvalue="336"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874470522883" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399466352643" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399466352643"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,884.522,571.143) scale(2.5,2.46667) translate(-512.713,-317.598)" width="24" x="854.52" y="534.14"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="2">
   <use class="kv10" height="50" transform="rotate(0,591.5,949) scale(1.52,1.52) translate(-195.855,-311.658)" width="25" x="572.5" xlink:href="#Compensator:石竹河电容_0" y="911" zvalue="415"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450449965061" ObjectName="10kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192450449965061"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,591.5,949) scale(1.52,1.52) translate(-195.855,-311.658)" width="25" x="572.5" y="911"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="8" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,148.468,200.556) scale(1,1) translate(0,0)" writing-mode="lr" x="148.66" xml:space="preserve" y="206.97" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128622030851" ObjectName="F"/>
   </metadata>
  </g>
  <g id="60">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="60" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,148.468,225.444) scale(1,1) translate(0,0)" writing-mode="lr" x="148.66" xml:space="preserve" y="231.85" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128621506563" ObjectName="F"/>
   </metadata>
  </g>
  <g id="61">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,148.468,247.639) scale(1,1) translate(-2.13163e-14,0)" writing-mode="lr" x="148.7" xml:space="preserve" y="254.13" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128623144963" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,148.468,270.639) scale(1,1) translate(0,-2.86592e-13)" writing-mode="lr" x="148.67" xml:space="preserve" y="277.13" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128623210499" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="7" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,149.234,176.278) scale(1,1) translate(0,0)" writing-mode="lr" x="149.39" xml:space="preserve" y="182.7" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128629174275" ObjectName="LOAD_PSUM"/>
   </metadata>
  </g>
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,146.643,517.556) scale(1,1) translate(-2.6455e-14,0)" writing-mode="lr" x="146.76" xml:space="preserve" y="522.47" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128621637635" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="70">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="70" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,146.643,540.556) scale(1,1) translate(-2.6455e-14,0)" writing-mode="lr" x="146.76" xml:space="preserve" y="545.47" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128621703171" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="71">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,146.643,563.556) scale(1,1) translate(-2.6455e-14,0)" writing-mode="lr" x="146.76" xml:space="preserve" y="568.47" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128621768707" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="67">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="67" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,146.643,492.056) scale(1,1) translate(0,0)" writing-mode="lr" x="146.76" xml:space="preserve" y="496.97" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128621899779" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="72">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,146.643,591.556) scale(1,1) translate(-2.6455e-14,0)" writing-mode="lr" x="146.76" xml:space="preserve" y="596.47" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128622096387" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="200">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,277.31,519.667) scale(1,1) translate(-5.54689e-14,0)" writing-mode="lr" x="277.43" xml:space="preserve" y="524.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128621113347" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="75">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="75" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,277.31,542.667) scale(1,1) translate(-5.54689e-14,0)" writing-mode="lr" x="277.43" xml:space="preserve" y="547.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128621178883" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="76">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,277.31,565.667) scale(1,1) translate(-5.54689e-14,0)" writing-mode="lr" x="277.43" xml:space="preserve" y="570.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128621244419" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,277.31,493.167) scale(1,1) translate(-5.54689e-14,0)" writing-mode="lr" x="277.43" xml:space="preserve" y="498.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128621375491" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="77">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,277.31,592.667) scale(1,1) translate(-5.54689e-14,0)" writing-mode="lr" x="277.43" xml:space="preserve" y="597.58" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128621572099" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="78" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,299.5,176.5) scale(1,1) translate(0,0)" writing-mode="lr" x="299.66" xml:space="preserve" y="182.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128629239811" ObjectName=""/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="85" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,869.909,21.465) scale(1,1) translate(0,0)" writing-mode="lr" x="826.78" xml:space="preserve" y="25.83" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128615739397" ObjectName="P"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="87" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,893.297,994.449) scale(1,1) translate(0,0)" writing-mode="lr" x="850.17" xml:space="preserve" y="998.8099999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128618622979" ObjectName="P"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="88" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1141.99,994.604) scale(1,1) translate(0,0)" writing-mode="lr" x="1098.86" xml:space="preserve" y="998.97" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128625242115" ObjectName="P"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="91" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,869.909,40.0035) scale(1,1) translate(-1.83167e-13,0)" writing-mode="lr" x="826.78" xml:space="preserve" y="44.36" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128615804934" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="95" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,894.067,1009.91) scale(1,1) translate(0,0)" writing-mode="lr" x="850.9400000000001" xml:space="preserve" y="1014.27" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128618688515" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="96" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1142.7,1010.89) scale(1,1) translate(0,0)" writing-mode="lr" x="1099.58" xml:space="preserve" y="1015.25" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128625307653" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="97" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,872.217,57.0035) scale(1,1) translate(-1.83679e-13,0)" writing-mode="lr" x="829.09" xml:space="preserve" y="61.36" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128615870467" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="99" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,894.836,1027.68) scale(1,1) translate(0,1.68229e-12)" writing-mode="lr" x="851.71" xml:space="preserve" y="1032.04" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128618754051" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="100" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1143.42,1026.46) scale(1,1) translate(0,0)" writing-mode="lr" x="1100.29" xml:space="preserve" y="1030.82" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128625373190" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="286">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="286" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1445.04,615.731) scale(1,1) translate(-1.57378e-12,0)" writing-mode="lr" x="1445.16" xml:space="preserve" y="620.64" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128621899779" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="103" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,983.038,470.038) scale(1,1) translate(0,0)" writing-mode="lr" x="982.46" xml:space="preserve" y="474.74" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128622686211" ObjectName="P"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="102" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,984.577,488.577) scale(1,1) translate(0,0)" writing-mode="lr" x="984" xml:space="preserve" y="493.28" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128622751747" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="101" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,984.577,507.885) scale(1,1) translate(0,0)" writing-mode="lr" x="984" xml:space="preserve" y="512.58" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128622948355" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="106">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="106" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,986.115,627.731) scale(1,1) translate(0,0)" writing-mode="lr" x="985.54" xml:space="preserve" y="632.4299999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128622817283" ObjectName="P"/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="105" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,987.654,646.269) scale(1,1) translate(0,3.51813e-13)" writing-mode="lr" x="987.08" xml:space="preserve" y="650.97" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128622882819" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="104" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,987.654,665.577) scale(1,1) translate(0,0)" writing-mode="lr" x="987.08" xml:space="preserve" y="670.28" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128623276035" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="287">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="287" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1125.81,535.731) scale(1,1) translate(-2.43873e-13,0)" writing-mode="lr" x="1125.93" xml:space="preserve" y="540.64" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128621375491" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="108">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="108" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,594.846,1015.73) scale(1,1) translate(0,0)" writing-mode="lr" x="595.04" xml:space="preserve" y="1020.64" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128626683907" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="107" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,594.846,1034.73) scale(1,1) translate(0,-2.7291e-12)" writing-mode="lr" x="595.04" xml:space="preserve" y="1039.64" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128626749443" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="993">
   <use height="30" transform="rotate(0,320.5,139.5) scale(1.27778,1.03333) translate(-57.1739,-4)" width="90" x="263" xlink:href="#State:全站检修_0" y="124" zvalue="475"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549598420994" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,320.5,139.5) scale(1.27778,1.03333) translate(-57.1739,-4)" width="90" x="263" y="124"/></g>
  <g id="897">
   <use height="30" transform="rotate(0,339.295,320.961) scale(0.708333,0.665547) translate(135.335,156.274)" width="30" x="328.67" xlink:href="#State:红绿圆(方形)_0" y="310.98" zvalue="479"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374899544067" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,339.295,320.961) scale(0.708333,0.665547) translate(135.335,156.274)" width="30" x="328.67" y="310.98"/></g>
  <g id="82">
   <use height="30" transform="rotate(0,243.67,320.961) scale(0.708333,0.665547) translate(95.9596,156.274)" width="30" x="233.04" xlink:href="#State:红绿圆(方形)_0" y="310.98" zvalue="480"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562962353750017" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,243.67,320.961) scale(0.708333,0.665547) translate(95.9596,156.274)" width="30" x="233.04" y="310.98"/></g>
 </g>
 <g id="DollyBreakerClass">
  <g id="134">
   <use class="kv10" height="22" transform="rotate(0,884.75,659.75) scale(0.659091,0.659091) translate(453.879,337.5)" width="22" x="877.4999999999999" xlink:href="#DollyBreaker:手车_0" y="652.5" zvalue="543"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450451472389" ObjectName="#1主变10kV侧001断路器小车工作位"/>
   <cge:TPSR_Ref TObjectID="6192450451472389"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,884.75,659.75) scale(0.659091,0.659091) translate(453.879,337.5)" width="22" x="877.4999999999999" y="652.5"/></g>
  <g id="140">
   <use class="kv10" height="22" transform="rotate(0,884.923,707.25) scale(0.659091,-0.659091) translate(453.969,-1784.07)" width="22" x="877.6733536369137" xlink:href="#DollyBreaker:手车_0" y="699.9999999999999" zvalue="547"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450451537925" ObjectName="#1主变10kV侧001断路器小车下"/>
   <cge:TPSR_Ref TObjectID="6192450451537925"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,884.923,707.25) scale(0.659091,-0.659091) translate(453.969,-1784.07)" width="22" x="877.6733536369137" y="699.9999999999999"/></g>
  <g id="145">
   <use class="kv10" height="22" transform="rotate(0,596.25,790.25) scale(0.659091,-0.659091) translate(304.655,-1993)" width="22" x="589.0000000000001" xlink:href="#DollyBreaker:手车_0" y="782.9999999999999" zvalue="551"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450451603461" ObjectName="10kV1号电容器032断路器小车下"/>
   <cge:TPSR_Ref TObjectID="6192450451603461"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,596.25,790.25) scale(0.659091,-0.659091) translate(304.655,-1993)" width="22" x="589.0000000000001" y="782.9999999999999"/></g>
  <g id="148">
   <use class="kv10" height="22" transform="rotate(0,596.25,741.25) scale(0.659091,0.659091) translate(304.655,379.655)" width="22" x="589.0000000000001" xlink:href="#DollyBreaker:手车_0" y="733.9999999999999" zvalue="555"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450451668997" ObjectName="10kV1号电容器032断路器小车工作位"/>
   <cge:TPSR_Ref TObjectID="6192450451668997"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,596.25,741.25) scale(0.659091,0.659091) translate(304.655,379.655)" width="22" x="589.0000000000001" y="733.9999999999999"/></g>
  <g id="151">
   <use class="kv10" height="22" transform="rotate(0,745.188,791.25) scale(0.659091,-0.659091) translate(381.692,-1995.52)" width="22" x="737.9380029950669" xlink:href="#DollyBreaker:手车_0" y="783.9999999999999" zvalue="559"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450451734533" ObjectName="10kV接地变033断路器小车下"/>
   <cge:TPSR_Ref TObjectID="6192450451734533"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,745.188,791.25) scale(0.659091,-0.659091) translate(381.692,-1995.52)" width="22" x="737.9380029950669" y="783.9999999999999"/></g>
  <g id="154">
   <use class="kv10" height="22" transform="rotate(0,744.75,742.25) scale(0.659091,0.659091) translate(381.466,380.172)" width="22" x="737.5000000000001" xlink:href="#DollyBreaker:手车_0" y="734.9999999999999" zvalue="563"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450451800069" ObjectName="10kV接地变033断路器小车工作位"/>
   <cge:TPSR_Ref TObjectID="6192450451800069"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,744.75,742.25) scale(0.659091,0.659091) translate(381.466,380.172)" width="22" x="737.5000000000001" y="734.9999999999999"/></g>
  <g id="157">
   <use class="kv10" height="22" transform="rotate(0,916.923,745.25) scale(0.659091,0.659091) translate(470.521,381.724)" width="22" x="909.6733536369137" xlink:href="#DollyBreaker:手车_0" y="737.9999999999999" zvalue="567"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450451865605" ObjectName="10kV腾拉拱线034断路器小车工作位"/>
   <cge:TPSR_Ref TObjectID="6192450451865605"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,916.923,745.25) scale(0.659091,0.659091) translate(470.521,381.724)" width="22" x="909.6733536369137" y="737.9999999999999"/></g>
  <g id="175">
   <use class="kv10" height="22" transform="rotate(0,917.75,794.75) scale(0.659091,-0.659091) translate(470.948,-2004.33)" width="22" x="910.5" xlink:href="#DollyBreaker:手车_0" y="787.4999999999999" zvalue="571"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450451931141" ObjectName="10kV腾拉拱线034断路器小车下"/>
   <cge:TPSR_Ref TObjectID="6192450451931141"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,917.75,794.75) scale(0.659091,-0.659091) translate(470.948,-2004.33)" width="22" x="910.5" y="787.4999999999999"/></g>
  <g id="198">
   <use class="kv10" height="22" transform="rotate(0,1151.43,742.75) scale(0.659091,0.659091) translate(591.815,380.431)" width="22" x="1144.175198100449" xlink:href="#DollyBreaker:手车_0" y="735.4999999999999" zvalue="576"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450451996677" ObjectName="10kV石竹河线035断路器小车工作位"/>
   <cge:TPSR_Ref TObjectID="6192450451996677"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1151.43,742.75) scale(0.659091,0.659091) translate(591.815,380.431)" width="22" x="1144.175198100449" y="735.4999999999999"/></g>
  <g id="221">
   <use class="kv10" height="22" transform="rotate(0,1151.25,790.25) scale(0.659091,-0.659091) translate(591.724,-1993)" width="22" x="1144" xlink:href="#DollyBreaker:手车_0" y="782.9999999999999" zvalue="583"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450452062213" ObjectName="10kV石竹河线035断路器小车下"/>
   <cge:TPSR_Ref TObjectID="6192450452062213"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1151.25,790.25) scale(0.659091,-0.659091) translate(591.724,-1993)" width="22" x="1144" y="782.9999999999999"/></g>
  <g id="226">
   <use class="kv10" height="22" transform="rotate(0,1309.25,739.75) scale(0.659091,0.659091) translate(673.448,378.879)" width="22" x="1302" xlink:href="#DollyBreaker:手车_0" y="732.4999999999999" zvalue="587"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450452127749" ObjectName="10kV备用Ⅰ回线036断路器小车工作位"/>
   <cge:TPSR_Ref TObjectID="6192450452127749"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1309.25,739.75) scale(0.659091,0.659091) translate(673.448,378.879)" width="22" x="1302" y="732.4999999999999"/></g>
  <g id="229">
   <use class="kv10" height="22" transform="rotate(0,1309.58,788.416) scale(0.659091,-0.659091) translate(673.621,-1988.38)" width="22" x="1302.333333333333" xlink:href="#DollyBreaker:手车_0" y="781.1660077358771" zvalue="591"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450452193285" ObjectName="10kV备用Ⅰ回线036断路器小车下"/>
   <cge:TPSR_Ref TObjectID="6192450452193285"/></metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,1309.58,788.416) scale(0.659091,-0.659091) translate(673.621,-1988.38)" width="22" x="1302.333333333333" y="781.1660077358771"/></g>
 </g>
</svg>