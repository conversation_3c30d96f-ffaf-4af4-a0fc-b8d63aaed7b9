<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549685256193" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:220kV线路PT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="12.90667828048158" xlink:href="#terminal" y="37.28457520218116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27" x2="13" y1="26.75" y2="26.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.08333333333334" x2="29.08333333333334" y1="14.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27" x2="27" y1="22.75" y2="26.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.93916361389983" x2="12.93916361389983" y1="33.96630546659506" y2="37.26790853551449"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.93916361389983" x2="12.93916361389983" y1="30.79012345679012" y2="22.71704491718448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.91963465760507" x2="12.91963465760507" y1="19.74560215515698" y2="13.06430394728183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.01963465760507" x2="13.01963465760507" y1="22.75831495554599" y2="22.75831495554599"/>
   <ellipse cx="23.37" cy="20.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.556517488649495" x2="16.39655620359946" y1="22.67498162221265" y2="22.67498162221265"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.01963465760507" x2="13.01963465760507" y1="33.90122531314907" y2="33.90122531314907"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982819" x2="16.47988953693279" y1="19.66306207843401" y2="19.66306207843401"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982824" x2="16.47988953693279" y1="33.90122531314905" y2="33.90122531314905"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982824" x2="16.47988953693279" y1="30.80597243603708" y2="30.80597243603708"/>
   <ellipse cx="26.96" cy="14.43" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="10.72318415531615" x2="15" y1="12.99639541176734" y2="12.99639541176734"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.47318415531615" x2="14.08333333333333" y1="11.74639541176734" y2="11.74639541176734"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.97318415531615" x2="13.41666666666667" y1="10.24639541176734" y2="10.24639541176734"/>
   <ellipse cx="30.62" cy="20.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.25" x2="25.25" y1="20.5" y2="20.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.08333333333334" x2="33.08333333333334" y1="20.41666666666667" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <rect fill-opacity="0" height="8.92" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,6.54) scale(1,1) translate(0,0)" width="4" x="13" y="2.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.999999999999998" y2="12.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.083333333333332" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:四卷带壁雷器母线PT_0" viewBox="0,0,40,35">
   <use terminal-index="0" type="0" x="22.10905664884498" xlink:href="#terminal" y="34.51612485684674"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="28.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <rect fill-opacity="0" height="15.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,30,14.36) scale(1,-1) translate(0,-930.43)" width="7" x="26.5" y="6.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="16.27740325661302" y2="15.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="7.166666666666671" y1="16.27740325661302" y2="12.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="12.27740325661302" y2="13.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="30.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="22" y1="28.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="14" y1="21.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="30" y1="28.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="30" y1="28.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.92943360505483" x2="29.92943360505483" y1="6.610736589946352" y2="3.462520268614096"/>
   <ellipse cx="13.81" cy="17.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.95921744067709" x2="27.68240159599324" y1="3.3946117330996" y2="3.3946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.12588410734375" x2="28.51573492932657" y1="2.144611733099605" y2="2.144611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="30.54255077401042" x2="29.09906826265991" y1="0.8946117330996053" y2="0.8946117330996053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599325" x2="16.18240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="13.78240159599324" y1="15.23154965466559" y2="17.7066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="11.38240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <ellipse cx="20.23" cy="13.99" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.48" cy="10.41" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="17.79906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="20.19906826265991" y1="11.73154965466559" y2="14.2066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="22.59906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="8.148216321332258" y2="10.62329629003646"/>
   <ellipse cx="6.64" cy="14.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_0" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3" x2="7" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.25" x2="5.083333333333332" y1="7.083333333333336" y2="19.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_1" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.833333333333333" x2="6.916666666666667" y1="5" y2="5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.05" x2="5.05" y1="5.000000000000004" y2="22.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="GroundDisconnector:配网地刀_2" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="4.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.166666666666668" x2="1.333333333333333" y1="5.083333333333332" y2="24.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.556121771001858" x2="9.725530966120488" y1="26.03810844520533" y2="26.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.08435663685496" x2="8.197296100267383" y1="27.91807677819227" y2="27.91807677819227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.4459248360414" x2="6.502394567747611" y1="29.76471177784586" y2="29.76471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.057493035227839" x2="5.057493035227839" y1="25.93333333333333" y2="20"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1" x2="8.833333333333334" y1="4.999999999999998" y2="24.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.041506727682536" x2="5.041506727682536" y1="4.875871411333476" y2="1.005461830931413"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="4.416666666666668" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.249999999999998" y2="20.16666666666667"/>
  </symbol>
  <symbol id="Accessory:带熔断器35kVPT11_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="1.1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="26.25" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="18.06481481481482" y1="24.96612466124661" y2="22.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.75" x2="20.69444444444444" y1="24.96612466124661" y2="24.96612466124661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.75" x2="11.75" y1="21.63888888888889" y2="23.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.69444444444444" x2="19.37962962962963" y1="24.96612466124661" y2="22.5"/>
   <rect fill-opacity="0" height="7.42" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15.01,5.71) scale(1,1) translate(0,0)" width="4.92" x="12.55" y="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="13" y2="1"/>
   <ellipse cx="15.03" cy="18.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.67" cy="23.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="18.5" cy="23.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="20.5" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17" x2="15" y1="15.88888888888889" y2="17.88888888888889"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":线路发电机1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="30"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.08333333333333215" y2="29.74999999999999"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV木笼河二级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="40.25" xlink:href="logo.png" y="38.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,189.25,68.75) scale(1,1) translate(0,0)" writing-mode="lr" x="189.25" xml:space="preserve" y="72.25" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,185.75,68.4403) scale(1,1) translate(0,0)" writing-mode="lr" x="185.75" xml:space="preserve" y="77.44" zvalue="3">110kV木笼河二级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="181" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="438"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="438">信号一览</text>
  <line fill="none" id="32" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382.25" x2="382.25" y1="6.75" y2="1036.75" zvalue="4"/>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.250000000000455" x2="375.25" y1="142.6204926140824" y2="142.6204926140824" zvalue="6"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1553.14,265.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1553.14" xml:space="preserve" y="270.07" zvalue="38">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1052.51,345.365) scale(1,1) translate(0,0)" writing-mode="lr" x="1052.51" xml:space="preserve" y="349.87" zvalue="40">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1061.95,393.365) scale(1,1) translate(0,0)" writing-mode="lr" x="1061.95" xml:space="preserve" y="397.87" zvalue="42">101</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1107.47,457.812) scale(1,1) translate(-2.39761e-13,4.91253e-14)" writing-mode="lr" x="1107.47" xml:space="preserve" y="462.31" zvalue="48">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1851.89,714.671) scale(1,1) translate(0,0)" writing-mode="lr" x="1851.89" xml:space="preserve" y="719.17" zvalue="58">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,744.796,363.31) scale(1,1) translate(0,0)" writing-mode="lr" x="744.8" xml:space="preserve" y="367.81" zvalue="60">1901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,662.112,927.375) scale(1,1) translate(0,3.01964e-13)" writing-mode="lr" x="662.1124018701021" xml:space="preserve" y="931.8753747717902" zvalue="77">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,710.24,994.526) scale(1,1) translate(-4.59126e-13,0)" writing-mode="lr" x="710.24" xml:space="preserve" y="999.03" zvalue="88">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="254" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1496.71,551.171) scale(1,1) translate(0,-1.20053e-13)" writing-mode="lr" x="1496.71" xml:space="preserve" y="555.67" zvalue="127">母线PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,611.073,213.31) scale(1,1) translate(0,0)" writing-mode="lr" x="611.0700000000001" xml:space="preserve" y="217.81" zvalue="154">122</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,602.296,260.865) scale(1,1) translate(0,0)" writing-mode="lr" x="602.3" xml:space="preserve" y="265.37" zvalue="155">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,603.962,152.865) scale(1,1) translate(0,0)" writing-mode="lr" x="603.96" xml:space="preserve" y="157.37" zvalue="158">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1332.07,213.31) scale(1,1) translate(0,0)" writing-mode="lr" x="1332.07" xml:space="preserve" y="217.81" zvalue="179">121</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1323.3,260.865) scale(1,1) translate(0,0)" writing-mode="lr" x="1323.3" xml:space="preserve" y="265.37" zvalue="180">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1324.96,152.865) scale(1,1) translate(0,0)" writing-mode="lr" x="1324.96" xml:space="preserve" y="157.37" zvalue="183">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1307.92,41.3929) scale(1,1) translate(0,0)" writing-mode="lr" x="1307.92" xml:space="preserve" y="45.89" zvalue="187">110kV辟嘎线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1098.5,341) scale(1,1) translate(0,0)" writing-mode="lr" x="1098.5" xml:space="preserve" y="345.5" zvalue="206">10117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,771.5,408) scale(1,1) translate(0,0)" writing-mode="lr" x="771.5" xml:space="preserve" y="412.5" zvalue="208">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="186" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,785.5,325) scale(1,1) translate(0,0)" writing-mode="lr" x="785.5" xml:space="preserve" y="329.5" zvalue="209">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,644,101) scale(1,1) translate(0,0)" writing-mode="lr" x="644" xml:space="preserve" y="105.5" zvalue="214">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,646,168) scale(1,1) translate(0,0)" writing-mode="lr" x="646" xml:space="preserve" y="172.5" zvalue="216">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,642,225) scale(1,1) translate(0,0)" writing-mode="lr" x="642" xml:space="preserve" y="229.5" zvalue="218">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="208" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1365,102) scale(1,1) translate(0,0)" writing-mode="lr" x="1365" xml:space="preserve" y="106.5" zvalue="223">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="207" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1372,162) scale(1,1) translate(0,0)" writing-mode="lr" x="1372" xml:space="preserve" y="166.5" zvalue="225">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="204" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1379,222) scale(1,1) translate(0,0)" writing-mode="lr" x="1379" xml:space="preserve" y="226.5" zvalue="227">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="227" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1062.53,612.198) scale(1,1) translate(0,0)" writing-mode="lr" x="1062.53" xml:space="preserve" y="616.7" zvalue="235">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="226" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1053.9,679.754) scale(1,1) translate(0,0)" writing-mode="lr" x="1053.9" xml:space="preserve" y="684.25" zvalue="236">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1056.59,563.754) scale(1,1) translate(0,0)" writing-mode="lr" x="1056.59" xml:space="preserve" y="568.25" zvalue="238">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="223" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1107.11,631.766) scale(1,1) translate(0,0)" writing-mode="lr" x="1107.11" xml:space="preserve" y="636.27" zvalue="244">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1525.16,678.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1525.16" xml:space="preserve" y="683.14" zvalue="262">6901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1430.44,646.877) scale(1,1) translate(0,0)" writing-mode="lr" x="1430.44" xml:space="preserve" y="651.38" zvalue="264">69017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,625.035,811.754) scale(1,1) translate(0,0)" writing-mode="lr" x="625.04" xml:space="preserve" y="816.25" zvalue="270">621</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,617.073,751.976) scale(1,1) translate(0,0)" writing-mode="lr" x="617.0700000000001" xml:space="preserve" y="756.48" zvalue="272">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="268" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,661.778,754.222) scale(1,1) translate(0,0)" writing-mode="lr" x="661.78" xml:space="preserve" y="758.72" zvalue="274">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="276" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,541.778,936.722) scale(1,1) translate(0,0)" writing-mode="lr" x="541.78" xml:space="preserve" y="941.22" zvalue="282">69117</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,524.429,863.508) scale(1,1) translate(0,0)" writing-mode="lr" x="524.4299999999999" xml:space="preserve" y="868.01" zvalue="285">6911</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,507.429,1011.16) scale(1,1) translate(0,0)" writing-mode="lr" x="507.43" xml:space="preserve" y="1015.66" zvalue="287">励磁PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,780.554,886.008) scale(1,1) translate(0,0)" writing-mode="lr" x="780.55" xml:space="preserve" y="890.51" zvalue="290">6912</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,765.682,1012.91) scale(1,1) translate(0,0)" writing-mode="lr" x="765.6799999999999" xml:space="preserve" y="1017.41" zvalue="292">仪用PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" x="730.9765625" xml:space="preserve" y="792.4774740134186" zvalue="300">高频切机第</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="730.9765625" xml:space="preserve" y="808.4774740134186" zvalue="300">二轮</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1443.59,950.804) scale(1,1) translate(-9.33554e-13,3.09767e-13)" writing-mode="lr" x="1443.588592346292" xml:space="preserve" y="955.3039462003617" zvalue="306">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1497.57,1009.53) scale(1,1) translate(0,0)" writing-mode="lr" x="1497.57" xml:space="preserve" y="1014.03" zvalue="308">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1408.37,826.754) scale(1,1) translate(0,0)" writing-mode="lr" x="1408.37" xml:space="preserve" y="831.25" zvalue="310">622</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1400.41,766.976) scale(1,1) translate(0,0)" writing-mode="lr" x="1400.41" xml:space="preserve" y="771.48" zvalue="312">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1445.11,769.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1445.11" xml:space="preserve" y="773.72" zvalue="315">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1321.11,951.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1321.11" xml:space="preserve" y="956.22" zvalue="320">69217</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1303.76,878.508) scale(1,1) translate(0,0)" writing-mode="lr" x="1303.76" xml:space="preserve" y="883.01" zvalue="322">6921</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1229.26,1000.04) scale(1,1) translate(0,0)" writing-mode="lr" x="1229.26" xml:space="preserve" y="1004.54" zvalue="324">励磁PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1563.89,901.008) scale(1,1) translate(0,0)" writing-mode="lr" x="1563.89" xml:space="preserve" y="905.51" zvalue="325">6922</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1588.3,1000.83) scale(1,1) translate(0,0)" writing-mode="lr" x="1588.3" xml:space="preserve" y="1005.33" zvalue="327">仪用PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,921.073,783.976) scale(1,1) translate(0,1.29143e-12)" writing-mode="lr" x="921.0700000000001" xml:space="preserve" y="788.48" zvalue="338">6231</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1084.78,801.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1084.78" xml:space="preserve" y="805.72" zvalue="341">62317</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="151" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,999,953.5) scale(1,1) translate(0,0)" writing-mode="lr" x="999" xml:space="preserve" y="958" zvalue="375">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,998,519) scale(1,1) translate(0,0)" writing-mode="lr" x="998" xml:space="preserve" y="523.5" zvalue="378">1010</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="265" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="412"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="263" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="414">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="249" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="415">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="248" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="416">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="241" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="417">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="239" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="418">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="233" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="420">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="232" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="421">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="230" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="422">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.054,956) scale(1,1) translate(0,0)" writing-mode="lr" x="237.05" xml:space="preserve" y="962" zvalue="423">MLH2-01-2011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="224" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,327.054,988) scale(1,1) translate(0,0)" writing-mode="lr" x="327.05" xml:space="preserve" y="994" zvalue="425">20200806</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="216" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="426">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="212" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="427">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="428">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="205" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,359.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="363.75" zvalue="430">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="177" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="439">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="440">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="443">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="445">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="446">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="448">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="449">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="451">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,589.672,40.75) scale(1,1) translate(0,0)" writing-mode="lr" x="589.67" xml:space="preserve" y="45.25" zvalue="516">110kV木笼河二三级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,662.5,947.5) scale(1,1) translate(0,0)" writing-mode="lr" x="662.5" xml:space="preserve" y="952" zvalue="531">6.3MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1444.5,965.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1444.5" xml:space="preserve" y="970" zvalue="533">6.3MW</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="438"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="173">
   <path class="kv110" d="M 533.57 295.14 L 1583.57 295.14" stroke-width="6" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674423472131" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674423472131"/></metadata>
  <path d="M 533.57 295.14 L 1583.57 295.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="kv6" d="M 469.27 715.67 L 1814.27 715.67" stroke-width="6" zvalue="57"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674423537667" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674423537667"/></metadata>
  <path d="M 469.27 715.67 L 1814.27 715.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="172">
   <use class="kv110" height="30" transform="rotate(0,1041.07,346.365) scale(1.11111,0.814815) translate(-103.273,75.9416)" width="15" x="1032.734182814564" xlink:href="#Disconnector:刀闸_0" y="334.1428571428572" zvalue="39"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454853394434" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454853394434"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1041.07,346.365) scale(1.11111,0.814815) translate(-103.273,75.9416)" width="15" x="1032.734182814564" y="334.1428571428572"/></g>
  <g id="160">
   <use class="kv110" height="30" transform="rotate(0,719.462,364.31) scale(-1.11111,-0.814815) translate(-1366.15,-814.194)" width="15" x="711.1290103419045" xlink:href="#Disconnector:刀闸_0" y="352.0873152566335" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454853328898" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454853328898"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,719.462,364.31) scale(-1.11111,-0.814815) translate(-1366.15,-814.194)" width="15" x="711.1290103419045" y="352.0873152566335"/></g>
  <g id="272">
   <use class="kv110" height="30" transform="rotate(0,589.74,261.865) scale(-1.11111,-0.814815) translate(-1119.67,-586.023)" width="15" x="581.4067881196823" xlink:href="#Disconnector:刀闸_0" y="249.6428704942976" zvalue="153"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454853001218" ObjectName="110kV木笼河二三级线1221隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454853001218"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,589.74,261.865) scale(-1.11111,-0.814815) translate(-1119.67,-586.023)" width="15" x="581.4067881196823" y="249.6428704942976"/></g>
  <g id="271">
   <use class="kv110" height="30" transform="rotate(0,589.74,153.865) scale(-1.11111,-0.814815) translate(-1119.67,-345.477)" width="15" x="581.4067881461734" xlink:href="#Disconnector:刀闸_0" y="141.6428569309296" zvalue="156"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454852935682" ObjectName="110kV木笼河二三级线1226隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454852935682"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,589.74,153.865) scale(-1.11111,-0.814815) translate(-1119.67,-345.477)" width="15" x="581.4067881461734" y="141.6428569309296"/></g>
  <g id="219">
   <use class="kv110" height="30" transform="rotate(0,1310.74,261.865) scale(-1.11111,-0.814815) translate(-2489.57,-586.023)" width="15" x="1302.406788119682" xlink:href="#Disconnector:刀闸_0" y="249.6428704942976" zvalue="178"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454852673538" ObjectName="110kV辟嘎线1211隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454852673538"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1310.74,261.865) scale(-1.11111,-0.814815) translate(-2489.57,-586.023)" width="15" x="1302.406788119682" y="249.6428704942976"/></g>
  <g id="218">
   <use class="kv110" height="30" transform="rotate(0,1310.74,153.865) scale(-1.11111,-0.814815) translate(-2489.57,-345.477)" width="15" x="1302.406788146173" xlink:href="#Disconnector:刀闸_0" y="141.6428569309296" zvalue="181"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454852608002" ObjectName="110kV辟嘎线1216隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454852608002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1310.74,153.865) scale(-1.11111,-0.814815) translate(-2489.57,-345.477)" width="15" x="1302.406788146173" y="141.6428569309296"/></g>
  <g id="236">
   <use class="kv6" height="30" transform="rotate(0,1041.35,680.754) scale(-1.11111,-0.814815) translate(-1977.73,-1519)" width="15" x="1033.014196723179" xlink:href="#Disconnector:刀闸_0" y="668.5317593831866" zvalue="234"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454854901762" ObjectName="#1主变6.3kV侧6011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454854901762"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1041.35,680.754) scale(-1.11111,-0.814815) translate(-1977.73,-1519)" width="15" x="1033.014196723179" y="668.5317593831866"/></g>
  <g id="235">
   <use class="kv6" height="30" transform="rotate(0,1041.25,565.865) scale(-1.11111,-0.814815) translate(-1977.55,-1263.11)" width="15" x="1032.92154408624" xlink:href="#Disconnector:刀闸_0" y="553.6428569309296" zvalue="237"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454854836226" ObjectName="#1主变6.3kV侧6016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454854836226"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1041.25,565.865) scale(-1.11111,-0.814815) translate(-1977.55,-1263.11)" width="15" x="1032.92154408624" y="553.6428569309296"/></g>
  <g id="250">
   <use class="kv6" height="30" transform="rotate(0,1498.72,679.643) scale(-1.11111,-0.814815) translate(-2846.73,-1516.53)" width="15" x="1490.384471025061" xlink:href="#Disconnector:刀闸_0" y="667.4206482720755" zvalue="261"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454854967298" ObjectName="6.3kV母线6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454854967298"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1498.72,679.643) scale(-1.11111,-0.814815) translate(-2846.73,-1516.53)" width="15" x="1490.384471025061" y="667.4206482720755"/></g>
  <g id="261">
   <use class="kv6" height="30" transform="rotate(0,604.518,752.976) scale(-1.11111,-0.814815) translate(-1147.75,-1679.86)" width="15" x="596.1845658974601" xlink:href="#Disconnector:刀闸_0" y="740.7539816054087" zvalue="271"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454855163906" ObjectName="#1发电机6211隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454855163906"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,604.518,752.976) scale(-1.11111,-0.814815) translate(-1147.75,-1679.86)" width="15" x="596.1845658974601" y="740.7539816054087"/></g>
  <g id="291">
   <use class="kv6" height="30" transform="rotate(0,499.887,870.758) scale(-0.805556,-0.814815) translate(-1121.89,-1942.19)" width="15" x="493.8452380952381" xlink:href="#Disconnector:刀闸_0" y="858.5357276371548" zvalue="284"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454855491586" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454855491586"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,499.887,870.758) scale(-0.805556,-0.814815) translate(-1121.89,-1942.19)" width="15" x="493.8452380952381" y="858.5357276371548"/></g>
  <g id="42">
   <use class="kv6" height="30" transform="rotate(0,755.673,887.008) scale(-0.976984,-0.814815) translate(-1529.32,-1978.39)" width="15" x="748.3452380952381" xlink:href="#Disconnector:刀闸_0" y="874.7857276371548" zvalue="289"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454855557122" ObjectName="#1发电机6912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454855557122"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,755.673,887.008) scale(-0.976984,-0.814815) translate(-1529.32,-1978.39)" width="15" x="748.3452380952381" y="874.7857276371548"/></g>
  <g id="111">
   <use class="kv6" height="30" transform="rotate(0,1387.85,767.976) scale(-1.11111,-0.814815) translate(-2636.08,-1713.27)" width="15" x="1379.517899230793" xlink:href="#Disconnector:刀闸_0" y="755.7539816054087" zvalue="311"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454856212482" ObjectName="#2发电机6221隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454856212482"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1387.85,767.976) scale(-1.11111,-0.814815) translate(-2636.08,-1713.27)" width="15" x="1379.517899230793" y="755.7539816054087"/></g>
  <g id="103">
   <use class="kv6" height="30" transform="rotate(0,1279.22,885.758) scale(-0.805556,-0.814815) translate(-2868.68,-1975.6)" width="15" x="1273.178571428571" xlink:href="#Disconnector:刀闸_0" y="873.5357276371548" zvalue="321"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454855884802" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454855884802"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1279.22,885.758) scale(-0.805556,-0.814815) translate(-2868.68,-1975.6)" width="15" x="1273.178571428571" y="873.5357276371548"/></g>
  <g id="97">
   <use class="kv6" height="30" transform="rotate(0,1539.01,902.008) scale(-0.976984,-0.814815) translate(-3114.44,-2011.8)" width="15" x="1531.678571428571" xlink:href="#Disconnector:刀闸_0" y="889.7857276371548" zvalue="324"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454856671234" ObjectName="#2发电机6922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454856671234"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1539.01,902.008) scale(-0.976984,-0.814815) translate(-3114.44,-2011.8)" width="15" x="1531.678571428571" y="889.7857276371548"/></g>
  <g id="140">
   <use class="kv6" height="30" transform="rotate(0,994.444,791.976) scale(-1.11111,-0.814815) translate(-1888.61,-1766.72)" width="15" x="986.1111100869132" xlink:href="#Disconnector:刀闸_0" y="779.7539816054089" zvalue="337"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454856540162" ObjectName="#1站用变6231隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454856540162"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,994.444,791.976) scale(-1.11111,-0.814815) translate(-1888.61,-1766.72)" width="15" x="986.1111100869132" y="779.7539816054089"/></g>
 </g>
 <g id="BreakerClass">
  <g id="171">
   <use class="kv110" height="20" transform="rotate(0,1041.18,394.365) scale(1.22222,1.11111) translate(-188.194,-38.3254)" width="10" x="1035.065106248756" xlink:href="#Breaker:开关_0" y="383.2539682539682" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925224890371" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925224890371"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1041.18,394.365) scale(1.22222,1.11111) translate(-188.194,-38.3254)" width="10" x="1035.065106248756" y="383.2539682539682"/></g>
  <g id="273">
   <use class="kv110" height="20" transform="rotate(0,589.74,214.31) scale(1.22222,1.11111) translate(-106.114,-20.3198)" width="10" x="583.6290103419046" xlink:href="#Breaker:开关_0" y="203.1984124864851" zvalue="152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925224824835" ObjectName="110kV木笼河二三级线122断路器"/>
   <cge:TPSR_Ref TObjectID="6473925224824835"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,589.74,214.31) scale(1.22222,1.11111) translate(-106.114,-20.3198)" width="10" x="583.6290103419046" y="203.1984124864851"/></g>
  <g id="220">
   <use class="kv110" height="20" transform="rotate(0,1310.74,214.31) scale(1.22222,1.11111) translate(-237.205,-20.3198)" width="10" x="1304.629010341905" xlink:href="#Breaker:开关_0" y="203.1984124864851" zvalue="177"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925224759299" ObjectName="110kV辟嘎线121断路器"/>
   <cge:TPSR_Ref TObjectID="6473925224759299"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1310.74,214.31) scale(1.22222,1.11111) translate(-237.205,-20.3198)" width="10" x="1304.629010341905" y="203.1984124864851"/></g>
  <g id="237">
   <use class="kv6" height="20" transform="rotate(0,1041.2,613.198) scale(1.22222,1.11111) translate(-188.198,-60.2087)" width="10" x="1035.086976992691" xlink:href="#Breaker:开关_0" y="602.087301375374" zvalue="233"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925224955907" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473925224955907"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1041.2,613.198) scale(1.22222,1.11111) translate(-188.198,-60.2087)" width="10" x="1035.086976992691" y="602.087301375374"/></g>
  <g id="259">
   <use class="kv6" height="20" transform="rotate(0,605.813,812.754) scale(1.22222,1.11111) translate(-109.037,-80.1643)" width="10" x="599.7017906114168" xlink:href="#Breaker:开关_0" y="801.6428569309297" zvalue="269"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925225021443" ObjectName="#1发电机621断路器"/>
   <cge:TPSR_Ref TObjectID="6473925225021443"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,605.813,812.754) scale(1.22222,1.11111) translate(-109.037,-80.1643)" width="10" x="599.7017906114168" y="801.6428569309297"/></g>
  <g id="112">
   <use class="kv6" height="20" transform="rotate(0,1389.15,827.754) scale(1.22222,1.11111) translate(-251.461,-81.6643)" width="10" x="1383.03512394475" xlink:href="#Breaker:开关_0" y="816.6428569309297" zvalue="309"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925225086979" ObjectName="#2发电机622断路器"/>
   <cge:TPSR_Ref TObjectID="6473925225086979"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1389.15,827.754) scale(1.22222,1.11111) translate(-251.461,-81.6643)" width="10" x="1383.03512394475" y="816.6428569309297"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="170">
   <path class="kv110" d="M 1041.14 358.38 L 1041.14 383.74" stroke-width="1" zvalue="43"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@1" LinkObjectIDznd="171@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1041.14 358.38 L 1041.14 383.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv110" d="M 1041.17 334.55 L 1041.17 295.14" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="172@0" LinkObjectIDznd="173@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1041.17 334.55 L 1041.17 295.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="165">
   <path class="kv110" d="M 1041.26 404.98 L 1041.26 438.98" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@1" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1041.26 404.98 L 1041.26 438.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv110" d="M 719.39 352.3 L 719.39 295.14" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160@1" LinkObjectIDznd="173@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 719.39 352.3 L 719.39 295.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv110" d="M 589.64 273.68 L 589.64 295.14" stroke-width="1" zvalue="157"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="173@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.64 273.68 L 589.64 295.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv110" d="M 589.67 249.85 L 589.67 224.92" stroke-width="1" zvalue="159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@1" LinkObjectIDznd="273@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.67 249.85 L 589.67 224.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv110" d="M 589.7 203.68 L 589.64 165.68" stroke-width="1" zvalue="160"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="273@0" LinkObjectIDznd="271@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.7 203.68 L 589.64 165.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv110" d="M 1310.64 273.68 L 1310.64 295.14" stroke-width="1" zvalue="182"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@0" LinkObjectIDznd="173@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1310.64 273.68 L 1310.64 295.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="91">
   <path class="kv110" d="M 1310.67 249.85 L 1310.67 224.92" stroke-width="1" zvalue="184"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@1" LinkObjectIDznd="220@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1310.67 249.85 L 1310.67 224.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv110" d="M 1310.7 203.68 L 1310.64 165.68" stroke-width="1" zvalue="185"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@0" LinkObjectIDznd="218@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1310.7 203.68 L 1310.64 165.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv110" d="M 1310.67 141.85 L 1310.67 100.17" stroke-width="1" zvalue="188"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@1" LinkObjectIDznd="214@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1310.67 141.85 L 1310.67 100.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv110" d="M 1249.1 101.51 L 1310.67 101.51" stroke-width="1" zvalue="199"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@0" LinkObjectIDznd="89" MaxPinNum="2"/>
   </metadata>
  <path d="M 1249.1 101.51 L 1310.67 101.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv110" d="M 1247.89 124.13 L 1266.24 124.13 L 1266.24 101.51" stroke-width="1" zvalue="201"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@0" LinkObjectIDznd="85" MaxPinNum="2"/>
   </metadata>
  <path d="M 1247.89 124.13 L 1266.24 124.13 L 1266.24 101.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv110" d="M 1075.68 322.01 L 1041.17 322.01" stroke-width="1" zvalue="206"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="167" MaxPinNum="2"/>
   </metadata>
  <path d="M 1075.68 322.01 L 1041.17 322.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv110" d="M 756.68 326.01 L 719.39 326.01" stroke-width="1" zvalue="209"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@0" LinkObjectIDznd="157" MaxPinNum="2"/>
   </metadata>
  <path d="M 756.68 326.01 L 719.39 326.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv110" d="M 719.13 439.08 L 719.13 376.13" stroke-width="1" zvalue="210"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="160@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 719.13 439.08 L 719.13 376.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="kv110" d="M 742.68 409.01 L 719.13 409.01" stroke-width="1" zvalue="211"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="188" MaxPinNum="2"/>
   </metadata>
  <path d="M 742.68 409.01 L 719.13 409.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv110" d="M 629.68 186.02 L 589.67 186.02" stroke-width="1" zvalue="219"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@0" LinkObjectIDznd="99" MaxPinNum="2"/>
   </metadata>
  <path d="M 629.68 186.02 L 589.67 186.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="kv110" d="M 626.68 239.01 L 589.67 239.01" stroke-width="1" zvalue="220"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="100" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.68 239.01 L 589.67 239.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv110" d="M 1356.68 122.01 L 1310.67 122.01" stroke-width="1" zvalue="229"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@0" LinkObjectIDznd="89" MaxPinNum="2"/>
   </metadata>
  <path d="M 1356.68 122.01 L 1310.67 122.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="221">
   <path class="kv110" d="M 1310.66 180.02 L 1356.68 180.02" stroke-width="1" zvalue="230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90" LinkObjectIDznd="213@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1310.66 180.02 L 1356.68 180.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="kv110" d="M 1310.67 236.01 L 1363.68 236.01" stroke-width="1" zvalue="231"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91" LinkObjectIDznd="209@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1310.67 236.01 L 1363.68 236.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="kv6" d="M 1041.19 553.85 L 1041.19 530.17" stroke-width="1" zvalue="251"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@1" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1041.19 553.85 L 1041.19 530.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="243">
   <path class="kv6" d="M 1041.16 577.68 L 1041.16 602.57" stroke-width="1" zvalue="252"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@0" LinkObjectIDznd="237@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1041.16 577.68 L 1041.16 602.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="255">
   <path class="kv6" d="M 1498.65 613.73 L 1498.65 667.63" stroke-width="1" zvalue="264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="250@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1498.65 613.73 L 1498.65 667.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="257">
   <path class="kv6" d="M 1470.32 658.87 L 1498.65 658.87" stroke-width="1" zvalue="266"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="252@0" LinkObjectIDznd="255" MaxPinNum="2"/>
   </metadata>
  <path d="M 1470.32 658.87 L 1498.65 658.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="258">
   <path class="kv6" d="M 1446.99 612.84 L 1446.99 630 L 1498.65 630" stroke-width="1" zvalue="267"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@0" LinkObjectIDznd="255" MaxPinNum="2"/>
   </metadata>
  <path d="M 1446.99 612.84 L 1446.99 630 L 1498.65 630" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv6" d="M 604.45 740.96 L 604.45 715.67" stroke-width="1" zvalue="274"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@1" LinkObjectIDznd="161@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 604.45 740.96 L 604.45 715.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="kv6" d="M 604.42 764.79 L 604.42 802.12" stroke-width="1" zvalue="275"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@0" LinkObjectIDznd="259@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 604.42 764.79 L 604.42 802.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="274">
   <path class="kv6" d="M 640.79 777.34 L 604.42 777.34" stroke-width="1" zvalue="276"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="266@0" LinkObjectIDznd="270" MaxPinNum="2"/>
   </metadata>
  <path d="M 640.79 777.34 L 604.42 777.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="275">
   <path class="kv6" d="M 605.89 823.37 L 605.89 880.64" stroke-width="1" zvalue="277"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="259@1" LinkObjectIDznd="850@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 605.89 823.37 L 605.89 880.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv6" d="M 527.04 909.84 L 500.83 909.84" stroke-width="1" zvalue="292"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="279@0" LinkObjectIDznd="55" MaxPinNum="2"/>
   </metadata>
  <path d="M 527.04 909.84 L 500.83 909.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv6" d="M 499.84 858.74 L 499.84 836.75 L 605.89 836.75" stroke-width="1" zvalue="293"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="291@1" LinkObjectIDznd="275" MaxPinNum="2"/>
   </metadata>
  <path d="M 499.84 858.74 L 499.84 836.75 L 605.89 836.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="kv6" d="M 755.59 932.38 L 755.59 898.83" stroke-width="1" zvalue="295"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="42@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.59 932.38 L 755.59 898.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="54">
   <path class="kv6" d="M 755.61 874.99 L 755.61 836.75 L 603.06 836.75" stroke-width="1" zvalue="296"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@1" LinkObjectIDznd="51" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.61 874.99 L 755.61 836.75 L 603.06 836.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv6" d="M 500.83 945.63 L 500.83 882.58" stroke-width="1" zvalue="301"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="287@0" LinkObjectIDznd="291@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 500.83 945.63 L 500.83 882.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv6" d="M 1498.62 715.67 L 1498.62 691.46" stroke-width="1" zvalue="302"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@4" LinkObjectIDznd="250@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1498.62 715.67 L 1498.62 691.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv6" d="M 1041.25 715.67 L 1041.25 692.57" stroke-width="1" zvalue="303"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="236@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1041.25 715.67 L 1041.25 692.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="kv6" d="M 1387.75 779.79 L 1387.75 817.12" stroke-width="1" zvalue="316"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="112@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1387.75 779.79 L 1387.75 817.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv6" d="M 1424.12 792.34 L 1387.75 792.34" stroke-width="1" zvalue="317"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110@0" LinkObjectIDznd="107" MaxPinNum="2"/>
   </metadata>
  <path d="M 1424.12 792.34 L 1387.75 792.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv6" d="M 1306.37 924.84 L 1279.16 924.84" stroke-width="1" zvalue="327"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="77" MaxPinNum="2"/>
   </metadata>
  <path d="M 1306.37 924.84 L 1279.16 924.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="77">
   <path class="kv6" d="M 1279.17 962.63 L 1279.15 897.58" stroke-width="1" zvalue="333"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="103@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1279.17 962.63 L 1279.15 897.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv6" d="M 994.38 779.96 L 994.38 715.67" stroke-width="1" zvalue="347"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@1" LinkObjectIDznd="161@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 994.38 779.96 L 994.38 715.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv6" d="M 1041.28 623.81 L 1041.28 668.74" stroke-width="1" zvalue="355"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@1" LinkObjectIDznd="236@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1041.28 623.81 L 1041.28 668.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv6" d="M 1091.79 645.78 L 1041.28 645.78" stroke-width="1" zvalue="358"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@0" LinkObjectIDznd="119" MaxPinNum="2"/>
   </metadata>
  <path d="M 1091.79 645.78 L 1041.28 645.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="kv6" d="M 708.99 943.26 L 708.99 914 L 755.59 914" stroke-width="1" zvalue="359"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@0" LinkObjectIDznd="53" MaxPinNum="2"/>
   </metadata>
  <path d="M 708.99 943.26 L 708.99 914 L 755.59 914" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv6" d="M 1538.92 959.38 L 1538.92 913.83" stroke-width="1" zvalue="360"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="97@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1538.92 959.38 L 1538.92 913.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv6" d="M 1496.32 958.26 L 1496.32 937 L 1538.92 937" stroke-width="1" zvalue="361"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="124" MaxPinNum="2"/>
   </metadata>
  <path d="M 1496.32 958.26 L 1496.32 937 L 1538.92 937" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv6" d="M 1390.23 894.64 L 1390.23 838.37" stroke-width="1" zvalue="363"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="112@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1390.23 894.64 L 1390.23 838.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv6" d="M 1279.17 873.74 L 1279.17 849 L 1390.23 849" stroke-width="1" zvalue="364"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@1" LinkObjectIDznd="128" MaxPinNum="2"/>
   </metadata>
  <path d="M 1279.17 873.74 L 1279.17 849 L 1390.23 849" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="133">
   <path class="kv6" d="M 1538.95 889.99 L 1538.95 853 L 1390.23 853" stroke-width="1" zvalue="367"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@1" LinkObjectIDznd="128" MaxPinNum="2"/>
   </metadata>
  <path d="M 1538.95 889.99 L 1538.95 853 L 1390.23 853" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv6" d="M 1387.78 755.96 L 1387.78 715.67" stroke-width="1" zvalue="369"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@1" LinkObjectIDznd="161@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1387.78 755.96 L 1387.78 715.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv6" d="M 995.5 855.55 L 995.5 803.79" stroke-width="1" zvalue="375"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="149@0" LinkObjectIDznd="140@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 995.5 855.55 L 995.5 803.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv6" d="M 1035.79 815.34 L 995.5 815.34" stroke-width="1" zvalue="376"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@0" LinkObjectIDznd="152" MaxPinNum="2"/>
   </metadata>
  <path d="M 1035.79 815.34 L 995.5 815.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv110" d="M 1041.23 465.27 L 948.6 465.27 L 948.6 507.8" stroke-width="1" zvalue="378"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@2" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1041.23 465.27 L 948.6 465.27 L 948.6 507.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv110" d="M 589.67 141.85 L 589.67 91.75" stroke-width="1" zvalue="518"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@1" LinkObjectIDznd="15@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.67 141.85 L 589.67 91.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv110" d="M 629.68 121.01 L 589.67 121.01" stroke-width="1" zvalue="519"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@0" LinkObjectIDznd="18" MaxPinNum="2"/>
   </metadata>
  <path d="M 629.68 121.01 L 589.67 121.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv110" d="M 528.1 101.51 L 589.67 101.51" stroke-width="1" zvalue="520"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@0" LinkObjectIDznd="18" MaxPinNum="2"/>
   </metadata>
  <path d="M 528.1 101.51 L 589.67 101.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv110" d="M 526.89 124.13 L 551 124.13 L 551 101.51" stroke-width="1" zvalue="521"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@0" LinkObjectIDznd="20" MaxPinNum="2"/>
   </metadata>
  <path d="M 526.89 124.13 L 551 124.13 L 551 101.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="166">
   <g id="1660">
    <use class="kv110" height="50" transform="rotate(0,1041.19,484.482) scale(1.96877,1.85355) translate(-497.807,-201.762)" width="30" x="1011.66" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="438.14" zvalue="47"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874595696642" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1661">
    <use class="kv6" height="50" transform="rotate(0,1041.19,484.482) scale(1.96877,1.85355) translate(-497.807,-201.762)" width="30" x="1011.66" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="438.14" zvalue="47"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874595762178" ObjectName="6"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399534903298" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399534903298"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1041.19,484.482) scale(1.96877,1.85355) translate(-497.807,-201.762)" width="30" x="1011.66" y="438.14"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="150">
   <use class="kv110" height="35" transform="rotate(0,716.49,463.393) scale(1.25,-1.42857) translate(-138.298,-780.268)" width="40" x="691.4901214530157" xlink:href="#Accessory:四卷带壁雷器母线PT_0" y="438.3928571428572" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454853263362" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,716.49,463.393) scale(1.25,-1.42857) translate(-138.298,-780.268)" width="40" x="691.4901214530157" y="438.3928571428572"/></g>
  <g id="185">
   <use class="kv6" height="40" transform="rotate(0,708.99,960.026) scale(1.25,-0.90625) translate(-138.048,-2021.24)" width="30" x="690.2401214530157" xlink:href="#Accessory:带熔断器的线路PT1_0" y="941.9007917131696" zvalue="87"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454856605698" ObjectName="#1发电机励磁变"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,708.99,960.026) scale(1.25,-0.90625) translate(-138.048,-2021.24)" width="30" x="690.2401214530157" y="941.9007917131696"/></g>
  <g id="137">
   <use class="kv6" height="30" transform="rotate(0,1498.71,595.421) scale(1.25,-1.25) translate(-295.992,-1068.01)" width="30" x="1479.962343887165" xlink:href="#Accessory:PT带熔断器_0" y="576.6706349206349" zvalue="126"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454853132290" ObjectName="6.3kV母线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1498.71,595.421) scale(1.25,-1.25) translate(-295.992,-1068.01)" width="30" x="1479.962343887165" y="576.6706349206349"/></g>
  <g id="139">
   <use class="kv6" height="26" transform="rotate(180,1447.03,599.474) scale(1.08054,1.08054) translate(-107.376,-43.637)" width="12" x="1440.543688767373" xlink:href="#Accessory:避雷器1_0" y="585.4270809599757" zvalue="128"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454853066754" ObjectName="避雷器3"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1447.03,599.474) scale(1.08054,1.08054) translate(-107.376,-43.637)" width="12" x="1440.543688767373" y="585.4270809599757"/></g>
  <g id="190">
   <use class="kv110" height="40" transform="rotate(270,506.49,92.6429) scale(1.25,1.25) translate(-96.298,-13.5286)" width="40" x="481.4901214530157" xlink:href="#Accessory:220kV线路PT_0" y="67.64285714285722" zvalue="173"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454852804610" ObjectName="110kV支那河一二级线线路PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,506.49,92.6429) scale(1.25,1.25) translate(-96.298,-13.5286)" width="40" x="481.4901214530157" y="67.64285714285722"/></g>
  <g id="193">
   <use class="kv110" height="26" transform="rotate(90,513.527,124.169) scale(-1.08054,1.08054) translate(-988.293,-8.20832)" width="12" x="507.0436887673734" xlink:href="#Accessory:避雷器1_0" y="110.1215254044201" zvalue="175"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454852739074" ObjectName="避雷器4"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,513.527,124.169) scale(-1.08054,1.08054) translate(-988.293,-8.20832)" width="12" x="507.0436887673734" y="110.1215254044201"/></g>
  <g id="206">
   <use class="kv110" height="40" transform="rotate(270,1227.49,92.6429) scale(1.25,1.25) translate(-240.498,-13.5286)" width="40" x="1202.490121453016" xlink:href="#Accessory:220kV线路PT_0" y="67.64285714285722" zvalue="198"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454852476930" ObjectName="110kV辟嘎线线路PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,1227.49,92.6429) scale(1.25,1.25) translate(-240.498,-13.5286)" width="40" x="1202.490121453016" y="67.64285714285722"/></g>
  <g id="203">
   <use class="kv110" height="26" transform="rotate(90,1234.53,124.169) scale(-1.08054,1.08054) translate(-2376.55,-8.20832)" width="12" x="1228.043688767373" xlink:href="#Accessory:避雷器1_0" y="110.1215254044201" zvalue="200"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454852411394" ObjectName="避雷器5"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1234.53,124.169) scale(-1.08054,1.08054) translate(-2376.55,-8.20832)" width="12" x="1228.043688767373" y="110.1215254044201"/></g>
  <g id="287">
   <use class="kv6" height="30" transform="rotate(0,500.929,972.036) scale(1.9,1.9) translate(-223.782,-446.938)" width="30" x="472.4285714285713" xlink:href="#Accessory:带熔断器35kVPT11_0" y="943.5357142857142" zvalue="286"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454855688194" ObjectName="#1发电机励磁PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,500.929,972.036) scale(1.9,1.9) translate(-223.782,-446.938)" width="30" x="472.4285714285713" y="943.5357142857142"/></g>
  <g id="44">
   <use class="kv6" height="30" transform="rotate(0,755.682,958.786) scale(1.9,1.9) translate(-344.455,-440.662)" width="30" x="727.1818622622146" xlink:href="#Accessory:带熔断器35kVPT11_0" y="930.2857142857142" zvalue="291"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454855622658" ObjectName="#1发电机仪用PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,755.682,958.786) scale(1.9,1.9) translate(-344.455,-440.662)" width="30" x="727.1818622622146" y="930.2857142857142"/></g>
  <g id="113">
   <use class="kv6" height="40" transform="rotate(0,1496.32,975.026) scale(1.25,-0.90625) translate(-295.515,-2052.79)" width="30" x="1477.573454786349" xlink:href="#Accessory:带熔断器的线路PT1_0" y="956.9007917131696" zvalue="307"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454856278018" ObjectName="#2发电机励磁变"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1496.32,975.026) scale(1.25,-0.90625) translate(-295.515,-2052.79)" width="30" x="1477.573454786349" y="956.9007917131696"/></g>
  <g id="102">
   <use class="kv6" height="30" transform="rotate(0,1279.26,989.036) scale(1.9,1.9) translate(-592.466,-454.991)" width="30" x="1250.761904761905" xlink:href="#Accessory:带熔断器35kVPT11_0" y="960.5357142857142" zvalue="323"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454855819266" ObjectName="#2发电机励磁PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1279.26,989.036) scale(1.9,1.9) translate(-592.466,-454.991)" width="30" x="1250.761904761905" y="960.5357142857142"/></g>
  <g id="96">
   <use class="kv6" height="30" transform="rotate(0,1539.02,985.786) scale(1.9,1.9) translate(-715.507,-453.451)" width="30" x="1510.515195595548" xlink:href="#Accessory:带熔断器35kVPT11_0" y="957.2857142857142" zvalue="326"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454855753730" ObjectName="#2发电机仪用PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1539.02,985.786) scale(1.9,1.9) translate(-715.507,-453.451)" width="30" x="1510.515195595548" y="957.2857142857142"/></g>
  <g id="149">
   <use class="kv6" height="40" transform="rotate(0,995.5,887) scale(1.7,-1.7) translate(-399.412,-1394.76)" width="30" x="970" xlink:href="#Accessory:带熔断器的线路PT1_0" y="853" zvalue="374"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454856736770" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,995.5,887) scale(1.7,-1.7) translate(-399.412,-1394.76)" width="30" x="970" y="853"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="kv6" height="30" transform="rotate(0,605.894,908.057) scale(1.85899,1.85899) translate(-267.083,-406.704)" width="30" x="578.009530051656" xlink:href="#Generator:发电机_0" y="880.1716568062666" zvalue="76"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454853197826" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192454853197826"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,605.894,908.057) scale(1.85899,1.85899) translate(-267.083,-406.704)" width="30" x="578.009530051656" y="880.1716568062666"/></g>
  <g id="114">
   <use class="kv6" height="30" transform="rotate(0,1390.23,922.057) scale(1.85899,1.85899) translate(-629.503,-413.173)" width="30" x="1362.342863384989" xlink:href="#Generator:发电机_0" y="894.1716568062666" zvalue="305"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454856343554" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192454856343554"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1390.23,922.057) scale(1.85899,1.85899) translate(-629.503,-413.173)" width="30" x="1362.342863384989" y="894.1716568062666"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="214">
   <use class="kv110" height="30" transform="rotate(0,1311.92,78.1706) scale(6.34921,1.48148) translate(-1086.57,-18.1832)" width="7" x="1289.699938786056" xlink:href="#ACLineSegment:线路_0" y="55.94841248648515" zvalue="186"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249332514818" ObjectName="110kV辟嘎线"/>
   <cge:TPSR_Ref TObjectID="8444249332514818_5066549685256193"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1311.92,78.1706) scale(6.34921,1.48148) translate(-1086.57,-18.1832)" width="7" x="1289.699938786056" y="55.94841248648515"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="178">
   <use class="kv110" height="30" transform="rotate(270,1090,322) scale(1,1) translate(0,0)" width="10" x="1085" xlink:href="#GroundDisconnector:配网地刀_0" y="307" zvalue="205"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454853591042" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454853591042"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1090,322) scale(1,1) translate(0,0)" width="10" x="1085" y="307"/></g>
  <g id="182">
   <use class="kv110" height="30" transform="rotate(270,757,409) scale(1,1) translate(0,0)" width="12" x="751" xlink:href="#GroundDisconnector:地刀12_0" y="394" zvalue="207"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454853722114" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454853722114"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,757,409) scale(1,1) translate(0,0)" width="12" x="751" y="394"/></g>
  <g id="184">
   <use class="kv110" height="30" transform="rotate(270,771,326) scale(1,1) translate(0,0)" width="12" x="765" xlink:href="#GroundDisconnector:地刀12_0" y="311" zvalue="208"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454853853186" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454853853186"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,771,326) scale(1,1) translate(0,0)" width="12" x="765" y="311"/></g>
  <g id="194">
   <use class="kv110" height="30" transform="rotate(270,644,121) scale(1,1) translate(0,0)" width="12" x="638" xlink:href="#GroundDisconnector:地刀12_0" y="106" zvalue="213"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454853984258" ObjectName="110kV木笼河二三级线12267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454853984258"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,644,121) scale(1,1) translate(0,0)" width="12" x="638" y="106"/></g>
  <g id="195">
   <use class="kv110" height="30" transform="rotate(270,644,186.005) scale(1,1) translate(0,0)" width="12" x="638" xlink:href="#GroundDisconnector:地刀12_0" y="171.0049725128926" zvalue="215"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454854115330" ObjectName="110kV木笼河二三级线12260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454854115330"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,644,186.005) scale(1,1) translate(0,0)" width="12" x="638" y="171.0049725128926"/></g>
  <g id="196">
   <use class="kv110" height="30" transform="rotate(270,641,239) scale(1,1) translate(0,0)" width="12" x="635" xlink:href="#GroundDisconnector:地刀12_0" y="224" zvalue="217"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454854246402" ObjectName="110kV木笼河二三级线12217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454854246402"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,641,239) scale(1,1) translate(0,0)" width="12" x="635" y="224"/></g>
  <g id="215">
   <use class="kv110" height="30" transform="rotate(270,1371,122) scale(1,1) translate(0,0)" width="12" x="1365" xlink:href="#GroundDisconnector:地刀12_0" y="107" zvalue="222"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454854639618" ObjectName="110kV辟嘎线12167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454854639618"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1371,122) scale(1,1) translate(0,0)" width="12" x="1365" y="107"/></g>
  <g id="213">
   <use class="kv110" height="30" transform="rotate(270,1371,180.005) scale(1,1) translate(0,0)" width="12" x="1365" xlink:href="#GroundDisconnector:地刀12_0" y="165.0049725128926" zvalue="224"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454854508546" ObjectName="110kV辟嘎线12160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454854508546"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1371,180.005) scale(1,1) translate(0,0)" width="12" x="1365" y="165.0049725128926"/></g>
  <g id="209">
   <use class="kv110" height="30" transform="rotate(270,1378,236) scale(1,1) translate(0,0)" width="12" x="1372" xlink:href="#GroundDisconnector:地刀12_0" y="221" zvalue="226"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454854377474" ObjectName="110kV辟嘎线12117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454854377474"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1378,236) scale(1,1) translate(0,0)" width="12" x="1372" y="221"/></g>
  <g id="231">
   <use class="kv6" height="30" transform="rotate(270,1106.11,645.766) scale(1,1) translate(0,0)" width="12" x="1100.111111111111" xlink:href="#GroundDisconnector:地刀12_0" y="630.7663107384792" zvalue="243"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454854770690" ObjectName="#1主变6.3kV侧60117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454854770690"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1106.11,645.766) scale(1,1) translate(0,0)" width="12" x="1100.111111111111" y="630.7663107384792"/></g>
  <g id="252">
   <use class="kv6" height="30" transform="rotate(90,1456,658.877) scale(1,1) translate(0,0)" width="12" x="1450" xlink:href="#GroundDisconnector:地刀12_0" y="643.8774218495903" zvalue="263"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454855098370" ObjectName="6.3kV母线69017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454855098370"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1456,658.877) scale(1,1) translate(0,0)" width="12" x="1450" y="643.8774218495903"/></g>
  <g id="266">
   <use class="kv6" height="30" transform="rotate(270,655.111,777.333) scale(1,1) translate(0,0)" width="12" x="649.111111111111" xlink:href="#GroundDisconnector:地刀12_0" y="762.3333333333333" zvalue="273"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454855294978" ObjectName="#1发电机62117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454855294978"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,655.111,777.333) scale(1,1) translate(0,0)" width="12" x="649.111111111111" y="762.3333333333333"/></g>
  <g id="279">
   <use class="kv6" height="30" transform="rotate(270,541.361,909.833) scale(1,1) translate(0,0)" width="12" x="535.3611111111111" xlink:href="#GroundDisconnector:地刀12_0" y="894.8333333333333" zvalue="281"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454855426050" ObjectName="#1发电机69117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454855426050"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,541.361,909.833) scale(1,1) translate(0,0)" width="12" x="535.3611111111111" y="894.8333333333333"/></g>
  <g id="110">
   <use class="kv6" height="30" transform="rotate(270,1438.44,792.333) scale(1,1) translate(0,0)" width="12" x="1432.444444444444" xlink:href="#GroundDisconnector:地刀12_0" y="777.3333333333333" zvalue="313"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454856146946" ObjectName="#2发电机62217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454856146946"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1438.44,792.333) scale(1,1) translate(0,0)" width="12" x="1432.444444444444" y="777.3333333333333"/></g>
  <g id="104">
   <use class="kv6" height="30" transform="rotate(270,1320.69,924.833) scale(1,1) translate(0,0)" width="12" x="1314.694444444444" xlink:href="#GroundDisconnector:地刀12_0" y="909.8333333333333" zvalue="319"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454856015874" ObjectName="#2发电机69217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454856015874"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1320.69,924.833) scale(1,1) translate(0,0)" width="12" x="1314.694444444444" y="909.8333333333333"/></g>
  <g id="138">
   <use class="kv6" height="30" transform="rotate(270,1050.11,815.333) scale(1,1) translate(0,0)" width="12" x="1044.111111111111" xlink:href="#GroundDisconnector:地刀12_0" y="800.3333333333333" zvalue="339"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454856474626" ObjectName="#1站用变62317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454856474626"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1050.11,815.333) scale(1,1) translate(0,0)" width="12" x="1044.111111111111" y="800.3333333333333"/></g>
  <g id="36">
   <use class="kv110" height="40" transform="rotate(0,962,520) scale(1,-1) translate(0,-1040)" width="40" x="942" xlink:href="#GroundDisconnector:中性点地刀12_0" y="500" zvalue="377"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454853459970" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454853459970"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,962,520) scale(1,-1) translate(0,-1040)" width="40" x="942" y="500"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="191">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="191" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136621092866" ObjectName="F"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136626860034" ObjectName="F"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136626925570" ObjectName="F"/>
   </metadata>
  </g>
  <g id="247">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="247" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,357.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="362.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136624173058" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136626728962" ObjectName="F"/>
   </metadata>
  </g>
  <g id="175">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="175" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136626794498" ObjectName="F"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127181680645" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127181615109" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="278">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="278" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,380.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="385.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136656613378" ObjectName="F"/>
   </metadata>
  </g>
  <g id="280">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="280" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,400.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="405.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136656941058" ObjectName="F"/>
   </metadata>
  </g>
  <g id="281">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="281" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,331.611,380.389) scale(1,1) translate(0,0)" writing-mode="lr" x="331.77" xml:space="preserve" y="385.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136656678914" ObjectName="F"/>
   </metadata>
  </g>
  <g id="282">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="282" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,331.611,400.389) scale(1,1) translate(0,0)" writing-mode="lr" x="331.77" xml:space="preserve" y="405.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136656875522" ObjectName="F"/>
   </metadata>
  </g>
  <g id="286">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="286" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1397.64,19.2599) scale(1,1) translate(0,9.02366e-15)" writing-mode="lr" x="1397.17" xml:space="preserve" y="23.97" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136616374274" ObjectName="P"/>
   </metadata>
  </g>
  <g id="288">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="288" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1397.64,47.7064) scale(1,1) translate(0,5.32383e-14)" writing-mode="lr" x="1397.17" xml:space="preserve" y="52.42" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136616439810" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="289">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="289" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1397.64,76.1528) scale(1,1) translate(0,9.74529e-14)" writing-mode="lr" x="1397.17" xml:space="preserve" y="80.87" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136616505346" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="293">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="293" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,604.466,967.491) scale(1,1) translate(0,-1.06036e-12)" writing-mode="lr" x="603.91" xml:space="preserve" y="972.1900000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136619520002" ObjectName="P"/>
   </metadata>
  </g>
  <g id="294">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="294" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1388.8,977.416) scale(1,1) translate(0,-1.07138e-12)" writing-mode="lr" x="1388.25" xml:space="preserve" y="982.11" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136625352706" ObjectName="P"/>
   </metadata>
  </g>
  <g id="295">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="295" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,604.466,993.722) scale(1,1) translate(0,-1.08948e-12)" writing-mode="lr" x="603.91" xml:space="preserve" y="998.42" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136619585538" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="296">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="296" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1388.8,1003.65) scale(1,1) translate(0,-1.1005e-12)" writing-mode="lr" x="1388.25" xml:space="preserve" y="1008.34" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136625418242" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="297">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="297" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,604.466,1019.95) scale(1,1) translate(0,4.47441e-13)" writing-mode="lr" x="603.91" xml:space="preserve" y="1024.65" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136619651074" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="298">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="298" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1388.8,1029.88) scale(1,1) translate(0,1.12962e-12)" writing-mode="lr" x="1388.25" xml:space="preserve" y="1034.58" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136625483778" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,725.573,509.643) scale(1,1) translate(0,0)" writing-mode="lr" x="725.1" xml:space="preserve" y="514.42" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136620699650" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="2" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1493.27,462.171) scale(1,1) translate(0,0)" writing-mode="lr" x="1492.8" xml:space="preserve" y="466.95" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136623779842" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,725.573,538.643) scale(1,1) translate(0,0)" writing-mode="lr" x="725.1" xml:space="preserve" y="543.42" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136620765186" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="4" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1493.27,491.171) scale(1,1) translate(0,0)" writing-mode="lr" x="1492.8" xml:space="preserve" y="495.95" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136623845378" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="5" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,725.573,567.643) scale(1,1) translate(0,0)" writing-mode="lr" x="725.1" xml:space="preserve" y="572.42" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136620830722" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="6" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1493.27,520.171) scale(1,1) translate(0,0)" writing-mode="lr" x="1492.8" xml:space="preserve" y="524.95" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136623910914" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="7" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,516.573,318.143) scale(1,1) translate(0,0)" writing-mode="lr" x="516.1" xml:space="preserve" y="322.92" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136620961794" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="8" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,507.268,739.671) scale(1,1) translate(0,0)" writing-mode="lr" x="506.8" xml:space="preserve" y="744.45" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136624041986" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="9" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1194.19,380.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1193.64" xml:space="preserve" y="384.92" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136621748226" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="10" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1194.19,409.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1193.64" xml:space="preserve" y="413.92" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136621813762" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="11" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1205.19,555.32) scale(1,1) translate(0,0)" writing-mode="lr" x="1204.64" xml:space="preserve" y="559.6" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136621879298" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="12" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1205.19,584.32) scale(1,1) translate(0,0)" writing-mode="lr" x="1204.64" xml:space="preserve" y="588.6" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136621944834" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="13" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1194.19,438.643) scale(1,1) translate(0,0)" writing-mode="lr" x="1193.64" xml:space="preserve" y="442.92" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136622010370" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="14" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1205.19,613.32) scale(1,1) translate(0,0)" writing-mode="lr" x="1204.64" xml:space="preserve" y="617.6" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136622338050" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="17" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,719.672,27.3214) scale(1,1) translate(2.24711e-13,-2.77119e-14)" writing-mode="lr" x="719.2" xml:space="preserve" y="32.03" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136617947138" ObjectName="P"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="93" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,719.672,64.3214) scale(1,1) translate(2.24711e-13,5.64738e-15)" writing-mode="lr" x="719.2" xml:space="preserve" y="69.03" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136618012674" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="94" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,718.244,99.8929) scale(1,1) translate(-1.4949e-13,9.5966e-15)" writing-mode="lr" x="717.77" xml:space="preserve" y="104.61" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136618078210" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="189">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="436"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374929166337" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="437"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562962195677185" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
 </g>
</svg>