<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549583544322" height="1045" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1045" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Compensator:西郊变电容_0" viewBox="0,0,24,40">
   <use terminal-index="0" type="0" x="12" xlink:href="#terminal" y="2.449999999999999"/>
   <rect fill-opacity="0" height="5.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12,23) scale(1,1) translate(0,0)" width="3" x="10.5" y="20.17"/>
   <path d="M 12 17.85 L 17.85 17.85 L 17.85 22.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="6.5" y1="33.10833333333333" y2="33.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.471296296296289" x2="6.471296296296289" y1="30.88611111111111" y2="33.12685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.50833333333333" x2="6.50833333333333" y1="20.10833333333333" y2="17.90462962962963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="29.60833333333333" y2="33.12685185185185"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="22.60833333333333" y2="28.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="17.79351851851851" y2="22.525"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.94090594744122" x2="6.94090594744122" y1="36.94166666666666" y2="34.94166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.416666666666666" x2="12.00833333333334" y1="17.85833333333333" y2="17.85833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.00833333333333" x2="12.00833333333333" y1="33.10833333333333" y2="37.10833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.09166666666667" x2="17.09166666666667" y1="34.94166666666666" y2="36.94166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.758333333333324" x2="2.758333333333324" y1="19.85833333333333" y2="31.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.08333333333333" x2="7.000000000000002" y1="37.10833333333333" y2="37.10833333333333"/>
   <path d="M 6.50833 23.7072 A 2.96392 1.81747 180 0 1 6.50833 20.0723" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.50833 27.3421 A 2.96392 1.81747 180 0 1 6.50833 23.7072" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 6.50833 30.8771 A 2.96392 1.81747 180 0 1 6.50833 27.2421" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="29.60833333333333" y2="29.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.50833333333333" x2="10.50833333333333" y1="28.60833333333333" y2="28.60833333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12" x2="12" y1="2.25" y2="4.916666666666668"/>
   <path d="M 7.26667 9.85 A 4.91667 4.75 -450 1 0 12.0167 4.93333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 7.33333 9.83333 L 12 9.91667 L 12 18" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="20.56666666666668" y2="21.08015580397416"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.28450340888154" x2="18.47116270499357" y1="26.7156109584975" y2="26.7156109584975"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510778" x2="17.14262023217246" y1="23.75922956383932" y2="22.95550743587977"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.50700202690252" x2="18.32283029297954" y1="27.0857461490052" y2="27.0857461490052"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="25.36667381975841" y2="26.33114037330986"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="16.98783858485353" x2="18.76782752902158" y1="26.34547576798984" y2="26.34547576798984"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510782" x2="17.83913764510782" y1="21.08015580397418" y2="23.73243882624067"/>
   <rect fill-opacity="0" height="4.29" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,17.83,23.22) scale(1,1) translate(0,0)" width="2.33" x="16.67" y="21.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="17.83913764510781" x2="18.53565505804314" y1="23.75922956383932" y2="22.95550743587977"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000004" y2="0.9166666666666679"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.89694619969018" y2="15.89694619969018"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.73157590710537" y2="17.73157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.48348701738202" y2="19.48348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.05" x2="5.05" y1="3.566666666666666" y2="15.81666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀（规范制图）_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:10kV母线PT带消谐装置_0" viewBox="0,0,35,30">
   <use terminal-index="0" type="0" x="17.56245852479325" xlink:href="#terminal" y="28.12373692455963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.61245852479333" x2="34.61245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.86245852479331" x2="30.86245852479331" y1="22.68299618381883" y2="19.84040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.6124585247933" x2="26.6124585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.61245852479332" x2="34.61245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666667" x2="31" y1="22.69225544307809" y2="22.69225544307809"/>
   <rect fill-opacity="0" height="3.55" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,30.81,15.38) scale(1,1) translate(0,0)" width="8.92" x="26.34" y="13.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.647721108666" x2="30.647721108666" y1="10.83674821859629" y2="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.71438777533266" x2="28.71438777533266" y1="7.51612768565195" y2="7.51612768565195"/>
   <ellipse cx="14.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.05048569403981" x2="22.05048569403981" y1="34.53084700683308" y2="34.53084700683308"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.7608382776216" x2="28.48402243293775" y1="7.754208141873304" y2="7.754208141873304"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.01083827762157" x2="29.40068909960439" y1="6.50420814187332" y2="6.50420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.51083827762159" x2="30.06735576627107" y1="5.004208141873296" y2="5.004208141873296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.89410730945214" x2="23.0877735452272" y1="24.15299989806297" y2="21.53208583485582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58713830216066" x2="22.81883560169719" y1="21.54040359122622" y2="21.54040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51457106407813" x2="19.32090482830307" y1="24.05654513693459" y2="21.43563107372743"/>
   <ellipse cx="14.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.3644003886642" x2="22.3644003886642" y1="30.19213090500035" y2="30.19213090500035"/>
   <ellipse cx="21.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.19220382559271" x2="17.19220382559271" y1="23.07076893362116" y2="23.07076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.4422038255927" x2="16.4422038255927" y1="16.82076893362115" y2="16.82076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.92264294445647" x2="20.87303257583197" y1="16.99992879670395" y2="15.47611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.873032575832" x2="20.37063985073817" y1="15.47611741162252" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.87303257583199" x2="23.32581493230132" y1="15.47611741162254" y2="16.60543752773795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.94220382559271" x2="16.94220382559271" y1="16.32076893362115" y2="16.32076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="16.07581493230131" y1="22.72611741162255" y2="23.85543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="13.12063985073816" y1="22.72611741162255" y2="20.07298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583197" y1="24.24992879670398" y2="22.72611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="16.07581493230131" y1="15.47611741162254" y2="16.60543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="13.12063985073816" y1="15.47611741162253" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583196" y1="16.99992879670396" y2="15.47611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.59103363843511" x2="17.59103363843511" y1="25.15822158129307" y2="28.16666666666667"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(0,255,127)" fill-opacity="1" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <rect height="12.17" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.75,25.42) scale(1,1) translate(0,0)" width="6" x="8.75" y="19.33"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="16.33333333333333" y2="31.58333333333333"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_1" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <rect fill-opacity="0" height="8" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(255,0,0)" fill-opacity="1" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="7.333333333333334" y2="41.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
   <rect height="12.17" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.75,25.42) scale(1,1) translate(0,0)" width="6" x="8.75" y="19.33"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_2" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.75" x2="14.75" y1="17" y2="32"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.65" x2="8.65" y1="17.1" y2="32.1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(0,255,127)" fill-opacity="1" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="32.35" y2="41.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.71666666666667" x2="11.71666666666667" y1="17.05" y2="7.466666666666663"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_0" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="3.333333333333332" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="24.08333333333333" y2="29.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="7" y1="19.5" y2="24.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_1" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="18.25" y2="32.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_2" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="4" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="10" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="25.83333333333333" y2="32.41666666666667"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变1接点_0" viewBox="0,0,26,38">
   <use terminal-index="0" type="0" x="9.116666666666667" xlink:href="#terminal" y="0.2500000000000071"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.45" x2="25.45" y1="30" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.45" x2="24.45" y1="31" y2="31"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="33" y2="38"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.45" x2="23.45" y1="32" y2="32"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="23" y1="24" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="19" y1="24" y2="30"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="23" y1="24" y2="24"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="2.25" y2="0.25"/>
   <line fill="none" stroke="rgb(255,85,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="9" y1="30" y2="37"/>
   <ellipse cx="9.210000000000001" cy="10.97" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="9.201922857336841" y1="6.6" y2="10.70894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="5.216666666666669" y1="10.73394833233988" y2="13.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.22969569739165" x2="13.31666666666667" y1="10.68990500916851" y2="13.5"/>
   <ellipse cx="9.210000000000001" cy="24.47" fill-opacity="0" rx="8.66" ry="8.66" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="9.201922857336841" y1="20.1" y2="24.20894833233987"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.201922857336841" x2="5.216666666666669" y1="24.23394833233988" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.22969569739165" x2="13.31666666666667" y1="24.18990500916851" y2="27"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="EnergyConsumer:矿选机组_0" viewBox="0,0,27,14">
   <use terminal-index="0" type="0" x="13.5" xlink:href="#terminal" y="0"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.5,7.75) scale(1,1) translate(0,0)" width="24" x="1.5" y="4.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="13.5" y1="0.25" y2="4.25"/>
  </symbol>
  <symbol id="Breaker:小车母联_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="17.58333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车母联_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="0.8333333333333304" y2="5.749999999999999"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车母联_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="14" y2="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.666666666666666" x2="7.25" y1="5.833333333333333" y2="14.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:20210813_0" viewBox="0,0,31,30">
   <use terminal-index="0" type="1" x="15.5" xlink:href="#terminal" y="0.5"/>
   <path d="M 11.25 12 L 20.3333 12 L 15.5833 4.66667 z" fill-opacity="0" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.44" cy="8.859999999999999" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:20210813_1" viewBox="0,0,31,30">
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.092684042066761" x2="9.092684042066761" y1="21.48811156835848" y2="25.52061970395131"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.105281207133061" x2="5.039204657102427" y1="21.39014631915866" y2="17.69460232855122"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.31666666666667" x2="9.097965249199818" y1="17.44460232855121" y2="21.39014631915866"/>
   <use terminal-index="1" type="1" x="15.66666666666667" xlink:href="#terminal" y="26.16666666666667"/>
   <ellipse cx="9.18" cy="20.94" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="22.25935070873343" x2="22.25935070873343" y1="21.48811156835848" y2="25.52061970395131"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="22.27194787379972" x2="18.20587132376909" y1="21.473479652492" y2="17.77793566188455"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.48333333333333" x2="22.26463191586648" y1="17.52793566188455" y2="21.473479652492"/>
   <ellipse cx="22.18" cy="20.86" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id="Accessory:传输线_0" viewBox="0,0,12,22">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="1"/>
   <path d="M 1.16667 1 L 10.8333 1 L 6 7.63889 L 1.16667 1 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="14.27777777777778" y2="7.638888888888888"/>
   <path d="M 1.16667 20.9167 L 10.8333 20.9167 L 6 14.2778 L 1.16667 20.9167 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV祥华变" InitShowingPlane="" fill="rgb(0,0,0)" height="1045" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="60.19" y="313.25" zvalue="186"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="236" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,96.625,325.25) scale(1,1) translate(0,0)" width="72.88" x="60.19" y="313.25" zvalue="186"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,96.625,325.25) scale(1,1) translate(0,0)" writing-mode="lr" x="96.63" xml:space="preserve" y="329.75" zvalue="186">信号一览</text>
  <image height="60" id="2" preserveAspectRatio="xMidYMid slice" width="273.81" x="54.44" xlink:href="logo.png" y="53.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,191.347,83.75) scale(1,1) translate(-1.20891e-14,0)" writing-mode="lr" x="191.35" xml:space="preserve" y="87.25" zvalue="361"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,184.833,81.8153) scale(1,1) translate(2.22045e-14,0)" writing-mode="lr" x="184.83" xml:space="preserve" y="90.81999999999999" zvalue="362">35kV祥华变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,612.076,343.591) scale(1,1) translate(0,0)" writing-mode="lr" x="612.08" xml:space="preserve" y="348.09" zvalue="149">35kV母线</text>
  <line fill="none" id="279" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="375" x2="375" y1="49.5" y2="1039.5" zvalue="155"/>
  <line fill="none" id="273" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.47692454998014" x2="318.0881846035993" y1="169.1279458827686" y2="169.1279458827686" zvalue="159"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="928.0627909390723" y2="928.0627909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="979.4022909390724" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="41.57142857142856" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0114285714285" x2="113.0114285714285" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="347.5719285714285" y1="928.0627909390723" y2="928.0627909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="347.5719285714285" y1="979.4022909390724" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="113.0119285714286" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.5719285714285" x2="347.5719285714285" y1="928.0627909390723" y2="979.4022909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="41.57142857142856" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0114285714285" x2="113.0114285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="113.0119285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8093285714285" x2="265.8093285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="979.4022709390724" y2="979.4022709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="1006.879770939072" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="265.8092285714285" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.5713285714286" x2="347.5713285714286" y1="979.4022709390724" y2="1006.879770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="113.0114285714285" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="41.57142857142856" x2="41.57142857142856" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0114285714285" x2="113.0114285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="184.0472285714286" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="113.0119285714286" x2="113.0119285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="265.8093285714285" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.0472285714286" x2="184.0472285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8093285714285" x2="265.8093285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="1006.879690939072" y2="1006.879690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="347.5713285714286" y1="1034.357190939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="265.8092285714285" x2="265.8092285714285" y1="1006.879690939072" y2="1034.357190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="347.5713285714286" x2="347.5713285714286" y1="1006.879690939072" y2="1034.357190939072"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="271" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,188.624,957.636) scale(1,1) translate(-1.04198e-14,1.05117e-13)" writing-mode="lr" x="46.93" xml:space="preserve" y="963.64" zvalue="161">参考图号       XiangHua-01-2024</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="270" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,119.149,995.005) scale(1,1) translate(5.03187e-14,-1.52972e-12)" writing-mode="lr" x="56.65" xml:space="preserve" y="1001" zvalue="162">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="269" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,262.452,996.005) scale(1,1) translate(1.29063e-13,-1.53127e-12)" writing-mode="lr" x="193.75" xml:space="preserve" y="1002" zvalue="163">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="267" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70.2423,1024.56) scale(1,1) translate(-4.58202e-14,1.01293e-12)" writing-mode="lr" x="70.23999999999999" xml:space="preserve" y="1030.56" zvalue="164">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="265" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,270.746,1022.56) scale(1,1) translate(0,1.12325e-13)" writing-mode="lr" x="193.92" xml:space="preserve" y="1028.56" zvalue="165">更新日期</text>
  <line fill="none" id="264" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="40.85611355802348" x2="345.4673736116426" y1="623.8945306693694" y2="623.8945306693694" zvalue="166"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.9207,640.936) scale(1,1) translate(2.94653e-15,-1.38164e-13)" writing-mode="lr" x="83.92070806204504" xml:space="preserve" y="645.4363811688671" zvalue="168">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="172.3571428571429" y2="172.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="172.3571428571429" y2="172.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="172.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="198.3571428571429" y2="198.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="198.3571428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="222.6071428571429" y2="222.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="222.6071428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="245.3571428571429" y2="245.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="245.3571428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="183.5714285714284" y1="290.8571428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="2.571428571428442" x2="2.571428571428442" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="268.1071428571429" y2="268.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="364.5714285714284" y1="290.8571428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="183.5714285714284" x2="183.5714285714284" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="364.5714285714284" x2="364.5714285714284" y1="268.1071428571429" y2="290.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="452.3571428571429" y2="452.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="452.3571428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="490.6394428571429" y2="490.6394428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="490.6394428571429" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="515.3188428571428" y2="515.3188428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="515.3188428571428" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="539.9982428571429" y2="539.9982428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="564.6776428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="539.9982428571429" y2="564.6776428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="564.6777428571429" y2="564.6777428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="564.6777428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="99.34592857142854" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="53.57142857142856" x2="53.57142857142856" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="158.1523285714286" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="99.34592857142854" x2="99.34592857142854" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="216.9587285714285" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="158.1523285714286" x2="158.1523285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9587285714285" x2="216.9587285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="275.7650285714285" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="216.9586285714286" x2="216.9586285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="589.3571428571429" y2="589.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="334.5714285714286" y1="614.0365428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="275.7650285714285" x2="275.7650285714285" y1="589.3571428571429" y2="614.0365428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.5714285714286" x2="334.5714285714286" y1="589.3571428571429" y2="614.0365428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="257" stroke="rgb(255,255,255)" text-anchor="middle" x="130.578125" xml:space="preserve" y="467.359375" zvalue="171">35kV     母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="257" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="130.578125" xml:space="preserve" y="483.359375" zvalue="171">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="256" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.97,327.199) scale(1,1) translate(0,0)" writing-mode="lr" x="199.97" xml:space="preserve" y="331.7" zvalue="172">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="255" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,304.97,327.199) scale(1,1) translate(0,0)" writing-mode="lr" x="304.97" xml:space="preserve" y="331.7" zvalue="173">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,502.857) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="507.3571428571429" zvalue="175">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="250" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,528.357) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="532.8571428571429" zvalue="176">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="248" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,553.857) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="558.3571428571429" zvalue="177">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,579.357) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="583.8571428571429" zvalue="178">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5714,604.857) scale(1,1) translate(0,0)" writing-mode="lr" x="78.57142857142856" xml:space="preserve" y="609.3571428571429" zvalue="179">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="244" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,40.5714,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="40.57" xml:space="preserve" y="191.86" zvalue="180">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="243" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.821,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="226.82" xml:space="preserve" y="191.86" zvalue="181">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="242" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.2589,210.607) scale(1,1) translate(0,0)" writing-mode="lr" x="44.26" xml:space="preserve" y="215.11" zvalue="182">35kV母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="239" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,47.7589,234.357) scale(1,1) translate(0,0)" writing-mode="lr" x="47.76" xml:space="preserve" y="238.86" zvalue="183">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="213" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1176.5,579.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1176.5" xml:space="preserve" y="584" zvalue="209">8000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1108,422.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1108" xml:space="preserve" y="427" zvalue="211">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1054,483.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1054" xml:space="preserve" y="488" zvalue="217">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1018,294.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1018" xml:space="preserve" y="299" zvalue="222">372</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,994.088,137) scale(1,1) translate(0,0)" writing-mode="lr" x="994.09" xml:space="preserve" y="141.5" zvalue="224">35kV彦祥线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1308,314.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1308" xml:space="preserve" y="319" zvalue="228">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1271.69,215) scale(1,1) translate(0,0)" writing-mode="lr" x="1271.69" xml:space="preserve" y="219.5" zvalue="230">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,768.872,479.5) scale(1,1) translate(0,0)" writing-mode="lr" x="768.87" xml:space="preserve" y="484" zvalue="235">35kV1号站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,795,390.5) scale(1,1) translate(0,0)" writing-mode="lr" x="795" xml:space="preserve" y="395" zvalue="236">3711</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1322,591.695) scale(1,1) translate(0,0)" writing-mode="lr" x="1322" xml:space="preserve" y="596.1900000000001" zvalue="259">备用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1547,430.695) scale(1,1) translate(0,0)" writing-mode="lr" x="1547" xml:space="preserve" y="435.19" zvalue="268">374</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1493,491.695) scale(1,1) translate(0,-1.07291e-13)" writing-mode="lr" x="1493" xml:space="preserve" y="496.19" zvalue="270">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1524.56,675.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1524.56" xml:space="preserve" y="680" zvalue="275">35kV1号电容器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,954.5,699.5) scale(1,1) translate(0,0)" writing-mode="lr" x="954.5" xml:space="preserve" y="704" zvalue="308">401</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,937.729,808) scale(1,1) translate(0,0)" writing-mode="lr" x="937.73" xml:space="preserve" y="814" zvalue="310">1.1kV1号矿选机组</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1257.5,703.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1257.5" xml:space="preserve" y="708" zvalue="314">402</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1240.73,812) scale(1,1) translate(0,0)" writing-mode="lr" x="1240.73" xml:space="preserve" y="818" zvalue="316">1.1kV2号矿选机组</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,939,844.5) scale(1,1) translate(0,0)" writing-mode="lr" x="939" xml:space="preserve" y="850.5" zvalue="320">3500kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1235,844.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1235" xml:space="preserve" y="850.5" zvalue="322">3500kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1495.5,548.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1495.5" xml:space="preserve" y="553" zvalue="329">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1576,548.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1576" xml:space="preserve" y="553" zvalue="331">8</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1532.5,598.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1532.5" xml:space="preserve" y="603" zvalue="333">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1615.5,598.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1615.5" xml:space="preserve" y="603" zvalue="335">87</text>
  <rect fill="none" fill-opacity="0" height="231" id="48" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1076.5,753) scale(1,1) translate(0,0)" width="585" x="784" y="637.5" zvalue="344"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1090,737.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1090" xml:space="preserve" y="743.5" zvalue="346">生产车间配电室</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1173,555.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1173" xml:space="preserve" y="560" zvalue="348">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,255.571,502.857) scale(1,1) translate(0,0)" writing-mode="lr" x="255.5714285714286" xml:space="preserve" y="507.3571428571429" zvalue="372">Ubc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,256.571,526.857) scale(1,1) translate(0,0)" writing-mode="lr" x="256.5714285714286" xml:space="preserve" y="531.3571428571429" zvalue="375">Uca</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="284">
   <path class="kv35" d="M 621.67 356.59 L 1590.29 356.59" stroke-width="6" zvalue="148"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674238267396" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674238267396"/></metadata>
  <path d="M 621.67 356.59 L 1590.29 356.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="235">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="235" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,127.571,210.468) scale(1,1) translate(0,0)" writing-mode="lr" x="127.21" xml:space="preserve" y="216.64" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124645830661" ObjectName="F"/>
   </metadata>
  </g>
  <g id="231">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="231" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,127.571,186.5) scale(1,1) translate(0,0)" writing-mode="lr" x="127.21" xml:space="preserve" y="192.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124651597829" ObjectName=""/>
   </metadata>
  </g>
  <g id="230">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="230" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,304.571,186.357) scale(1,1) translate(0,0)" writing-mode="lr" x="304.21" xml:space="preserve" y="192.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124651663365" ObjectName=""/>
   </metadata>
  </g>
  <g id="229">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="229" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,123,234.552) scale(1,1) translate(-2.22045e-14,0)" writing-mode="lr" x="122.76" xml:space="preserve" y="240.75" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124650024965" ObjectName="YW1"/>
   </metadata>
  </g>
  <g id="227">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="227" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,125.071,528.093) scale(1,1) translate(0,0)" writing-mode="lr" x="124.76" xml:space="preserve" y="532.76" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124645437445" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="226">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="226" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,125.071,553.218) scale(1,1) translate(0,-1.20508e-13)" writing-mode="lr" x="124.76" xml:space="preserve" y="557.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124645502981" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="225">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="225" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,125.071,578.343) scale(1,1) translate(0,-2.52173e-13)" writing-mode="lr" x="124.76" xml:space="preserve" y="583.01" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124645568517" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="224">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="224" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,126.5,502.968) scale(1,1) translate(0,0)" writing-mode="lr" x="126.19" xml:space="preserve" y="507.63" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124645699591" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="217">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="217" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,124.321,603.468) scale(1,1) translate(0,0)" writing-mode="lr" x="124.01" xml:space="preserve" y="608.13" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124645896197" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="152">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="152" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,994.088,66) scale(1,1) translate(0,0)" writing-mode="lr" x="993.65" xml:space="preserve" y="70.67" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124643471365" ObjectName="P"/>
   </metadata>
  </g>
  <g id="151">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="151" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,994.088,84) scale(1,1) translate(0,0)" writing-mode="lr" x="993.65" xml:space="preserve" y="88.67" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124643536901" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="150" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,994.088,103) scale(1,1) translate(0,0)" writing-mode="lr" x="993.65" xml:space="preserve" y="107.67" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124643602437" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="12" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1193.5,402.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1192.95" xml:space="preserve" y="408.69" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124649566213" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="13" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1193.25,439) scale(1,1) translate(0,0)" writing-mode="lr" x="1192.7" xml:space="preserve" y="445.19" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124649631749" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="17" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1193.25,476) scale(1,1) translate(0,0)" writing-mode="lr" x="1192.7" xml:space="preserve" y="482.19" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124649828357" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="26">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="26" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1532.56,704.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1532.09" xml:space="preserve" y="708.83" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124639801349" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="52" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1532.56,720.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1532.09" xml:space="preserve" y="725" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124640063493" ObjectName="Cos"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,303.5,502.968) scale(1,1) translate(0,0)" writing-mode="lr" x="303.19" xml:space="preserve" y="507.63" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124645765125" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="60">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,304.5,526.968) scale(1,1) translate(0,0)" writing-mode="lr" x="304.19" xml:space="preserve" y="531.63" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124645634055" ObjectName="Uca"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="233">
   <use height="30" transform="rotate(0,334.196,327.857) scale(0.708333,0.665547) translate(133.235,159.739)" width="30" x="323.57" xlink:href="#State:红绿圆(方形)_0" y="317.87" zvalue="189"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374888992771" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.196,327.857) scale(0.708333,0.665547) translate(133.235,159.739)" width="30" x="323.57" y="317.87"/></g>
  <g id="232">
   <use height="30" transform="rotate(0,238.571,327.857) scale(0.708333,0.665547) translate(93.8603,159.739)" width="30" x="227.95" xlink:href="#State:红绿圆(方形)_0" y="317.87" zvalue="190"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562951071137797" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,238.571,327.857) scale(0.708333,0.665547) translate(93.8603,159.739)" width="30" x="227.95" y="317.87"/></g>
  <g id="146">
   <use height="30" transform="rotate(0,315.812,140.964) scale(1.27778,1.03333) translate(-56.1549,-4.04722)" width="90" x="258.31" xlink:href="#State:全站检修_0" y="125.46" zvalue="364"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549583544322" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,315.812,140.964) scale(1.27778,1.03333) translate(-56.1549,-4.04722)" width="90" x="258.31" y="125.46"/></g>
 </g>
 <g id="BreakerClass">
  <g id="211">
   <use class="kv35" height="20" transform="rotate(0,1085,423.5) scale(2.2,2.2) translate(-585.818,-219)" width="10" x="1074" xlink:href="#Breaker:小车断路器_0" y="401.5" zvalue="210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924490493957" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924490493957"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1085,423.5) scale(2.2,2.2) translate(-585.818,-219)" width="10" x="1074" y="401.5"/></g>
  <g id="199">
   <use class="kv35" height="20" transform="rotate(0,994,295.5) scale(2.2,2.2) translate(-536.182,-149.182)" width="10" x="983" xlink:href="#Breaker:小车断路器_0" y="273.5" zvalue="221"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924490428421" ObjectName="35kV彦祥线372断路器"/>
   <cge:TPSR_Ref TObjectID="6473924490428421"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,994,295.5) scale(2.2,2.2) translate(-536.182,-149.182)" width="10" x="983" y="273.5"/></g>
  <g id="175">
   <use class="kv35" height="20" transform="rotate(0,1321,430.695) scale(2.2,2.2) translate(-714.545,-222.924)" width="10" x="1310" xlink:href="#Breaker:小车断路器_0" y="408.6948051948052" zvalue="253"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924490362885" ObjectName="备用023断路器"/>
   <cge:TPSR_Ref TObjectID="6473924490362885"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1321,430.695) scale(2.2,2.2) translate(-714.545,-222.924)" width="10" x="1310" y="408.6948051948052"/></g>
  <g id="161">
   <use class="kv35" height="20" transform="rotate(0,1524,431.695) scale(2.2,2.2) translate(-825.273,-223.47)" width="10" x="1513" xlink:href="#Breaker:小车母联_0" y="409.6948051948052" zvalue="267"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924490297349" ObjectName="35kV1号电容器374断路器"/>
   <cge:TPSR_Ref TObjectID="6473924490297349"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1524,431.695) scale(2.2,2.2) translate(-825.273,-223.47)" width="10" x="1513" y="409.6948051948052"/></g>
  <g id="15">
   <use class="v400" height="20" transform="rotate(0,935,700.5) scale(1.5,1.35) translate(-309.167,-178.111)" width="10" x="927.5" xlink:href="#Breaker:开关_0" y="687" zvalue="307"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924490559493" ObjectName="1.1kV1号矿选机组401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924490559493"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,935,700.5) scale(1.5,1.35) translate(-309.167,-178.111)" width="10" x="927.5" y="687"/></g>
  <g id="25">
   <use class="v400" height="20" transform="rotate(0,1238,704.5) scale(1.5,1.35) translate(-410.167,-179.148)" width="10" x="1230.5" xlink:href="#Breaker:开关_0" y="691" zvalue="313"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924490625029" ObjectName="1.1kV2号矿选机组402断路器"/>
   <cge:TPSR_Ref TObjectID="6473924490625029"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1238,704.5) scale(1.5,1.35) translate(-410.167,-179.148)" width="10" x="1230.5" y="691"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="209">
   <path class="kv35" d="M 1085 403.15 L 1085 356.59" stroke-width="1" zvalue="212"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@0" LinkObjectIDznd="284@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1085 403.15 L 1085 356.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv35" d="M 994 315.3 L 994 356.59" stroke-width="1" zvalue="226"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@1" LinkObjectIDznd="284@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 994 315.3 L 994 356.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv35" d="M 1277.74 273.87 L 1277.75 295.75" stroke-width="1" zvalue="231"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@0" LinkObjectIDznd="195@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1277.74 273.87 L 1277.75 295.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="kv35" d="M 1277.75 330.25 L 1277.75 356.59" stroke-width="1" zvalue="232"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@0" LinkObjectIDznd="284@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1277.75 330.25 L 1277.75 356.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv35" d="M 769 375.5 L 769 356.59" stroke-width="1" zvalue="237"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@1" LinkObjectIDznd="284@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 769 375.5 L 769 356.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv35" d="M 768.99 407.8 L 768.99 423.75" stroke-width="1" zvalue="238"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@0" LinkObjectIDznd="191@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 768.99 407.8 L 768.99 423.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="171">
   <path class="kv35" d="M 1321 410.34 L 1321 356.59" stroke-width="1" zvalue="260"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@0" LinkObjectIDznd="284@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1321 410.34 L 1321 356.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv35" d="M 1321 450.49 L 1321 541.04" stroke-width="1" zvalue="261"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@1" LinkObjectIDznd="172@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1321 450.49 L 1321 541.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv35" d="M 1275.05 483.19 L 1275.05 472.5 L 1321 472.5" stroke-width="1" zvalue="262"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="170" MaxPinNum="2"/>
   </metadata>
  <path d="M 1275.05 483.19 L 1275.05 472.5 L 1321 472.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv35" d="M 1370.03 481.33 L 1370.03 472.5 L 1320 472.5" stroke-width="1" zvalue="263"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="173@0" LinkObjectIDznd="168" MaxPinNum="2"/>
   </metadata>
  <path d="M 1370.03 481.33 L 1370.03 472.5 L 1320 472.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv35" d="M 1524 411.89 L 1524 356.59" stroke-width="1" zvalue="271"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="284@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1524 411.89 L 1524 356.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="kv35" d="M 1036.03 255.13 L 1036.03 247.5 L 994.03 247.5" stroke-width="1" zvalue="298"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@0" LinkObjectIDznd="56" MaxPinNum="2"/>
   </metadata>
  <path d="M 1036.03 255.13 L 1036.03 247.5 L 994.03 247.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv35" d="M 1085 443.3 L 1085 543.81" stroke-width="1" zvalue="304"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@1" LinkObjectIDznd="46@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1085 443.3 L 1085 543.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv35" d="M 1134.03 474.13 L 1134.03 467.5 L 1085 467.5" stroke-width="1" zvalue="305"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201@0" LinkObjectIDznd="10" MaxPinNum="2"/>
   </metadata>
  <path d="M 1134.03 474.13 L 1134.03 467.5 L 1085 467.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv35" d="M 1040.05 477 L 1040.05 467.5 L 1085 467.5" stroke-width="1" zvalue="306"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="11" MaxPinNum="2"/>
   </metadata>
  <path d="M 1040.05 477 L 1040.05 467.5 L 1085 467.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="v400" d="M 935.1 713.39 L 935.1 755.17" stroke-width="1" zvalue="311"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="15@1" LinkObjectIDznd="18@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 935.1 713.39 L 935.1 755.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv35" d="M 1483.56 604.77 L 1483.56 560.31" stroke-width="1" zvalue="335"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@0" LinkObjectIDznd="31@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1483.56 604.77 L 1483.56 560.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv35" d="M 1483.59 538.86 L 1483.59 527.5 L 1524 527.5 L 1524 450.94" stroke-width="1" zvalue="336"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31@0" LinkObjectIDznd="161@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1483.59 538.86 L 1483.59 527.5 L 1524 527.5 L 1524 450.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv35" d="M 1517.05 590 L 1517.05 575.5 L 1483.56 575.5" stroke-width="1" zvalue="337"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="39" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517.05 590 L 1517.05 575.5 L 1483.56 575.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv35" d="M 1479.05 485.19 L 1479.05 470.5 L 1524 470.5" stroke-width="1" zvalue="338"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="40" MaxPinNum="2"/>
   </metadata>
  <path d="M 1479.05 485.19 L 1479.05 470.5 L 1524 470.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv35" d="M 1579.03 481.33 L 1579.03 470.5 L 1524 470.5" stroke-width="1" zvalue="339"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="30@0" LinkObjectIDznd="42" MaxPinNum="2"/>
   </metadata>
  <path d="M 1579.03 481.33 L 1579.03 470.5 L 1524 470.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv35" d="M 1567 604.77 L 1567 560.31" stroke-width="1" zvalue="340"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="29@0" LinkObjectIDznd="33@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1567 604.77 L 1567 560.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv35" d="M 1567.59 538.86 L 1567.59 527.5 L 1524 527.5" stroke-width="1" zvalue="341"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="40" MaxPinNum="2"/>
   </metadata>
  <path d="M 1567.59 538.86 L 1567.59 527.5 L 1524 527.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="47">
   <path class="kv35" d="M 1567 576.5 L 1600.05 576.5 L 1600.05 590" stroke-width="1" zvalue="343"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44" LinkObjectIDznd="37@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1567 576.5 L 1600.05 576.5 L 1600.05 590" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="v400" d="M 1238.1 717.39 L 1238.1 759.17" stroke-width="1" zvalue="359"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="25@1" LinkObjectIDznd="24@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1238.1 717.39 L 1238.1 759.17" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="kv35" d="M 994 275.15 L 994.09 190.3" stroke-width="1" zvalue="377"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@0" LinkObjectIDznd="198@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 994 275.15 L 994.09 190.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv35" d="M 994 211.5 L 994.07 211.5" stroke-width="1" zvalue="379"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@0" LinkObjectIDznd="56" MaxPinNum="2"/>
   </metadata>
  <path d="M 994 211.5 L 994.07 211.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="v400" d="M 1084.69 611.29 L 1077.33 617.17 L 1068 619.83 L 1068 631.17 L 935.33 631.17 L 934.95 687.59" stroke-width="1" zvalue="383"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@1" LinkObjectIDznd="15@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1084.69 611.29 L 1077.33 617.17 L 1068 619.83 L 1068 631.17 L 935.33 631.17 L 934.95 687.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="v400" d="M 1084.69 611.29 L 1090.53 616.18 L 1096.84 619.34 L 1103.16 619.34 L 1103.16 629.87 L 1237.37 629.87 L 1237.95 691.59" stroke-width="1" zvalue="384"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="2" LinkObjectIDznd="25@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1084.69 611.29 L 1090.53 616.18 L 1096.84 619.34 L 1103.16 619.34 L 1103.16 629.87 L 1237.37 629.87 L 1237.95 691.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="205">
   <use class="kv35" height="20" transform="rotate(0,1040,486.5) scale(1,1) translate(0,0)" width="10" x="1035" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="476.5" zvalue="216"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449682538501" ObjectName="#1主变35kV侧30167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449682538501"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1040,486.5) scale(1,1) translate(0,0)" width="10" x="1035" y="476.5"/></g>
  <g id="174">
   <use class="kv35" height="20" transform="rotate(0,1275,492.695) scale(1,1) translate(0,0)" width="10" x="1270" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="482.6948051948052" zvalue="255"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449682014213" ObjectName="备用02367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449682014213"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1275,492.695) scale(1,1) translate(0,0)" width="10" x="1270" y="482.6948051948052"/></g>
  <g id="158">
   <use class="kv35" height="20" transform="rotate(0,1479,494.695) scale(1,1) translate(0,0)" width="10" x="1474" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="484.6948051948052" zvalue="269"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449681752070" ObjectName="35kV1号电容器37417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449681752070"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1479,494.695) scale(1,1) translate(0,0)" width="10" x="1474" y="484.6948051948052"/></g>
  <g id="35">
   <use class="kv35" height="20" transform="rotate(0,1517,599.5) scale(1,1) translate(0,0)" width="10" x="1512" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="589.5" zvalue="332"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449683324934" ObjectName="35kV1号电容器37467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449683324934"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1517,599.5) scale(1,1) translate(0,0)" width="10" x="1512" y="589.5"/></g>
  <g id="37">
   <use class="kv35" height="20" transform="rotate(0,1600,599.5) scale(1,1) translate(0,0)" width="10" x="1595" xlink:href="#GroundDisconnector:地刀（规范制图）_0" y="589.5" zvalue="334"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449683456006" ObjectName="35kV1号电容器37487接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449683456006"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1600,599.5) scale(1,1) translate(0,0)" width="10" x="1595" y="589.5"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="201">
   <use class="kv35" height="26" transform="rotate(0,1134,486.5) scale(1,1) translate(0,0)" width="12" x="1128" xlink:href="#Accessory:避雷器1_0" y="473.5" zvalue="219"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449682407429" ObjectName="#1主变35kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1134,486.5) scale(1,1) translate(0,0)" width="12" x="1128" y="473.5"/></g>
  <g id="194">
   <use class="kv35" height="30" transform="rotate(0,1277.69,255.5) scale(0.857143,1.4) translate(210.448,-67)" width="35" x="1262.687541475207" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="234.5" zvalue="229"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449682210822" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1277.69,255.5) scale(0.857143,1.4) translate(210.448,-67)" width="35" x="1262.687541475207" y="234.5"/></g>
  <g id="173">
   <use class="kv35" height="26" transform="rotate(0,1370,493.695) scale(1,1) translate(0,0)" width="12" x="1364" xlink:href="#Accessory:避雷器1_0" y="480.6948051452637" zvalue="257"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449681883141" ObjectName="备用避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1370,493.695) scale(1,1) translate(0,0)" width="12" x="1364" y="480.6948051452637"/></g>
  <g id="5">
   <use class="kv35" height="26" transform="rotate(0,1036,267.5) scale(1,1) translate(0,0)" width="12" x="1030" xlink:href="#Accessory:避雷器1_0" y="254.5" zvalue="295"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449682800645" ObjectName="35kV彦祥线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1036,267.5) scale(1,1) translate(0,0)" width="12" x="1030" y="254.5"/></g>
  <g id="30">
   <use class="kv35" height="26" transform="rotate(0,1579,493.695) scale(1,1) translate(0,0)" width="12" x="1573" xlink:href="#Accessory:避雷器1_0" y="480.6948051452637" zvalue="326"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449683062790" ObjectName="35kV1号电容器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1579,493.695) scale(1,1) translate(0,0)" width="12" x="1573" y="480.6948051452637"/></g>
  <g id="58">
   <use class="kv35" height="22" transform="rotate(0,994,221.5) scale(1,1) translate(0,0)" width="12" x="988" xlink:href="#Accessory:传输线_0" y="210.5" zvalue="378"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450451210245" ObjectName="彦祥线"/>
   </metadata>
  <rect fill="white" height="22" opacity="0" stroke="white" transform="rotate(0,994,221.5) scale(1,1) translate(0,0)" width="12" x="988" y="210.5"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="195">
   <use class="kv35" height="42" transform="rotate(0,1281,316.5) scale(1,-1) translate(0,-633)" width="30" x="1266" xlink:href="#Disconnector:手车刀闸带避雷器_0" y="295.5" zvalue="227"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449682276358" ObjectName="35KV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449682276358"/></metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1281,316.5) scale(1,-1) translate(0,-633)" width="30" x="1266" y="295.5"/></g>
  <g id="190">
   <use class="kv35" height="33" transform="rotate(0,769,391.5) scale(1,-1) translate(0,-783)" width="14" x="762" xlink:href="#Disconnector:手车隔离开关13_0" y="375" zvalue="234"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449682079750" ObjectName="35kV1号站用变3711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449682079750"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,769,391.5) scale(1,-1) translate(0,-783)" width="14" x="762" y="375"/></g>
  <g id="31">
   <use class="kv35" height="30" transform="rotate(0,1483.5,549.5) scale(1,0.733333) translate(0,195.818)" width="15" x="1476" xlink:href="#Disconnector:刀闸_0" y="538.5" zvalue="328"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449683128326" ObjectName="35kV1号电容器3746隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449683128326"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1483.5,549.5) scale(1,0.733333) translate(0,195.818)" width="15" x="1476" y="538.5"/></g>
  <g id="33">
   <use class="kv35" height="30" transform="rotate(0,1567.5,549.5) scale(1,0.733333) translate(0,195.818)" width="15" x="1560" xlink:href="#Disconnector:刀闸_0" y="538.5" zvalue="330"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449683193862" ObjectName="35kV1号电容器3748隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449683193862"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1567.5,549.5) scale(1,0.733333) translate(0,195.818)" width="15" x="1560" y="538.5"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="191">
   <use class="kv35" height="38" transform="rotate(0,772.872,442.5) scale(1,1) translate(0,0)" width="26" x="759.8716773608404" xlink:href="#EnergyConsumer:站用变1接点_0" y="423.5" zvalue="233"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449682145286" ObjectName="35kV1号站用变"/>
   </metadata>
  <rect fill="white" height="38" opacity="0" stroke="white" transform="rotate(0,772.872,442.5) scale(1,1) translate(0,0)" width="26" x="759.8716773608404" y="423.5"/></g>
  <g id="172">
   <use class="kv35" height="30" transform="rotate(0,1321,557.695) scale(1.25,-1.23333) translate(-262.7,-1006.38)" width="12" x="1313.5" xlink:href="#EnergyConsumer:负荷_0" y="539.1948051948052" zvalue="258"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449681817605" ObjectName="备用"/>
   <cge:TPSR_Ref TObjectID="6192449681817605"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1321,557.695) scale(1.25,-1.23333) translate(-262.7,-1006.38)" width="12" x="1313.5" y="539.1948051948052"/></g>
  <g id="18">
   <use class="v400" height="14" transform="rotate(0,935.1,799.833) scale(6.38095,6.38095) translate(-715.912,-636.82)" width="27" x="848.957142857143" xlink:href="#EnergyConsumer:矿选机组_0" y="755.166666666667" zvalue="309"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449682866182" ObjectName="1.1kV1号矿选机组"/>
   <cge:TPSR_Ref TObjectID="6192449682866182"/></metadata>
  <rect fill="white" height="14" opacity="0" stroke="white" transform="rotate(0,935.1,799.833) scale(6.38095,6.38095) translate(-715.912,-636.82)" width="27" x="848.957142857143" y="755.166666666667"/></g>
  <g id="24">
   <use class="v400" height="14" transform="rotate(0,1238.1,803.833) scale(6.38095,6.38095) translate(-971.427,-640.193)" width="27" x="1151.957142857143" xlink:href="#EnergyConsumer:矿选机组_0" y="759.166666666667" zvalue="315"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449682931718" ObjectName="1.1kV2号矿选机组"/>
   <cge:TPSR_Ref TObjectID="6192449682931718"/></metadata>
  <rect fill="white" height="14" opacity="0" stroke="white" transform="rotate(0,1238.1,803.833) scale(6.38095,6.38095) translate(-971.427,-640.193)" width="27" x="1151.957142857143" y="759.166666666667"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="153">
   <use class="kv35" height="40" transform="rotate(0,1483.56,628.167) scale(1.33333,1.33333) translate(-366.89,-150.375)" width="24" x="1467.561164424106" xlink:href="#Compensator:西郊变电容_0" y="601.4999949137373" zvalue="274"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449681620997" ObjectName="35kV1号电容器"/>
   <cge:TPSR_Ref TObjectID="6192449681620997"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1483.56,628.167) scale(1.33333,1.33333) translate(-366.89,-150.375)" width="24" x="1467.561164424106" y="601.4999949137373"/></g>
  <g id="29">
   <use class="kv35" height="40" transform="rotate(0,1567,628.167) scale(1.33333,1.33333) translate(-387.75,-150.375)" width="24" x="1551" xlink:href="#Compensator:西郊变电容_0" y="601.4999952316284" zvalue="324"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449682997254" ObjectName="35kV1号电容器2"/>
   <cge:TPSR_Ref TObjectID="6192449682997254"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1567,628.167) scale(1.33333,1.33333) translate(-387.75,-150.375)" width="24" x="1551" y="601.4999952316284"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="46">
   <g id="460">
    <use class="kv35" height="30" transform="rotate(0,1084.25,581.935) scale(2.62903,2.62903) translate(-646.586,-336.15)" width="31" x="1043.5" xlink:href="#PowerTransformer2:20210813_0" y="542.5" zvalue="347"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874427400196" ObjectName="35"/>
    </metadata>
   </g>
   <g id="461">
    <use class="v400" height="30" transform="rotate(0,1084.25,581.935) scale(2.62903,2.62903) translate(-646.586,-336.15)" width="31" x="1043.5" xlink:href="#PowerTransformer2:20210813_1" y="542.5" zvalue="347"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874427465732" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399445970948" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399445970948"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1084.25,581.935) scale(2.62903,2.62903) translate(-646.586,-336.15)" width="31" x="1043.5" y="542.5"/></g>
 </g>
</svg>