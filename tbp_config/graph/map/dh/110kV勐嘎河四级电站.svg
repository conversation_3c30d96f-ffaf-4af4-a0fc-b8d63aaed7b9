<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549685059585" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:消谐装置_0" viewBox="0,0,20,29">
   <use terminal-index="0" type="0" x="10.15872800538976" xlink:href="#terminal" y="25.96480127873437"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.12399698478855" x2="10.12399698478855" y1="26.28498263464221" y2="23.3231983778117"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.938434782209253" x2="14.28859387834882" y1="21.48754948791521" y2="14.19403516164853"/>
   <rect fill-opacity="0" height="5.22" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,10.11,17.58) scale(1,1) translate(0,0)" width="11.46" x="4.38" y="14.97"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.945791858126634" x2="5.945791858126634" y1="23.57968743439848" y2="21.49582619832228"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.28123680243143" x2="14.28123680243143" y1="14.18575845124145" y2="12.10189721516526"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.15025549608711" x2="10.15025549608711" y1="23.41666666666667" y2="4.867706583258689"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.135856301213458" x2="8.135856301213458" y1="4.455199311740065" y2="4.455199311740065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.659702116379664" x2="8.659702116379664" y1="29.26065666272731" y2="29.26065666272731"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.35591008128069" x2="7.891898490280618" y1="4.703700522631296" y2="4.703700522631296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.57292939360264" x2="8.848534280964973" y1="3.398988163859496" y2="3.398988163859496"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.05089117767898" x2="9.544227599895001" y1="1.833333333333339" y2="1.833333333333339"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.986779448160899" x2="8.986779448160899" y1="29.68994241064946" y2="29.68994241064946"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Accessory:RT1122_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="22.91666666666667" y2="26"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="18.16666666666666" y2="20.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="18.16666666666666" y2="20.16666666666666"/>
   <path d="M 13 11 L 17 11 L 15 8 L 13 11 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="15.41666666666666" y2="18.16666666666666"/>
   <ellipse cx="14.95" cy="18.02" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.95" cy="9.92" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变11_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="9.25" y2="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="26.83333333333333" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0928507596068" x2="10.0928507596068" y1="10.97013412501683" y2="12.64189293057399"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.77292225201073" x2="10.0928507596068" y1="14.31365173613114" y2="12.64189293057398"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="Accessory:4绕组母线PT_0" viewBox="0,0,25,20">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="0"/>
   <ellipse cx="12.75" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.75" cy="9.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.67" cy="14.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.82" cy="9.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="5.583333333333334" y1="7.38888888888889" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.583333333333336" x2="5.583333333333336" y1="12" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="5.5" y1="7.38888888888889" y2="9.388888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.75" x2="12.75" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="12.75" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.75" x2="12.75" y1="7.500000000000002" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.58333333333333" x2="12.58333333333333" y1="12.63888888888889" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.58333333333333" x2="12.58333333333333" y1="12.63888888888889" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="17.25" y2="14.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="19.31481481481482" y1="10.21612466124661" y2="7.749999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.94444444444444" x2="20.62962962962963" y1="10.21612466124661" y2="7.749999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="21.94444444444444" y1="10.21612466124661" y2="10.21612466124661"/>
  </symbol>
  <symbol id="Accessory:腊撒线路PT_0" viewBox="0,0,12,32">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.6666666666666661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="3" y1="27" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="27" y2="30"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="13.66666666666667" y2="1"/>
   <path d="M 6 15 L 3 20 L 9 20 L 6 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="9" y1="27" y2="25"/>
   <rect fill-opacity="0" height="6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,8) scale(1,1) translate(0,0)" width="4" x="4" y="5"/>
   <ellipse cx="5.9" cy="18.6" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.9" cy="26.28" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id=":110kV线路带PT避雷器_0" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="22.5" xlink:href="#terminal" y="44.60000000000001"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="33" y1="29.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="16" y1="40.5" y2="40.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="27.41094883543335" x2="32.88823024054981" y1="18.42541949757221" y2="18.42541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.66022336769755" x2="22.66022336769755" y1="44.33333333333334" y2="0.2499999999999964"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="13.5" y1="37.5" y2="40.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36" x2="36" y1="31.25" y2="36.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36" x2="39" y1="31.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="15" y1="41.5" y2="41.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="14" y1="42.5" y2="42.5"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.54,30.42) scale(1,1) translate(0,0)" width="6.08" x="10.5" y="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36" x2="39" y1="36.25" y2="34.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.96058610156545" x2="22.65436235204273" y1="18.42541949757221" y2="18.42541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="12.5" y1="31.25" y2="25.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="14.5" y1="31.25" y2="25.25"/>
   <path d="M 22.5 18.5 L 13.5 18.5 L 13.5 30.5 L 13.5 30.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="30" y1="26.25" y2="29.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.00545055364641" x2="30.00545055364641" y1="18.54138864447597" y2="24.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="27" y1="29.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.96383161512026" x2="39.91666666666666" y1="18.49758812251703" y2="18.49758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.85940244368076" x2="32.85940244368076" y1="18.49758812251703" y2="18.49758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="39.86598892707138" x2="39.86598892707138" y1="15.25" y2="21.74517624503405"/>
   <ellipse cx="30.01" cy="29.32" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="37.05" cy="33.58" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="30.26" cy="37.58" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="32.85940244368077" x2="32.85940244368077" y1="15.98325293741726" y2="21.08409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="41.01910080183274" x2="41.01910080183274" y1="16.51295093653441" y2="20.84306843322376"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="41.88393470790376" x2="41.88393470790376" y1="17.41505874834466" y2="19.39969593432727"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="25.07589728904158" x2="25.07589728904158" y1="18.49758812251703" y2="18.49758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="24.99256395570825" x2="24.99256395570825" y1="15.83333333333333" y2="20.93417232847779"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="35.02148720885832" x2="35.02148720885832" y1="15.98325293741726" y2="21.08409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="27.23798205421914" x2="27.23798205421914" y1="15.98325293741726" y2="21.08409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.08333333333334" x2="33.08333333333334" y1="38" y2="40"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.08333333333334" x2="27.08333333333334" y1="38" y2="40"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.08333333333334" x2="30.08333333333334" y1="35" y2="38"/>
  </symbol>
  <symbol id="Accessory:大地附属设备_0" viewBox="0,0,8,25">
   <use terminal-index="0" type="0" x="4.05" xlink:href="#terminal" y="0.5999999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.083333333333335" x2="6.083333333333335" y1="16.1" y2="16.1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.083333333333335" x2="6.083333333333335" y1="14.35" y2="14.35"/>
   <rect fill-opacity="0" height="8.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,4.03,4.59) scale(1,1) translate(0,0)" width="6.08" x="0.98" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.833333333333333" x2="7.166666666666667" y1="21.59999999999999" y2="21.59999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.851981351981355" x2="6.192307692307693" y1="23.03094803979166" y2="23.03094803979166"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.924443536244111" x2="5.031267419466841" y1="24.50147998261393" y2="24.50147998261393"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.083333333333335" x2="4.083333333333335" y1="14.35" y2="8.600000000000003"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.083333333333335" x2="4.083333333333335" y1="21.6" y2="16.18333333333333"/>
  </symbol>
  <symbol id="ACLineSegment:线路带壁雷器_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="14.875" xlink:href="#terminal" y="39.83880854456296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.25" x2="6.25" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="5.75" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.25" x2="7.25" y1="32.25" y2="32.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="8.25" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="6.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="4.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.91022336769755" x2="14.91022336769755" y1="39.75" y2="0.8333333333333321"/>
   <path d="M 14.75 9.25 L 5.75 9.25 L 5.75 21.25 L 5.75 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.79,21.17) scale(1,1) translate(0,0)" width="6.08" x="2.75" y="14"/>
  </symbol>
  <symbol id="Accessory:PT6_0" viewBox="0,0,15,20">
   <use terminal-index="0" type="0" x="7.560000000000002" xlink:href="#terminal" y="0.42361930658363"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.160000000000005" x2="8.559999999999999" y1="15.48035578286757" y2="14.24281579851547"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.060000000000002" x2="8.459999999999996" y1="16.09912577504353" y2="17.33666575939564"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.459999999999999" x2="5.060000000000007" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000005" x2="9.859999999999998" y1="4.75500925181603" y2="5.992549236168133"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.859999999999999" x2="8.859999999999999" y1="14.65532912663279" y2="17.13040909533699"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.460000000000003" x2="7.460000000000003" y1="2.279929283111803" y2="4.755009251816007"/>
   <ellipse cx="7.5" cy="6.51" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.7" cy="13.44" fill-opacity="0" rx="6" ry="6.06" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.08333333333333" x2="9.833333333333332" y1="16.5" y2="18.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14" y1="9.75" y2="16.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="11" y1="11.58333333333333" y2="9.666666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="9.75" y1="11.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8" x2="15.08333333333333" y1="8.416666666666668" y2="25.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_1" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.96,13) scale(1,1) translate(0,0)" width="4.42" x="12.75" y="9"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="5.416666666666668" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_2" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="20" y1="6" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="10.08333333333333" y1="6.000000000000004" y2="25.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV勐嘎河四级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="43.75" xlink:href="logo.png" y="49.25"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,178.375,79.25) scale(1,1) translate(0,0)" writing-mode="lr" x="178.37" xml:space="preserve" y="82.75" zvalue="41"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,180.083,78.9403) scale(1,1) translate(6.85563e-15,0)" writing-mode="lr" x="180.08" xml:space="preserve" y="87.94" zvalue="42">110kV勐嘎河四级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="130" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="519"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="519">信号一览</text>
  <line fill="none" id="74" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.75" x2="377.75" y1="17.25000000000006" y2="1047.25" zvalue="43"/>
  <line fill="none" id="72" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.750000000000227" x2="370.7499999999998" y1="153.1204926140824" y2="153.1204926140824" zvalue="45"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,593.222,298.222) scale(1,1) translate(0,0)" writing-mode="lr" x="593.22" xml:space="preserve" y="302.72" zvalue="81">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="125" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,497.459,690.889) scale(1,1) translate(0,0)" writing-mode="lr" x="497.46" xml:space="preserve" y="695.39" zvalue="83">10kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,703.175,256) scale(1,1) translate(0,0)" writing-mode="lr" x="703.17" xml:space="preserve" y="260.5" zvalue="87">1411</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,666.551,100.528) scale(1,1) translate(0,0)" writing-mode="lr" x="666.55" xml:space="preserve" y="105.03" zvalue="95">110kV勐嘎四级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,725.056,217.111) scale(1,1) translate(0,0)" writing-mode="lr" x="725.0599999999999" xml:space="preserve" y="221.61" zvalue="97">7</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1341.56,256) scale(1,1) translate(0,0)" writing-mode="lr" x="1341.56" xml:space="preserve" y="260.5" zvalue="103">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1366.33,243.278) scale(1,1) translate(0,0)" writing-mode="lr" x="1366.33" xml:space="preserve" y="247.78" zvalue="105">7</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,681.869,352.556) scale(1,1) translate(0,0)" writing-mode="lr" x="681.87" xml:space="preserve" y="357.06" zvalue="109">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,690.358,408.556) scale(1,1) translate(0,0)" writing-mode="lr" x="690.36" xml:space="preserve" y="413.06" zvalue="111">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,714.535,386.556) scale(1,1) translate(0,0)" writing-mode="lr" x="714.54" xml:space="preserve" y="391.06" zvalue="114">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1317.7,133.083) scale(1,1) translate(0,0)" writing-mode="lr" x="1317.698017697246" xml:space="preserve" y="137.5833333333333" zvalue="118">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1366.33,300.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1366.33" xml:space="preserve" y="304.94" zvalue="120">0</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,607.524,487.197) scale(1,1) translate(4.43969e-13,0)" writing-mode="lr" x="607.5236116961414" xml:space="preserve" y="491.6969696969697" zvalue="122">1号主变25MVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" x="623.234375" xml:space="preserve" y="953.96875" zvalue="124">#1发电机      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="623.234375" xml:space="preserve" y="969.96875" zvalue="124">20MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,776.636,516.515) scale(1,1) translate(0,0)" writing-mode="lr" x="776.64" xml:space="preserve" y="521.02" zvalue="134">1010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,784.041,935.5) scale(1,1) translate(0,0)" writing-mode="lr" x="784.04" xml:space="preserve" y="940" zvalue="136">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1732.78,654) scale(1,1) translate(0,0)" writing-mode="lr" x="1732.78" xml:space="preserve" y="658.5" zvalue="138">10kVⅡ母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1327.47,353.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1327.47" xml:space="preserve" y="358.06" zvalue="141">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1336.34,409.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1336.34" xml:space="preserve" y="414.06" zvalue="142">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="103" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1361.56,386.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1361.56" xml:space="preserve" y="391.06" zvalue="145">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1411.02,453.833) scale(1,1) translate(1.06841e-12,0)" writing-mode="lr" x="1411.024440768448" xml:space="preserve" y="458.3333333333333" zvalue="147">2号主变25MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1372.09,513.131) scale(1,1) translate(0,0)" writing-mode="lr" x="1372.09" xml:space="preserve" y="517.63" zvalue="164">1020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,699.341,604.5) scale(1,1) translate(0,0)" writing-mode="lr" x="699.34" xml:space="preserve" y="609" zvalue="171">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,785.148,616.06) scale(1,1) translate(1.70752e-13,0)" writing-mode="lr" x="785.15" xml:space="preserve" y="620.5599999999999" zvalue="179">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,856.064,627) scale(1,1) translate(0,0)" writing-mode="lr" x="856.0599999999999" xml:space="preserve" y="631.5" zvalue="182">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,603.285,704.319) scale(1,1) translate(0,0)" writing-mode="lr" x="603.29" xml:space="preserve" y="708.8200000000001" zvalue="189">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="206" stroke="rgb(255,255,255)" text-anchor="middle" x="519.890625" xml:space="preserve" y="811.46875" zvalue="198">高频切机第</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="206" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="519.890625" xml:space="preserve" y="827.46875" zvalue="198">一轮</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,533.043,878.895) scale(1,1) translate(0,0)" writing-mode="lr" x="533.04" xml:space="preserve" y="883.4" zvalue="202">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,581.52,882.798) scale(1,1) translate(0,0)" writing-mode="lr" x="581.52" xml:space="preserve" y="887.3" zvalue="206">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,688.855,782.222) scale(1,1) translate(0,0)" writing-mode="lr" x="688.85" xml:space="preserve" y="786.72" zvalue="209">1LB</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,661.619,881.319) scale(1,1) translate(0,0)" writing-mode="lr" x="661.62" xml:space="preserve" y="885.8200000000001" zvalue="212">0912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,718.187,897.465) scale(1,1) translate(4.65086e-13,0)" writing-mode="lr" x="718.1900000000001" xml:space="preserve" y="901.96" zvalue="216">09127</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,747.952,730.319) scale(1,1) translate(0,0)" writing-mode="lr" x="747.95" xml:space="preserve" y="734.8200000000001" zvalue="219">0431</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,803.854,730.354) scale(1,1) translate(0,0)" writing-mode="lr" x="803.85" xml:space="preserve" y="734.85" zvalue="222">04317</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,751.315,787.139) scale(1,1) translate(0,0)" writing-mode="lr" x="751.3099999999999" xml:space="preserve" y="791.64" zvalue="225">043</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" x="1406" xml:space="preserve" y="956.078125" zvalue="232">#2发电机    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1406" xml:space="preserve" y="972.078125" zvalue="232">20MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1129.93,905.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1129.93" xml:space="preserve" y="909.83" zvalue="234">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1380.29,708.319) scale(1,1) translate(0,0)" writing-mode="lr" x="1380.29" xml:space="preserve" y="712.8200000000001" zvalue="236">0422</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1318.95,873.986) scale(1,1) translate(0,0)" writing-mode="lr" x="1318.95" xml:space="preserve" y="878.49" zvalue="249">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1366.52,878.798) scale(1,1) translate(0,0)" writing-mode="lr" x="1366.52" xml:space="preserve" y="883.3" zvalue="252">09217</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1477.92,783.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1477.92" xml:space="preserve" y="787.97" zvalue="256">2LB</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1450.68,871.569) scale(1,1) translate(0,0)" writing-mode="lr" x="1450.68" xml:space="preserve" y="876.0700000000001" zvalue="258">0922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1517.56,881.277) scale(1,1) translate(0,0)" writing-mode="lr" x="1517.56" xml:space="preserve" y="885.78" zvalue="260">09227</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1100.95,710.319) scale(1,1) translate(0,0)" writing-mode="lr" x="1100.95" xml:space="preserve" y="714.8200000000001" zvalue="263">0442</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1171.85,713.354) scale(1,1) translate(0,0)" writing-mode="lr" x="1171.85" xml:space="preserve" y="717.85" zvalue="266">04427</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1107.2,766.028) scale(1,1) translate(0,0)" writing-mode="lr" x="1107.2" xml:space="preserve" y="770.53" zvalue="269">044</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1277.34,604.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1277.34" xml:space="preserve" y="609" zvalue="315">0022</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1162.8,620.06) scale(1,1) translate(0,0)" writing-mode="lr" x="1162.8" xml:space="preserve" y="624.5599999999999" zvalue="324">0902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1230.06,623) scale(1,1) translate(0,0)" writing-mode="lr" x="1230.06" xml:space="preserve" y="627.5" zvalue="327">09027</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="276" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1657.12,540.667) scale(1,1) translate(0,0)" writing-mode="lr" x="1657.12" xml:space="preserve" y="545.17" zvalue="419">045</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="277" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1657.71,598.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1657.71" xml:space="preserve" y="602.72" zvalue="423">0452</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="278" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1686.56,626) scale(1,1) translate(0,0)" writing-mode="lr" x="1686.56" xml:space="preserve" y="630.5" zvalue="425">04527</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,597.333,763.444) scale(1,1) translate(0,0)" writing-mode="lr" x="597.33" xml:space="preserve" y="767.9400000000001" zvalue="431">041</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="290" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1380.33,763.889) scale(1,1) translate(0,0)" writing-mode="lr" x="1380.33" xml:space="preserve" y="768.39" zvalue="435">042</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="148" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="499"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="501">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="144" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="502">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="503">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="142" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="504">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="141" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="505">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="507">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="509">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="510">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="511">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="512">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="133" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.9375,358) scale(1,1) translate(0,0)" writing-mode="lr" x="60.94" xml:space="preserve" y="362.5" zvalue="513">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="520">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="521">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="524">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="526">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="101" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="528">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="530">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="531">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="533">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,242.188,333) scale(1,1) translate(0,0)" writing-mode="lr" x="242.19" xml:space="preserve" y="337.5" zvalue="536">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,230.944,952) scale(1,1) translate(-5.4623e-14,0)" writing-mode="lr" x="230.94" xml:space="preserve" y="958" zvalue="549">MengGaHe4-01-2022</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1630.85,234.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1630.85" xml:space="preserve" y="239" zvalue="565">10kV近区线路勐四五联络线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1697,424.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1697" xml:space="preserve" y="429" zvalue="570">10kV近区变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,147.222,1014.56) scale(1,1) translate(0,0)" writing-mode="lr" x="147.22" xml:space="preserve" y="1020.56" zvalue="579">唐涛</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,327.222,1014.56) scale(1,1) translate(3.78956e-13,0)" writing-mode="lr" x="327.22" xml:space="preserve" y="1020.56" zvalue="581">20220906</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="519"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="362">
   <path class="kv110" d="M 553 316 L 1467 316" stroke-width="4" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674423078915" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674423078915"/></metadata>
  <path d="M 553 316 L 1467 316" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="360">
   <path class="kv10" d="M 496.89 669.22 L 921.64 669.22" stroke-width="4" zvalue="82"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674423013379" ObjectName="10kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674423013379"/></metadata>
  <path d="M 496.89 669.22 L 921.64 669.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="kv10" d="M 1092.23 669.11 L 1760 669.11" stroke-width="4" zvalue="137"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674422947843" ObjectName="10kVⅡ母"/>
   <cge:TPSR_Ref TObjectID="9288674422947843"/></metadata>
  <path d="M 1092.23 669.11 L 1760 669.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="358">
   <use class="kv110" height="30" transform="rotate(0,670.619,253) scale(-1.11111,-0.814815) translate(-1273.34,-566.278)" width="15" x="662.2859793764594" xlink:href="#Disconnector:刀闸_0" y="240.7777913411458" zvalue="86"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454836682754" ObjectName="110kV勐嘎四级线1411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454836682754"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,670.619,253) scale(-1.11111,-0.814815) translate(-1273.34,-566.278)" width="15" x="662.2859793764594" y="240.7777913411458"/></g>
  <g id="336">
   <use class="kv110" height="30" transform="rotate(0,1316.22,257) scale(-1.11111,-0.814815) translate(-2499.99,-575.187)" width="15" x="1307.888888888889" xlink:href="#Disconnector:刀闸_0" y="244.7777914471096" zvalue="102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454836420610" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454836420610"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1316.22,257) scale(-1.11111,-0.814815) translate(-2499.99,-575.187)" width="15" x="1307.888888888889" y="244.7777914471096"/></g>
  <g id="330">
   <use class="kv110" height="30" transform="rotate(0,670.424,353.556) scale(1.11111,0.814815) translate(-66.2091,77.5758)" width="15" x="662.0909192634354" xlink:href="#Disconnector:刀闸_0" y="341.3333333333333" zvalue="108"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454836224002" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454836224002"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,670.424,353.556) scale(1.11111,0.814815) translate(-66.2091,77.5758)" width="15" x="662.0909192634354" y="341.3333333333333"/></g>
  <g id="230">
   <use class="kv110" height="30" transform="rotate(0,1316.03,354.556) scale(1.11111,0.814815) translate(-130.769,77.803)" width="15" x="1307.693828775865" xlink:href="#Disconnector:刀闸_0" y="342.3333333333333" zvalue="139"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454835634178" ObjectName="#2主变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454835634178"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1316.03,354.556) scale(1.11111,0.814815) translate(-130.769,77.803)" width="15" x="1307.693828775865" y="342.3333333333333"/></g>
  <g id="378">
   <use class="kv10" height="30" transform="rotate(0,669.591,605.5) scale(1.11111,0.814815) translate(-66.1258,134.836)" width="15" x="661.2575757575758" xlink:href="#Disconnector:刀闸_0" y="593.2777777777778" zvalue="169"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454835306498" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454835306498"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,669.591,605.5) scale(1.11111,0.814815) translate(-66.1258,134.836)" width="15" x="661.2575757575758" y="593.2777777777778"/></g>
  <g id="396">
   <use class="kv10" height="30" transform="rotate(0,827.045,615.867) scale(1.11111,0.814815) translate(-81.8712,137.192)" width="15" x="818.7121212121211" xlink:href="#Disconnector:刀闸_0" y="603.6445612117578" zvalue="178"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454835175426" ObjectName="10kVⅠ母电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454835175426"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,827.045,615.867) scale(1.11111,0.814815) translate(-81.8712,137.192)" width="15" x="818.7121212121211" y="603.6445612117578"/></g>
  <g id="406">
   <use class="kv10" height="30" transform="rotate(0,620.633,705.319) scale(1.11111,0.814815) translate(-61.2299,157.522)" width="15" x="612.2992424242425" xlink:href="#Disconnector:刀闸_0" y="693.0972222222222" zvalue="188"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454834847746" ObjectName="#1发电机0411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454834847746"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,620.633,705.319) scale(1.11111,0.814815) translate(-61.2299,157.522)" width="15" x="612.2992424242425" y="693.0972222222222"/></g>
  <g id="429">
   <use class="kv10" height="30" transform="rotate(0,558.612,879.895) scale(1.11111,0.814815) translate(-55.0279,197.198)" width="15" x="550.2790404040406" xlink:href="#Disconnector:刀闸_0" y="867.6729797979797" zvalue="201"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454834716674" ObjectName="#1发电机0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454834716674"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,558.612,879.895) scale(1.11111,0.814815) translate(-55.0279,197.198)" width="15" x="550.2790404040406" y="867.6729797979797"/></g>
  <g id="440">
   <use class="kv10" height="30" transform="rotate(0,687.521,882.319) scale(1.11111,0.814815) translate(-67.9188,197.749)" width="15" x="679.1881313131315" xlink:href="#Disconnector:刀闸_0" y="870.0972222222221" zvalue="210"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454834388994" ObjectName="#1发电机0912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454834388994"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,687.521,882.319) scale(1.11111,0.814815) translate(-67.9188,197.749)" width="15" x="679.1881313131315" y="870.0972222222221"/></g>
  <g id="463">
   <use class="kv10" height="30" transform="rotate(0,773.299,731.319) scale(1.11111,0.814815) translate(-76.4966,163.431)" width="15" x="764.9659090909091" xlink:href="#Disconnector:刀闸_0" y="719.0972222222222" zvalue="218"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454834192386" ObjectName="#1站用变0431隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454834192386"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,773.299,731.319) scale(1.11111,0.814815) translate(-76.4966,163.431)" width="15" x="764.9659090909091" y="719.0972222222222"/></g>
  <g id="515">
   <use class="kv10" height="30" transform="rotate(0,1405.63,709.319) scale(1.11111,0.814815) translate(-139.73,158.431)" width="15" x="1397.299242424242" xlink:href="#Disconnector:刀闸_0" y="697.0972222222222" zvalue="235"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454833799170" ObjectName="#2发电机0422隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454833799170"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1405.63,709.319) scale(1.11111,0.814815) translate(-139.73,158.431)" width="15" x="1397.299242424242" y="697.0972222222222"/></g>
  <g id="503">
   <use class="kv10" height="30" transform="rotate(0,1344.52,874.986) scale(1.11111,0.814815) translate(-133.619,196.083)" width="15" x="1336.188131313132" xlink:href="#Disconnector:刀闸_0" y="862.7638888888889" zvalue="248"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454833602562" ObjectName="#2发电机0921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454833602562"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1344.52,874.986) scale(1.11111,0.814815) translate(-133.619,196.083)" width="15" x="1336.188131313132" y="862.7638888888889"/></g>
  <g id="496">
   <use class="kv10" height="30" transform="rotate(0,1476.58,872.569) scale(1.11111,0.814815) translate(-146.825,195.533)" width="15" x="1468.25062821369" xlink:href="#Disconnector:刀闸_0" y="860.3472222222221" zvalue="257"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454833274882" ObjectName="#2发电机0922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454833274882"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1476.58,872.569) scale(1.11111,0.814815) translate(-146.825,195.533)" width="15" x="1468.25062821369" y="860.3472222222221"/></g>
  <g id="489">
   <use class="kv10" height="30" transform="rotate(0,1130.3,711.319) scale(1.11111,0.814815) translate(-112.197,158.886)" width="15" x="1121.965909090909" xlink:href="#Disconnector:刀闸_0" y="699.0972222222223" zvalue="262"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454833078274" ObjectName="#2站用变0442隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454833078274"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1130.3,711.319) scale(1.11111,0.814815) translate(-112.197,158.886)" width="15" x="1121.965909090909" y="699.0972222222223"/></g>
  <g id="606">
   <use class="kv10" height="30" transform="rotate(0,1315.59,605.5) scale(1.11111,0.814815) translate(-130.726,134.836)" width="15" x="1307.257575757576" xlink:href="#Disconnector:刀闸_0" y="593.2777777777778" zvalue="314"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454832816130" ObjectName="#2主变10kV侧0022隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454832816130"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1315.59,605.5) scale(1.11111,0.814815) translate(-130.726,134.836)" width="15" x="1307.257575757576" y="593.2777777777778"/></g>
  <g id="600">
   <use class="kv10" height="30" transform="rotate(0,1201.05,619.867) scale(1.11111,0.814815) translate(-119.271,138.101)" width="15" x="1192.712121212121" xlink:href="#Disconnector:刀闸_0" y="607.6445612117578" zvalue="323"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454832685058" ObjectName="10kVⅡ母电压互感器0902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454832685058"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1201.05,619.867) scale(1.11111,0.814815) translate(-119.271,138.101)" width="15" x="1192.712121212121" y="607.6445612117578"/></g>
  <g id="273">
   <use class="kv10" height="30" transform="rotate(0,1630.71,599.222) scale(-1.11111,-0.814815) translate(-3097.51,-1337.41)" width="15" x="1622.371698247228" xlink:href="#Disconnector:刀闸_0" y="587.0000136693318" zvalue="422"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454836944898" ObjectName="10kV近区变0452隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454836944898"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1630.71,599.222) scale(-1.11111,-0.814815) translate(-3097.51,-1337.41)" width="15" x="1622.371698247228" y="587.0000136693318"/></g>
  <g id="68">
   <use class="kv10" height="30" transform="rotate(0,1630.73,335.5) scale(1.3,1.3) translate(-371.822,-72.9231)" width="30" x="1611.227811850074" xlink:href="#Disconnector:跌落刀闸_0" y="316" zvalue="572"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450174648325" ObjectName="10kV近区线跌落保险"/>
   <cge:TPSR_Ref TObjectID="6192450174648325"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1630.73,335.5) scale(1.3,1.3) translate(-371.822,-72.9231)" width="30" x="1611.227811850074" y="316"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="250">
   <path class="kv110" d="M 670.52 264.82 L 670.52 316" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="358@0" LinkObjectIDznd="362@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 670.52 264.82 L 670.52 316" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="246">
   <path class="kv110" d="M 1316.12 268.82 L 1316.12 316" stroke-width="1" zvalue="106"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="336@0" LinkObjectIDznd="362@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1316.12 268.82 L 1316.12 316" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="245">
   <path class="kv110" d="M 1316.15 244.99 L 1316.15 206.33" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="336@1" LinkObjectIDznd="268@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1316.15 244.99 L 1316.15 206.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="244">
   <path class="kv110" d="M 670.49 365.57 L 670.49 398.93" stroke-width="1" zvalue="112"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="330@1" LinkObjectIDznd="329@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 670.49 365.57 L 670.49 398.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="243">
   <path class="kv110" d="M 670.52 341.74 L 670.52 316" stroke-width="1" zvalue="115"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="330@0" LinkObjectIDznd="362@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 670.52 341.74 L 670.52 316" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="238">
   <path class="kv110" d="M 1355.5 227.11 L 1316.15 227.11" stroke-width="1" zvalue="128"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="335@0" LinkObjectIDznd="245" MaxPinNum="2"/>
   </metadata>
  <path d="M 1355.5 227.11 L 1316.15 227.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="237">
   <path class="kv110" d="M 1355.5 286.94 L 1316.12 286.94" stroke-width="1" zvalue="129"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="266@0" LinkObjectIDznd="246" MaxPinNum="2"/>
   </metadata>
  <path d="M 1355.5 286.94 L 1316.12 286.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="236">
   <path class="kv110" d="M 669.66 420.17 L 669.66 459.14" stroke-width="1" zvalue="130"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="329@1" LinkObjectIDznd="261@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 669.66 420.17 L 669.66 459.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="235">
   <path class="kv110" d="M 703.7 374.39 L 670.49 374.39" stroke-width="1" zvalue="131"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="326@0" LinkObjectIDznd="244" MaxPinNum="2"/>
   </metadata>
  <path d="M 703.7 374.39 L 670.49 374.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="kv110" d="M 669.64 476.76 L 737.26 476.76 L 737.26 507.84" stroke-width="1" zvalue="133"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@2" LinkObjectIDznd="234@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 669.64 476.76 L 737.26 476.76 L 737.26 507.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="228">
   <path class="kv110" d="M 1316.1 366.57 L 1316.1 399.93" stroke-width="1" zvalue="143"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@1" LinkObjectIDznd="229@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1316.1 366.57 L 1316.1 399.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="225">
   <path class="kv110" d="M 1315.64 421.17 L 1315.64 451.2" stroke-width="1" zvalue="148"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="229@1" LinkObjectIDznd="226@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1315.64 421.17 L 1315.64 451.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="kv110" d="M 1350.73 374.39 L 1316.1 374.39" stroke-width="1" zvalue="149"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="227@0" LinkObjectIDznd="228" MaxPinNum="2"/>
   </metadata>
  <path d="M 1350.73 374.39 L 1316.1 374.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="218">
   <path class="kv10" d="M 669.61 520.26 L 669.69 593.68" stroke-width="1" zvalue="170"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@1" LinkObjectIDznd="378@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 669.61 520.26 L 669.69 593.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv10" d="M 690.76 559.95 L 669.65 559.95" stroke-width="1" zvalue="177"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@0" LinkObjectIDznd="218" MaxPinNum="2"/>
   </metadata>
  <path d="M 690.76 559.95 L 669.65 559.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="kv10" d="M 847.21 604.05 L 827.14 604.05" stroke-width="1" zvalue="185"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="392@0" LinkObjectIDznd="396@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 847.21 604.05 L 827.14 604.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="kv10" d="M 827.05 604.05 L 827.05 560.63" stroke-width="1" zvalue="186"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212" LinkObjectIDznd="399@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 827.05 604.05 L 827.05 560.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="205">
   <path class="kv10" d="M 558.68 891.91 L 558.68 947.67" stroke-width="1" zvalue="203"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="429@1" LinkObjectIDznd="434@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 558.68 891.91 L 558.68 947.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="kv10" d="M 572.02 899.08 L 558.68 899.08" stroke-width="1" zvalue="205"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="433@0" LinkObjectIDznd="205" MaxPinNum="2"/>
   </metadata>
  <path d="M 572.02 899.08 L 558.68 899.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="kv10" d="M 793.02 755.3 L 774.41 755.3" stroke-width="1" zvalue="223"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="461@0" LinkObjectIDznd="198" MaxPinNum="2"/>
   </metadata>
  <path d="M 793.02 755.3 L 774.41 755.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="kv10" d="M 774.41 773.56 L 774.41 743.33" stroke-width="1" zvalue="226"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="459@0" LinkObjectIDznd="463@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 774.41 773.56 L 774.41 743.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv10" d="M 774.54 796.03 L 774.54 855.12" stroke-width="1" zvalue="228"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="459@1" LinkObjectIDznd="232@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 774.54 796.03 L 774.54 855.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv110" d="M 1315.62 470.61 L 1396.44 470.61 L 1396.44 501.46" stroke-width="1" zvalue="229"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@2" LinkObjectIDznd="364@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1315.62 470.61 L 1396.44 470.61 L 1396.44 501.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv10" d="M 1344.59 887 L 1344.59 935.67" stroke-width="1" zvalue="250"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="503@1" LinkObjectIDznd="498@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1344.59 887 L 1344.59 935.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv10" d="M 1357.02 895.08 L 1344.59 895.08" stroke-width="1" zvalue="253"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="500@0" LinkObjectIDznd="189" MaxPinNum="2"/>
   </metadata>
  <path d="M 1357.02 895.08 L 1344.59 895.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv10" d="M 1130.4 699.5 L 1130.4 669.11" stroke-width="1" zvalue="264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="489@0" LinkObjectIDznd="231@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1130.4 699.5 L 1130.4 669.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv10" d="M 1150.02 735.3 L 1130.34 735.3" stroke-width="1" zvalue="267"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="487@0" LinkObjectIDznd="185" MaxPinNum="2"/>
   </metadata>
  <path d="M 1150.02 735.3 L 1130.34 735.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv10" d="M 1130.3 752.45 L 1130.37 723.33" stroke-width="1" zvalue="270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="485@0" LinkObjectIDznd="489@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1130.3 752.45 L 1130.37 723.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="184">
   <path class="kv10" d="M 1130.43 774.92 L 1130.43 835.12" stroke-width="1" zvalue="272"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="485@1" LinkObjectIDznd="516@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1130.43 774.92 L 1130.43 835.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="kv10" d="M 1315.59 518.54 L 1315.69 593.68" stroke-width="1" zvalue="316"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="226@1" LinkObjectIDznd="606@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1315.59 518.54 L 1315.69 593.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv10" d="M 1315.66 617.51 L 1315.66 669.11" stroke-width="1" zvalue="317"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="606@1" LinkObjectIDznd="231@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1315.66 617.51 L 1315.66 669.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="kv10" d="M 1335.76 552.95 L 1315.64 552.95" stroke-width="1" zvalue="322"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="173" MaxPinNum="2"/>
   </metadata>
  <path d="M 1335.76 552.95 L 1315.64 552.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="kv10" d="M 1201.11 631.88 L 1201.11 669.11" stroke-width="1" zvalue="325"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="600@1" LinkObjectIDznd="231@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1201.11 631.88 L 1201.11 669.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="kv10" d="M 1220.14 643.7 L 1201.11 643.7" stroke-width="1" zvalue="328"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="599@0" LinkObjectIDznd="169" MaxPinNum="2"/>
   </metadata>
  <path d="M 1220.14 643.7 L 1201.11 643.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="167">
   <path class="kv10" d="M 1221.21 600.58 L 1211.18 600.58 L 1211.18 608.05 L 1201.14 608.05" stroke-width="1" zvalue="330"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="597@0" LinkObjectIDznd="600@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1221.21 600.58 L 1211.18 600.58 L 1211.18 608.05 L 1201.14 608.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv10" d="M 1201.05 608.05 L 1201.05 564.63" stroke-width="1" zvalue="331"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="167" LinkObjectIDznd="594@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1201.05 608.05 L 1201.05 564.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv10" d="M 558.71 829.1 L 558.71 868.08" stroke-width="1" zvalue="375"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="429@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 558.71 829.1 L 558.71 868.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv10" d="M 798.68 813.01 L 774.54 813.01" stroke-width="1" zvalue="379"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="456@0" LinkObjectIDznd="197" MaxPinNum="2"/>
   </metadata>
  <path d="M 798.68 813.01 L 774.54 813.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv10" d="M 1159.07 790.51 L 1130.43 790.51" stroke-width="1" zvalue="395"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="482@0" LinkObjectIDznd="184" MaxPinNum="2"/>
   </metadata>
  <path d="M 1159.07 790.51 L 1130.43 790.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="252">
   <path class="kv110" d="M 670.55 240.99 L 670.55 159.13" stroke-width="1" zvalue="399"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="358@1" LinkObjectIDznd="353@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 670.55 240.99 L 670.55 159.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="253">
   <path class="kv110" d="M 712.78 202.61 L 670.55 202.61" stroke-width="1" zvalue="400"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="351@0" LinkObjectIDznd="252" MaxPinNum="2"/>
   </metadata>
  <path d="M 712.78 202.61 L 670.55 202.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="262">
   <path class="kv10" d="M 688.19 941.44 L 688.19 894.33" stroke-width="1" zvalue="408"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="257@0" LinkObjectIDznd="440@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 688.19 941.44 L 688.19 894.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="263">
   <path class="kv10" d="M 708.02 918.41 L 688.19 918.41" stroke-width="1" zvalue="409"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="437@0" LinkObjectIDznd="262" MaxPinNum="2"/>
   </metadata>
  <path d="M 708.02 918.41 L 688.19 918.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv10" d="M 1405.73 697.5 L 1405.73 669.11" stroke-width="1" zvalue="414"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="515@0" LinkObjectIDznd="231@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1405.73 697.5 L 1405.73 669.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="280">
   <path class="kv10" d="M 1630.64 552.28 L 1630.64 587.21" stroke-width="1" zvalue="426"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@1" LinkObjectIDznd="273@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1630.64 552.28 L 1630.64 587.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="281">
   <path class="kv10" d="M 1630.61 611.04 L 1630.61 669.11" stroke-width="1" zvalue="427"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="273@0" LinkObjectIDznd="231@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1630.61 611.04 L 1630.61 669.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="282">
   <path class="kv10" d="M 1651.72 640.28 L 1630.61 640.28" stroke-width="1" zvalue="428"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="281" MaxPinNum="2"/>
   </metadata>
  <path d="M 1651.72 640.28 L 1630.61 640.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="283">
   <path class="kv10" d="M 1611.94 494.91 L 1631.25 494.91" stroke-width="1" zvalue="429"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="11" MaxPinNum="2"/>
   </metadata>
  <path d="M 1611.94 494.91 L 1631.25 494.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="294">
   <path class="kv10" d="M 1406.67 746.39 L 1406.67 721.33" stroke-width="1" zvalue="438"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="289@0" LinkObjectIDznd="515@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1406.67 746.39 L 1406.67 721.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="295">
   <path class="kv10" d="M 1406.67 782.89 L 1406.67 894.21" stroke-width="1" zvalue="439"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="289@1" LinkObjectIDznd="517@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1406.67 782.89 L 1406.67 894.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="296">
   <path class="kv10" d="M 1344.62 863.17 L 1344.71 829.1" stroke-width="1" zvalue="440"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="503@0" LinkObjectIDznd="44@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1344.62 863.17 L 1344.71 829.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="298">
   <path class="kv10" d="M 1344.66 847.56 L 1406.67 847.56" stroke-width="1" zvalue="442"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="296" LinkObjectIDznd="295" MaxPinNum="2"/>
   </metadata>
  <path d="M 1344.66 847.56 L 1406.67 847.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="300">
   <path class="kv10" d="M 1426.76 813.87 L 1406.67 813.87" stroke-width="1" zvalue="444"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="506@0" LinkObjectIDznd="295" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426.76 813.87 L 1406.67 813.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="302">
   <path class="kv10" d="M 621.11 782.44 L 621.11 892.1" stroke-width="1" zvalue="446"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="284@1" LinkObjectIDznd="850@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 621.11 782.44 L 621.11 892.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="304">
   <path class="kv10" d="M 773.4 719.5 L 773.4 669.22" stroke-width="1" zvalue="448"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="463@0" LinkObjectIDznd="360@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 773.4 719.5 L 773.4 669.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="305">
   <path class="kv10" d="M 687.52 829.03 L 687.62 870.5" stroke-width="1" zvalue="449"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="441@0" LinkObjectIDznd="440@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 687.52 829.03 L 687.62 870.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="306">
   <path class="kv10" d="M 558.71 848.59 L 621.11 848.59" stroke-width="1" zvalue="450"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="149" LinkObjectIDznd="302" MaxPinNum="2"/>
   </metadata>
  <path d="M 558.71 848.59 L 621.11 848.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="307">
   <path class="kv10" d="M 687.57 849.11 L 618.92 849.11" stroke-width="1" zvalue="451"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="305" LinkObjectIDznd="306" MaxPinNum="2"/>
   </metadata>
  <path d="M 687.57 849.11 L 618.92 849.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="308">
   <path class="kv10" d="M 646.8 830.55 L 621.11 830.55" stroke-width="1" zvalue="452"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="421@0" LinkObjectIDznd="302" MaxPinNum="2"/>
   </metadata>
  <path d="M 646.8 830.55 L 621.11 830.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv10" d="M 827.11 627.88 L 827.11 669.22" stroke-width="1" zvalue="455"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="396@1" LinkObjectIDznd="360@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 827.11 627.88 L 827.11 669.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="312">
   <path class="kv10" d="M 846.14 647.7 L 827.11 647.7" stroke-width="1" zvalue="456"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="394@0" LinkObjectIDznd="311" MaxPinNum="2"/>
   </metadata>
  <path d="M 846.14 647.7 L 827.11 647.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="313">
   <path class="kv10" d="M 669.66 617.51 L 669.66 669.22" stroke-width="1" zvalue="457"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="378@1" LinkObjectIDznd="360@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 669.66 617.51 L 669.66 669.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="314">
   <path class="kv10" d="M 620.73 693.5 L 620.73 669.22" stroke-width="1" zvalue="458"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="406@0" LinkObjectIDznd="360@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 620.73 693.5 L 620.73 669.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="315">
   <path class="kv10" d="M 621.11 745.94 L 621.11 717.33" stroke-width="1" zvalue="459"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="284@0" LinkObjectIDznd="406@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 621.11 745.94 L 621.11 717.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv10" d="M 1476.44 918.49 L 1476.44 884.58" stroke-width="1" zvalue="466"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="264@0" LinkObjectIDznd="496@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1476.44 918.49 L 1476.44 884.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="kv10" d="M 1476.68 860.75 L 1476.75 842 L 1406.67 842" stroke-width="1" zvalue="467"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="496@0" LinkObjectIDznd="295" MaxPinNum="2"/>
   </metadata>
  <path d="M 1476.68 860.75 L 1476.75 842 L 1406.67 842" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="3">
   <path class="kv10" d="M 1476.58 830.28 L 1476.58 843.4" stroke-width="1" zvalue="468"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="497@0" LinkObjectIDznd="2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1476.58 830.28 L 1476.58 843.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="4">
   <path class="kv10" d="M 1507.4 902.22 L 1476.44 902.22" stroke-width="1" zvalue="469"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="493@0" LinkObjectIDznd="1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1507.4 902.22 L 1476.44 902.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="kv110" d="M 1316.12 342.74 L 1316.12 316" stroke-width="1" zvalue="472"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="362@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1316.12 342.74 L 1316.12 316" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv10" d="M 1630.73 287.84 L 1630.73 316" stroke-width="1" zvalue="573"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@0" LinkObjectIDznd="68@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1630.73 287.84 L 1630.73 316" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv10" d="M 1630.73 353.7 L 1630.73 396.31" stroke-width="1" zvalue="576"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@1" LinkObjectIDznd="11" MaxPinNum="2"/>
   </metadata>
  <path d="M 1630.73 353.7 L 1630.73 396.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv10" d="M 1631.56 396.29 L 1625.86 396.62 L 1621.72 399.38 L 1617.93 402.48 L 1615.52 406.28 L 1613.79 409.03 L 1613.1 412.48 L 1613.1 415.93 L 1613.79 419.72 L 1614.83 423.86 L 1616.55 425.24 L 1616.55 426.28 L 1615.17 430.41 L 1613.45 435.24 L 1613.79 440.07 L 1615.52 444.21 L 1618.28 448.69 L 1622.76 452.48 L 1627.93 453.86 L 1632.07 454.55 L 1630.51 531.04" stroke-width="1" zvalue="577"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@0" LinkObjectIDznd="271@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1631.56 396.29 L 1625.86 396.62 L 1621.72 399.38 L 1617.93 402.48 L 1615.52 406.28 L 1613.79 409.03 L 1613.1 412.48 L 1613.1 415.93 L 1613.79 419.72 L 1614.83 423.86 L 1616.55 425.24 L 1616.55 426.28 L 1615.17 430.41 L 1613.45 435.24 L 1613.79 440.07 L 1615.52 444.21 L 1618.28 448.69 L 1622.76 452.48 L 1627.93 453.86 L 1632.07 454.55 L 1630.51 531.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="351">
   <use class="kv110" height="20" transform="rotate(270,723.611,202.556) scale(-1.11111,1.11111) translate(-1374.31,-19.1444)" width="10" x="718.0555555555554" xlink:href="#GroundDisconnector:地刀_0" y="191.4444444444443" zvalue="96"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454836551682" ObjectName="110kV勐嘎四级线14117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454836551682"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,723.611,202.556) scale(-1.11111,1.11111) translate(-1374.31,-19.1444)" width="10" x="718.0555555555554" y="191.4444444444443"/></g>
  <g id="335">
   <use class="kv110" height="20" transform="rotate(270,1366.33,227.056) scale(-1.11111,1.11111) translate(-2595.48,-21.5944)" width="10" x="1360.777784559462" xlink:href="#GroundDisconnector:地刀_0" y="215.9444341659546" zvalue="104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449626243077" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449626243077"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1366.33,227.056) scale(-1.11111,1.11111) translate(-2595.48,-21.5944)" width="10" x="1360.777784559462" y="215.9444341659546"/></g>
  <g id="326">
   <use class="kv110" height="20" transform="rotate(270,714.535,374.333) scale(-1.11111,1.11111) translate(-1357.06,-36.3222)" width="10" x="708.979808152324" xlink:href="#GroundDisconnector:地刀_0" y="363.2222222222222" zvalue="113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454836158466" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454836158466"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,714.535,374.333) scale(-1.11111,1.11111) translate(-1357.06,-36.3222)" width="10" x="708.979808152324" y="363.2222222222222"/></g>
  <g id="266">
   <use class="kv110" height="20" transform="rotate(270,1366.33,286.889) scale(-1.11111,1.11111) translate(-2595.48,-27.5778)" width="10" x="1360.777784559462" xlink:href="#GroundDisconnector:地刀_0" y="275.7777777777778" zvalue="119"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449626046469" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449626046469"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1366.33,286.889) scale(-1.11111,1.11111) translate(-2595.48,-27.5778)" width="10" x="1360.777784559462" y="275.7777777777778"/></g>
  <g id="234">
   <use class="kv110" height="40" transform="rotate(0,746.636,518.515) scale(0.7,-0.875) translate(313.987,-1113.6)" width="40" x="732.6363636363637" xlink:href="#GroundDisconnector:中性点地刀12_0" y="501.0151515151515" zvalue="132"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454835830786" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454835830786"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,746.636,518.515) scale(0.7,-0.875) translate(313.987,-1113.6)" width="40" x="732.6363636363637" y="501.0151515151515"/></g>
  <g id="227">
   <use class="kv110" height="20" transform="rotate(270,1361.56,374.333) scale(-1.11111,1.11111) translate(-2586.41,-36.3222)" width="10" x="1356.005962416492" xlink:href="#GroundDisconnector:地刀_0" y="363.2222222222222" zvalue="144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454835568642" ObjectName="#2主变110kV侧10217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454835568642"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1361.56,374.333) scale(-1.11111,1.11111) translate(-2586.41,-36.3222)" width="10" x="1356.005962416492" y="363.2222222222222"/></g>
  <g id="364">
   <use class="kv110" height="40" transform="rotate(0,1405.82,512.131) scale(0.7,-0.875) translate(596.494,-1099.92)" width="40" x="1391.818181818182" xlink:href="#GroundDisconnector:中性点地刀12_0" y="494.6311619540688" zvalue="163"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454835437570" ObjectName="#2主变110kV侧1020中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454835437570"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1405.82,512.131) scale(0.7,-0.875) translate(596.494,-1099.92)" width="40" x="1391.818181818182" y="494.6311619540688"/></g>
  <g id="394">
   <use class="kv10" height="20" transform="rotate(270,856.975,647.646) scale(-1.11111,1.11111) translate(-1627.7,-63.6535)" width="10" x="851.4191919191916" xlink:href="#GroundDisconnector:地刀_0" y="636.5353535353536" zvalue="181"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454835109890" ObjectName="10kVⅠ母电压互感器090117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454835109890"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,856.975,647.646) scale(-1.11111,1.11111) translate(-1627.7,-63.6535)" width="10" x="851.4191919191916" y="636.5353535353536"/></g>
  <g id="433">
   <use class="kv10" height="20" transform="rotate(270,582.854,899.02) scale(-1.11111,1.11111) translate(-1106.87,-88.7909)" width="10" x="577.2979797979797" xlink:href="#GroundDisconnector:地刀_0" y="887.909090909091" zvalue="204"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454834651138" ObjectName="#1发电机09117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454834651138"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,582.854,899.02) scale(-1.11111,1.11111) translate(-1106.87,-88.7909)" width="10" x="577.2979797979797" y="887.909090909091"/></g>
  <g id="437">
   <use class="kv10" height="20" transform="rotate(270,718.854,918.354) scale(-1.11111,1.11111) translate(-1365.27,-90.7242)" width="10" x="713.2979797979797" xlink:href="#GroundDisconnector:地刀_0" y="907.2424242424242" zvalue="214"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454834323458" ObjectName="#1发电机09127接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454834323458"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,718.854,918.354) scale(-1.11111,1.11111) translate(-1365.27,-90.7242)" width="10" x="713.2979797979797" y="907.2424242424242"/></g>
  <g id="461">
   <use class="kv10" height="20" transform="rotate(270,803.854,755.242) scale(-1.11111,1.11111) translate(-1526.77,-74.4131)" width="10" x="798.2979797979797" xlink:href="#GroundDisconnector:地刀_0" y="744.1313131313132" zvalue="221"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454834126850" ObjectName="#1站用变04317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454834126850"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,803.854,755.242) scale(-1.11111,1.11111) translate(-1526.77,-74.4131)" width="10" x="798.2979797979797" y="744.1313131313132"/></g>
  <g id="500">
   <use class="kv10" height="20" transform="rotate(270,1367.85,895.02) scale(-1.11111,1.11111) translate(-2598.37,-88.3909)" width="10" x="1362.29797979798" xlink:href="#GroundDisconnector:地刀_0" y="883.909090909091" zvalue="251"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454833537026" ObjectName="#2发电机09217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454833537026"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1367.85,895.02) scale(-1.11111,1.11111) translate(-2598.37,-88.3909)" width="10" x="1362.29797979798" y="883.909090909091"/></g>
  <g id="493">
   <use class="kv10" height="20" transform="rotate(270,1518.23,902.166) scale(-1.11111,1.11111) translate(-2884.08,-89.1055)" width="10" x="1512.67297979798" xlink:href="#GroundDisconnector:地刀_0" y="891.0549242424242" zvalue="259"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454833209346" ObjectName="#2发电机09227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454833209346"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1518.23,902.166) scale(-1.11111,1.11111) translate(-2884.08,-89.1055)" width="10" x="1512.67297979798" y="891.0549242424242"/></g>
  <g id="487">
   <use class="kv10" height="20" transform="rotate(270,1160.85,735.242) scale(-1.11111,1.11111) translate(-2205.07,-72.4131)" width="10" x="1155.29797979798" xlink:href="#GroundDisconnector:地刀_0" y="724.131313131313" zvalue="265"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454833012738" ObjectName="#2站用变04427接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454833012738"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1160.85,735.242) scale(-1.11111,1.11111) translate(-2205.07,-72.4131)" width="10" x="1155.29797979798" y="724.131313131313"/></g>
  <g id="599">
   <use class="kv10" height="20" transform="rotate(270,1230.97,643.646) scale(-1.11111,1.11111) translate(-2338.3,-63.2535)" width="10" x="1225.419191919192" xlink:href="#GroundDisconnector:地刀_0" y="632.5353535353536" zvalue="326"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454832619522" ObjectName="10kVⅡ母电压互感器09027接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454832619522"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1230.97,643.646) scale(-1.11111,1.11111) translate(-2338.3,-63.2535)" width="10" x="1225.419191919192" y="632.5353535353536"/></g>
  <g id="274">
   <use class="kv10" height="20" transform="rotate(270,1662.56,640.222) scale(-1.11111,1.11111) translate(-3158.3,-62.9111)" width="10" x="1657.00003390842" xlink:href="#GroundDisconnector:地刀_0" y="629.1111111111111" zvalue="424"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454837075970" ObjectName="10kV近区变04527接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454837075970"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1662.56,640.222) scale(-1.11111,1.11111) translate(-3158.3,-62.9111)" width="10" x="1657.00003390842" y="629.1111111111111"/></g>
 </g>
 <g id="BreakerClass">
  <g id="329">
   <use class="kv110" height="20" transform="rotate(0,669.58,409.556) scale(1.22222,1.11111) translate(-120.631,-39.8444)" width="10" x="663.4690983913207" xlink:href="#Breaker:开关_0" y="398.4444444444444" zvalue="110"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925222858755" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925222858755"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,669.58,409.556) scale(1.22222,1.11111) translate(-120.631,-39.8444)" width="10" x="663.4690983913207" y="398.4444444444444"/></g>
  <g id="229">
   <use class="kv110" height="20" transform="rotate(0,1315.56,410.556) scale(1.22222,1.11111) translate(-238.082,-39.9444)" width="10" x="1309.450588582426" xlink:href="#Breaker:开关_0" y="399.4444444444444" zvalue="140"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925222793219" ObjectName="#2主变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473925222793219"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1315.56,410.556) scale(1.22222,1.11111) translate(-238.082,-39.9444)" width="10" x="1309.450588582426" y="399.4444444444444"/></g>
  <g id="459">
   <use class="kv10" height="20" transform="rotate(0,774.454,784.806) scale(1.30556,1.175) translate(-179.727,-115.136)" width="10" x="767.9260942760945" xlink:href="#Breaker:开关_0" y="773.0555555555554" zvalue="224"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925222727683" ObjectName="#1站用变043断路器"/>
   <cge:TPSR_Ref TObjectID="6473925222727683"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,774.454,784.806) scale(1.30556,1.175) translate(-179.727,-115.136)" width="10" x="767.9260942760945" y="773.0555555555554"/></g>
  <g id="485">
   <use class="kv10" height="20" transform="rotate(0,1130.34,763.694) scale(1.30556,1.175) translate(-263.021,-111.992)" width="10" x="1123.814983164983" xlink:href="#Breaker:开关_0" y="751.9444444444446" zvalue="268"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925222662147" ObjectName="#2站用变044断路器"/>
   <cge:TPSR_Ref TObjectID="6473925222662147"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1130.34,763.694) scale(1.30556,1.175) translate(-263.021,-111.992)" width="10" x="1123.814983164983" y="751.9444444444446"/></g>
  <g id="271">
   <use class="kv10" height="20" transform="rotate(0,1630.56,541.667) scale(1.22222,1.11111) translate(-295.354,-53.0556)" width="10" x="1624.44447851674" xlink:href="#Breaker:开关_0" y="530.5555555555555" zvalue="418"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925222924291" ObjectName="10kV近区变045断路器"/>
   <cge:TPSR_Ref TObjectID="6473925222924291"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1630.56,541.667) scale(1.22222,1.11111) translate(-295.354,-53.0556)" width="10" x="1624.44447851674" y="530.5555555555555"/></g>
  <g id="284">
   <use class="kv10" height="20" transform="rotate(0,621.111,764.444) scale(1.55556,2) translate(-219.048,-372.222)" width="10" x="613.3333333333333" xlink:href="#Breaker:小车断路器_0" y="744.4444444444446" zvalue="430"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924482367493" ObjectName="#1发电机041断路器"/>
   <cge:TPSR_Ref TObjectID="6473924482367493"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,621.111,764.444) scale(1.55556,2) translate(-219.048,-372.222)" width="10" x="613.3333333333333" y="744.4444444444446"/></g>
  <g id="289">
   <use class="kv10" height="20" transform="rotate(0,1406.67,764.889) scale(1.55556,2) translate(-499.603,-372.444)" width="10" x="1398.888888888889" xlink:href="#Breaker:小车断路器_0" y="744.8888888888889" zvalue="434"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924482433029" ObjectName="#2发电机042断路器"/>
   <cge:TPSR_Ref TObjectID="6473924482433029"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1406.67,764.889) scale(1.55556,2) translate(-499.603,-372.444)" width="10" x="1398.888888888889" y="744.8888888888889"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="268">
   <use class="kv110" height="20" transform="rotate(0,1316.15,183.583) scale(2.08,-2.275) translate(-669.888,-251.529)" width="25" x="1290.154261750994" xlink:href="#Accessory:4绕组母线PT_0" y="160.8333333333333" zvalue="117"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454836027394" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1316.15,183.583) scale(2.08,-2.275) translate(-669.888,-251.529)" width="25" x="1290.154261750994" y="160.8333333333333"/></g>
  <g id="392">
   <use class="kv10" height="29" transform="rotate(90,857.636,604.193) scale(-0.909091,0.909091) translate(-1801.95,59.1011)" width="20" x="848.5454545454544" xlink:href="#Accessory:消谐装置_0" y="591.011139343384" zvalue="184"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454834978818" ObjectName="10kVⅠ母电压互感器带消谐避雷器"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(90,857.636,604.193) scale(-0.909091,0.909091) translate(-1801.95,59.1011)" width="20" x="848.5454545454544" y="591.011139343384"/></g>
  <g id="399">
   <use class="kv10" height="18" transform="rotate(0,830.021,538.333) scale(3.2,-2.66667) translate(-554.14,-725.208)" width="15" x="806.0211883829555" xlink:href="#Accessory:PT8_0" y="514.3333333333334" zvalue="187"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454834913282" ObjectName="10kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,830.021,538.333) scale(3.2,-2.66667) translate(-554.14,-725.208)" width="15" x="806.0211883829555" y="514.3333333333334"/></g>
  <g id="421">
   <use class="kv10" height="29" transform="rotate(90,657.223,830.698) scale(-0.909091,0.909091) translate(-1381.08,81.7516)" width="20" x="648.1325757575755" xlink:href="#Accessory:消谐装置_0" y="817.5164141414141" zvalue="199"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454837272578" ObjectName="#1发电机带消谐避雷器"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(90,657.223,830.698) scale(-0.909091,0.909091) translate(-1381.08,81.7516)" width="20" x="648.1325757575755" y="817.5164141414141"/></g>
  <g id="434">
   <use class="kv10" height="30" transform="rotate(0,558.661,961.889) scale(0.970396,0.970396) translate(16.5993,28.9008)" width="30" x="544.1049591750846" xlink:href="#Accessory:PT789_0" y="947.3333333333333" zvalue="207"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454834520066" ObjectName="#1发电机3LB"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,558.661,961.889) scale(0.970396,0.970396) translate(16.5993,28.9008)" width="30" x="544.1049591750846" y="947.3333333333333"/></g>
  <g id="441">
   <use class="kv10" height="30" transform="rotate(0,687.521,812.05) scale(1.58148,1.54406) translate(-244.067,-277.971)" width="30" x="663.7992424242427" xlink:href="#Accessory:RT1122_0" y="788.8888888888889" zvalue="208"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454834454530" ObjectName="#1发电机1LB"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,687.521,812.05) scale(1.58148,1.54406) translate(-244.067,-277.971)" width="30" x="663.7992424242427" y="788.8888888888889"/></g>
  <g id="456">
   <use class="kv10" height="29" transform="rotate(90,809.098,813.157) scale(-0.909091,0.909091) translate(-1700.02,79.9975)" width="20" x="800.0075757575755" xlink:href="#Accessory:消谐装置_0" y="799.9747474747473" zvalue="227"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454833995778" ObjectName="#1站用变带消谐避雷器"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(90,809.098,813.157) scale(-0.909091,0.909091) translate(-1700.02,79.9975)" width="20" x="800.0075757575755" y="799.9747474747473"/></g>
  <g id="506">
   <use class="kv10" height="29" transform="rotate(90,1437.18,814.014) scale(-0.909091,0.909091) translate(-3018.99,80.0832)" width="20" x="1428.090909090909" xlink:href="#Accessory:消谐装置_0" y="800.8318903318905" zvalue="246"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454833733634" ObjectName="#2发电机带消谐避雷器"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(90,1437.18,814.014) scale(-0.909091,0.909091) translate(-3018.99,80.0832)" width="20" x="1428.090909090909" y="800.8318903318905"/></g>
  <g id="498">
   <use class="kv10" height="30" transform="rotate(0,1344.57,949.889) scale(0.970396,0.970396) translate(40.5754,28.5347)" width="30" x="1330.014050084176" xlink:href="#Accessory:PT789_0" y="935.3333333333333" zvalue="254"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454833405954" ObjectName="#2发电机3LB"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1344.57,949.889) scale(0.970396,0.970396) translate(40.5754,28.5347)" width="30" x="1330.014050084176" y="935.3333333333333"/></g>
  <g id="497">
   <use class="kv10" height="30" transform="rotate(0,1476.58,813.3) scale(1.58148,1.54406) translate(-534.19,-278.411)" width="30" x="1452.861739264594" xlink:href="#Accessory:RT1122_0" y="790.1388888888889" zvalue="255"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454833340418" ObjectName="#2发电机2LB"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1476.58,813.3) scale(1.58148,1.54406) translate(-534.19,-278.411)" width="30" x="1452.861739264594" y="790.1388888888889"/></g>
  <g id="482">
   <use class="kv10" height="29" transform="rotate(90,1169.49,790.657) scale(-0.909091,0.909091) translate(-2456.84,77.7475)" width="20" x="1160.400432900433" xlink:href="#Accessory:消谐装置_0" y="777.4747474747476" zvalue="271"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454832881666" ObjectName="#2站用变带消谐避雷器"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(90,1169.49,790.657) scale(-0.909091,0.909091) translate(-2456.84,77.7475)" width="20" x="1160.400432900433" y="777.4747474747476"/></g>
  <g id="597">
   <use class="kv10" height="29" transform="rotate(90,1231.64,600.727) scale(-0.909091,0.909091) translate(-2587.35,58.7545)" width="20" x="1222.545454545455" xlink:href="#Accessory:消谐装置_0" y="587.5454545454545" zvalue="329"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454832488450" ObjectName="10kVⅡ母电压互感器带消谐避雷器"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(90,1231.64,600.727) scale(-0.909091,0.909091) translate(-2587.35,58.7545)" width="20" x="1222.545454545455" y="587.5454545454545"/></g>
  <g id="594">
   <use class="kv10" height="18" transform="rotate(0,1204.02,542.333) scale(3.2,-2.66667) translate(-811.265,-730.708)" width="15" x="1180.021188382955" xlink:href="#Accessory:PT8_0" y="518.3333333333334" zvalue="332"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454832422914" ObjectName="10kVⅡ母电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1204.02,542.333) scale(3.2,-2.66667) translate(-811.265,-730.708)" width="15" x="1180.021188382955" y="518.3333333333334"/></g>
  <g id="257">
   <use class="kv10" height="32" transform="rotate(0,688.19,968.063) scale(2.22222,1.73611) translate(-371.171,-398.681)" width="12" x="674.8571428571428" xlink:href="#Accessory:腊撒线路PT_0" y="940.2857142857144" zvalue="404"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454837403650" ObjectName="#1发电机PT带熔断器"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,688.19,968.063) scale(2.22222,1.73611) translate(-371.171,-398.681)" width="12" x="674.8571428571428" y="940.2857142857144"/></g>
  <g id="264">
   <use class="kv10" height="32" transform="rotate(0,1476.44,945.111) scale(2.22222,1.73611) translate(-804.711,-388.949)" width="12" x="1463.111111111111" xlink:href="#Accessory:腊撒线路PT_0" y="917.3333333333334" zvalue="411"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454836748290" ObjectName="#2发电机PT带熔断器"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,1476.44,945.111) scale(2.22222,1.73611) translate(-804.711,-388.949)" width="12" x="1463.111111111111" y="917.3333333333334"/></g>
  <g id="272">
   <use class="kv10" height="29" transform="rotate(270,1601.52,494.768) scale(-0.909091,0.909091) translate(-3364.09,48.1586)" width="20" x="1592.424242424242" xlink:href="#Accessory:消谐装置_0" y="481.5858585858586" zvalue="420"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454836879362" ObjectName="10kV近区变带消谐避雷器"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(270,1601.52,494.768) scale(-0.909091,0.909091) translate(-3364.09,48.1586)" width="20" x="1592.424242424242" y="481.5858585858586"/></g>
  <g id="40">
   <use class="kv10" height="29" transform="rotate(90,701.182,560.091) scale(-0.909091,0.909091) translate(-1473.39,54.6909)" width="20" x="692.090909090909" xlink:href="#Accessory:消谐装置_0" y="546.909090909091" zvalue="541"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450116845573" ObjectName="1主变带消谐避雷器"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(90,701.182,560.091) scale(-0.909091,0.909091) translate(-1473.39,54.6909)" width="20" x="692.090909090909" y="546.909090909091"/></g>
  <g id="41">
   <use class="kv10" height="29" transform="rotate(90,1346.18,553.091) scale(-0.909091,0.909091) translate(-2827.89,53.9909)" width="20" x="1337.090909090909" xlink:href="#Accessory:消谐装置_0" y="539.9090909090909" zvalue="543"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450116911109" ObjectName="2主变带消谐避雷器"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(90,1346.18,553.091) scale(-0.909091,0.909091) translate(-2827.89,53.9909)" width="20" x="1337.090909090909" y="539.9090909090909"/></g>
  <g id="43">
   <use class="kv10" height="25" transform="rotate(0,558.635,811.25) scale(1.5,-1.5) translate(-184.212,-1345.83)" width="8" x="552.634903793886" xlink:href="#Accessory:大地附属设备_0" y="792.5" zvalue="545"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450116976645" ObjectName="1G接地"/>
   </metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,558.635,811.25) scale(1.5,-1.5) translate(-184.212,-1345.83)" width="8" x="552.634903793886" y="792.5"/></g>
  <g id="44">
   <use class="kv10" height="25" transform="rotate(0,1344.63,811.25) scale(1.5,-1.5) translate(-446.212,-1345.83)" width="8" x="1338.634903793886" xlink:href="#Accessory:大地附属设备_0" y="792.5" zvalue="547"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450117042181" ObjectName="2G接地"/>
   </metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1344.63,811.25) scale(1.5,-1.5) translate(-446.212,-1345.83)" width="8" x="1338.634903793886" y="792.5"/></g>
  <g id="64">
   <use class="kv10" height="20" transform="rotate(0,1631.38,425.5) scale(3.05,3.05) translate(-1081.12,-265.492)" width="15" x="1608.5" xlink:href="#Accessory:PT6_0" y="395" zvalue="569"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454836813826" ObjectName="10kV近区变"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1631.38,425.5) scale(3.05,3.05) translate(-1081.12,-265.492)" width="15" x="1608.5" y="395"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="261">
   <g id="2610">
    <use class="kv110" height="50" transform="rotate(0,669.614,489.639) scale(1.44089,1.24221) translate(-198.279,-89.4174)" width="30" x="648" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="458.58" zvalue="121"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874594910210" ObjectName="110"/>
    </metadata>
   </g>
   <g id="2611">
    <use class="kv10" height="50" transform="rotate(0,669.614,489.639) scale(1.44089,1.24221) translate(-198.279,-89.4174)" width="30" x="648" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="458.58" zvalue="121"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874594975746" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399534510082" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399534510082"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,669.614,489.639) scale(1.44089,1.24221) translate(-198.279,-89.4174)" width="30" x="648" y="458.58"/></g>
  <g id="226">
   <g id="2260">
    <use class="kv110" height="50" transform="rotate(0,1315.59,484.804) scale(1.58776,1.36884) translate(-478.194,-121.411)" width="30" x="1291.77" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="450.58" zvalue="146"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874594779138" ObjectName="110"/>
    </metadata>
   </g>
   <g id="2261">
    <use class="kv10" height="50" transform="rotate(0,1315.59,484.804) scale(1.58776,1.36884) translate(-478.194,-121.411)" width="30" x="1291.77" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="450.58" zvalue="146"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874594844674" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399534444546" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399534444546"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1315.59,484.804) scale(1.58776,1.36884) translate(-478.194,-121.411)" width="30" x="1291.77" y="450.58"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="kv10" height="30" transform="rotate(0,622.222,914.222) scale(1.5,1.5) translate(-199.907,-297.241)" width="30" x="599.7222222222224" xlink:href="#Generator:发电机_0" y="891.7222222222223" zvalue="123"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454837338114" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192454837338114"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,622.222,914.222) scale(1.5,1.5) translate(-199.907,-297.241)" width="30" x="599.7222222222224" y="891.7222222222223"/></g>
  <g id="517">
   <use class="kv10" height="30" transform="rotate(0,1405,916.333) scale(1.5,1.5) translate(-460.833,-297.944)" width="30" x="1382.5" xlink:href="#Generator:发电机_0" y="893.8333333333335" zvalue="231"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454833930242" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192454833930242"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1405,916.333) scale(1.5,1.5) translate(-460.833,-297.944)" width="30" x="1382.5" y="893.8333333333335"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="232">
   <use class="kv10" height="30" transform="rotate(0,774.541,884.833) scale(2.1,2.1) translate(-394.712,-446.984)" width="20" x="753.5409090909093" xlink:href="#EnergyConsumer:站用变11_0" y="853.3333333333335" zvalue="135"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454835699714" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,774.541,884.833) scale(2.1,2.1) translate(-394.712,-446.984)" width="20" x="753.5409090909093" y="853.3333333333335"/></g>
  <g id="516">
   <use class="kv10" height="30" transform="rotate(0,1130.43,864.833) scale(2.1,2.1) translate(-581.13,-436.508)" width="20" x="1109.429797979798" xlink:href="#EnergyConsumer:站用变11_0" y="833.3333333333335" zvalue="233"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454833864706" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1130.43,864.833) scale(2.1,2.1) translate(-581.13,-436.508)" width="20" x="1109.429797979798" y="833.3333333333335"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="7" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,672.551,39.0417) scale(1,1) translate(0,0)" writing-mode="lr" x="672.08" xml:space="preserve" y="43.7" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136565387266" ObjectName="P"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="8" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,672.551,59.0417) scale(1,1) translate(0,-1.07877e-14)" writing-mode="lr" x="672.08" xml:space="preserve" y="63.71" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136565452802" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="9" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,672.551,77.0417) scale(1,1) translate(0,-1.51175e-14)" writing-mode="lr" x="672.08" xml:space="preserve" y="81.68000000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136565518338" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="20" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1377.59,592.013) scale(1,1) translate(0,6.42847e-14)" writing-mode="lr" x="1377.04" xml:space="preserve" y="596.72" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136559292418" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="21" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,586.614,569.347) scale(1,1) translate(0,-1.23611e-13)" writing-mode="lr" x="586.0599999999999" xml:space="preserve" y="574.05" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136562962434" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="22" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1377.59,613.013) scale(1,1) translate(0,6.65607e-14)" writing-mode="lr" x="1377.04" xml:space="preserve" y="617.73" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136559357954" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="23" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,586.614,589.347) scale(1,1) translate(0,-1.28385e-13)" writing-mode="lr" x="586.0599999999999" xml:space="preserve" y="594.02" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136563027970" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="26">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="26" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1377.59,632.513) scale(1,1) translate(0,6.88366e-14)" writing-mode="lr" x="1377.04" xml:space="preserve" y="637.21" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136559751170" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="27">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="27" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,586.614,611.847) scale(1,1) translate(0,0)" writing-mode="lr" x="586.0599999999999" xml:space="preserve" y="616.54" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136563421186" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="28">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="28" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1400,989.417) scale(1,1) translate(0,-2.16679e-13)" writing-mode="lr" x="1399.45" xml:space="preserve" y="994.13" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136557981698" ObjectName="P"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="37" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,612.222,978.861) scale(1,1) translate(0,1.07328e-13)" writing-mode="lr" x="611.67" xml:space="preserve" y="983.55" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136568598530" ObjectName="P"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="38" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1400,1006.92) scale(1,1) translate(0,-2.20897e-13)" writing-mode="lr" x="1399.45" xml:space="preserve" y="1011.61" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136558047234" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="39" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,612.222,999.861) scale(1,1) translate(0,1.09715e-13)" writing-mode="lr" x="611.67" xml:space="preserve" y="1004.55" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136568664066" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="54" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1400,1027.42) scale(1,1) translate(0,2.02605e-12)" writing-mode="lr" x="1399.45" xml:space="preserve" y="1032.13" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136558112770" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="88" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,612.222,1019.86) scale(1,1) translate(0,2.24203e-13)" writing-mode="lr" x="611.67" xml:space="preserve" y="1024.52" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136568729602" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="132">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="132" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136567353346" ObjectName="F"/>
   </metadata>
  </g>
  <g id="249">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="249" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136569384962" ObjectName="F"/>
   </metadata>
  </g>
  <g id="248">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="248" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136569450498" ObjectName="F"/>
   </metadata>
  </g>
  <g id="242">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="242" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136569253890" ObjectName="F"/>
   </metadata>
  </g>
  <g id="241">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="241" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136569319426" ObjectName="F"/>
   </metadata>
  </g>
  <g id="219">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="219" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127181549573" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127181484037" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="191">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="191" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,358.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="363.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136562110466" ObjectName="F"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.111,333.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.27" xml:space="preserve" y="338.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136566829058" ObjectName="F"/>
   </metadata>
  </g>
  <g id="45">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="45" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1129.23,520.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1128.76" xml:space="preserve" y="525.39" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136561717250" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="46">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="46" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,845.889,435.722) scale(1,1) translate(-1.77833e-13,0)" writing-mode="lr" x="845.42" xml:space="preserve" y="440.5" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136566435842" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="47">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="47" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1197,112.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1196.53" xml:space="preserve" y="117.28" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136566960130" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="48">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="48" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1129.23,549.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1128.76" xml:space="preserve" y="554.39" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136561782786" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="49">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="49" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,845.889,464.722) scale(1,1) translate(-1.77833e-13,0)" writing-mode="lr" x="845.42" xml:space="preserve" y="469.5" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136566501378" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="50">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="50" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1197,141.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1196.53" xml:space="preserve" y="146.28" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136567025666" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="51">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="51" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1129.23,578.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1128.76" xml:space="preserve" y="583.39" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136561848322" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="52" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,845.889,493.722) scale(1,1) translate(-1.77833e-13,0)" writing-mode="lr" x="845.42" xml:space="preserve" y="498.5" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136566566914" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="53" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1197,170.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1196.53" xml:space="preserve" y="175.28" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136567091202" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="55" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1051.23,657.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1050.76" xml:space="preserve" y="661.89" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136561979394" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="56" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,491.889,649.222) scale(1,1) translate(0,-2.80541e-13)" writing-mode="lr" x="491.42" xml:space="preserve" y="654" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136566697986" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="57" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,540,337) scale(1,1) translate(0,0)" writing-mode="lr" x="539.53" xml:space="preserve" y="341.78" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136567222274" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="60">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="60" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,598.307,386.045) scale(1,1) translate(0,-2.498e-13)" writing-mode="lr" x="597.8099999999999" xml:space="preserve" y="392.22" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136562831362" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="61">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="61" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,598.307,407.868) scale(1,1) translate(0,-2.64337e-13)" writing-mode="lr" x="597.8099999999999" xml:space="preserve" y="414.04" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136562896898" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="62" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,598.307,429.691) scale(1,1) translate(0,-2.78874e-13)" writing-mode="lr" x="597.8099999999999" xml:space="preserve" y="435.87" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136563093506" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="17" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1228.31,366.417) scale(1,1) translate(0,3.92385e-14)" writing-mode="lr" x="1177.52" xml:space="preserve" y="370.81" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136559161346" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="19" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1228.31,395.417) scale(1,1) translate(0,4.24582e-14)" writing-mode="lr" x="1177.52" xml:space="preserve" y="399.81" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136559226882" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="25">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="25" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1228.31,424.417) scale(1,1) translate(0,4.56778e-14)" writing-mode="lr" x="1177.52" xml:space="preserve" y="428.81" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136559423490" ObjectName="HIa"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="247">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="517"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374885781508" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="58">
   <use class="kv10" height="40" transform="rotate(0,1630.85,268) scale(1,1) translate(0,0)" width="30" x="1615.852811850074" xlink:href="#ACLineSegment:线路带壁雷器_0" y="248" zvalue="564"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249326419971" ObjectName="10kV近区线路勐四五联络线"/>
   <cge:TPSR_Ref TObjectID="8444249326419971_5066549685059585"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1630.85,268) scale(1,1) translate(0,0)" width="30" x="1615.852811850074" y="248"/></g>
 </g>
</svg>