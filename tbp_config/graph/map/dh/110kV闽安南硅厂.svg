<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549590163458" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Accessory:5绕组母线PT带避雷器_0" viewBox="0,0,40,35">
   <use terminal-index="0" type="0" x="22.10905664884498" xlink:href="#terminal" y="34.51612485684674"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.91666666666667" x2="34.5" y1="28.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="21.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="22" y1="28.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.46666666666667" x2="35.46666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.46666666666667" x2="33.46666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.13333333333333" x2="18.13333333333333" y1="19.27740325661302" y2="15.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.13333333333333" x2="20.13333333333333" y1="19.27740325661302" y2="18.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.13333333333333" x2="20.13333333333333" y1="15.27740325661302" y2="16.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.5" x2="34.5" y1="28.5" y2="14.5"/>
   <rect fill-opacity="0" height="15.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,34.5,14.36) scale(1,-1) translate(0,-926.43)" width="7" x="31" y="6.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.42943360505483" x2="34.42943360505483" y1="6.610736589946352" y2="3.462520268614096"/>
   <ellipse cx="11.81" cy="17.24" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="36.45921744067709" x2="32.18240159599324" y1="3.3946117330996" y2="3.3946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.62588410734375" x2="33.01573492932658" y1="2.144611733099605" y2="2.144611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.04255077401042" x2="33.59906826265991" y1="0.8946117330996053" y2="0.8946117330996053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.78240159599324" x2="9.382401595993244" y1="17.45662962336982" y2="18.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.78240159599325" x2="14.18240159599324" y1="17.45662962336982" y2="18.69416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.78240159599324" x2="11.78240159599324" y1="14.98154965466559" y2="17.4566296233698"/>
   <ellipse cx="4.73" cy="17.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.98" cy="10.41" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.949068262659907" x2="5.549068262659912" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.949068262659912" x2="10.34906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.69906826265991" x2="4.69906826265991" y1="15.23154965466559" y2="17.7066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.699068262659912" x2="7.099068262659907" y1="17.70662962336982" y2="18.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.94906826265991" x2="7.94906826265991" y1="8.148216321332258" y2="10.62329629003646"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.699068262659907" x2="2.299068262659912" y1="17.70662962336982" y2="18.94416960772192"/>
   <ellipse cx="18.81" cy="17.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.48" cy="10.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.44906826265991" x2="17.84906826265991" y1="10.53996295670315" y2="11.77750294105526"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.4490682626599" x2="13.04906826265991" y1="10.53996295670315" y2="11.77750294105526"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.44906826265991" x2="15.44906826265991" y1="8.064882987998928" y2="10.53996295670313"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="PowerTransformer2:接地可调两卷变_0" viewBox="0,0,22,30">
   <use terminal-index="0" type="1" x="11.00731595793324" xlink:href="#terminal" y="1.08736282578875"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.02194787379973" x2="6.955871323769093" y1="7.640146319158664" y2="3.944602328551218"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.23333333333333" x2="11.01463191586648" y1="3.694602328551216" y2="7.640146319158664"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.00935070873343" x2="11.00935070873343" y1="7.654778235025146" y2="11.68728637061797"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="21.53783721993598" x2="20.53783721993598" y1="2.175582990397805" y2="5.175582990397805"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.5394375857338822" x2="21.53943758573388" y1="13.09064929126657" y2="2.173982624599901"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="18.53052126200274" x2="21.53052126200274" y1="1.175582990397805" y2="2.175582990397805"/>
   <ellipse cx="11.09" cy="9.44" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="2" x="11" xlink:href="#terminal" y="7.7"/>
  </symbol>
  <symbol id="PowerTransformer2:接地可调两卷变_1" viewBox="0,0,22,30">
   <use terminal-index="1" type="1" x="11" xlink:href="#terminal" y="29.05121170553269"/>
   <path d="M 6.75 25.8333 L 15.8333 25.8333 L 11.0833 18.5 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="11.09" cy="20.69" fill-opacity="0" rx="8.359999999999999" ry="8.359999999999999" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Compensator:无功补偿20210816_0" viewBox="0,0,12,13">
   <use terminal-index="0" type="0" x="6.1" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.4" x2="4" y1="3.141666666666663" y2="3.141666666666663"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.141666666666663" x2="6.141666666666663" y1="0.04999999999999893" y2="3.141666666666661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.141666666666663" x2="6.141666666666663" y1="5.15" y2="8.09166666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.629535048371164" x2="2.363367518766924" y1="11.72804518360739" y2="7.956509060518093"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.02953748863437" x2="7.38155138676536" y1="8.002747606646338" y2="11.51674385085443"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.116482967203315" x2="3.466407395865956" y1="8.199949647924862" y2="9.792275696188446"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.4" x2="4" y1="5.141666666666663" y2="5.141666666666663"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.201323776119781" x2="8.670438561349325" y1="7.945721353591398" y2="9.806332800169811"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.915200446966938" x2="0.6490329173626979" y1="12.7581213334275" y2="8.986585210338204"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.62680850872896" x2="8.978822406859944" y1="9.206377652950431" y2="12.72037389715852"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="EnergyConsumer:电炉_0" viewBox="0,0,27,14">
   <use terminal-index="0" type="0" x="13.5" xlink:href="#terminal" y="0.25"/>
   <rect fill-opacity="0" height="10.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.54,5.54) scale(1,1) translate(0,0)" width="3.08" x="12" y="0.25"/>
   <path d="M 0.833333 8.25 A 12.5 5 0 0 0 25.8333 8.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1" x2="25.75" y1="8.25" y2="8.25"/>
  </symbol>
  <symbol id="Accessory:熔断器12_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="10"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1.916666666666668" y2="18.25"/>
   <rect fill-opacity="0" height="16.08" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.06,10.06) scale(1,1) translate(0,0)" width="9.58" x="0.27" y="2.02"/>
  </symbol>
  <symbol id="PowerTransformer3:主变高压侧有中性点_0" viewBox="0,0,50,50">
   <use terminal-index="0" type="1" x="14.75" xlink:href="#terminal" y="0.2499999999999929"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.33333333333333" x2="15.15740740740741" y1="10.58333333333333" y2="14.1754686785551"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.08333333333333" x2="15.17203932327389" y1="10.66666666666667" y2="14.1754686785551"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.16666666666666" x2="15.16666666666666" y1="14.16666666666666" y2="18.91666666666666"/>
   <ellipse cx="14.93" cy="15" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="2" x="15.08333333333334" xlink:href="#terminal" y="14.16666666666666"/>
  </symbol>
  <symbol id="PowerTransformer3:主变高压侧有中性点_1" viewBox="0,0,50,50">
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.66666666666666" x2="34.66666666666666" y1="29.41666666666667" y2="25.11796982167353"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.66598079561043" x2="29.75" y1="25.11796982167353" y2="20.66666666666667"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.67329675354367" x2="39.25" y1="25.11065386374029" y2="20.58333333333334"/>
   <ellipse cx="34.78" cy="25.08" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="1" x="49.50000000000001" xlink:href="#terminal" y="25.08333333333333"/>
  </symbol>
  <symbol id="PowerTransformer3:主变高压侧有中性点_2" viewBox="0,0,50,50">
   <path d="M 15.1667 32 L 10.0833 40 L 20.1667 40 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.12" cy="35.17" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="3" type="1" x="15.08333333333334" xlink:href="#terminal" y="49.91666666666667"/>
  </symbol>
  <symbol id="PowerTransformer3:三卷变带中性点_0" viewBox="0,0,50,50">
   <use terminal-index="0" type="1" x="14.75" xlink:href="#terminal" y="0.2499999999999929"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.33333333333333" x2="15.15740740740741" y1="10.58333333333333" y2="14.1754686785551"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.08333333333333" x2="15.17203932327389" y1="10.66666666666667" y2="14.1754686785551"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.16666666666666" x2="15.16666666666666" y1="14.16666666666666" y2="18.91666666666666"/>
   <ellipse cx="14.93" cy="15" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="2" x="15.08333333333334" xlink:href="#terminal" y="14.16666666666666"/>
  </symbol>
  <symbol id="PowerTransformer3:三卷变带中性点_1" viewBox="0,0,50,50">
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.66666666666666" x2="34.66666666666666" y1="29.41666666666667" y2="25.11796982167353"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.66598079561043" x2="29.75" y1="25.11796982167353" y2="20.66666666666667"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.67329675354367" x2="39.25" y1="25.11065386374029" y2="20.58333333333334"/>
   <ellipse cx="34.78" cy="25.08" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="2" type="1" x="49.50000000000001" xlink:href="#terminal" y="25.08333333333333"/>
  </symbol>
  <symbol id="PowerTransformer3:三卷变带中性点_2" viewBox="0,0,50,50">
   <path d="M 15.1667 32 L 10.0833 40 L 20.1667 40 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.12" cy="35.17" fill-opacity="0" rx="14.82" ry="14.82" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="3" type="1" x="15.00000000000001" xlink:href="#terminal" y="49.83333333333334"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变0716_0" viewBox="0,0,20,25">
   <use terminal-index="0" type="0" x="8" xlink:href="#terminal" y="1.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="8" y1="2.75" y2="1.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.25" x2="15.75" y1="20.5" y2="15.15"/>
   <ellipse cx="8.130000000000001" cy="6.97" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.19" cy="12.96" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.259517426273471" x2="8.259517426273471" y1="11.6368007916835" y2="13.30855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.939588918677396" x2="8.259517426273467" y1="14.98031840279781" y2="13.30855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.579445933869528" x2="8.259517426273458" y1="14.98031840279781" y2="13.30855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85" x2="13.64982126899018" y1="18.56619780557639" y2="18.56619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.00996425379804" x2="14.48985701519214" y1="19.40207720835499" y2="19.40207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.16992850759607" x2="15.32989276139411" y1="20.23795661113357" y2="20.23795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.259517426273472" x2="15.75" y1="13.30855959724066" y2="13.30855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.74991063449509" x2="15.74991063449509" y1="13.33333333333333" y2="18.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="8.353115032679748" x2="8.353115032679748" y1="22.5" y2="17.14357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.259517426273471" x2="8.259517426273471" y1="4.3868007916835" y2="6.058559597240661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.939588918677396" x2="8.259517426273467" y1="7.730318402797811" y2="6.05855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.579445933869528" x2="8.259517426273458" y1="7.730318402797811" y2="6.05855959724065"/>
  </symbol>
  <symbol id=":单相三绕组PT带避雷器_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="20" xlink:href="#terminal" y="39"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.79,20.83) scale(1,1) translate(0,0)" width="6.08" x="8.75" y="13.67"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666666" x2="31.16666666666666" y1="19.66666666666667" y2="19.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.25" x2="14.25" y1="30.91666666666666" y2="30.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.25" x2="13.25" y1="31.91666666666666" y2="31.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.25" x2="12.25" y1="32.91666666666666" y2="32.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="27.91666666666666" y2="30.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.5" x2="31.5" y1="27.25" y2="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.91022336769755" x2="19.91022336769755" y1="39.83333333333334" y2="0.9166666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="12.75" y1="21.66666666666667" y2="15.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="10.75" y1="21.66666666666667" y2="15.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="21.66666666666667" y2="8.999999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.33333333333333" x2="11.75" y1="9.008752830905545" y2="9.008752830905545"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="28.25545055364641" x2="28.25545055364641" y1="9.041388644475965" y2="15.16666666666666"/>
   <ellipse cx="28.43" cy="19.74" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="28.51" cy="27.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="35.07589728904158" x2="35.07589728904158" y1="8.997588122517026" y2="8.997588122517026"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.25000000000001" x2="38.25000000000001" y1="24.41666666666667" y2="24.41666666666667"/>
   <ellipse cx="34.78" cy="24.19" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV闽安南硅厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="66.95999999999999" xlink:href="logo.png" y="45.64"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.589,75.6429) scale(1,1) translate(0,0)" writing-mode="lr" x="201.59" xml:space="preserve" y="79.14" zvalue="10493"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,203.298,75.3332) scale(1,1) translate(9.43293e-15,0)" writing-mode="lr" x="203.3" xml:space="preserve" y="84.33" zvalue="10494">110kV闽安南硅厂</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="16" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" width="72.88" x="71.94" y="327" zvalue="10496"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" writing-mode="lr" x="108.38" xml:space="preserve" y="343.5" zvalue="10496">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,664.076,506.091) scale(1,1) translate(0,0)" writing-mode="lr" x="664.08" xml:space="preserve" y="510.59" zvalue="7577">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,994.98,473.52) scale(1,1) translate(0,0)" writing-mode="lr" x="994.98" xml:space="preserve" y="478.02" zvalue="8016">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1042.61,459.394) scale(1,1) translate(0,0)" writing-mode="lr" x="1042.61" xml:space="preserve" y="463.89" zvalue="8030">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="154" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1046.61,512.644) scale(1,1) translate(0,0)" writing-mode="lr" x="1046.61" xml:space="preserve" y="517.14" zvalue="8035">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,790.155,441.645) scale(1,1) translate(0,0)" writing-mode="lr" x="790.15" xml:space="preserve" y="446.14" zvalue="9968">161</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,733.526,310.63) scale(1,1) translate(0,0)" writing-mode="lr" x="733.53" xml:space="preserve" y="315.13" zvalue="9972">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,789.425,496.549) scale(1,1) translate(0,1.08036e-13)" writing-mode="lr" x="789.42" xml:space="preserve" y="501.05" zvalue="9975">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,734.276,398.065) scale(1,1) translate(0,0)" writing-mode="lr" x="734.28" xml:space="preserve" y="402.56" zvalue="10028">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,788.925,348.088) scale(1,1) translate(0,0)" writing-mode="lr" x="788.92" xml:space="preserve" y="352.59" zvalue="10032">6</text>
  <line fill="none" id="174" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="152.8704926140824" y2="152.8704926140824" zvalue="10076"/>
  <line fill="none" id="173" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="380" x2="380" y1="11" y2="1041" zvalue="10077"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="165" y2="165"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="165" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="191" y2="191"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="191" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="215.25" y2="215.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="215.25" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="238" y2="238"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="238" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="260.75" y2="260.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="283.5" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="260.75" y2="283.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="260.75" y2="283.5"/>
  <line fill="none" id="171" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="622.8704926140824" y2="622.8704926140824" zvalue="10079"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="445" y2="445"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="445" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="483.2823" y2="483.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="483.2823" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="507.9617" y2="507.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="532.6411000000001" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="507.9617" y2="532.6411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="532.6410999999999" y2="532.6410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="557.3205" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="532.6410999999999" y2="557.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="557.3206" y2="557.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="557.3206" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="109.7745" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="64" x2="64" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="168.5809" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="109.7745" x2="109.7745" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="227.3873" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="168.5809" x2="168.5809" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3873" x2="227.3873" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="286.1936000000001" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="227.3872" x2="227.3872" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="582" y2="582"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="345" y1="606.6794" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="286.1936000000001" x2="286.1936000000001" y1="582" y2="606.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="345" x2="345" y1="582" y2="606.6794"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="938" y2="938"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="938" y2="938"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="977.1632999999999" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="938" y2="977.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="977.16327" y2="977.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1005.08167" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="977.16327" y2="1005.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1005.0816" y2="1005.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1033" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1005.0816" y2="1033"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1005.0816" y2="1033"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,958) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="964" zvalue="10083">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="163" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,992) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="998" zvalue="10084">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237,992) scale(1,1) translate(0,0)" writing-mode="lr" x="237" xml:space="preserve" y="998" zvalue="10085">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1026" zvalue="10086">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,1020) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="1026" zvalue="10087">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" x="137.53125" xml:space="preserve" y="462.3993055555555" zvalue="10088">110kV母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="137.53125" xml:space="preserve" y="479.3993055555555" zvalue="10088">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.5,652.5) scale(1,1) translate(0,0)" writing-mode="lr" x="78.5" xml:space="preserve" y="657" zvalue="10090">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,212.399,338.071) scale(1,1) translate(0,0)" writing-mode="lr" x="212.4" xml:space="preserve" y="342.57" zvalue="10091">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,317.399,338.071) scale(1,1) translate(0,0)" writing-mode="lr" x="317.4" xml:space="preserve" y="342.57" zvalue="10092">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90,495.75) scale(1,1) translate(0,0)" writing-mode="lr" x="90" xml:space="preserve" y="500.25" zvalue="10095">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90,521.25) scale(1,1) translate(0,0)" writing-mode="lr" x="90" xml:space="preserve" y="525.75" zvalue="10096">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90,544.25) scale(1,1) translate(0,0)" writing-mode="lr" x="90" xml:space="preserve" y="548.75" zvalue="10097">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90,567.25) scale(1,1) translate(0,0)" writing-mode="lr" x="90" xml:space="preserve" y="571.75" zvalue="10098">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,90,594.25) scale(1,1) translate(0,0)" writing-mode="lr" x="90" xml:space="preserve" y="598.75" zvalue="10099">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.054,960) scale(1,1) translate(0,0)" writing-mode="lr" x="237.05" xml:space="preserve" y="966" zvalue="10100">MinAnNan-01-2011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52,179) scale(1,1) translate(0,0)" writing-mode="lr" x="52" xml:space="preserve" y="183.5" zvalue="10103">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232,179) scale(1,1) translate(0,0)" writing-mode="lr" x="232" xml:space="preserve" y="183.5" zvalue="10104">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,203.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="207.75" zvalue="10105">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,764.545,221.097) scale(1,1) translate(1.51777e-13,0)" writing-mode="lr" x="764.54" xml:space="preserve" y="225.6" zvalue="10140">110kV卡典T线闽安南支线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,740.196,490.545) scale(1,1) translate(0,0)" writing-mode="lr" x="740.2" xml:space="preserve" y="495.05" zvalue="10259">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,823.155,611.821) scale(1,1) translate(0,0)" writing-mode="lr" x="823.15" xml:space="preserve" y="616.3200000000001" zvalue="10435">103</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,814.425,560.917) scale(1,1) translate(0,0)" writing-mode="lr" x="814.42" xml:space="preserve" y="565.42" zvalue="10442">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,748.196,578.92) scale(1,1) translate(0,0)" writing-mode="lr" x="748.2" xml:space="preserve" y="583.42" zvalue="10458">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,795.518,969.761) scale(1,1) translate(0,0)" writing-mode="lr" x="795.5175053526352" xml:space="preserve" y="974.2611111111112" zvalue="10504">动力变4MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,693,803) scale(1,1) translate(0,0)" writing-mode="lr" x="693" xml:space="preserve" y="807.5" zvalue="10506">1030</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" x="1194.6328125" xml:space="preserve" y="893.25" zvalue="10510">#1电容补偿装置</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1194.6328125" xml:space="preserve" y="909.25" zvalue="10510">7.2MVar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,743.418,641.16) scale(1,1) translate(0,0)" writing-mode="lr" x="743.42" xml:space="preserve" y="645.66" zvalue="10540">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,816.388,664.498) scale(1,1) translate(0,0)" writing-mode="lr" x="816.39" xml:space="preserve" y="669" zvalue="10545">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,744.529,695.382) scale(1,1) translate(0,0)" writing-mode="lr" x="744.53" xml:space="preserve" y="699.88" zvalue="10549">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1112.04,611.821) scale(1,1) translate(0,0)" writing-mode="lr" x="1112.04" xml:space="preserve" y="616.3200000000001" zvalue="10588">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1103.31,560.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1103.31" xml:space="preserve" y="565.42" zvalue="10590">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1037.08,578.92) scale(1,1) translate(0,0)" writing-mode="lr" x="1037.08" xml:space="preserve" y="583.42" zvalue="10594">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" x="1175" xml:space="preserve" y="739.4216856060606" zvalue="10597">#1炉变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1175" xml:space="preserve" y="755.4216856060606" zvalue="10597">12MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,981.889,803) scale(1,1) translate(-2.14581e-13,0)" writing-mode="lr" x="981.89" xml:space="preserve" y="807.5" zvalue="10598">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1032.31,641.16) scale(1,1) translate(0,0)" writing-mode="lr" x="1032.31" xml:space="preserve" y="645.66" zvalue="10602">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1105.28,664.498) scale(1,1) translate(0,0)" writing-mode="lr" x="1105.28" xml:space="preserve" y="669" zvalue="10606">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1033.42,695.382) scale(1,1) translate(0,0)" writing-mode="lr" x="1033.42" xml:space="preserve" y="699.88" zvalue="10611">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="135" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1083.78,933) scale(1,1) translate(0,0)" writing-mode="lr" x="1083.78" xml:space="preserve" y="937.5" zvalue="10615">#1硅冶炼炉</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="177" stroke="rgb(255,255,255)" text-anchor="middle" x="1494.6328125" xml:space="preserve" y="893.25" zvalue="10621">#2电容补偿装置</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="177" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1494.6328125" xml:space="preserve" y="909.25" zvalue="10621">7.2MVar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1412.04,611.821) scale(1,1) translate(0,0)" writing-mode="lr" x="1412.04" xml:space="preserve" y="616.3200000000001" zvalue="10623">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1403.31,560.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1403.31" xml:space="preserve" y="565.42" zvalue="10625">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="166" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1337.08,578.92) scale(1,1) translate(0,0)" writing-mode="lr" x="1337.08" xml:space="preserve" y="583.42" zvalue="10629">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" x="1473.890625" xml:space="preserve" y="739.4216856060606" zvalue="10632">#2炉变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="164" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1473.890625" xml:space="preserve" y="755.4216856060606" zvalue="10632">12MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1281.89,803) scale(1,1) translate(0,0)" writing-mode="lr" x="1281.89" xml:space="preserve" y="807.5" zvalue="10634">1020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1332.31,641.16) scale(1,1) translate(0,0)" writing-mode="lr" x="1332.31" xml:space="preserve" y="645.66" zvalue="10638">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1405.28,664.498) scale(1,1) translate(0,0)" writing-mode="lr" x="1405.28" xml:space="preserve" y="669" zvalue="10642">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1333.42,695.382) scale(1,1) translate(0,0)" writing-mode="lr" x="1333.42" xml:space="preserve" y="699.88" zvalue="10647">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1383.78,933) scale(1,1) translate(0,0)" writing-mode="lr" x="1383.78" xml:space="preserve" y="937.5" zvalue="10652">#2硅冶炼炉</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" x="1388.59375" xml:space="preserve" y="393.6458308961656" zvalue="10658">备用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1388.59375" xml:space="preserve" y="409.6458308961656" zvalue="10658">0.25MVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1388.22,212.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1388.22" xml:space="preserve" y="216.94" zvalue="10659">T接勐典一级至盆都村10kV线路</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="71.94" y="327" zvalue="10496"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="48">
   <path class="kv110" d="M 634 525.09 L 1537.11 525.09" stroke-width="6" zvalue="7576"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674249801732" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674249801732"/></metadata>
  <path d="M 634 525.09 L 1537.11 525.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="1453">
   <use class="kv110" height="30" transform="rotate(180,1014.01,472.828) scale(0.947693,-0.6712) translate(55.5751,-1182.21)" width="15" x="1006.902401042228" xlink:href="#Disconnector:刀闸_0" y="462.7597602301333" zvalue="8015"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449920106501" ObjectName="110kV母线PT1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449920106501"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1014.01,472.828) scale(0.947693,-0.6712) translate(55.5751,-1182.21)" width="15" x="1006.902401042228" y="462.7597602301333"/></g>
  <g id="96">
   <use class="kv110" height="30" transform="rotate(180,764.392,495.857) scale(0.947693,-0.6712) translate(41.7976,-1239.55)" width="15" x="757.2841334259955" xlink:href="#Disconnector:刀闸_0" y="485.7885305077181" zvalue="9974"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449919385605" ObjectName="110kV卡典T线闽安南支线1611隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449919385605"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,764.392,495.857) scale(0.947693,-0.6712) translate(41.7976,-1239.55)" width="15" x="757.2841334259955" y="485.7885305077181"/></g>
  <g id="32">
   <use class="kv110" height="30" transform="rotate(0,764.211,354.088) scale(-0.947693,0.6712) translate(-1570.99,168.524)" width="15" x="757.1034928921569" xlink:href="#Disconnector:刀闸_0" y="344.0197927208583" zvalue="10031"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449919713285" ObjectName="110kV卡典T线闽安南支线1616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449919713285"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,764.211,354.088) scale(-0.947693,0.6712) translate(-1570.99,168.524)" width="15" x="757.1034928921569" y="344.0197927208583"/></g>
  <g id="53">
   <use class="kv110" height="30" transform="rotate(180,795.392,556.609) scale(0.947693,0.6712) translate(43.5087,267.733)" width="15" x="788.2841334259955" xlink:href="#Disconnector:刀闸_0" y="546.5413658803764" zvalue="10441"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449920499718" ObjectName="动力变110kV侧1031隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449920499718"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,795.392,556.609) scale(0.947693,0.6712) translate(43.5087,267.733)" width="15" x="788.2841334259955" y="546.5413658803764"/></g>
  <g id="13">
   <use class="kv110" height="30" transform="rotate(180,795.392,665.498) scale(0.947693,0.6712) translate(43.5087,321.074)" width="15" x="788.2841334259955" xlink:href="#Disconnector:刀闸_0" y="655.4302547692652" zvalue="10544"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449921024006" ObjectName="动力变110kV侧1036隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449921024006"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,795.392,665.498) scale(0.947693,0.6712) translate(43.5087,321.074)" width="15" x="788.2841334259955" y="655.4302547692652"/></g>
  <g id="125">
   <use class="kv110" height="30" transform="rotate(180,1084.28,556.609) scale(0.947693,0.6712) translate(59.4536,267.733)" width="15" x="1077.173022314884" xlink:href="#Disconnector:刀闸_0" y="546.5413658803764" zvalue="10589"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449921875974" ObjectName="#1炉变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449921875974"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1084.28,556.609) scale(0.947693,0.6712) translate(59.4536,267.733)" width="15" x="1077.173022314884" y="546.5413658803764"/></g>
  <g id="75">
   <use class="kv110" height="30" transform="rotate(180,1084.28,665.498) scale(0.947693,0.6712) translate(59.4536,321.074)" width="15" x="1077.173022314884" xlink:href="#Disconnector:刀闸_0" y="655.4302547692652" zvalue="10605"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449921351686" ObjectName="#1炉变110kV侧1016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449921351686"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1084.28,665.498) scale(0.947693,0.6712) translate(59.4536,321.074)" width="15" x="1077.173022314884" y="655.4302547692652"/></g>
  <g id="214">
   <use class="kv110" height="30" transform="rotate(180,1384.28,556.609) scale(0.947693,0.6712) translate(76.0119,267.733)" width="15" x="1377.173022314884" xlink:href="#Disconnector:刀闸_0" y="546.5413658803764" zvalue="10624"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449922859014" ObjectName="#2炉变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449922859014"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1384.28,556.609) scale(0.947693,0.6712) translate(76.0119,267.733)" width="15" x="1377.173022314884" y="546.5413658803764"/></g>
  <g id="202">
   <use class="kv110" height="30" transform="rotate(180,1384.28,665.498) scale(0.947693,0.6712) translate(76.0119,321.074)" width="15" x="1377.173022314884" xlink:href="#Disconnector:刀闸_0" y="655.4302547692652" zvalue="10641"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449922334726" ObjectName="#2炉变110kV侧1026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449922334726"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1384.28,665.498) scale(0.947693,0.6712) translate(76.0119,321.074)" width="15" x="1377.173022314884" y="655.4302547692652"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="150">
   <use class="kv110" height="20" transform="rotate(90,1040.37,445.393) scale(1.24619,-1.0068) translate(-204.297,-887.709)" width="10" x="1034.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="435.3248086033777" zvalue="8029"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449920040965" ObjectName="110kV母线PT19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449920040965"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1040.37,445.393) scale(1.24619,-1.0068) translate(-204.297,-887.709)" width="10" x="1034.143242399862" y="435.3248086033777"/></g>
  <g id="170">
   <use class="kv110" height="20" transform="rotate(90,1044.37,498.643) scale(1.24619,-1.0068) translate(-205.087,-993.849)" width="10" x="1038.143242399862" xlink:href="#GroundDisconnector:地刀_0" y="488.5748086033776" zvalue="8034"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449919909893" ObjectName="110kV母线PT19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449919909893"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1044.37,498.643) scale(1.24619,-1.0068) translate(-205.087,-993.849)" width="10" x="1038.143242399862" y="488.5748086033776"/></g>
  <g id="98">
   <use class="kv110" height="20" transform="rotate(270,734.196,329.315) scale(-1.24619,-1.0068) translate(-1322.12,-656.337)" width="10" x="727.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="319.246507813326" zvalue="9971"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449919516677" ObjectName="110kV卡典T线闽安南支线16167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449919516677"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,734.196,329.315) scale(-1.24619,-1.0068) translate(-1322.12,-656.337)" width="10" x="727.9650768138584" y="319.246507813326"/></g>
  <g id="29">
   <use class="kv110" height="20" transform="rotate(270,734.196,381.315) scale(-1.24619,-1.0068) translate(-1322.12,-759.985)" width="10" x="727.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="371.246507813326" zvalue="10027"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449919647749" ObjectName="110kV卡典T线闽安南支线16160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449919647749"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,734.196,381.315) scale(-1.24619,-1.0068) translate(-1322.12,-759.985)" width="10" x="727.9650768138584" y="371.246507813326"/></g>
  <g id="114">
   <use class="kv110" height="20" transform="rotate(270,740.196,473.315) scale(-1.24619,-1.0068) translate(-1332.93,-943.364)" width="10" x="733.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="463.246507813326" zvalue="10258"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449920303109" ObjectName="110kV卡典T线闽安南支线16117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449920303109"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,740.196,473.315) scale(-1.24619,-1.0068) translate(-1332.93,-943.364)" width="10" x="733.9650768138584" y="463.246507813326"/></g>
  <g id="28">
   <use class="kv110" height="20" transform="rotate(90,771.196,579.151) scale(-1.24619,1.0068) translate(-1388.81,-3.84399)" width="10" x="764.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="569.0833885747684" zvalue="10456"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449920434182" ObjectName="动力变110kV侧10317接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449920434182"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,771.196,579.151) scale(-1.24619,1.0068) translate(-1388.81,-3.84399)" width="10" x="764.9650768138584" y="569.0833885747684"/></g>
  <g id="104">
   <use class="kv110" height="40" transform="rotate(0,729,804) scale(1,-1) translate(0,-1608)" width="40" x="709" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="784" zvalue="10505"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449920630789" ObjectName="动力变110kV侧中性点1030接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449920630789"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,729,804) scale(1,-1) translate(0,-1608)" width="40" x="709" y="784"/></g>
  <g id="5">
   <use class="kv110" height="20" transform="rotate(90,771.196,643.151) scale(-1.24619,1.0068) translate(-1388.81,-4.27629)" width="10" x="764.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="633.0833885747684" zvalue="10539"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449920958469" ObjectName="动力变110kV侧10360接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449920958469"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,771.196,643.151) scale(-1.24619,1.0068) translate(-1388.81,-4.27629)" width="10" x="764.9650768138584" y="633.0833885747684"/></g>
  <g id="22">
   <use class="kv110" height="20" transform="rotate(90,771.196,695.151) scale(-1.24619,1.0068) translate(-1388.81,-4.62753)" width="10" x="764.9650768138584" xlink:href="#GroundDisconnector:地刀_0" y="685.0833885747684" zvalue="10548"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449921155078" ObjectName="动力变110kV侧10367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449921155078"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,771.196,695.151) scale(-1.24619,1.0068) translate(-1388.81,-4.62753)" width="10" x="764.9650768138584" y="685.0833885747684"/></g>
  <g id="123">
   <use class="kv110" height="20" transform="rotate(90,1060.08,579.151) scale(-1.24619,1.0068) translate(-1909.52,-3.84399)" width="10" x="1053.853965702747" xlink:href="#GroundDisconnector:地刀_0" y="569.0833885747684" zvalue="10592"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449921810438" ObjectName="#1炉变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449921810438"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1060.08,579.151) scale(-1.24619,1.0068) translate(-1909.52,-3.84399)" width="10" x="1053.853965702747" y="569.0833885747684"/></g>
  <g id="116">
   <use class="kv110" height="40" transform="rotate(0,1017.89,804) scale(1,-1) translate(0,-1608)" width="40" x="997.8888888888889" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="784" zvalue="10597"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449921679366" ObjectName="#1炉变110kV侧中性点1010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449921679366"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1017.89,804) scale(1,-1) translate(0,-1608)" width="40" x="997.8888888888889" y="784"/></g>
  <g id="112">
   <use class="kv110" height="20" transform="rotate(90,1060.08,643.151) scale(-1.24619,1.0068) translate(-1909.52,-4.27629)" width="10" x="1053.853965702747" xlink:href="#GroundDisconnector:地刀_0" y="633.0833885747684" zvalue="10601"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449921482758" ObjectName="#1炉变110kV侧10160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449921482758"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1060.08,643.151) scale(-1.24619,1.0068) translate(-1909.52,-4.27629)" width="10" x="1053.853965702747" y="633.0833885747684"/></g>
  <g id="62">
   <use class="kv110" height="20" transform="rotate(90,1060.08,695.151) scale(-1.24619,1.0068) translate(-1909.52,-4.62753)" width="10" x="1053.853965702747" xlink:href="#GroundDisconnector:地刀_0" y="685.0833885747684" zvalue="10609"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449921286150" ObjectName="#1炉变110kV侧10167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449921286150"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1060.08,695.151) scale(-1.24619,1.0068) translate(-1909.52,-4.62753)" width="10" x="1053.853965702747" y="685.0833885747684"/></g>
  <g id="212">
   <use class="kv110" height="20" transform="rotate(90,1360.08,579.151) scale(-1.24619,1.0068) translate(-2450.25,-3.84399)" width="10" x="1353.853965702747" xlink:href="#GroundDisconnector:地刀_0" y="569.0833885747684" zvalue="10627"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449922793478" ObjectName="#2炉变110kV侧10217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449922793478"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1360.08,579.151) scale(-1.24619,1.0068) translate(-2450.25,-3.84399)" width="10" x="1353.853965702747" y="569.0833885747684"/></g>
  <g id="208">
   <use class="kv110" height="40" transform="rotate(0,1317.89,804) scale(1,-1) translate(0,-1608)" width="40" x="1297.888888888889" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="784" zvalue="10633"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449922662406" ObjectName="#2炉变110kV侧中性点1020接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449922662406"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1317.89,804) scale(1,-1) translate(0,-1608)" width="40" x="1297.888888888889" y="784"/></g>
  <g id="205">
   <use class="kv110" height="20" transform="rotate(90,1360.08,643.151) scale(-1.24619,1.0068) translate(-2450.25,-4.27629)" width="10" x="1353.853965702747" xlink:href="#GroundDisconnector:地刀_0" y="633.0833885747684" zvalue="10637"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449922465798" ObjectName="#2炉变110kV侧10260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449922465798"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1360.08,643.151) scale(-1.24619,1.0068) translate(-2450.25,-4.27629)" width="10" x="1353.853965702747" y="633.0833885747684"/></g>
  <g id="199">
   <use class="kv110" height="20" transform="rotate(90,1360.08,695.151) scale(-1.24619,1.0068) translate(-2450.25,-4.62753)" width="10" x="1353.853965702747" xlink:href="#GroundDisconnector:地刀_0" y="685.0833885747684" zvalue="10645"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449922269190" ObjectName="#2炉变110kV侧10267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449922269190"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1360.08,695.151) scale(-1.24619,1.0068) translate(-2450.25,-4.62753)" width="10" x="1353.853965702747" y="685.0833885747684"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="151">
   <path class="kv110" d="M 1013.95 482.72 L 1013.95 525.09" stroke-width="1" zvalue="8031"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@1" LinkObjectIDznd="48@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1013.95 482.72 L 1013.95 525.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv110" d="M 1013.93 463.09 L 1013.93 420.57" stroke-width="1" zvalue="8038"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1453@0" LinkObjectIDznd="218@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1013.93 463.09 L 1013.93 420.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv110" d="M 764.31 454.95 L 764.31 486.12" stroke-width="1" zvalue="9978"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="100@1" LinkObjectIDznd="96@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 764.31 454.95 L 764.31 486.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv110" d="M 764.33 505.75 L 764.33 525.09" stroke-width="1" zvalue="9981"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@1" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 764.33 505.75 L 764.33 525.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv110" d="M 764.13 289.39 L 764.13 344.35" stroke-width="1" zvalue="10040"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="32@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 764.13 289.39 L 764.13 344.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv110" d="M 744.01 381.38 L 764.15 381.38" stroke-width="1" zvalue="10141"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="29@0" LinkObjectIDznd="73" MaxPinNum="2"/>
   </metadata>
  <path d="M 744.01 381.38 L 764.15 381.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="kv110" d="M 744.01 329.38 L 764.13 329.38" stroke-width="1" zvalue="10255"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="98@0" LinkObjectIDznd="41" MaxPinNum="2"/>
   </metadata>
  <path d="M 744.01 329.38 L 764.13 329.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv110" d="M 750.01 473.38 L 764.31 473.38" stroke-width="1" zvalue="10259"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="93" MaxPinNum="2"/>
   </metadata>
  <path d="M 750.01 473.38 L 764.31 473.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="52">
   <path class="kv110" d="M 795.31 597.51 L 795.31 566.34" stroke-width="1" zvalue="10443"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@1" LinkObjectIDznd="53@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 795.31 597.51 L 795.31 566.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv110" d="M 781.01 579.09 L 795.31 579.09" stroke-width="1" zvalue="10457"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="52" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.01 579.09 L 795.31 579.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv110" d="M 795.33 546.71 L 795.33 525.09" stroke-width="1" zvalue="10459"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 795.33 546.71 L 795.33 525.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv110" d="M 764.15 363.98 L 764.15 429.06" stroke-width="1" zvalue="10463"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@1" LinkObjectIDznd="100@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 764.15 363.98 L 764.15 429.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="2">
   <path class="kv110" d="M 1034.56 498.71 L 1013.95 498.71" stroke-width="1" zvalue="10466"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="170@0" LinkObjectIDznd="151" MaxPinNum="2"/>
   </metadata>
  <path d="M 1034.56 498.71 L 1013.95 498.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="6">
   <path class="kv110" d="M 1030.56 445.46 L 1013.93 445.46" stroke-width="1" zvalue="10467"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="179" MaxPinNum="2"/>
   </metadata>
  <path d="M 1030.56 445.46 L 1013.93 445.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv10" d="M 795.13 925.05 L 795.13 831.62" stroke-width="1" zvalue="10508"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="97@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 795.13 925.05 L 795.13 831.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv110" d="M 824.63 720.97 L 795.15 720.97" stroke-width="1" zvalue="10534"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@0" LinkObjectIDznd="17" MaxPinNum="2"/>
   </metadata>
  <path d="M 824.63 720.97 L 795.15 720.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="kv110" d="M 731.6 791.8 L 730.89 759.33 L 795.13 759.8" stroke-width="1" zvalue="10541"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="97@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 731.6 791.8 L 730.89 759.33 L 795.13 759.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="10">
   <path class="kv110" d="M 781.01 643.09 L 795.33 643.09" stroke-width="1" zvalue="10542"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="5@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.01 643.09 L 795.33 643.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="kv110" d="M 795.15 737.56 L 795.15 675.23" stroke-width="1" zvalue="10545"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="13@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 795.15 737.56 L 795.15 675.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv110" d="M 795.33 655.6 L 795.33 623.4" stroke-width="1" zvalue="10546"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="13@1" LinkObjectIDznd="61@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 795.33 655.6 L 795.33 623.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv110" d="M 781.01 695.09 L 795.15 695.09" stroke-width="1" zvalue="10549"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@0" LinkObjectIDznd="17" MaxPinNum="2"/>
   </metadata>
  <path d="M 781.01 695.09 L 795.15 695.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv110" d="M 1084.2 597.51 L 1084.2 566.34" stroke-width="1" zvalue="10591"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@1" LinkObjectIDznd="125@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1084.2 597.51 L 1084.2 566.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv110" d="M 1069.9 579.09 L 1084.2 579.09" stroke-width="1" zvalue="10593"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@0" LinkObjectIDznd="124" MaxPinNum="2"/>
   </metadata>
  <path d="M 1069.9 579.09 L 1084.2 579.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="120">
   <path class="kv110" d="M 1084.22 546.71 L 1084.22 525.09" stroke-width="1" zvalue="10595"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@1" LinkObjectIDznd="48@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1084.22 546.71 L 1084.22 525.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv110" d="M 1113.52 720.97 L 1085.43 720.97" stroke-width="1" zvalue="10600"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="71" MaxPinNum="2"/>
   </metadata>
  <path d="M 1113.52 720.97 L 1085.43 720.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="kv110" d="M 1020.49 791.8 L 1020.49 761 L 1086.1 761.16" stroke-width="1" zvalue="10603"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@0" LinkObjectIDznd="117@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1020.49 791.8 L 1020.49 761 L 1086.1 761.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv110" d="M 1069.9 643.09 L 1084.22 643.09" stroke-width="1" zvalue="10604"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="69" MaxPinNum="2"/>
   </metadata>
  <path d="M 1069.9 643.09 L 1084.22 643.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="kv110" d="M 1085.43 733.07 L 1085.43 675.23" stroke-width="1" zvalue="10607"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@0" LinkObjectIDznd="75@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1085.43 733.07 L 1085.43 675.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="kv110" d="M 1084.22 655.6 L 1084.22 623.4" stroke-width="1" zvalue="10608"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75@1" LinkObjectIDznd="128@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1084.22 655.6 L 1084.22 623.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv110" d="M 1069.9 695.09 L 1085.43 695.09" stroke-width="1" zvalue="10610"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="71" MaxPinNum="2"/>
   </metadata>
  <path d="M 1069.9 695.09 L 1085.43 695.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv10" d="M 1084.89 896.28 L 1084.89 833.31" stroke-width="1" zvalue="10615"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@0" LinkObjectIDznd="117@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1084.89 896.28 L 1084.89 833.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="kv35" d="M 1155 783.19 L 1194.28 783.19 L 1194.28 834.21" stroke-width="1" zvalue="10616"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@2" LinkObjectIDznd="110@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1155 783.19 L 1194.28 783.19 L 1194.28 834.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv35" d="M 1193.78 808.22 L 1194.28 808.22" stroke-width="1" zvalue="10618"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="143@0" LinkObjectIDznd="141" MaxPinNum="2"/>
   </metadata>
  <path d="M 1193.78 808.22 L 1194.28 808.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv110" d="M 1384.2 597.51 L 1384.2 566.34" stroke-width="1" zvalue="10626"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@1" LinkObjectIDznd="214@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1384.2 597.51 L 1384.2 566.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="211">
   <path class="kv110" d="M 1369.9 579.09 L 1384.2 579.09" stroke-width="1" zvalue="10628"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@0" LinkObjectIDznd="213" MaxPinNum="2"/>
   </metadata>
  <path d="M 1369.9 579.09 L 1384.2 579.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="210">
   <path class="kv110" d="M 1384.22 546.71 L 1384.22 525.09" stroke-width="1" zvalue="10630"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@1" LinkObjectIDznd="48@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1384.22 546.71 L 1384.22 525.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="206">
   <path class="kv110" d="M 1413.52 720.97 L 1384.32 720.97" stroke-width="1" zvalue="10636"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="201" MaxPinNum="2"/>
   </metadata>
  <path d="M 1413.52 720.97 L 1384.32 720.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="kv110" d="M 1320.49 791.8 L 1320.49 761.56 L 1384.99 761.16" stroke-width="1" zvalue="10639"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@0" LinkObjectIDznd="209@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1320.49 791.8 L 1320.49 761.56 L 1384.99 761.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="203">
   <path class="kv110" d="M 1369.9 643.09 L 1384.22 643.09" stroke-width="1" zvalue="10640"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="200" MaxPinNum="2"/>
   </metadata>
  <path d="M 1369.9 643.09 L 1384.22 643.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv110" d="M 1384.32 733.07 L 1384.32 675.23" stroke-width="1" zvalue="10643"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="209@0" LinkObjectIDznd="202@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1384.32 733.07 L 1384.32 675.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="200">
   <path class="kv110" d="M 1384.22 655.6 L 1384.22 623.4" stroke-width="1" zvalue="10644"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@1" LinkObjectIDznd="215@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1384.22 655.6 L 1384.22 623.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="kv110" d="M 1369.9 695.09 L 1384.32 695.09" stroke-width="1" zvalue="10646"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@0" LinkObjectIDznd="201" MaxPinNum="2"/>
   </metadata>
  <path d="M 1369.9 695.09 L 1384.32 695.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="187">
   <path class="kv10" d="M 1384.89 896.28 L 1384.82 833.14" stroke-width="1" zvalue="10651"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="188@0" LinkObjectIDznd="209@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1384.89 896.28 L 1384.82 833.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="181">
   <path class="kv35" d="M 1453.89 783.19 L 1494.28 783.19 L 1494.28 834.21" stroke-width="1" zvalue="10653"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="209@2" LinkObjectIDznd="216@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1453.89 783.19 L 1494.28 783.19 L 1494.28 834.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv35" d="M 1493.78 808.22 L 1494.28 808.22" stroke-width="1" zvalue="10655"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="180@0" LinkObjectIDznd="181" MaxPinNum="2"/>
   </metadata>
  <path d="M 1493.78 808.22 L 1494.28 808.22" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv35" d="M 1453.89 787.19 L 1453.89 783.19" stroke-width="1" zvalue="10656"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="" LinkObjectIDznd="181" MaxPinNum="2"/>
   </metadata>
  <path d="M 1453.89 787.19 L 1453.89 783.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv10" d="M 1388.22 312.53 L 1388.22 275.61" stroke-width="1" zvalue="10659"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="31@0" LinkObjectIDznd="34@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1388.22 312.53 L 1388.22 275.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="218">
   <use class="kv110" height="35" transform="rotate(0,1011.95,402.341) scale(0.9375,1.07143) translate(66.2133,-25.5727)" width="40" x="993.199671629758" xlink:href="#Accessory:5绕组母线PT带避雷器_0" y="383.5909090909091" zvalue="9702"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449919778821" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1011.95,402.341) scale(0.9375,1.07143) translate(66.2133,-25.5727)" width="40" x="993.199671629758" y="383.5909090909091"/></g>
  <g id="184">
   <use class="kv110" height="26" transform="rotate(270,837,721) scale(1,1) translate(0,0)" width="12" x="831" xlink:href="#Accessory:避雷器1_0" y="708" zvalue="10533"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449920827397" ObjectName="动力变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,837,721) scale(1,1) translate(0,0)" width="12" x="831" y="708"/></g>
  <g id="115">
   <use class="kv110" height="26" transform="rotate(270,1125.89,721) scale(1,1) translate(0,0)" width="12" x="1119.888888888889" xlink:href="#Accessory:避雷器1_0" y="708" zvalue="10599"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449921548293" ObjectName="#1炉变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1125.89,721) scale(1,1) translate(0,0)" width="12" x="1119.888888888889" y="708"/></g>
  <g id="143">
   <use class="kv35" height="20" transform="rotate(0,1193.78,808.222) scale(1.11111,1.11111) translate(-118.822,-79.7111)" width="10" x="1188.222222222222" xlink:href="#Accessory:熔断器12_0" y="797.1111111111111" zvalue="10617"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449922007046" ObjectName="#1电容熔断器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1193.78,808.222) scale(1.11111,1.11111) translate(-118.822,-79.7111)" width="10" x="1188.222222222222" y="797.1111111111111"/></g>
  <g id="207">
   <use class="kv110" height="26" transform="rotate(270,1425.89,721) scale(1,1) translate(0,0)" width="12" x="1419.888888888889" xlink:href="#Accessory:避雷器1_0" y="708" zvalue="10635"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449922531334" ObjectName="#2炉变避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1425.89,721) scale(1,1) translate(0,0)" width="12" x="1419.888888888889" y="708"/></g>
  <g id="180">
   <use class="kv35" height="20" transform="rotate(0,1493.78,808.222) scale(1.11111,1.11111) translate(-148.822,-79.7111)" width="10" x="1488.222222222222" xlink:href="#Accessory:熔断器12_0" y="797.1111111111111" zvalue="10654"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449922072582" ObjectName="#2电容熔断器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1493.78,808.222) scale(1.11111,1.11111) translate(-148.822,-79.7111)" width="10" x="1488.222222222222" y="797.1111111111111"/></g>
 </g>
 <g id="BreakerClass">
  <g id="100">
   <use class="kv110" height="20" transform="rotate(0,764.205,442.02) scale(1.5542,1.35421) translate(-269.731,-112.074)" width="10" x="756.4340345345652" xlink:href="#Breaker:开关_0" y="428.477975852458" zvalue="9966"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924539580421" ObjectName="110kV卡典T线闽安南支线161断路器"/>
   <cge:TPSR_Ref TObjectID="6473924539580421"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,764.205,442.02) scale(1.5542,1.35421) translate(-269.731,-112.074)" width="10" x="756.4340345345652" y="428.477975852458"/></g>
  <g id="61">
   <use class="kv110" height="20" transform="rotate(0,795.205,610.446) scale(1.5542,-1.35421) translate(-280.785,-1057.68)" width="10" x="787.4340345345652" xlink:href="#Breaker:开关_0" y="596.9037238148987" zvalue="10434"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924539645957" ObjectName="动力变110kV侧103断路器"/>
   <cge:TPSR_Ref TObjectID="6473924539645957"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,795.205,610.446) scale(1.5542,-1.35421) translate(-280.785,-1057.68)" width="10" x="787.4340345345652" y="596.9037238148987"/></g>
  <g id="128">
   <use class="kv110" height="20" transform="rotate(0,1084.09,610.446) scale(1.5542,-1.35421) translate(-383.797,-1057.68)" width="10" x="1076.322923423454" xlink:href="#Breaker:开关_0" y="596.9037238148987" zvalue="10587"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924539711493" ObjectName="#1炉变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473924539711493"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1084.09,610.446) scale(1.5542,-1.35421) translate(-383.797,-1057.68)" width="10" x="1076.322923423454" y="596.9037238148987"/></g>
  <g id="215">
   <use class="kv110" height="20" transform="rotate(0,1384.09,610.446) scale(1.5542,-1.35421) translate(-490.772,-1057.68)" width="10" x="1376.322923423454" xlink:href="#Breaker:开关_0" y="596.9037238148987" zvalue="10622"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924539777029" ObjectName="#2炉变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473924539777029"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1384.09,610.446) scale(1.5542,-1.35421) translate(-490.772,-1057.68)" width="10" x="1376.322923423454" y="596.9037238148987"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="68" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,521.25) scale(1,1) translate(1.24653e-14,0)" writing-mode="lr" x="137.75" xml:space="preserve" y="526.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126041747462" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="67">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,544.25) scale(1,1) translate(1.24653e-14,5.96467e-14)" writing-mode="lr" x="137.75" xml:space="preserve" y="549.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126041812998" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,567.25) scale(1,1) translate(1.24653e-14,-1.244e-13)" writing-mode="lr" x="137.75" xml:space="preserve" y="572.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126041878534" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,137.639,495.75) scale(1,1) translate(1.24653e-14,-1.08524e-13)" writing-mode="lr" x="137.75" xml:space="preserve" y="500.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126042009606" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,142,203.111) scale(1,1) translate(0,0)" writing-mode="lr" x="142.15" xml:space="preserve" y="209.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126042140678" ObjectName="F"/>
   </metadata>
  </g>
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,764.628,149.139) scale(1,1) translate(0,0)" writing-mode="lr" x="764.8" xml:space="preserve" y="154.01" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126042796038" ObjectName="P"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="18" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,764.628,170.139) scale(1,1) translate(0,0)" writing-mode="lr" x="764.8" xml:space="preserve" y="175.01" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126042861574" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,764.628,191.139) scale(1,1) translate(0,0)" writing-mode="lr" x="764.8" xml:space="preserve" y="196.01" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126042927110" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="39" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,610.129,734.4) scale(1,1) translate(0,0)" writing-mode="lr" x="609.58" xml:space="preserve" y="740.6799999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126044368902" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="40">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="40" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,610.129,771.4) scale(1,1) translate(0,0)" writing-mode="lr" x="609.58" xml:space="preserve" y="777.6799999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126044434438" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="50">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="50" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,610.129,808.4) scale(1,1) translate(0,0)" writing-mode="lr" x="609.58" xml:space="preserve" y="814.6799999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126044631046" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="51">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="51" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,953.953,683.067) scale(1,1) translate(0,0)" writing-mode="lr" x="953.48" xml:space="preserve" y="687.84" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126048038918" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="59">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="59" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,953.953,720.067) scale(1,1) translate(0,0)" writing-mode="lr" x="953.48" xml:space="preserve" y="724.84" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126048104454" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="79">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="79" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,952.953,756.067) scale(1,1) translate(0,0)" writing-mode="lr" x="952.48" xml:space="preserve" y="760.84" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126048169990" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="80" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1264.84,684.067) scale(1,1) translate(0,0)" writing-mode="lr" x="1264.37" xml:space="preserve" y="688.84" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126053019654" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="83">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="83" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1264.84,721.067) scale(1,1) translate(0,0)" writing-mode="lr" x="1264.37" xml:space="preserve" y="725.84" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126053085190" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="91" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1264.84,756.067) scale(1,1) translate(0,0)" writing-mode="lr" x="1264.37" xml:space="preserve" y="760.84" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126053150726" ObjectName="HIa"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="1056">
   <use height="30" transform="rotate(0,265.286,338.071) scale(0.708333,0.665547) translate(104.86,164.872)" width="30" x="254.66" xlink:href="#State:红绿圆(方形)_0" y="328.09" zvalue="10247"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562951408386052" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,265.286,338.071) scale(0.708333,0.665547) translate(104.86,164.872)" width="30" x="254.66" y="328.09"/></g>
  <g id="1057">
   <use height="30" transform="rotate(0,352.911,338.071) scale(0.708333,0.665547) translate(140.941,164.872)" width="30" x="342.29" xlink:href="#State:红绿圆(方形)_0" y="328.09" zvalue="10249"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374889910275" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,352.911,338.071) scale(0.708333,0.665547) translate(140.941,164.872)" width="30" x="342.29" y="328.09"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="97">
   <g id="970">
    <use class="kv110" height="30" transform="rotate(0,795.129,784.355) scale(3.36364,3.36364) translate(-532.739,-515.714)" width="22" x="758.13" xlink:href="#PowerTransformer2:接地可调两卷变_0" y="733.9" zvalue="10503"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874445750275" ObjectName="110"/>
    </metadata>
   </g>
   <g id="971">
    <use class="kv10" height="30" transform="rotate(0,795.129,784.355) scale(3.36364,3.36364) translate(-532.739,-515.714)" width="22" x="758.13" xlink:href="#PowerTransformer2:接地可调两卷变_1" y="733.9" zvalue="10503"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874445815811" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399455145987" ObjectName="动力变"/>
   <cge:TPSR_Ref TObjectID="6755399455145987"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,795.129,784.355) scale(3.36364,3.36364) translate(-532.739,-515.714)" width="22" x="758.13" y="733.9"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="107">
   <use class="kv10" height="30" transform="rotate(0,795.129,934.5) scale(1.98234,-0.7) translate(-388.128,-2274)" width="12" x="783.2345913622861" xlink:href="#EnergyConsumer:负荷_0" y="924" zvalue="10507"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449920696325" ObjectName="动力变负荷"/>
   <cge:TPSR_Ref TObjectID="6192449920696325"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,795.129,934.5) scale(1.98234,-0.7) translate(-388.128,-2274)" width="12" x="783.2345913622861" y="924"/></g>
  <g id="133">
   <use class="kv10" height="14" transform="rotate(0,1084.89,903.778) scale(1.11111,1.11111) translate(-106.989,-89.6)" width="27" x="1069.888888888889" xlink:href="#EnergyConsumer:电炉_0" y="896" zvalue="10614"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449921941509" ObjectName="#1硅冶炼炉"/>
   <cge:TPSR_Ref TObjectID="6192449921941509"/></metadata>
  <rect fill="white" height="14" opacity="0" stroke="white" transform="rotate(0,1084.89,903.778) scale(1.11111,1.11111) translate(-106.989,-89.6)" width="27" x="1069.888888888889" y="896"/></g>
  <g id="188">
   <use class="kv10" height="14" transform="rotate(0,1384.89,903.778) scale(1.11111,1.11111) translate(-136.989,-89.6)" width="27" x="1369.888888888889" xlink:href="#EnergyConsumer:电炉_0" y="896" zvalue="10650"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449922138118" ObjectName="#2硅冶炼炉"/>
   <cge:TPSR_Ref TObjectID="6192449922138118"/></metadata>
  <rect fill="white" height="14" opacity="0" stroke="white" transform="rotate(0,1384.89,903.778) scale(1.11111,1.11111) translate(-136.989,-89.6)" width="27" x="1369.888888888889" y="896"/></g>
  <g id="31">
   <use class="kv10" height="25" transform="rotate(0,1393.22,340.028) scale(2.5,2.5) translate(-820.933,-185.267)" width="20" x="1368.222222222222" xlink:href="#EnergyConsumer:站用变0716_0" y="308.7777777777777" zvalue="10657"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449922990086" ObjectName="备用变"/>
   <cge:TPSR_Ref TObjectID="6192449922990086"/></metadata>
  <rect fill="white" height="25" opacity="0" stroke="white" transform="rotate(0,1393.22,340.028) scale(2.5,2.5) translate(-820.933,-185.267)" width="20" x="1368.222222222222" y="308.7777777777777"/></g>
  <g id="34">
   <use class="kv10" height="30" transform="rotate(0,1388.22,257.111) scale(1.38889,1.37037) translate(-386.369,-63.9339)" width="12" x="1379.888888888889" xlink:href="#EnergyConsumer:负荷_0" y="236.5555555555555" zvalue="10658"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449923055622" ObjectName="T接勐典一级至盆都村10kV线路"/>
   <cge:TPSR_Ref TObjectID="6192449923055622"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1388.22,257.111) scale(1.38889,1.37037) translate(-386.369,-63.9339)" width="12" x="1379.888888888889" y="236.5555555555555"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="110">
   <use class="kv35" height="13" transform="rotate(0,1194,851.917) scale(2.83333,2.83333) translate(-761.588,-539.324)" width="12" x="1177" xlink:href="#Compensator:无功补偿20210816_0" y="833.5" zvalue="10509"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449920761861" ObjectName="#1电容补偿装置"/>
   <cge:TPSR_Ref TObjectID="6192449920761861"/></metadata>
  <rect fill="white" height="13" opacity="0" stroke="white" transform="rotate(0,1194,851.917) scale(2.83333,2.83333) translate(-761.588,-539.324)" width="12" x="1177" y="833.5"/></g>
  <g id="216">
   <use class="kv35" height="13" transform="rotate(0,1494,851.917) scale(2.83333,2.83333) translate(-955.706,-539.324)" width="12" x="1477" xlink:href="#Compensator:无功补偿20210816_0" y="833.5" zvalue="10620"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449922924550" ObjectName="#2电容补偿装置"/>
   <cge:TPSR_Ref TObjectID="6192449922924550"/></metadata>
  <rect fill="white" height="13" opacity="0" stroke="white" transform="rotate(0,1494,851.917) scale(2.83333,2.83333) translate(-955.706,-539.324)" width="12" x="1477" y="833.5"/></g>
 </g>
 <g id="PowerTransformer3Class">
  <g id="117">
   <g id="1170">
    <use class="kv110" height="50" transform="rotate(0,1105.95,783.021) scale(2.00187,2.01818) translate(-528.447,-369.583)" width="50" x="1055.91" xlink:href="#PowerTransformer3:主变高压侧有中性点_0" y="732.5700000000001" zvalue="10596"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874445881347" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1171">
    <use class="kv35" height="50" transform="rotate(0,1105.95,783.021) scale(2.00187,2.01818) translate(-528.447,-369.583)" width="50" x="1055.91" xlink:href="#PowerTransformer3:主变高压侧有中性点_1" y="732.5700000000001" zvalue="10596"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874445946883" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1172">
    <use class="kv10" height="50" transform="rotate(0,1105.95,783.021) scale(2.00187,2.01818) translate(-528.447,-369.583)" width="50" x="1055.91" xlink:href="#PowerTransformer3:主变高压侧有中性点_2" y="732.5700000000001" zvalue="10596"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874446012419" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399455211523" ObjectName="#1炉变"/>
   <cge:TPSR_Ref TObjectID="6755399455211523"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1105.95,783.021) scale(2.00187,2.01818) translate(-528.447,-369.583)" width="50" x="1055.91" y="732.5700000000001"/></g>
  <g id="209">
   <g id="2090">
    <use class="kv110" height="50" transform="rotate(0,1404.84,783.021) scale(2.00187,2.01818) translate(-678.031,-369.583)" width="50" x="1354.8" xlink:href="#PowerTransformer3:三卷变带中性点_0" y="732.5700000000001" zvalue="10631"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874446077955" ObjectName="110"/>
    </metadata>
   </g>
   <g id="2091">
    <use class="kv35" height="50" transform="rotate(0,1404.84,783.021) scale(2.00187,2.01818) translate(-678.031,-369.583)" width="50" x="1354.8" xlink:href="#PowerTransformer3:三卷变带中性点_1" y="732.5700000000001" zvalue="10631"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874446143491" ObjectName="35"/>
    </metadata>
   </g>
   <g id="2092">
    <use class="kv10" height="50" transform="rotate(0,1404.84,783.021) scale(2.00187,2.01818) translate(-678.031,-369.583)" width="50" x="1354.8" xlink:href="#PowerTransformer3:三卷变带中性点_2" y="732.5700000000001" zvalue="10631"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874446209027" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399455277059" ObjectName="#2炉变"/>
   <cge:TPSR_Ref TObjectID="6755399455277059"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1404.84,783.021) scale(2.00187,2.01818) translate(-678.031,-369.583)" width="50" x="1354.8" y="732.5700000000001"/></g>
 </g>
</svg>