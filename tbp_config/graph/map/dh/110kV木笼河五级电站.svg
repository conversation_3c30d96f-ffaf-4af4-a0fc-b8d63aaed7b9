<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549581053954" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.08333333333333" x2="9.833333333333332" y1="16.5" y2="18.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14" y1="9.75" y2="16.75"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="11" y1="11.58333333333333" y2="9.666666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.75" x2="9.75" y1="11.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8" x2="15.08333333333333" y1="8.416666666666668" y2="25.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_1" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,14.96,13) scale(1,1) translate(0,0)" width="4.42" x="12.75" y="9"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="5.416666666666668" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="Disconnector:跌落刀闸_2" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0"/>
   <use terminal-index="1" type="0" x="15" xlink:href="#terminal" y="29"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="20" y1="6" y2="25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20" x2="10.08333333333333" y1="6.000000000000004" y2="25.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.08333333333333" x2="15.08333333333333" y1="24.91666666666666" y2="29.16666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.21666666666667" x2="16.83333333333333" y1="5.505662181544974" y2="5.505662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.09150672768254" x2="15.09150672768254" y1="5.41666666666667" y2="0.25"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Accessory:设备233_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.097643878606505" xlink:href="#terminal" y="23.59422303238503"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="4" y1="2" y2="4"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="8" y1="2" y2="4"/>
   <rect fill-opacity="0" height="12.18" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.04,12.51) scale(1,1) translate(0,0)" width="7.42" x="2.33" y="6.42"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.5" x2="8.611111111111114" y1="23.63508771929826" y2="23.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.5" x2="8.611111111111114" y1="22.41666666666666" y2="22.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="2.166666666666664" y2="6.599999999999997"/>
  </symbol>
  <symbol id="Accessory:PT789_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="12.41666666666667" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:熔断器_0" viewBox="0,0,10,18">
   <use terminal-index="0" type="0" x="5.016666666666667" xlink:href="#terminal" y="1.083333333333336"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="1" y2="17"/>
   <rect fill-opacity="0" height="16.08" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.06,9.06) scale(1,1) translate(0,0)" width="9.58" x="0.27" y="1.02"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="12.0194189818494"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="5.416666666666668" y2="12.02321291373541"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.92126160277786" x2="6.666666666666666" y1="12.10207526123773" y2="18.50000000000001"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.18646053143751" x2="24.5" y1="12.18904818695178" y2="19.00000000000001"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 7.33333 43.5833 L 23 43.5833 L 15 31.5 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:四卷母线PT1_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="38.46862402653686" xlink:href="#terminal" y="12.12084577745815"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.4340488243557" x2="13.4340488243557" y1="18.97990242630313" y2="12.13083416364323"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.97990242630313" x2="9.979902426303131" y1="28.97990242630313" y2="28.97990242630313"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.97990242630313" x2="14.97990242630313" y1="32.22990242630313" y2="29.22990242630313"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.979902426303131" x2="10.97990242630313" y1="28.72990242630313" y2="31.72990242630313"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="8" y1="12" y2="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.65035429095077" x2="38.58333333333334" y1="12.06999777754306" y2="12.06999777754306"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.5" x2="10.90109374154018" y1="12.06999777754306" y2="12.06999777754306"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.192363779901694" x2="9.192363779901694" y1="12.1504688212483" y2="12.1504688212483"/>
   <ellipse cx="20.03" cy="23.25" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="10.85903044656835" x2="10.85903044656835" y1="8.687351652292719" y2="15.52739036724268"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.83527413750478" x2="17.83527413750478" y1="12.1504688212483" y2="12.1504688212483"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="19.58527413750476" x2="19.58527413750476" y1="8.770684985626051" y2="15.61072370057602"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="7.84711090278971" x2="7.84711090278971" y1="8.770684985626044" y2="15.61072370057602"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="16.49002126039279" x2="16.49002126039279" y1="8.770684985626051" y2="15.61072370057602"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.180444236123046" x2="4.180444236123046" y1="9.854018318959376" y2="14.13083416364322"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.930444236123051" x2="2.930444236123051" y1="10.60401831895938" y2="13.21416749697656"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.430444236123051" x2="1.430444236123051" y1="11.10401831895938" y2="12.54750083030989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.24246212639326" x2="21.48000211074537" y1="23.28083416364322" y2="25.68083416364322"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.76738215768904" x2="20.24246212639324" y1="23.28083416364322" y2="23.28083416364322"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.24246212639326" x2="21.48000211074537" y1="23.28083416364322" y2="20.88083416364323"/>
   <ellipse cx="13.03" cy="23.25" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.03" cy="30.25" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.76738215768904" x2="13.24246212639324" y1="23.28083416364322" y2="23.28083416364322"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.24246212639326" x2="21.48000211074537" y1="30.28083416364322" y2="32.68083416364322"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.76738215768904" x2="20.24246212639324" y1="30.28083416364322" y2="30.28083416364322"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.24246212639326" x2="21.48000211074537" y1="30.28083416364322" y2="27.88083416364322"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.24246212639326" x2="14.48000211074537" y1="23.28083416364322" y2="20.88083416364323"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.24246212639326" x2="14.48000211074537" y1="23.28083416364322" y2="25.68083416364322"/>
   <ellipse cx="13.03" cy="30.25" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.86944444444444" x2="35.38333333333333" y1="39.01666666666667" y2="39.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.3" x2="33.3" y1="32.6" y2="36.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="30.91111111111111" x2="36.02222222222223" y1="37.63508771929826" y2="37.63508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.63333333333333" x2="37.3" y1="36.25350877192984" y2="36.25350877192984"/>
   <rect fill-opacity="0" height="15.98" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.15,24.59) scale(1,1) translate(0,0)" width="7.8" x="29.25" y="16.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.3" x2="36.3" y1="26.6" y2="23.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.3" x2="30.3" y1="26.6" y2="23.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.3" x2="33.3" y1="16.6" y2="26.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.3" x2="33.3" y1="11.91666666666666" y2="16.6"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_0" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.416666666666625" x2="14.8889734851558" y1="30.74450652239035" y2="19.51308960898588"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_1" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.8889734851558" x2="14.8889734851558" y1="32.25" y2="19.51308960898588"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_2" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.88620689655174" x2="9.700000000000015" y1="19.49501474926254" y2="32.31404129793511"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.699999999999994" x2="20.24999999999999" y1="19.33333333333333" y2="32.38333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV木笼河五级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="47.75" xlink:href="logo.png" y="49.25"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,182.375,79.25) scale(1,1) translate(0,0)" writing-mode="lr" x="182.37" xml:space="preserve" y="82.75" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,184.083,78.9403) scale(1,1) translate(7.29972e-15,0)" writing-mode="lr" x="184.08" xml:space="preserve" y="87.94" zvalue="3">110kV木笼河五级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="71" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="149"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="149">信号一览</text>
  <line fill="none" id="61" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="381.75" x2="381.75" y1="17.25000000000006" y2="1047.25" zvalue="4"/>
  <line fill="none" id="59" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.750000000000227" x2="374.7499999999998" y1="153.1204926140824" y2="153.1204926140824" zvalue="6"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,563.325,617.449) scale(1,1) translate(0,0)" writing-mode="lr" x="563.33" xml:space="preserve" y="621.95" zvalue="37">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,651.282,664.196) scale(1,1) translate(0,0)" writing-mode="lr" x="651.28" xml:space="preserve" y="668.7" zvalue="39">651</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,694.668,867.628) scale(1,1) translate(-2.92939e-13,-5.66461e-13)" writing-mode="lr" x="694.6684752281722" xml:space="preserve" y="872.1277576427464" zvalue="42">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,573.854,871.724) scale(1,1) translate(0,0)" writing-mode="lr" x="573.85" xml:space="preserve" y="876.22" zvalue="44">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,810.508,879) scale(1,1) translate(0,0)" writing-mode="lr" x="810.51" xml:space="preserve" y="883.5" zvalue="47">出口PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1075.44,720.333) scale(1,1) translate(0,0)" writing-mode="lr" x="1075.44" xml:space="preserve" y="724.83" zvalue="53">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,693.329,120.889) scale(1,1) translate(0,0)" writing-mode="lr" x="693.33" xml:space="preserve" y="125.39" zvalue="57">110kV木笼河三五级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1457.88,664.196) scale(1,1) translate(0,0)" writing-mode="lr" x="1457.88" xml:space="preserve" y="668.7" zvalue="62">652</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1502.67,867.628) scale(1,1) translate(1.14059e-12,-5.66461e-13)" writing-mode="lr" x="1502.668475228172" xml:space="preserve" y="872.1277576427464" zvalue="65">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1384.08,867.057) scale(1,1) translate(0,0)" writing-mode="lr" x="1384.08" xml:space="preserve" y="871.5599999999999" zvalue="67">励磁变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1618.51,873.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1618.51" xml:space="preserve" y="877.9400000000001" zvalue="70">出口PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1501.91,434.111) scale(1,1) translate(0,0)" writing-mode="lr" x="1501.91" xml:space="preserve" y="438.61" zvalue="74">6.3kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1599.84,298.286) scale(1,1) translate(0,0)" writing-mode="lr" x="1599.84" xml:space="preserve" y="302.79" zvalue="76">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1657.18,58.3056) scale(1,1) translate(0,0)" writing-mode="lr" x="1657.18" xml:space="preserve" y="62.81" zvalue="81">10kV外接电源</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1740.08,396.556) scale(1,1) translate(0,-1.672e-13)" writing-mode="lr" x="1740.080357142857" xml:space="preserve" y="401.0555555555555" zvalue="88">0.4kV外接电源（高河二级站）</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,635.489,425.889) scale(1,1) translate(0,0)" writing-mode="lr" x="635.49" xml:space="preserve" y="430.39" zvalue="92">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,751.413,523.667) scale(1,1) translate(0,0)" writing-mode="lr" x="751.41" xml:space="preserve" y="528.17" zvalue="94">6011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,718.111,342.556) scale(1,1) translate(0,0)" writing-mode="lr" x="718.11" xml:space="preserve" y="347.06" zvalue="97">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,662.322,259) scale(1,1) translate(0,0)" writing-mode="lr" x="662.3200000000001" xml:space="preserve" y="263.5" zvalue="100">1011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,624.742,875.057) scale(1,1) translate(0,0)" writing-mode="lr" x="624.74" xml:space="preserve" y="879.5599999999999" zvalue="106">励磁PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1432.96,871.724) scale(1,1) translate(0,0)" writing-mode="lr" x="1432.96" xml:space="preserve" y="876.22" zvalue="109">励磁PT</text>
  <line fill="none" id="111" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1659.583333333333" x2="1659.583333333333" y1="330.8333333333334" y2="437.5000000000001" zvalue="119"/>
  <line fill="none" id="112" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1658.472222222222" x2="1746.25" y1="369.7222222222222" y2="369.7222222222222" zvalue="120"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="120" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="123"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="125">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="117" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="126">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="127">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="128">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="114" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="129">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="131">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="132">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="133">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="137">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="138">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,359.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="363.75" zvalue="141">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="150">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="151">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="154">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="156">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="158">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="160">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="162">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="164">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,773.861,274.167) scale(1,1) translate(0,-4.71919e-13)" writing-mode="lr" x="773.86" xml:space="preserve" y="278.67" zvalue="168">10</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,773.861,174.167) scale(1,1) translate(0,0)" writing-mode="lr" x="773.86" xml:space="preserve" y="178.67" zvalue="170">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,840.125,467) scale(1,1) translate(0,0)" writing-mode="lr" x="840.13" xml:space="preserve" y="471.5" zvalue="175">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,217.5,953) scale(1,1) translate(0,0)" writing-mode="lr" x="217.5" xml:space="preserve" y="959" zvalue="216">MuLongHe5-01-2012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,694,884.5) scale(1,1) translate(0,0)" writing-mode="lr" x="694" xml:space="preserve" y="889" zvalue="222">7.5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1502,884.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1502" xml:space="preserve" y="889" zvalue="224">7.5MW</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="149"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="253">
   <path class="v6300" d="M 568.05 595.43 L 1643.35 595.43" stroke-width="6" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674232041476" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674232041476"/></metadata>
  <path d="M 568.05 595.43 L 1643.35 595.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="195">
   <use class="v6300" height="20" transform="rotate(0,695.067,659.56) scale(2.06048,2.06048) translate(-352.432,-328.855)" width="10" x="684.7644887616284" xlink:href="#Breaker:小车断路器_0" y="638.9550984421437" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924466638853" ObjectName="#1发电机651断路器"/>
   <cge:TPSR_Ref TObjectID="6473924466638853"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,695.067,659.56) scale(2.06048,2.06048) translate(-352.432,-328.855)" width="10" x="684.7644887616284" y="638.9550984421437"/></g>
  <g id="199">
   <use class="v6300" height="20" transform="rotate(0,1501.67,659.56) scale(2.06048,2.06048) translate(-767.57,-328.855)" width="10" x="1491.366307273421" xlink:href="#Breaker:小车断路器_0" y="638.9550984421437" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924466573317" ObjectName="#2发电机652断路器"/>
   <cge:TPSR_Ref TObjectID="6473924466573317"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1501.67,659.56) scale(2.06048,2.06048) translate(-767.57,-328.855)" width="10" x="1491.366307273421" y="638.9550984421437"/></g>
  <g id="80">
   <use class="kv110" height="20" transform="rotate(0,693.333,343.556) scale(1.66667,1.5) translate(-274,-109.519)" width="10" x="685" xlink:href="#Breaker:开关_0" y="328.5555555555556" zvalue="96"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924466704389" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473924466704389"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,693.333,343.556) scale(1.66667,1.5) translate(-274,-109.519)" width="10" x="685" y="328.5555555555556"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="28">
   <path class="v6300" d="M 693.64 797.19 L 693.64 678.1" stroke-width="1" zvalue="40"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@0" LinkObjectIDznd="195@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 693.64 797.19 L 693.64 678.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="v6300" d="M 695.07 640.5 L 695.07 595.43" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@0" LinkObjectIDznd="253@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 695.07 640.5 L 695.07 595.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="v6300" d="M 810.44 814.69 L 810.44 756.67 L 693.64 756.67" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="28" MaxPinNum="2"/>
   </metadata>
  <path d="M 810.44 814.69 L 810.44 756.67 L 693.64 756.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="v6300" d="M 573.85 818.42 L 573.85 758 L 693.64 758" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="28" MaxPinNum="2"/>
   </metadata>
  <path d="M 573.85 818.42 L 573.85 758 L 693.64 758" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="v6300" d="M 1501.64 797.19 L 1501.67 678.1" stroke-width="1" zvalue="63"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="199@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1501.64 797.19 L 1501.67 678.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="17">
   <path class="v6300" d="M 1501.67 640.5 L 1501.67 595.43" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@0" LinkObjectIDznd="253@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1501.67 640.5 L 1501.67 595.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="v6300" d="M 1501.67 502.08 L 1501.67 595.43" stroke-width="1" zvalue="74"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="253@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1501.67 502.08 L 1501.67 595.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv10" d="M 1658.16 285.39 L 1658.16 198.09" stroke-width="1" zvalue="78"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@0" LinkObjectIDznd="13@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1658.16 285.39 L 1658.16 198.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv10" d="M 1658 157.33 L 1658 116.03" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="13@0" LinkObjectIDznd="232@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1658 157.33 L 1658 116.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv10" d="M 1705.36 243.63 L 1658.16 243.63" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64@0" LinkObjectIDznd="12" MaxPinNum="2"/>
   </metadata>
  <path d="M 1705.36 243.63 L 1658.16 243.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="v6300" d="M 695.07 595.43 L 695.07 548.75" stroke-width="1" zvalue="94"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@3" LinkObjectIDznd="76@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 695.07 595.43 L 695.07 548.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="v6300" d="M 695.12 507.59 L 695.12 467.09" stroke-width="1" zvalue="95"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="76@0" LinkObjectIDznd="73@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 695.12 507.59 L 695.12 467.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="kv110" d="M 694.48 391.47 L 694.48 357.88" stroke-width="1" zvalue="97"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@0" LinkObjectIDznd="80@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 694.48 391.47 L 694.48 357.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv110" d="M 693.28 329.21 L 693.28 280.75" stroke-width="1" zvalue="101"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="83@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 693.28 329.21 L 693.28 280.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv110" d="M 693.33 239.59 L 693.33 181.33" stroke-width="1" zvalue="103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="83@0" LinkObjectIDznd="179@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 693.33 239.59 L 693.33 181.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="v6300" d="M 612.74 817.76 L 612.74 796.67 L 573.85 796.67" stroke-width="1" zvalue="106"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="89@0" LinkObjectIDznd="20" MaxPinNum="2"/>
   </metadata>
  <path d="M 612.74 817.76 L 612.74 796.67 L 573.85 796.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="v6300" d="M 1127.79 708.55 L 1127.79 595.43" stroke-width="1" zvalue="116"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="253@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1127.79 708.55 L 1127.79 595.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="v6300" d="M 1126.69 639.6 L 1127.79 639.6" stroke-width="1" zvalue="118"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@0" LinkObjectIDznd="107" MaxPinNum="2"/>
   </metadata>
  <path d="M 1126.69 639.6 L 1127.79 639.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv110" d="M 763.03 307.78 L 693.28 307.78" stroke-width="1" zvalue="170"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="122@0" LinkObjectIDznd="86" MaxPinNum="2"/>
   </metadata>
  <path d="M 763.03 307.78 L 693.28 307.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv110" d="M 763.03 215.78 L 693.33 215.78" stroke-width="1" zvalue="171"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@0" LinkObjectIDznd="88" MaxPinNum="2"/>
   </metadata>
  <path d="M 763.03 215.78 L 693.33 215.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv110" d="M 630.84 211.6 L 693.33 211.6" stroke-width="1" zvalue="173"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="88" MaxPinNum="2"/>
   </metadata>
  <path d="M 630.84 211.6 L 693.33 211.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="kv110" d="M 694.46 409.25 L 803.75 409.25 L 803.75 448" stroke-width="1" zvalue="175"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@2" LinkObjectIDznd="128@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 694.46 409.25 L 803.75 409.25 L 803.75 448" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="49">
   <path class="v6300" d="M 1618.44 814.69 L 1618.44 740.5 L 1501.65 740.5" stroke-width="1" zvalue="200"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@0" LinkObjectIDznd="19" MaxPinNum="2"/>
   </metadata>
  <path d="M 1618.44 814.69 L 1618.44 740.5 L 1501.65 740.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="v6300" d="M 1381.85 810.42 L 1381.85 740.5 L 1501.65 740.5" stroke-width="1" zvalue="201"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="49" MaxPinNum="2"/>
   </metadata>
  <path d="M 1381.85 810.42 L 1381.85 740.5 L 1501.65 740.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="v6300" d="M 1432.96 814.42 L 1432.96 777.75 L 1381.85 777.75" stroke-width="1" zvalue="202"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@0" LinkObjectIDznd="50" MaxPinNum="2"/>
   </metadata>
  <path d="M 1432.96 814.42 L 1432.96 777.75 L 1381.85 777.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="193">
   <use class="v6300" height="30" transform="rotate(0,693.638,819.979) scale(1.54536,1.54536) translate(-236.605,-281.191)" width="30" x="670.4578529557438" xlink:href="#Generator:发电机_0" y="796.7988095053233" zvalue="41"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449505132550" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449505132550"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,693.638,819.979) scale(1.54536,1.54536) translate(-236.605,-281.191)" width="30" x="670.4578529557438" y="796.7988095053233"/></g>
  <g id="197">
   <use class="v6300" height="30" transform="rotate(0,1501.64,819.979) scale(1.54536,1.54536) translate(-521.749,-281.191)" width="30" x="1478.457852955744" xlink:href="#Generator:发电机_0" y="796.7988095053233" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449504804870" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449504804870"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1501.64,819.979) scale(1.54536,1.54536) translate(-521.749,-281.191)" width="30" x="1478.457852955744" y="796.7988095053233"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="186">
   <use class="v6300" height="29" transform="rotate(0,573.854,838.619) scale(1.41717,-1.41717) translate(-162.667,-1424.33)" width="30" x="552.5959806595417" xlink:href="#Accessory:PT12321_0" y="818.0702610846947" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449505067014" ObjectName="#1发电机励磁变"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,573.854,838.619) scale(1.41717,-1.41717) translate(-162.667,-1424.33)" width="30" x="552.5959806595417" y="818.0702610846947"/></g>
  <g id="108">
   <use class="v6300" height="30" transform="rotate(0,810.508,834.444) scale(1.34815,1.34815) translate(-204.085,-210.266)" width="30" x="790.286111111111" xlink:href="#Accessory:PT789_0" y="814.2222222222223" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449505001478" ObjectName="#1发电机出口PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,810.508,834.444) scale(1.34815,1.34815) translate(-204.085,-210.266)" width="30" x="790.286111111111" y="814.2222222222223"/></g>
  <g id="196">
   <use class="v6300" height="29" transform="rotate(0,1381.85,830.619) scale(1.41717,-1.41717) translate(-400.517,-1410.68)" width="30" x="1360.595980659542" xlink:href="#Accessory:PT12321_0" y="810.0702610846947" zvalue="66"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449504739334" ObjectName="#2发电机励磁变"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1381.85,830.619) scale(1.41717,-1.41717) translate(-400.517,-1410.68)" width="30" x="1360.595980659542" y="810.0702610846947"/></g>
  <g id="191">
   <use class="v6300" height="30" transform="rotate(0,1618.51,834.444) scale(1.34815,1.34815) translate(-412.744,-210.266)" width="30" x="1598.286111111111" xlink:href="#Accessory:PT789_0" y="814.2222222222223" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449504673798" ObjectName="#2发电机出口PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1618.51,834.444) scale(1.34815,1.34815) translate(-412.744,-210.266)" width="30" x="1598.286111111111" y="814.2222222222223"/></g>
  <g id="207">
   <use class="v6300" height="18" transform="rotate(0,1504.14,479.889) scale(2.65432,-2.65432) translate(-925.055,-645.795)" width="15" x="1484.229590065663" xlink:href="#Accessory:PT8_0" y="456" zvalue="73"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449504608262" ObjectName="6.3kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1504.14,479.889) scale(2.65432,-2.65432) translate(-925.055,-645.795)" width="15" x="1484.229590065663" y="456"/></g>
  <g id="64">
   <use class="kv10" height="26" transform="rotate(90,1720.64,243.773) scale(-1.44231,1.44231) translate(-2910.97,-69.007)" width="12" x="1711.989010989011" xlink:href="#Accessory:设备233_0" y="225.0228937728938" zvalue="84"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449505198086" ObjectName="#2发电机附属"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1720.64,243.773) scale(-1.44231,1.44231) translate(-2910.97,-69.007)" width="12" x="1711.989010989011" y="225.0228937728938"/></g>
  <g id="89">
   <use class="v6300" height="29" transform="rotate(0,612.742,837.953) scale(1.41717,-1.41717) translate(-174.115,-1423.19)" width="30" x="591.4848695484307" xlink:href="#Accessory:PT12321_0" y="817.403594418028" zvalue="105"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449505394694" ObjectName="#1发电机励磁PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,612.742,837.953) scale(1.41717,-1.41717) translate(-174.115,-1423.19)" width="30" x="591.4848695484307" y="817.403594418028"/></g>
  <g id="95">
   <use class="v6300" height="29" transform="rotate(0,1432.96,834.619) scale(1.41717,-1.41717) translate(-415.563,-1417.5)" width="30" x="1411.707091770653" xlink:href="#Accessory:PT12321_0" y="814.0702610846947" zvalue="108"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449505460230" ObjectName="#2发电机励磁PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1432.96,834.619) scale(1.41717,-1.41717) translate(-415.563,-1417.5)" width="30" x="1411.707091770653" y="814.0702610846947"/></g>
  <g id="109">
   <use class="v6300" height="18" transform="rotate(0,1126.67,651.333) scale(1.66667,1.48148) translate(-447.333,-207.35)" width="10" x="1118.333333333333" xlink:href="#Accessory:熔断器_0" y="638" zvalue="117"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449505525766" ObjectName="#1站用变熔断器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1126.67,651.333) scale(1.66667,1.48148) translate(-447.333,-207.35)" width="10" x="1118.333333333333" y="638"/></g>
  <g id="126">
   <use class="kv110" height="40" transform="rotate(0,607.75,201.75) scale(1.25,-1.25) translate(-116.55,-358.15)" width="40" x="582.75" xlink:href="#Accessory:四卷母线PT1_0" y="176.75" zvalue="172"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449668120582" ObjectName="110kV木笼河三五级线PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,607.75,201.75) scale(1.25,-1.25) translate(-116.55,-358.15)" width="40" x="582.75" y="176.75"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="55">
   <use class="v6300" height="30" transform="rotate(0,1127.65,729.833) scale(1.46748,1.47778) translate(-352.679,-228.794)" width="28" x="1107.105582482907" xlink:href="#EnergyConsumer:站用变DY接地_0" y="707.6666666666667" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449504935942" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1127.65,729.833) scale(1.46748,1.47778) translate(-352.679,-228.794)" width="28" x="1107.105582482907" y="707.6666666666667"/></g>
  <g id="153">
   <use class="kv10" height="30" transform="rotate(0,1658,310) scale(1.69643,1.70833) translate(-670.903,-117.912)" width="28" x="1634.25" xlink:href="#EnergyConsumer:站用变DY接地_0" y="284.375" zvalue="75"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449504542726" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1658,310) scale(1.69643,1.70833) translate(-670.903,-117.912)" width="28" x="1634.25" y="284.375"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="13">
   <use class="kv10" height="30" transform="rotate(0,1658,178.417) scale(3.13333,1.4055) translate(-1096.85,-45.3927)" width="30" x="1611" xlink:href="#Disconnector:跌落刀闸_0" y="157.334974364315" zvalue="77"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449504477190" ObjectName="#2站用变刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449504477190"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1658,178.417) scale(3.13333,1.4055) translate(-1096.85,-45.3927)" width="30" x="1611" y="157.334974364315"/></g>
  <g id="76">
   <use class="v6300" height="30" transform="rotate(0,694.949,528) scale(1.91919,1.40741) translate(-325.95,-146.731)" width="15" x="680.5555555555555" xlink:href="#Disconnector:刀闸_0" y="506.8888888888889" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449505263622" ObjectName="#1主变6.3kV侧6011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449505263622"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,694.949,528) scale(1.91919,1.40741) translate(-325.95,-146.731)" width="15" x="680.5555555555555" y="506.8888888888889"/></g>
  <g id="83">
   <use class="kv110" height="30" transform="rotate(0,693.16,260) scale(1.91919,1.40741) translate(-325.093,-69.152)" width="15" x="678.7664521153524" xlink:href="#Disconnector:刀闸_0" y="238.8888888888889" zvalue="99"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449505329158" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449505329158"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,693.16,260) scale(1.91919,1.40741) translate(-325.093,-69.152)" width="15" x="678.7664521153524" y="238.8888888888889"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="232">
   <use class="kv10" height="30" transform="rotate(0,1657.18,94.0278) scale(6.34921,1.48148) translate(-1377.45,-23.3368)" width="7" x="1634.95981733304" xlink:href="#ACLineSegment:线路_0" y="71.80555534362799" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449504411654" ObjectName="10kV外接电源"/>
   <cge:TPSR_Ref TObjectID="6192449504411654_5066549581053954"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1657.18,94.0278) scale(6.34921,1.48148) translate(-1377.45,-23.3368)" width="7" x="1634.95981733304" y="71.80555534362799"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="73">
   <g id="730">
    <use class="kv110" height="50" transform="rotate(0,694.433,429.204) scale(1.53704,1.53704) translate(-234.578,-136.537)" width="30" x="671.38" xlink:href="#PowerTransformer2:Y-D_0" y="390.78" zvalue="91"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874418290692" ObjectName="110"/>
    </metadata>
   </g>
   <g id="731">
    <use class="v6300" height="50" transform="rotate(0,694.433,429.204) scale(1.53704,1.53704) translate(-234.578,-136.537)" width="30" x="671.38" xlink:href="#PowerTransformer2:Y-D_1" y="390.78" zvalue="91"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874418356228" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399441317892" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399441317892"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,694.433,429.204) scale(1.53704,1.53704) translate(-234.578,-136.537)" width="30" x="671.38" y="390.78"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123810246660" ObjectName="F"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123810312196" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123810115588" ObjectName="F"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123810181124" ObjectName="F"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127182729221" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127182663685" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="29">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="29" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,691.9,17.6051) scale(1,1) translate(0,5.56874e-16)" writing-mode="lr" x="691.4299999999999" xml:space="preserve" y="22.3" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123804217348" ObjectName="P"/>
   </metadata>
  </g>
  <g id="30">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="30" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,691.9,44.2222) scale(1,1) translate(0,3.51197e-15)" writing-mode="lr" x="691.4299999999999" xml:space="preserve" y="48.92" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123804282884" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="31">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="31" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,691.9,70.8394) scale(1,1) translate(0,6.46707e-15)" writing-mode="lr" x="691.4299999999999" xml:space="preserve" y="75.54000000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123804348420" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="33" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,605.862,311.116) scale(1,1) translate(0,-6.61199e-14)" writing-mode="lr" x="605.3099999999999" xml:space="preserve" y="315.83" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123807559684" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="34">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="34" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,605.862,339.317) scale(1,1) translate(0,-7.23819e-14)" writing-mode="lr" x="605.3099999999999" xml:space="preserve" y="344.03" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123807625221" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="35" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,605.862,492.212) scale(1,1) translate(0,0)" writing-mode="lr" x="605.3099999999999" xml:space="preserve" y="496.91" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123807690757" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="36">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="36" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,605.862,518.815) scale(1,1) translate(0,0)" writing-mode="lr" x="605.3099999999999" xml:space="preserve" y="523.51" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123807756293" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="37">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="37" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,605.862,367.519) scale(1,1) translate(0,-7.86438e-14)" writing-mode="lr" x="605.3099999999999" xml:space="preserve" y="372.23" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123807821829" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="38" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,605.862,545.418) scale(1,1) translate(0,0)" writing-mode="lr" x="605.3099999999999" xml:space="preserve" y="550.12" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123808149509" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="39">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="39" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1496.78,921.403) scale(1,1) translate(0,1.20953e-12)" writing-mode="lr" x="1496.23" xml:space="preserve" y="926.12" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123803037700" ObjectName="P"/>
   </metadata>
  </g>
  <g id="40">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="40" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,690.781,915.403) scale(1,1) translate(0,1.20154e-12)" writing-mode="lr" x="690.23" xml:space="preserve" y="920.12" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123805855749" ObjectName="P"/>
   </metadata>
  </g>
  <g id="41">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="41" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1499.78,945.008) scale(1,1) translate(0,1.24098e-12)" writing-mode="lr" x="1499.23" xml:space="preserve" y="949.72" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123803103236" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="42">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="42" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,689.781,936.008) scale(1,1) translate(0,1.22899e-12)" writing-mode="lr" x="689.23" xml:space="preserve" y="940.72" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123805921285" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="43">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="43" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1499.78,965.613) scale(1,1) translate(0,1.26843e-12)" writing-mode="lr" x="1499.23" xml:space="preserve" y="970.33" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123803168774" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="44">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="44" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,689.781,958.613) scale(1,1) translate(0,1.2591e-12)" writing-mode="lr" x="689.23" xml:space="preserve" y="963.33" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123805986822" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="45">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,355.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="360.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123807428612" ObjectName="F"/>
   </metadata>
  </g>
  <g id="27">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,335.611,378.167) scale(1,1) translate(0,0)" writing-mode="lr" x="335.77" xml:space="preserve" y="383.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124566859780" ObjectName="F"/>
   </metadata>
  </g>
  <g id="46">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,378.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="383.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124566794244" ObjectName="F"/>
   </metadata>
  </g>
  <g id="47">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,335.611,400.167) scale(1,1) translate(0,2.5957e-13)" writing-mode="lr" x="335.77" xml:space="preserve" y="405.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124567056388" ObjectName="F"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="16" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1497.05,353.927) scale(1,1) translate(0,0)" writing-mode="lr" x="1496.58" xml:space="preserve" y="358.71" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123807035396" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="24">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="24" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1497.05,378.927) scale(1,1) translate(0,0)" writing-mode="lr" x="1496.58" xml:space="preserve" y="383.71" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123807100932" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="48">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="48" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1497.05,403.927) scale(1,1) translate(0,-1.71608e-13)" writing-mode="lr" x="1496.58" xml:space="preserve" y="408.71" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123807166468" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="52" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,547.047,643.427) scale(1,1) translate(0,2.77968e-13)" writing-mode="lr" x="546.58" xml:space="preserve" y="648.21" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123807297540" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="32" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,695.9,95.5) scale(1,1) translate(0,9.20495e-15)" writing-mode="lr" x="695.4299999999999" xml:space="preserve" y="100.2" zvalue="1">Ua:dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123804741636" ObjectName="Ua"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="72">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="147"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374883880964" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="148"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950259933192" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="122">
   <use class="kv110" height="20" transform="rotate(270,773.861,307.722) scale(-1.11111,1.11111) translate(-1469.78,-29.6611)" width="10" x="768.3055556615193" xlink:href="#GroundDisconnector:地刀_0" y="296.6111111111111" zvalue="167"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449667923974" ObjectName="#1主变110kV侧10110接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449667923974"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,773.861,307.722) scale(-1.11111,1.11111) translate(-1469.78,-29.6611)" width="10" x="768.3055556615193" y="296.6111111111111"/></g>
  <g id="123">
   <use class="kv110" height="20" transform="rotate(270,773.861,215.722) scale(-1.11111,1.11111) translate(-1469.78,-20.4611)" width="10" x="768.3055556615193" xlink:href="#GroundDisconnector:地刀_0" y="204.6111111111111" zvalue="169"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449668055046" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449668055046"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,773.861,215.722) scale(-1.11111,1.11111) translate(-1469.78,-20.4611)" width="10" x="768.3055556615193" y="204.6111111111111"/></g>
  <g id="128">
   <use class="kv110" height="45" transform="rotate(0,793.75,468) scale(-1.25,-1.25) translate(-1423.12,-836.775)" width="45" x="765.625" xlink:href="#GroundDisconnector:12547_0" y="439.875" zvalue="174"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449668251654" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449668251654"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,793.75,468) scale(-1.25,-1.25) translate(-1423.12,-836.775)" width="45" x="765.625" y="439.875"/></g>
 </g>
</svg>