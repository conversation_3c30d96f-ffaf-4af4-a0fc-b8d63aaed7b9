<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549597110274" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:PT1111_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="33.75" xlink:href="#terminal" y="11.95"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="27.02377700368697" x2="19.18246971500333" y1="12.01581866675157" y2="12.01581866675157"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.4317302644139" x2="33.73333333333333" y1="12.01581866675157" y2="12.01581866675157"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="23.30947355115262" x2="23.30947355115262" y1="12.14512912951974" y2="19.34675167759026"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.21102695297582" x2="12.70307369224894" y1="12.09628971045682" y2="12.09628971045682"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.22373975336484" x2="19.22373975336484" y1="12.09628971045682" y2="12.09628971045682"/>
   <ellipse cx="23.29" cy="23.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="19.22373975336483" x2="19.22373975336483" y1="8.716505874834564" y2="15.55654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="30.36665011096792" x2="30.36665011096792" y1="12.09628971045682" y2="12.09628971045682"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="30.3666501109679" x2="30.3666501109679" y1="8.716505874834564" y2="15.55654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="16.12848687625285" x2="16.12848687625285" y1="8.716505874834558" y2="15.55654458978453"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="27.27139723385593" x2="27.27139723385593" y1="8.716505874834564" y2="15.55654458978453"/>
   <ellipse cx="25.79" cy="27.85" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.79" cy="27.85" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.96296296296296" x2="8.779897241477752" y1="12.08795620351516" y2="12.08795620351516"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="8.711988705963254" x2="8.711988705963254" y1="14.28440670580408" y2="10.00759086112023"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="7.461988705963254" x2="7.461988705963254" y1="13.53440670580408" y2="10.9242575277869"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.961988705963256" x2="5.961988705963256" y1="13.03440670580408" y2="11.59092419445357"/>
  </symbol>
  <symbol id="Breaker:母联开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill="rgb(170,0,0)" fill-opacity="1" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.333333333333333" x2="9.75" y1="0.4166666666666696" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.666666666666668" x2="0.4166666666666661" y1="0.4999999999999982" y2="19.66666666666667"/>
   <rect fill-opacity="0" height="19.58" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.04,10.04) scale(1,1) translate(0,0)" width="9.58" x="0.25" y="0.25"/>
  </symbol>
  <symbol id="Accessory:2绕组线路PT_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.4166666666666714"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="17" y1="28" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.5" x2="17.5" y1="9.16666666666667" y2="9.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="9.166666666666671" y2="0.3333333333333321"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.5) scale(1,1) translate(0,0)" width="4" x="13" y="11"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="23.41666666666666" y2="9.083333333333332"/>
   <ellipse cx="15.15" cy="28.4" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="34" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.16666666666667" x2="17.16666666666667" y1="34.50000000000001" y2="34.50000000000001"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="PowerTransformer3:可调三卷变Y-Y-D2_0" viewBox="0,0,96,97">
   <ellipse cx="37.85" cy="30.17" fill-opacity="0" rx="24.09" ry="24.09" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.85185185185185" x2="38.34979423868313" y1="20.35185185185185" y2="28.33437484123355"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="47.07407407407407" x2="38.38230960727531" y1="20.53703703703704" y2="28.33437484123355"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="38.37037037037037" x2="38.37037037037037" y1="28.31481481481481" y2="38.87037037037037"/>
   <path d="M 77.6296 0.907407 L 69.8519 3.68519 L 73.1852 7.2037 z" fill="rgb(255,0,0)" fill-opacity="1" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <use terminal-index="0" type="1" x="37.81481481481481" xlink:href="#terminal" y="5.907407407407398"/>
   <use terminal-index="3" type="2" x="38.37037037037037" xlink:href="#terminal" y="28.31481481481481"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="76.25239741365428" x2="0.2222222222222214" y1="2.843277472023516" y2="55.35185185185186"/>
  </symbol>
  <symbol id="PowerTransformer3:可调三卷变Y-Y-D2_1" viewBox="0,0,96,97">
   <use terminal-index="1" type="1" x="94.85185185185185" xlink:href="#terminal" y="52.38888888888889"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="70.96296296296296" x2="70.96296296296296" y1="42.94444444444445" y2="52.49710409998476"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="70.9776964893563" x2="81.14814814814815" y1="52.51336178428086" y2="62.57407407407408"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="70.96143880506021" x2="60.03703703703704" y1="52.49710409998476" y2="62.38888888888889"/>
   <ellipse cx="69.93000000000001" cy="52.76" fill-opacity="0" rx="24.19" ry="24.19" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer3:可调三卷变Y-Y-D2_2" viewBox="0,0,96,97">
   <use terminal-index="2" type="1" x="38.18518518518518" xlink:href="#terminal" y="94.61111111111111"/>
   <ellipse cx="38.07" cy="70.91" fill-opacity="0" rx="23.78" ry="23.78" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 38.1852 59.7963 L 26.8889 77.5741 L 49.2963 77.5741 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id="Accessory:中间电缆_0" viewBox="0,0,8,21">
   <use terminal-index="0" type="0" x="4" xlink:href="#terminal" y="10.5"/>
   <path d="M 1.08333 0.5 L 7 0.5 L 4 7.13889 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4" x2="4" y1="20.83333333333333" y2="0.5000000000000036"/>
   <path d="M 1.08333 20.6389 L 7 20.6389 L 4 14 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:间隔模板_0" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(85,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="State:间隔模板_1" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(185,185,185)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="Ground:大地_0" viewBox="0,0,12,18">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6" x2="6" y1="13.08333333333334" y2="0.1666666666666643"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.5833333333333313" x2="11.25" y1="12.99453511141348" y2="12.99453511141348"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.083333333333333" x2="8" y1="17.63116790988687" y2="17.63116790988687"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666667" x2="10.33333333333333" y1="15.40451817731685" y2="15.40451817731685"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV模拟B站" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="59.4" id="1" preserveAspectRatio="xMidYMid slice" width="189.48" x="72.66" xlink:href="logo.png" y="27.68"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="467" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,167.398,57.3772) scale(1,1) translate(8.06667e-15,-2.45823e-14)" writing-mode="lr" x="167.4" xml:space="preserve" y="61.88" zvalue="11351"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="21" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,177.601,55.6394) scale(1,1) translate(0,3.73375e-15)" writing-mode="lr" x="177.6" xml:space="preserve" y="63.14" zvalue="11352">    110kV模拟B站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="627" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,63.2857,363.269) scale(1,1) translate(0,0)" width="72.88" x="26.85" y="351.27" zvalue="11522"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63.2857,363.269) scale(1,1) translate(0,0)" writing-mode="lr" x="63.29" xml:space="preserve" y="367.77" zvalue="11522">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="170" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,63.2857,408) scale(1,1) translate(0,0)" width="72.88" x="26.85" y="396" zvalue="11787"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63.2857,408) scale(1,1) translate(0,0)" writing-mode="lr" x="63.29" xml:space="preserve" y="412.5" zvalue="11787">信号一览</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="514" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,169.281,408) scale(1,1) translate(0,0)" width="72.88" x="132.84" y="396" zvalue="11788"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,169.281,408) scale(1,1) translate(0,0)" writing-mode="lr" x="169.28" xml:space="preserve" y="412.5" zvalue="11788">光字巡检</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,599.295,68.3248) scale(1,1) translate(0,0)" writing-mode="lr" x="599.3" xml:space="preserve" y="72.81999999999999" zvalue="7514">110kV模拟A线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,621.251,232.579) scale(1,1) translate(0,0)" writing-mode="lr" x="621.25" xml:space="preserve" y="237.08" zvalue="7515">144</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,612.77,172.894) scale(1,1) translate(0,0)" writing-mode="lr" x="612.77" xml:space="preserve" y="177.39" zvalue="7517">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,573.186,156.548) scale(1,1) translate(0,0)" writing-mode="lr" x="573.1900000000001" xml:space="preserve" y="161.05" zvalue="7519">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,607.963,294.175) scale(1,1) translate(0,0)" writing-mode="lr" x="607.96" xml:space="preserve" y="298.67" zvalue="7531">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,568.434,214.468) scale(1,1) translate(0,0)" writing-mode="lr" x="568.4299999999999" xml:space="preserve" y="218.97" zvalue="7567">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,569.043,277.142) scale(1,1) translate(0,4.47378e-13)" writing-mode="lr" x="569.04" xml:space="preserve" y="281.64" zvalue="7571">27</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="529" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,392.406,304.035) scale(1,1) translate(0,0)" writing-mode="lr" x="392.41" xml:space="preserve" y="308.53" zvalue="7578">110kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,709.013,428.275) scale(1,1) translate(0,0)" writing-mode="lr" x="709.01" xml:space="preserve" y="432.78" zvalue="7804">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,696.733,464.979) scale(1,1) translate(0,-2.02718e-13)" writing-mode="lr" x="696.73" xml:space="preserve" y="469.48" zvalue="7806">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="247" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,652.664,419.382) scale(1,1) translate(0,0)" writing-mode="lr" x="652.66" xml:space="preserve" y="423.88" zvalue="7819">27</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.77160856406601" x2="182.700608564066" y1="143" y2="143"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.77160856406601" x2="182.700608564066" y1="167.8333" y2="167.8333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.77160856406601" x2="16.77160856406601" y1="143" y2="167.8333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="182.700608564066" y1="143" y2="167.8333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="348.629608564066" y1="143" y2="143"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="348.629608564066" y1="167.8333" y2="167.8333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="182.700608564066" y1="143" y2="167.8333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.629608564066" x2="348.629608564066" y1="143" y2="167.8333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.77160856406601" x2="182.700608564066" y1="167.8333" y2="167.8333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.77160856406601" x2="182.700608564066" y1="192.6666" y2="192.6666"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.77160856406601" x2="16.77160856406601" y1="167.8333" y2="192.6666"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="182.700608564066" y1="167.8333" y2="192.6666"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="348.629608564066" y1="167.8333" y2="167.8333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="348.629608564066" y1="192.6666" y2="192.6666"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="182.700608564066" y1="167.8333" y2="192.6666"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.629608564066" x2="348.629608564066" y1="167.8333" y2="192.6666"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.77160856406601" x2="182.700608564066" y1="192.6667" y2="192.6667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.77160856406601" x2="182.700608564066" y1="217.5" y2="217.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.77160856406601" x2="16.77160856406601" y1="192.6667" y2="217.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="182.700608564066" y1="192.6667" y2="217.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="348.629608564066" y1="192.6667" y2="192.6667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="348.629608564066" y1="217.5" y2="217.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="182.700608564066" y1="192.6667" y2="217.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.629608564066" x2="348.629608564066" y1="192.6667" y2="217.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.77160856406601" x2="182.700608564066" y1="217.5" y2="217.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.77160856406601" x2="182.700608564066" y1="242.3333" y2="242.3333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.77160856406601" x2="16.77160856406601" y1="217.5" y2="242.3333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="182.700608564066" y1="217.5" y2="242.3333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="348.629608564066" y1="217.5" y2="217.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="348.629608564066" y1="242.3333" y2="242.3333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="182.700608564066" y1="217.5" y2="242.3333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.629608564066" x2="348.629608564066" y1="217.5" y2="242.3333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.77160856406601" x2="182.700608564066" y1="242.3333" y2="242.3333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.77160856406601" x2="182.700608564066" y1="267.1666" y2="267.1666"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.77160856406601" x2="16.77160856406601" y1="242.3333" y2="267.1666"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="182.700608564066" y1="242.3333" y2="267.1666"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="348.629608564066" y1="242.3333" y2="242.3333"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="348.629608564066" y1="267.1666" y2="267.1666"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="182.700608564066" y1="242.3333" y2="267.1666"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.629608564066" x2="348.629608564066" y1="242.3333" y2="267.1666"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.77160856406601" x2="182.700608564066" y1="267.1667" y2="267.1667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.77160856406601" x2="182.700608564066" y1="292" y2="292"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.77160856406601" x2="16.77160856406601" y1="267.1667" y2="292"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="182.700608564066" y1="267.1667" y2="292"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="348.629608564066" y1="267.1667" y2="267.1667"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="348.629608564066" y1="292" y2="292"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.700608564066" x2="182.700608564066" y1="267.1667" y2="292"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.629608564066" x2="348.629608564066" y1="267.1667" y2="292"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="530" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,393.479,350.375) scale(1,1) translate(0,0)" writing-mode="lr" x="393.48" xml:space="preserve" y="354.88" zvalue="8775">110kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" x="732.515625" xml:space="preserve" y="576.6244424338935" zvalue="9330">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="732.515625" xml:space="preserve" y="592.6244424338935" zvalue="9330">63000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="854" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,651.715,465.04) scale(1,1) translate(0,0)" writing-mode="lr" x="651.72" xml:space="preserve" y="469.54" zvalue="9770">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1240.63,446.197) scale(1,1) translate(0,0)" writing-mode="lr" x="1240.63" xml:space="preserve" y="450.7" zvalue="10385">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1241.64,507.649) scale(1,1) translate(0,0)" writing-mode="lr" x="1241.64" xml:space="preserve" y="512.15" zvalue="10389">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1186.16,496.206) scale(1,1) translate(0,1.08292e-13)" writing-mode="lr" x="1186.16" xml:space="preserve" y="500.71" zvalue="10393">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1185,427.77) scale(1,1) translate(0,0)" writing-mode="lr" x="1185" xml:space="preserve" y="432.27" zvalue="10395">27</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1128.42,324.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1128.42" xml:space="preserve" y="329.06" zvalue="10407">35kVⅠ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1127.81,341.506) scale(1,1) translate(0,0)" writing-mode="lr" x="1127.81" xml:space="preserve" y="346.01" zvalue="10408">35kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1211.94,364.378) scale(1,1) translate(0,0)" writing-mode="lr" x="1211.94" xml:space="preserve" y="368.88" zvalue="10415">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1264.61,364.378) scale(1,1) translate(0,0)" writing-mode="lr" x="1264.61" xml:space="preserve" y="368.88" zvalue="10417">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,646.265,294.466) scale(1,1) translate(0,0)" writing-mode="lr" x="646.27" xml:space="preserve" y="298.97" zvalue="10423">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,534.33,424.481) scale(1,1) translate(0,0)" writing-mode="lr" x="534.33" xml:space="preserve" y="428.98" zvalue="10472">112</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="528" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,528.665,374.26) scale(1,1) translate(0,0)" writing-mode="lr" x="528.67" xml:space="preserve" y="378.76" zvalue="10474">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,564.349,460.068) scale(1,1) translate(0,0)" writing-mode="lr" x="564.35" xml:space="preserve" y="464.57" zvalue="10478">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,509.676,459.361) scale(1,1) translate(0,0)" writing-mode="lr" x="509.68" xml:space="preserve" y="463.86" zvalue="10480">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,573.728,375.758) scale(1,1) translate(0,0)" writing-mode="lr" x="573.73" xml:space="preserve" y="380.26" zvalue="10484">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,665.691,362.711) scale(1,1) translate(0,0)" writing-mode="lr" x="665.6900000000001" xml:space="preserve" y="367.21" zvalue="10498">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,722.362,362.711) scale(1,1) translate(0,0)" writing-mode="lr" x="722.36" xml:space="preserve" y="367.21" zvalue="10500">2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="303" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1346.49,76.3248) scale(1,1) translate(0,0)" writing-mode="lr" x="1346.49" xml:space="preserve" y="80.81999999999999" zvalue="10671">35kV模拟A线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1367.45,238.245) scale(1,1) translate(0,0)" writing-mode="lr" x="1367.45" xml:space="preserve" y="242.75" zvalue="10673">342</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="301" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1363.13,181.894) scale(1,1) translate(0,0)" writing-mode="lr" x="1363.13" xml:space="preserve" y="186.39" zvalue="10675">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="298" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1319.38,162.215) scale(1,1) translate(0,0)" writing-mode="lr" x="1319.38" xml:space="preserve" y="166.71" zvalue="10677">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="296" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1355.07,299.841) scale(1,1) translate(0,0)" writing-mode="lr" x="1355.07" xml:space="preserve" y="304.34" zvalue="10680">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="295" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1314.63,220.135) scale(1,1) translate(0,0)" writing-mode="lr" x="1314.63" xml:space="preserve" y="224.63" zvalue="10683">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="290" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1315.24,282.808) scale(1,1) translate(0,0)" writing-mode="lr" x="1315.24" xml:space="preserve" y="287.31" zvalue="10686">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1395.78,300.133) scale(1,1) translate(0,0)" writing-mode="lr" x="1395.78" xml:space="preserve" y="304.63" zvalue="10693">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1570.43,431.697) scale(1,1) translate(0,0)" writing-mode="lr" x="1570.43" xml:space="preserve" y="436.2" zvalue="10789">312</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="527" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1557.44,375.926) scale(1,1) translate(0,0)" writing-mode="lr" x="1557.44" xml:space="preserve" y="380.43" zvalue="10791">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="513" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1597.65,461.735) scale(1,1) translate(0,0)" writing-mode="lr" x="1597.65" xml:space="preserve" y="466.23" zvalue="10795">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="512" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1542.98,461.027) scale(1,1) translate(0,0)" writing-mode="lr" x="1542.98" xml:space="preserve" y="465.53" zvalue="10797">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="511" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1614.83,377.425) scale(1,1) translate(0,0)" writing-mode="lr" x="1614.83" xml:space="preserve" y="381.92" zvalue="10801">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="660" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,706.361,639.8) scale(1,1) translate(0,0)" writing-mode="lr" x="706.36" xml:space="preserve" y="644.3" zvalue="10917">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="661" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,698.549,673.344) scale(1,1) translate(0,0)" writing-mode="lr" x="698.55" xml:space="preserve" y="677.84" zvalue="10918">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="662" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,715.576,605.307) scale(1,1) translate(0,0)" writing-mode="lr" x="715.58" xml:space="preserve" y="609.8099999999999" zvalue="10919">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="670" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,725.363,768.846) scale(1,1) translate(0,0)" writing-mode="lr" x="725.36" xml:space="preserve" y="773.35" zvalue="10925">045</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="669" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,715.292,727.088) scale(1,1) translate(4.66106e-13,0)" writing-mode="lr" x="715.29" xml:space="preserve" y="731.59" zvalue="10926">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="668" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,712.549,810.33) scale(1,1) translate(0,0)" writing-mode="lr" x="712.55" xml:space="preserve" y="814.83" zvalue="10927">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="678" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,709.512,878.374) scale(1,1) translate(-6.16265e-13,0)" writing-mode="lr" x="709.51" xml:space="preserve" y="882.87" zvalue="10937">8</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="677" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,700.427,944.763) scale(1,1) translate(0,0)" writing-mode="lr" x="700.4299999999999" xml:space="preserve" y="949.26" zvalue="10940">10kV模拟A线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1463.63,76.2657) scale(1,1) translate(-9.48662e-13,0)" writing-mode="lr" x="1463.63" xml:space="preserve" y="80.77" zvalue="11248">35kV模拟B线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1484.64,238.186) scale(1,1) translate(0,0)" writing-mode="lr" x="1484.64" xml:space="preserve" y="242.69" zvalue="11250">347</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1477.11,178.502) scale(1,1) translate(0,0)" writing-mode="lr" x="1477.11" xml:space="preserve" y="183" zvalue="11252">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1437.52,162.156) scale(1,1) translate(0,0)" writing-mode="lr" x="1437.52" xml:space="preserve" y="166.66" zvalue="11254">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1471.37,299.782) scale(1,1) translate(0,0)" writing-mode="lr" x="1471.37" xml:space="preserve" y="304.28" zvalue="11257">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1436.77,220.076) scale(1,1) translate(0,0)" writing-mode="lr" x="1436.77" xml:space="preserve" y="224.58" zvalue="11260">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1437.38,282.749) scale(1,1) translate(0,0)" writing-mode="lr" x="1437.38" xml:space="preserve" y="287.25" zvalue="11263">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1513.45,300.074) scale(1,1) translate(0,0)" writing-mode="lr" x="1513.45" xml:space="preserve" y="304.57" zvalue="11270">2</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.70827253600123" x2="45.36397253600126" y1="457.6944" y2="457.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.70827253600123" x2="45.36397253600126" y1="490.287" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.70827253600123" x2="10.70827253600123" y1="457.6944" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36397253600126" x2="45.36397253600126" y1="457.6944" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36427253600118" x2="93.37537253600112" y1="457.6944" y2="457.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36427253600118" x2="93.37537253600112" y1="490.287" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36427253600118" x2="45.36427253600118" y1="457.6944" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37537253600112" x2="93.37537253600112" y1="457.6944" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37517253600117" x2="143.4508725360012" y1="457.6944" y2="457.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37517253600117" x2="143.4508725360012" y1="490.287" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37517253600117" x2="93.37517253600117" y1="457.6944" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="143.4508725360012" y1="457.6944" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="194.7785725360012" y1="457.6944" y2="457.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="194.7785725360012" y1="490.287" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="143.4508725360012" y1="457.6944" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="194.7785725360012" y1="457.6944" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="247.1855725360012" y1="457.6944" y2="457.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="247.1855725360012" y1="490.287" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="194.7785725360012" y1="457.6944" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="247.1855725360012" y1="457.6944" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="299.5925725360012" y1="457.6944" y2="457.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="299.5925725360012" y1="490.287" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="247.1855725360012" y1="457.6944" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5925725360012" x2="299.5925725360012" y1="457.6944" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5932725360012" x2="352.0002725360011" y1="457.6944" y2="457.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5932725360012" x2="352.0002725360011" y1="490.287" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5932725360012" x2="299.5932725360012" y1="457.6944" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="352.0002725360011" x2="352.0002725360011" y1="457.6944" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.70827253600123" x2="45.36397253600126" y1="490.287" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.70827253600123" x2="45.36397253600126" y1="513.9906999999999" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.70827253600123" x2="10.70827253600123" y1="490.287" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36397253600126" x2="45.36397253600126" y1="490.287" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36427253600118" x2="93.37537253600112" y1="490.287" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36427253600118" x2="93.37537253600112" y1="513.9906999999999" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36427253600118" x2="45.36427253600118" y1="490.287" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37537253600112" x2="93.37537253600112" y1="490.287" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37517253600117" x2="143.4508725360012" y1="490.287" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37517253600117" x2="143.4508725360012" y1="513.9906999999999" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37517253600117" x2="93.37517253600117" y1="490.287" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="143.4508725360012" y1="490.287" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="194.7785725360012" y1="490.287" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="194.7785725360012" y1="513.9906999999999" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="143.4508725360012" y1="490.287" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="194.7785725360012" y1="490.287" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="247.1855725360012" y1="490.287" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="247.1855725360012" y1="513.9906999999999" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="194.7785725360012" y1="490.287" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="247.1855725360012" y1="490.287" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="299.5925725360012" y1="490.287" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="299.5925725360012" y1="513.9906999999999" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="247.1855725360012" y1="490.287" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5925725360012" x2="299.5925725360012" y1="490.287" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5932725360012" x2="352.0002725360011" y1="490.287" y2="490.287"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5932725360012" x2="352.0002725360011" y1="513.9906999999999" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5932725360012" x2="299.5932725360012" y1="490.287" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="352.0002725360011" x2="352.0002725360011" y1="490.287" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.70827253600123" x2="45.36397253600126" y1="513.9906999999999" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.70827253600123" x2="45.36397253600126" y1="537.6944" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.70827253600123" x2="10.70827253600123" y1="513.9906999999999" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36397253600126" x2="45.36397253600126" y1="513.9906999999999" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36427253600118" x2="93.37537253600112" y1="513.9906999999999" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36427253600118" x2="93.37537253600112" y1="537.6944" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36427253600118" x2="45.36427253600118" y1="513.9906999999999" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37537253600112" x2="93.37537253600112" y1="513.9906999999999" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37517253600117" x2="143.4508725360012" y1="513.9906999999999" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37517253600117" x2="143.4508725360012" y1="537.6944" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37517253600117" x2="93.37517253600117" y1="513.9906999999999" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="143.4508725360012" y1="513.9906999999999" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="194.7785725360012" y1="513.9906999999999" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="194.7785725360012" y1="537.6944" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="143.4508725360012" y1="513.9906999999999" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="194.7785725360012" y1="513.9906999999999" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="247.1855725360012" y1="513.9906999999999" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="247.1855725360012" y1="537.6944" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="194.7785725360012" y1="513.9906999999999" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="247.1855725360012" y1="513.9906999999999" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="299.5925725360012" y1="513.9906999999999" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="299.5925725360012" y1="537.6944" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="247.1855725360012" y1="513.9906999999999" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5925725360012" x2="299.5925725360012" y1="513.9906999999999" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5932725360012" x2="352.0002725360011" y1="513.9906999999999" y2="513.9906999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5932725360012" x2="352.0002725360011" y1="537.6944" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5932725360012" x2="299.5932725360012" y1="513.9906999999999" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="352.0002725360011" x2="352.0002725360011" y1="513.9906999999999" y2="537.6944"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.70827253600123" x2="45.36397253600126" y1="537.69444" y2="537.69444"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.70827253600123" x2="45.36397253600126" y1="561.39814" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.70827253600123" x2="10.70827253600123" y1="537.69444" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36397253600126" x2="45.36397253600126" y1="537.69444" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36427253600118" x2="93.37537253600112" y1="537.69444" y2="537.69444"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36427253600118" x2="93.37537253600112" y1="561.39814" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36427253600118" x2="45.36427253600118" y1="537.69444" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37537253600112" x2="93.37537253600112" y1="537.69444" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37517253600117" x2="143.4508725360012" y1="537.69444" y2="537.69444"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37517253600117" x2="143.4508725360012" y1="561.39814" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37517253600117" x2="93.37517253600117" y1="537.69444" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="143.4508725360012" y1="537.69444" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="194.7785725360012" y1="537.69444" y2="537.69444"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="194.7785725360012" y1="561.39814" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="143.4508725360012" y1="537.69444" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="194.7785725360012" y1="537.69444" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="247.1855725360012" y1="537.69444" y2="537.69444"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="247.1855725360012" y1="561.39814" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="194.7785725360012" y1="537.69444" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="247.1855725360012" y1="537.69444" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="299.5925725360012" y1="537.69444" y2="537.69444"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="299.5925725360012" y1="561.39814" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="247.1855725360012" y1="537.69444" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5925725360012" x2="299.5925725360012" y1="537.69444" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5932725360012" x2="352.0002725360011" y1="537.69444" y2="537.69444"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5932725360012" x2="352.0002725360011" y1="561.39814" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5932725360012" x2="299.5932725360012" y1="537.69444" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="352.0002725360011" x2="352.0002725360011" y1="537.69444" y2="561.39814"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.70827253600123" x2="45.36397253600126" y1="561.3981" y2="561.3981"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.70827253600123" x2="45.36397253600126" y1="585.1018" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.70827253600123" x2="10.70827253600123" y1="561.3981" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36397253600126" x2="45.36397253600126" y1="561.3981" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36427253600118" x2="93.37537253600112" y1="561.3981" y2="561.3981"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36427253600118" x2="93.37537253600112" y1="585.1018" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36427253600118" x2="45.36427253600118" y1="561.3981" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37537253600112" x2="93.37537253600112" y1="561.3981" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37517253600117" x2="143.4508725360012" y1="561.3981" y2="561.3981"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37517253600117" x2="143.4508725360012" y1="585.1018" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37517253600117" x2="93.37517253600117" y1="561.3981" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="143.4508725360012" y1="561.3981" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="194.7785725360012" y1="561.3981" y2="561.3981"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="194.7785725360012" y1="585.1018" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="143.4508725360012" y1="561.3981" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="194.7785725360012" y1="561.3981" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="247.1855725360012" y1="561.3981" y2="561.3981"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="247.1855725360012" y1="585.1018" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="194.7785725360012" y1="561.3981" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="247.1855725360012" y1="561.3981" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="299.5925725360012" y1="561.3981" y2="561.3981"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="299.5925725360012" y1="585.1018" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="247.1855725360012" y1="561.3981" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5925725360012" x2="299.5925725360012" y1="561.3981" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5932725360012" x2="352.0002725360011" y1="561.3981" y2="561.3981"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5932725360012" x2="352.0002725360011" y1="585.1018" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5932725360012" x2="299.5932725360012" y1="561.3981" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="352.0002725360011" x2="352.0002725360011" y1="561.3981" y2="585.1018"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.70827253600123" x2="45.36397253600126" y1="585.1019" y2="585.1019"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.70827253600123" x2="45.36397253600126" y1="608.8056" y2="608.8056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.70827253600123" x2="10.70827253600123" y1="585.1019" y2="608.8056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36397253600126" x2="45.36397253600126" y1="585.1019" y2="608.8056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36427253600118" x2="93.37537253600112" y1="585.1019" y2="585.1019"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36427253600118" x2="93.37537253600112" y1="608.8056" y2="608.8056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45.36427253600118" x2="45.36427253600118" y1="585.1019" y2="608.8056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37537253600112" x2="93.37537253600112" y1="585.1019" y2="608.8056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37517253600117" x2="143.4508725360012" y1="585.1019" y2="585.1019"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37517253600117" x2="143.4508725360012" y1="608.8056" y2="608.8056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="93.37517253600117" x2="93.37517253600117" y1="585.1019" y2="608.8056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="143.4508725360012" y1="585.1019" y2="608.8056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="194.7785725360012" y1="585.1019" y2="585.1019"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="194.7785725360012" y1="608.8056" y2="608.8056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="143.4508725360012" x2="143.4508725360012" y1="585.1019" y2="608.8056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="194.7785725360012" y1="585.1019" y2="608.8056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="247.1855725360012" y1="585.1019" y2="585.1019"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="247.1855725360012" y1="608.8056" y2="608.8056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194.7785725360012" x2="194.7785725360012" y1="585.1019" y2="608.8056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="247.1855725360012" y1="585.1019" y2="608.8056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="299.5925725360012" y1="585.1019" y2="585.1019"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="299.5925725360012" y1="608.8056" y2="608.8056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="247.1855725360012" x2="247.1855725360012" y1="585.1019" y2="608.8056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5925725360012" x2="299.5925725360012" y1="585.1019" y2="608.8056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5932725360012" x2="352.0002725360011" y1="585.1019" y2="585.1019"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5932725360012" x2="352.0002725360011" y1="608.8056" y2="608.8056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="299.5932725360012" x2="299.5932725360012" y1="585.1019" y2="608.8056"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="352.0002725360011" x2="352.0002725360011" y1="585.1019" y2="608.8056"/>
  <line fill="none" id="464" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.70854507200272" x2="354.4389508529936" y1="630.1204926140824" y2="630.1204926140824" zvalue="11354"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="463" stroke="rgb(255,255,255)" text-anchor="middle" x="66.296875" xml:space="preserve" y="472.9409722222222" zvalue="11355">110kV    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="463" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="66.296875" xml:space="preserve" y="488.9409722222222" zvalue="11355">Ⅰ母</text>
  <line fill="none" id="460" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.13711650057439" x2="356.4389508529937" y1="139.1204926140824" y2="139.1204926140824" zvalue="11358"/>
  <line fill="none" id="459" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="360.7125133259707" x2="360.7125133259707" y1="11" y2="1038.5" zvalue="11359"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="456" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50.8007,156.091) scale(1,1) translate(0,0)" writing-mode="lr" x="50.8" xml:space="preserve" y="160.59" zvalue="11361">有功总加</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="453" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,275.472,318.539) scale(1,1) translate(0,0)" writing-mode="lr" x="275.47" xml:space="preserve" y="323.04" zvalue="11364">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="452" stroke="rgb(255,255,255)" text-anchor="middle" x="115.109375" xml:space="preserve" y="472.9409722222222" zvalue="11366">110kV    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="452" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="115.109375" xml:space="preserve" y="488.9409722222222" zvalue="11366">Ⅱ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="449" stroke="rgb(255,255,255)" text-anchor="middle" x="170.390625" xml:space="preserve" y="472.9409722222222" zvalue="11369">35kV    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="449" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="170.390625" xml:space="preserve" y="488.9409722222222" zvalue="11369">Ⅰ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="448" stroke="rgb(255,255,255)" text-anchor="middle" x="223.96875" xml:space="preserve" y="472.9409722222222" zvalue="11370">35kV    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="448" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="223.96875" xml:space="preserve" y="488.9409722222222" zvalue="11370">Ⅱ母</text>
  <text fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="447" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,30.5271,503.59) scale(1,1) translate(0,0)" writing-mode="lr" x="30.53" xml:space="preserve" y="508.09" zvalue="11371">Uab</text>
  <text fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="446" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,27.8327,527.367) scale(1,1) translate(0,0)" writing-mode="lr" x="27.83" xml:space="preserve" y="531.87" zvalue="11372">Ua</text>
  <text fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="428" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,30.7216,550.701) scale(1,1) translate(0,0)" writing-mode="lr" x="30.72" xml:space="preserve" y="555.2" zvalue="11373">Ub</text>
  <text fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="421" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,29.6105,598.479) scale(1,1) translate(0,0)" writing-mode="lr" x="29.61" xml:space="preserve" y="602.98" zvalue="11374">U0</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="419" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,104.351,654.75) scale(1,1) translate(0,0)" writing-mode="lr" x="104.3514022148597" xml:space="preserve" y="659.25" zvalue="11382">危险点(双击编辑）：</text>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.70897364343125" x2="97.49467364343127" y1="942.2499499999999" y2="942.2499499999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.70897364343125" x2="97.49467364343127" y1="984.2499499999999" y2="984.2499499999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.70897364343125" x2="14.70897364343125" y1="942.2499499999999" y2="984.2499499999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.49467364343127" x2="97.49467364343127" y1="942.2499499999999" y2="984.2499499999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.49427364343126" x2="345.8512736434313" y1="942.2499499999999" y2="942.2499499999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.49427364343126" x2="345.8512736434313" y1="984.2499499999999" y2="984.2499499999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.49427364343126" x2="97.49427364343126" y1="942.2499499999999" y2="984.2499499999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="345.8512736434313" x2="345.8512736434313" y1="942.2499499999999" y2="984.2499499999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.70897364343125" x2="97.49467364343127" y1="984.2499699999998" y2="984.2499699999998"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.70897364343125" x2="97.49467364343127" y1="1012.16837" y2="1012.16837"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.70897364343125" x2="14.70897364343125" y1="984.2499699999998" y2="1012.16837"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.49467364343127" x2="97.49467364343127" y1="984.2499699999998" y2="1012.16837"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.49427364343126" x2="180.2799736434313" y1="984.2499699999998" y2="984.2499699999998"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.49427364343126" x2="180.2799736434313" y1="1012.16837" y2="1012.16837"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.49427364343126" x2="97.49427364343126" y1="984.2499699999998" y2="1012.16837"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.2799736434313" x2="180.2799736434313" y1="984.2499699999998" y2="1012.16837"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.2799736434314" x2="263.0656736434314" y1="984.2499699999998" y2="984.2499699999998"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.2799736434314" x2="263.0656736434314" y1="1012.16837" y2="1012.16837"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.2799736434314" x2="180.2799736434314" y1="984.2499699999998" y2="1012.16837"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="263.0656736434314" x2="263.0656736434314" y1="984.2499699999998" y2="1012.16837"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="263.0656736434313" x2="345.8513736434313" y1="984.2499699999998" y2="984.2499699999998"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="263.0656736434313" x2="345.8513736434313" y1="1012.16837" y2="1012.16837"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="263.0656736434313" x2="263.0656736434313" y1="984.2499699999998" y2="1012.16837"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="345.8513736434313" x2="345.8513736434313" y1="984.2499699999998" y2="1012.16837"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.70897364343125" x2="97.49467364343127" y1="1012.16835" y2="1012.16835"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.70897364343125" x2="97.49467364343127" y1="1040.08675" y2="1040.08675"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.70897364343125" x2="14.70897364343125" y1="1012.16835" y2="1040.08675"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.49467364343127" x2="97.49467364343127" y1="1012.16835" y2="1040.08675"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.49427364343126" x2="180.2799736434313" y1="1012.16835" y2="1012.16835"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.49427364343126" x2="180.2799736434313" y1="1040.08675" y2="1040.08675"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="97.49427364343126" x2="97.49427364343126" y1="1012.16835" y2="1040.08675"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.2799736434313" x2="180.2799736434313" y1="1012.16835" y2="1040.08675"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.2799736434314" x2="263.0656736434314" y1="1012.16835" y2="1012.16835"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.2799736434314" x2="263.0656736434314" y1="1040.08675" y2="1040.08675"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="180.2799736434314" x2="180.2799736434314" y1="1012.16835" y2="1040.08675"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="263.0656736434314" x2="263.0656736434314" y1="1012.16835" y2="1040.08675"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="263.0656736434313" x2="345.8513736434313" y1="1012.16835" y2="1012.16835"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="263.0656736434313" x2="345.8513736434313" y1="1040.08675" y2="1040.08675"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="263.0656736434313" x2="263.0656736434313" y1="1012.16835" y2="1040.08675"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="345.8513736434313" x2="345.8513736434313" y1="1012.16835" y2="1040.08675"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="417" stroke="rgb(255,255,255)" text-anchor="middle" x="56.359375" xml:space="preserve" y="960.75" zvalue="11384">参考     图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="417" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="56.359375" xml:space="preserve" y="977.75" zvalue="11384">号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="409" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.3514,998.25) scale(1,1) translate(0,0)" writing-mode="lr" x="57.35" xml:space="preserve" y="1004.25" zvalue="11385">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="406" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,219.851,1000.25) scale(1,1) translate(0,0)" writing-mode="lr" x="219.85" xml:space="preserve" y="1006.25" zvalue="11386">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="404" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.3514,1029.25) scale(1,1) translate(0,0)" writing-mode="lr" x="55.35" xml:space="preserve" y="1035.25" zvalue="11387">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="375" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,218.851,1028.25) scale(1,1) translate(0,0)" writing-mode="lr" x="218.85" xml:space="preserve" y="1034.25" zvalue="11388">更新日期</text>
  <text fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="373" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,31.2771,574.034) scale(1,1) translate(0,0)" writing-mode="lr" x="31.28" xml:space="preserve" y="578.53" zvalue="11390">Uc</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="372" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,220.245,158.314) scale(1,1) translate(0,0)" writing-mode="lr" x="220.25" xml:space="preserve" y="162.81" zvalue="11392">无功总加</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="347" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.3007,229.258) scale(1,1) translate(0,0)" writing-mode="lr" x="60.3" xml:space="preserve" y="233.76" zvalue="11395">10kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="346" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231.495,180.953) scale(1,1) translate(0,0)" writing-mode="lr" x="231.5" xml:space="preserve" y="185.45" zvalue="11396">110kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="345" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.2451,183.203) scale(1,1) translate(0,0)" writing-mode="lr" x="62.25" xml:space="preserve" y="187.7" zvalue="11397">110kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="344" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.051,204.425) scale(1,1) translate(0,0)" writing-mode="lr" x="232.05" xml:space="preserve" y="208.92" zvalue="11398">35kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="342" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.8007,205.425) scale(1,1) translate(0,0)" writing-mode="lr" x="62.8" xml:space="preserve" y="209.92" zvalue="11399">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="336" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.3236,254.434) scale(1,1) translate(0,0)" writing-mode="lr" x="56.32" xml:space="preserve" y="258.93" zvalue="11400">#1主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="186" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.75,254.903) scale(1,1) translate(0,0)" writing-mode="lr" x="226.75" xml:space="preserve" y="259.4" zvalue="11442">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="703" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1623.28,647.067) scale(1,1) translate(0,0)" writing-mode="lr" x="1623.28" xml:space="preserve" y="651.5700000000001" zvalue="11557">002</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="875" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1616.26,679.243) scale(1,1) translate(0,0)" writing-mode="lr" x="1616.26" xml:space="preserve" y="683.74" zvalue="11559">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="702" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1615.02,617.839) scale(1,1) translate(0,0)" writing-mode="lr" x="1615.02" xml:space="preserve" y="622.34" zvalue="11560">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="701" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1860.86,675.492) scale(1,1) translate(0,0)" writing-mode="lr" x="1860.86" xml:space="preserve" y="679.99" zvalue="11565">10kVⅡ母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1498.91,787.843) scale(1,1) translate(0,0)" writing-mode="lr" x="1498.91" xml:space="preserve" y="792.34" zvalue="11607">012</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="697" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1477.49,732.331) scale(1,1) translate(0,0)" writing-mode="lr" x="1477.49" xml:space="preserve" y="736.83" zvalue="11609">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="696" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1542.85,731.953) scale(1,1) translate(0,0)" writing-mode="lr" x="1542.85" xml:space="preserve" y="736.45" zvalue="11611">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="807" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1694.24,447.31) scale(1,1) translate(0,0)" writing-mode="lr" x="1694.24" xml:space="preserve" y="451.81" zvalue="11648">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="806" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1695.22,506.374) scale(1,1) translate(0,0)" writing-mode="lr" x="1695.22" xml:space="preserve" y="510.87" zvalue="11650">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="805" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1642.89,494.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1642.89" xml:space="preserve" y="498.87" zvalue="11654">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="804" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1640.77,429.6) scale(1,1) translate(0,0)" writing-mode="lr" x="1640.77" xml:space="preserve" y="434.1" zvalue="11657">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="803" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1666.67,368.672) scale(1,1) translate(0,0)" writing-mode="lr" x="1666.67" xml:space="preserve" y="373.17" zvalue="11660">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="802" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1717.29,368.672) scale(1,1) translate(0,0)" writing-mode="lr" x="1717.29" xml:space="preserve" y="373.17" zvalue="11663">2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="885" stroke="rgb(255,255,255)" text-anchor="middle" x="1008.4453125" xml:space="preserve" y="561.6127879734472" zvalue="11667">#2主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="885" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1008.4453125" xml:space="preserve" y="577.6127879734472" zvalue="11667">63000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="861" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,967.627,419.559) scale(1,1) translate(0,0)" writing-mode="lr" x="967.63" xml:space="preserve" y="424.06" zvalue="11671">102</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="857" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,962.414,462.681) scale(1,1) translate(0,0)" writing-mode="lr" x="962.41" xml:space="preserve" y="467.18" zvalue="11673">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="853" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,913.963,416.712) scale(1,1) translate(0,0)" writing-mode="lr" x="913.96" xml:space="preserve" y="421.21" zvalue="11676">27</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="850" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,913.051,455.555) scale(1,1) translate(0,0)" writing-mode="lr" x="913.05" xml:space="preserve" y="460.06" zvalue="11680">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="849" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,930.85,361.505) scale(1,1) translate(1.01902e-13,0)" writing-mode="lr" x="930.85" xml:space="preserve" y="366" zvalue="11684">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="848" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,981.474,361.505) scale(1,1) translate(-2.14933e-13,0)" writing-mode="lr" x="981.47" xml:space="preserve" y="366" zvalue="11686">2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="894" stroke="rgb(255,255,255)" text-anchor="middle" x="274.390625" xml:space="preserve" y="472.9409722222222" zvalue="11715">10kV    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="894" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="274.390625" xml:space="preserve" y="488.9409722222222" zvalue="11715">Ⅰ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="896" stroke="rgb(255,255,255)" text-anchor="middle" x="323.96875" xml:space="preserve" y="472.9409722222222" zvalue="11719">10kV    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="896" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="323.96875" xml:space="preserve" y="488.9409722222222" zvalue="11719">Ⅱ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="904" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232.051,228.425) scale(1,1) translate(0,0)" writing-mode="lr" x="232.05" xml:space="preserve" y="232.92" zvalue="11727">10kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="907" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.3236,278.434) scale(1,1) translate(0,-3.57625e-13)" writing-mode="lr" x="56.32" xml:space="preserve" y="282.93" zvalue="11731">#2主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="909" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.75,278.903) scale(1,1) translate(0,0)" writing-mode="lr" x="226.75" xml:space="preserve" y="283.4" zvalue="11732">2号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="864" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.5,964.5) scale(1,1) translate(0,0)" writing-mode="lr" x="222.5" xml:space="preserve" y="969" zvalue="11735">XuanGang-01-2022</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="912" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,304,1000) scale(1,1) translate(0,0)" writing-mode="lr" x="304" xml:space="preserve" y="1004.5" zvalue="11737">2024-07-30</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="913" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,911.764,491.064) scale(1,1) translate(0,0)" writing-mode="lr" x="911.76" xml:space="preserve" y="495.56" zvalue="11740">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="977" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,158.028,318.539) scale(1,1) translate(0,0)" writing-mode="lr" x="158.03" xml:space="preserve" y="323.04" zvalue="11822">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1083" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,279.235,408) scale(1,1) translate(0,-1.75859e-13)" writing-mode="lr" x="279.2350158691406" xml:space="preserve" y="412.5" zvalue="12069">小电流接地</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="629" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63.2857,318.539) scale(1,1) translate(0,3.41327e-13)" writing-mode="lr" x="63.28570556640625" xml:space="preserve" y="323.0388641357421" zvalue="12070">全站公用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1145" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,586.741,540.222) scale(1,1) translate(0,-1.18177e-13)" writing-mode="lr" x="586.74" xml:space="preserve" y="544.72" zvalue="12144">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1148" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,873.407,539.111) scale(1,1) translate(0,0)" writing-mode="lr" x="873.41" xml:space="preserve" y="543.61" zvalue="12147">1020</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1710.36,768.846) scale(1,1) translate(0,0)" writing-mode="lr" x="1710.36" xml:space="preserve" y="773.35" zvalue="12151">046</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1155" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1700.29,727.088) scale(1,1) translate(-3.36674e-12,0)" writing-mode="lr" x="1700.29" xml:space="preserve" y="731.59" zvalue="12153">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1722.55,815.785) scale(1,1) translate(0,0)" writing-mode="lr" x="1722.55" xml:space="preserve" y="820.28" zvalue="12155">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1150" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1694.51,878.374) scale(1,1) translate(1.67751e-12,0)" writing-mode="lr" x="1694.51" xml:space="preserve" y="882.87" zvalue="12159">8</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1685.43,944.763) scale(1,1) translate(0,0)" writing-mode="lr" x="1685.43" xml:space="preserve" y="949.26" zvalue="12162">10kV模拟B线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,305,1028) scale(1,1) translate(0,0)" writing-mode="lr" x="305" xml:space="preserve" y="1032.5" zvalue="12170">2024-07-30</text>
 </g>
 <g id="ButtonClass">
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="26.85" y="351.27" zvalue="11522"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="26.85" y="396" zvalue="11787"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="132.84" y="396" zvalue="11788"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="1430">
   <use class="kv110" height="30" transform="rotate(0,599.81,89.4905) scale(1.88213,0.496271) translate(-278.036,83.2795)" width="7" x="593.2220938521705" xlink:href="#ACLineSegment:线路_0" y="82.04642003124678" zvalue="7513"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450376040453" ObjectName="110kV模拟A线"/>
   <cge:TPSR_Ref TObjectID="6192450376040453_5066549597110274"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,599.81,89.4905) scale(1.88213,0.496271) translate(-278.036,83.2795)" width="7" x="593.2220938521705" y="82.04642003124678"/></g>
 </g>
 <g id="BreakerClass">
  <g id="1429">
   <use class="kv110" height="20" transform="rotate(0,599.799,232.935) scale(1.47498,1.28518) translate(-190.774,-48.8363)" width="10" x="592.4236846386799" xlink:href="#Breaker:开关_0" y="220.0830822006736" zvalue="7514"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924601315332" ObjectName="110kV模拟A线144断路器"/>
   <cge:TPSR_Ref TObjectID="6473924601315332"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,599.799,232.935) scale(1.47498,1.28518) translate(-190.774,-48.8363)" width="10" x="592.4236846386799" y="220.0830822006736"/></g>
  <g id="276">
   <use class="kv110" height="20" transform="rotate(180,686.312,426.919) scale(1.47498,1.28518) translate(-218.633,-91.8812)" width="10" x="678.9374813792983" xlink:href="#Breaker:开关_0" y="414.0673217609358" zvalue="7802"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924601380868" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473924601380868"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,686.312,426.919) scale(1.47498,1.28518) translate(-218.633,-91.8812)" width="10" x="678.9374813792983" y="414.0673217609358"/></g>
  <g id="495">
   <use class="kv35" height="20" transform="rotate(0,1222.01,448.668) scale(1.47498,1.28518) translate(-391.139,-96.7073)" width="10" x="1214.631117469795" xlink:href="#Breaker:开关_0" y="435.8163027249851" zvalue="10384"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924601511940" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924601511940"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1222.01,448.668) scale(1.47498,1.28518) translate(-391.139,-96.7073)" width="10" x="1214.631117469795" y="435.8163027249851"/></g>
  <g id="150">
   <use class="kv110" height="20" transform="rotate(270,535.853,407.992) scale(1.47498,1.28518) translate(-170.182,-87.6812)" width="10" x="528.4781762568193" xlink:href="#Breaker:母联开关_0" y="395.139829189844" zvalue="10471"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924601446404" ObjectName="110kV母联112断路器"/>
   <cge:TPSR_Ref TObjectID="6473924601446404"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,535.853,407.992) scale(1.47498,1.28518) translate(-170.182,-87.6812)" width="10" x="528.4781762568193" y="395.139829189844"/></g>
  <g id="343">
   <use class="kv35" height="20" transform="rotate(0,1345.99,238.602) scale(1.47498,1.28518) translate(-431.066,-50.0937)" width="10" x="1338.619719312221" xlink:href="#Breaker:开关_0" y="225.7497488673403" zvalue="10672"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924601577476" ObjectName="35kV模拟A线342断路器"/>
   <cge:TPSR_Ref TObjectID="6473924601577476"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1345.99,238.602) scale(1.47498,1.28518) translate(-431.066,-50.0937)" width="10" x="1338.619719312221" y="225.7497488673403"/></g>
  <g id="526">
   <use class="kv35" height="20" transform="rotate(270,1569.16,409.658) scale(1.47498,1.28518) translate(-502.928,-88.051)" width="10" x="1561.780677694349" xlink:href="#Breaker:母联开关_0" y="396.8064958565107" zvalue="10788"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924601643012" ObjectName="35kV母联312断路器"/>
   <cge:TPSR_Ref TObjectID="6473924601643012"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1569.16,409.658) scale(1.47498,1.28518) translate(-502.928,-88.051)" width="10" x="1561.780677694349" y="396.8064958565107"/></g>
  <g id="654">
   <use class="kv10" height="20" transform="rotate(0,684.988,639.998) scale(1.47498,1.28518) translate(-218.207,-139.163)" width="10" x="677.6127972116726" xlink:href="#Breaker:开关_0" y="627.1464916373062" zvalue="10916"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924601708548" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924601708548"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,684.988,639.998) scale(1.47498,1.28518) translate(-218.207,-139.163)" width="10" x="677.6127972116726" y="627.1464916373062"/></g>
  <g id="665">
   <use class="kv10" height="20" transform="rotate(0,700.168,767.357) scale(1.47498,1.28518) translate(-223.095,-167.424)" width="10" x="692.7929490148556" xlink:href="#Breaker:开关_0" y="754.5054659962806" zvalue="10924"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924601774084" ObjectName="10kV模拟A线045断路器"/>
   <cge:TPSR_Ref TObjectID="6473924601774084"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,700.168,767.357) scale(1.47498,1.28518) translate(-223.095,-167.424)" width="10" x="692.7929490148556" y="754.5054659962806"/></g>
  <g id="297">
   <use class="kv35" height="20" transform="rotate(0,1463.19,238.543) scale(1.47498,1.28518) translate(-468.804,-50.0806)" width="10" x="1455.811200525886" xlink:href="#Breaker:开关_0" y="225.6906954190663" zvalue="11249"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924601839620" ObjectName="35kV模拟B线347断路器"/>
   <cge:TPSR_Ref TObjectID="6473924601839620"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1463.19,238.543) scale(1.47498,1.28518) translate(-468.804,-50.0806)" width="10" x="1455.811200525886" y="225.6906954190663"/></g>
  <g id="799">
   <use class="kv10" height="20" transform="rotate(0,1602.74,647.257) scale(1.41764,1.23522) translate(-470.08,-120.903)" width="10" x="1595.654598622186" xlink:href="#Breaker:开关_0" y="634.9048202502357" zvalue="11556"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924601970692" ObjectName="#2主变10kV侧002断路器"/>
   <cge:TPSR_Ref TObjectID="6473924601970692"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1602.74,647.257) scale(1.41764,1.23522) translate(-470.08,-120.903)" width="10" x="1595.654598622186" y="634.9048202502357"/></g>
  <g id="970">
   <use class="kv10" height="20" transform="rotate(270,1498.76,765.843) scale(1.41764,1.23522) translate(-439.447,-143.485)" width="10" x="1491.671270904871" xlink:href="#Breaker:母联开关_0" y="753.4904084673499" zvalue="11606"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924601905156" ObjectName="10kV分段012断路器"/>
   <cge:TPSR_Ref TObjectID="6473924601905156"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1498.76,765.843) scale(1.41764,1.23522) translate(-439.447,-143.485)" width="10" x="1491.671270904871" y="753.4904084673499"/></g>
  <g id="837">
   <use class="kv35" height="20" transform="rotate(0,1676.34,449.685) scale(1.41764,1.23522) translate(-491.763,-83.2802)" width="10" x="1669.255461562286" xlink:href="#Breaker:开关_0" y="437.332930381942" zvalue="11647"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924602036228" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473924602036228"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1676.34,449.685) scale(1.41764,1.23522) translate(-491.763,-83.2802)" width="10" x="1669.255461562286" y="437.332930381942"/></g>
  <g id="883">
   <use class="kv110" height="20" transform="rotate(180,946.981,419.217) scale(1.41764,1.23522) translate(-276.893,-77.4782)" width="10" x="939.8932101443459" xlink:href="#Breaker:开关_0" y="406.8646416956206" zvalue="11670"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924602101764" ObjectName="#2主变110kV侧102断路器"/>
   <cge:TPSR_Ref TObjectID="6473924602101764"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,946.981,419.217) scale(1.41764,1.23522) translate(-276.893,-77.4782)" width="10" x="939.8932101443459" y="406.8646416956206"/></g>
  <g id="1190">
   <use class="kv10" height="20" transform="rotate(0,1685.17,767.357) scale(1.47498,1.28518) translate(-540.287,-167.424)" width="10" x="1677.792949014856" xlink:href="#Breaker:开关_0" y="754.5054659962806" zvalue="12149"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924602167300" ObjectName="10kV模拟B线046断路器"/>
   <cge:TPSR_Ref TObjectID="6473924602167300"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1685.17,767.357) scale(1.47498,1.28518) translate(-540.287,-167.424)" width="10" x="1677.792949014856" y="754.5054659962806"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="1428">
   <use class="kv110" height="30" transform="rotate(0,599.81,170.838) scale(-0.899385,0.636986) translate(-1267.48,91.914)" width="15" x="593.0641788441169" xlink:href="#Disconnector:刀闸_0" y="161.2831743628194" zvalue="7516"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450372370437" ObjectName="110kV模拟A线1446隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450372370437"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,599.81,170.838) scale(-0.899385,0.636986) translate(-1267.48,91.914)" width="15" x="593.0641788441169" y="161.2831743628194"/></g>
  <g id="1397">
   <use class="kv110" height="30" transform="rotate(180,599.799,295.415) scale(-0.899385,-0.636986) translate(-1267.45,-764.631)" width="15" x="593.0531733207658" xlink:href="#Disconnector:刀闸_0" y="285.8605795387119" zvalue="7530"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450372304901" ObjectName="110kV模拟A线1441隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450372304901"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,599.799,295.415) scale(-0.899385,-0.636986) translate(-1267.45,-764.631)" width="15" x="593.0531733207658" y="285.8605795387119"/></g>
  <g id="275">
   <use class="kv110" height="30" transform="rotate(180,686.307,466.239) scale(-0.899385,0.636986) translate(-1450.15,260.261)" width="15" x="679.5611141261088" xlink:href="#Disconnector:刀闸_0" y="456.6846304607806" zvalue="7805"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450372435973" ObjectName="#1主变110kV侧1016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450372435973"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,686.307,466.239) scale(-0.899385,0.636986) translate(-1450.15,260.261)" width="15" x="679.5611141261088" y="456.6846304607806"/></g>
  <g id="499">
   <use class="kv35" height="30" transform="rotate(180,1222.01,509.579) scale(-0.899385,-0.636986) translate(-2581.47,-1315.01)" width="15" x="1215.260606184936" xlink:href="#Disconnector:刀闸_0" y="500.0238881466314" zvalue="10388"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450374336517" ObjectName="#1主变35kV侧3016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450374336517"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1222.01,509.579) scale(-0.899385,-0.636986) translate(-2581.47,-1315.01)" width="15" x="1215.260606184936" y="500.0238881466314"/></g>
  <g id="32">
   <use class="kv35" height="30" transform="rotate(180,1195.43,365.327) scale(-0.899385,-0.636986) translate(-2525.36,-944.296)" width="15" x="1188.687886712106" xlink:href="#Disconnector:刀闸_0" y="355.7719824369846" zvalue="10414"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450373943301" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450373943301"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1195.43,365.327) scale(-0.899385,-0.636986) translate(-2525.36,-944.296)" width="15" x="1188.687886712106" y="355.7719824369846"/></g>
  <g id="33">
   <use class="kv35" height="30" transform="rotate(180,1248.58,365.327) scale(-0.899385,-0.636986) translate(-2637.59,-944.296)" width="15" x="1241.833325657765" xlink:href="#Disconnector:刀闸_0" y="355.7719824369846" zvalue="10416"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450373877765" ObjectName="#1主变35kV侧3012隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450373877765"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1248.58,365.327) scale(-0.899385,-0.636986) translate(-2637.59,-944.296)" width="15" x="1241.833325657765" y="355.7719824369846"/></g>
  <g id="97">
   <use class="kv110" height="30" transform="rotate(180,637.76,295.415) scale(-0.899385,-0.636986) translate(-1347.62,-764.631)" width="15" x="631.0142011390942" xlink:href="#Disconnector:刀闸_0" y="285.8605795387119" zvalue="10422"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450372698117" ObjectName="110kV模拟A线1442隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450372698117"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,637.76,295.415) scale(-0.899385,-0.636986) translate(-1347.62,-764.631)" width="15" x="631.0142011390942" y="285.8605795387119"/></g>
  <g id="153">
   <use class="kv110" height="30" transform="rotate(180,509.26,375.209) scale(0.899385,-0.636986) translate(56.2168,-969.691)" width="15" x="502.5143763181045" xlink:href="#Disconnector:刀闸_0" y="365.6538044320872" zvalue="10473"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450373353477" ObjectName="110kV母联1122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450373353477"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,509.26,375.209) scale(0.899385,-0.636986) translate(56.2168,-969.691)" width="15" x="502.5143763181045" y="365.6538044320872"/></g>
  <g id="25">
   <use class="kv110" height="30" transform="rotate(0,564.317,375.601) scale(0.899385,-0.636986) translate(62.376,-970.7)" width="15" x="557.5713396784201" xlink:href="#Disconnector:刀闸_0" y="366.0463469131837" zvalue="10483"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450373025797" ObjectName="110kV母联1121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450373025797"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,564.317,375.601) scale(0.899385,-0.636986) translate(62.376,-970.7)" width="15" x="557.5713396784201" y="366.0463469131837"/></g>
  <g id="203">
   <use class="kv110" height="30" transform="rotate(180,657.185,363.66) scale(-0.899385,-0.636986) translate(-1388.64,-940.012)" width="15" x="650.4394642628019" xlink:href="#Disconnector:刀闸_0" y="354.1053157703178" zvalue="10497"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450373484549" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450373484549"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,657.185,363.66) scale(-0.899385,-0.636986) translate(-1388.64,-940.012)" width="15" x="650.4394642628019" y="354.1053157703178"/></g>
  <g id="202">
   <use class="kv110" height="30" transform="rotate(180,710.33,363.66) scale(-0.899385,-0.636986) translate(-1500.88,-940.012)" width="15" x="703.5849032084616" xlink:href="#Disconnector:刀闸_0" y="354.1053157703178" zvalue="10499"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450373419013" ObjectName="#1主变110kV侧1012隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450373419013"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,710.33,363.66) scale(-0.899385,-0.636986) translate(-1500.88,-940.012)" width="15" x="703.5849032084616" y="354.1053157703178"/></g>
  <g id="341">
   <use class="kv35" height="30" transform="rotate(0,1346.01,176.505) scale(-0.899385,0.636986) translate(-2843.34,95.1434)" width="15" x="1339.260213517659" xlink:href="#Disconnector:刀闸_0" y="166.9498410294859" zvalue="10674"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450374991877" ObjectName="35kV模拟A线3426隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450374991877"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1346.01,176.505) scale(-0.899385,0.636986) translate(-2843.34,95.1434)" width="15" x="1339.260213517659" y="166.9498410294859"/></g>
  <g id="330">
   <use class="kv35" height="30" transform="rotate(180,1345.99,301.082) scale(-0.899385,-0.636986) translate(-2843.32,-779.194)" width="15" x="1339.249207994308" xlink:href="#Disconnector:刀闸_0" y="291.5272462053786" zvalue="10679"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450374795269" ObjectName="35kV模拟A线3421隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450374795269"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1345.99,301.082) scale(-0.899385,-0.636986) translate(-2843.32,-779.194)" width="15" x="1339.249207994308" y="291.5272462053786"/></g>
  <g id="313">
   <use class="kv35" height="30" transform="rotate(180,1383.96,301.082) scale(-0.899385,-0.636986) translate(-2923.49,-779.194)" width="15" x="1377.210235812636" xlink:href="#Disconnector:刀闸_0" y="291.5272462053786" zvalue="10692"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450374402053" ObjectName="35kV模拟A线3422隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450374402053"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1383.96,301.082) scale(-0.899385,-0.636986) translate(-2923.49,-779.194)" width="15" x="1377.210235812636" y="291.5272462053786"/></g>
  <g id="524">
   <use class="kv35" height="30" transform="rotate(180,1542.56,376.875) scale(0.899385,-0.636986) translate(171.813,-973.974)" width="15" x="1535.816877755633" xlink:href="#Disconnector:刀闸_0" y="367.320471098754" zvalue="10790"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450375450629" ObjectName="35kV母联3122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450375450629"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1542.56,376.875) scale(0.899385,-0.636986) translate(171.813,-973.974)" width="15" x="1535.816877755633" y="367.320471098754"/></g>
  <g id="516">
   <use class="kv35" height="30" transform="rotate(0,1597.62,377.268) scale(-0.899385,0.636986) translate(-3374.72,209.557)" width="15" x="1590.873841115949" xlink:href="#Disconnector:刀闸_0" y="367.7130135798504" zvalue="10800"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450375122949" ObjectName="35kV母联3121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450375122949"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1597.62,377.268) scale(-0.899385,0.636986) translate(-3374.72,209.557)" width="15" x="1590.873841115949" y="367.7130135798504"/></g>
  <g id="655">
   <use class="kv10" height="30" transform="rotate(0,685.007,675.497) scale(0.899385,0.636986) translate(75.8778,379.515)" width="15" x="678.2616721897233" xlink:href="#Disconnector:刀闸_0" y="665.9424823240006" zvalue="10917"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450375581701" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450375581701"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,685.007,675.497) scale(0.899385,0.636986) translate(75.8778,379.515)" width="15" x="678.2616721897233" y="665.9424823240006"/></g>
  <g id="653">
   <use class="kv10" height="30" transform="rotate(180,686.483,606.256) scale(-0.899385,-0.636986) translate(-1450.52,-1563.46)" width="15" x="679.7381096724046" xlink:href="#Disconnector:刀闸_0" y="596.7012184381939" zvalue="10918"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450375516165" ObjectName="#1主变10kV侧0016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450375516165"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,686.483,606.256) scale(-0.899385,-0.636986) translate(-1450.52,-1563.46)" width="15" x="679.7381096724046" y="596.7012184381939"/></g>
  <g id="663">
   <use class="kv10" height="30" transform="rotate(0,700.064,724.241) scale(0.899385,0.636986) translate(77.5622,407.294)" width="15" x="693.3182614755874" xlink:href="#Disconnector:刀闸_0" y="714.6860720675903" zvalue="10925"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450375647237" ObjectName="10kV模拟A线0451隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450375647237"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,700.064,724.241) scale(0.899385,0.636986) translate(77.5622,407.294)" width="15" x="693.3182614755874" y="714.6860720675903"/></g>
  <g id="667">
   <use class="kv10" height="30" transform="rotate(180,700.187,811.279) scale(-0.899385,-0.636986) translate(-1479.46,-2090.34)" width="15" x="693.4418239929062" xlink:href="#Disconnector:刀闸_0" y="801.7242386231366" zvalue="10926"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450375712773" ObjectName="10kV模拟A线0456隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450375712773"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,700.187,811.279) scale(-0.899385,-0.636986) translate(-1479.46,-2090.34)" width="15" x="693.4418239929062" y="801.7242386231366"/></g>
  <g id="683">
   <use class="kv10" height="30" transform="rotate(0,699.943,878.374) scale(0.899385,0.636986) translate(77.5487,495.133)" width="15" x="693.1973141181315" xlink:href="#Disconnector:刀闸_0" y="868.8194240039306" zvalue="10936"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450375909381" ObjectName="10kV模拟A线0458隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450375909381"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,699.943,878.374) scale(0.899385,0.636986) translate(77.5487,495.133)" width="15" x="693.1973141181315" y="868.8194240039306"/></g>
  <g id="294">
   <use class="kv35" height="30" transform="rotate(0,1464.15,176.446) scale(-0.899385,0.636986) translate(-3092.84,95.1097)" width="15" x="1457.400720426782" xlink:href="#Disconnector:刀闸_0" y="166.890787581212" zvalue="11251"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450376826885" ObjectName="35kV模拟B线3476隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450376826885"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1464.15,176.446) scale(-0.899385,0.636986) translate(-3092.84,95.1097)" width="15" x="1457.400720426782" y="166.890787581212"/></g>
  <g id="291">
   <use class="kv35" height="30" transform="rotate(180,1463.21,301.023) scale(-0.899385,-0.636986) translate(-3090.86,-779.042)" width="15" x="1456.460075503936" xlink:href="#Disconnector:刀闸_0" y="291.4681927571047" zvalue="11256"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450376630277" ObjectName="35kV模拟B线3471隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450376630277"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1463.21,301.023) scale(-0.899385,-0.636986) translate(-3090.86,-779.042)" width="15" x="1456.460075503936" y="291.4681927571047"/></g>
  <g id="140">
   <use class="kv35" height="30" transform="rotate(180,1502.1,301.023) scale(-0.899385,-0.636986) translate(-3172.99,-779.042)" width="15" x="1495.350742721758" xlink:href="#Disconnector:刀闸_0" y="291.4681927571047" zvalue="11269"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450376237061" ObjectName="35kV模拟B线3472隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450376237061"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1502.1,301.023) scale(-0.899385,-0.636986) translate(-3172.99,-779.042)" width="15" x="1495.350742721758" y="291.4681927571047"/></g>
  <g id="796">
   <use class="kv10" height="30" transform="rotate(0,1602.74,676.606) scale(0.864422,0.612224) translate(250.362,422.739)" width="15" x="1596.259616111576" xlink:href="#Disconnector:刀闸_0" y="667.4228636957766" zvalue="11558"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450377089029" ObjectName="#2主变10kV侧0022隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450377089029"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1602.74,676.606) scale(0.864422,0.612224) translate(250.362,422.739)" width="15" x="1596.259616111576" y="667.4228636957766"/></g>
  <g id="795">
   <use class="kv10" height="30" transform="rotate(180,1602.74,618.751) scale(-0.864422,-0.612224) translate(-3457.88,-1635.23)" width="15" x="1596.259616134077" xlink:href="#Disconnector:刀闸_0" y="609.5673914627739" zvalue="11559"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450377023493" ObjectName="#2主变10kV侧0026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450377023493"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1602.74,618.751) scale(-0.864422,-0.612224) translate(-3457.88,-1635.23)" width="15" x="1596.259616134077" y="609.5673914627739"/></g>
  <g id="968">
   <use class="kv10" height="30" transform="rotate(180,1461.2,730.334) scale(0.864422,-0.612224) translate(228.162,-1929.07)" width="15" x="1454.716803745858" xlink:href="#Disconnector:刀闸_0" y="721.1506416302888" zvalue="11608"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450376957957" ObjectName="10kV分段0121隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450376957957"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1461.2,730.334) scale(0.864422,-0.612224) translate(228.162,-1929.07)" width="15" x="1454.716803745858" y="721.1506416302888"/></g>
  <g id="961">
   <use class="kv10" height="30" transform="rotate(0,1526.12,729.802) scale(0.864422,-0.612224) translate(238.344,-1927.67)" width="15" x="1519.633448739772" xlink:href="#Disconnector:刀闸_0" y="720.6188332636671" zvalue="11610"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450378334213" ObjectName="10kV分段0122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450378334213"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1526.12,729.802) scale(0.864422,-0.612224) translate(238.344,-1927.67)" width="15" x="1519.633448739772" y="720.6188332636671"/></g>
  <g id="836">
   <use class="kv35" height="30" transform="rotate(180,1676.34,508.228) scale(-0.864422,-0.612224) translate(-3616.63,-1344.18)" width="15" x="1669.860479144691" xlink:href="#Disconnector:刀闸_0" y="499.0444705088105" zvalue="11649"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450377613317" ObjectName="#2主变35kV侧3026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450377613317"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1676.34,508.228) scale(-0.864422,-0.612224) translate(-3616.63,-1344.18)" width="15" x="1669.860479144691" y="499.0444705088105"/></g>
  <g id="812">
   <use class="kv35" height="30" transform="rotate(180,1650.8,369.584) scale(-0.864422,-0.612224) translate(-3561.54,-979.074)" width="15" x="1644.320763965265" xlink:href="#Disconnector:刀闸_0" y="360.4003023919233" zvalue="11659"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450377285637" ObjectName="#2主变35kV侧3021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450377285637"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1650.8,369.584) scale(-0.864422,-0.612224) translate(-3561.54,-979.074)" width="15" x="1644.320763965265" y="360.4003023919233"/></g>
  <g id="811">
   <use class="kv35" height="30" transform="rotate(180,1701.88,369.584) scale(-0.864422,-0.612224) translate(-3671.71,-979.074)" width="15" x="1695.400194324117" xlink:href="#Disconnector:刀闸_0" y="360.4003023919233" zvalue="11661"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450377220101" ObjectName="#2主变35kV侧3022隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450377220101"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1701.88,369.584) scale(-0.864422,-0.612224) translate(-3671.71,-979.074)" width="15" x="1695.400194324117" y="360.4003023919233"/></g>
  <g id="882">
   <use class="kv110" height="30" transform="rotate(180,946.976,461.009) scale(-0.864422,0.612224) translate(-2043.49,286.181)" width="15" x="940.4925994069462" xlink:href="#Disconnector:刀闸_0" y="451.8252188401859" zvalue="11672"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450378137605" ObjectName="#2主变110kV侧1026隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450378137605"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,946.976,461.009) scale(-0.864422,0.612224) translate(-2043.49,286.181)" width="15" x="940.4925994069462" y="451.8252188401859"/></g>
  <g id="873">
   <use class="kv110" height="30" transform="rotate(180,918.986,362.417) scale(-0.864422,-0.612224) translate(-1983.13,-960.202)" width="15" x="912.5030425298519" xlink:href="#Disconnector:刀闸_0" y="353.2336357252566" zvalue="11683"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450377744389" ObjectName="#2主变110kV侧1021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450377744389"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,918.986,362.417) scale(-0.864422,-0.612224) translate(-1983.13,-960.202)" width="15" x="912.5030425298519" y="353.2336357252566"/></g>
  <g id="872">
   <use class="kv110" height="30" transform="rotate(180,970.066,362.417) scale(-0.864422,-0.612224) translate(-2093.3,-960.202)" width="15" x="963.582472888705" xlink:href="#Disconnector:刀闸_0" y="353.2336357252566" zvalue="11685"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450377678853" ObjectName="#2主变110kV侧1022隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450377678853"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,970.066,362.417) scale(-0.864422,-0.612224) translate(-2093.3,-960.202)" width="15" x="963.582472888705" y="353.2336357252566"/></g>
  <g id="1188">
   <use class="kv10" height="30" transform="rotate(0,1685.06,724.241) scale(0.899385,0.636986) translate(187.755,407.294)" width="15" x="1678.318261475587" xlink:href="#Disconnector:刀闸_0" y="714.6860720675903" zvalue="12150"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450378924037" ObjectName="10kV模拟B线0461隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450378924037"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1685.06,724.241) scale(0.899385,0.636986) translate(187.755,407.294)" width="15" x="1678.318261475587" y="714.6860720675903"/></g>
  <g id="1187">
   <use class="kv10" height="30" transform="rotate(180,1685.19,811.279) scale(-0.899385,-0.636986) translate(-3559.65,-2090.34)" width="15" x="1678.441823992906" xlink:href="#Disconnector:刀闸_0" y="801.7242386231366" zvalue="12152"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450378858501" ObjectName="10kV模拟B线0466隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450378858501"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1685.19,811.279) scale(-0.899385,-0.636986) translate(-3559.65,-2090.34)" width="15" x="1678.441823992906" y="801.7242386231366"/></g>
  <g id="1183">
   <use class="kv10" height="30" transform="rotate(0,1684.94,878.374) scale(0.899385,0.636986) translate(187.742,495.133)" width="15" x="1678.197314118132" xlink:href="#Disconnector:刀闸_0" y="868.8194240039306" zvalue="12158"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450378792965" ObjectName="10kV模拟B线0468隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450378792965"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1684.94,878.374) scale(0.899385,0.636986) translate(187.742,495.133)" width="15" x="1678.197314118132" y="868.8194240039306"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="1427">
   <use class="kv110" height="20" transform="rotate(270,578.151,143.261) scale(-1.18266,-0.95548) translate(-1066.09,-293.642)" width="10" x="572.2380979201819" xlink:href="#GroundDisconnector:地刀_0" y="133.7059720089254" zvalue="7518"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450372829189" ObjectName="110kV模拟A线14467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450372829189"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,578.151,143.261) scale(-1.18266,-0.95548) translate(-1066.09,-293.642)" width="10" x="572.2380979201819" y="133.7059720089254"/></g>
  <g id="40">
   <use class="kv110" height="20" transform="rotate(270,573.4,201.181) scale(-1.18266,-0.95548) translate(-1057.32,-412.181)" width="10" x="567.4868429113151" xlink:href="#GroundDisconnector:地刀_0" y="191.6259018862924" zvalue="7566"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450372960261" ObjectName="110kV模拟A线14460接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450372960261"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,573.4,201.181) scale(-1.18266,-0.95548) translate(-1057.32,-412.181)" width="10" x="567.4868429113151" y="191.6259018862924"/></g>
  <g id="43">
   <use class="kv110" height="20" transform="rotate(270,574.009,263.854) scale(-1.18266,-0.95548) translate(-1058.45,-540.448)" width="10" x="568.0958046480519" xlink:href="#GroundDisconnector:地刀_0" y="254.299494518788" zvalue="7570"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450372632581" ObjectName="110kV模拟A线14427接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450372632581"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,574.009,263.854) scale(-1.18266,-0.95548) translate(-1058.45,-540.448)" width="10" x="568.0958046480519" y="254.299494518788"/></g>
  <g id="267">
   <use class="kv110" height="20" transform="rotate(90,650.711,401.332) scale(1.18266,0.95548) translate(-99.5894,18.2548)" width="10" x="644.7972514821661" xlink:href="#GroundDisconnector:地刀_0" y="391.7775473612281" zvalue="7818"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450373746693" ObjectName="#1主变110kV侧10127接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450373746693"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,650.711,401.332) scale(1.18266,0.95548) translate(-99.5894,18.2548)" width="10" x="644.7972514821661" y="391.7775473612281"/></g>
  <g id="855">
   <use class="kv110" height="20" transform="rotate(90,650.711,448.939) scale(1.18266,0.95548) translate(-99.5894,20.473)" width="10" x="644.7972649513488" xlink:href="#GroundDisconnector:地刀_0" y="439.3837840580425" zvalue="9769"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450373615621" ObjectName="#1主变110kV侧10167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450373615621"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,650.711,448.939) scale(1.18266,0.95548) translate(-99.5894,20.473)" width="10" x="644.7972649513488" y="439.3837840580425"/></g>
  <g id="266">
   <use class="kv35" height="20" transform="rotate(90,1186.83,479.967) scale(1.18266,0.95548) translate(-182.394,21.9187)" width="10" x="1180.918761761792" xlink:href="#GroundDisconnector:地刀_0" y="470.4119604089713" zvalue="10392"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450374270981" ObjectName="#1主变35kV侧30167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450374270981"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1186.83,479.967) scale(1.18266,0.95548) translate(-182.394,21.9187)" width="10" x="1180.918761761792" y="470.4119604089713"/></g>
  <g id="274">
   <use class="kv35" height="20" transform="rotate(90,1186.83,415.433) scale(1.18266,0.95548) translate(-182.394,18.9118)" width="10" x="1180.918761761792" xlink:href="#GroundDisconnector:地刀_0" y="405.8782131178135" zvalue="10394"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450374139909" ObjectName="#1主变35kV侧30127接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450374139909"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1186.83,415.433) scale(1.18266,0.95548) translate(-182.394,18.9118)" width="10" x="1180.918761761792" y="405.8782131178135"/></g>
  <g id="103">
   <use class="kv110" height="20" transform="rotate(180,564.147,439.964) scale(-1.18266,-0.95548) translate(-1040.25,-900.874)" width="10" x="558.2338642030636" xlink:href="#GroundDisconnector:地刀_0" y="430.4094944057618" zvalue="10476"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450373156869" ObjectName="110kV母联11217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450373156869"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,564.147,439.964) scale(-1.18266,-0.95548) translate(-1040.25,-900.874)" width="10" x="558.2338642030636" y="430.4094944057618"/></g>
  <g id="151">
   <use class="kv110" height="20" transform="rotate(180,509.281,439.015) scale(-1.18266,-0.95548) translate(-938.99,-898.932)" width="10" x="503.3677959153302" xlink:href="#GroundDisconnector:地刀_0" y="429.4604687103036" zvalue="10479"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450373287941" ObjectName="110kV母联11227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450373287941"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,509.281,439.015) scale(-1.18266,-0.95548) translate(-938.99,-898.932)" width="10" x="503.3677959153302" y="429.4604687103036"/></g>
  <g id="340">
   <use class="kv35" height="20" transform="rotate(270,1324.35,148.927) scale(-1.18266,-0.95548) translate(-2443.24,-305.239)" width="10" x="1318.434132593724" xlink:href="#GroundDisconnector:地刀_0" y="139.372638675592" zvalue="10676"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450374926341" ObjectName="35kV模拟A线Ⅱ34267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450374926341"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1324.35,148.927) scale(-1.18266,-0.95548) translate(-2443.24,-305.239)" width="10" x="1318.434132593724" y="139.372638675592"/></g>
  <g id="325">
   <use class="kv35" height="20" transform="rotate(270,1319.6,206.847) scale(-1.18266,-0.95548) translate(-2434.47,-423.778)" width="10" x="1313.682877584857" xlink:href="#GroundDisconnector:地刀_0" y="197.292568552959" zvalue="10682"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450374729733" ObjectName="35kV模拟A线34260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450374729733"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1319.6,206.847) scale(-1.18266,-0.95548) translate(-2434.47,-423.778)" width="10" x="1313.682877584857" y="197.292568552959"/></g>
  <g id="320">
   <use class="kv35" height="20" transform="rotate(270,1320.21,269.521) scale(-1.18266,-0.95548) translate(-2435.59,-552.045)" width="10" x="1314.291839321594" xlink:href="#GroundDisconnector:地刀_0" y="259.9661611854546" zvalue="10685"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450374598661" ObjectName="35kV模拟A线34227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450374598661"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1320.21,269.521) scale(-1.18266,-0.95548) translate(-2435.59,-552.045)" width="10" x="1314.291839321594" y="259.9661611854546"/></g>
  <g id="521">
   <use class="kv35" height="20" transform="rotate(180,1597.45,441.631) scale(-1.18266,-0.95548) translate(-2947.26,-904.285)" width="10" x="1591.536365640593" xlink:href="#GroundDisconnector:地刀_0" y="432.0761610724285" zvalue="10793"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450375385093" ObjectName="35kV母联31217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450375385093"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1597.45,441.631) scale(-1.18266,-0.95548) translate(-2947.26,-904.285)" width="10" x="1591.536365640593" y="432.0761610724285"/></g>
  <g id="519">
   <use class="kv35" height="20" transform="rotate(180,1542.58,440.682) scale(-1.18266,-0.95548) translate(-2846,-902.343)" width="10" x="1536.67029735286" xlink:href="#GroundDisconnector:地刀_0" y="431.1271353769703" zvalue="10796"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450375254021" ObjectName="35kV母联31227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450375254021"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1542.58,440.682) scale(-1.18266,-0.95548) translate(-2846,-902.343)" width="10" x="1536.67029735286" y="431.1271353769703"/></g>
  <g id="293">
   <use class="kv35" height="20" transform="rotate(270,1442.49,148.868) scale(-1.18266,-0.95548) translate(-2661.27,-305.118)" width="10" x="1436.574639502846" xlink:href="#GroundDisconnector:地刀_0" y="139.3135852273184" zvalue="11253"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450376761349" ObjectName="35kV模拟B线34767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450376761349"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1442.49,148.868) scale(-1.18266,-0.95548) translate(-2661.27,-305.118)" width="10" x="1436.574639502846" y="139.3135852273184"/></g>
  <g id="287">
   <use class="kv35" height="20" transform="rotate(270,1437.74,206.788) scale(-1.18266,-0.95548) translate(-2652.5,-423.657)" width="10" x="1431.82338449398" xlink:href="#GroundDisconnector:地刀_0" y="197.233515104685" zvalue="11259"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450376564741" ObjectName="35kV模拟B线34760接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450376564741"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1437.74,206.788) scale(-1.18266,-0.95548) translate(-2652.5,-423.657)" width="10" x="1431.82338449398" y="197.233515104685"/></g>
  <g id="230">
   <use class="kv35" height="20" transform="rotate(270,1438.35,269.462) scale(-1.18266,-0.95548) translate(-2653.62,-551.925)" width="10" x="1432.432346230717" xlink:href="#GroundDisconnector:地刀_0" y="259.9071077371809" zvalue="11262"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450376433669" ObjectName="35kV模拟B线34727接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450376433669"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1438.35,269.462) scale(-1.18266,-0.95548) translate(-2653.62,-551.925)" width="10" x="1432.432346230717" y="259.9071077371809"/></g>
  <g id="823">
   <use class="kv35" height="20" transform="rotate(90,1643.54,478.767) scale(1.13669,0.918336) translate(-196.953,41.7584)" width="10" x="1637.853660772184" xlink:href="#GroundDisconnector:地刀_0" y="469.5836951219748" zvalue="11653"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450377547781" ObjectName="#2主变35kV侧30267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450377547781"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1643.54,478.767) scale(1.13669,0.918336) translate(-196.953,41.7584)" width="10" x="1637.853660772184" y="469.5836951219748"/></g>
  <g id="816">
   <use class="kv35" height="20" transform="rotate(90,1642.54,417.742) scale(1.13669,0.918336) translate(-196.833,36.3316)" width="10" x="1636.853660772184" xlink:href="#GroundDisconnector:地刀_0" y="408.5586725433674" zvalue="11655"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450377416709" ObjectName="#2主变35kV侧30227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450377416709"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1642.54,417.742) scale(1.13669,0.918336) translate(-196.833,36.3316)" width="10" x="1636.853660772184" y="408.5586725433674"/></g>
  <g id="879">
   <use class="kv110" height="20" transform="rotate(90,912.764,398.625) scale(1.13669,0.918336) translate(-109.077,34.6316)" width="10" x="907.0801686103604" xlink:href="#GroundDisconnector:地刀_0" y="389.4413737397651" zvalue="11675"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450378072069" ObjectName="#2主变110kV侧10227接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450378072069"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,912.764,398.625) scale(1.13669,0.918336) translate(-109.077,34.6316)" width="10" x="907.0801686103604" y="389.4413737397651"/></g>
  <g id="876">
   <use class="kv110" height="20" transform="rotate(90,912.764,440.38) scale(1.13669,0.918336) translate(-109.077,38.3448)" width="10" x="907.0801815559338" xlink:href="#GroundDisconnector:地刀_0" y="431.196936215943" zvalue="11679"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450377940997" ObjectName="#2主变110kV侧10260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450377940997"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,912.764,440.38) scale(1.13669,0.918336) translate(-109.077,38.3448)" width="10" x="907.0801815559338" y="431.196936215943"/></g>
  <g id="451">
   <use class="kv110" height="20" transform="rotate(90,912.764,476.38) scale(1.13669,0.918336) translate(-109.077,41.5461)" width="10" x="907.0801815559338" xlink:href="#GroundDisconnector:地刀_0" y="467.196936215943" zvalue="11739"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450378268677" ObjectName="#2主变110kV侧10267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450378268677"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,912.764,476.38) scale(1.13669,0.918336) translate(-109.077,41.5461)" width="10" x="907.0801815559338" y="467.196936215943"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="18">
   <path class="kv110" d="M 599.75 220.64 L 599.75 180.23" stroke-width="1" zvalue="7521"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1429@0" LinkObjectIDznd="1428@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 599.75 220.64 L 599.75 180.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv110" d="M 599.73 161.6 L 599.81 96.86" stroke-width="1" zvalue="7535"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1428@0" LinkObjectIDznd="1430@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 599.73 161.6 L 599.81 96.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv110" d="M 582.72 201.24 L 599.75 201.24" stroke-width="1" zvalue="7568"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@0" LinkObjectIDznd="18" MaxPinNum="2"/>
   </metadata>
  <path d="M 582.72 201.24 L 599.75 201.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="9">
   <path class="kv110" d="M 599.85 304.81 L 599.85 323.72" stroke-width="1" zvalue="7580"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1397@1" LinkObjectIDznd="48@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 599.85 304.81 L 599.85 323.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="273">
   <path class="kv110" d="M 686.36 439.21 L 686.36 456.85" stroke-width="1" zvalue="7809"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@0" LinkObjectIDznd="275@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 686.36 439.21 L 686.36 456.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv110" d="M 599.9 245.21 L 599.88 286.18" stroke-width="1" zvalue="8775"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1429@1" LinkObjectIDznd="1397@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 599.9 245.21 L 599.88 286.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="27">
   <path class="kv110" d="M 583.33 263.91 L 599.89 263.91" stroke-width="1" zvalue="8776"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="26" MaxPinNum="2"/>
   </metadata>
  <path d="M 583.33 263.91 L 599.89 263.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv110" d="M 587.47 143.32 L 599.75 143.32" stroke-width="1" zvalue="8782"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1427@0" LinkObjectIDznd="12" MaxPinNum="2"/>
   </metadata>
  <path d="M 587.47 143.32 L 599.75 143.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="kv110" d="M 686.39 475.48 L 686.39 496.48" stroke-width="1" zvalue="8875"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="275@0" LinkObjectIDznd="190@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 686.39 475.48 L 686.39 496.48" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="856">
   <path class="kv110" d="M 660.03 449 L 686.36 449.93" stroke-width="1" zvalue="9771"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="855@0" LinkObjectIDznd="273" MaxPinNum="2"/>
   </metadata>
  <path d="M 660.03 449 L 686.36 449.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv35" d="M 1221.95 394.22 L 1221.96 436.37" stroke-width="1" zvalue="10390"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38" LinkObjectIDznd="495@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1221.95 394.22 L 1221.96 436.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="28">
   <path class="kv35" d="M 1222.1 460.94 L 1222.08 500.34" stroke-width="1" zvalue="10391"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="495@1" LinkObjectIDznd="499@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1222.1 460.94 L 1222.08 500.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv35" d="M 1196.15 480.03 L 1222.1 479.58" stroke-width="1" zvalue="10395"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="266@0" LinkObjectIDznd="28" MaxPinNum="2"/>
   </metadata>
  <path d="M 1196.15 480.03 L 1222.1 479.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv35" d="M 1196.15 415.49 L 1221.96 415.49" stroke-width="1" zvalue="10396"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="274@0" LinkObjectIDznd="31" MaxPinNum="2"/>
   </metadata>
  <path d="M 1196.15 415.49 L 1221.96 415.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv35" d="M 1195.49 374.72 L 1195.49 394.22 L 1248.63 394.22 L 1248.63 374.72" stroke-width="1" zvalue="10417"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@1" LinkObjectIDznd="33@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1195.49 374.72 L 1195.49 394.22 L 1248.63 394.22 L 1248.63 374.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv110" d="M 637.81 304.81 L 637.81 338.66" stroke-width="1" zvalue="10423"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@1" LinkObjectIDznd="24@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 637.81 304.81 L 637.81 338.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv110" d="M 637.84 286.18 L 637.84 263.77 L 599.88 263.77 L 599.88 272.77" stroke-width="1" zvalue="10424"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97@0" LinkObjectIDznd="26" MaxPinNum="2"/>
   </metadata>
  <path d="M 637.84 286.18 L 637.84 263.77 L 599.88 263.77 L 599.88 272.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="kv110" d="M 509.2 384.6 L 509.2 429.7" stroke-width="1" zvalue="10474"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@1" LinkObjectIDznd="151@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 509.2 384.6 L 509.2 429.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv110" d="M 523.23 407.92 L 509.2 407.92" stroke-width="1" zvalue="10475"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@1" LinkObjectIDznd="134" MaxPinNum="2"/>
   </metadata>
  <path d="M 523.23 407.92 L 509.2 407.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="kv110" d="M 548.57 407.85 L 564.21 407.85" stroke-width="1" zvalue="10477"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 548.57 407.85 L 564.21 407.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv110" d="M 564.21 430.65 L 564.21 384.84" stroke-width="1" zvalue="10482"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="25@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 564.21 430.65 L 564.21 384.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv110" d="M 657.24 373.05 L 657.24 392.56 L 710.39 392.56 L 710.39 373.05" stroke-width="1" zvalue="10501"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@1" LinkObjectIDznd="202@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 657.24 373.05 L 657.24 392.56 L 710.39 392.56 L 710.39 373.05" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="200">
   <path class="kv110" d="M 657.26 354.42 L 657.26 323.72" stroke-width="1" zvalue="10502"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@0" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 657.26 354.42 L 657.26 323.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="kv110" d="M 710.41 354.42 L 710.41 338.66" stroke-width="1" zvalue="10503"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="24@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 710.41 354.42 L 710.41 338.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="kv110" d="M 686.21 414.65 L 686.21 392.56" stroke-width="1" zvalue="10504"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="276@1" LinkObjectIDznd="201" MaxPinNum="2"/>
   </metadata>
  <path d="M 686.21 414.65 L 686.21 392.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="206">
   <path class="kv110" d="M 660.03 401.39 L 686.21 401.33" stroke-width="1" zvalue="10506"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@0" LinkObjectIDznd="204" MaxPinNum="2"/>
   </metadata>
  <path d="M 660.03 401.39 L 686.21 401.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv110" d="M 621.46 113.54 L 599.79 113.54" stroke-width="1" zvalue="10638"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195" LinkObjectIDznd="12" MaxPinNum="2"/>
   </metadata>
  <path d="M 621.46 113.54 L 599.79 113.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv110" d="M 578.7 106.73 L 578.7 113.54 L 621.46 113.54" stroke-width="1" zvalue="10639"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="248@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 578.7 106.73 L 578.7 113.54 L 621.46 113.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="332">
   <path class="kv35" d="M 1345.95 226.31 L 1345.95 185.9" stroke-width="1" zvalue="10678"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="343@0" LinkObjectIDznd="341@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1345.95 226.31 L 1345.95 185.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="326">
   <path class="kv35" d="M 1345.93 167.27 L 1346.01 102.53" stroke-width="1" zvalue="10681"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@0" LinkObjectIDznd="348@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1345.93 167.27 L 1346.01 102.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="324">
   <path class="kv35" d="M 1328.91 206.91 L 1345.95 206.91" stroke-width="1" zvalue="10684"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="325@0" LinkObjectIDznd="332" MaxPinNum="2"/>
   </metadata>
  <path d="M 1328.91 206.91 L 1345.95 206.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="319">
   <path class="kv35" d="M 1346.05 310.47 L 1346.05 325.98" stroke-width="1" zvalue="10687"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="330@1" LinkObjectIDznd="36@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1346.05 310.47 L 1346.05 325.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="318">
   <path class="kv35" d="M 1346.09 250.88 L 1346.07 291.84" stroke-width="1" zvalue="10688"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="343@1" LinkObjectIDznd="330@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1346.09 250.88 L 1346.07 291.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="317">
   <path class="kv35" d="M 1329.52 269.58 L 1346.08 269.58" stroke-width="1" zvalue="10689"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="320@0" LinkObjectIDznd="318" MaxPinNum="2"/>
   </metadata>
  <path d="M 1329.52 269.58 L 1346.08 269.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="316">
   <path class="kv35" d="M 1333.66 148.99 L 1345.95 148.99" stroke-width="1" zvalue="10690"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="340@0" LinkObjectIDznd="326" MaxPinNum="2"/>
   </metadata>
  <path d="M 1333.66 148.99 L 1345.95 148.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="312">
   <path class="kv35" d="M 1384.03 291.84 L 1384.03 269.44 L 1346.08 269.44 L 1346.08 278.44" stroke-width="1" zvalue="10693"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="313@0" LinkObjectIDznd="318" MaxPinNum="2"/>
   </metadata>
  <path d="M 1384.03 291.84 L 1384.03 269.44 L 1346.08 269.44 L 1346.08 278.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv35" d="M 1384.01 310.47 L 1384.01 339.66" stroke-width="1" zvalue="10694"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="313@1" LinkObjectIDznd="37@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1384.01 310.47 L 1384.01 339.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="308">
   <path class="kv35" d="M 1324.9 119.99 L 1324.9 127.52 L 1345.98 127.52" stroke-width="1" zvalue="10695"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="315@0" LinkObjectIDznd="305" MaxPinNum="2"/>
   </metadata>
  <path d="M 1324.9 119.99 L 1324.9 127.52 L 1345.98 127.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="305">
   <path class="kv35" d="M 1352.44 127.52 L 1345.98 127.52" stroke-width="1" zvalue="10697"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="326" MaxPinNum="2"/>
   </metadata>
  <path d="M 1352.44 127.52 L 1345.98 127.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="509">
   <path class="kv35" d="M 1195.51 356.09 L 1195.51 325.98" stroke-width="1" zvalue="10785"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1195.51 356.09 L 1195.51 325.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="510">
   <path class="kv35" d="M 1248.66 356.09 L 1248.66 339.66" stroke-width="1" zvalue="10786"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="37@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1248.66 356.09 L 1248.66 339.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="523">
   <path class="kv35" d="M 1542.51 386.27 L 1542.51 431.37" stroke-width="1" zvalue="10791"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="524@1" LinkObjectIDznd="519@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1542.51 386.27 L 1542.51 431.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="522">
   <path class="kv35" d="M 1556.54 409.58 L 1542.51 409.58" stroke-width="1" zvalue="10792"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="526@1" LinkObjectIDznd="523" MaxPinNum="2"/>
   </metadata>
  <path d="M 1556.54 409.58 L 1542.51 409.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="520">
   <path class="kv35" d="M 1581.88 409.51 L 1597.54 409.59" stroke-width="1" zvalue="10794"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="526@0" LinkObjectIDznd="517" MaxPinNum="2"/>
   </metadata>
  <path d="M 1581.88 409.51 L 1597.54 409.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="517">
   <path class="kv35" d="M 1597.51 432.32 L 1597.56 386.66" stroke-width="1" zvalue="10799"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="521@0" LinkObjectIDznd="516@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1597.51 432.32 L 1597.56 386.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="666">
   <path class="kv10" d="M 700.27 802.04 L 700.27 779.63" stroke-width="1" zvalue="10927"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="667@0" LinkObjectIDznd="665@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 700.27 802.04 L 700.27 779.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="664">
   <path class="kv10" d="M 700.12 733.63 L 700.12 755.06" stroke-width="1" zvalue="10928"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="663@1" LinkObjectIDznd="665@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 700.12 733.63 L 700.12 755.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="674">
   <path class="kv10" d="M 700.24 820.67 L 700.24 869.14" stroke-width="1" zvalue="10933"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="667@1" LinkObjectIDznd="683@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 700.24 820.67 L 700.24 869.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="682">
   <path class="kv10" d="M 700 887.77 L 700 904.49" stroke-width="1" zvalue="10938"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="683@1" LinkObjectIDznd="681@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 700 887.77 L 700 904.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="679">
   <path class="kv10" d="M 680.18 901.99 L 680.18 888.06 L 700 888.06" stroke-width="1" zvalue="10942"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="680@0" LinkObjectIDznd="682" MaxPinNum="2"/>
   </metadata>
  <path d="M 680.18 901.99 L 680.18 888.06 L 700 888.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="684">
   <path class="kv10" d="M 700.14 715 L 700.14 693.49" stroke-width="1" zvalue="10943"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="663@0" LinkObjectIDznd="166@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 700.14 715 L 700.14 693.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="292">
   <path class="kv35" d="M 1463.14 226.25 L 1463.14 185.84" stroke-width="1" zvalue="11255"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="297@0" LinkObjectIDznd="294@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1463.14 226.25 L 1463.14 185.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="288">
   <path class="kv35" d="M 1464.07 167.21 L 1464.15 102.47" stroke-width="1" zvalue="11258"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="294@0" LinkObjectIDznd="299@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1464.07 167.21 L 1464.15 102.47" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="286">
   <path class="kv35" d="M 1447.05 206.85 L 1463.14 206.85" stroke-width="1" zvalue="11261"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="287@0" LinkObjectIDznd="292" MaxPinNum="2"/>
   </metadata>
  <path d="M 1447.05 206.85 L 1463.14 206.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="229">
   <path class="kv35" d="M 1463.26 310.41 L 1463.26 325.98" stroke-width="1" zvalue="11264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="291@1" LinkObjectIDznd="36@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1463.26 310.41 L 1463.26 325.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="214">
   <path class="kv35" d="M 1463.28 250.82 L 1463.28 291.78" stroke-width="1" zvalue="11265"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="297@1" LinkObjectIDznd="291@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1463.28 250.82 L 1463.28 291.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv35" d="M 1447.66 269.52 L 1463.28 269.39" stroke-width="1" zvalue="11266"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="139" MaxPinNum="2"/>
   </metadata>
  <path d="M 1447.66 269.52 L 1463.28 269.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="212">
   <path class="kv35" d="M 1451.8 148.93 L 1464.09 148.93" stroke-width="1" zvalue="11267"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="293@0" LinkObjectIDznd="288" MaxPinNum="2"/>
   </metadata>
  <path d="M 1451.8 148.93 L 1464.09 148.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv35" d="M 1502.18 291.78 L 1502.18 269.38 L 1463.28 269.38 L 1463.28 278.38" stroke-width="1" zvalue="11270"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="214" MaxPinNum="2"/>
   </metadata>
  <path d="M 1502.18 291.78 L 1502.18 269.38 L 1463.28 269.38 L 1463.28 278.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv35" d="M 1502.15 310.41 L 1502.15 339.66" stroke-width="1" zvalue="11271"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@1" LinkObjectIDznd="37@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1502.15 310.41 L 1502.15 339.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="94">
   <path class="kv35" d="M 1443.04 119.93 L 1443.04 141.23 L 1464.1 141.23" stroke-width="1" zvalue="11272"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@0" LinkObjectIDznd="288" MaxPinNum="2"/>
   </metadata>
  <path d="M 1443.04 119.93 L 1443.04 141.23 L 1464.1 141.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv35" d="M 1469.63 126.51 L 1443.04 126.51" stroke-width="1" zvalue="11273"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="94" MaxPinNum="2"/>
   </metadata>
  <path d="M 1469.63 126.51 L 1443.04 126.51" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv10" d="M 686.71 572.2 L 686.71 597.02" stroke-width="1" zvalue="11524"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@2" LinkObjectIDznd="653@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 686.71 572.2 L 686.71 597.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="374">
   <path class="kv10" d="M 686.54 615.65 L 686.54 627.7" stroke-width="1" zvalue="11525"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="653@1" LinkObjectIDznd="654@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 686.54 615.65 L 686.54 627.7" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="454">
   <path class="kv10" d="M 685.09 652.27 L 685.09 666.26" stroke-width="1" zvalue="11526"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="654@1" LinkObjectIDznd="655@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 685.09 652.27 L 685.09 666.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="631">
   <path class="kv10" d="M 668.22 581.89 L 686.71 581.89" stroke-width="1" zvalue="11528"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="860@0" LinkObjectIDznd="61" MaxPinNum="2"/>
   </metadata>
  <path d="M 668.22 581.89 L 686.71 581.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="632">
   <path class="kv10" d="M 685.06 684.89 L 685.06 693.49" stroke-width="1" zvalue="11529"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="655@1" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 685.06 684.89 L 685.06 693.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="794">
   <path class="kv10" d="M 1602.8 627.78 L 1602.8 635.44" stroke-width="1" zvalue="11561"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="795@1" LinkObjectIDznd="799@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1602.8 627.78 L 1602.8 635.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="792">
   <path class="kv10" d="M 1602.84 659.05 L 1602.82 667.73" stroke-width="1" zvalue="11562"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="799@1" LinkObjectIDznd="796@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1602.84 659.05 L 1602.82 667.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="786">
   <path class="kv10" d="M 1602.8 685.63 L 1602.8 694.78" stroke-width="1" zvalue="11566"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="796@1" LinkObjectIDznd="788@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1602.8 685.63 L 1602.8 694.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="718">
   <path class="kv10" d="M 1526.17 720.78 L 1526.17 694.78" stroke-width="1" zvalue="11612"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="961@1" LinkObjectIDznd="788@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1526.17 720.78 L 1526.17 694.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="717">
   <path class="kv10" d="M 1461.12 721.45 L 1461.12 693.49" stroke-width="1" zvalue="11613"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="968@0" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1461.12 721.45 L 1461.12 693.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="708">
   <path class="kv10" d="M 1461.15 739.36 L 1461.15 765.77 L 1486.63 765.77" stroke-width="1" zvalue="11635"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="968@1" LinkObjectIDznd="970@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1461.15 739.36 L 1461.15 765.77 L 1486.63 765.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="707">
   <path class="kv10" d="M 1510.99 765.7 L 1526.19 765.7 L 1526.19 738.68" stroke-width="1" zvalue="11636"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="970@0" LinkObjectIDznd="961@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1510.99 765.7 L 1526.19 765.7 L 1526.19 738.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="835">
   <path class="kv35" d="M 1676.29 397.36 L 1676.3 437.87" stroke-width="1" zvalue="11651"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="810" LinkObjectIDznd="837@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1676.29 397.36 L 1676.3 437.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="824">
   <path class="kv35" d="M 1676.44 461.48 L 1676.42 499.35" stroke-width="1" zvalue="11652"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="837@1" LinkObjectIDznd="836@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1676.44 461.48 L 1676.42 499.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="815">
   <path class="kv35" d="M 1652.49 478.82 L 1676.43 478.82" stroke-width="1" zvalue="11656"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="823@0" LinkObjectIDznd="824" MaxPinNum="2"/>
   </metadata>
  <path d="M 1652.49 478.82 L 1676.43 478.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="813">
   <path class="kv35" d="M 1651.49 417.8 L 1676.29 418.14" stroke-width="1" zvalue="11658"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="816@0" LinkObjectIDznd="835" MaxPinNum="2"/>
   </metadata>
  <path d="M 1651.49 417.8 L 1676.29 418.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="810">
   <path class="kv35" d="M 1650.86 378.61 L 1650.86 397.36 L 1701.94 397.36 L 1701.94 378.61" stroke-width="1" zvalue="11662"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="812@1" LinkObjectIDznd="811@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1650.86 378.61 L 1650.86 397.36 L 1701.94 397.36 L 1701.94 378.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="809">
   <path class="kv35" d="M 1650.88 360.7 L 1650.88 325.98" stroke-width="1" zvalue="11664"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="812@0" LinkObjectIDznd="36@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1650.88 360.7 L 1650.88 325.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="808">
   <path class="kv35" d="M 1701.96 360.7 L 1701.96 339.66" stroke-width="1" zvalue="11665"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="811@0" LinkObjectIDznd="37@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1701.96 360.7 L 1701.96 339.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="881">
   <path class="kv110" d="M 947.03 431.03 L 947.03 451.98" stroke-width="1" zvalue="11674"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="883@0" LinkObjectIDznd="882@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 947.03 431.03 L 947.03 451.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="878">
   <path class="kv110" d="M 947.05 469.89 L 947.05 496.89" stroke-width="1" zvalue="11677"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="882@0" LinkObjectIDznd="877@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 947.05 469.89 L 947.05 496.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="871">
   <path class="kv110" d="M 919.04 371.44 L 919.04 390.19 L 970.12 390.19 L 970.12 371.44" stroke-width="1" zvalue="11687"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="873@1" LinkObjectIDznd="872@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.04 371.44 L 919.04 390.19 L 970.12 390.19 L 970.12 371.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="869">
   <path class="kv110" d="M 970.14 353.54 L 970.14 338.66" stroke-width="1" zvalue="11689"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="872@0" LinkObjectIDznd="24@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 970.14 353.54 L 970.14 338.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="868">
   <path class="kv110" d="M 946.89 407.42 L 946.89 390.19" stroke-width="1" zvalue="11690"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="883@1" LinkObjectIDznd="871" MaxPinNum="2"/>
   </metadata>
  <path d="M 946.89 407.42 L 946.89 390.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="867">
   <path class="kv110" d="M 921.72 398.68 L 946.89 398.68" stroke-width="1" zvalue="11691"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="879@0" LinkObjectIDznd="868" MaxPinNum="2"/>
   </metadata>
  <path d="M 921.72 398.68 L 946.89 398.68" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="863">
   <path class="kv35" d="M 1058.22 511.24 L 1058.22 535.03" stroke-width="1" zvalue="11693"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="874@0" LinkObjectIDznd="890" MaxPinNum="2"/>
   </metadata>
  <path d="M 1058.22 511.24 L 1058.22 535.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="887">
   <path class="kv10" d="M 947.02 569.67 L 947.02 596.83 L 1602.82 596.83 L 1602.82 609.87" stroke-width="1" zvalue="11695"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="877@2" LinkObjectIDznd="795@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 947.02 569.67 L 947.02 596.83 L 1602.82 596.83 L 1602.82 609.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="890">
   <path class="kv35" d="M 994.3 535.03 L 1676.4 535.03 L 1676.4 517.25" stroke-width="1" zvalue="11697"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="877@1" LinkObjectIDznd="836@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 994.3 535.03 L 1676.4 535.03 L 1676.4 517.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="915">
   <path class="kv110" d="M 921.72 476.44 L 947.05 476.44" stroke-width="1" zvalue="11741"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="451@0" LinkObjectIDznd="878" MaxPinNum="2"/>
   </metadata>
  <path d="M 921.72 476.44 L 947.05 476.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="916">
   <path class="kv10" d="M 1601.5 576.02 L 1601.5 596.83" stroke-width="1" zvalue="11742"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="800@0" LinkObjectIDznd="887" MaxPinNum="2"/>
   </metadata>
  <path d="M 1601.5 576.02 L 1601.5 596.83" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="889">
   <path class="kv110" d="M 921.72 440.44 L 947.03 440.44" stroke-width="1" zvalue="11743"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="876@0" LinkObjectIDznd="881" MaxPinNum="2"/>
   </metadata>
  <path d="M 921.72 440.44 L 947.03 440.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="925">
   <path class="kv110" d="M 564.37 366.21 L 564.37 338.66" stroke-width="1" zvalue="11763"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="25@1" LinkObjectIDznd="24@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 564.37 366.21 L 564.37 338.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="935">
   <path class="kv35" d="M 735.9 536.16 L 770 536.16 L 770 612 L 1222.06 612 L 1222.06 518.97" stroke-width="1" zvalue="11768"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@1" LinkObjectIDznd="499@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 735.9 536.16 L 770 536.16 L 770 612 L 1222.06 612 L 1222.06 518.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="936">
   <path class="kv35" d="M 766.44 511.84 L 766.44 536.16" stroke-width="1" zvalue="11769"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="515@0" LinkObjectIDznd="935" MaxPinNum="2"/>
   </metadata>
  <path d="M 766.44 511.84 L 766.44 536.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="937">
   <path class="kv110" d="M 919.06 353.54 L 919.06 323.72" stroke-width="1" zvalue="11770"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="873@0" LinkObjectIDznd="48@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 919.06 353.54 L 919.06 323.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="939">
   <path class="kv35" d="M 1542.48 367.64 L 1542.48 339.66" stroke-width="1" zvalue="11772"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="524@0" LinkObjectIDznd="37@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1542.48 367.64 L 1542.48 339.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="943">
   <path class="kv35" d="M 1597.54 368.03 L 1597.54 325.98" stroke-width="1" zvalue="11773"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="516@0" LinkObjectIDznd="36@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1597.54 368.03 L 1597.54 325.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1025">
   <path class="kv10" d="M 700.1 845.65 L 700.24 845.65" stroke-width="1" zvalue="11872"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="999@0" LinkObjectIDznd="674" MaxPinNum="2"/>
   </metadata>
  <path d="M 700.1 845.65 L 700.24 845.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1060">
   <path class="kv110" d="M 509.18 365.97 L 509.18 323.72" stroke-width="1" zvalue="11953"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@0" LinkObjectIDznd="48@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 509.18 365.97 L 509.18 323.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1144">
   <path class="kv110" d="M 621.19 521.86 L 621.19 515.61 L 686.87 515.61" stroke-width="1" zvalue="12144"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="900@0" LinkObjectIDznd="190@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 621.19 521.86 L 621.19 515.61 L 686.87 515.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1147">
   <path class="kv110" d="M 947.17 515.28 L 902.3 515.28 L 902.3 522.98" stroke-width="1" zvalue="12147"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="877@3" LinkObjectIDznd="1146@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 947.17 515.28 L 902.3 515.28 L 902.3 522.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1185">
   <path class="kv10" d="M 1685.12 733.63 L 1685.12 755.06" stroke-width="1" zvalue="12156"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1188@1" LinkObjectIDznd="1190@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1685.12 733.63 L 1685.12 755.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1184">
   <path class="kv10" d="M 1685.24 820.67 L 1685.24 869.14" stroke-width="1" zvalue="12157"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1187@1" LinkObjectIDznd="1183@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1685.24 820.67 L 1685.24 869.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1162">
   <path class="kv10" d="M 1685 887.77 L 1685 904.49" stroke-width="1" zvalue="12160"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1183@1" LinkObjectIDznd="1161@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1685 887.77 L 1685 904.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1159">
   <path class="kv10" d="M 1665.18 901.99 L 1665.18 888.06 L 1685 888.06" stroke-width="1" zvalue="12164"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1160@0" LinkObjectIDznd="1162" MaxPinNum="2"/>
   </metadata>
  <path d="M 1665.18 901.99 L 1665.18 888.06 L 1685 888.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1157">
   <path class="kv10" d="M 1685.1 845.65 L 1685.24 845.65" stroke-width="1" zvalue="12166"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1158@0" LinkObjectIDznd="1184" MaxPinNum="2"/>
   </metadata>
  <path d="M 1685.1 845.65 L 1685.24 845.65" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1191">
   <path class="kv10" d="M 1685.14 715 L 1685.14 694.78" stroke-width="1" zvalue="12167"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1188@0" LinkObjectIDznd="788@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1685.14 715 L 1685.14 694.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1192">
   <path class="kv10" d="M 1685.27 802.04 L 1685.27 779.63" stroke-width="1" zvalue="12168"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1187@0" LinkObjectIDznd="1190@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1685.27 802.04 L 1685.27 779.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="48">
   <path class="kv110" d="M 388.35 323.72 L 1041.83 323.72" stroke-width="6" zvalue="7577"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674264481797" ObjectName="110kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674264481797"/></metadata>
  <path d="M 388.35 323.72 L 1041.83 323.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv10" d="M 371 693.49 L 1478 693.49" stroke-width="6" zvalue="7716"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674264743941" ObjectName="10kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674264743941"/></metadata>
  <path d="M 371 693.49 L 1478 693.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="24">
   <path class="kv110" d="M 387.35 338.66 L 1043.33 338.66" stroke-width="6" zvalue="8774"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674264547333" ObjectName="110kVⅡ母"/>
   <cge:TPSR_Ref TObjectID="9288674264547333"/></metadata>
  <path d="M 387.35 338.66 L 1043.33 338.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv35" d="M 1160.42 325.98 L 1886.42 325.98" stroke-width="6" zvalue="10406"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674264612869" ObjectName="35kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674264612869"/></metadata>
  <path d="M 1160.42 325.98 L 1886.42 325.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv35" d="M 1156.86 339.66 L 1886.42 339.66" stroke-width="6" zvalue="10407"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674264809477" ObjectName="35kVⅡ母"/>
   <cge:TPSR_Ref TObjectID="9288674264809477"/></metadata>
  <path d="M 1156.86 339.66 L 1886.42 339.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="788">
   <path class="kv10" d="M 1515 694.78 L 1885.83 694.78" stroke-width="6" zvalue="11564"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674264678405" ObjectName="10kVⅡ母"/>
   <cge:TPSR_Ref TObjectID="9288674264678405"/></metadata>
  <path d="M 1515 694.78 L 1885.83 694.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer3Class">
  <g id="190">
   <g id="1900">
    <use class="kv110" height="97" transform="rotate(0,695.228,532.839) scale(0.868173,0.853634) translate(99.2387,84.2628)" width="96" x="653.5599999999999" xlink:href="#PowerTransformer3:可调三卷变Y-Y-D2_0" y="491.44" zvalue="8899"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874467639299" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1901">
    <use class="kv35" height="97" transform="rotate(0,695.228,532.839) scale(0.868173,0.853634) translate(99.2387,84.2628)" width="96" x="653.5599999999999" xlink:href="#PowerTransformer3:可调三卷变Y-Y-D2_1" y="491.44" zvalue="8899"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874467704835" ObjectName="35"/>
    </metadata>
   </g>
   <g id="1902">
    <use class="kv10" height="97" transform="rotate(0,695.228,532.839) scale(0.868173,0.853634) translate(99.2387,84.2628)" width="96" x="653.5599999999999" xlink:href="#PowerTransformer3:可调三卷变Y-Y-D2_2" y="491.44" zvalue="8899"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874467770371" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399465304067" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399465304067"/></metadata>
  <rect fill="white" height="97" opacity="0" stroke="white" transform="rotate(0,695.228,532.839) scale(0.868173,0.853634) translate(99.2387,84.2628)" width="96" x="653.5599999999999" y="491.44"/></g>
  <g id="877">
   <g id="8770">
    <use class="kv110" height="97" transform="rotate(0,955.206,531.836) scale(0.834423,0.82045) translate(181.597,107.681)" width="96" x="915.15" xlink:href="#PowerTransformer3:可调三卷变Y-Y-D2_0" y="492.04" zvalue="11678"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874467835907" ObjectName="110"/>
    </metadata>
   </g>
   <g id="8771">
    <use class="kv35" height="97" transform="rotate(0,955.206,531.836) scale(0.834423,0.82045) translate(181.597,107.681)" width="96" x="915.15" xlink:href="#PowerTransformer3:可调三卷变Y-Y-D2_1" y="492.04" zvalue="11678"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874467901443" ObjectName="35"/>
    </metadata>
   </g>
   <g id="8772">
    <use class="kv10" height="97" transform="rotate(0,955.206,531.836) scale(0.834423,0.82045) translate(181.597,107.681)" width="96" x="915.15" xlink:href="#PowerTransformer3:可调三卷变Y-Y-D2_2" y="492.04" zvalue="11678"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874467966979" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399465369603" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399465369603"/></metadata>
  <rect fill="white" height="97" opacity="0" stroke="white" transform="rotate(0,955.206,531.836) scale(0.834423,0.82045) translate(181.597,107.681)" width="96" x="915.15" y="492.04"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="860">
   <use class="kv10" height="26" transform="rotate(90,657.337,581.914) scale(-0.79533,0.880146) translate(-1485.06,77.6838)" width="12" x="652.5654525633802" xlink:href="#Accessory:避雷器1_0" y="570.4716180995521" zvalue="9777"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450372501509" ObjectName="#1主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,657.337,581.914) scale(-0.79533,0.880146) translate(-1485.06,77.6838)" width="12" x="652.5654525633802" y="570.4716180995521"/></g>
  <g id="515">
   <use class="kv35" height="26" transform="rotate(180,766.418,500.958) scale(-0.79533,0.880146) translate(-1731.29,66.6597)" width="12" x="761.645762140708" xlink:href="#Accessory:避雷器1_0" y="489.5159384301302" zvalue="10397"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450374008837" ObjectName="#1主变35kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,766.418,500.958) scale(-0.79533,0.880146) translate(-1731.29,66.6597)" width="12" x="761.645762140708" y="489.5159384301302"/></g>
  <g id="205">
   <use class="kv110" height="26" transform="rotate(180,578.675,95.8498) scale(-0.79533,0.880146) translate(-1307.49,11.4942)" width="12" x="573.9028908193511" xlink:href="#Accessory:避雷器1_0" y="84.40786299894444" zvalue="10636"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450375974917" ObjectName="110kV模拟A线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,578.675,95.8498) scale(-0.79533,0.880146) translate(-1307.49,11.4942)" width="12" x="573.9028908193511" y="84.40786299894444"/></g>
  <g id="248">
   <use class="kv110" height="40" transform="rotate(270,629.374,100.022) scale(-0.983202,0.983202) translate(-1269.84,1.37292)" width="40" x="609.710269000682" xlink:href="#Accessory:PT1111_0" y="80.35782786069365" zvalue="10637"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450373812229" ObjectName="110kV模拟A线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,629.374,100.022) scale(-0.983202,0.983202) translate(-1269.84,1.37292)" width="40" x="609.710269000682" y="80.35782786069365"/></g>
  <g id="315">
   <use class="kv35" height="26" transform="rotate(180,1324.87,109.109) scale(-0.79533,0.880146) translate(-2991.91,13.2997)" width="12" x="1320.098925492893" xlink:href="#Accessory:避雷器1_0" y="97.66673522927664" zvalue="10691"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450374467589" ObjectName="35kV模拟A线Ⅱ避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1324.87,109.109) scale(-0.79533,0.880146) translate(-2991.91,13.2997)" width="12" x="1320.098925492893" y="97.66673522927664"/></g>
  <g id="680">
   <use class="kv10" height="26" transform="rotate(0,680.209,912.878) scale(-0.79533,0.880146) translate(-1536.69,122.753)" width="12" x="675.4365429900938" xlink:href="#Accessory:避雷器1_0" y="901.4360068229284" zvalue="10941"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450375778309" ObjectName="10kV模拟A线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,680.209,912.878) scale(-0.79533,0.880146) translate(-1536.69,122.753)" width="12" x="675.4365429900938" y="901.4360068229284"/></g>
  <g id="57">
   <use class="kv35" height="40" transform="rotate(270,1381.25,127.521) scale(1.17047,1.47099) translate(-198.606,-31.4107)" width="30" x="1363.688435685409" xlink:href="#Accessory:2绕组线路PT_0" y="98.10162857334961" zvalue="11239"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450376105989" ObjectName="35kV模拟A线Ⅱ电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,1381.25,127.521) scale(1.17047,1.47099) translate(-198.606,-31.4107)" width="30" x="1363.688435685409" y="98.10162857334961"/></g>
  <g id="211">
   <use class="kv35" height="26" transform="rotate(180,1443.01,109.05) scale(-0.79533,0.880146) translate(-3258.6,13.2917)" width="12" x="1438.239432402016" xlink:href="#Accessory:避雷器1_0" y="97.60768178100273" zvalue="11268"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450376302597" ObjectName="35kV模拟B线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1443.01,109.05) scale(-0.79533,0.880146) translate(-3258.6,13.2917)" width="12" x="1438.239432402016" y="97.60768178100273"/></g>
  <g id="92">
   <use class="kv35" height="40" transform="rotate(270,1498.44,126.513) scale(1.17047,1.47099) translate(-215.673,-31.088)" width="30" x="1480.879916899073" xlink:href="#Accessory:2绕组线路PT_0" y="97.09354942961784" zvalue="11274"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450376171525" ObjectName="35kV轩南线电压互感器"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,1498.44,126.513) scale(1.17047,1.47099) translate(-215.673,-31.088)" width="30" x="1480.879916899073" y="97.09354942961784"/></g>
  <g id="800">
   <use class="kv10" height="26" transform="rotate(0,1601.52,565.559) scale(-0.764412,-0.845931) translate(-3698.04,-1236.13)" width="12" x="1596.935272738287" xlink:href="#Accessory:避雷器1_0" y="554.5617967825865" zvalue="11555"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450377154565" ObjectName="#2主变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1601.52,565.559) scale(-0.764412,-0.845931) translate(-3698.04,-1236.13)" width="12" x="1596.935272738287" y="554.5617967825865"/></g>
  <g id="874">
   <use class="kv35" height="26" transform="rotate(180,1058.19,500.775) scale(-0.764412,0.845931) translate(-2443.93,89.2031)" width="12" x="1053.604811294443" xlink:href="#Accessory:避雷器1_0" y="489.7783466468205" zvalue="11682"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450377809925" ObjectName="#2主变35kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1058.19,500.775) scale(-0.764412,0.845931) translate(-2443.93,89.2031)" width="12" x="1053.604811294443" y="489.7783466468205"/></g>
  <g id="999">
   <use class="kv10" height="21" transform="rotate(0,700.103,845.654) scale(1.33333,1.33333) translate(-173.692,-207.913)" width="8" x="694.7692307692311" xlink:href="#Accessory:中间电缆_0" y="831.653846153846" zvalue="11844"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450378399749" ObjectName="10kV模拟A线电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,700.103,845.654) scale(1.33333,1.33333) translate(-173.692,-207.913)" width="8" x="694.7692307692311" y="831.653846153846"/></g>
  <g id="1160">
   <use class="kv10" height="26" transform="rotate(0,1665.21,912.878) scale(-0.79533,0.880146) translate(-3760.17,122.753)" width="12" x="1660.436542990094" xlink:href="#Accessory:避雷器1_0" y="901.4360068229284" zvalue="12163"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450378661893" ObjectName="10kV模拟B线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1665.21,912.878) scale(-0.79533,0.880146) translate(-3760.17,122.753)" width="12" x="1660.436542990094" y="901.4360068229284"/></g>
  <g id="1158">
   <use class="kv10" height="21" transform="rotate(0,1685.1,845.654) scale(1.33333,1.33333) translate(-419.942,-207.913)" width="8" x="1679.769230769231" xlink:href="#Accessory:中间电缆_0" y="831.653846153846" zvalue="12165"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450378596357" ObjectName="10kV模拟B线电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,1685.1,845.654) scale(1.33333,1.33333) translate(-419.942,-207.913)" width="8" x="1679.769230769231" y="831.653846153846"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="681">
   <use class="kv10" height="30" transform="rotate(180,700.001,918.154) scale(0.395427,1.01229) translate(1066.61,-10.9664)" width="12" x="697.6281834692784" xlink:href="#EnergyConsumer:负荷_0" y="902.969793686756" zvalue="10939"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450375843845" ObjectName="10kV模拟A线"/>
   <cge:TPSR_Ref TObjectID="6192450375843845"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,700.001,918.154) scale(0.395427,1.01229) translate(1066.61,-10.9664)" width="12" x="697.6281834692784" y="902.969793686756"/></g>
  <g id="1161">
   <use class="kv10" height="30" transform="rotate(180,1685,918.154) scale(0.395427,1.01229) translate(2572.59,-10.9664)" width="12" x="1682.628183469278" xlink:href="#EnergyConsumer:负荷_0" y="902.969793686756" zvalue="12161"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450378727429" ObjectName="10kV模拟B线"/>
   <cge:TPSR_Ref TObjectID="6192450378727429"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1685,918.154) scale(0.395427,1.01229) translate(2572.59,-10.9664)" width="12" x="1682.628183469278" y="902.969793686756"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="314">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="314" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1346.01,14.8133) scale(1,1) translate(0,1.14928e-14)" writing-mode="lr" x="1345.54" xml:space="preserve" y="19.48" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128337145860" ObjectName="P"/>
   </metadata>
  </g>
  <g id="321">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="321" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1464.15,14.7542) scale(1,1) translate(9.45341e-13,-1.13355e-14)" writing-mode="lr" x="1463.68" xml:space="preserve" y="19.42" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128342650884" ObjectName="P"/>
   </metadata>
  </g>
  <g id="358">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="358" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1346.01,37.8133) scale(1,1) translate(0,0)" writing-mode="lr" x="1345.54" xml:space="preserve" y="42.48" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128337211396" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="390">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="390" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1464.15,37.7542) scale(1,1) translate(9.45341e-13,0)" writing-mode="lr" x="1463.68" xml:space="preserve" y="42.42" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128342716420" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="440">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="440" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1346.01,60.8133) scale(1,1) translate(0,0)" writing-mode="lr" x="1345.54" xml:space="preserve" y="65.48" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128337276932" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="441">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="441" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1464.15,60.7542) scale(1,1) translate(9.45341e-13,0)" writing-mode="lr" x="1463.68" xml:space="preserve" y="65.42" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128342781956" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="20" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,700.001,985.654) scale(1,1) translate(-2.94595e-13,0)" writing-mode="lr" x="699.62" xml:space="preserve" y="990.3200000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128340160516" ObjectName="P"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="52" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,700.001,1008.65) scale(1,1) translate(-2.94595e-13,0)" writing-mode="lr" x="699.62" xml:space="preserve" y="1013.32" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128340226052" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="62" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,700.001,1031.65) scale(1,1) translate(-2.94595e-13,0)" writing-mode="lr" x="699.62" xml:space="preserve" y="1036.32" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128340291588" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="68" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,747.506,403.291) scale(1,1) translate(0,-3.50491e-13)" writing-mode="lr" x="747.08" xml:space="preserve" y="407.93" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128332034052" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="69">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="69" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,747.506,424.464) scale(1,1) translate(0,0)" writing-mode="lr" x="747.08" xml:space="preserve" y="429.1" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128332099588" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="72">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="72" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,747.506,447.464) scale(1,1) translate(0,0)" writing-mode="lr" x="747.08" xml:space="preserve" y="452.1" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128332165124" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="73" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1279.77,421.89) scale(1,1) translate(0,-1.82694e-13)" writing-mode="lr" x="1279.38" xml:space="preserve" y="426.56" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128332492804" ObjectName="MP"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="78" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1279.77,450.844) scale(1,1) translate(2.75726e-13,0)" writing-mode="lr" x="1279.38" xml:space="preserve" y="455.51" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128332558340" ObjectName="MQ"/>
   </metadata>
  </g>
  <g id="79">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="79" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1279.77,479.799) scale(1,1) translate(2.75726e-13,0)" writing-mode="lr" x="1279.38" xml:space="preserve" y="484.47" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128332820484" ObjectName="MIa"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="80" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,606.228,667.964) scale(1,1) translate(0,0)" writing-mode="lr" x="605.8099999999999" xml:space="preserve" y="672.6" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128332296196" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="81">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="81" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,606.228,612.964) scale(1,1) translate(0,-4.02539e-13)" writing-mode="lr" x="605.8099999999999" xml:space="preserve" y="617.6" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128332623876" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="84">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="84" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,606.228,639.964) scale(1,1) translate(0,0)" writing-mode="lr" x="605.8099999999999" xml:space="preserve" y="644.6" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128332689412" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="455">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="455" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,143.907,156.444) scale(1,1) translate(0,-3.26406e-14)" writing-mode="lr" x="143.52" xml:space="preserve" y="161.09" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128351825925" ObjectName="P"/>
   </metadata>
  </g>
  <g id="1097">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1097" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,220.379,549) scale(1,1) translate(-4.33089e-14,-1.19571e-13)" writing-mode="lr" x="220.12" xml:space="preserve" y="553.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128349925382" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="1102">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1102" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,220.379,503.444) scale(1,1) translate(-4.33089e-14,0)" writing-mode="lr" x="220.12" xml:space="preserve" y="508.11" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128350121988" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="1107">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1107" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,220.379,526.778) scale(1,1) translate(-4.33089e-14,0)" writing-mode="lr" x="220.12" xml:space="preserve" y="531.4400000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128349859844" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="1112">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1112" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,143.907,205) scale(1,1) translate(-2.63286e-14,0)" writing-mode="lr" x="143.64" xml:space="preserve" y="209.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128335966212" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="1113">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1113" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,220.379,596.778) scale(1,1) translate(-4.33089e-14,0)" writing-mode="lr" x="220.12" xml:space="preserve" y="601.4400000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128350318596" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="1131">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="1131" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,142.907,253.155) scale(1,1) translate(0,0)" writing-mode="lr" x="142.67" xml:space="preserve" y="259.32" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128332754948" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="1139">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1139" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,220.379,573.583) scale(1,1) translate(-4.33089e-14,0)" writing-mode="lr" x="220.12" xml:space="preserve" y="578.25" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128349990919" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="1140">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1140" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,308.966,155.875) scale(1,1) translate(0,0)" writing-mode="lr" x="308.58" xml:space="preserve" y="160.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128351891461" ObjectName="P"/>
   </metadata>
  </g>
  <g id="1151">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1151" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,143.716,230.508) scale(1,1) translate(0,0)" writing-mode="lr" x="143.45" xml:space="preserve" y="235.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128349728772" ObjectName="F"/>
   </metadata>
  </g>
  <g id="1153">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1153" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,308.966,178.417) scale(1,1) translate(-6.29792e-14,0)" writing-mode="lr" x="308.7" xml:space="preserve" y="183.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128331902980" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="1154">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1154" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,308.966,205.667) scale(1,1) translate(-6.29792e-14,0)" writing-mode="lr" x="308.7" xml:space="preserve" y="210.33" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128350253060" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="1167">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1167" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,169.129,549) scale(1,1) translate(-3.19291e-14,-1.19571e-13)" writing-mode="lr" x="168.87" xml:space="preserve" y="553.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128335638532" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="1166">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1166" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,169.129,503.444) scale(1,1) translate(-3.19291e-14,0)" writing-mode="lr" x="168.87" xml:space="preserve" y="508.11" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128335835140" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="1165">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1165" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,169.129,526.778) scale(1,1) translate(-3.19291e-14,0)" writing-mode="lr" x="168.87" xml:space="preserve" y="531.4400000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128335572996" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="1164">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1164" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,169.129,596.778) scale(1,1) translate(-3.19291e-14,0)" writing-mode="lr" x="168.87" xml:space="preserve" y="601.4400000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128336031748" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="1163">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1163" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,169.129,573.583) scale(1,1) translate(-3.19291e-14,0)" writing-mode="lr" x="168.87" xml:space="preserve" y="578.25" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128335704068" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="1177">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1177" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,119.129,549) scale(1,1) translate(-2.08269e-14,-1.19571e-13)" writing-mode="lr" x="118.87" xml:space="preserve" y="553.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128331575300" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="1176">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1176" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,119.129,503.444) scale(1,1) translate(-2.08269e-14,0)" writing-mode="lr" x="118.87" xml:space="preserve" y="508.11" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128331771908" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="1175">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1175" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,119.129,526.778) scale(1,1) translate(-2.08269e-14,0)" writing-mode="lr" x="118.87" xml:space="preserve" y="531.4400000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128331509764" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="1174">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1174" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,119.129,596.778) scale(1,1) translate(-2.08269e-14,0)" writing-mode="lr" x="118.87" xml:space="preserve" y="601.4400000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128331968516" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="1173">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1173" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,119.129,573.583) scale(1,1) translate(-2.08269e-14,0)" writing-mode="lr" x="118.87" xml:space="preserve" y="578.25" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128331640836" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="1172">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1172" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,67.8792,549) scale(1,1) translate(-9.44708e-15,-1.19571e-13)" writing-mode="lr" x="67.62" xml:space="preserve" y="553.67" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128330526724" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="1171">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1171" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,67.8792,503.444) scale(1,1) translate(-9.44708e-15,0)" writing-mode="lr" x="67.62" xml:space="preserve" y="508.11" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128330723332" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="1170">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1170" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,67.8792,526.778) scale(1,1) translate(-9.44708e-15,0)" writing-mode="lr" x="67.62" xml:space="preserve" y="531.4400000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128330461188" ObjectName="Ubc"/>
   </metadata>
  </g>
  <g id="1169">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1169" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,67.8792,596.778) scale(1,1) translate(-9.44708e-15,0)" writing-mode="lr" x="67.62" xml:space="preserve" y="601.4400000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128330919940" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="1168">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1168" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,67.8792,573.583) scale(1,1) translate(-9.44708e-15,0)" writing-mode="lr" x="67.62" xml:space="preserve" y="578.25" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128330592260" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="1182">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1182" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,273.129,548.75) scale(1,1) translate(-5.50217e-14,-1.19516e-13)" writing-mode="lr" x="272.87" xml:space="preserve" y="553.42" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128349401092" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="1181">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1181" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,273.129,503.194) scale(1,1) translate(-5.50217e-14,0)" writing-mode="lr" x="272.87" xml:space="preserve" y="507.86" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128349597700" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="1180">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1180" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,273.129,526.528) scale(1,1) translate(-5.50217e-14,0)" writing-mode="lr" x="272.87" xml:space="preserve" y="531.1900000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128349335556" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="1179">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1179" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,273.129,595.278) scale(1,1) translate(-5.50217e-14,0)" writing-mode="lr" x="272.87" xml:space="preserve" y="599.9400000000001" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128349794308" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="1178">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1178" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,273.129,573.333) scale(1,1) translate(-5.50217e-14,0)" writing-mode="lr" x="272.87" xml:space="preserve" y="578" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128349466628" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="1189">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1189" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,143.907,179.417) scale(1,1) translate(-2.63286e-14,0)" writing-mode="lr" x="143.64" xml:space="preserve" y="184.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128330854404" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="1036">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1036" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,309.312,252.5) scale(1,1) translate(0,0)" writing-mode="lr" x="308.84" xml:space="preserve" y="257.17" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128333017092" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="22" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1019.65,402.272) scale(1,1) translate(0,0)" writing-mode="lr" x="1019.25" xml:space="preserve" y="406.94" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128346189828" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="169">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="169" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1019.65,431.226) scale(1,1) translate(0,0)" writing-mode="lr" x="1019.25" xml:space="preserve" y="435.89" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128346255364" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="185">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="185" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1019.65,460.181) scale(1,1) translate(0,0)" writing-mode="lr" x="1019.25" xml:space="preserve" y="464.85" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128346320900" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="462">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="462" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1731.13,427.836) scale(1,1) translate(0,0)" writing-mode="lr" x="1730.73" xml:space="preserve" y="432.5" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128346648580" ObjectName="MP"/>
   </metadata>
  </g>
  <g id="633">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="633" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1733.26,448.168) scale(1,1) translate(0,0)" writing-mode="lr" x="1732.86" xml:space="preserve" y="452.83" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128346714116" ObjectName="MQ"/>
   </metadata>
  </g>
  <g id="652">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="652" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1735.26,469.836) scale(1,1) translate(0,0)" writing-mode="lr" x="1734.86" xml:space="preserve" y="474.5" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128346976260" ObjectName="MIa"/>
   </metadata>
  </g>
  <g id="657">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="657" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1671.21,665.128) scale(1,1) translate(0,0)" writing-mode="lr" x="1670.74" xml:space="preserve" y="669.79" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128346451972" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="658">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="658" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1671.21,618.128) scale(1,1) translate(0,0)" writing-mode="lr" x="1670.74" xml:space="preserve" y="622.79" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128346779652" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="659">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="659" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1671.21,640.128) scale(1,1) translate(0,0)" writing-mode="lr" x="1670.74" xml:space="preserve" y="644.79" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128346845188" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="908">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="908" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,143.907,277.155) scale(1,1) translate(0,3.55256e-13)" writing-mode="lr" x="143.67" xml:space="preserve" y="283.32" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128346910724" ObjectName="Tap"/>
   </metadata>
  </g>
  <g id="740">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="740" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1492.76,817.254) scale(1,1) translate(0,0)" writing-mode="lr" x="1492.39" xml:space="preserve" y="821.9299999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128343699460" ObjectName="P"/>
   </metadata>
  </g>
  <g id="741">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="741" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1492.76,840.254) scale(1,1) translate(0,0)" writing-mode="lr" x="1492.39" xml:space="preserve" y="844.9299999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128343764996" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="742">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="742" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1492.76,862.254) scale(1,1) translate(0,0)" writing-mode="lr" x="1492.39" xml:space="preserve" y="866.9299999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128343830532" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="780">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="780" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,328.333,550.056) scale(1,1) translate(-6.72795e-14,-1.19805e-13)" writing-mode="lr" x="328.07" xml:space="preserve" y="554.72" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128344682500" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="772">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="772" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,328.333,504.5) scale(1,1) translate(-6.72795e-14,0)" writing-mode="lr" x="328.07" xml:space="preserve" y="509.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128344879108" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="774">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="774" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,328.333,527.833) scale(1,1) translate(-6.72795e-14,0)" writing-mode="lr" x="328.07" xml:space="preserve" y="532.5" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128344616964" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="922">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="922" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,328.333,596.583) scale(1,1) translate(-6.72795e-14,0)" writing-mode="lr" x="328.07" xml:space="preserve" y="601.25" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128345075716" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="914">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="914" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,328.333,574.639) scale(1,1) translate(-6.72795e-14,0)" writing-mode="lr" x="328.07" xml:space="preserve" y="579.3099999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128344748036" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,307.716,230.508) scale(1,1) translate(0,0)" writing-mode="lr" x="307.45" xml:space="preserve" y="235.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128345010180" ObjectName="F"/>
   </metadata>
  </g>
  <g id="630">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="630" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,995.333,652.5) scale(1,1) translate(0,0)" writing-mode="lr" x="995.0700000000001" xml:space="preserve" y="657.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128349597700" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="1079">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1079" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1020.33,130.5) scale(1,1) translate(-2.20934e-13,0)" writing-mode="lr" x="1020.07" xml:space="preserve" y="135.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128330723332" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="1080">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1080" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,457.333,521.5) scale(1,1) translate(3.83693e-13,0)" writing-mode="lr" x="457.07" xml:space="preserve" y="526.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128331771908" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="1081">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1081" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1859.33,655.5) scale(1,1) translate(-4.0723e-13,0)" writing-mode="lr" x="1859.07" xml:space="preserve" y="660.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128344879108" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="1085">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1085" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1366.33,524.5) scale(1,1) translate(-2.97762e-13,0)" writing-mode="lr" x="1366.07" xml:space="preserve" y="529.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128335835140" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="1086">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="1086" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1450.33,523.5) scale(1,1) translate(1.26565e-12,0)" writing-mode="lr" x="1450.07" xml:space="preserve" y="528.17" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128350121988" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="11" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,599.81,14.8133) scale(1,1) translate(0,0)" writing-mode="lr" x="556.6799999999999" xml:space="preserve" y="19.23" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128341078020" ObjectName="P"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="13" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,599.81,37.8133) scale(1,1) translate(0,0)" writing-mode="lr" x="556.6799999999999" xml:space="preserve" y="42.23" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128341143556" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="14" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,598.53,52.8133) scale(1,1) translate(6.14542e-14,0)" writing-mode="lr" x="555.4" xml:space="preserve" y="57.23" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481128341209092" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="StateClass">
  <g id="334">
   <use height="30" transform="rotate(0,319.262,318.539) scale(0.708333,0.665547) translate(127.086,155.057)" width="30" x="308.64" xlink:href="#State:红绿圆(方形)_0" y="308.56" zvalue="11432"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,319.262,318.539) scale(0.708333,0.665547) translate(127.086,155.057)" width="30" x="308.64" y="308.56"/></g>
  <g id="927">
   <use height="30" transform="rotate(0,287.812,113.464) scale(1.22222,1.03092) translate(-42.3295,-2.93945)" width="90" x="232.81" xlink:href="#State:全站检修_0" y="98" zvalue="11819"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549597110274" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,287.812,113.464) scale(1.22222,1.03092) translate(-42.3295,-2.93945)" width="90" x="232.81" y="98"/></g>
  <g id="711">
   <use height="30" transform="rotate(0,200.637,318.539) scale(0.708333,0.665547) translate(78.2403,155.057)" width="30" x="190.01" xlink:href="#State:红绿圆(方形)_0" y="308.56" zvalue="11823"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,200.637,318.539) scale(0.708333,0.665547) translate(78.2403,155.057)" width="30" x="190.01" y="308.56"/></g>
  <g id="1084">
   <use height="30" transform="rotate(0,279.235,408) scale(0.910937,0.8) translate(23.7384,99)" width="80" x="242.8" xlink:href="#State:间隔模板_0" y="396" zvalue="12067"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,279.235,408) scale(0.910937,0.8) translate(23.7384,99)" width="80" x="242.8" y="396"/></g>
  <g id="1082">
   <use height="30" transform="rotate(0,63.2857,318.539) scale(0.910937,0.8) translate(2.62495,76.6347)" width="80" x="26.85" xlink:href="#State:间隔模板_0" y="306.54" zvalue="12068"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,63.2857,318.539) scale(0.910937,0.8) translate(2.62495,76.6347)" width="80" x="26.85" y="306.54"/></g>
 </g>
 <g id="GroundClass">
  <g id="900">
   <use class="kv110" height="18" transform="rotate(0,621.185,532.667) scale(1.23457,1.23457) translate(-116.618,-99.0956)" width="12" x="613.7777777777778" xlink:href="#Ground:大地_0" y="521.5555555555555" zvalue="12143"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450378465285" ObjectName="1010"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,621.185,532.667) scale(1.23457,1.23457) translate(-116.618,-99.0956)" width="12" x="613.7777777777778" y="521.5555555555555"/></g>
  <g id="1146">
   <use class="kv110" height="18" transform="rotate(0,902.296,533.778) scale(1.23457,1.23457) translate(-170.029,-99.3067)" width="12" x="894.8888888888889" xlink:href="#Ground:大地_0" y="522.6666666666666" zvalue="12146"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450378530821" ObjectName="1020"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,902.296,533.778) scale(1.23457,1.23457) translate(-170.029,-99.3067)" width="12" x="894.8888888888889" y="522.6666666666666"/></g>
 </g>
</svg>