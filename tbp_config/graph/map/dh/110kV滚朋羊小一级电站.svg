<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549684600833" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="ACLineSegment:220kV线路_0" viewBox="0,0,35,40">
   <use terminal-index="0" type="0" x="14.20833333333333" xlink:href="#terminal" y="39.00547521122963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.5" x2="30.5" y1="30.75" y2="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.5" x2="27.5" y1="25.75" y2="30.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="21.50545055364641" x2="21.50545055364641" y1="13.04138864447597" y2="19.16666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.5" x2="24.5" y1="23.75" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.5" x2="5.5" y1="37" y2="37"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="18.91094883543335" x2="24.38823024054981" y1="12.92541949757221" y2="12.92541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.5" x2="18.5" y1="23.75" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.5" x2="30.5" y1="25.75" y2="27.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.5" x2="6.5" y1="36" y2="36"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="7.5" y1="35" y2="35"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.04,24.92) scale(1,1) translate(0,0)" width="6.08" x="2" y="17.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="32" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="4" y1="25.75" y2="19.75"/>
   <path d="M 14 13 L 5 13 L 5 25 L 5 25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="6" y1="25.75" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.5" x2="21.5" y1="20.75" y2="23.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.46058610156545" x2="14.15436235204273" y1="12.92541949757221" y2="12.92541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.16022336769755" x2="14.16022336769755" y1="38.83333333333334" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.46383161512025" x2="31.41666666666666" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.35940244368076" x2="24.35940244368076" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.36598892707138" x2="31.36598892707138" y1="9.749999999999996" y2="16.24517624503405"/>
   <ellipse cx="21.51" cy="23.82" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="28.55" cy="28.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.76" cy="32.08" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="24.35940244368077" x2="24.35940244368077" y1="10.48325293741726" y2="15.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="32.51910080183274" x2="32.51910080183274" y1="11.01295093653441" y2="15.34306843322376"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="33.38393470790376" x2="33.38393470790376" y1="11.91505874834466" y2="13.89969593432727"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.57589728904158" x2="16.57589728904158" y1="12.99758812251703" y2="12.99758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="26.52148720885832" x2="26.52148720885832" y1="10.48325293741726" y2="15.5840919325617"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="16.49256395570825" x2="16.49256395570825" y1="10.33333333333333" y2="15.43417232847779"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="18.73798205421914" x2="18.73798205421914" y1="10.48325293741726" y2="15.58409193256171"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.58333333333333" x2="18.58333333333333" y1="32.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.58333333333334" x2="24.58333333333334" y1="32.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.58333333333333" x2="21.58333333333333" y1="29.5" y2="32.5"/>
  </symbol>
  <symbol id="Breaker:手车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.45181617405996" x2="1.714850492606708" y1="5.276370517142858" y2="14.05696281619048"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.583333333333333" x2="8.583333333333334" y1="5.166666666666667" y2="14.16666666666667"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Accessory:10kV母线PT带消谐装置_0" viewBox="0,0,35,30">
   <use terminal-index="0" type="0" x="17.56245852479325" xlink:href="#terminal" y="28.12373692455963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.61245852479333" x2="34.61245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.86245852479331" x2="30.86245852479331" y1="22.68299618381883" y2="19.84040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.6124585247933" x2="26.6124585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.61245852479332" x2="34.61245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666667" x2="31" y1="22.69225544307809" y2="22.69225544307809"/>
   <rect fill-opacity="0" height="3.55" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,30.81,15.38) scale(1,1) translate(0,0)" width="8.92" x="26.34" y="13.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.647721108666" x2="30.647721108666" y1="10.83674821859629" y2="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.71438777533266" x2="28.71438777533266" y1="7.51612768565195" y2="7.51612768565195"/>
   <ellipse cx="14.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.05048569403981" x2="22.05048569403981" y1="34.53084700683308" y2="34.53084700683308"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.7608382776216" x2="28.48402243293775" y1="7.754208141873304" y2="7.754208141873304"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.01083827762157" x2="29.40068909960439" y1="6.50420814187332" y2="6.50420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.51083827762159" x2="30.06735576627107" y1="5.004208141873296" y2="5.004208141873296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.89410730945214" x2="23.0877735452272" y1="24.15299989806297" y2="21.53208583485582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58713830216066" x2="22.81883560169719" y1="21.54040359122622" y2="21.54040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51457106407813" x2="19.32090482830307" y1="24.05654513693459" y2="21.43563107372743"/>
   <ellipse cx="14.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.3644003886642" x2="22.3644003886642" y1="30.19213090500035" y2="30.19213090500035"/>
   <ellipse cx="21.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.19220382559271" x2="17.19220382559271" y1="23.07076893362116" y2="23.07076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.4422038255927" x2="16.4422038255927" y1="16.82076893362115" y2="16.82076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.92264294445647" x2="20.87303257583197" y1="16.99992879670395" y2="15.47611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.873032575832" x2="20.37063985073817" y1="15.47611741162252" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.87303257583199" x2="23.32581493230132" y1="15.47611741162254" y2="16.60543752773795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.94220382559271" x2="16.94220382559271" y1="16.32076893362115" y2="16.32076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="16.07581493230131" y1="22.72611741162255" y2="23.85543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="13.12063985073816" y1="22.72611741162255" y2="20.07298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583197" y1="24.24992879670398" y2="22.72611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="16.07581493230131" y1="15.47611741162254" y2="16.60543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="13.12063985073816" y1="15.47611741162253" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583196" y1="16.99992879670396" y2="15.47611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.59103363843511" x2="17.59103363843511" y1="25.15822158129307" y2="28.16666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Disconnector:刀闸333_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,17.5) scale(1,1) translate(0,0)" width="6" x="3" y="12"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="12.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:刀闸333_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="4.583333333333332" y2="25.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,17.5) scale(1,1) translate(0,0)" width="6" x="3" y="12"/>
  </symbol>
  <symbol id="Disconnector:刀闸333_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:四卷PT_0" viewBox="0,0,18,18">
   <use terminal-index="0" type="0" x="9" xlink:href="#terminal" y="0.2666666666666639"/>
   <ellipse cx="9.25" cy="5.25" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.25" cy="9.17" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.08" cy="12.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.82" cy="9.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="5" y1="11.75" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="5" y1="7.13888888888889" y2="9.138888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.25" x2="9.25" y1="2.88888888888889" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.25" x2="9.25" y1="7.500000000000001" y2="4.88888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="9" y1="10.63888888888889" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="9" y1="15.25" y2="12.63888888888889"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="12.31481481481481" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.94444444444444" x2="13.62962962962963" y1="9.96612466124661" y2="7.499999999999999"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="14.94444444444444" y1="9.966124661246614" y2="9.966124661246614"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="ACLineSegment:线路（规范制图）_0" viewBox="0,0,5,20">
   <use terminal-index="0" type="0" x="2.5" xlink:href="#terminal" y="19.35"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.5" x2="2.5" y1="0.5833333333333321" y2="19.26666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV滚朋羊小一级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="53.14" xlink:href="logo.png" y="36.43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.143,68.4286) scale(1,1) translate(0,0)" writing-mode="lr" x="202.14" xml:space="preserve" y="71.93000000000001" zvalue="465"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,204.643,66.1189) scale(1,1) translate(0,0)" writing-mode="lr" x="204.64" xml:space="preserve" y="75.12" zvalue="466">110kV滚朋羊小一级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="71" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,85.375,167) scale(1,1) translate(0,0)" width="72.88" x="48.94" y="155" zvalue="535"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.375,167) scale(1,1) translate(0,0)" writing-mode="lr" x="85.38" xml:space="preserve" y="171.5" zvalue="535">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,579.917,622.592) scale(1,1) translate(0,3.37816e-13)" writing-mode="lr" x="579.92" xml:space="preserve" y="627.09" zvalue="363">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,700.07,412.852) scale(1,1) translate(7.19186e-14,-8.80687e-14)" writing-mode="lr" x="700.0700503887324" xml:space="preserve" y="417.3524560960195" zvalue="365">6.3kV母线PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,682.692,526.262) scale(1,1) translate(0,0)" writing-mode="lr" x="682.6900000000001" xml:space="preserve" y="530.76" zvalue="367">6901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,935.319,234.807) scale(1,1) translate(0,0)" writing-mode="lr" x="935.3200000000001" xml:space="preserve" y="239.31" zvalue="369">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,944.934,292.5) scale(1,1) translate(0,0)" writing-mode="lr" x="944.9299999999999" xml:space="preserve" y="297" zvalue="371">151</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1012.82,266.027) scale(1,1) translate(0,0)" writing-mode="lr" x="1012.82" xml:space="preserve" y="270.53" zvalue="374">60</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1013.6,351.684) scale(1,1) translate(-2.1873e-13,-2.23748e-13)" writing-mode="lr" x="1013.6" xml:space="preserve" y="356.18" zvalue="377">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,788.087,397.098) scale(1,1) translate(0,0)" writing-mode="lr" x="788.09" xml:space="preserve" y="401.6" zvalue="381">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,948.479,512.137) scale(1,1) translate(0,0)" writing-mode="lr" x="948.48" xml:space="preserve" y="516.64" zvalue="383">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1008.76,372.289) scale(1,1) translate(0,0)" writing-mode="lr" x="1008.76" xml:space="preserve" y="376.79" zvalue="384">10MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1009.84,219.21) scale(1,1) translate(0,0)" writing-mode="lr" x="1009.84" xml:space="preserve" y="223.71" zvalue="386">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,927.449,85.9658) scale(1,1) translate(0,0)" writing-mode="lr" x="927.45" xml:space="preserve" y="90.47" zvalue="388">110kV滚朋羊小一级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1003.83,461.316) scale(1,1) translate(0,0)" writing-mode="lr" x="1003.83" xml:space="preserve" y="465.82" zvalue="392">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,665.312,669.339) scale(1,1) translate(0,0)" writing-mode="lr" x="665.3099999999999" xml:space="preserve" y="673.84" zvalue="400">651</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" x="710.1015625" xml:space="preserve" y="898.2565073643904" zvalue="403">#1发电机   </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="710.1015625" xml:space="preserve" y="914.2565073643904" zvalue="403">4.5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,667.857,815.003) scale(1,1) translate(0,0)" writing-mode="lr" x="667.86" xml:space="preserve" y="819.5" zvalue="405">6911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,815.181,819.124) scale(1,1) translate(0,0)" writing-mode="lr" x="815.1799999999999" xml:space="preserve" y="823.62" zvalue="408">6912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,802.933,708.574) scale(1,1) translate(0,0)" writing-mode="lr" x="802.9299999999999" xml:space="preserve" y="713.0700000000001" zvalue="415">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="162" stroke="rgb(255,255,255)" text-anchor="middle" x="771.1484375" xml:space="preserve" y="665.5377573643904" zvalue="418">高频切机第</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="162" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="771.1484375" xml:space="preserve" y="681.5377573643904" zvalue="418">二轮</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1035.59,674.49) scale(1,1) translate(0,0)" writing-mode="lr" x="1035.59" xml:space="preserve" y="678.99" zvalue="420">652</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" x="1082.2734375" xml:space="preserve" y="908.8888364189645" zvalue="423">#2发电机   </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1082.2734375" xml:space="preserve" y="924.8888364189645" zvalue="423">4.5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1038.14,820.154) scale(1,1) translate(0,0)" writing-mode="lr" x="1038.14" xml:space="preserve" y="824.65" zvalue="424">6921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1185.46,824.275) scale(1,1) translate(0,0)" writing-mode="lr" x="1185.46" xml:space="preserve" y="828.78" zvalue="427">6922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1173.21,713.725) scale(1,1) translate(0,0)" writing-mode="lr" x="1173.21" xml:space="preserve" y="718.22" zvalue="435">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" x="1141.4140625" xml:space="preserve" y="670.6940073643904" zvalue="437">高频切机第</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1141.4140625" xml:space="preserve" y="686.6940073643904" zvalue="437">二轮</text>
  <line fill="none" id="120" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="387.1428571428571" x2="387.1428571428571" y1="4.428571428571331" y2="1034.428571428571" zvalue="467"/>
  <line fill="none" id="118" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.14285714285757" x2="380.1428571428571" y1="140.2990640426538" y2="140.2990640426538" zvalue="469"/>
  <line fill="none" id="116" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.14285714285757" x2="380.1428571428571" y1="610.2990640426539" y2="610.2990640426539" zvalue="471"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.14285714285711" x2="103.1428571428571" y1="925.4285714285713" y2="925.4285714285713"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.14285714285711" x2="103.1428571428571" y1="964.5918714285714" y2="964.5918714285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.14285714285711" x2="13.14285714285711" y1="925.4285714285713" y2="964.5918714285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.1428571428571" x2="103.1428571428571" y1="925.4285714285713" y2="964.5918714285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.1428571428571" x2="373.1428571428571" y1="925.4285714285713" y2="925.4285714285713"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.1428571428571" x2="373.1428571428571" y1="964.5918714285714" y2="964.5918714285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.1428571428571" x2="103.1428571428571" y1="925.4285714285713" y2="964.5918714285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="373.1428571428571" x2="373.1428571428571" y1="925.4285714285713" y2="964.5918714285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.14285714285711" x2="103.1428571428571" y1="964.5918414285713" y2="964.5918414285713"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.14285714285711" x2="103.1428571428571" y1="992.5102414285714" y2="992.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.14285714285711" x2="13.14285714285711" y1="964.5918414285713" y2="992.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.1428571428571" x2="103.1428571428571" y1="964.5918414285713" y2="992.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.1428571428571" x2="193.1428571428571" y1="964.5918414285713" y2="964.5918414285713"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.1428571428571" x2="193.1428571428571" y1="992.5102414285714" y2="992.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.1428571428571" x2="103.1428571428571" y1="964.5918414285713" y2="992.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.1428571428571" x2="193.1428571428571" y1="964.5918414285713" y2="992.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.1428571428572" x2="283.1428571428572" y1="964.5918414285713" y2="964.5918414285713"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.1428571428572" x2="283.1428571428572" y1="992.5102414285714" y2="992.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.1428571428572" x2="193.1428571428572" y1="964.5918414285713" y2="992.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.1428571428572" x2="283.1428571428572" y1="964.5918414285713" y2="992.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.1428571428571" x2="373.1428571428571" y1="964.5918414285713" y2="964.5918414285713"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.1428571428571" x2="373.1428571428571" y1="992.5102414285714" y2="992.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.1428571428571" x2="283.1428571428571" y1="964.5918414285713" y2="992.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="373.1428571428571" x2="373.1428571428571" y1="964.5918414285713" y2="992.5102414285714"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.14285714285711" x2="103.1428571428571" y1="992.5101714285713" y2="992.5101714285713"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.14285714285711" x2="103.1428571428571" y1="1020.428571428571" y2="1020.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="13.14285714285711" x2="13.14285714285711" y1="992.5101714285713" y2="1020.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.1428571428571" x2="103.1428571428571" y1="992.5101714285713" y2="1020.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.1428571428571" x2="193.1428571428571" y1="992.5101714285713" y2="992.5101714285713"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.1428571428571" x2="193.1428571428571" y1="1020.428571428571" y2="1020.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="103.1428571428571" x2="103.1428571428571" y1="992.5101714285713" y2="1020.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.1428571428571" x2="193.1428571428571" y1="992.5101714285713" y2="1020.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.1428571428572" x2="283.1428571428572" y1="992.5101714285713" y2="992.5101714285713"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.1428571428572" x2="283.1428571428572" y1="1020.428571428571" y2="1020.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="193.1428571428572" x2="193.1428571428572" y1="992.5101714285713" y2="1020.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.1428571428572" x2="283.1428571428572" y1="992.5101714285713" y2="1020.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.1428571428571" x2="373.1428571428571" y1="992.5101714285713" y2="992.5101714285713"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.1428571428571" x2="373.1428571428571" y1="1020.428571428571" y2="1020.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="283.1428571428571" x2="283.1428571428571" y1="992.5101714285713" y2="1020.428571428571"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="373.1428571428571" x2="373.1428571428571" y1="992.5101714285713" y2="1020.428571428571"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.1429,945.429) scale(1,1) translate(0,0)" writing-mode="lr" x="58.14" xml:space="preserve" y="951.4299999999999" zvalue="475">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="105" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.1429,979.429) scale(1,1) translate(0,0)" writing-mode="lr" x="55.14" xml:space="preserve" y="985.4299999999999" zvalue="476">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.143,979.429) scale(1,1) translate(0,0)" writing-mode="lr" x="237.14" xml:space="preserve" y="985.4299999999999" zvalue="477">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.1429,1007.43) scale(1,1) translate(0,0)" writing-mode="lr" x="54.14" xml:space="preserve" y="1013.43" zvalue="478">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236.143,1007.43) scale(1,1) translate(0,0)" writing-mode="lr" x="236.14" xml:space="preserve" y="1013.43" zvalue="479">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.6429,639.929) scale(1,1) translate(0,-2.75748e-13)" writing-mode="lr" x="78.64285714285711" xml:space="preserve" y="644.4285714285714" zvalue="482">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,238.197,947.429) scale(1,1) translate(0,0)" writing-mode="lr" x="238.2" xml:space="preserve" y="953.4299999999999" zvalue="490"> GunPengYangX1-01-2012</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,148.197,979.429) scale(1,1) translate(0,0)" writing-mode="lr" x="148.2" xml:space="preserve" y="985.4299999999999" zvalue="491"> 杨立超</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,328.197,979.429) scale(1,1) translate(0,0)" writing-mode="lr" x="328.2" xml:space="preserve" y="985.4299999999999" zvalue="492">20210201</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1384.17,505.657) scale(1,1) translate(0,0)" writing-mode="lr" x="1384.17" xml:space="preserve" y="510.16" zvalue="497">6541</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1491.82,481.406) scale(1,1) translate(0,0)" writing-mode="lr" x="1491.82" xml:space="preserve" y="485.91" zvalue="500">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1407.59,365.59) scale(1,1) translate(0,0)" writing-mode="lr" x="1407.59" xml:space="preserve" y="370.09" zvalue="501">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1604.91,380.406) scale(1,1) translate(0,0)" writing-mode="lr" x="1604.91" xml:space="preserve" y="384.91" zvalue="506">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1599.39,216.254) scale(1,1) translate(0,0)" writing-mode="lr" x="1599.39" xml:space="preserve" y="220.75" zvalue="509">至卡场镇供电所10kV近区线</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="192.75" y1="228.75" y2="228.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="192.75" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="11.75" y1="228.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="192.75" y1="228.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="373.75" y1="228.75" y2="228.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="373.75" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="192.75" y1="228.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="373.75" x2="373.75" y1="228.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="192.75" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="192.75" y1="279" y2="279"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="11.75" y1="254.75" y2="279"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="192.75" y1="254.75" y2="279"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="373.75" y1="254.75" y2="254.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="373.75" y1="279" y2="279"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="192.75" y1="254.75" y2="279"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="373.75" x2="373.75" y1="254.75" y2="279"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="192.75" y1="279" y2="279"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="192.75" y1="301.75" y2="301.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="11.75" y1="279" y2="301.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="192.75" y1="279" y2="301.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="373.75" y1="279" y2="279"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="373.75" y1="301.75" y2="301.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="192.75" y1="279" y2="301.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="373.75" x2="373.75" y1="279" y2="301.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="192.75" y1="301.75" y2="301.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="192.75" y1="324.5" y2="324.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="11.75" y1="301.75" y2="324.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="192.75" y1="301.75" y2="324.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="373.75" y1="301.75" y2="301.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="373.75" y1="324.5" y2="324.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="192.75" y1="301.75" y2="324.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="373.75" x2="373.75" y1="301.75" y2="324.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="192.75" y1="324.5" y2="324.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="192.75" y1="347.25" y2="347.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="11.75" y1="324.5" y2="347.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="192.75" y1="324.5" y2="347.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="373.75" y1="324.5" y2="324.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="373.75" y1="347.25" y2="347.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="192.75" y1="324.5" y2="347.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="373.75" x2="373.75" y1="324.5" y2="347.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="192.75" y1="347.25" y2="347.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="192.75" y1="370" y2="370"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="11.75" y1="347.25" y2="370"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="192.75" y1="347.25" y2="370"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="373.75" y1="347.25" y2="347.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="373.75" y1="370" y2="370"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="192.75" y1="347.25" y2="370"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="373.75" x2="373.75" y1="347.25" y2="370"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="192.75" y1="370" y2="370"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="192.75" y1="392.75" y2="392.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="11.75" x2="11.75" y1="370" y2="392.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="192.75" y1="370" y2="392.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="373.75" y1="370" y2="370"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="373.75" y1="392.75" y2="392.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="192.75" x2="192.75" y1="370" y2="392.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="373.75" x2="373.75" y1="370" y2="392.75"/>
  <line fill="none" id="83" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.750000000000455" x2="377.75" y1="474.6204926140824" y2="474.6204926140824" zvalue="523"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.149,167.591) scale(1,1) translate(0,0)" writing-mode="lr" x="201.15" xml:space="preserve" y="172.09" zvalue="524">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,306.149,167.591) scale(1,1) translate(0,0)" writing-mode="lr" x="306.15" xml:space="preserve" y="172.09" zvalue="525">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.25,241.75) scale(1,1) translate(0,0)" writing-mode="lr" x="15.75" xml:space="preserve" y="246.25" zvalue="526">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.75,241.75) scale(1,1) translate(0,0)" writing-mode="lr" x="196.25" xml:space="preserve" y="246.25" zvalue="527">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.4375,339) scale(1,1) translate(0,0)" writing-mode="lr" x="58.44" xml:space="preserve" y="343.5" zvalue="529">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,57.25,267.75) scale(1,1) translate(0,0)" writing-mode="lr" x="14.75" xml:space="preserve" y="272.25" zvalue="536">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,237.75,267.75) scale(1,1) translate(0,0)" writing-mode="lr" x="195.25" xml:space="preserve" y="272.25" zvalue="537">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.4375,361) scale(1,1) translate(0,0)" writing-mode="lr" x="58.44" xml:space="preserve" y="365.5" zvalue="540">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.438,360) scale(1,1) translate(0,0)" writing-mode="lr" x="226.44" xml:space="preserve" y="364.5" zvalue="541">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,58.4375,384) scale(1,1) translate(0,0)" writing-mode="lr" x="58.44" xml:space="preserve" y="388.5" zvalue="543">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.438,383) scale(1,1) translate(0,0)" writing-mode="lr" x="226.44" xml:space="preserve" y="387.5" zvalue="544">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,57.25,290.75) scale(1,1) translate(0,0)" writing-mode="lr" x="14.75" xml:space="preserve" y="295.25" zvalue="546">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,237.25,289.75) scale(1,1) translate(0,0)" writing-mode="lr" x="194.75" xml:space="preserve" y="294.25" zvalue="548">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1408.8,669.49) scale(1,1) translate(1.54987e-13,0)" writing-mode="lr" x="1408.8" xml:space="preserve" y="673.99" zvalue="580">653</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" x="1451.2734375" xml:space="preserve" y="904.8888364189645" zvalue="584">#3发电机   </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1451.2734375" xml:space="preserve" y="920.8888364189645" zvalue="584">4.5MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1409.32,820.154) scale(1,1) translate(0,0)" writing-mode="lr" x="1409.32" xml:space="preserve" y="824.65" zvalue="585">6931</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1556.48,827.406) scale(1,1) translate(0,0)" writing-mode="lr" x="1556.48" xml:space="preserve" y="831.91" zvalue="588">6932</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1532.36,709.725) scale(1,1) translate(0,0)" writing-mode="lr" x="1532.36" xml:space="preserve" y="714.22" zvalue="596">67</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="48.94" y="155" zvalue="535"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="253">
   <path class="kv6" d="M 584.64 600.57 L 1665.72 600.57" stroke-width="6" zvalue="362"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674422292483" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674422292483"/></metadata>
  <path d="M 584.64 600.57 L 1665.72 600.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="252">
   <use class="kv6" height="30" transform="rotate(0,709.085,466.167) scale(1.61895,1.88877) translate(-260.262,-206.026)" width="35" x="680.7530645330714" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="437.8357578026743" zvalue="364"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454810009602" ObjectName="6.3kV母线PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,709.085,466.167) scale(1.61895,1.88877) translate(-260.262,-206.026)" width="35" x="680.7530645330714" y="437.8357578026743"/></g>
  <g id="199">
   <use class="kv6" height="26" transform="rotate(90,656.717,568.478) scale(1.26265,1.26265) translate(-135.032,-114.838)" width="12" x="649.1412246233247" xlink:href="#Accessory:避雷器1_0" y="552.0632496415853" zvalue="395"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454810206210" ObjectName="#1发电机避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,656.717,568.478) scale(1.26265,1.26265) translate(-135.032,-114.838)" width="12" x="649.1412246233247" y="552.0632496415853"/></g>
  <g id="187">
   <use class="kv6" height="40" transform="rotate(0,787.365,862.394) scale(1.03024,-1.03024) translate(-22.6569,-1698.87)" width="30" x="771.9114013948582" xlink:href="#Accessory:带熔断器的线路PT1_0" y="841.7894179729856" zvalue="411"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454810337282" ObjectName="#1发电机PT2"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,787.365,862.394) scale(1.03024,-1.03024) translate(-22.6569,-1698.87)" width="30" x="771.9114013948582" y="841.7894179729856"/></g>
  <g id="186">
   <use class="kv6" height="29" transform="rotate(0,750.978,865.485) scale(1.03024,-1.03024) translate(-21.5889,-1705.13)" width="30" x="735.5245520881131" xlink:href="#Accessory:PT12321_0" y="850.5464515608852" zvalue="412"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454810271746" ObjectName="#1发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,750.978,865.485) scale(1.03024,-1.03024) translate(-21.5889,-1705.13)" width="30" x="735.5245520881131" y="850.5464515608852"/></g>
  <g id="152">
   <use class="kv6" height="18" transform="rotate(0,1015.47,869.606) scale(1.71707,1.71707) translate(-417.619,-356.703)" width="18" x="1000.019729013178" xlink:href="#Accessory:四卷PT_0" y="854.1522889206087" zvalue="430"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454810992642" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1015.47,869.606) scale(1.71707,1.71707) translate(-417.619,-356.703)" width="18" x="1000.019729013178" y="854.1522889206087"/></g>
  <g id="151">
   <use class="kv6" height="40" transform="rotate(0,1157.65,867.545) scale(1.03024,-1.03024) translate(-33.5253,-1709.02)" width="30" x="1142.192744910842" xlink:href="#Accessory:带熔断器的线路PT1_0" y="846.9406142011619" zvalue="431"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454810927106" ObjectName="#2发电机PT2"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1157.65,867.545) scale(1.03024,-1.03024) translate(-33.5253,-1709.02)" width="30" x="1142.192744910842" y="846.9406142011619"/></g>
  <g id="150">
   <use class="kv6" height="29" transform="rotate(0,1130.86,870.636) scale(1.03024,-1.03024) translate(-32.739,-1715.28)" width="30" x="1115.406524524325" xlink:href="#Accessory:PT12321_0" y="855.6976477890619" zvalue="432"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454810861570" ObjectName="#2发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1130.86,870.636) scale(1.03024,-1.03024) translate(-32.739,-1715.28)" width="30" x="1115.406524524325" y="855.6976477890619"/></g>
  <g id="144">
   <use class="kv6" height="18" transform="rotate(0,645.707,870.121) scale(1.71707,1.71707) translate(-263.201,-356.919)" width="18" x="630.2535051200118" xlink:href="#Accessory:四卷PT_0" y="854.6674085434264" zvalue="439"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454811254786" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,645.707,870.121) scale(1.71707,1.71707) translate(-263.201,-356.919)" width="18" x="630.2535051200118" y="854.6674085434264"/></g>
  <g id="107">
   <use class="kv6" height="18" transform="rotate(0,1384.47,869.606) scale(1.71707,1.71707) translate(-571.718,-356.703)" width="18" x="1369.019729013178" xlink:href="#Accessory:四卷PT_0" y="854.1522889206087" zvalue="591"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454811582466" ObjectName="#3发电机PT1"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1384.47,869.606) scale(1.71707,1.71707) translate(-571.718,-356.703)" width="18" x="1369.019729013178" y="854.1522889206087"/></g>
  <g id="106">
   <use class="kv6" height="40" transform="rotate(0,1526.65,865.545) scale(1.03024,-1.03024) translate(-44.356,-1705.08)" width="30" x="1511.192744910842" xlink:href="#Accessory:带熔断器的线路PT1_0" y="844.9406142011619" zvalue="592"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454811516930" ObjectName="#3发电机PT2"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1526.65,865.545) scale(1.03024,-1.03024) translate(-44.356,-1705.08)" width="30" x="1511.192744910842" y="844.9406142011619"/></g>
  <g id="103">
   <use class="kv6" height="29" transform="rotate(0,1494.86,871.636) scale(1.03024,-1.03024) translate(-43.4231,-1717.25)" width="30" x="1479.406524524326" xlink:href="#Accessory:PT12321_0" y="856.697647789062" zvalue="593"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454811451394" ObjectName="#3发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1494.86,871.636) scale(1.03024,-1.03024) translate(-43.4231,-1717.25)" width="30" x="1479.406524524326" y="856.697647789062"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="251">
   <use class="kv6" height="26" transform="rotate(0,709.553,527.292) scale(1.36704,-0.925379) translate(-188.306,-1098.07)" width="12" x="701.3508195330525" xlink:href="#Disconnector:刀闸333_0" y="515.2621887576938" zvalue="366"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454809944066" ObjectName="6.3kV母线6901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454809944066"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,709.553,527.292) scale(1.36704,-0.925379) translate(-188.306,-1098.07)" width="12" x="701.3508195330525" y="515.2621887576938"/></g>
  <g id="250">
   <use class="kv110" height="30" transform="rotate(0,923.528,235.837) scale(1.14471,0.839454) translate(-115.664,42.6957)" width="15" x="914.9429604773541" xlink:href="#Disconnector:刀闸_0" y="223.2452237430028" zvalue="368"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454809878530" ObjectName="#1主变110kV侧1516隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454809878530"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,923.528,235.837) scale(1.14471,0.839454) translate(-115.664,42.6957)" width="15" x="914.9429604773541" y="223.2452237430028"/></g>
  <g id="192">
   <use class="kv6" height="26" transform="rotate(0,645.192,816.033) scale(1.03024,1.03024) translate(-18.756,-23.5588)" width="12" x="639.0105387079116" xlink:href="#Disconnector:刀闸333_0" y="802.6403266388463" zvalue="404"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454810468354" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454810468354"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,645.192,816.033) scale(1.03024,1.03024) translate(-18.756,-23.5588)" width="12" x="639.0105387079116" y="802.6403266388463"/></g>
  <g id="190">
   <use class="kv6" height="26" transform="rotate(0,787.365,816.033) scale(1.03024,1.03024) translate(-22.929,-23.5588)" width="12" x="781.1835546055754" xlink:href="#Disconnector:刀闸333_0" y="802.6403266388463" zvalue="407"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454810402818" ObjectName="#1发电机6912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454810402818"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,787.365,816.033) scale(1.03024,1.03024) translate(-22.929,-23.5588)" width="12" x="781.1835546055754" y="802.6403266388463"/></g>
  <g id="158">
   <use class="kv6" height="26" transform="rotate(0,1015.47,821.185) scale(1.03024,1.03024) translate(-29.6244,-23.71)" width="12" x="1009.291882223895" xlink:href="#Disconnector:刀闸333_0" y="807.7915228670226" zvalue="423"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454811123714" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454811123714"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1015.47,821.185) scale(1.03024,1.03024) translate(-29.6244,-23.71)" width="12" x="1009.291882223895" y="807.7915228670226"/></g>
  <g id="156">
   <use class="kv6" height="26" transform="rotate(0,1157.65,821.185) scale(1.03024,1.03024) translate(-33.7974,-23.71)" width="12" x="1151.464898121559" xlink:href="#Disconnector:刀闸333_0" y="807.7915228670226" zvalue="426"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454811058178" ObjectName="#2发电机6922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454811058178"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1157.65,821.185) scale(1.03024,1.03024) translate(-33.7974,-23.71)" width="12" x="1151.464898121559" y="807.7915228670226"/></g>
  <g id="55">
   <use class="kv6" height="26" transform="rotate(0,1404.39,506.687) scale(1.36704,0.925379) translate(-374.865,39.8884)" width="12" x="1396.191616990957" xlink:href="#Disconnector:刀闸333_0" y="494.6574038449888" zvalue="496"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454811844610" ObjectName="#1站用变6541隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454811844610"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1404.39,506.687) scale(1.36704,0.925379) translate(-374.865,39.8884)" width="12" x="1396.191616990957" y="494.6574038449888"/></g>
  <g id="35">
   <use class="kv10" height="30" transform="rotate(0,1599.39,288.714) scale(-1.39655,0.686826) translate(-2741.66,126.948)" width="15" x="1588.915631729185" xlink:href="#Disconnector:令克_0" y="278.4120823961853" zvalue="507"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454812172290" ObjectName="#2站用变刀闸"/>
   <cge:TPSR_Ref TObjectID="6192454812172290"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1599.39,288.714) scale(-1.39655,0.686826) translate(-2741.66,126.948)" width="15" x="1588.915631729185" y="278.4120823961853"/></g>
  <g id="114">
   <use class="kv6" height="26" transform="rotate(0,1384.47,821.185) scale(1.03024,1.03024) translate(-40.4552,-23.71)" width="12" x="1378.291882223895" xlink:href="#Disconnector:刀闸333_0" y="807.7915228670226" zvalue="583"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454811713538" ObjectName="#3发电机6931隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454811713538"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1384.47,821.185) scale(1.03024,1.03024) translate(-40.4552,-23.71)" width="12" x="1378.291882223895" y="807.7915228670226"/></g>
  <g id="112">
   <use class="kv6" height="26" transform="rotate(0,1526.65,824.316) scale(1.03024,1.03024) translate(-44.6282,-23.8019)" width="12" x="1520.464898121559" xlink:href="#Disconnector:刀闸333_0" y="810.922601939519" zvalue="587"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454811648002" ObjectName="#3发电机6932隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454811648002"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1526.65,824.316) scale(1.03024,1.03024) translate(-44.6282,-23.8019)" width="12" x="1520.464898121559" y="810.922601939519"/></g>
 </g>
 <g id="BreakerClass">
  <g id="30">
   <use class="kv110" height="20" transform="rotate(0,923.528,293.53) scale(1.25918,1.14471) translate(-188.797,-35.66)" width="10" x="917.2323810368559" xlink:href="#Breaker:开关_0" y="282.0833317715045" zvalue="370"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925219713027" ObjectName="#1主变110kV侧151断路器"/>
   <cge:TPSR_Ref TObjectID="6473925219713027"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,923.528,293.53) scale(1.25918,1.14471) translate(-188.797,-35.66)" width="10" x="917.2323810368559" y="282.0833317715045"/></g>
  <g id="237">
   <use class="kv6" height="20" transform="rotate(0,921.472,513.168) scale(1.54536,1.39082) translate(-322.462,-140.293)" width="10" x="913.745147386758" xlink:href="#Breaker:手车开关_0" y="499.2594062899803" zvalue="382"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925219647491" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473925219647491"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,921.472,513.168) scale(1.54536,1.39082) translate(-322.462,-140.293)" width="10" x="913.745147386758" y="499.2594062899803"/></g>
  <g id="195">
   <use class="kv6" height="20" transform="rotate(0,709.097,664.703) scale(2.06048,2.06048) translate(-359.653,-331.502)" width="10" x="698.794878701992" xlink:href="#Breaker:小车断路器_0" y="644.0979555850008" zvalue="399"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925219778563" ObjectName="#1发电机651断路器"/>
   <cge:TPSR_Ref TObjectID="6473925219778563"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,709.097,664.703) scale(2.06048,2.06048) translate(-359.653,-331.502)" width="10" x="698.794878701992" y="644.0979555850008"/></g>
  <g id="161">
   <use class="kv6" height="20" transform="rotate(0,1079.38,669.854) scale(2.06048,2.06048) translate(-550.228,-334.153)" width="10" x="1069.076222217975" xlink:href="#Breaker:小车断路器_0" y="649.2491518131768" zvalue="419"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925219844099" ObjectName="#2发电机652断路器"/>
   <cge:TPSR_Ref TObjectID="6473925219844099"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1079.38,669.854) scale(2.06048,2.06048) translate(-550.228,-334.153)" width="10" x="1069.076222217975" y="649.2491518131768"/></g>
  <g id="123">
   <use class="kv6" height="20" transform="rotate(0,1448.38,665.854) scale(2.06048,2.06048) translate(-740.143,-332.094)" width="10" x="1438.076222217975" xlink:href="#Breaker:小车断路器_0" y="645.2491518131768" zvalue="579"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925219909635" ObjectName="#3发电机653断路器"/>
   <cge:TPSR_Ref TObjectID="6473925219909635"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1448.38,665.854) scale(2.06048,2.06048) translate(-740.143,-332.094)" width="10" x="1438.076222217975" y="645.2491518131768"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="248">
   <path class="kv110" d="M 923.6 248.21 L 923.6 282.58" stroke-width="1" zvalue="372"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@1" LinkObjectIDznd="30@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 923.6 248.21 L 923.6 282.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="242">
   <path class="kv110" d="M 923.63 223.66 L 923.63 144.52" stroke-width="1" zvalue="375"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@0" LinkObjectIDznd="232@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 923.63 223.66 L 923.63 144.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="240">
   <path class="kv110" d="M 923.61 304.46 L 923.61 323.01" stroke-width="1" zvalue="378"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="30@1" LinkObjectIDznd="241@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 923.61 304.46 L 923.61 323.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="239">
   <path class="kv110" d="M 923.58 350.09 L 829.12 350.09 L 829.12 374.85" stroke-width="1" zvalue="379"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="241@2" LinkObjectIDznd="238@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 923.58 350.09 L 829.12 350.09 L 829.12 374.85" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="231">
   <path class="kv110" d="M 1003.72 252.23 L 923.6 252.23" stroke-width="1" zvalue="389"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="246@0" LinkObjectIDznd="248" MaxPinNum="2"/>
   </metadata>
  <path d="M 1003.72 252.23 L 923.6 252.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="kv6" d="M 923.55 416.95 L 923.55 458.63 L 921.47 458.63 L 921.47 500.3" stroke-width="1" zvalue="390"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="241@1" LinkObjectIDznd="237@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 923.55 416.95 L 923.55 458.63 L 921.47 458.63 L 921.47 500.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="kv6" d="M 993.7 445.12 L 923.55 444.73" stroke-width="1" zvalue="393"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="204" MaxPinNum="2"/>
   </metadata>
  <path d="M 993.7 445.12 L 923.55 444.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="200">
   <path class="kv6" d="M 921.47 600.57 L 921.47 525.69" stroke-width="1" zvalue="394"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="253@3" LinkObjectIDznd="237@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 921.47 600.57 L 921.47 525.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv6" d="M 709.56 539.25 L 709.56 600.57" stroke-width="1" zvalue="397"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="251@1" LinkObjectIDznd="253@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 709.56 539.25 L 709.56 600.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="kv6" d="M 672.33 568.52 L 709.56 568.52" stroke-width="1" zvalue="398"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@0" LinkObjectIDznd="197" MaxPinNum="2"/>
   </metadata>
  <path d="M 672.33 568.52 L 709.56 568.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv6" d="M 709.07 830.33 L 709.1 683.25" stroke-width="1" zvalue="401"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@0" LinkObjectIDznd="195@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 709.07 830.33 L 709.1 683.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="kv6" d="M 645.19 802.72 L 645.19 797.88 L 709.07 797.88" stroke-width="1" zvalue="406"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@1" LinkObjectIDznd="194" MaxPinNum="2"/>
   </metadata>
  <path d="M 645.19 802.72 L 645.19 797.88 L 709.07 797.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv6" d="M 709.07 798.03 L 787.37 798.03 L 787.37 802.72" stroke-width="1" zvalue="409"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194" LinkObjectIDznd="190@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 709.07 798.03 L 787.37 798.03 L 787.37 802.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv6" d="M 787.45 829.39 L 787.36 843.33" stroke-width="1" zvalue="410"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@0" LinkObjectIDznd="187@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 787.45 829.39 L 787.36 843.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="185">
   <path class="kv6" d="M 750.98 850.8 L 750.98 798.03" stroke-width="1" zvalue="413"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="186@0" LinkObjectIDznd="189" MaxPinNum="2"/>
   </metadata>
  <path d="M 750.98 850.8 L 750.98 798.03" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv6" d="M 754.68 702.62 L 709.09 702.62" stroke-width="1" zvalue="416"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="184@0" LinkObjectIDznd="194" MaxPinNum="2"/>
   </metadata>
  <path d="M 754.68 702.62 L 709.09 702.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv6" d="M 709.1 645.64 L 709.1 600.57" stroke-width="1" zvalue="417"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="195@0" LinkObjectIDznd="253@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 709.1 645.64 L 709.1 600.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv6" d="M 1079.35 835.48 L 1079.38 688.4" stroke-width="1" zvalue="421"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="161@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1079.35 835.48 L 1079.38 688.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv6" d="M 1015.48 807.87 L 1015.48 802.84 L 1079.35 802.84" stroke-width="1" zvalue="425"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@1" LinkObjectIDznd="160" MaxPinNum="2"/>
   </metadata>
  <path d="M 1015.48 807.87 L 1015.48 802.84 L 1079.35 802.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="kv6" d="M 1079.35 803.87 L 1157.65 803.87 L 1157.65 807.87" stroke-width="1" zvalue="428"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="160" LinkObjectIDznd="156@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1079.35 803.87 L 1157.65 803.87 L 1157.65 807.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv6" d="M 1157.73 834.54 L 1157.65 848.49" stroke-width="1" zvalue="429"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="156@0" LinkObjectIDznd="151@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1157.73 834.54 L 1157.65 848.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv6" d="M 1129.86 856.96 L 1129.57 804.87" stroke-width="1" zvalue="433"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="154" MaxPinNum="2"/>
   </metadata>
  <path d="M 1129.86 856.96 L 1129.57 804.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv6" d="M 1124.96 707.77 L 1079.37 707.77" stroke-width="1" zvalue="436"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="148@0" LinkObjectIDznd="160" MaxPinNum="2"/>
   </metadata>
  <path d="M 1124.96 707.77 L 1079.37 707.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="143">
   <path class="kv6" d="M 1015.56 834.54 L 1015.47 854.61" stroke-width="1" zvalue="440"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="152@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1015.56 834.54 L 1015.47 854.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv6" d="M 645.28 829.39 L 645.28 855.13" stroke-width="1" zvalue="441"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@0" LinkObjectIDznd="144@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 645.28 829.39 L 645.28 855.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="kv6" d="M 1079.38 650.79 L 1079.38 600.57" stroke-width="1" zvalue="442"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@0" LinkObjectIDznd="253@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1079.38 650.79 L 1079.38 600.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv6" d="M 1403.94 422.04 L 1403.94 494.73" stroke-width="1" zvalue="502"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="51@0" LinkObjectIDznd="55@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1403.94 422.04 L 1403.94 494.73" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv6" d="M 1404.51 518.69 L 1404.51 600.57" stroke-width="1" zvalue="503"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="253@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1404.51 518.69 L 1404.51 600.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv6" d="M 1479.97 457.48 L 1403.94 457.08" stroke-width="1" zvalue="504"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@0" LinkObjectIDznd="44" MaxPinNum="2"/>
   </metadata>
  <path d="M 1479.97 457.48 L 1403.94 457.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv10" d="M 1599.39 240.19 L 1599.39 279.61" stroke-width="1" zvalue="510"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@0" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1599.39 240.19 L 1599.39 279.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="kv10" d="M 1599.51 297.13 L 1599.51 330.14" stroke-width="1" zvalue="511"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@1" LinkObjectIDznd="37@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1599.51 297.13 L 1599.51 330.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv110" d="M 999.71 203.01 L 923.63 203.01" stroke-width="1" zvalue="512"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@0" LinkObjectIDznd="242" MaxPinNum="2"/>
   </metadata>
  <path d="M 999.71 203.01 L 923.63 203.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="117">
   <path class="kv6" d="M 1448.35 835.48 L 1448.38 684.4" stroke-width="1" zvalue="581"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="123@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1448.35 835.48 L 1448.38 684.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="113">
   <path class="kv6" d="M 1384.48 807.87 L 1384.48 802.84 L 1448.35 802.84" stroke-width="1" zvalue="586"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@1" LinkObjectIDznd="117" MaxPinNum="2"/>
   </metadata>
  <path d="M 1384.48 807.87 L 1384.48 802.84 L 1448.35 802.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv6" d="M 1526.73 837.68 L 1526.65 846.49" stroke-width="1" zvalue="590"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="106@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1526.73 837.68 L 1526.65 846.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv6" d="M 1493.96 707.77 L 1448.37 707.77" stroke-width="1" zvalue="597"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@0" LinkObjectIDznd="117" MaxPinNum="2"/>
   </metadata>
  <path d="M 1493.96 707.77 L 1448.37 707.77" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv6" d="M 1384.56 834.54 L 1384.47 854.61" stroke-width="1" zvalue="599"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="107@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1384.56 834.54 L 1384.47 854.61" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="kv6" d="M 1448.38 646.79 L 1448.38 600.57" stroke-width="1" zvalue="600"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@0" LinkObjectIDznd="253@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1448.38 646.79 L 1448.38 600.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv6" d="M 1448.35 804 L 1526.65 804 L 1526.65 811" stroke-width="1" zvalue="602"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117" LinkObjectIDznd="112@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1448.35 804 L 1526.65 804 L 1526.65 811" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv6" d="M 1494.86 856.96 L 1494.86 804" stroke-width="1" zvalue="604"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="126" MaxPinNum="2"/>
   </metadata>
  <path d="M 1494.86 856.96 L 1494.86 804" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv6" d="M 709.67 515.29 L 709.67 503.12 L 709.19 503.12 L 709.19 490.96" stroke-width="1" zvalue="605"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="251@0" LinkObjectIDznd="252@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 709.67 515.29 L 709.67 503.12 L 709.19 503.12 L 709.19 490.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="246">
   <use class="kv110" height="20" transform="rotate(270,1014.88,252.206) scale(-0.566649,1.14471) translate(-2808.06,-30.436)" width="10" x="1012.042921170668" xlink:href="#GroundDisconnector:地刀_0" y="240.7593570746577" zvalue="373"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454809812994" ObjectName="#1主变110kV侧15160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454809812994"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1014.88,252.206) scale(-0.566649,1.14471) translate(-2808.06,-30.436)" width="10" x="1012.042921170668" y="240.7593570746577"/></g>
  <g id="238">
   <use class="kv110" height="40" transform="rotate(0,826.403,390.799) scale(1.04613,-1.30767) translate(-35.52,-683.498)" width="40" x="805.4800301484735" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="364.6455602064402" zvalue="380"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454809681922" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454809681922"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,826.403,390.799) scale(1.04613,-1.30767) translate(-35.52,-683.498)" width="40" x="805.4800301484735" y="364.6455602064402"/></g>
  <g id="235">
   <use class="kv110" height="20" transform="rotate(270,1010.87,202.984) scale(-0.566649,1.14471) translate(-2796.98,-24.2134)" width="10" x="1008.03643521542" xlink:href="#GroundDisconnector:地刀_0" y="191.5368153387514" zvalue="385"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454809550850" ObjectName="#1主变110kV侧15167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454809550850"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1010.87,202.984) scale(-0.566649,1.14471) translate(-2796.98,-24.2134)" width="10" x="1008.03643521542" y="191.5368153387514"/></g>
  <g id="202">
   <use class="kv6" height="20" transform="rotate(270,1004.86,445.09) scale(-0.566649,1.14471) translate(-2780.36,-54.8196)" width="10" x="1002.026706282548" xlink:href="#GroundDisconnector:地刀_0" y="433.6430380630342" zvalue="391"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454810140674" ObjectName="#1主变6.3kV侧60167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454810140674"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1004.86,445.09) scale(-0.566649,1.14471) translate(-2780.36,-54.8196)" width="10" x="1002.026706282548" y="433.6430380630342"/></g>
  <g id="184">
   <use class="kv6" height="20" transform="rotate(90,765.844,702.65) scale(-0.566649,-1.14471) translate(-2119.54,-1315.03)" width="10" x="763.0112012951707" xlink:href="#GroundDisconnector:地刀_0" y="691.2028494718454" zvalue="414"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454810664962" ObjectName="#1主变6.3kV侧65167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454810664962"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,765.844,702.65) scale(-0.566649,-1.14471) translate(-2119.54,-1315.03)" width="10" x="763.0112012951707" y="691.2028494718454"/></g>
  <g id="148">
   <use class="kv6" height="20" transform="rotate(90,1136.13,707.801) scale(-0.566649,-1.14471) translate(-3143.28,-1324.68)" width="10" x="1133.292544811154" xlink:href="#GroundDisconnector:地刀_0" y="696.3540457000217" zvalue="434"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454810796034" ObjectName="#2主变6.3kV侧65267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454810796034"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1136.13,707.801) scale(-0.566649,-1.14471) translate(-3143.28,-1324.68)" width="10" x="1133.292544811154" y="696.3540457000217"/></g>
  <g id="54">
   <use class="kv6" height="20" transform="rotate(270,1491.13,457.453) scale(-0.566649,1.14471) translate(-4124.79,-56.3825)" width="10" x="1488.299630222384" xlink:href="#GroundDisconnector:地刀_0" y="446.0059090106571" zvalue="498"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454811975682" ObjectName="#1站用变65417接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454811975682"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1491.13,457.453) scale(-0.566649,1.14471) translate(-4124.79,-56.3825)" width="10" x="1488.299630222384" y="446.0059090106571"/></g>
  <g id="101">
   <use class="kv6" height="20" transform="rotate(90,1505.13,707.801) scale(-0.566649,-1.14471) translate(-4163.48,-1324.68)" width="10" x="1502.292544811154" xlink:href="#GroundDisconnector:地刀_0" y="696.3540457000217" zvalue="595"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2主变6.3kV侧65367接地开关"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1505.13,707.801) scale(-0.566649,-1.14471) translate(-4163.48,-1324.68)" width="10" x="1502.292544811154" y="696.3540457000217"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="241">
   <g id="2410">
    <use class="kv110" height="50" transform="rotate(0,923.546,369.888) scale(2.01952,1.9096) translate(-450.944,-153.449)" width="30" x="893.25" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="322.15" zvalue="376"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874593861634" ObjectName="110"/>
    </metadata>
   </g>
   <g id="2411">
    <use class="kv6" height="50" transform="rotate(0,923.546,369.888) scale(2.01952,1.9096) translate(-450.944,-153.449)" width="30" x="893.25" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="322.15" zvalue="376"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874593927170" ObjectName="6"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399534051330" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399534051330"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,923.546,369.888) scale(2.01952,1.9096) translate(-450.944,-153.449)" width="30" x="893.25" y="322.15"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="232">
   <use class="kv110" height="40" transform="rotate(0,927.449,122.768) scale(1.30824,1.14471) translate(-213.126,-12.6257)" width="35" x="904.5547042703961" xlink:href="#ACLineSegment:220kV线路_0" y="99.87407407818188" zvalue="387"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249302695941" ObjectName="110kV滚朋羊小一级线"/>
   <cge:TPSR_Ref TObjectID="8444249302695941_5066549684600833"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,927.449,122.768) scale(1.30824,1.14471) translate(-213.126,-12.6257)" width="35" x="904.5547042703961" y="99.87407407818188"/></g>
  <g id="34">
   <use class="kv10" height="20" transform="rotate(0,1599.39,233.768) scale(0.686826,0.686826) translate(728.495,103.46)" width="5" x="1597.672665317085" xlink:href="#ACLineSegment:线路（规范制图）_0" y="226.9001201144229" zvalue="508"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454812237826" ObjectName="至卡场镇供电所10kV近区线"/>
   <cge:TPSR_Ref TObjectID="6192454812237826_5066549684600833"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1599.39,233.768) scale(0.686826,0.686826) translate(728.495,103.46)" width="5" x="1597.672665317085" y="226.9001201144229"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="193">
   <use class="kv6" height="30" transform="rotate(0,709.067,853.122) scale(1.54536,1.54536) translate(-242.05,-292.887)" width="30" x="685.8864243843152" xlink:href="#Generator:发电机_0" y="829.9416666481804" zvalue="402"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#1发电机   4.5MW"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,709.067,853.122) scale(1.54536,1.54536) translate(-242.05,-292.887)" width="30" x="685.8864243843152" y="829.9416666481804"/></g>
  <g id="159">
   <use class="kv6" height="30" transform="rotate(0,1079.35,858.273) scale(1.54536,1.54536) translate(-372.723,-294.705)" width="30" x="1056.167767900299" xlink:href="#Generator:发电机_0" y="835.0928628763565" zvalue="422"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#2发电机   4.5MW"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1079.35,858.273) scale(1.54536,1.54536) translate(-372.723,-294.705)" width="30" x="1056.167767900299" y="835.0928628763565"/></g>
  <g id="115">
   <use class="kv6" height="30" transform="rotate(0,1448.35,858.273) scale(1.54536,1.54536) translate(-502.943,-294.705)" width="30" x="1425.167767900299" xlink:href="#Generator:发电机_0" y="835.0928628763565" zvalue="582"/>
   <metadata>
    <cge:PSR_Ref ObjectID="" ObjectName="#3发电机   4.5MW"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1448.35,858.273) scale(1.54536,1.54536) translate(-502.943,-294.705)" width="30" x="1425.167767900299" y="835.0928628763565"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="EnergyConsumerClass">
  <g id="51">
   <use class="kv6" height="30" transform="rotate(0,1403.82,408.517) scale(1.32459,-0.938662) translate(-339.463,-844.648)" width="28" x="1385.271558148206" xlink:href="#EnergyConsumer:站用变DY接地_0" y="394.4366450593927" zvalue="499"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454812041218" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1403.82,408.517) scale(1.32459,-0.938662) translate(-339.463,-844.648)" width="28" x="1385.271558148206" y="394.4366450593927"/></g>
  <g id="37">
   <use class="kv10" height="30" transform="rotate(0,1599.39,343.661) scale(1.32459,0.938662) translate(-387.388,21.5367)" width="28" x="1580.845424305043" xlink:href="#EnergyConsumer:站用变DY接地_0" y="329.5806315960692" zvalue="505"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454812106754" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1599.39,343.661) scale(1.32459,0.938662) translate(-387.388,21.5367)" width="28" x="1580.845424305043" y="329.5806315960692"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="74">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="74" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,158.361,241.917) scale(1,1) translate(0,0)" writing-mode="lr" x="158.52" xml:space="preserve" y="246.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136490086402" ObjectName="F"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,335.972,242.917) scale(1,1) translate(0,0)" writing-mode="lr" x="336.13" xml:space="preserve" y="247.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136490151938" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,158.361,266.917) scale(1,1) translate(0,0)" writing-mode="lr" x="158.52" xml:space="preserve" y="271.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136489955330" ObjectName="F"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,335.972,267.917) scale(1,1) translate(0,0)" writing-mode="lr" x="336.13" xml:space="preserve" y="272.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136490020866" ObjectName="F"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,336.361,359.139) scale(1,1) translate(0,0)" writing-mode="lr" x="336.52" xml:space="preserve" y="364.05" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136626335746" ObjectName="降雨量"/>
   </metadata>
  </g>
  <g id="50">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,155.111,358.389) scale(1,1) translate(0,0)" writing-mode="lr" x="155.27" xml:space="preserve" y="363.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136626204674" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="48">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,158.361,290.917) scale(1,1) translate(0,0)" writing-mode="lr" x="158.52" xml:space="preserve" y="295.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127181025285" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="46">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,338.361,289.917) scale(1,1) translate(0,0)" writing-mode="lr" x="338.52" xml:space="preserve" y="294.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127180959749" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="45">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,157.361,336.917) scale(1,1) translate(0,0)" writing-mode="lr" x="157.52" xml:space="preserve" y="341.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136484057090" ObjectName="F"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,154,380.611) scale(1,1) translate(0,0)" writing-mode="lr" x="154.15" xml:space="preserve" y="385.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136626270210" ObjectName="坝下水位"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,336.361,382.472) scale(1,1) translate(0,0)" writing-mode="lr" x="336.52" xml:space="preserve" y="387.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136626597890" ObjectName="入库流量"/>
   </metadata>
  </g>
  <g id="40">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="40" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,927.449,18.3741) scale(1,1) translate(0,0)" writing-mode="lr" x="926.98" xml:space="preserve" y="22.65" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136479535106" ObjectName="P"/>
   </metadata>
  </g>
  <g id="41">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="41" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,927.449,41.3741) scale(1,1) translate(0,0)" writing-mode="lr" x="926.98" xml:space="preserve" y="45.65" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136479600642" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="42">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="42" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,927.449,63.3741) scale(1,1) translate(0,0)" writing-mode="lr" x="926.98" xml:space="preserve" y="67.65000000000001" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136479666178" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="43">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="43" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,828.546,227.648) scale(1,1) translate(0,0)" writing-mode="lr" x="827.99" xml:space="preserve" y="231.93" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136481107970" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="57">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="57" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,828.546,264.648) scale(1,1) translate(0,0)" writing-mode="lr" x="827.99" xml:space="preserve" y="268.93" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136481173506" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="58">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="12" id="58" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,828.546,438.128) scale(1,1) translate(0,0)" writing-mode="lr" x="827.99" xml:space="preserve" y="442.41" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136481239042" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="59">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="12" id="59" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,828.546,475.128) scale(1,1) translate(0,0)" writing-mode="lr" x="827.99" xml:space="preserve" y="479.41" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136481304578" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="63" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,828.546,301.648) scale(1,1) translate(0,0)" writing-mode="lr" x="827.99" xml:space="preserve" y="305.93" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136481370114" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="64">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="64" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,828.546,512.128) scale(1,1) translate(0,0)" writing-mode="lr" x="827.99" xml:space="preserve" y="516.41" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136481697794" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="65" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,617.639,431.07) scale(1,1) translate(0,0)" writing-mode="lr" x="617.17" xml:space="preserve" y="435.85" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136483663874" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="66" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,617.639,468.07) scale(1,1) translate(0,0)" writing-mode="lr" x="617.17" xml:space="preserve" y="472.85" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136483729410" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="75">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="75" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,617.639,505.07) scale(1,1) translate(0,0)" writing-mode="lr" x="617.17" xml:space="preserve" y="509.85" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136483794946" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="85" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,848.067,887.802) scale(1,1) translate(8.82701e-14,0)" writing-mode="lr" x="847.51" xml:space="preserve" y="892.08" zvalue="1">P:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="86" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,848.067,924.802) scale(1,1) translate(8.82701e-14,0)" writing-mode="lr" x="847.51" xml:space="preserve" y="929.08" zvalue="1">Q:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="90">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="90" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,848.067,961.802) scale(1,1) translate(8.82701e-14,0)" writing-mode="lr" x="847.51" xml:space="preserve" y="966.08" zvalue="1">Ia:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="91" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1203.35,894.954) scale(1,1) translate(-2.55429e-13,0)" writing-mode="lr" x="1202.8" xml:space="preserve" y="899.23" zvalue="1">P:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="93" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1203.35,931.954) scale(1,1) translate(-2.55429e-13,0)" writing-mode="lr" x="1202.8" xml:space="preserve" y="936.23" zvalue="1">Q:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="12" id="94" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1203.35,968.954) scale(1,1) translate(-2.55429e-13,0)" writing-mode="lr" x="1202.8" xml:space="preserve" y="973.23" zvalue="1">Ia:????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="100" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,564.639,577.57) scale(1,1) translate(0,0)" writing-mode="lr" x="564.17" xml:space="preserve" y="582.35" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136483926018" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="72">
   <use height="30" transform="rotate(0,333.423,168.107) scale(0.708333,0.665547) translate(132.917,79.4611)" width="30" x="322.8" xlink:href="#State:红绿圆(方形)_0" y="158.12" zvalue="533"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374928576513" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,333.423,168.107) scale(0.708333,0.665547) translate(132.917,79.4611)" width="30" x="322.8" y="158.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,237.798,168.107) scale(0.708333,0.665547) translate(93.5417,79.4611)" width="30" x="227.17" xlink:href="#State:红绿圆(方形)_0" y="158.12" zvalue="534"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562962189189121" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,237.798,168.107) scale(0.708333,0.665547) translate(93.5417,79.4611)" width="30" x="227.17" y="158.12"/></g>
  <g id="96">
   <use height="30" transform="rotate(0,322.812,129.464) scale(1.22222,1.03092) translate(-48.6932,-3.41936)" width="90" x="267.81" xlink:href="#State:全站检修_0" y="114" zvalue="607"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549684600833" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,322.812,129.464) scale(1.22222,1.03092) translate(-48.6932,-3.41936)" width="90" x="267.81" y="114"/></g>
 </g>
</svg>