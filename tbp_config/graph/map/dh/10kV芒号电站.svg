<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549587738626" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:20210316PT_0" viewBox="0,0,15,26">
   <use terminal-index="0" type="0" x="7.45" xlink:href="#terminal" y="0.09999999999999964"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="8.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="7.5" y1="6.833333333333331" y2="0"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="4.916666666666666" y1="11.25" y2="13.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.5" x2="10.5" y1="11.25" y2="13.25"/>
   <ellipse cx="7.53" cy="11.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.5" cy="20.27" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333333" x2="7.583333333333333" y1="17.5" y2="20.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333332" x2="4.999999999999998" y1="20.5" y2="22.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333335" x2="10.58333333333334" y1="20.5" y2="22.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <rect fill="rgb(170,0,0)" fill-opacity="1" height="19.08" rx="0" ry="0" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-opacity="0" stroke-width="1" transform="rotate(180,5.08,10.04) scale(1,1) translate(0,0)" width="9.17" x="0.5" y="0.5"/>
  </symbol>
  <symbol id="Breaker:母联开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.09845679012346" xlink:href="#terminal" y="19.89845679012345"/>
   <use terminal-index="1" type="0" x="5.051543209876539" xlink:href="#terminal" y="0.1817901234567891"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="0.333333333333333" x2="9.75" y1="0.4166666666666696" y2="19.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.666666666666668" x2="0.4166666666666661" y1="0.4999999999999982" y2="19.66666666666667"/>
   <rect fill-opacity="0" height="19.58" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.04,10.04) scale(1,1) translate(0,0)" width="9.58" x="0.25" y="0.25"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-y不带中性点_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="1" x="10" xlink:href="#terminal" y="2.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.02838478538902" x2="10.02838478538902" y1="6.047799292079558" y2="8.646787392096492"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.434837295917547" x2="10.028384785389" y1="11.24577549211341" y2="8.646787392096474"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.62193227486047" x2="10.02838478538902" y1="11.24577549211341" y2="8.646787392096474"/>
   <ellipse cx="10.03" cy="9.050000000000001" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-y不带中性点_1" viewBox="0,0,20,30">
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.434837295917547" x2="10.028384785389" y1="22.74577549211341" y2="20.14678739209647"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.02838478538902" x2="10.02838478538902" y1="17.54779929207956" y2="20.14678739209649"/>
   <use terminal-index="1" type="1" x="10.08333333333333" xlink:href="#terminal" y="26.16666666666667"/>
   <line fill="none" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.62193227486047" x2="10.02838478538902" y1="22.74577549211341" y2="20.14678739209647"/>
   <ellipse cx="10.03" cy="19.8" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="Breaker:自动断路器_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="3"/>
   <use terminal-index="1" type="0" x="7.483333333333333" xlink:href="#terminal" y="28.08333333333334"/>
   <rect fill="rgb(0,0,255)" fill-opacity="1" height="3" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-30,3.75,14.75) scale(1,1) translate(0,0)" width="2" x="2.75" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="1.166666666666666" y1="19" y2="8.249999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="19" y2="28"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="8.916666666666666" y1="5.750000000000007" y2="8.333333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.916666666666664" y2="2.916666666666664"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.916666666666666" x2="6" y1="5.750000000000007" y2="8.333333333333334"/>
  </symbol>
  <symbol id="Breaker:自动断路器_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="3"/>
   <use terminal-index="1" type="0" x="7.483333333333333" xlink:href="#terminal" y="28.08333333333334"/>
   <rect fill="rgb(0,0,255)" fill-opacity="1" height="3" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-10,5.42,13) scale(1,1) translate(0,0)" width="2" x="4.42" y="11.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="19" y2="28"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="8.916666666666666" y1="5.750000000000007" y2="8.333333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.916666666666664" y2="2.916666666666664"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.916666666666666" x2="6" y1="5.750000000000007" y2="8.333333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="5.583333333333333" y1="18.91666666666666" y2="7.333333333333334"/>
  </symbol>
  <symbol id="Breaker:自动断路器_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.5" xlink:href="#terminal" y="3"/>
   <use terminal-index="1" type="0" x="7.483333333333333" xlink:href="#terminal" y="28.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="19" y2="28"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.916666666666664" y2="2.916666666666664"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="8.916666666666666" y1="5.750000000000007" y2="8.333333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.916666666666666" x2="6" y1="5.750000000000007" y2="8.333333333333334"/>
   <rect fill="rgb(0,0,255)" fill-opacity="1" height="3" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-30,6.92,14.75) scale(1,1) translate(0,0)" width="2" x="5.92" y="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.66666666666667" x2="4.333333333333333" y1="19" y2="8.249999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.333333333333333" x2="10.66666666666667" y1="19" y2="8.249999999999998"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV芒号电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="269.25" x="43.29" xlink:href="logo.png" y="42.86"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,177.911,72.8571) scale(1,1) translate(0,0)" writing-mode="lr" x="177.91" xml:space="preserve" y="76.36" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,179.619,72.5475) scale(1,1) translate(6.80408e-15,0)" writing-mode="lr" x="179.62" xml:space="preserve" y="81.55" zvalue="3">10kV芒号电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="6" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,68.4375,358) scale(1,1) translate(0,0)" width="72.88" x="32" y="346" zvalue="112"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,68.4375,358) scale(1,1) translate(0,0)" writing-mode="lr" x="68.44" xml:space="preserve" y="362.5" zvalue="112">信号一览</text>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377.2857142857144" x2="377.2857142857144" y1="10.85714285714289" y2="1040.857142857143" zvalue="4"/>
  <line fill="none" id="28" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.285714285714675" x2="370.2857142857142" y1="146.7276354712253" y2="146.7276354712253" zvalue="6"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="158.8571428571429" y2="158.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="184.8571428571429" y2="184.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="4.285714285714448" y1="158.8571428571429" y2="184.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="158.8571428571429" y2="184.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="158.8571428571429" y2="158.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="184.8571428571429" y2="184.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="158.8571428571429" y2="184.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.2857142857144" x2="366.2857142857144" y1="158.8571428571429" y2="184.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="184.8571428571429" y2="184.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="209.1071428571429" y2="209.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="4.285714285714448" y1="184.8571428571429" y2="209.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="184.8571428571429" y2="209.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="184.8571428571429" y2="184.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="209.1071428571429" y2="209.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="184.8571428571429" y2="209.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.2857142857144" x2="366.2857142857144" y1="184.8571428571429" y2="209.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="209.1071428571429" y2="209.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="231.8571428571429" y2="231.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="4.285714285714448" y1="209.1071428571429" y2="231.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="209.1071428571429" y2="231.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="209.1071428571429" y2="209.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="231.8571428571429" y2="231.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="209.1071428571429" y2="231.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.2857142857144" x2="366.2857142857144" y1="209.1071428571429" y2="231.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="231.8571428571429" y2="231.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="254.6071428571429" y2="254.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="4.285714285714448" y1="231.8571428571429" y2="254.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="231.8571428571429" y2="254.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="231.8571428571429" y2="231.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="254.6071428571429" y2="254.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="231.8571428571429" y2="254.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.2857142857144" x2="366.2857142857144" y1="231.8571428571429" y2="254.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="254.6071428571429" y2="254.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="185.2857142857144" y1="277.3571428571429" y2="277.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="4.285714285714448" x2="4.285714285714448" y1="254.6071428571429" y2="277.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="254.6071428571429" y2="277.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="254.6071428571429" y2="254.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="366.2857142857144" y1="277.3571428571429" y2="277.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="185.2857142857144" x2="185.2857142857144" y1="254.6071428571429" y2="277.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="366.2857142857144" x2="366.2857142857144" y1="254.6071428571429" y2="277.3571428571429"/>
  <line fill="none" id="26" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.285714285714675" x2="370.2857142857142" y1="616.7276354712253" y2="616.7276354712253" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="93.28571428571445" y1="931.8571428571431" y2="931.8571428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="93.28571428571445" y1="971.0204428571431" y2="971.0204428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="3.285714285714448" y1="931.8571428571431" y2="971.0204428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="93.28571428571445" y1="931.8571428571431" y2="971.0204428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="363.2857142857144" y1="931.8571428571431" y2="931.8571428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="363.2857142857144" y1="971.0204428571431" y2="971.0204428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="93.28571428571445" y1="931.8571428571431" y2="971.0204428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.2857142857144" x2="363.2857142857144" y1="931.8571428571431" y2="971.0204428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="93.28571428571445" y1="971.0204128571431" y2="971.0204128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="93.28571428571445" y1="998.9388128571431" y2="998.9388128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="3.285714285714448" y1="971.0204128571431" y2="998.9388128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="93.28571428571445" y1="971.0204128571431" y2="998.9388128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="183.2857142857144" y1="971.0204128571431" y2="971.0204128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="183.2857142857144" y1="998.9388128571431" y2="998.9388128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="93.28571428571445" y1="971.0204128571431" y2="998.9388128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857144" x2="183.2857142857144" y1="971.0204128571431" y2="998.9388128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857146" x2="273.2857142857146" y1="971.0204128571431" y2="971.0204128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857146" x2="273.2857142857146" y1="998.9388128571431" y2="998.9388128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857146" x2="183.2857142857146" y1="971.0204128571431" y2="998.9388128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857146" x2="273.2857142857146" y1="971.0204128571431" y2="998.9388128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857144" x2="363.2857142857144" y1="971.0204128571431" y2="971.0204128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857144" x2="363.2857142857144" y1="998.9388128571431" y2="998.9388128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857144" x2="273.2857142857144" y1="971.0204128571431" y2="998.9388128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.2857142857144" x2="363.2857142857144" y1="971.0204128571431" y2="998.9388128571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="93.28571428571445" y1="998.9387428571431" y2="998.9387428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="93.28571428571445" y1="1026.857142857143" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3.285714285714448" x2="3.285714285714448" y1="998.9387428571431" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="93.28571428571445" y1="998.9387428571431" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="183.2857142857144" y1="998.9387428571431" y2="998.9387428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="183.2857142857144" y1="1026.857142857143" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93.28571428571445" x2="93.28571428571445" y1="998.9387428571431" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857144" x2="183.2857142857144" y1="998.9387428571431" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857146" x2="273.2857142857146" y1="998.9387428571431" y2="998.9387428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857146" x2="273.2857142857146" y1="1026.857142857143" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.2857142857146" x2="183.2857142857146" y1="998.9387428571431" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857146" x2="273.2857142857146" y1="998.9387428571431" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857144" x2="363.2857142857144" y1="998.9387428571431" y2="998.9387428571431"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857144" x2="363.2857142857144" y1="1026.857142857143" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.2857142857144" x2="273.2857142857144" y1="998.9387428571431" y2="1026.857142857143"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363.2857142857144" x2="363.2857142857144" y1="998.9387428571431" y2="1026.857142857143"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48.2857,951.857) scale(1,1) translate(0,0)" writing-mode="lr" x="48.29" xml:space="preserve" y="957.86" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.2857,985.857) scale(1,1) translate(0,0)" writing-mode="lr" x="45.29" xml:space="preserve" y="991.86" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.286,985.857) scale(1,1) translate(0,0)" writing-mode="lr" x="227.29" xml:space="preserve" y="991.86" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44.2857,1013.86) scale(1,1) translate(0,0)" writing-mode="lr" x="44.29" xml:space="preserve" y="1019.86" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226.286,1013.86) scale(1,1) translate(0,0)" writing-mode="lr" x="226.29" xml:space="preserve" y="1019.86" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,68.7857,646.357) scale(1,1) translate(0,-2.78603e-13)" writing-mode="lr" x="68.78571428571445" xml:space="preserve" y="650.8571428571429" zvalue="18">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.34,953.857) scale(1,1) translate(0,0)" writing-mode="lr" x="228.34" xml:space="preserve" y="959.86" zvalue="26">MangHao-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,138.34,1013.86) scale(1,1) translate(0,0)" writing-mode="lr" x="138.34" xml:space="preserve" y="1019.86" zvalue="27">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,42.2857,172.857) scale(1,1) translate(0,0)" writing-mode="lr" x="42.29" xml:space="preserve" y="178.36" zvalue="29">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.286,172.857) scale(1,1) translate(0,0)" writing-mode="lr" x="222.29" xml:space="preserve" y="178.36" zvalue="30">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.4732,244.857) scale(1,1) translate(0,0)" writing-mode="lr" x="49.47" xml:space="preserve" y="249.36" zvalue="31">10kV#1变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.369,199.552) scale(1,1) translate(0,0)" writing-mode="lr" x="234.37" xml:space="preserve" y="204.05" zvalue="32">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,675.571,265.571) scale(1,1) translate(0,0)" writing-mode="lr" x="675.5700000000001" xml:space="preserve" y="270.07" zvalue="35">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,501.286,718.429) scale(1,1) translate(0,0)" writing-mode="lr" x="501.29" xml:space="preserve" y="722.9299999999999" zvalue="36">0.4kV I段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1679.14,707) scale(1,1) translate(0,0)" writing-mode="lr" x="1679.14" xml:space="preserve" y="711.5" zvalue="37">0.4kV II段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1014.29,84.2857) scale(1,1) translate(0,0)" writing-mode="lr" x="1014.29" xml:space="preserve" y="88.79000000000001" zvalue="38">10kV芒号线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" x="662.34375" xml:space="preserve" y="508.4308035714286" zvalue="40">#1主变          </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="662.34375" xml:space="preserve" y="524.4308035714286" zvalue="40">200KVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,761.429,1027.14) scale(1,1) translate(0,0)" writing-mode="lr" x="761.4299999999999" xml:space="preserve" y="1031.64" zvalue="43">#1发电机      160KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,787,855.571) scale(1,1) translate(0,0)" writing-mode="lr" x="787" xml:space="preserve" y="860.0700000000001" zvalue="48">401</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,744.429,782.714) scale(1,1) translate(0,-1.37617e-12)" writing-mode="lr" x="744.4299999999999" xml:space="preserve" y="787.21" zvalue="51">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" x="1359.328125" xml:space="preserve" y="503.0424107142857" zvalue="63">#2主变     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1359.328125" xml:space="preserve" y="519.0424107142857" zvalue="63">160KVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1374.43,388.714) scale(1,1) translate(0,0)" writing-mode="lr" x="1374.43" xml:space="preserve" y="393.21" zvalue="65">0021</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1539.86,1028.57) scale(1,1) translate(0,0)" writing-mode="lr" x="1539.86" xml:space="preserve" y="1033.07" zvalue="69">#2发电机       125KW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1567.57,855.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1567.57" xml:space="preserve" y="860.0700000000001" zvalue="70">402</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1521.98,782.714) scale(1,1) translate(0,-1.37617e-12)" writing-mode="lr" x="1521.98" xml:space="preserve" y="787.21" zvalue="72">2</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1094.29,620) scale(1,1) translate(0,0)" writing-mode="lr" x="1094.29" xml:space="preserve" y="624.5" zvalue="78">412</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.9286,200.357) scale(1,1) translate(0,0)" writing-mode="lr" x="54.93" xml:space="preserve" y="204.86" zvalue="81">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.857,246.071) scale(1,1) translate(0,0)" writing-mode="lr" x="233.86" xml:space="preserve" y="250.57" zvalue="83">10kV#2变油温</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,766.25,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="766.25" xml:space="preserve" y="407.75" zvalue="103">0011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,184.211,358.591) scale(1,1) translate(0,0)" writing-mode="lr" x="184.21" xml:space="preserve" y="363.09" zvalue="108">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,289.211,358.591) scale(1,1) translate(0,0)" writing-mode="lr" x="289.21" xml:space="preserve" y="363.09" zvalue="109">通道</text>
 </g>
 <g id="ButtonClass">
  <g href="10kV芒号电站.svg"><rect fill-opacity="0" height="24" width="72.88" x="32" y="346" zvalue="112"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="33">
   <path class="kv10" d="M 637.14 288 L 1477.14 288" stroke-width="6" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674245279748" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674245279748"/></metadata>
  <path d="M 637.14 288 L 1477.14 288" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="v400" d="M 594.71 733.71 L 970 733.71" stroke-width="6" zvalue="35"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674245345284" ObjectName="0.4kV I段母线"/>
   <cge:TPSR_Ref TObjectID="9288674245345284"/></metadata>
  <path d="M 594.71 733.71 L 970 733.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="v400" d="M 1227.14 730.86 L 1721.43 730.86" stroke-width="6" zvalue="36"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674245410820" ObjectName="0.4kV II段母线"/>
   <cge:TPSR_Ref TObjectID="9288674245410820"/></metadata>
  <path d="M 1227.14 730.86 L 1721.43 730.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="38">
   <g id="380">
    <use class="kv10" height="30" transform="rotate(0,721.714,516.857) scale(2.14286,2.38095) translate(-373.486,-279.063)" width="20" x="700.29" xlink:href="#PowerTransformer2:Y-y不带中性点_0" y="481.14" zvalue="39"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874440048644" ObjectName="10"/>
    </metadata>
   </g>
   <g id="381">
    <use class="v400" height="30" transform="rotate(0,721.714,516.857) scale(2.14286,2.38095) translate(-373.486,-279.063)" width="20" x="700.29" xlink:href="#PowerTransformer2:Y-y不带中性点_1" y="481.14" zvalue="39"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874440114180" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399452262403" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399452262403"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,721.714,516.857) scale(2.14286,2.38095) translate(-373.486,-279.063)" width="20" x="700.29" y="481.14"/></g>
  <g id="63">
   <g id="630">
    <use class="kv10" height="30" transform="rotate(0,1405.71,508.571) scale(2.14286,2.38095) translate(-738.286,-274.257)" width="20" x="1384.29" xlink:href="#PowerTransformer2:Y-y不带中性点_0" y="472.86" zvalue="62"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874440179716" ObjectName="10"/>
    </metadata>
   </g>
   <g id="631">
    <use class="v400" height="30" transform="rotate(0,1405.71,508.571) scale(2.14286,2.38095) translate(-738.286,-274.257)" width="20" x="1384.29" xlink:href="#PowerTransformer2:Y-y不带中性点_1" y="472.86" zvalue="62"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874440245252" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399452327939" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399452327939"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1405.71,508.571) scale(2.14286,2.38095) translate(-738.286,-274.257)" width="20" x="1384.29" y="472.86"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="41">
   <use class="v400" height="30" transform="rotate(0,760,983.714) scale(2.14286,2.14286) translate(-388.19,-507.505)" width="30" x="727.857142857143" xlink:href="#Generator:发电机_0" y="951.5714285714286" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449819901958" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449819901958"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,760,983.714) scale(2.14286,2.14286) translate(-388.19,-507.505)" width="30" x="727.857142857143" y="951.5714285714286"/></g>
  <g id="71">
   <use class="v400" height="30" transform="rotate(0,1539.86,983.714) scale(2.14286,2.14286) translate(-804.114,-507.505)" width="30" x="1507.714285714286" xlink:href="#Generator:发电机_0" y="951.5714285714286" zvalue="68"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449820426245" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449820426245"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1539.86,983.714) scale(2.14286,2.14286) translate(-804.114,-507.505)" width="30" x="1507.714285714286" y="951.5714285714286"/></g>
 </g>
 <g id="BreakerClass">
  <g id="46">
   <use class="v400" height="20" transform="rotate(0,760,856.571) scale(2.14286,1.92857) translate(-399.619,-403.138)" width="10" x="749.2857142857144" xlink:href="#Breaker:开关_0" y="837.2857142857143" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924516380677" ObjectName="#1发电机401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924516380677"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,760,856.571) scale(2.14286,1.92857) translate(-399.619,-403.138)" width="10" x="749.2857142857144" y="837.2857142857143"/></g>
  <g id="70">
   <use class="v400" height="20" transform="rotate(0,1539.86,856.571) scale(2.14286,1.92857) translate(-815.543,-403.138)" width="10" x="1529.142857142857" xlink:href="#Breaker:开关_0" y="837.2857142857143" zvalue="69"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924516446213" ObjectName="#2发电机402断路器"/>
   <cge:TPSR_Ref TObjectID="6473924516446213"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1539.86,856.571) scale(2.14286,1.92857) translate(-815.543,-403.138)" width="10" x="1529.142857142857" y="837.2857142857143"/></g>
  <g id="86">
   <use class="v400" height="20" transform="rotate(270,1094.29,649.429) scale(3,1) translate(-719.524,0)" width="10" x="1079.285714285714" xlink:href="#Breaker:母联开关_0" y="639.4285714285714" zvalue="77"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924516511749" ObjectName="0.4kV母线412母联断路器"/>
   <cge:TPSR_Ref TObjectID="6473924516511749"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1094.29,649.429) scale(3,1) translate(-719.524,0)" width="10" x="1079.285714285714" y="639.4285714285714"/></g>
  <g id="103">
   <use class="kv10" height="30" transform="rotate(0,723.625,405.25) scale(1.71667,1.71667) translate(-296.721,-158.432)" width="15" x="710.7500000000001" xlink:href="#Breaker:自动断路器_0" y="379.5" zvalue="102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924569137156" ObjectName="#1主变0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6473924569137156"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,723.625,405.25) scale(1.71667,1.71667) translate(-296.721,-158.432)" width="15" x="710.7500000000001" y="379.5"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="47">
   <path class="v400" d="M 760 952.11 L 760 874.99" stroke-width="1" zvalue="48"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="46@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 760 952.11 L 760 874.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="v400" d="M 759.93 838.12 L 759.93 799.16" stroke-width="1" zvalue="51"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="49@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 759.93 838.12 L 759.93 799.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="v400" d="M 760.13 768.52 L 760.13 733.71" stroke-width="1" zvalue="52"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@0" LinkObjectIDznd="34@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 760.13 768.52 L 760.13 733.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="53">
   <path class="v400" d="M 897.37 939.69 L 897.37 906.57 L 760 906.57" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@0" LinkObjectIDznd="47" MaxPinNum="2"/>
   </metadata>
  <path d="M 897.37 939.69 L 897.37 906.57 L 760 906.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="v400" d="M 1405.89 535.16 L 1405.89 730.86" stroke-width="1" zvalue="63"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@1" LinkObjectIDznd="35@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1405.89 535.16 L 1405.89 730.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="kv10" d="M 1407.26 370.79 L 1407.26 288" stroke-width="1" zvalue="66"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="33@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1407.26 370.79 L 1407.26 288" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="v400" d="M 1539.86 952.11 L 1539.86 874.99" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="71@0" LinkObjectIDznd="70@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1539.86 952.11 L 1539.86 874.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="v400" d="M 1539.79 838.12 L 1539.79 799.16" stroke-width="1" zvalue="72"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="70@0" LinkObjectIDznd="68@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1539.79 838.12 L 1539.79 799.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="v400" d="M 1539.82 768.52 L 1539.82 730.86" stroke-width="1" zvalue="73"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="35@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1539.82 768.52 L 1539.82 730.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="v400" d="M 1677.23 939.69 L 1677.23 906.57 L 1539.86 906.57" stroke-width="1" zvalue="75"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="65@0" LinkObjectIDznd="69" MaxPinNum="2"/>
   </metadata>
  <path d="M 1677.23 939.69 L 1677.23 906.57 L 1539.86 906.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="v400" d="M 931.43 733.71 L 931.43 649.27 L 1084.47 649.27" stroke-width="1" zvalue="78"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@1" LinkObjectIDznd="86@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 931.43 733.71 L 931.43 649.27 L 1084.47 649.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="v400" d="M 1104.18 649.13 L 1261.43 649.13 L 1261.43 730.86" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="35@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1104.18 649.13 L 1261.43 649.13 L 1261.43 730.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="48">
   <path class="kv10" d="M 1407.02 407.21 L 1407.02 479.4" stroke-width="1" zvalue="88"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@1" LinkObjectIDznd="63@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1407.02 407.21 L 1407.02 479.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="99">
   <path class="kv10" d="M 1007.14 161.31 L 1007.14 288" stroke-width="1" zvalue="98"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="36@0" LinkObjectIDznd="33@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1007.14 161.31 L 1007.14 288" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv10" d="M 1034.41 228.98 L 1007.14 228.98" stroke-width="1" zvalue="99"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101@0" LinkObjectIDznd="99" MaxPinNum="2"/>
   </metadata>
  <path d="M 1034.41 228.98 L 1007.14 228.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv10" d="M 723.63 384.65 L 723.63 288" stroke-width="1" zvalue="103"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="33@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 723.63 384.65 L 723.63 288" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="107">
   <path class="v400" d="M 721.89 543.44 L 721.89 733.71" stroke-width="1" zvalue="105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@1" LinkObjectIDznd="34@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 721.89 543.44 L 721.89 733.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv10" d="M 721.71 487.69 L 721.71 427.71" stroke-width="1" zvalue="106"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="103@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 721.71 487.69 L 721.71 427.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="49">
   <use class="v400" height="30" transform="rotate(0,760,783.714) scale(1.42857,1.04762) translate(-224.786,-34.9091)" width="15" x="749.2857142857142" xlink:href="#Disconnector:刀闸_0" y="768" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449820033029" ObjectName="#1发电机4011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449820033029"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,760,783.714) scale(1.42857,1.04762) translate(-224.786,-34.9091)" width="15" x="749.2857142857142" y="768"/></g>
  <g id="61">
   <use class="kv10" height="30" transform="rotate(0,1407.14,389.714) scale(1.42857,1.42857) translate(-418.929,-110.486)" width="15" x="1396.428571428571" xlink:href="#Disconnector:令克_0" y="368.2857142857142" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449820229637" ObjectName="#2主变0021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449820229637"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1407.14,389.714) scale(1.42857,1.42857) translate(-418.929,-110.486)" width="15" x="1396.428571428571" y="368.2857142857142"/></g>
  <g id="68">
   <use class="v400" height="30" transform="rotate(0,1539.7,783.714) scale(1.42857,1.04762) translate(-458.695,-34.9091)" width="15" x="1528.984050822706" xlink:href="#Disconnector:刀闸_0" y="768" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449820360709" ObjectName="#2发电机4022隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449820360709"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1539.7,783.714) scale(1.42857,1.04762) translate(-458.695,-34.9091)" width="15" x="1528.984050822706" y="768"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="52">
   <use class="v400" height="26" transform="rotate(0,897.5,973.476) scale(2.61905,2.61905) translate(-542.675,-580.738)" width="15" x="877.8571428571428" xlink:href="#Accessory:20210316PT_0" y="939.4285714285714" zvalue="53"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449820098565" ObjectName="#1发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,897.5,973.476) scale(2.61905,2.61905) translate(-542.675,-580.738)" width="15" x="877.8571428571428" y="939.4285714285714"/></g>
  <g id="65">
   <use class="v400" height="26" transform="rotate(0,1677.36,973.476) scale(2.61905,2.61905) translate(-1024.77,-580.738)" width="15" x="1657.714285714285" xlink:href="#Accessory:20210316PT_0" y="939.4285714285714" zvalue="74"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449820295173" ObjectName="#2发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1677.36,973.476) scale(2.61905,2.61905) translate(-1024.77,-580.738)" width="15" x="1657.714285714285" y="939.4285714285714"/></g>
  <g id="101">
   <use class="kv10" height="29" transform="rotate(90,1049.63,228.976) scale(-1.06825,1.06825) translate(-2031.18,-13.6403)" width="30" x="1033.60873015873" xlink:href="#Accessory:PT12321_0" y="213.486507936508" zvalue="100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450106556421" ObjectName="线路PT"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(90,1049.63,228.976) scale(-1.06825,1.06825) translate(-2031.18,-13.6403)" width="30" x="1033.60873015873" y="213.486507936508"/></g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,316.485,359.107) scale(0.708333,0.665547) translate(125.942,175.443)" width="30" x="305.86" xlink:href="#State:红绿圆(方形)_0" y="349.12" zvalue="110"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,316.485,359.107) scale(0.708333,0.665547) translate(125.942,175.443)" width="30" x="305.86" y="349.12"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,220.86,359.107) scale(0.708333,0.665547) translate(86.5674,175.443)" width="30" x="210.24" xlink:href="#State:红绿圆(方形)_0" y="349.12" zvalue="111"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,220.86,359.107) scale(0.708333,0.665547) translate(86.5674,175.443)" width="30" x="210.24" y="349.12"/></g>
 </g>
</svg>