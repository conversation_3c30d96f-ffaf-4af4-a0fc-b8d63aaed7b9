<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549684076545" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:空挂线路_0" viewBox="0,0,11,13">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="-0.0833333333333357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="12.91666666666667" y2="0.2500000000000009"/>
  </symbol>
  <symbol id="Breaker:手车开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:手车开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="Breaker:手车开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.45181617405996" x2="1.714850492606708" y1="5.276370517142858" y2="14.05696281619048"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.583333333333333" x2="8.583333333333334" y1="5.166666666666667" y2="14.16666666666667"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:厂用变2020_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <path d="M 13 13.5 L 17 13.5 L 15 10.5 L 13 13.5 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="20.66666666666666" y2="22.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="17.91666666666666" y2="20.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="25.41666666666667" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="20.66666666666666" y2="22.66666666666666"/>
   <ellipse cx="14.95" cy="20.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.95" cy="12.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0833 3.5 L 14.0833 4.5 L 16.0833 4.5 L 15.0833 3.5 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="1" y2="7.333333333333331"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="1" y1="23" y2="13"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_1" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6" x2="6" y1="23" y2="11"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="6" y1="0" y2="11.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5" x2="7" y1="11.41666666666667" y2="11.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
  </symbol>
  <symbol id="Disconnector:单手车刀闸1212_2" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.084067223915516" xlink:href="#terminal" y="25.96744525732867"/>
   <use terminal-index="1" type="0" x="6.00238862656395" xlink:href="#terminal" y="0.07500000000000639"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="2" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2" x2="10" y1="12" y2="22"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="26" y2="23"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="4.5" y2="11.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.003251536859219" x2="11.35" y1="0.07422050210116105" y2="7.359987407552576"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7430590191188813" y1="0.07340761788636385" y2="7.359987407552579"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6" x2="0.7166666666666694" y1="4.47715704632715" y2="11.77109851866368"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.014631915866484" x2="11.3" y1="4.484473004260391" y2="11.77109851866368"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Accessory:PT带熔断器_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="14.95" xlink:href="#terminal" y="0.3499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="22" y2="28"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="18" y1="17" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="22" y2="24"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="8" y1="28" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.41666666666667" y1="17" y2="19.41666666666667"/>
   <rect fill-opacity="0" height="8.92" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,6.54) scale(1,1) translate(0,0)" width="4" x="13" y="2.08"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="14" y2="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.999999999999998" y2="12.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.083333333333332" y2="0.25"/>
   <ellipse cx="15.03" cy="17.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10.5" cy="24.83" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.5" cy="24.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="19.58333333333333" y1="22" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333333" x2="17" y1="25" y2="27.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58333333333334" x2="22.58333333333334" y1="25" y2="27"/>
  </symbol>
  <symbol id="Accessory:四卷带壁雷器母线PT_0" viewBox="0,0,40,35">
   <use terminal-index="0" type="0" x="22.10905664884498" xlink:href="#terminal" y="34.51612485684674"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="28.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <rect fill-opacity="0" height="15.5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,30,14.36) scale(1,-1) translate(0,-926.43)" width="7" x="26.5" y="6.61"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="16.27740325661302" y2="15.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="7.166666666666671" y1="16.27740325661302" y2="12.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.166666666666671" x2="5.166666666666671" y1="12.27740325661302" y2="13.27740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.96666666666667" x2="30.96666666666667" y1="13.77740325661302" y2="18.77740325661302"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22" x2="22" y1="28.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="14" y1="21.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30" x2="30" y1="28.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14" x2="30" y1="28.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.92943360505483" x2="29.92943360505483" y1="6.610736589946352" y2="3.462520268614096"/>
   <ellipse cx="13.81" cy="17.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.95921744067709" x2="27.68240159599324" y1="3.3946117330996" y2="3.3946117330996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.12588410734375" x2="28.51573492932657" y1="2.144611733099605" y2="2.144611733099605"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="30.54255077401042" x2="29.09906826265991" y1="0.8946117330996053" y2="0.8946117330996053"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599325" x2="16.18240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="13.78240159599324" y1="15.23154965466559" y2="17.7066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.78240159599324" x2="11.38240159599324" y1="17.70662962336982" y2="18.94416960772192"/>
   <ellipse cx="20.23" cy="13.99" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.48" cy="10.41" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="17.79906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="20.19906826265991" y1="11.73154965466559" y2="14.2066296233698"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.19906826265991" x2="22.59906826265991" y1="14.20662962336982" y2="15.44416960772192"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="10.62329629003648" y2="11.86083627438859"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="8.148216321332258" y2="10.62329629003646"/>
   <ellipse cx="6.64" cy="14.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:220kV线路PT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="12.90667828048158" xlink:href="#terminal" y="37.28457520218116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27" x2="13" y1="26.75" y2="26.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.08333333333334" x2="29.08333333333334" y1="14.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27" x2="27" y1="22.75" y2="26.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.93916361389983" x2="12.93916361389983" y1="33.96630546659506" y2="37.26790853551449"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.93916361389983" x2="12.93916361389983" y1="30.79012345679012" y2="22.71704491718448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="12.91963465760507" x2="12.91963465760507" y1="19.74560215515698" y2="13.06430394728183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.01963465760507" x2="13.01963465760507" y1="22.75831495554599" y2="22.75831495554599"/>
   <ellipse cx="23.37" cy="20.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.556517488649495" x2="16.39655620359946" y1="22.67498162221265" y2="22.67498162221265"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.01963465760507" x2="13.01963465760507" y1="33.90122531314907" y2="33.90122531314907"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982819" x2="16.47988953693279" y1="19.66306207843401" y2="19.66306207843401"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982824" x2="16.47988953693279" y1="33.90122531314905" y2="33.90122531314905"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="9.639850821982824" x2="16.47988953693279" y1="30.80597243603708" y2="30.80597243603708"/>
   <ellipse cx="26.96" cy="14.43" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="10.72318415531615" x2="15" y1="12.99639541176734" y2="12.99639541176734"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.47318415531615" x2="14.08333333333333" y1="11.74639541176734" y2="11.74639541176734"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="11.97318415531615" x2="13.41666666666667" y1="10.24639541176734" y2="10.24639541176734"/>
   <ellipse cx="30.62" cy="20.6" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.25" x2="25.25" y1="20.5" y2="20.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.08333333333334" x2="33.08333333333334" y1="20.41666666666667" y2="20.41666666666667"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV支那河二级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="36.25" xlink:href="logo.png" y="36.75"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,185.25,66.75) scale(1,1) translate(0,0)" writing-mode="lr" x="185.25" xml:space="preserve" y="70.25" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,181.75,66.4403) scale(1,1) translate(0,0)" writing-mode="lr" x="181.75" xml:space="preserve" y="75.44" zvalue="3">110kV支那河二级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="243" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,81.625,224) scale(1,1) translate(0,0)" width="72.88" x="45.19" y="212" zvalue="311"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,81.625,224) scale(1,1) translate(0,0)" writing-mode="lr" x="81.63" xml:space="preserve" y="228.5" zvalue="311">信号一览</text>
  <line fill="none" id="32" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="378.25" x2="378.25" y1="4.75" y2="1034.75" zvalue="4"/>
  <line fill="none" id="30" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.250000000000455" x2="371.25" y1="140.6204926140824" y2="140.6204926140824" zvalue="6"/>
  <line fill="none" id="28" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.250000000000455" x2="371.25" y1="610.6204926140824" y2="610.6204926140824" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="94.25" y1="925.75" y2="925.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="94.25" y1="964.9132999999999" y2="964.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="4.25" y1="925.75" y2="964.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="94.25" y1="925.75" y2="964.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="364.25" y1="925.75" y2="925.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="364.25" y1="964.9132999999999" y2="964.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="94.25" y1="925.75" y2="964.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="364.25" x2="364.25" y1="925.75" y2="964.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="94.25" y1="964.91327" y2="964.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="94.25" y1="992.83167" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="4.25" y1="964.91327" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="94.25" y1="964.91327" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="184.25" y1="964.91327" y2="964.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="184.25" y1="992.83167" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="94.25" y1="964.91327" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.25" x2="184.25" y1="964.91327" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.2500000000001" x2="274.2500000000001" y1="964.91327" y2="964.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.2500000000001" x2="274.2500000000001" y1="992.83167" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.2500000000001" x2="184.2500000000001" y1="964.91327" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.2500000000001" x2="274.2500000000001" y1="964.91327" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.25" x2="364.25" y1="964.91327" y2="964.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.25" x2="364.25" y1="992.83167" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.25" x2="274.25" y1="964.91327" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="364.25" x2="364.25" y1="964.91327" y2="992.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="94.25" y1="992.8316" y2="992.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="94.25" y1="1020.75" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="4.25" x2="4.25" y1="992.8316" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="94.25" y1="992.8316" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="184.25" y1="992.8316" y2="992.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="184.25" y1="1020.75" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="94.25" x2="94.25" y1="992.8316" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.25" x2="184.25" y1="992.8316" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.2500000000001" x2="274.2500000000001" y1="992.8316" y2="992.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.2500000000001" x2="274.2500000000001" y1="1020.75" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="184.2500000000001" x2="184.2500000000001" y1="992.8316" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.2500000000001" x2="274.2500000000001" y1="992.8316" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.25" x2="364.25" y1="992.8316" y2="992.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.25" x2="364.25" y1="1020.75" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="274.25" x2="274.25" y1="992.8316" y2="1020.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="364.25" x2="364.25" y1="992.8316" y2="1020.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49.25,945.75) scale(1,1) translate(0,0)" writing-mode="lr" x="49.25" xml:space="preserve" y="951.75" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,46.25,979.75) scale(1,1) translate(0,0)" writing-mode="lr" x="46.25" xml:space="preserve" y="985.75" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,228.25,979.75) scale(1,1) translate(0,0)" writing-mode="lr" x="228.25" xml:space="preserve" y="985.75" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45.25,1007.75) scale(1,1) translate(0,0)" writing-mode="lr" x="45.25" xml:space="preserve" y="1013.75" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.25,1007.75) scale(1,1) translate(0,0)" writing-mode="lr" x="227.25" xml:space="preserve" y="1013.75" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.75,640.25) scale(1,1) translate(0,0)" writing-mode="lr" x="69.75" xml:space="preserve" y="644.75" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1601.75,330) scale(1,1) translate(0,0)" writing-mode="lr" x="1601.75" xml:space="preserve" y="334.5" zvalue="39">110kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1054.53,373.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1054.53" xml:space="preserve" y="377.72" zvalue="41">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1063.86,429.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1063.86" xml:space="preserve" y="433.72" zvalue="43">101</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1086.19,406.222) scale(1,1) translate(0,-3.53248e-13)" writing-mode="lr" x="1086.19" xml:space="preserve" y="410.72" zvalue="46">17</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" x="1101.5625" xml:space="preserve" y="527.2513440860215" zvalue="49">#1主变           </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1101.5625" xml:space="preserve" y="543.2513440860215" zvalue="49">50MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,919.867,541.75) scale(1,1) translate(0,0)" writing-mode="lr" x="919.87" xml:space="preserve" y="546.25" zvalue="54">1010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1067.9,623.611) scale(1,1) translate(0,0)" writing-mode="lr" x="1067.9" xml:space="preserve" y="628.11" zvalue="56">001</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1853.62,668.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1853.63" xml:space="preserve" y="673.25" zvalue="60">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,744.306,399.167) scale(1,1) translate(0,0)" writing-mode="lr" x="744.3099999999999" xml:space="preserve" y="403.67" zvalue="62">1901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,754.333,467.694) scale(1,1) translate(0,2.03923e-13)" writing-mode="lr" x="754.33" xml:space="preserve" y="472.19" zvalue="64">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,761.833,373.861) scale(1,1) translate(0,0)" writing-mode="lr" x="761.83" xml:space="preserve" y="378.36" zvalue="66">10</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,731,561.625) scale(1,1) translate(0,0)" writing-mode="lr" x="731" xml:space="preserve" y="566.13" zvalue="72">110kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,612.31,729.365) scale(1,1) translate(0,0)" writing-mode="lr" x="612.3099999999999" xml:space="preserve" y="733.87" zvalue="77">031</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,588.836,925.455) scale(1,1) translate(0,3.01324e-13)" writing-mode="lr" x="588.8360901404426" xml:space="preserve" y="929.9547398511555" zvalue="81">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,529.024,826.031) scale(1,1) translate(-2.27229e-13,-5.43643e-13)" writing-mode="lr" x="529.02" xml:space="preserve" y="830.53" zvalue="84">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,655.713,834.321) scale(1,1) translate(-2.83215e-13,-5.49165e-13)" writing-mode="lr" x="655.71" xml:space="preserve" y="838.8200000000001" zvalue="86">0912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="106" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,590.075,950.241) scale(1,1) translate(0,3.0958e-13)" writing-mode="lr" x="590.0754169472128" xml:space="preserve" y="954.7412759865815" zvalue="88">18MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,502.5,936.133) scale(1,1) translate(0,0)" writing-mode="lr" x="502.5" xml:space="preserve" y="940.63" zvalue="92">励磁变PT1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,434.796,929.558) scale(1,1) translate(0,0)" writing-mode="lr" x="434.8" xml:space="preserve" y="934.0599999999999" zvalue="94">励磁PT2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,680,929.625) scale(1,1) translate(0,0)" writing-mode="lr" x="680" xml:space="preserve" y="934.13" zvalue="97">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1089.31,725.365) scale(1,1) translate(0,0)" writing-mode="lr" x="1089.31" xml:space="preserve" y="729.87" zvalue="101">032</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1065.84,921.455) scale(1,1) translate(0,2.99992e-13)" writing-mode="lr" x="1065.836090140443" xml:space="preserve" y="925.9547398511555" zvalue="104">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1006.02,822.031) scale(1,1) translate(-2.1953e-13,-5.40978e-13)" writing-mode="lr" x="1006.02" xml:space="preserve" y="826.53" zvalue="107">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1132.71,830.321) scale(1,1) translate(-4.95045e-13,-5.46501e-13)" writing-mode="lr" x="1132.71" xml:space="preserve" y="834.8200000000001" zvalue="109">0922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1063.58,934.991) scale(1,1) translate(-2.26805e-13,3.04501e-13)" writing-mode="lr" x="1063.575416947213" xml:space="preserve" y="939.4912759865815" zvalue="111">18MW</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,979.5,932.133) scale(1,1) translate(0,0)" writing-mode="lr" x="979.5" xml:space="preserve" y="936.63" zvalue="115">励磁变PT1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,903.296,929.308) scale(1,1) translate(0,0)" writing-mode="lr" x="903.3" xml:space="preserve" y="933.8099999999999" zvalue="117">励磁PT2</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1157,941.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1157" xml:space="preserve" y="946.38" zvalue="121">机组PT</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1318.12,893.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1318.13" xml:space="preserve" y="897.75" zvalue="124">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1383.94,812.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1383.94" xml:space="preserve" y="817.1900000000001" zvalue="127">03367</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1431.53,577.743) scale(1,1) translate(0,0)" writing-mode="lr" x="1431.53" xml:space="preserve" y="582.24" zvalue="129">0901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1290,575.243) scale(1,1) translate(0,0)" writing-mode="lr" x="1290" xml:space="preserve" y="579.74" zvalue="131">0801</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1287.94,741.993) scale(1,1) translate(0,0)" writing-mode="lr" x="1287.94" xml:space="preserve" y="746.49" zvalue="139">033</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1647.88,616.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1647.88" xml:space="preserve" y="621" zvalue="143">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1713.69,519.944) scale(1,1) translate(0,0)" writing-mode="lr" x="1713.69" xml:space="preserve" y="524.4400000000001" zvalue="145">03467</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1653.84,352.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1653.84" xml:space="preserve" y="357.25" zvalue="159">10kV外接电源</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1678.85,440.446) scale(1,1) translate(0,0)" writing-mode="lr" x="1678.85" xml:space="preserve" y="444.95" zvalue="169">034</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1652.59,366.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1652.59" xml:space="preserve" y="371" zvalue="173">（T接盏西供电所庐山10kV线潞）</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,610.583,249.167) scale(1,1) translate(0,0)" writing-mode="lr" x="610.58" xml:space="preserve" y="253.67" zvalue="181">132</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,601.806,296.722) scale(1,1) translate(0,0)" writing-mode="lr" x="601.8099999999999" xml:space="preserve" y="301.22" zvalue="182">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,603.472,188.722) scale(1,1) translate(0,0)" writing-mode="lr" x="603.47" xml:space="preserve" y="193.22" zvalue="185">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="171" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,585.182,78.5) scale(1,1) translate(0,0)" writing-mode="lr" x="585.1799999999999" xml:space="preserve" y="83" zvalue="189">110kV支那河一二级线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="170" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,638.306,284.167) scale(1,1) translate(0,0)" writing-mode="lr" x="638.3099999999999" xml:space="preserve" y="288.67" zvalue="192">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,638.306,235.389) scale(1,1) translate(0,0)" writing-mode="lr" x="638.3099999999999" xml:space="preserve" y="239.89" zvalue="194">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,638.306,180.944) scale(1,1) translate(0,0)" writing-mode="lr" x="638.3099999999999" xml:space="preserve" y="185.44" zvalue="196">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="201" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1331.58,249.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1331.58" xml:space="preserve" y="253.67" zvalue="214">131</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1322.81,296.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1322.81" xml:space="preserve" y="301.22" zvalue="215">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1324.47,188.722) scale(1,1) translate(0,0)" writing-mode="lr" x="1324.47" xml:space="preserve" y="193.22" zvalue="218">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="198" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1306.18,78.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1306.18" xml:space="preserve" y="83" zvalue="222">110kV支盏T线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="197" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1359.31,284.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1359.31" xml:space="preserve" y="288.67" zvalue="225">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="196" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1359.31,235.389) scale(1,1) translate(0,0)" writing-mode="lr" x="1359.31" xml:space="preserve" y="239.89" zvalue="227">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="195" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1359.31,180.944) scale(1,1) translate(0,0)" writing-mode="lr" x="1359.31" xml:space="preserve" y="185.44" zvalue="229">67</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="285.75" y2="285.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="311.75" y2="311.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="285.75" y2="311.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="285.75" y2="311.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="285.75" y2="285.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="311.75" y2="311.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="285.75" y2="311.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="285.75" y2="311.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="311.75" y2="311.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="336" y2="336"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="311.75" y2="336"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="311.75" y2="336"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="311.75" y2="311.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="336" y2="336"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="311.75" y2="336"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="311.75" y2="336"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="336" y2="336"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="358.75" y2="358.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="336" y2="358.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="336" y2="358.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="336" y2="336"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="358.75" y2="358.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="336" y2="358.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="336" y2="358.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="358.75" y2="358.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="381.5" y2="381.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="358.75" y2="381.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="358.75" y2="381.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="358.75" y2="358.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="381.5" y2="381.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="358.75" y2="381.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="358.75" y2="381.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="381.5" y2="381.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="404.25" y2="404.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="381.5" y2="404.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="381.5" y2="404.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="381.5" y2="381.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="404.25" y2="404.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="381.5" y2="404.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="381.5" y2="404.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="404.25" y2="404.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="427" y2="427"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="404.25" y2="427"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="404.25" y2="427"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="404.25" y2="404.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="427" y2="427"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="404.25" y2="427"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="404.25" y2="427"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="427" y2="427"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="189" y1="449.75" y2="449.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="8" x2="8" y1="427" y2="449.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="427" y2="449.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="427" y2="427"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="370" y1="449.75" y2="449.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="189" x2="189" y1="427" y2="449.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="370" x2="370" y1="427" y2="449.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="254" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,302.399,224.591) scale(1,1) translate(0,0)" writing-mode="lr" x="302.4" xml:space="preserve" y="229.09" zvalue="299">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,54.5,298.75) scale(1,1) translate(0,0)" writing-mode="lr" x="12" xml:space="preserve" y="303.25" zvalue="300">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235,298.75) scale(1,1) translate(0,0)" writing-mode="lr" x="192.5" xml:space="preserve" y="303.25" zvalue="301">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.6875,372) scale(1,1) translate(0,0)" writing-mode="lr" x="57.69" xml:space="preserve" y="376.5" zvalue="302">110kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="250" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.875,370.75) scale(1,1) translate(0,0)" writing-mode="lr" x="235.88" xml:space="preserve" y="375.25" zvalue="303">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="242" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,53.5,324.75) scale(1,1) translate(0,0)" writing-mode="lr" x="11" xml:space="preserve" y="329.25" zvalue="312">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="241" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,234,324.75) scale(1,1) translate(0,0)" writing-mode="lr" x="191.5" xml:space="preserve" y="329.25" zvalue="313">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.6875,418) scale(1,1) translate(0,0)" writing-mode="lr" x="54.69" xml:space="preserve" y="422.5" zvalue="316">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="236" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,222.688,417) scale(1,1) translate(0,0)" writing-mode="lr" x="222.69" xml:space="preserve" y="421.5" zvalue="318">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="232" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,53.5,347.75) scale(1,1) translate(0,0)" writing-mode="lr" x="11" xml:space="preserve" y="352.25" zvalue="322">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="229" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,233.5,346.75) scale(1,1) translate(0,0)" writing-mode="lr" x="191" xml:space="preserve" y="351.25" zvalue="324">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,191.5,945) scale(1,1) translate(0,0)" writing-mode="lr" x="191.5" xml:space="preserve" y="951" zvalue="330">ZhiNaHe2-01-2012</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.5,223.5) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="228" zvalue="332">事故总</text>
  <rect fill="none" fill-opacity="0" height="311" id="29" stroke="rgb(255,255,255)" stroke-dasharray="6 2" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,597.5,165.5) scale(1,1) translate(0,0)" width="239" x="478" y="10" zvalue="346"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" glyph-orientation-vertical="0" id="35" letter-spacing="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,689,180) scale(1,1) translate(0,0)" writing-mode="tb" x="689" xml:space="preserve" y="180" zvalue="347">未纳入调度管辖</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="45.19" y="212" zvalue="311"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="146">
   <path class="kv110" d="M 509.75 331 L 1559.75 331" stroke-width="6" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674420588547" ObjectName="110kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674420588547"/></metadata>
  <path d="M 509.75 331 L 1559.75 331" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv10" d="M 471 669.75 L 1816 669.75" stroke-width="6" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674420523011" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674420523011"/></metadata>
  <path d="M 471 669.75 L 1816 669.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="145">
   <use class="kv110" height="30" transform="rotate(0,1043.08,374.222) scale(1.11111,0.814815) translate(-103.475,82.2727)" width="15" x="1034.750010172526" xlink:href="#Disconnector:刀闸_0" y="362" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454765117442" ObjectName="#1主变110kV侧1011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454765117442"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1043.08,374.222) scale(1.11111,0.814815) translate(-103.475,82.2727)" width="15" x="1034.750010172526" y="362"/></g>
  <g id="125">
   <use class="kv110" height="30" transform="rotate(0,718.972,400.167) scale(-1.11111,-0.814815) translate(-1365.21,-894.058)" width="15" x="710.6388888888888" xlink:href="#Disconnector:刀闸_0" y="387.9444581137762" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454764789762" ObjectName="110kV母线电压互感器1901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454764789762"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,718.972,400.167) scale(-1.11111,-0.814815) translate(-1365.21,-894.058)" width="15" x="710.6388888888888" y="387.9444581137762"/></g>
  <g id="132">
   <use class="kv10" height="26" transform="rotate(0,501.759,827.27) scale(1.23933,1.23933) translate(-95.4588,-156.643)" width="12" x="494.3225404732252" xlink:href="#Disconnector:单手车刀闸1212_0" y="811.1588615061935" zvalue="82"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454764331010" ObjectName="#1发电机0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454764331010"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,501.759,827.27) scale(1.23933,1.23933) translate(-95.4588,-156.643)" width="12" x="494.3225404732252" y="811.1588615061935"/></g>
  <g id="129">
   <use class="kv10" height="26" transform="rotate(0,681.681,834.655) scale(1.23933,1.23933) translate(-130.204,-158.069)" width="12" x="674.2454530434896" xlink:href="#Disconnector:单手车刀闸1212_0" y="818.544054079156" zvalue="85"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454764265474" ObjectName="#1发电机0912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454764265474"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,681.681,834.655) scale(1.23933,1.23933) translate(-130.204,-158.069)" width="12" x="674.2454530434896" y="818.544054079156"/></g>
  <g id="116">
   <use class="kv10" height="26" transform="rotate(0,978.759,823.27) scale(1.23933,1.23933) translate(-187.572,-155.871)" width="12" x="971.3225404732252" xlink:href="#Disconnector:单手车刀闸1212_0" y="807.1588615061935" zvalue="105"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454763872258" ObjectName="#2发电机0921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454763872258"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,978.759,823.27) scale(1.23933,1.23933) translate(-187.572,-155.871)" width="12" x="971.3225404732252" y="807.1588615061935"/></g>
  <g id="113">
   <use class="kv10" height="26" transform="rotate(0,1158.68,830.655) scale(1.23933,1.23933) translate(-222.317,-157.297)" width="12" x="1151.24545304349" xlink:href="#Disconnector:单手车刀闸1212_0" y="814.544054079156" zvalue="108"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454763806722" ObjectName="#2发电机0922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454763806722"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1158.68,830.655) scale(1.23933,1.23933) translate(-222.317,-157.297)" width="12" x="1151.24545304349" y="814.544054079156"/></g>
  <g id="130">
   <use class="kv10" height="26" transform="rotate(0,1393.53,576.243) scale(2.45312,2.09669) translate(-816.749,-287.151)" width="12" x="1378.8125" xlink:href="#Disconnector:单手车刀闸1212_0" y="548.9861111111111" zvalue="128"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454763282434" ObjectName="10kV母线0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454763282434"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1393.53,576.243) scale(2.45312,2.09669) translate(-816.749,-287.151)" width="12" x="1378.8125" y="548.9861111111111"/></g>
  <g id="133">
   <use class="kv10" height="26" transform="rotate(0,1324.84,576.243) scale(2.45312,2.09669) translate(-776.061,-287.151)" width="12" x="1310.125" xlink:href="#Disconnector:单手车刀闸1212_0" y="548.9861111111111" zvalue="130"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454763216898" ObjectName="10kV母线0801隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454763216898"/></metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1324.84,576.243) scale(2.45312,2.09669) translate(-776.061,-287.151)" width="12" x="1310.125" y="548.9861111111111"/></g>
  <g id="272">
   <use class="kv110" height="30" transform="rotate(0,589.25,297.722) scale(-1.11111,-0.814815) translate(-1118.74,-665.886)" width="15" x="580.9166666666666" xlink:href="#Disconnector:刀闸_0" y="285.5000133514404" zvalue="180"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454765707266" ObjectName="110kV支那河一二级线1321隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454765707266"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,589.25,297.722) scale(-1.11111,-0.814815) translate(-1118.74,-665.886)" width="15" x="580.9166666666666" y="285.5000133514404"/></g>
  <g id="271">
   <use class="kv110" height="30" transform="rotate(0,589.25,189.722) scale(-1.11111,-0.814815) translate(-1118.74,-425.341)" width="15" x="580.9166666931577" xlink:href="#Disconnector:刀闸_0" y="177.4999997880724" zvalue="183"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454765641730" ObjectName="110kV支那河一二级线1326隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454765641730"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,589.25,189.722) scale(-1.11111,-0.814815) translate(-1118.74,-425.341)" width="15" x="580.9166666931577" y="177.4999997880724"/></g>
  <g id="219">
   <use class="kv110" height="30" transform="rotate(0,1310.25,297.722) scale(-1.11111,-0.814815) translate(-2488.64,-665.886)" width="15" x="1301.916666666667" xlink:href="#Disconnector:刀闸_0" y="285.5000133514404" zvalue="213"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454766559234" ObjectName="110kV支盏T线1311隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454766559234"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1310.25,297.722) scale(-1.11111,-0.814815) translate(-2488.64,-665.886)" width="15" x="1301.916666666667" y="285.5000133514404"/></g>
  <g id="218">
   <use class="kv110" height="30" transform="rotate(0,1310.25,189.722) scale(-1.11111,-0.814815) translate(-2488.64,-425.341)" width="15" x="1301.916666693158" xlink:href="#Disconnector:刀闸_0" y="177.4999997880724" zvalue="216"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454766493698" ObjectName="110kV支盏T线1316隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454766493698"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1310.25,189.722) scale(-1.11111,-0.814815) translate(-2488.64,-425.341)" width="15" x="1301.916666693158" y="177.4999997880724"/></g>
 </g>
 <g id="BreakerClass">
  <g id="144">
   <use class="kv110" height="20" transform="rotate(0,1043.08,430.222) scale(1.22222,1.11111) translate(-188.54,-41.9111)" width="10" x="1036.972232407994" xlink:href="#Breaker:开关_0" y="419.1111111111111" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925213290499" ObjectName="#1主变110kV侧101断路器"/>
   <cge:TPSR_Ref TObjectID="6473925213290499"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1043.08,430.222) scale(1.22222,1.11111) translate(-188.54,-41.9111)" width="10" x="1036.972232407994" y="419.1111111111111"/></g>
  <g id="191">
   <use class="kv10" height="20" transform="rotate(0,1042.78,624.611) scale(2,2) translate(-516.39,-302.306)" width="10" x="1032.779569585283" xlink:href="#Breaker:手车开关_0" y="604.6111128065321" zvalue="55"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925213224963" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925213224963"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1042.78,624.611) scale(2,2) translate(-516.39,-302.306)" width="10" x="1032.779569585283" y="604.6111128065321"/></g>
  <g id="341">
   <use class="kv10" height="20" transform="rotate(0,585.297,730.446) scale(2.16108,2.16108) translate(-308.657,-380.835)" width="10" x="574.4912202553933" xlink:href="#Breaker:手车开关_0" y="708.834974364315" zvalue="76"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925213159427" ObjectName="#1发电机031断路器"/>
   <cge:TPSR_Ref TObjectID="6473925213159427"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,585.297,730.446) scale(2.16108,2.16108) translate(-308.657,-380.835)" width="10" x="574.4912202553933" y="708.834974364315"/></g>
  <g id="120">
   <use class="kv10" height="20" transform="rotate(0,1062.3,726.446) scale(2.16108,2.16108) translate(-564.934,-378.686)" width="10" x="1051.491220255393" xlink:href="#Breaker:手车开关_0" y="704.834974364315" zvalue="100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925213093891" ObjectName="#2发电机032断路器"/>
   <cge:TPSR_Ref TObjectID="6473925213093891"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1062.3,726.446) scale(2.16108,2.16108) translate(-564.934,-378.686)" width="10" x="1051.491220255393" y="704.834974364315"/></g>
  <g id="141">
   <use class="kv10" height="20" transform="rotate(0,1322.16,742.993) scale(2.94375,2.72569) translate(-863.297,-453.148)" width="10" x="1307.437105299286" xlink:href="#Breaker:手车开关_0" y="715.7361111111111" zvalue="138"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925233541123" ObjectName="#1站用变033断路器"/>
   <cge:TPSR_Ref TObjectID="6473925233541123"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1322.16,742.993) scale(2.94375,2.72569) translate(-863.297,-453.148)" width="10" x="1307.437105299286" y="715.7361111111111"/></g>
  <g id="154">
   <use class="kv10" height="20" transform="rotate(0,1652.3,441.446) scale(2.16108,2.16108) translate(-881.923,-225.564)" width="10" x="1641.491220255393" xlink:href="#Breaker:手车开关_0" y="419.834974364315" zvalue="168"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925213356035" ObjectName="#2站用变034断路器"/>
   <cge:TPSR_Ref TObjectID="6473925213356035"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1652.3,441.446) scale(2.16108,2.16108) translate(-881.923,-225.564)" width="10" x="1641.491220255393" y="419.834974364315"/></g>
  <g id="273">
   <use class="kv110" height="20" transform="rotate(0,589.25,250.167) scale(1.22222,1.11111) translate(-106.025,-23.9056)" width="10" x="583.1388888888889" xlink:href="#Breaker:开关_0" y="239.0555553436279" zvalue="179"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925213421571" ObjectName="110kV支那河一二级线132断路器"/>
   <cge:TPSR_Ref TObjectID="6473925213421571"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,589.25,250.167) scale(1.22222,1.11111) translate(-106.025,-23.9056)" width="10" x="583.1388888888889" y="239.0555553436279"/></g>
  <g id="220">
   <use class="kv110" height="20" transform="rotate(0,1310.25,250.167) scale(1.22222,1.11111) translate(-237.116,-23.9056)" width="10" x="1304.138888888889" xlink:href="#Breaker:开关_0" y="239.0555553436279" zvalue="212"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925213487107" ObjectName="110kV支盏T线131断路器"/>
   <cge:TPSR_Ref TObjectID="6473925213487107"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1310.25,250.167) scale(1.22222,1.11111) translate(-237.116,-23.9056)" width="10" x="1304.138888888889" y="239.0555553436279"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="143">
   <path class="kv110" d="M 1043.15 386.24 L 1043.15 419.59" stroke-width="1" zvalue="44"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@1" LinkObjectIDznd="144@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1043.15 386.24 L 1043.15 419.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="140">
   <path class="kv110" d="M 1043.18 362.4 L 1043.18 331" stroke-width="1" zvalue="47"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="146@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1043.18 362.4 L 1043.18 331" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="136">
   <path class="kv110" d="M 1043.16 440.83 L 1043.16 482.84" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="144@1" LinkObjectIDznd="138@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1043.16 440.83 L 1043.16 482.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv110" d="M 1075.36 394.06 L 1043.15 394.06" stroke-width="1" zvalue="51"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="142@0" LinkObjectIDznd="143" MaxPinNum="2"/>
   </metadata>
  <path d="M 1075.36 394.06 L 1043.15 394.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="131">
   <path class="kv10" d="M 1041.25 574.02 L 1041.25 606.11" stroke-width="1" zvalue="57"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@1" LinkObjectIDznd="191@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1041.25 574.02 L 1041.25 606.11" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="128">
   <path class="kv10" d="M 1042.78 642.61 L 1042.78 669.75" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="191@1" LinkObjectIDznd="127@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1042.78 642.61 L 1042.78 669.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv110" d="M 718.9 388.15 L 718.9 331" stroke-width="1" zvalue="67"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="125@1" LinkObjectIDznd="146@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 718.9 388.15 L 718.9 331" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv110" d="M 751 360.36 L 718.9 360.36" stroke-width="1" zvalue="68"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@0" LinkObjectIDznd="122" MaxPinNum="2"/>
   </metadata>
  <path d="M 751 360.36 L 718.9 360.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="119">
   <path class="kv110" d="M 743.5 451.53 L 718.87 451.53 L 718.87 411.98" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@0" LinkObjectIDznd="125@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 743.5 451.53 L 718.87 451.53 L 718.87 411.98" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv110" d="M 718.64 490.94 L 718.64 451.53" stroke-width="1" zvalue="70"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="115@0" LinkObjectIDznd="119" MaxPinNum="2"/>
   </metadata>
  <path d="M 718.64 490.94 L 718.64 451.53" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="112">
   <path class="kv110" d="M 1043.18 331 L 1043.18 331" stroke-width="1" zvalue="74"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140" LinkObjectIDznd="146@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1043.18 331 L 1043.18 331" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="111">
   <path class="kv10" d="M 585.3 710.46 L 585.3 669.75" stroke-width="1" zvalue="78"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="341@0" LinkObjectIDznd="127@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 585.3 710.46 L 585.3 669.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="110">
   <path class="kv10" d="M 584.05 840.72 L 584.05 749.9" stroke-width="1" zvalue="79"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="341@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 584.05 840.72 L 584.05 749.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv10" d="M 501.76 811.25 L 501.76 793.96 L 584.05 793.96" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="132@1" LinkObjectIDznd="110" MaxPinNum="2"/>
   </metadata>
  <path d="M 501.76 811.25 L 501.76 793.96 L 584.05 793.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="108">
   <path class="kv10" d="M 681.79 850.73 L 681.79 864.94" stroke-width="1" zvalue="87"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="100@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 681.79 850.73 L 681.79 864.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="kv10" d="M 634.67 771.24 L 584.05 771.24" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="181@0" LinkObjectIDznd="110" MaxPinNum="2"/>
   </metadata>
  <path d="M 634.67 771.24 L 584.05 771.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv10" d="M 501.25 884.87 L 501.25 843.34" stroke-width="1" zvalue="95"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="185@0" LinkObjectIDznd="132@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 501.25 884.87 L 501.25 843.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="kv10" d="M 584.05 796 L 681.68 796 L 681.68 818.64" stroke-width="1" zvalue="99"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="110" LinkObjectIDznd="129@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 584.05 796 L 681.68 796 L 681.68 818.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="kv10" d="M 1061.05 836.72 L 1061.05 745.9" stroke-width="1" zvalue="102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@0" LinkObjectIDznd="120@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1061.05 836.72 L 1061.05 745.9" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="kv10" d="M 978.76 807.25 L 978.76 789.96 L 1061.05 789.96" stroke-width="1" zvalue="106"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="116@1" LinkObjectIDznd="97" MaxPinNum="2"/>
   </metadata>
  <path d="M 978.76 807.25 L 978.76 789.96 L 1061.05 789.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv10" d="M 1158.79 846.73 L 1158.79 877.19" stroke-width="1" zvalue="110"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="102@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1158.79 846.73 L 1158.79 877.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv10" d="M 1111.67 767.24 L 1061.05 767.24" stroke-width="1" zvalue="113"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="97" MaxPinNum="2"/>
   </metadata>
  <path d="M 1111.67 767.24 L 1061.05 767.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv10" d="M 978.25 880.87 L 978.25 839.34" stroke-width="1" zvalue="118"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@0" LinkObjectIDznd="116@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 978.25 880.87 L 978.25 839.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv10" d="M 1061.05 792 L 1158.68 792 L 1158.68 814.64" stroke-width="1" zvalue="122"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="97" LinkObjectIDznd="113@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1061.05 792 L 1158.68 792 L 1158.68 814.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv10" d="M 1371.86 791.81 L 1322.16 791.81" stroke-width="1" zvalue="126"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="126@0" LinkObjectIDznd="84" MaxPinNum="2"/>
   </metadata>
  <path d="M 1371.86 791.81 L 1322.16 791.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv10" d="M 1392.94 509.81 L 1393.54 549.14" stroke-width="1" zvalue="135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="130@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1392.94 509.81 L 1393.54 549.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv10" d="M 1325.5 501.14 L 1324.85 549.14" stroke-width="1" zvalue="137"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@0" LinkObjectIDznd="133@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1325.5 501.14 L 1324.85 549.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv10" d="M 1322.16 824.64 L 1322.16 767.52" stroke-width="1" zvalue="140"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="205@0" LinkObjectIDznd="141@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.16 824.64 L 1322.16 767.52" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="kv10" d="M 1322.16 717.78 L 1322.16 669.75" stroke-width="1" zvalue="141"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="141@0" LinkObjectIDznd="127@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.16 717.78 L 1322.16 669.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv10" d="M 1062.3 706.46 L 1062.3 669.75" stroke-width="1" zvalue="157"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="120@0" LinkObjectIDznd="127@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1062.3 706.46 L 1062.3 669.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="76">
   <path class="kv10" d="M 1701.61 499.06 L 1652.3 499.06" stroke-width="1" zvalue="162"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="152@0" LinkObjectIDznd="157" MaxPinNum="2"/>
   </metadata>
  <path d="M 1701.61 499.06 L 1652.3 499.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv10" d="M 434.8 871.67 L 434.8 793.96 L 507.25 793.96" stroke-width="1" zvalue="163"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="189@0" LinkObjectIDznd="109" MaxPinNum="2"/>
   </metadata>
  <path d="M 434.8 871.67 L 434.8 793.96 L 507.25 793.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="kv10" d="M 903.3 871.42 L 903.3 789.96 L 979.75 789.96" stroke-width="1" zvalue="164"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@0" LinkObjectIDznd="96" MaxPinNum="2"/>
   </metadata>
  <path d="M 903.3 871.42 L 903.3 789.96 L 979.75 789.96" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv10" d="M 1325.05 603.43 L 1325.05 669.75" stroke-width="1" zvalue="165"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@0" LinkObjectIDznd="127@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1325.05 603.43 L 1325.05 669.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv10" d="M 1393.74 603.43 L 1393.74 669.75" stroke-width="1" zvalue="166"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="130@0" LinkObjectIDznd="127@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1393.74 603.43 L 1393.74 669.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv10" d="M 1651.34 394.98 L 1651.34 421.46" stroke-width="1" zvalue="170"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="169@0" LinkObjectIDznd="154@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1651.34 394.98 L 1651.34 421.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv10" d="M 1652.3 460.9 L 1652.3 547.89" stroke-width="1" zvalue="171"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@1" LinkObjectIDznd="153@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1652.3 460.9 L 1652.3 547.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="kv110" d="M 947.48 519.28 L 947.48 509.12 L 1041.29 509.12" stroke-width="1" zvalue="177"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="138@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 947.48 519.28 L 947.48 509.12 L 1041.29 509.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="186">
   <path class="kv110" d="M 589.15 309.54 L 589.15 331" stroke-width="1" zvalue="184"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="146@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.15 309.54 L 589.15 331" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="184">
   <path class="kv110" d="M 589.18 285.71 L 589.18 260.78" stroke-width="1" zvalue="186"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@1" LinkObjectIDznd="273@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.18 285.71 L 589.18 260.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="183">
   <path class="kv110" d="M 589.21 239.54 L 589.15 201.54" stroke-width="1" zvalue="187"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="273@0" LinkObjectIDznd="271@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.21 239.54 L 589.15 201.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="182">
   <path class="kv110" d="M 589.18 177.71 L 589.18 137.28" stroke-width="1" zvalue="190"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="271@1" LinkObjectIDznd="267@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 589.18 177.71 L 589.18 137.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="179">
   <path class="kv110" d="M 626.03 166.44 L 589.18 166.44" stroke-width="1" zvalue="199"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="263@0" LinkObjectIDznd="182" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.03 166.44 L 589.18 166.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv110" d="M 626.03 220.89 L 589.18 220.89" stroke-width="1" zvalue="200"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="264@0" LinkObjectIDznd="183" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.03 220.89 L 589.18 220.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="kv110" d="M 626.03 269.67 L 589.18 269.67" stroke-width="1" zvalue="201"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="265@0" LinkObjectIDznd="184" MaxPinNum="2"/>
   </metadata>
  <path d="M 626.03 269.67 L 589.18 269.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="192">
   <path class="kv110" d="M 527.61 137.37 L 589.18 137.37" stroke-width="1" zvalue="207"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="190@0" LinkObjectIDznd="182" MaxPinNum="2"/>
   </metadata>
  <path d="M 527.61 137.37 L 589.18 137.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="194">
   <path class="kv110" d="M 526.4 159.99 L 544.75 159.99 L 544.75 137.37" stroke-width="1" zvalue="210"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="193@0" LinkObjectIDznd="192" MaxPinNum="2"/>
   </metadata>
  <path d="M 526.4 159.99 L 544.75 159.99 L 544.75 137.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv110" d="M 1310.15 309.54 L 1310.15 331" stroke-width="1" zvalue="217"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@0" LinkObjectIDznd="146@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1310.15 309.54 L 1310.15 331" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv110" d="M 1310.18 285.71 L 1310.18 260.78" stroke-width="1" zvalue="219"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@1" LinkObjectIDznd="220@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1310.18 285.71 L 1310.18 260.78" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="215">
   <path class="kv110" d="M 1310.21 239.54 L 1310.15 201.54" stroke-width="1" zvalue="220"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@0" LinkObjectIDznd="218@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1310.21 239.54 L 1310.15 201.54" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="213">
   <path class="kv110" d="M 1310.18 177.71 L 1310.18 137.28" stroke-width="1" zvalue="223"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@1" LinkObjectIDznd="214@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1310.18 177.71 L 1310.18 137.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="kv110" d="M 1347.03 166.44 L 1310.18 166.44" stroke-width="1" zvalue="230"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="210@0" LinkObjectIDznd="213" MaxPinNum="2"/>
   </metadata>
  <path d="M 1347.03 166.44 L 1310.18 166.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv110" d="M 1347.03 220.89 L 1310.18 220.89" stroke-width="1" zvalue="231"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="211@0" LinkObjectIDznd="215" MaxPinNum="2"/>
   </metadata>
  <path d="M 1347.03 220.89 L 1310.18 220.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="207">
   <path class="kv110" d="M 1347.03 269.67 L 1310.18 269.67" stroke-width="1" zvalue="232"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@0" LinkObjectIDznd="216" MaxPinNum="2"/>
   </metadata>
  <path d="M 1347.03 269.67 L 1310.18 269.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="kv110" d="M 1248.61 137.37 L 1310.18 137.37" stroke-width="1" zvalue="234"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@0" LinkObjectIDznd="213" MaxPinNum="2"/>
   </metadata>
  <path d="M 1248.61 137.37 L 1310.18 137.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="202">
   <path class="kv110" d="M 1247.4 159.99 L 1265.75 159.99 L 1265.75 137.37" stroke-width="1" zvalue="236"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="203@0" LinkObjectIDznd="204" MaxPinNum="2"/>
   </metadata>
  <path d="M 1247.4 159.99 L 1265.75 159.99 L 1265.75 137.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="142">
   <use class="kv110" height="20" transform="rotate(270,1086.19,394) scale(-1.11111,1.11111) translate(-2063.21,-38.2889)" width="10" x="1080.638899061415" xlink:href="#GroundDisconnector:地刀_0" y="382.8888888888889" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454765051906" ObjectName="#1主变110kV侧10117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454765051906"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1086.19,394) scale(-1.11111,1.11111) translate(-2063.21,-38.2889)" width="10" x="1080.638899061415" y="382.8888888888889"/></g>
  <g id="230">
   <use class="kv110" height="20" transform="rotate(0,947.375,539.886) scale(2.125,2.11357) translate(-495.926,-273.312)" width="10" x="936.75" xlink:href="#GroundDisconnector:地刀_0" y="518.75" zvalue="53"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454764920834" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454764920834"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,947.375,539.886) scale(2.125,2.11357) translate(-495.926,-273.312)" width="10" x="936.75" y="518.75"/></g>
  <g id="124">
   <use class="kv110" height="20" transform="rotate(270,754.333,451.472) scale(-1.11111,1.11111) translate(-1432.68,-44.0361)" width="10" x="748.7777845594618" xlink:href="#GroundDisconnector:地刀_0" y="440.3611008326212" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454764724226" ObjectName="110kV母线电压互感器19017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454764724226"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,754.333,451.472) scale(-1.11111,1.11111) translate(-1432.68,-44.0361)" width="10" x="748.7777845594618" y="440.3611008326212"/></g>
  <g id="123">
   <use class="kv110" height="20" transform="rotate(270,761.833,360.306) scale(-1.11111,1.11111) translate(-1446.93,-34.9194)" width="10" x="756.2777845594618" xlink:href="#GroundDisconnector:地刀_0" y="349.1944444444444" zvalue="65"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454764593154" ObjectName="110kV母线电压互感器19010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454764593154"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,761.833,360.306) scale(-1.11111,1.11111) translate(-1446.93,-34.9194)" width="10" x="756.2777845594618" y="349.1944444444444"/></g>
  <g id="126">
   <use class="kv10" height="20" transform="rotate(270,1382.69,791.75) scale(-1.11111,1.11111) translate(-2626.56,-78.0639)" width="10" x="1377.138899061415" xlink:href="#GroundDisconnector:地刀_0" y="780.6388888888889" zvalue="125"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454763413506" ObjectName="#1站用变03367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454763413506"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1382.69,791.75) scale(-1.11111,1.11111) translate(-2626.56,-78.0639)" width="10" x="1377.138899061415" y="780.6388888888889"/></g>
  <g id="152">
   <use class="kv10" height="20" transform="rotate(270,1712.44,499) scale(-1.11111,1.11111) translate(-3253.09,-48.7889)" width="10" x="1706.888899061415" xlink:href="#GroundDisconnector:地刀_0" y="487.8888888888889" zvalue="144"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454762889218" ObjectName="#2站用变03467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454762889218"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1712.44,499) scale(-1.11111,1.11111) translate(-3253.09,-48.7889)" width="10" x="1706.888899061415" y="487.8888888888889"/></g>
  <g id="265">
   <use class="kv110" height="20" transform="rotate(270,636.861,269.611) scale(-1.11111,1.11111) translate(-1209.48,-25.85)" width="10" x="631.3055555555555" xlink:href="#GroundDisconnector:地刀_0" y="258.5000050862629" zvalue="191"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454765510658" ObjectName="110kV支那河一二级线13217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454765510658"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,636.861,269.611) scale(-1.11111,1.11111) translate(-1209.48,-25.85)" width="10" x="631.3055555555555" y="258.5000050862629"/></g>
  <g id="264">
   <use class="kv110" height="20" transform="rotate(270,636.861,220.833) scale(-1.11111,1.11111) translate(-1209.48,-20.9722)" width="10" x="631.3055555555557" xlink:href="#GroundDisconnector:地刀_0" y="209.7222220102947" zvalue="193"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454765379586" ObjectName="110kV支那河一二级线13260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454765379586"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,636.861,220.833) scale(-1.11111,1.11111) translate(-1209.48,-20.9722)" width="10" x="631.3055555555557" y="209.7222220102947"/></g>
  <g id="263">
   <use class="kv110" height="20" transform="rotate(270,636.861,166.389) scale(-1.11111,1.11111) translate(-1209.48,-15.5278)" width="10" x="631.3055556615193" xlink:href="#GroundDisconnector:地刀_0" y="155.2777775658501" zvalue="195"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454765248514" ObjectName="110kV支那河一二级线13267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454765248514"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,636.861,166.389) scale(-1.11111,1.11111) translate(-1209.48,-15.5278)" width="10" x="631.3055556615193" y="155.2777775658501"/></g>
  <g id="212">
   <use class="kv110" height="20" transform="rotate(270,1357.86,269.611) scale(-1.11111,1.11111) translate(-2579.38,-25.85)" width="10" x="1352.305555555556" xlink:href="#GroundDisconnector:地刀_0" y="258.5000050862629" zvalue="224"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454766362626" ObjectName="110kV支盏T线13117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454766362626"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1357.86,269.611) scale(-1.11111,1.11111) translate(-2579.38,-25.85)" width="10" x="1352.305555555556" y="258.5000050862629"/></g>
  <g id="211">
   <use class="kv110" height="20" transform="rotate(270,1357.86,220.833) scale(-1.11111,1.11111) translate(-2579.38,-20.9722)" width="10" x="1352.305555555556" xlink:href="#GroundDisconnector:地刀_0" y="209.7222220102947" zvalue="226"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454766231554" ObjectName="110kV支盏T线13160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454766231554"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1357.86,220.833) scale(-1.11111,1.11111) translate(-2579.38,-20.9722)" width="10" x="1352.305555555556" y="209.7222220102947"/></g>
  <g id="210">
   <use class="kv110" height="20" transform="rotate(270,1357.86,166.389) scale(-1.11111,1.11111) translate(-2579.38,-15.5278)" width="10" x="1352.305555661519" xlink:href="#GroundDisconnector:地刀_0" y="155.2777775658501" zvalue="228"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454766100482" ObjectName="110kV支盏T线13167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192454766100482"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1357.86,166.389) scale(-1.11111,1.11111) translate(-2579.38,-15.5278)" width="10" x="1352.305555661519" y="155.2777775658501"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="138">
   <g id="1380">
    <use class="kv110" height="50" transform="rotate(0,1041.25,528.339) scale(2.15,1.85355) translate(-539.698,-221.958)" width="30" x="1009" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="482" zvalue="48"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874591043586" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1381">
    <use class="kv10" height="50" transform="rotate(0,1041.25,528.339) scale(2.15,1.85355) translate(-539.698,-221.958)" width="30" x="1009" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="482" zvalue="48"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874591109122" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399532806146" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399532806146"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1041.25,528.339) scale(2.15,1.85355) translate(-539.698,-221.958)" width="30" x="1009" y="482"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="115">
   <use class="kv110" height="35" transform="rotate(0,716,515.25) scale(1.25,-1.42857) translate(-138.2,-868.425)" width="40" x="691" xlink:href="#Accessory:四卷带壁雷器母线PT_0" y="490.25" zvalue="71"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454764462082" ObjectName="110kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,716,515.25) scale(1.25,-1.42857) translate(-138.2,-868.425)" width="40" x="691" y="490.25"/></g>
  <g id="181">
   <use class="kv10" height="26" transform="rotate(270,648.037,771.276) scale(1.08054,1.08054) translate(-47.8206,-56.4429)" width="12" x="641.5535673143577" xlink:href="#Accessory:避雷器1_0" y="757.2286682615629" zvalue="89"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454764199938" ObjectName="避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,648.037,771.276) scale(1.08054,1.08054) translate(-47.8206,-56.4429)" width="12" x="641.5535673143577" y="757.2286682615629"/></g>
  <g id="185">
   <use class="kv10" height="40" transform="rotate(0,501.25,901.633) scale(1.25,-0.90625) translate(-96.5,-1898.41)" width="30" x="482.5" xlink:href="#Accessory:带熔断器的线路PT1_0" y="883.5079345703125" zvalue="91"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454764134402" ObjectName="#1发电机励磁变PT1"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,501.25,901.633) scale(1.25,-0.90625) translate(-96.5,-1898.41)" width="30" x="482.5" y="883.5079345703125"/></g>
  <g id="189">
   <use class="kv10" height="29" transform="rotate(0,434.796,893.487) scale(1.53077,-1.53077) translate(-142.797,-1469.48)" width="30" x="411.8340697067018" xlink:href="#Accessory:厂用变2020_0" y="871.2905701754385" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454764068866" ObjectName="#1发电机励磁PT2"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,434.796,893.487) scale(1.53077,-1.53077) translate(-142.797,-1469.48)" width="30" x="411.8340697067018" y="871.2905701754385"/></g>
  <g id="100">
   <use class="kv10" height="30" transform="rotate(0,681.25,883.25) scale(1.25,1.25) translate(-132.5,-172.9)" width="30" x="662.5" xlink:href="#Accessory:PT带熔断器_0" y="864.5" zvalue="96"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454764003330" ObjectName="#1机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,681.25,883.25) scale(1.25,1.25) translate(-132.5,-172.9)" width="30" x="662.5" y="864.5"/></g>
  <g id="107">
   <use class="kv10" height="26" transform="rotate(270,1125.04,767.276) scale(1.08054,1.08054) translate(-83.3756,-56.1447)" width="12" x="1118.553567314358" xlink:href="#Accessory:避雷器1_0" y="753.2286682615629" zvalue="112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454763741186" ObjectName="避雷器2"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(270,1125.04,767.276) scale(1.08054,1.08054) translate(-83.3756,-56.1447)" width="12" x="1118.553567314358" y="753.2286682615629"/></g>
  <g id="105">
   <use class="kv10" height="40" transform="rotate(0,978.25,897.633) scale(1.25,-0.90625) translate(-191.9,-1890)" width="30" x="959.5" xlink:href="#Accessory:带熔断器的线路PT1_0" y="879.5079345703125" zvalue="114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454763675650" ObjectName="#2发电机励磁变PT1"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,978.25,897.633) scale(1.25,-0.90625) translate(-191.9,-1890)" width="30" x="959.5" y="879.5079345703125"/></g>
  <g id="104">
   <use class="kv10" height="29" transform="rotate(0,903.296,893.237) scale(1.53077,-1.53077) translate(-305.241,-1469.06)" width="30" x="880.3340697067018" xlink:href="#Accessory:厂用变2020_0" y="871.0405701754385" zvalue="116"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454763610114" ObjectName="#2发电机励磁PT2"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,903.296,893.237) scale(1.53077,-1.53077) translate(-305.241,-1469.06)" width="30" x="880.3340697067018" y="871.0405701754385"/></g>
  <g id="102">
   <use class="kv10" height="30" transform="rotate(0,1158.25,895.5) scale(1.25,1.25) translate(-227.9,-175.35)" width="30" x="1139.5" xlink:href="#Accessory:PT带熔断器_0" y="876.75" zvalue="119"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454763544578" ObjectName="#2机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1158.25,895.5) scale(1.25,1.25) translate(-227.9,-175.35)" width="30" x="1139.5" y="876.75"/></g>
  <g id="137">
   <use class="kv10" height="30" transform="rotate(0,1393,491.5) scale(1.25,-1.25) translate(-274.85,-880.95)" width="30" x="1374.25" xlink:href="#Accessory:PT带熔断器_0" y="472.75" zvalue="134"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454763151362" ObjectName="10kV机组PT"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1393,491.5) scale(1.25,-1.25) translate(-274.85,-880.95)" width="30" x="1374.25" y="472.75"/></g>
  <g id="139">
   <use class="kv10" height="26" transform="rotate(180,1325.54,487.776) scale(1.08054,1.08054) translate(-98.3206,-35.3111)" width="12" x="1319.053567314358" xlink:href="#Accessory:避雷器1_0" y="473.7286682615629" zvalue="136"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454763085826" ObjectName="避雷器3"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(180,1325.54,487.776) scale(1.08054,1.08054) translate(-98.3206,-35.3111)" width="12" x="1319.053567314358" y="473.7286682615629"/></g>
  <g id="169">
   <use class="kv10" height="13" transform="rotate(0,1651.34,386.75) scale(1.25,-1.25) translate(-328.894,-694.525)" width="11" x="1644.46875" xlink:href="#Accessory:空挂线路_0" y="378.625" zvalue="158"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454762758146" ObjectName="10kV外接电源"/>
   </metadata>
  <rect fill="white" height="13" opacity="0" stroke="white" transform="rotate(0,1651.34,386.75) scale(1.25,-1.25) translate(-328.894,-694.525)" width="11" x="1644.46875" y="378.625"/></g>
  <g id="190">
   <use class="kv110" height="40" transform="rotate(270,506,128.5) scale(1.25,1.25) translate(-96.2,-20.7)" width="40" x="481" xlink:href="#Accessory:220kV线路PT_0" y="103.5" zvalue="206"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454765772802" ObjectName="110kV支那河一二级线线路PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,506,128.5) scale(1.25,1.25) translate(-96.2,-20.7)" width="40" x="481" y="103.5"/></g>
  <g id="193">
   <use class="kv110" height="26" transform="rotate(90,513.037,160.026) scale(-1.08054,1.08054) translate(-987.349,-10.8811)" width="12" x="506.5535673143577" xlink:href="#Accessory:避雷器1_0" y="145.9786682615629" zvalue="209"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454765838338" ObjectName="避雷器4"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,513.037,160.026) scale(-1.08054,1.08054) translate(-987.349,-10.8811)" width="12" x="506.5535673143577" y="145.9786682615629"/></g>
  <g id="206">
   <use class="kv110" height="40" transform="rotate(270,1227,128.5) scale(1.25,1.25) translate(-240.4,-20.7)" width="40" x="1202" xlink:href="#Accessory:220kV线路PT_0" y="103.5" zvalue="233"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454765969410" ObjectName="110kV支盏T线线路PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,1227,128.5) scale(1.25,1.25) translate(-240.4,-20.7)" width="40" x="1202" y="103.5"/></g>
  <g id="203">
   <use class="kv110" height="26" transform="rotate(90,1234.04,160.026) scale(-1.08054,1.08054) translate(-2375.61,-10.8811)" width="12" x="1227.553567314358" xlink:href="#Accessory:避雷器1_0" y="145.9786682615629" zvalue="235"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454765903874" ObjectName="避雷器5"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1234.04,160.026) scale(-1.08054,1.08054) translate(-2375.61,-10.8811)" width="12" x="1227.553567314358" y="145.9786682615629"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="kv10" height="30" transform="rotate(0,584.047,868.136) scale(1.85899,1.85899) translate(-256.988,-388.258)" width="30" x="556.1617897505679" xlink:href="#Generator:发电机_0" y="840.2510218856319" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454764396546" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192454764396546"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,584.047,868.136) scale(1.85899,1.85899) translate(-256.988,-388.258)" width="30" x="556.1617897505679" y="840.2510218856319"/></g>
  <g id="117">
   <use class="kv10" height="30" transform="rotate(0,1061.05,864.136) scale(1.85899,1.85899) translate(-477.397,-386.409)" width="30" x="1033.161789750568" xlink:href="#Generator:发电机_0" y="836.2510218856319" zvalue="103"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454763937794" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192454763937794"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1061.05,864.136) scale(1.85899,1.85899) translate(-477.397,-386.409)" width="30" x="1033.161789750568" y="836.2510218856319"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="205">
   <use class="kv10" height="30" transform="rotate(0,1322,849.25) scale(1.69643,1.70833) translate(-532.966,-341.503)" width="28" x="1298.25" xlink:href="#EnergyConsumer:站用变DY接地_0" y="823.625" zvalue="123"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454763479042" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1322,849.25) scale(1.69643,1.70833) translate(-532.966,-341.503)" width="28" x="1298.25" y="823.625"/></g>
  <g id="153">
   <use class="kv10" height="30" transform="rotate(0,1651.75,572.5) scale(1.69643,1.70833) translate(-668.337,-226.753)" width="28" x="1628" xlink:href="#EnergyConsumer:站用变DY接地_0" y="546.875" zvalue="142"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454762954754" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1651.75,572.5) scale(1.69643,1.70833) translate(-668.337,-226.753)" width="28" x="1628" y="546.875"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="9" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,589.182,17.5556) scale(1,1) translate(0,-8.63504e-17)" writing-mode="lr" x="588.71" xml:space="preserve" y="22.33" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136317661186" ObjectName="P"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="10" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1310.18,13.5556) scale(1,1) translate(0,0)" writing-mode="lr" x="1309.71" xml:space="preserve" y="18.33" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136319234050" ObjectName="P"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="11" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,589.182,36.5556) scale(1,1) translate(0,0)" writing-mode="lr" x="588.71" xml:space="preserve" y="41.33" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136317726722" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="12" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1310.18,34.5556) scale(1,1) translate(0,0)" writing-mode="lr" x="1309.71" xml:space="preserve" y="39.33" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136319299586" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="13" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,589.182,55.5556) scale(1,1) translate(0,0)" writing-mode="lr" x="588.71" xml:space="preserve" y="60.33" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136317792258" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="14" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1310.18,55.5556) scale(1,1) translate(0,0)" writing-mode="lr" x="1309.71" xml:space="preserve" y="60.33" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136319365122" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="19" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,973.25,394.278) scale(1,1) translate(0,0)" writing-mode="lr" x="972.7" xml:space="preserve" y="400.56" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136314056706" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="26">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="26" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,973.25,415.722) scale(1,1) translate(0,0)" writing-mode="lr" x="972.7" xml:space="preserve" y="422" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136314122242" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="80" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,960.583,598.622) scale(1,1) translate(0,-1.29035e-13)" writing-mode="lr" x="960.03" xml:space="preserve" y="604.9" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136314187778" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="81">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="81" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,960.583,620.066) scale(1,1) translate(0,0)" writing-mode="lr" x="960.03" xml:space="preserve" y="626.34" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136314253314" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="82">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="82" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,973.25,437.167) scale(1,1) translate(0,0)" writing-mode="lr" x="972.7" xml:space="preserve" y="443.44" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136314318850" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="87" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,960.583,641.511) scale(1,1) translate(0,-2.77116e-13)" writing-mode="lr" x="960.03" xml:space="preserve" y="647.79" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136314646530" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="99" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1057.05,971.521) scale(1,1) translate(1.11472e-13,0)" writing-mode="lr" x="1056.49" xml:space="preserve" y="977.8" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136310648834" ObjectName="P"/>
   </metadata>
  </g>
  <g id="114">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="114" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,584.047,967.521) scale(1,1) translate(0,0)" writing-mode="lr" x="583.49" xml:space="preserve" y="973.8" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136311828482" ObjectName="P"/>
   </metadata>
  </g>
  <g id="134">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="134" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1057.05,996.521) scale(1,1) translate(1.11472e-13,0)" writing-mode="lr" x="1056.49" xml:space="preserve" y="1002.8" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136310714370" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="150">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="150" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,584.047,992.521) scale(1,1) translate(0,0)" writing-mode="lr" x="583.49" xml:space="preserve" y="998.8" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136311894018" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="155">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="155" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1057.05,1021.52) scale(1,1) translate(1.11472e-13,-1.56056e-12)" writing-mode="lr" x="1056.49" xml:space="preserve" y="1027.8" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136310779906" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="160">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="160" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,584.047,1013.52) scale(1,1) translate(0,0)" writing-mode="lr" x="583.49" xml:space="preserve" y="1019.8" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136311959554" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="248">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="248" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,154.611,370.917) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="375.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136317005826" ObjectName="F"/>
   </metadata>
  </g>
  <g id="247">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="247" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,154.611,298.917) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="303.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136352395266" ObjectName="F"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,332.222,299.917) scale(1,1) translate(0,0)" writing-mode="lr" x="332.38" xml:space="preserve" y="304.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136352460802" ObjectName="F"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="246" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,332.222,371.917) scale(1,1) translate(0,0)" writing-mode="lr" x="332.38" xml:space="preserve" y="376.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136313401346" ObjectName="F"/>
   </metadata>
  </g>
  <g id="240">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="240" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,154.611,323.917) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="328.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136352264194" ObjectName="F"/>
   </metadata>
  </g>
  <g id="239">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="239" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,332.222,324.917) scale(1,1) translate(0,0)" writing-mode="lr" x="332.38" xml:space="preserve" y="329.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136352329730" ObjectName="F"/>
   </metadata>
  </g>
  <g id="237">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="237" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,154.611,416.139) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="421.05" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123869949956" ObjectName="二级大坝水位"/>
   </metadata>
  </g>
  <g id="235">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="235" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,332.611,416.139) scale(1,1) translate(0,0)" writing-mode="lr" x="332.77" xml:space="preserve" y="421.05" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481123870015492" ObjectName="二级大坝雨量"/>
   </metadata>
  </g>
  <g id="231">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="231" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,154.611,347.917) scale(1,1) translate(0,0)" writing-mode="lr" x="154.77" xml:space="preserve" y="352.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136352264194" ObjectName="F"/>
   </metadata>
  </g>
  <g id="228">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="228" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,334.611,346.917) scale(1,1) translate(0,0)" writing-mode="lr" x="334.77" xml:space="preserve" y="351.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136352264194" ObjectName="F"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="4" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1380,389.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1379.53" xml:space="preserve" y="394.03" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136313008130" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="5" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,637.75,466.5) scale(1,1) translate(0,0)" writing-mode="lr" x="637.28" xml:space="preserve" y="471.28" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136316612610" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="6" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1380,414.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1379.53" xml:space="preserve" y="419.03" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136313073666" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="7" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,637.75,491.5) scale(1,1) translate(0,0)" writing-mode="lr" x="637.28" xml:space="preserve" y="496.28" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136316678146" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="8" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1380,439.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1379.53" xml:space="preserve" y="444.03" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136313139202" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="15" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,637.75,516.5) scale(1,1) translate(0,0)" writing-mode="lr" x="637.28" xml:space="preserve" y="521.28" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136316743682" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="16" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,449,687.75) scale(1,1) translate(0,0)" writing-mode="lr" x="448.53" xml:space="preserve" y="692.53" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136313270274" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="27">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="27" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,503.75,350) scale(1,1) translate(0,0)" writing-mode="lr" x="503.28" xml:space="preserve" y="354.78" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481136316874754" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="244">
   <use height="30" transform="rotate(0,329.673,225.107) scale(0.708333,0.665547) translate(131.373,108.105)" width="30" x="319.05" xlink:href="#State:红绿圆(方形)_0" y="215.12" zvalue="309"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374929625089" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.673,225.107) scale(0.708333,0.665547) translate(131.373,108.105)" width="30" x="319.05" y="215.12"/></g>
  <g id="2">
   <use height="30" transform="rotate(0,238.048,224.107) scale(0.708333,0.665547) translate(93.6446,107.602)" width="30" x="227.42" xlink:href="#State:红绿圆(方形)_0" y="214.12" zvalue="337"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562962236833793" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,238.048,224.107) scale(0.708333,0.665547) translate(93.6446,107.602)" width="30" x="227.42" y="214.12"/></g>
 </g>
</svg>