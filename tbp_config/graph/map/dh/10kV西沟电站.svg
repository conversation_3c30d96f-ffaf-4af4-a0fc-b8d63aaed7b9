<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549592391682" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV西沟电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="50.21" xlink:href="logo.png" y="47.57"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.214,79.5714) scale(1,1) translate(0,0)" writing-mode="lr" x="199.21" xml:space="preserve" y="83.06999999999999" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,201.714,77.2618) scale(1,1) translate(0,0)" writing-mode="lr" x="201.71" xml:space="preserve" y="86.26000000000001" zvalue="5">10kV西沟电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="49" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,83.625,209) scale(1,1) translate(0,0)" width="72.88" x="47.19" y="197" zvalue="67"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.625,209) scale(1,1) translate(0,0)" writing-mode="lr" x="83.63" xml:space="preserve" y="213.5" zvalue="67">信号一览</text>
  <line fill="none" id="37" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384.2142857142856" x2="384.2142857142856" y1="15.57142857142867" y2="1045.571428571429" zvalue="6"/>
  <line fill="none" id="35" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="151.441921185511" y2="151.441921185511" zvalue="8"/>
  <line fill="none" id="34" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="621.441921185511" y2="621.441921185511" zvalue="9"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="936.5714285714287" y2="936.5714285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="975.7347285714286" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="370.2142857142856" y1="936.5714285714287" y2="936.5714285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="370.2142857142856" y1="975.7347285714286" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142856" x2="190.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="190.2142857142857" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142857" x2="280.2142857142857" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="280.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142856" x2="190.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="190.2142857142857" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142857" x2="280.2142857142857" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="280.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.2143,956.571) scale(1,1) translate(0,0)" writing-mode="lr" x="55.21" xml:space="preserve" y="962.5700000000001" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.2143,990.571) scale(1,1) translate(0,0)" writing-mode="lr" x="52.21" xml:space="preserve" y="996.5700000000001" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.214,990.571) scale(1,1) translate(0,0)" writing-mode="lr" x="234.21" xml:space="preserve" y="996.5700000000001" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.2143,1018.57) scale(1,1) translate(0,0)" writing-mode="lr" x="51.21" xml:space="preserve" y="1024.57" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.214,1018.57) scale(1,1) translate(0,0)" writing-mode="lr" x="233.21" xml:space="preserve" y="1024.57" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,75.7143,651.071) scale(1,1) translate(0,2.10522e-13)" writing-mode="lr" x="75.71428571428555" xml:space="preserve" y="655.5714285714286" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.268,958.571) scale(1,1) translate(0,0)" writing-mode="lr" x="235.27" xml:space="preserve" y="964.5700000000001" zvalue="27">XiGou-01-2014</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,687.686,539.352) scale(1,1) translate(0,0)" writing-mode="lr" x="687.6900000000001" xml:space="preserve" y="543.85" zvalue="33">#1主变630kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,772.061,879.99) scale(1,1) translate(0,2.86182e-13)" writing-mode="lr" x="772.0606713030318" xml:space="preserve" y="884.49045413687" zvalue="36">#1发电机500kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,793.402,320.869) scale(1,1) translate(0,0)" writing-mode="lr" x="793.4" xml:space="preserve" y="325.37" zvalue="39">0012</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,774.125,188.125) scale(1,1) translate(-1.59983e-13,0)" writing-mode="lr" x="774.13" xml:space="preserve" y="192.63" zvalue="42">10kV工业园区线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,786.551,692.758) scale(1,1) translate(0,0)" writing-mode="lr" x="786.55" xml:space="preserve" y="697.26" zvalue="45">401</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,797.889,408.369) scale(1,1) translate(-1.7375e-13,3.55155e-13)" writing-mode="lr" x="797.89" xml:space="preserve" y="412.87" zvalue="54">0011</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="270.75" y2="270.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="296.75" y2="296.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="270.75" y2="296.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="270.75" y2="296.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="270.75" y2="270.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="296.75" y2="296.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="270.75" y2="296.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="270.75" y2="296.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="296.75" y2="296.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="321" y2="321"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="296.75" y2="321"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="296.75" y2="321"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="296.75" y2="296.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="321" y2="321"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="296.75" y2="321"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="296.75" y2="321"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="321" y2="321"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="343.75" y2="343.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="321" y2="343.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="321" y2="343.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="321" y2="321"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="343.75" y2="343.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="321" y2="343.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="321" y2="343.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="343.75" y2="343.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="366.5" y2="366.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="343.75" y2="366.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="343.75" y2="366.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="343.75" y2="343.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="366.5" y2="366.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="343.75" y2="366.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="343.75" y2="366.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="366.5" y2="366.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="389.25" y2="389.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="366.5" y2="389.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="366.5" y2="389.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="366.5" y2="366.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="389.25" y2="389.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="366.5" y2="389.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="366.5" y2="389.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="389.25" y2="389.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="412" y2="412"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="389.25" y2="412"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="389.25" y2="412"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="389.25" y2="389.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="412" y2="412"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="389.25" y2="412"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="389.25" y2="412"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="412" y2="412"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="191" y1="434.75" y2="434.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10" x2="10" y1="412" y2="434.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="412" y2="434.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="412" y2="412"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="372" y1="434.75" y2="434.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191" x2="191" y1="412" y2="434.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372" x2="372" y1="412" y2="434.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.399,209.591) scale(1,1) translate(0,0)" writing-mode="lr" x="199.4" xml:space="preserve" y="214.09" zvalue="58">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,304.399,209.591) scale(1,1) translate(0,0)" writing-mode="lr" x="304.4" xml:space="preserve" y="214.09" zvalue="59">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,56.5,283.75) scale(1,1) translate(0,0)" writing-mode="lr" x="14" xml:space="preserve" y="288.25" zvalue="60">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,237,283.75) scale(1,1) translate(0,0)" writing-mode="lr" x="194.5" xml:space="preserve" y="288.25" zvalue="61">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,55.5,309.75) scale(1,1) translate(0,0)" writing-mode="lr" x="13" xml:space="preserve" y="314.25" zvalue="68">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,236,309.75) scale(1,1) translate(0,0)" writing-mode="lr" x="193.5" xml:space="preserve" y="314.25" zvalue="69">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.6875,403) scale(1,1) translate(0,0)" writing-mode="lr" x="56.69" xml:space="preserve" y="407.5" zvalue="72">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224.688,402) scale(1,1) translate(0,0)" writing-mode="lr" x="224.69" xml:space="preserve" y="406.5" zvalue="73">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.6875,426) scale(1,1) translate(0,0)" writing-mode="lr" x="56.69" xml:space="preserve" y="430.5" zvalue="74">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,224.688,425) scale(1,1) translate(0,0)" writing-mode="lr" x="224.69" xml:space="preserve" y="429.5" zvalue="75">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,55.5,332.75) scale(1,1) translate(0,0)" writing-mode="lr" x="13" xml:space="preserve" y="337.25" zvalue="76">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,235.5,331.75) scale(1,1) translate(0,0)" writing-mode="lr" x="193" xml:space="preserve" y="336.25" zvalue="78">厂用电率</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="47.19" y="197" zvalue="67"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="PowerTransformer2Class">
  <g id="414">
   <g id="4140">
    <use class="kv10" height="60" transform="rotate(0,767.179,536.077) scale(1.6125,1.54462) translate(-279.159,-172.678)" width="40" x="734.9299999999999" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="489.74" zvalue="32"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874453876739" ObjectName="10"/>
    </metadata>
   </g>
   <g id="4141">
    <use class="v400" height="60" transform="rotate(0,767.179,536.077) scale(1.6125,1.54462) translate(-279.159,-172.678)" width="40" x="734.9299999999999" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="489.74" zvalue="32"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874453942275" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399458881539" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399458881539"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,767.179,536.077) scale(1.6125,1.54462) translate(-279.159,-172.678)" width="40" x="734.9299999999999" y="489.74"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="212">
   <use class="v400" height="30" transform="rotate(0,767.271,822.672) scale(1.85899,1.85899) translate(-341.651,-367.25)" width="30" x="739.3863709131574" xlink:href="#Generator:发电机_0" y="794.7867361713461" zvalue="35"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450011463685" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192450011463685"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,767.271,822.672) scale(1.85899,1.85899) translate(-341.651,-367.25)" width="30" x="739.3863709131574" y="794.7867361713461"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="206">
   <use class="kv10" height="30" transform="rotate(0,768.069,321.869) scale(-1.11111,-0.814815) translate(-1458.5,-719.668)" width="15" x="759.7353709301144" xlink:href="#Disconnector:刀闸_0" y="309.6468390661572" zvalue="37"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450011398149" ObjectName="#1主变10kV侧0012隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450011398149"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,768.069,321.869) scale(-1.11111,-0.814815) translate(-1458.5,-719.668)" width="15" x="759.7353709301144" y="309.6468390661572"/></g>
  <g id="44">
   <use class="kv10" height="30" transform="rotate(0,766.819,409.369) scale(-1.11111,-0.814815) translate(-1456.12,-914.554)" width="15" x="758.4853709301144" xlink:href="#Disconnector:刀闸_0" y="397.1468390661572" zvalue="53"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450011529221" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450011529221"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,766.819,409.369) scale(-1.11111,-0.814815) translate(-1456.12,-914.554)" width="15" x="758.4853709301144" y="397.1468390661572"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="156">
   <use class="kv10" height="30" transform="rotate(0,768.001,228.688) scale(2.32143,1.52083) translate(-432.545,-70.5051)" width="7" x="759.8757437922191" xlink:href="#ACLineSegment:线路_0" y="205.875" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450011332613" ObjectName="10kV工业园区线"/>
   <cge:TPSR_Ref TObjectID="6192450011332613_5066549592391682"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,768.001,228.688) scale(2.32143,1.52083) translate(-432.545,-70.5051)" width="7" x="759.8757437922191" y="205.875"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="11">
   <path class="kv10" d="M 768 309.86 L 768 251.27" stroke-width="1" zvalue="41"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@1" LinkObjectIDznd="156@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 768 309.86 L 768 251.27" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="8">
   <path class="v400" d="M 767.27 704.37 L 767.27 795.25" stroke-width="1" zvalue="49"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@1" LinkObjectIDznd="212@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 767.27 704.37 L 767.27 795.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="v400" d="M 767.18 581.76 L 767.15 683.13" stroke-width="1" zvalue="50"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@1" LinkObjectIDznd="108@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 767.18 581.76 L 767.15 683.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv10" d="M 767.24 490.58 L 767.24 421.19" stroke-width="1" zvalue="54"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@0" LinkObjectIDznd="44@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 767.24 490.58 L 767.24 421.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="46">
   <path class="kv10" d="M 766.75 397.36 L 766.75 333.69" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@1" LinkObjectIDznd="206@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 766.75 397.36 L 766.75 333.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="108">
   <use class="v400" height="20" transform="rotate(0,767.19,693.758) scale(1.22222,1.11111) translate(-138.378,-68.2647)" width="10" x="761.078631472918" xlink:href="#Breaker:开关_0" y="682.6468253968253" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924555374596" ObjectName="#1主变0.4kV侧401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924555374596"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,767.19,693.758) scale(1.22222,1.11111) translate(-138.378,-68.2647)" width="10" x="761.078631472918" y="682.6468253968253"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="273">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="273" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,156.611,283.917) scale(1,1) translate(0,0)" writing-mode="lr" x="156.77" xml:space="preserve" y="288.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126607650820" ObjectName="F"/>
   </metadata>
  </g>
  <g id="272">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="272" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,334.222,284.917) scale(1,1) translate(0,0)" writing-mode="lr" x="334.38" xml:space="preserve" y="289.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126607716356" ObjectName="F"/>
   </metadata>
  </g>
  <g id="260">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="260" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,156.611,308.917) scale(1,1) translate(0,0)" writing-mode="lr" x="156.77" xml:space="preserve" y="313.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126607519748" ObjectName="F"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,334.222,309.917) scale(1,1) translate(0,0)" writing-mode="lr" x="334.38" xml:space="preserve" y="314.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126607585284" ObjectName="F"/>
   </metadata>
  </g>
  <g id="209">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="209" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,156.611,332.917) scale(1,1) translate(0,0)" writing-mode="lr" x="156.77" xml:space="preserve" y="337.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126607519748" ObjectName="F"/>
   </metadata>
  </g>
  <g id="198">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,336.611,331.917) scale(1,1) translate(0,0)" writing-mode="lr" x="336.77" xml:space="preserve" y="336.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126607519748" ObjectName="F"/>
   </metadata>
  </g>
  <g id="287">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="287" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,156.611,399.917) scale(1,1) translate(0,0)" writing-mode="lr" x="156.77" xml:space="preserve" y="404.83" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="288">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="288" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,332.611,399.917) scale(1,1) translate(0,0)" writing-mode="lr" x="332.77" xml:space="preserve" y="404.83" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="雨量采集"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="271">
   <use height="30" transform="rotate(0,331.673,210.107) scale(0.708333,0.665547) translate(132.196,100.567)" width="30" x="321.05" xlink:href="#State:红绿圆(方形)_0" y="200.12" zvalue="66"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,331.673,210.107) scale(0.708333,0.665547) translate(132.196,100.567)" width="30" x="321.05" y="200.12"/></g>
  <g id="286">
   <use height="30" transform="rotate(0,236.048,210.107) scale(0.708333,0.665547) translate(92.8211,100.567)" width="30" x="225.42" xlink:href="#State:红绿圆(方形)_0" y="200.12" zvalue="80"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,236.048,210.107) scale(0.708333,0.665547) translate(92.8211,100.567)" width="30" x="225.42" y="200.12"/></g>
 </g>
</svg>