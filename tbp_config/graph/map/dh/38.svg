<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" height="2000" id="thSvg" source="NR-PCS9000" viewBox="0 0 2000 2000" width="2000">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="38" InitShowingPlane="" fill="rgb(0,0,0)" height="2000" width="2000" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="243.14" xlink:href="logo.png" y="614.67"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,367.987,655.953) scale(1,1) translate(-5.39879e-14,0)" writing-mode="lr" x="367.99" xml:space="preserve" y="659.45" zvalue="6"/>
  <text fill="rgb(0,0,0)" font-family="SimSun" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,396.833,655.93) scale(1,1) translate(0,0)" writing-mode="lr" x="396.83" xml:space="preserve" y="664.9299999999999" zvalue="7">38</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="27" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,378.365,972.489) scale(1,1) translate(0,-2.13272e-13)" width="72.88" x="341.93" y="960.49" zvalue="75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,378.365,972.489) scale(1,1) translate(0,-2.13272e-13)" writing-mode="lr" x="378.36" xml:space="preserve" y="976.99" zvalue="75">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="26" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,276.458,972.489) scale(1,1) translate(0,-2.13272e-13)" width="72.88" x="240.02" y="960.49" zvalue="76"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,276.458,972.489) scale(1,1) translate(0,-2.13272e-13)" writing-mode="lr" x="276.46" xml:space="preserve" y="976.99" zvalue="76">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="25" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,276.458,931.989) scale(1,1) translate(0,0)" width="72.88" x="240.02" y="919.99" zvalue="77"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,276.458,931.989) scale(1,1) translate(0,0)" writing-mode="lr" x="276.46" xml:space="preserve" y="936.49" zvalue="77">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,729.409,970.83) scale(1,1) translate(0,0)" writing-mode="lr" x="729.41" xml:space="preserve" y="975.33" zvalue="3">35kVI段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,980.321,1309.19) scale(1,1) translate(0,0)" writing-mode="lr" x="980.3200000000001" xml:space="preserve" y="1313.69" zvalue="5">10kVI母线</text>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="224.8333333333335" x2="314.8333333333335" y1="1505.239460784315" y2="1505.239460784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="224.8333333333335" x2="314.8333333333335" y1="1557.402760784315" y2="1557.402760784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="224.8333333333335" x2="224.8333333333335" y1="1505.239460784315" y2="1557.402760784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="314.8333333333335" x2="314.8333333333335" y1="1505.239460784315" y2="1557.402760784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="314.8333333333335" x2="584.8333333333335" y1="1505.239460784315" y2="1505.239460784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="314.8333333333335" x2="584.8333333333335" y1="1557.402760784315" y2="1557.402760784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="314.8333333333335" x2="314.8333333333335" y1="1505.239460784315" y2="1557.402760784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="584.8333333333335" x2="584.8333333333335" y1="1505.239460784315" y2="1557.402760784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="224.8333333333335" x2="314.8333333333335" y1="1557.402730784315" y2="1557.402730784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="224.8333333333335" x2="314.8333333333335" y1="1585.321130784315" y2="1585.321130784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="224.8333333333335" x2="224.8333333333335" y1="1557.402730784315" y2="1585.321130784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="314.8333333333335" x2="314.8333333333335" y1="1557.402730784315" y2="1585.321130784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="314.8333333333335" x2="404.8333333333335" y1="1557.402730784315" y2="1557.402730784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="314.8333333333335" x2="404.8333333333335" y1="1585.321130784315" y2="1585.321130784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="314.8333333333335" x2="314.8333333333335" y1="1557.402730784315" y2="1585.321130784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="404.8333333333335" x2="404.8333333333335" y1="1557.402730784315" y2="1585.321130784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="404.8333333333336" x2="494.8333333333336" y1="1557.402730784315" y2="1557.402730784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="404.8333333333336" x2="494.8333333333336" y1="1585.321130784315" y2="1585.321130784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="404.8333333333336" x2="404.8333333333336" y1="1557.402730784315" y2="1585.321130784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="494.8333333333336" x2="494.8333333333336" y1="1557.402730784315" y2="1585.321130784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="494.8333333333335" x2="584.8333333333335" y1="1557.402730784315" y2="1557.402730784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="494.8333333333335" x2="584.8333333333335" y1="1585.321130784315" y2="1585.321130784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="494.8333333333335" x2="494.8333333333335" y1="1557.402730784315" y2="1585.321130784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="584.8333333333335" x2="584.8333333333335" y1="1557.402730784315" y2="1585.321130784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="224.8333333333335" x2="314.8333333333335" y1="1585.321060784315" y2="1585.321060784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="224.8333333333335" x2="314.8333333333335" y1="1613.239460784315" y2="1613.239460784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="224.8333333333335" x2="224.8333333333335" y1="1585.321060784315" y2="1613.239460784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="314.8333333333335" x2="314.8333333333335" y1="1585.321060784315" y2="1613.239460784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="314.8333333333335" x2="404.8333333333335" y1="1585.321060784315" y2="1585.321060784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="314.8333333333335" x2="404.8333333333335" y1="1613.239460784315" y2="1613.239460784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="314.8333333333335" x2="314.8333333333335" y1="1585.321060784315" y2="1613.239460784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="404.8333333333335" x2="404.8333333333335" y1="1585.321060784315" y2="1613.239460784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="404.8333333333336" x2="494.8333333333336" y1="1585.321060784315" y2="1585.321060784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="404.8333333333336" x2="494.8333333333336" y1="1613.239460784315" y2="1613.239460784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="404.8333333333336" x2="404.8333333333336" y1="1585.321060784315" y2="1613.239460784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="494.8333333333336" x2="494.8333333333336" y1="1585.321060784315" y2="1613.239460784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="494.8333333333335" x2="584.8333333333335" y1="1585.321060784315" y2="1585.321060784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="494.8333333333335" x2="584.8333333333335" y1="1613.239460784315" y2="1613.239460784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="494.8333333333335" x2="494.8333333333335" y1="1585.321060784315" y2="1613.239460784315"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="584.8333333333335" x2="584.8333333333335" y1="1585.321060784315" y2="1613.239460784315"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="68" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,327.333,1572.24) scale(1,1) translate(0,0)" writing-mode="lr" x="251.83" xml:space="preserve" y="1578.24" zvalue="10">制图               李文杰</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="67" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,496.833,1572.24) scale(1,1) translate(0,0)" writing-mode="lr" x="413.83" xml:space="preserve" y="1578.24" zvalue="11">绘制日期      20200922</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,265.833,1600.24) scale(1,1) translate(0,0)" writing-mode="lr" x="265.83" xml:space="preserve" y="1606.24" zvalue="12">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,447.833,1600.24) scale(1,1) translate(0,0)" writing-mode="lr" x="447.83" xml:space="preserve" y="1606.24" zvalue="13">更新日期</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.8333333333335" x2="401.8333333333335" y1="741.239460784315" y2="741.239460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.8333333333335" x2="401.8333333333335" y1="767.239460784315" y2="767.239460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.8333333333335" x2="220.8333333333335" y1="741.239460784315" y2="767.239460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="401.8333333333335" x2="401.8333333333335" y1="741.239460784315" y2="767.239460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="401.8333333333335" x2="582.8333333333335" y1="741.239460784315" y2="741.239460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="401.8333333333335" x2="582.8333333333335" y1="767.239460784315" y2="767.239460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="401.8333333333335" x2="401.8333333333335" y1="741.239460784315" y2="767.239460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="582.8333333333335" x2="582.8333333333335" y1="741.239460784315" y2="767.239460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.8333333333335" x2="401.8333333333335" y1="767.239460784315" y2="767.239460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.8333333333335" x2="401.8333333333335" y1="791.489460784315" y2="791.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.8333333333335" x2="220.8333333333335" y1="767.239460784315" y2="791.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="401.8333333333335" x2="401.8333333333335" y1="767.239460784315" y2="791.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="401.8333333333335" x2="582.8333333333335" y1="767.239460784315" y2="767.239460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="401.8333333333335" x2="582.8333333333335" y1="791.489460784315" y2="791.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="401.8333333333335" x2="401.8333333333335" y1="767.239460784315" y2="791.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="582.8333333333335" x2="582.8333333333335" y1="767.239460784315" y2="791.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.8333333333335" x2="401.8333333333335" y1="791.489460784315" y2="791.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.8333333333335" x2="401.8333333333335" y1="815.739460784315" y2="815.739460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.8333333333335" x2="220.8333333333335" y1="791.489460784315" y2="815.739460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="401.8333333333335" x2="401.8333333333335" y1="791.489460784315" y2="815.739460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="401.8333333333335" x2="582.8333333333335" y1="791.489460784315" y2="791.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="401.8333333333335" x2="582.8333333333335" y1="815.739460784315" y2="815.739460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="401.8333333333335" x2="401.8333333333335" y1="791.489460784315" y2="815.739460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="582.8333333333335" x2="582.8333333333335" y1="791.489460784315" y2="815.739460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.8333333333335" x2="401.8333333333335" y1="815.739460784315" y2="815.739460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.8333333333335" x2="401.8333333333335" y1="838.489460784315" y2="838.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.8333333333335" x2="220.8333333333335" y1="815.739460784315" y2="838.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="401.8333333333335" x2="401.8333333333335" y1="815.739460784315" y2="838.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="401.8333333333335" x2="582.8333333333335" y1="815.739460784315" y2="815.739460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="401.8333333333335" x2="582.8333333333335" y1="838.489460784315" y2="838.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="401.8333333333335" x2="401.8333333333335" y1="815.739460784315" y2="838.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="582.8333333333335" x2="582.8333333333335" y1="815.739460784315" y2="838.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.8333333333335" x2="401.8333333333335" y1="838.489460784315" y2="838.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.8333333333335" x2="401.8333333333335" y1="861.239460784315" y2="861.239460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="220.8333333333335" x2="220.8333333333335" y1="838.489460784315" y2="861.239460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="401.8333333333335" x2="401.8333333333335" y1="838.489460784315" y2="861.239460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="401.8333333333335" x2="582.8333333333335" y1="838.489460784315" y2="838.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="401.8333333333335" x2="582.8333333333335" y1="861.239460784315" y2="861.239460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="401.8333333333335" x2="401.8333333333335" y1="838.489460784315" y2="861.239460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="582.8333333333335" x2="582.8333333333335" y1="838.489460784315" y2="861.239460784315"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,261.833,757.239) scale(1,1) translate(0,0)" writing-mode="lr" x="261.83" xml:space="preserve" y="761.74" zvalue="15">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,448.833,755.239) scale(1,1) translate(0,0)" writing-mode="lr" x="448.83" xml:space="preserve" y="759.74" zvalue="16">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,277.333,781.489) scale(1,1) translate(0,0)" writing-mode="lr" x="277.33" xml:space="preserve" y="785.99" zvalue="17">35kVⅠ段母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,461.833,780.239) scale(1,1) translate(0,0)" writing-mode="lr" x="461.83" xml:space="preserve" y="784.74" zvalue="18">35kVⅡ段母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,274.333,828.239) scale(1,1) translate(0,0)" writing-mode="lr" x="274.33" xml:space="preserve" y="832.74" zvalue="19">35kV#1主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,461.333,827.739) scale(1,1) translate(0,0)" writing-mode="lr" x="461.33" xml:space="preserve" y="832.24" zvalue="20">35kV#2主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,268.333,804.989) scale(1,1) translate(0,0)" writing-mode="lr" x="268.33" xml:space="preserve" y="809.49" zvalue="21">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,405.232,912.581) scale(1,1) translate(0,0)" writing-mode="lr" x="405.23" xml:space="preserve" y="917.08" zvalue="22">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="55" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,510.232,912.581) scale(1,1) translate(0,0)" writing-mode="lr" x="510.23" xml:space="preserve" y="917.08" zvalue="23">通道</text>
  <line fill="none" id="54" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="223.0833333333339" x2="591.0833333333335" y1="1191.109953398397" y2="1191.109953398397" zvalue="24"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="242.8333197732143" x2="318.2083197732143" y1="999.239460784315" y2="999.239460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="242.8333197732143" x2="318.2083197732143" y1="1034.120060784315" y2="1034.120060784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="242.8333197732143" x2="242.8333197732143" y1="999.239460784315" y2="1034.120060784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="318.2083197732143" y1="999.239460784315" y2="1034.120060784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="393.5833197732143" y1="999.239460784315" y2="999.239460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="393.5833197732143" y1="1034.120060784315" y2="1034.120060784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="318.2083197732143" y1="999.239460784315" y2="1034.120060784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5833197732143" x2="393.5833197732143" y1="999.239460784315" y2="1034.120060784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5829893622142" x2="468.9579893622142" y1="999.239460784315" y2="999.239460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5829893622142" x2="468.9579893622142" y1="1034.120060784315" y2="1034.120060784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5829893622142" x2="393.5829893622142" y1="999.239460784315" y2="1034.120060784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9579893622142" x2="468.9579893622142" y1="999.239460784315" y2="1034.120060784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9583197732143" x2="544.3333197732143" y1="999.239460784315" y2="999.239460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9583197732143" x2="544.3333197732143" y1="1034.120060784315" y2="1034.120060784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9583197732143" x2="468.9583197732143" y1="999.239460784315" y2="1034.120060784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="544.3333197732143" x2="544.3333197732143" y1="999.239460784315" y2="1034.120060784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="242.8333197732143" x2="318.2083197732143" y1="1034.120060784315" y2="1034.120060784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="242.8333197732143" x2="318.2083197732143" y1="1062.593960784315" y2="1062.593960784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="242.8333197732143" x2="242.8333197732143" y1="1034.120060784315" y2="1062.593960784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="318.2083197732143" y1="1034.120060784315" y2="1062.593960784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="393.5833197732143" y1="1034.120060784315" y2="1034.120060784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="393.5833197732143" y1="1062.593960784315" y2="1062.593960784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="318.2083197732143" y1="1034.120060784315" y2="1062.593960784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5833197732143" x2="393.5833197732143" y1="1034.120060784315" y2="1062.593960784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5829893622142" x2="468.9579893622142" y1="1034.120060784315" y2="1034.120060784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5829893622142" x2="468.9579893622142" y1="1062.593960784315" y2="1062.593960784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5829893622142" x2="393.5829893622142" y1="1034.120060784315" y2="1062.593960784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9579893622142" x2="468.9579893622142" y1="1034.120060784315" y2="1062.593960784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9583197732143" x2="544.3333197732143" y1="1034.120060784315" y2="1034.120060784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9583197732143" x2="544.3333197732143" y1="1062.593960784315" y2="1062.593960784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9583197732143" x2="468.9583197732143" y1="1034.120060784315" y2="1062.593960784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="544.3333197732143" x2="544.3333197732143" y1="1034.120060784315" y2="1062.593960784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="242.8333197732143" x2="318.2083197732143" y1="1062.593860784315" y2="1062.593860784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="242.8333197732143" x2="318.2083197732143" y1="1091.067760784315" y2="1091.067760784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="242.8333197732143" x2="242.8333197732143" y1="1062.593860784315" y2="1091.067760784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="318.2083197732143" y1="1062.593860784315" y2="1091.067760784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="393.5833197732143" y1="1062.593860784315" y2="1062.593860784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="393.5833197732143" y1="1091.067760784315" y2="1091.067760784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="318.2083197732143" y1="1062.593860784315" y2="1091.067760784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5833197732143" x2="393.5833197732143" y1="1062.593860784315" y2="1091.067760784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5829893622142" x2="468.9579893622142" y1="1062.593860784315" y2="1062.593860784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5829893622142" x2="468.9579893622142" y1="1091.067760784315" y2="1091.067760784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5829893622142" x2="393.5829893622142" y1="1062.593860784315" y2="1091.067760784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9579893622142" x2="468.9579893622142" y1="1062.593860784315" y2="1091.067760784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9583197732143" x2="544.3333197732143" y1="1062.593860784315" y2="1062.593860784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9583197732143" x2="544.3333197732143" y1="1091.067760784315" y2="1091.067760784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9583197732143" x2="468.9583197732143" y1="1062.593860784315" y2="1091.067760784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="544.3333197732143" x2="544.3333197732143" y1="1062.593860784315" y2="1091.067760784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="242.8333197732143" x2="318.2083197732143" y1="1091.067770784315" y2="1091.067770784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="242.8333197732143" x2="318.2083197732143" y1="1119.541670784315" y2="1119.541670784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="242.8333197732143" x2="242.8333197732143" y1="1091.067770784315" y2="1119.541670784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="318.2083197732143" y1="1091.067770784315" y2="1119.541670784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="393.5833197732143" y1="1091.067770784315" y2="1091.067770784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="393.5833197732143" y1="1119.541670784315" y2="1119.541670784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="318.2083197732143" y1="1091.067770784315" y2="1119.541670784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5833197732143" x2="393.5833197732143" y1="1091.067770784315" y2="1119.541670784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5829893622142" x2="468.9579893622142" y1="1091.067770784315" y2="1091.067770784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5829893622142" x2="468.9579893622142" y1="1119.541670784315" y2="1119.541670784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5829893622142" x2="393.5829893622142" y1="1091.067770784315" y2="1119.541670784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9579893622142" x2="468.9579893622142" y1="1091.067770784315" y2="1119.541670784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9583197732143" x2="544.3333197732143" y1="1091.067770784315" y2="1091.067770784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9583197732143" x2="544.3333197732143" y1="1119.541670784315" y2="1119.541670784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9583197732143" x2="468.9583197732143" y1="1091.067770784315" y2="1119.541670784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="544.3333197732143" x2="544.3333197732143" y1="1091.067770784315" y2="1119.541670784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="242.8333197732143" x2="318.2083197732143" y1="1119.541660784315" y2="1119.541660784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="242.8333197732143" x2="318.2083197732143" y1="1148.015560784315" y2="1148.015560784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="242.8333197732143" x2="242.8333197732143" y1="1119.541660784315" y2="1148.015560784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="318.2083197732143" y1="1119.541660784315" y2="1148.015560784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="393.5833197732143" y1="1119.541660784315" y2="1119.541660784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="393.5833197732143" y1="1148.015560784315" y2="1148.015560784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="318.2083197732143" y1="1119.541660784315" y2="1148.015560784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5833197732143" x2="393.5833197732143" y1="1119.541660784315" y2="1148.015560784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5829893622142" x2="468.9579893622142" y1="1119.541660784315" y2="1119.541660784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5829893622142" x2="468.9579893622142" y1="1148.015560784315" y2="1148.015560784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5829893622142" x2="393.5829893622142" y1="1119.541660784315" y2="1148.015560784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9579893622142" x2="468.9579893622142" y1="1119.541660784315" y2="1148.015560784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9583197732143" x2="544.3333197732143" y1="1119.541660784315" y2="1119.541660784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9583197732143" x2="544.3333197732143" y1="1148.015560784315" y2="1148.015560784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9583197732143" x2="468.9583197732143" y1="1119.541660784315" y2="1148.015560784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="544.3333197732143" x2="544.3333197732143" y1="1119.541660784315" y2="1148.015560784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="242.8333197732143" x2="318.2083197732143" y1="1148.015560784315" y2="1148.015560784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="242.8333197732143" x2="318.2083197732143" y1="1176.489460784315" y2="1176.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="242.8333197732143" x2="242.8333197732143" y1="1148.015560784315" y2="1176.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="318.2083197732143" y1="1148.015560784315" y2="1176.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="393.5833197732143" y1="1148.015560784315" y2="1148.015560784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="393.5833197732143" y1="1176.489460784315" y2="1176.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="318.2083197732143" x2="318.2083197732143" y1="1148.015560784315" y2="1176.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5833197732143" x2="393.5833197732143" y1="1148.015560784315" y2="1176.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5829893622142" x2="468.9579893622142" y1="1148.015560784315" y2="1148.015560784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5829893622142" x2="468.9579893622142" y1="1176.489460784315" y2="1176.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="393.5829893622142" x2="393.5829893622142" y1="1148.015560784315" y2="1176.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9579893622142" x2="468.9579893622142" y1="1148.015560784315" y2="1176.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9583197732143" x2="544.3333197732143" y1="1148.015560784315" y2="1148.015560784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9583197732143" x2="544.3333197732143" y1="1176.489460784315" y2="1176.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="468.9583197732143" x2="468.9583197732143" y1="1148.015560784315" y2="1176.489460784315"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="544.3333197732143" x2="544.3333197732143" y1="1148.015560784315" y2="1176.489460784315"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,275.833,1059.99) scale(1,1) translate(0,-2.32145e-13)" writing-mode="lr" x="275.8333333333335" xml:space="preserve" y="1064.489460784315" zvalue="26">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,275.833,1085.49) scale(1,1) translate(0,0)" writing-mode="lr" x="275.8333333333335" xml:space="preserve" y="1089.989460784315" zvalue="27">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,275.833,1110.99) scale(1,1) translate(0,0)" writing-mode="lr" x="275.8333333333335" xml:space="preserve" y="1115.489460784315" zvalue="28">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,275.833,1136.49) scale(1,1) translate(0,0)" writing-mode="lr" x="275.8333333333335" xml:space="preserve" y="1140.989460784315" zvalue="29">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,275.833,1161.99) scale(1,1) translate(0,0)" writing-mode="lr" x="275.8333333333335" xml:space="preserve" y="1166.489460784315" zvalue="30">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" x="353.84375" xml:space="preserve" y="1013.359375" zvalue="31">35kV     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="353.84375" xml:space="preserve" y="1030.359375" zvalue="31">Ⅰ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" x="433.5" xml:space="preserve" y="1013.359375" zvalue="32">35kV      </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="433.5" xml:space="preserve" y="1030.359375" zvalue="32">Ⅱ母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,290.083,1212.49) scale(1,1) translate(0,0)" writing-mode="lr" x="290.0833333333335" xml:space="preserve" y="1216.989460784315" zvalue="34">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,271.833,1534.24) scale(1,1) translate(0,0)" writing-mode="lr" x="271.83" xml:space="preserve" y="1540.24" zvalue="35">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,446.833,1530.24) scale(1,1) translate(0,0)" writing-mode="lr" x="446.83" xml:space="preserve" y="1536.24" zvalue="36">ChengZi-01-2020</text>
  <line fill="none" id="41" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="600.8333333333333" x2="600.8333333333333" y1="621.239460784315" y2="1611.239460784315" zvalue="37"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1033.79,883.884) scale(1,1) translate(0,0)" writing-mode="lr" x="1033.79" xml:space="preserve" y="888.38" zvalue="39">311</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1025.28,824.994) scale(1,1) translate(-2.24549e-13,0)" writing-mode="lr" x="1025.28" xml:space="preserve" y="829.49" zvalue="41">3116</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1025.41,948.289) scale(1,1) translate(0,0)" writing-mode="lr" x="1025.41" xml:space="preserve" y="952.79" zvalue="44">3111</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1133.73,1073.82) scale(1,1) translate(0,0)" writing-mode="lr" x="1133.73" xml:space="preserve" y="1078.32" zvalue="49">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1130.55,1023.7) scale(1,1) translate(0,0)" writing-mode="lr" x="1130.55" xml:space="preserve" y="1028.2" zvalue="51">3016</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1132.32,1269.99) scale(1,1) translate(0,0)" writing-mode="lr" x="1132.32" xml:space="preserve" y="1274.49" zvalue="53">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1126.83,1300.27) scale(1,1) translate(-4.94194e-13,0)" writing-mode="lr" x="1126.83" xml:space="preserve" y="1304.77" zvalue="55">0011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1176.61,1185.2) scale(1,1) translate(0,0)" writing-mode="lr" x="1176.61" xml:space="preserve" y="1189.7" zvalue="57">35kV#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1481.15,1395.99) scale(1,1) translate(0,0)" writing-mode="lr" x="1481.15" xml:space="preserve" y="1400.49" zvalue="63">011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1476.69,1449.65) scale(1,1) translate(0,0)" writing-mode="lr" x="1476.69" xml:space="preserve" y="1454.15" zvalue="66">0116</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1487.67,1351.13) scale(1,1) translate(0,0)" writing-mode="lr" x="1487.67" xml:space="preserve" y="1355.63" zvalue="68">0111</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1459.45,1529.93) scale(1,1) translate(0,0)" writing-mode="lr" x="1459.45" xml:space="preserve" y="1534.43" zvalue="71">10kVywx</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,274.333,852.739) scale(1,1) translate(0,0)" writing-mode="lr" x="274.33" xml:space="preserve" y="857.24" zvalue="73">35kV#1主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" x="506.84375" xml:space="preserve" y="1013.359375" zvalue="74">10kV     </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="506.84375" xml:space="preserve" y="1030.359375" zvalue="74">母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,480.068,972.239) scale(1,1) translate(0,-2.13216e-13)" writing-mode="lr" x="480.0683492024741" xml:space="preserve" y="976.739460784315" zvalue="78">小电流接地</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,277.119,893.778) scale(1,1) translate(0,-1.95994e-13)" writing-mode="lr" x="277.1190388997397" xml:space="preserve" y="898.2783249200572" zvalue="79">全站公用</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1085.92,1112.86) scale(1,1) translate(0,0)" writing-mode="lr" x="1085.92" xml:space="preserve" y="1117.36" zvalue="82">3011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1084.22,1232.84) scale(1,1) translate(0,0)" writing-mode="lr" x="1084.22" xml:space="preserve" y="1237.34" zvalue="85">0016</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1006,637.917) scale(1,1) translate(0,0)" writing-mode="lr" x="1006" xml:space="preserve" y="642.42" zvalue="89">38</text>
 </g>
 <g id="ButtonClass">
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="341.93" y="960.49" zvalue="75"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="240.02" y="960.49" zvalue="76"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="240.02" y="919.99" zvalue="77"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="383">
   <path class="kv35" d="M 749.33 985.33 L 1331.83 985.33" stroke-width="6" zvalue="2"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674265726981" ObjectName="35kVI段母线"/>
   </metadata>
  <path d="M 749.33 985.33 L 1331.83 985.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="382">
   <path class="kv10" d="M 801.91 1332.74 L 1886.25 1332.74" stroke-width="6" zvalue="4"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674265661445" ObjectName="10kVI母线"/>
   </metadata>
  <path d="M 801.91 1332.74 L 1886.25 1332.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BreakerClass">
  <g id="282">
   <use class="kv35" height="20" transform="rotate(0,1007.19,884.26) scale(1.5542,1.35421) translate(-356.374,-227.747)" width="10" x="999.4173678678985" xlink:href="#Breaker:开关_0" y="870.7174366367728" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924607016964" ObjectName="311"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1007.19,884.26) scale(1.5542,1.35421) translate(-356.374,-227.747)" width="10" x="999.4173678678985" y="870.7174366367728"/></g>
  <g id="281">
   <use class="kv35" height="20" transform="rotate(180,1107.33,1073.45) scale(1.5542,1.35421) translate(-392.083,-277.232)" width="10" x="1099.558924954966" xlink:href="#Breaker:开关_0" y="1059.906156996549" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924606951429" ObjectName="301"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1107.33,1073.45) scale(1.5542,1.35421) translate(-392.083,-277.232)" width="10" x="1099.558924954966" y="1059.906156996549"/></g>
  <g id="278">
   <use class="kv10" height="20" transform="rotate(180,1107.44,1266.45) scale(1.5542,1.35421) translate(-392.121,-327.713)" width="10" x="1099.665949827324" xlink:href="#Breaker:开关_0" y="1252.906156996548" zvalue="52"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924606885893" ObjectName="001"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1107.44,1266.45) scale(1.5542,1.35421) translate(-392.121,-327.713)" width="10" x="1099.665949827324" y="1252.906156996548"/></g>
  <g id="335">
   <use class="kv10" height="20" transform="rotate(180,1459.42,1394.45) scale(1.5542,1.35421) translate(-517.632,-361.193)" width="10" x="1451.650044056234" xlink:href="#Breaker:开关_0" y="1380.906156996548" zvalue="62"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924606820357" ObjectName="011"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1459.42,1394.45) scale(1.5542,1.35421) translate(-517.632,-361.193)" width="10" x="1451.650044056234" y="1380.906156996548"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="276">
   <use class="kv35" height="30" transform="rotate(0,1007.19,822.827) scale(-0.947693,0.6712) translate(-2070.37,398.145)" width="15" x="1000.08682622549" xlink:href="#Disconnector:刀闸_0" y="812.7592535051733" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450419032069" ObjectName="3116"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1007.19,822.827) scale(-0.947693,0.6712) translate(-2070.37,398.145)" width="15" x="1000.08682622549" y="812.7592535051733"/></g>
  <g id="272">
   <use class="kv35" height="30" transform="rotate(180,1007.38,947.596) scale(0.947693,-0.6712) translate(55.2089,-2364.32)" width="15" x="1000.267466759329" xlink:href="#Disconnector:刀闸_0" y="937.5279912920332" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450418966533" ObjectName="3111"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1007.38,947.596) scale(0.947693,-0.6712) translate(55.2089,-2364.32)" width="15" x="1000.267466759329" y="937.5279912920332"/></g>
  <g id="280">
   <use class="kv35" height="30" transform="rotate(0,1107.14,1025.61) scale(0.947693,-0.6712) translate(60.7155,-2558.57)" width="15" x="1100.035425886219" xlink:href="#Disconnector:刀闸_0" y="1015.543799583567" zvalue="50"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450418900997" ObjectName="3016"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1107.14,1025.61) scale(0.947693,-0.6712) translate(60.7155,-2558.57)" width="15" x="1100.035425886219" y="1015.543799583567"/></g>
  <g id="277">
   <use class="kv10" height="30" transform="rotate(180,1107.41,1301.13) scale(-0.947693,-0.6712) translate(-2276.33,-3244.58)" width="15" x="1100.297870694509" xlink:href="#Disconnector:刀闸_0" y="1291.062537196579" zvalue="54"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450418835461" ObjectName="0011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1107.41,1301.13) scale(-0.947693,-0.6712) translate(-2276.33,-3244.58)" width="15" x="1100.297870694509" y="1291.062537196579"/></g>
  <g id="334">
   <use class="kv10" height="30" transform="rotate(180,1458.91,1447.65) scale(-0.947693,-0.6712) translate(-2998.74,-3609.4)" width="15" x="1451.805774447229" xlink:href="#Disconnector:刀闸_0" y="1437.586346720389" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450418769925" ObjectName="0116"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1458.91,1447.65) scale(-0.947693,-0.6712) translate(-2998.74,-3609.4)" width="15" x="1451.805774447229" y="1437.586346720389"/></g>
  <g id="332">
   <use class="kv10" height="30" transform="rotate(180,1460.06,1340.13) scale(0.947693,0.6712) translate(80.1945,651.555)" width="15" x="1452.953258764361" xlink:href="#Disconnector:刀闸_0" y="1330.062537196579" zvalue="67"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450418704389" ObjectName="0111"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1460.06,1340.13) scale(0.947693,0.6712) translate(80.1945,651.555)" width="15" x="1452.953258764361" y="1330.062537196579"/></g>
  <g id="394">
   <use class="kv35" height="30" transform="rotate(0,1107.33,1113.86) scale(0.588235,0.431373) translate(772.043,1459.74)" width="15" x="1102.918330131696" xlink:href="#Disconnector:刀闸_0" y="1107.387254901961" zvalue="80"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450418573317" ObjectName="3011"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1107.33,1113.86) scale(0.588235,0.431373) translate(772.043,1459.74)" width="15" x="1102.918330131696" y="1107.387254901961"/></g>
  <g id="398">
   <use class="kv10" height="30" transform="rotate(0,1107.3,1233.84) scale(0.588235,0.431373) translate(772.02,1617.9)" width="15" x="1102.885589554448" xlink:href="#Disconnector:刀闸_0" y="1227.372476391609" zvalue="84"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450418507781" ObjectName="0016"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1107.3,1233.84) scale(0.588235,0.431373) translate(772.02,1617.9)" width="15" x="1102.885589554448" y="1227.372476391609"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="40">
   <path class="kv35" d="M 1007.14 871.3 L 1007.14 832.72" stroke-width="1" zvalue="42"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="282@0" LinkObjectIDznd="276@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1007.14 871.3 L 1007.14 832.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv35" d="M 1007.29 897.19 L 1007.29 937.86" stroke-width="1" zvalue="45"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="282@1" LinkObjectIDznd="272@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1007.29 897.19 L 1007.29 937.86" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv35" d="M 1006 729.42 L 1006 813.09" stroke-width="1" zvalue="46"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="401@0" LinkObjectIDznd="276@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1006 729.42 L 1006 813.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv35" d="M 1007.32 957.49 L 1007.32 985.33" stroke-width="1" zvalue="47"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@1" LinkObjectIDznd="383@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1007.32 957.49 L 1007.32 985.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv35" d="M 1107.23 1060.52 L 1107.23 1035.35" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="281@1" LinkObjectIDznd="280@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1107.23 1060.52 L 1107.23 1035.35" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="35">
   <path class="kv35" d="M 1107.2 1015.72 L 1107.2 985.33" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@1" LinkObjectIDznd="383@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1107.2 1015.72 L 1107.2 985.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv10" d="M 1107.49 1291.4 L 1107.49 1279.4" stroke-width="1" zvalue="60"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="277@0" LinkObjectIDznd="278@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1107.49 1291.4 L 1107.49 1279.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="kv10" d="M 1107.46 1311.03 L 1107.46 1332.74" stroke-width="1" zvalue="61"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="277@1" LinkObjectIDznd="382@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1107.46 1311.03 L 1107.46 1332.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="kv10" d="M 1459 1437.92 L 1459 1407.4" stroke-width="1" zvalue="65"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="334@0" LinkObjectIDznd="335@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1459 1437.92 L 1459 1407.4" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="kv10" d="M 1459.32 1381.52 L 1459.32 1349.87" stroke-width="1" zvalue="69"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="335@1" LinkObjectIDznd="332@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1459.32 1381.52 L 1459.32 1349.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv10" d="M 1459.45 1501.02 L 1459.45 1457.55" stroke-width="1" zvalue="72"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="328@0" LinkObjectIDznd="334@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1459.45 1501.02 L 1459.45 1457.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="22">
   <path class="kv35" d="M 1107.38 1086.4 L 1107.38 1107.6" stroke-width="1" zvalue="81"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="281@0" LinkObjectIDznd="394@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1107.38 1086.4 L 1107.38 1107.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv35" d="M 1107.37 1120.22 L 1107.38 1157.84" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="394@1" LinkObjectIDznd="275@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1107.37 1120.22 L 1107.38 1157.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv10" d="M 1107.33 1253.52 L 1107.33 1240.2" stroke-width="1" zvalue="86"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="278@1" LinkObjectIDznd="398@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1107.33 1253.52 L 1107.33 1240.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv10" d="M 1107.35 1227.59 L 1107.33 1223.59" stroke-width="1" zvalue="87"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="398@0" LinkObjectIDznd="275@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1107.35 1227.59 L 1107.33 1223.59" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv10" d="M 1460 1330.23 L 1460 1332.74" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="332@1" LinkObjectIDznd="382@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1460 1330.23 L 1460 1332.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="275">
   <g id="2750">
    <use class="kv35" height="60" transform="rotate(0,1107.33,1190.65) scale(1.225,1.11364) translate(-198.888,-118.086)" width="40" x="1082.83" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="1157.24" zvalue="56"/>
    <metadata>
     <cge:PSR_Ref ObjectID="null" ObjectName="35"/>
    </metadata>
   </g>
   <g id="2751">
    <use class="kv10" height="60" transform="rotate(0,1107.33,1190.65) scale(1.225,1.11364) translate(-198.888,-118.086)" width="40" x="1082.83" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="1157.24" zvalue="56"/>
    <metadata>
     <cge:PSR_Ref ObjectID="null" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399466024963" ObjectName="35kV#1主变"/>
   </metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1107.33,1190.65) scale(1.225,1.11364) translate(-198.888,-118.086)" width="40" x="1082.83" y="1157.24"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="328">
   <use class="kv10" height="30" transform="rotate(0,1459.45,1508.08) scale(1.15688,-0.522926) translate(-196.972,-4399.17)" width="12" x="1452.506326991989" xlink:href="#EnergyConsumer:负荷_0" y="1500.239450722709" zvalue="70"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450418638853" ObjectName="10kVywx"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1459.45,1508.08) scale(1.15688,-0.522926) translate(-196.972,-4399.17)" width="12" x="1452.506326991989" y="1500.239450722709"/></g>
  <g id="401">
   <use class="kv35" height="30" transform="rotate(0,1006,710.917) scale(1.38889,1.37037) translate(-279.347,-186.584)" width="12" x="997.6668929768682" xlink:href="#EnergyConsumer:负荷_0" y="690.3611111111111" zvalue="88"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450418442245" ObjectName="38"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1006,710.917) scale(1.38889,1.37037) translate(-279.347,-186.584)" width="12" x="997.6668929768682" y="690.3611111111111"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="73" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1150.29,745.258) scale(1,1) translate(0,0)" writing-mode="lr" x="1107.16" xml:space="preserve" y="749.62" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="P"/>
   </metadata>
  </g>
  <g id="74">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="74" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1143.14,795.115) scale(1,1) translate(0,0)" writing-mode="lr" x="1100.02" xml:space="preserve" y="799.48" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="75">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="75" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1161.83,675.774) scale(1,1) translate(0,0)" writing-mode="lr" x="1108.65" xml:space="preserve" y="681.63" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
 </g>
</svg>