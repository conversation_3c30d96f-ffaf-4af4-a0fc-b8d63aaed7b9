<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549595930626" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="4.416666666666668" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.249999999999998" y2="20.16666666666667"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
  <symbol id="Accessory:五绕组带地刀PT_0" viewBox="0,0,40,30">
   <use terminal-index="0" type="0" x="13.02572331551165" xlink:href="#terminal" y="7.84945819018008"/>
   <path d="M 13.25 14 L 13.25 8 L 33.8333 8 L 33.8333 17.8333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29" x2="38" y1="17.75" y2="17.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33" x2="35" y1="22" y2="22"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.08333333333334" x2="37.08333333333334" y1="19.83333333333334" y2="19.83333333333334"/>
   <ellipse cx="17.23" cy="24.49" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.14" cy="18.41" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.89" cy="18.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.98" cy="23.99" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.56" cy="18.33" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:接地变接地设备_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.75"/>
   <rect fill-opacity="0" height="13.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.94,7.54) scale(1,1) translate(0,0)" width="6.08" x="2.9" y="0.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.2499999999999956" x2="11.58333333333334" y1="20.49453511141348" y2="20.49453511141348"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.833333333333332" x2="10" y1="22.90451817731685" y2="22.90451817731685"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6" x2="6" y1="20.66666666666667" y2="14.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.934588934424228" x2="7.898744398909104" y1="25.38116790988687" y2="25.38116790988687"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id=":农业光伏线路_0" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="22.5" xlink:href="#terminal" y="44.60000000000001"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="15" y1="34.75" y2="34.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="14.5" y1="24.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="27.41094883543335" x2="34" y1="11.67541949757221" y2="11.67541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11" x2="16" y1="33.75" y2="33.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="13.5" y1="30.75" y2="33.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="14" y1="35.75" y2="35.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="34.00545055364641" x2="34.00545055364641" y1="11.79138864447597" y2="17.91666666666666"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.54,23.67) scale(1,1) translate(0,0)" width="6.08" x="10.5" y="16.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="24.96058610156545" x2="22.65436235204273" y1="11.67541949757221" y2="11.67541949757221"/>
   <path d="M 22.5 11.75 L 13.5 11.75 L 13.5 23.75 L 13.5 23.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="12.5" y1="24.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.66022336769755" x2="22.66022336769755" y1="44.33333333333334" y2="0.2499999999999964"/>
   <ellipse cx="34.01" cy="22.57" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="25.07589728904158" x2="25.07589728904158" y1="11.74758812251703" y2="11.74758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="24.99256395570825" x2="24.99256395570825" y1="9.083333333333334" y2="14.18417232847779"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="27.23798205421914" x2="27.23798205421914" y1="9.233252937417257" y2="14.33409193256171"/>
   <ellipse cx="37.85" cy="27.32" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="30.28" cy="27.36" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="34.01" cy="32.16" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:主变中性点接地刀_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="22.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2052.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="PowerTransformer3:可调Y-Y-Y带4卷_0" viewBox="0,0,30,30">
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.555983291032005" x2="9.555983291032005" y1="5.120556207052585" y2="8.655876939634236"/>
   <use terminal-index="2" type="1" x="9.34927333821094" xlink:href="#terminal" y="2.75"/>
   <use terminal-index="0" type="2" x="9.59927333821094" xlink:href="#terminal" y="8.75"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.538752725964429" x2="7.014608591541853" y1="8.622327175528927" y2="11.23035066677768"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.538752725964429" x2="12.11898895226306" y1="8.622327175528927" y2="11.23035066677768"/>
   <line fill="none" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.727392527623852" x2="16.3934244186439" y1="14.79705508449664" y2="3.228989310747176"/>
   <ellipse cx="9.44" cy="9.380000000000001" fill-opacity="0" rx="6.88" ry="6.88" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 18.0044 2.21739 L 17.085 4.11196 L 15.874 2.69224 L 15.9529 2.60012 z" fill="rgb(255,0,0)" fill-opacity="1" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer3:可调Y-Y-Y带4卷_1" viewBox="0,0,30,30">
   <use terminal-index="3" type="1" x="9.34927333821094" xlink:href="#terminal" y="27.66238646974517"/>
   <use terminal-index="1" type="2" x="9.34927333821094" xlink:href="#terminal" y="21.2457198030785"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.34927333821094" x2="6.84927333821094" y1="21.1068309141896" y2="23.6068309141896"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.349273338210944" x2="11.9048288937665" y1="21.1068309141896" y2="23.6068309141896"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.34927333821094" x2="9.34927333821094" y1="17.71794202530072" y2="21.10683091418962"/>
   <ellipse cx="9.32" cy="20.76" fill-opacity="0" rx="6.97" ry="6.97" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer3:可调Y-Y-Y带4卷_2" viewBox="0,0,30,30">
   <use terminal-index="4" type="1" x="28" xlink:href="#terminal" y="8.75"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.2540913359118" x2="23.91557109687444" y1="8.815507264119384" y2="11.50657475917485"/>
   <use terminal-index="5" type="2" x="21.25" xlink:href="#terminal" y="8.75"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.25409133591179" x2="18.65046983062225" y1="8.815507264119384" y2="11.50657475917485"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.25409133591179" x2="21.25409133591179" y1="5.167615770821973" y2="8.81550726411939"/>
   <ellipse cx="21" cy="9.220000000000001" fill-opacity="0" rx="7.08" ry="6.89" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_0" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.416666666666625" x2="14.8889734851558" y1="30.74450652239035" y2="19.51308960898588"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_1" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.8889734851558" x2="14.8889734851558" y1="32.25" y2="19.51308960898588"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="GroundDisconnector:12547_2" viewBox="0,0,45,45">
   <use terminal-index="0" type="0" x="14.5" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="19.88620689655174" x2="9.700000000000015" y1="19.49501474926254" y2="32.31404129793511"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="9.699999999999994" x2="20.24999999999999" y1="19.33333333333333" y2="32.38333333333333"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.77607149197439" x2="33.45507703101916" y1="38.59219204932567" y2="38.59219204932567"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.53290622076525" x2="33.53290622076525" y1="22.06726518291196" y2="38.52333818738227"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.96414179204035" x2="14.96414179204035" y1="19.56938379593636" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="33.72817975850747" x2="33.72817975850747" y1="18.16909267494318" y2="8.835942485322821"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.84076764320101" x2="14.84076764320101" y1="32.44927516103209" y2="38.6666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.85395107780631" x2="12.1757277082614" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="36.61798904427344" x2="30.93976567472853" y1="8.708197977045206" y2="8.708197977045206"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6532657596445" x2="12.86778351328125" y1="7.44637056159085" y2="7.44637056159085"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="35.41730372611165" x2="31.6318214797484" y1="7.446370561590845" y2="7.446370561590845"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.00950311944433" x2="14.2744904231945" y1="6.499999999999968" y2="6.499999999999968"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.77354108591146" x2="33.03852838966164" y1="6.499999999999968" y2="6.499999999999968"/>
   <rect fill-opacity="0" height="14.22" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,33.51,25.24) scale(-1,1) translate(-2360.48,0)" width="8.140000000000001" x="29.44" y="18.13"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.45029550323286" x2="19.13605385634846" y1="32.46251758535202" y2="32.46251758535202"/>
  </symbol>
  <symbol id="Disconnector:SVG隔离刀闸_0" viewBox="0,0,24,24">
   <use terminal-index="0" type="0" x="12" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="12" xlink:href="#terminal" y="23.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,12.5) scale(1,1) translate(0,0)" width="3" x="4.5" y="9"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="21" y2="23.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="21" y2="16"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.867489181242" x2="17.867489181242" y1="20" y2="21"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="9.083333333333336" y2="4.083333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="0.6500000000000004" y2="4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="6" y1="4" y2="4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.867489181242" x2="17.867489181242" y1="19.91666666666666" y2="17.5142916052417"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="13.66666666666667" x2="17.83141025641025" y1="7.666666666666666" y2="17.5142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.16666666666667" x2="19.5" y1="7.54249548976499" y2="7.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.867489181242" x2="17.867489181242" y1="7.500000000000004" y2="3.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91666666666666" x2="5.916666666666666" y1="20.91666666666666" y2="20.91666666666666"/>
  </symbol>
  <symbol id="Disconnector:SVG隔离刀闸_1" viewBox="0,0,24,24">
   <use terminal-index="0" type="0" x="12" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="12" xlink:href="#terminal" y="23.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="20.75" y2="23.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.867489181242" x2="17.867489181242" y1="19.91666666666666" y2="17.5142916052417"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.867489181242" x2="17.867489181242" y1="20" y2="21"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="6" y1="4" y2="4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="0.6500000000000004" y2="4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.867489181242" x2="17.867489181242" y1="7.416666666666666" y2="17.41666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.867489181242" x2="17.867489181242" y1="7.500000000000004" y2="3.916666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.16666666666667" x2="19.5" y1="7.54249548976499" y2="7.54249548976499"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91666666666666" x2="5.916666666666666" y1="20.91666666666666" y2="20.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="21" y2="16"/>
   <rect fill-opacity="0" height="7" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,12.5) scale(1,1) translate(0,0)" width="3" x="4.5" y="9"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="9.083333333333336" y2="4.083333333333334"/>
  </symbol>
  <symbol id="Disconnector:SVG隔离刀闸_2" viewBox="0,0,24,24">
   <use terminal-index="0" type="0" x="12" xlink:href="#terminal" y="0.5"/>
   <use terminal-index="1" type="0" x="12" xlink:href="#terminal" y="23.5"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="20.75" y2="23.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.867489181242" x2="17.867489181242" y1="19.91666666666666" y2="17.5142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="6" y1="4" y2="4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.867489181242" x2="17.867489181242" y1="20" y2="21"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="12" y1="0.6500000000000004" y2="4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="20.33333333333334" x2="15.33141025641026" y1="7.916666666666666" y2="17.58333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="15.33141025641025" x2="20.33333333333333" y1="7.916666666666666" y2="17.58333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.867489181242" x2="17.867489181242" y1="7.500000000000004" y2="3.916666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.16666666666667" x2="19.5" y1="7.54249548976499" y2="7.54249548976499"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91666666666666" x2="5.916666666666666" y1="20.91666666666666" y2="20.91666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="21" y2="16"/>
   <rect fill-opacity="0" height="7" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,12.5) scale(1,1) translate(0,0)" width="3" x="4.5" y="9"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="9.083333333333336" y2="4.083333333333334"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变D-Y_0" viewBox="0,0,23,39">
   <use terminal-index="0" type="0" x="7.666666666666666" xlink:href="#terminal" y="0.4166666666666714"/>
   <path d="M 7.5 4.25 L 4.58333 10.1667 L 10.8333 10.1667 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.42034 32.6694 L 7.64848 37.3333 L 7.05673 32.6694 L 7.72567 32.7212 z" fill="rgb(170,170,127)" fill-opacity="1" stroke="rgb(170,170,127)" stroke-dasharray="6 2 2 2" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 6.20768 29.4824 L 9.29511 29.4824 L 7.7514 31.037 L 6.20768 29.4824" fill="none" stroke="rgb(170,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.597025948103791" x2="7.597025948103791" y1="2.04293756914176" y2="0.4882945839350796"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.597025948103783" x2="19.17487025948103" y1="29.63785055656028" y2="21.32051058570456"/>
   <ellipse cx="7.42" cy="8.6" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="7.5" cy="17.92" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.611718118722353" x2="7.611718118722353" y1="15.85874008086165" y2="18.45772818087859"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.2052656081938" x2="7.61171811872235" y1="21.05671628089551" y2="18.45772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.018170629250884" x2="7.611718118722337" y1="21.05671628089551" y2="18.45772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.41666666666666" x2="15.93279794298801" y1="26.63147854022231" y2="26.63147854022231"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.11989292193093" x2="17.22957168772375" y1="27.93097259023087" y2="27.93097259023087"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8231191771952" x2="18.52634543245948" y1="29.23046664023932" y2="29.23046664023932"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.611718118722351" x2="19.17487025948103" y1="18.45772818087858" y2="18.45772818087858"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.17473230482734" x2="19.17473230482734" y1="18.49624249591244" y2="26.39901100404639"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.756205920707614" x2="7.756205920707614" y1="32.74713652697363" y2="24.41980688231738"/>
  </symbol>
  <symbol id="Accessory:中间电缆_0" viewBox="0,0,8,21">
   <use terminal-index="0" type="0" x="4" xlink:href="#terminal" y="10.5"/>
   <path d="M 1.08333 0.5 L 7 0.5 L 4 7.13889 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4" x2="4" y1="20.83333333333333" y2="0.5000000000000036"/>
   <path d="M 1.08333 20.6389 L 7 20.6389 L 4 14 z" fill-opacity="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:5卷PT带容断器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="5.47912519145992" xlink:href="#terminal" y="41.37373692455962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.86245852479332" x2="27.86245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="36"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,23.86,15.09) scale(1,1) translate(0,0)" width="11" x="18.36" y="12.59"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18" x2="13" y1="35" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="16.5" y1="35" y2="35"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.8624585247933" x2="19.8624585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <path d="M 16.75 6.75 L 23.75 6.75 L 23.75 9.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.86245852479333" x2="27.86245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <rect fill-opacity="0" height="11" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.54,25.5) scale(1,1) translate(0,0)" width="4.58" x="3.25" y="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.89772110866599" x2="23.89772110866599" y1="20.41666666666667" y2="23.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.96438777533266" x2="21.96438777533266" y1="24.15816200815096" y2="24.15816200815096"/>
   <ellipse cx="5.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.0108382776216" x2="21.73402243293775" y1="23.92008155192961" y2="23.92008155192961"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.26083827762157" x2="22.6506890996044" y1="25.17008155192959" y2="25.17008155192959"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="24.76083827762159" x2="23.31735576627107" y1="26.67008155192962" y2="26.67008155192962"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.08713830216066" x2="14.31883560169719" y1="12.79040359122621" y2="12.79040359122621"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.39410730945214" x2="14.5877735452272" y1="15.40299989806297" y2="12.78208583485582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.01457106407813" x2="10.82090482830307" y1="15.30654513693459" y2="12.68563107372743"/>
   <ellipse cx="5.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="13.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.54" cy="6.88" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.692203825592705" x2="8.692203825592705" y1="14.32076893362116" y2="14.32076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.942203825592701" x2="7.942203825592701" y1="8.070768933621144" y2="8.070768933621144"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="14.82581493230132" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.37303257583199" x2="11.87063985073817" y1="6.726117411622521" y2="4.07298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.42264294445647" x2="12.37303257583197" y1="8.249928796703951" y2="6.726117411622536"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.442203825592705" x2="8.442203825592705" y1="7.570768933621149" y2="7.570768933621149"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831976" x2="7.575814932301304" y1="13.97611741162255" y2="15.10543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456463" x2="5.123032575831967" y1="15.49992879670398" y2="13.97611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831989" x2="4.620639850738163" y1="13.97611741162255" y2="11.32298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.172642944456459" x2="5.123032575831962" y1="8.249928796703964" y2="6.726117411622548"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831981" x2="4.620639850738158" y1="6.726117411622528" y2="4.072985910425693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.123032575831987" x2="7.575814932301315" y1="6.726117411622543" y2="7.855437527737958"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.507700305101775" x2="5.507700305101775" y1="17.83333333333333" y2="41.49610160569022"/>
   <rect fill-opacity="0" height="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-180,17.7,35.01) scale(-1,1) translate(-2027.83,0)" width="11" x="12.2" y="32.51"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23.2207229126482" x2="26.7207229126482" y1="34.70975002257848" y2="34.70975002257848"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.96221825413249" x2="26.96221825413249" y1="36.64308335591183" y2="36.64308335591183"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.72413779791114" x2="26.72413779791114" y1="32.59663285362289" y2="36.87344869830673"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.97413779791113" x2="27.97413779791113" y1="33.34663285362291" y2="35.95678203164009"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="29.47413779791115" x2="29.47413779791115" y1="33.8466328536229" y2="35.29011536497342"/>
   <ellipse cx="8.710000000000001" cy="0.47" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.10887049225937" x2="11.10887049225937" y1="1.654102266954478" y2="1.654102266954478"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.60887049225937" x2="11.60887049225937" y1="1.154102266954478" y2="1.154102266954478"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.289699242498646" x2="7.787306517404822" y1="0.309450744955857" y2="-2.343680756240978"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.289699242498656" x2="10.74248159896798" y1="0.3094507449558748" y2="1.438770861071294"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.339309611123124" x2="8.289699242498626" y1="1.833262130037291" y2="0.3094507449558748"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.750000000000004" y2="4.166666666666668"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="32"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="1" y1="27" y2="11"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="27" y2="9.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.916666666666667" x2="6.916666666666667" y1="9.666666666666661" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35.16666666666667"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="9" y1="9.75" y2="9.75"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="ACLineSegment:农业光伏_0" viewBox="0,0,41,45">
   <use terminal-index="0" type="0" x="20.45" xlink:href="#terminal" y="44.5"/>
   <path d="M 20.25 12.25 L 11.25 12.25 L 11.25 24.25 L 11.25 24.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="25.16094883543335" x2="31.75" y1="12.17541949757221" y2="12.17541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.25" x2="10.25" y1="25" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.75" x2="12.75" y1="35.25" y2="35.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.75" x2="13.75" y1="34.25" y2="34.25"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.29,24.17) scale(1,1) translate(0,0)" width="6.08" x="8.25" y="17"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.71058610156545" x2="20.40436235204273" y1="12.17541949757221" y2="12.17541949757221"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.75545055364641" x2="31.75545055364641" y1="12.29138864447597" y2="18.41666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.25" x2="12.25" y1="25" y2="19"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.25" x2="11.25" y1="31.25" y2="34.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="20.41022336769755" x2="20.41022336769755" y1="44.83333333333334" y2="0.7499999999999964"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.75" x2="11.75" y1="36.25" y2="36.25"/>
   <ellipse cx="31.76" cy="23.07" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.82589728904158" x2="22.82589728904158" y1="12.24758812251703" y2="12.24758812251703"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="22.74256395570825" x2="22.74256395570825" y1="9.583333333333334" y2="14.68417232847779"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="24.98798205421914" x2="24.98798205421914" y1="9.733252937417257" y2="14.83409193256171"/>
   <ellipse cx="28.03" cy="27.86" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="35.6" cy="27.82" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="31.76" cy="32.66" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Compensator:SVG20230208_0" viewBox="0,0,25,35">
   <use terminal-index="0" type="0" x="12.5" xlink:href="#terminal" y="0.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="8.583333333333332" y2="0.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="16.33333333333333" y1="17.41666666666667" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.66666666666667" x2="11.66666666666667" y1="18.41666666666666" y2="23.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.33333333333333" x2="16.33333333333333" y1="17.25" y2="24.25"/>
   <path d="M 20 15.9167 L 23 15.9167 L 23 19.9167" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 11.75 19.4167 L 12.75 18.4167 L 12.75 16.4167" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="9.583333333333332" y1="8.5" y2="8.5"/>
   <rect fill-opacity="0" height="13.25" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,12.58,20.88) scale(1,1) translate(0,0)" width="14.83" x="5.17" y="14.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="11.5" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.66666666666667" x2="10.66666666666667" y1="19.41666666666667" y2="22.41666666666667"/>
   <path d="M 11.5833 22 L 12.5833 23 L 12.5833 25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 6.58333 32.5 L 6.58333 34.5 L 18.5833 34.5 L 18.5833 32.5" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.58333333333333" x2="12.58333333333333" y1="34.5" y2="27.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.25" x2="12.58333333333333" y1="24.25" y2="24.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15.33333333333333" x2="17.33333333333333" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.44166666666667" x2="24.525" y1="21.19166666666666" y2="21.19166666666666"/>
   <path d="M 16.3333 19.25 L 15.3333 22.25 L 17.3333 22.25 L 16.3333 19.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.58333333333333" x2="8.583333333333332" y1="21" y2="21"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.44166666666667" x2="24.525" y1="19.85833333333333" y2="19.85833333333333"/>
   <path d="M 20 25.1667 L 23 25.1667 L 23 21.1667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 9.50026 8.46947 A 3.01038 3.17474 -450 1 1 12.675 11.4799" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":光伏发电2_0" viewBox="0,0,10,30">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="29.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="5.000411522633746" x2="5.000411522633746" y1="29.91666666666667" y2="1.116666666666665"/>
   <path d="M 4.91667 14.5224 L 3.5 7.60569 L 5 11.1057 L 6.5 7.43903 L 4.95238 14.739" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="盈江县农业光伏电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="296.73" x="45.71" xlink:href="logo.png" y="33.57"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="297" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,194.079,63.5714) scale(1,1) translate(-2.03012e-14,0)" writing-mode="lr" x="194.08" xml:space="preserve" y="67.06999999999999" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,203.079,63.2618) scale(1,1) translate(-2.82948e-14,0)" writing-mode="lr" x="203.08" xml:space="preserve" y="72.26000000000001" zvalue="5">盈江县农业光伏电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="69" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="732"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="732">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,820.52,254.544) scale(1,1) translate(0,0)" writing-mode="lr" x="820.52" xml:space="preserve" y="259.04" zvalue="3">220kVⅠ母</text>
  <line fill="none" id="295" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="379.7142857142858" x2="379.7142857142858" y1="13.57142857142856" y2="1043.571428571428" zvalue="6"/>
  <line fill="none" id="293" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.714285714286234" x2="372.7142857142858" y1="149.441921185511" y2="149.441921185511" zvalue="8"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(180,1104.73,307.558) scale(-1,-1) translate(-2209.46,-615.116)" writing-mode="lr" x="1104.73" xml:space="preserve" y="312.06" zvalue="277">10</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="191.5" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="10.5" x2="10.5" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="372.5" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="191.5" x2="191.5" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="372.5" x2="372.5" y1="390.25" y2="413"/>
  <line fill="none" id="119" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="708"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="710">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="711">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="100" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="712">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="99" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="713">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="714">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="716">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="95" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="717">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="94" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="718">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,64.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="22" xml:space="preserve" y="266.5" zvalue="722">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="723">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,62.6875,335.25) scale(1,1) translate(0,0)" writing-mode="lr" x="62.69" xml:space="preserve" y="339.75" zvalue="724">220kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="733">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="734">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,357.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="361.75" zvalue="737">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,309.75) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="314.25" zvalue="741">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="743">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,149,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="149" xml:space="preserve" y="1022" zvalue="747">李艳</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,200,955) scale(1,1) translate(0,0)" writing-mode="lr" x="200" xml:space="preserve" y="961" zvalue="751">中调YYKBH 22-099</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="132" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1020.35,337.958) scale(1,1) translate(0,0)" writing-mode="lr" x="1020.35" xml:space="preserve" y="342.46" zvalue="786">2901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="134" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1061.44,440.833) scale(1,1) translate(0,0)" writing-mode="lr" x="1061.44" xml:space="preserve" y="445.33" zvalue="787">220kVⅠ母电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="124" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1203.41,195.016) scale(1,1) translate(0,0)" writing-mode="lr" x="1203.41" xml:space="preserve" y="199.52" zvalue="818">252</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="123" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1199.43,246.498) scale(1,1) translate(0,0)" writing-mode="lr" x="1199.43" xml:space="preserve" y="251" zvalue="819">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1202.92,140.423) scale(1,1) translate(0,0)" writing-mode="lr" x="1202.92" xml:space="preserve" y="144.92" zvalue="820">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1174,41.3492) scale(1,1) translate(0,0)" writing-mode="lr" x="1174" xml:space="preserve" y="45.85" zvalue="821">220kV农盈线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="107" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1112.11,117.914) scale(1,1) translate(0,0)" writing-mode="lr" x="1112.11" xml:space="preserve" y="122.41" zvalue="825">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="139" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1335.17,299.873) scale(1,1) translate(0,0)" writing-mode="lr" x="1335.17" xml:space="preserve" y="304.37" zvalue="838">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="157" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1350.15,356.266) scale(1,1) translate(1.48572e-13,0)" writing-mode="lr" x="1350.15" xml:space="preserve" y="360.77" zvalue="843">201</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="146" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1338.26,418.798) scale(1,1) translate(0,0)" writing-mode="lr" x="1338.26" xml:space="preserve" y="423.3" zvalue="848">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="156" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1271.08,446.789) scale(1,1) translate(0,0)" writing-mode="lr" x="1271.08" xml:space="preserve" y="451.29" zvalue="853">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="152" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1288.37,576.451) scale(1,1) translate(0,0)" writing-mode="lr" x="1288.37" xml:space="preserve" y="580.95" zvalue="854">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="327" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1188.69,536.046) scale(1,1) translate(3.79925e-13,0)" writing-mode="lr" x="1188.69" xml:space="preserve" y="540.55" zvalue="859">35kV接地电阻柜</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="162" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1427.21,493.198) scale(1,1) translate(0,0)" writing-mode="lr" x="1427.21" xml:space="preserve" y="497.7" zvalue="862">2010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="166" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1424.73,570.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1424.73" xml:space="preserve" y="574.9400000000001" zvalue="867">1010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1398.51,601.524) scale(1,1) translate(0,0)" writing-mode="lr" x="1398.51" xml:space="preserve" y="606.02" zvalue="875">至110kV设备</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,457.833,665.571) scale(1,1) translate(3.27442e-13,0)" writing-mode="lr" x="457.83" xml:space="preserve" y="670.0700000000001" zvalue="884">35kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1636.73,666.39) scale(1,1) translate(0,0)" writing-mode="lr" x="1636.73" xml:space="preserve" y="670.89" zvalue="886">35kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1113.11,169.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1113.11" xml:space="preserve" y="173.72" zvalue="904">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1110.11,229.222) scale(1,1) translate(0,0)" writing-mode="lr" x="1110.11" xml:space="preserve" y="233.72" zvalue="908">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,940.409,195.016) scale(1,1) translate(0,0)" writing-mode="lr" x="940.41" xml:space="preserve" y="199.52" zvalue="911">252</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,936.427,246.498) scale(1,1) translate(0,0)" writing-mode="lr" x="936.4299999999999" xml:space="preserve" y="251" zvalue="914">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,939.924,140.423) scale(1,1) translate(0,0)" writing-mode="lr" x="939.92" xml:space="preserve" y="144.92" zvalue="916">6</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,910.998,41.3492) scale(1,1) translate(0,0)" writing-mode="lr" x="911" xml:space="preserve" y="45.85" zvalue="917">220kV备用线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,849.111,117.914) scale(1,1) translate(0,0)" writing-mode="lr" x="849.11" xml:space="preserve" y="122.41" zvalue="919">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,850.111,169.222) scale(1,1) translate(0,0)" writing-mode="lr" x="850.11" xml:space="preserve" y="173.72" zvalue="925">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,847.111,229.222) scale(1,1) translate(0,0)" writing-mode="lr" x="847.11" xml:space="preserve" y="233.72" zvalue="930">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1103.73,368) scale(1,1) translate(0,0)" writing-mode="lr" x="1103.73" xml:space="preserve" y="372.5" zvalue="933">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1268.46,329.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1268.46" xml:space="preserve" y="334.06" zvalue="936">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1272.9,392.444) scale(1,1) translate(0,0)" writing-mode="lr" x="1272.9" xml:space="preserve" y="396.94" zvalue="938">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1068.17,623.56) scale(1,1) translate(0,0)" writing-mode="lr" x="1068.17" xml:space="preserve" y="628.0599999999999" zvalue="949">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1183.94,627.996) scale(1,1) translate(0,0)" writing-mode="lr" x="1183.94" xml:space="preserve" y="632.5" zvalue="953">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="101" stroke="rgb(255,255,255)" text-anchor="middle" x="1035.0234375" xml:space="preserve" y="988.4583334922791" zvalue="956">#1SVG </text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="101" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1035.0234375" xml:space="preserve" y="1006.458333492279" zvalue="956">+-25Mvar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1060.99,703.663) scale(1,1) translate(0,0)" writing-mode="lr" x="1060.99" xml:space="preserve" y="708.16" zvalue="959">366</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="126" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1093.11,748.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1093.11" xml:space="preserve" y="753.0599999999999" zvalue="962">07</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1023,777) scale(1,1) translate(-1.13187e-12,0)" writing-mode="lr" x="1023" xml:space="preserve" y="781.5" zvalue="965">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1093.56,811.056) scale(1,1) translate(0,0)" writing-mode="lr" x="1093.56" xml:space="preserve" y="815.5599999999999" zvalue="973">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1058.41,851.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1058.41" xml:space="preserve" y="856.36" zvalue="978">3</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="189" stroke="rgb(255,255,255)" text-anchor="middle" x="1166.7734375" xml:space="preserve" y="987.9583334922791" zvalue="982">#2SVG </text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="15" id="189" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1166.7734375" xml:space="preserve" y="1005.958333492279" zvalue="982">+-25Mvar</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1193.36,704.663) scale(1,1) translate(0,0)" writing-mode="lr" x="1193.36" xml:space="preserve" y="709.16" zvalue="984">375</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1226.11,748.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1226.11" xml:space="preserve" y="753.0599999999999" zvalue="987">07</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="186" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1156,777) scale(1,1) translate(0,0)" writing-mode="lr" x="1156" xml:space="preserve" y="781.5" zvalue="990">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1226.56,811.056) scale(1,1) translate(0,0)" writing-mode="lr" x="1226.56" xml:space="preserve" y="815.5599999999999" zvalue="993">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1191.41,851.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1191.41" xml:space="preserve" y="856.36" zvalue="996">3</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="203" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,963.607,704.44) scale(1,1) translate(0,0)" writing-mode="lr" x="963.61" xml:space="preserve" y="708.9400000000001" zvalue="1001">365</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,963.632,781.061) scale(1,1) translate(0,1.37323e-12)" writing-mode="lr" x="963.63" xml:space="preserve" y="785.5599999999999" zvalue="1003">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="210" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,938.461,902) scale(1,1) translate(0,0)" writing-mode="lr" x="938.46" xml:space="preserve" y="906.5" zvalue="1008">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="212" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,880.607,704.44) scale(1,1) translate(0,0)" writing-mode="lr" x="880.61" xml:space="preserve" y="708.9400000000001" zvalue="1010">364</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,880.874,779.571) scale(1,1) translate(0,0)" writing-mode="lr" x="880.87" xml:space="preserve" y="784.0700000000001" zvalue="1012">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="middle" x="855.796875" xml:space="preserve" y="911.6505681818182" zvalue="1016">#4集电线路太平</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="211" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="855.796875" xml:space="preserve" y="927.6505681818182" zvalue="1016">片区4UL</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="228" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,781.607,704.44) scale(1,1) translate(0,0)" writing-mode="lr" x="781.61" xml:space="preserve" y="708.9400000000001" zvalue="1024">363</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="227" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,781.874,779.571) scale(1,1) translate(0,0)" writing-mode="lr" x="781.87" xml:space="preserve" y="784.0700000000001" zvalue="1027">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="226" stroke="rgb(255,255,255)" text-anchor="middle" x="756.796875" xml:space="preserve" y="911.6505681818182" zvalue="1031">#3集电线路太平</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="226" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="756.796875" xml:space="preserve" y="927.6505681818182" zvalue="1031">片区3UL</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="240" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,682.607,704.44) scale(1,1) translate(0,0)" writing-mode="lr" x="682.61" xml:space="preserve" y="708.9400000000001" zvalue="1036">362</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="239" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,682.874,779.571) scale(1,1) translate(0,0)" writing-mode="lr" x="682.87" xml:space="preserve" y="784.0700000000001" zvalue="1039">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="middle" x="657.796875" xml:space="preserve" y="911.6505681818182" zvalue="1043">#2集电线路太平</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="238" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="657.796875" xml:space="preserve" y="927.6505681818182" zvalue="1043">片区2UL</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="254" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,579.607,704.44) scale(1,1) translate(0,0)" writing-mode="lr" x="579.61" xml:space="preserve" y="708.9400000000001" zvalue="1048">361</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,579.874,779.571) scale(1,1) translate(0,0)" writing-mode="lr" x="579.87" xml:space="preserve" y="784.0700000000001" zvalue="1051">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="middle" x="554.796875" xml:space="preserve" y="911.6505681818182" zvalue="1055">#1集电线路太平</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="554.796875" xml:space="preserve" y="927.6505681818182" zvalue="1055">片区1UL</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="265" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1292.61,704.44) scale(1,1) translate(0,0)" writing-mode="lr" x="1292.61" xml:space="preserve" y="708.9400000000001" zvalue="1060">371</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1292.87,779.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1292.87" xml:space="preserve" y="784.0700000000001" zvalue="1063">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="263" stroke="rgb(255,255,255)" text-anchor="middle" x="1268.59375" xml:space="preserve" y="911.6505681818182" zvalue="1067">#5集电线路铜避关</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="263" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1268.59375" xml:space="preserve" y="927.6505681818182" zvalue="1067">片区A回</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="276" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1381.61,704.44) scale(1,1) translate(0,0)" writing-mode="lr" x="1381.61" xml:space="preserve" y="708.9400000000001" zvalue="1072">372</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="275" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1381.87,779.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1381.87" xml:space="preserve" y="784.0700000000001" zvalue="1075">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="274" stroke="rgb(255,255,255)" text-anchor="middle" x="1357.59375" xml:space="preserve" y="911.6505681818182" zvalue="1079">#6集电线路铜避关</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="274" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1357.59375" xml:space="preserve" y="927.6505681818182" zvalue="1079">片区B回</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="288" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1474.61,704.44) scale(1,1) translate(0,0)" writing-mode="lr" x="1474.61" xml:space="preserve" y="708.9400000000001" zvalue="1084">373</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="287" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1474.87,779.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1474.87" xml:space="preserve" y="784.0700000000001" zvalue="1087">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="286" stroke="rgb(255,255,255)" text-anchor="middle" x="1450.59375" xml:space="preserve" y="911.6505681818182" zvalue="1091">#7集电线路铜避关</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="286" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1450.59375" xml:space="preserve" y="927.6505681818182" zvalue="1091">片区C回</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="305" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1563.61,704.44) scale(1,1) translate(0,0)" writing-mode="lr" x="1563.61" xml:space="preserve" y="708.9400000000001" zvalue="1096">374</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="304" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1563.87,779.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1563.87" xml:space="preserve" y="784.0700000000001" zvalue="1099">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="303" stroke="rgb(255,255,255)" text-anchor="middle" x="1539.59375" xml:space="preserve" y="911.6505681818182" zvalue="1103">#8集电线路铜避关</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="303" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1539.59375" xml:space="preserve" y="927.6505681818182" zvalue="1103">片区D回</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="315" stroke="rgb(255,255,255)" text-anchor="middle" x="817.6328125" xml:space="preserve" y="506.6178985942494" zvalue="1107">35kVⅠ段母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="315" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="817.6328125" xml:space="preserve" y="522.6178985942494" zvalue="1107">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="318" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,783.455,629.364) scale(1,1) translate(0,0)" writing-mode="lr" x="783.45" xml:space="preserve" y="633.86" zvalue="1109">3901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="326" stroke="rgb(255,255,255)" text-anchor="middle" x="1521.0703125" xml:space="preserve" y="508.4460235942494" zvalue="1113">35kVⅡ段母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="326" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1521.0703125" xml:space="preserve" y="524.4460235942494" zvalue="1113">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="321" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1489.36,628.455) scale(1,1) translate(0,0)" writing-mode="lr" x="1489.36" xml:space="preserve" y="632.95" zvalue="1115">3902</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="732"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="362">
   <path class="kv220" d="M 796.83 275.57 L 1438.89 275.57" stroke-width="4" zvalue="2"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674261663748" ObjectName="220kVⅠ母"/>
   <cge:TPSR_Ref TObjectID="9288674261663748"/></metadata>
  <path d="M 796.83 275.57 L 1438.89 275.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="1">
   <path class="kv35" d="M 507.83 666.57 L 1067 666.57" stroke-width="4" zvalue="883"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674261794820" ObjectName="35kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674261794820"/></metadata>
  <path d="M 507.83 666.57 L 1067 666.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="kv35" d="M 1134.83 666.57 L 1587.27 666.57" stroke-width="4" zvalue="885"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674261860356" ObjectName="35kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674261860356"/></metadata>
  <path d="M 1134.83 666.57 L 1587.27 666.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="GroundDisconnectorClass">
  <g id="561">
   <use class="kv220" height="30" transform="rotate(90,1079.73,306.558) scale(1.01852,-0.740741) translate(-19.5203,-724.301)" width="12" x="1073.618545532226" xlink:href="#GroundDisconnector:地刀12_0" y="295.447092472389" zvalue="276"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450234744837" ObjectName="220kVⅠ母电压互感器29010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450234744837"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1079.73,306.558) scale(1.01852,-0.740741) translate(-19.5203,-724.301)" width="12" x="1073.618545532226" y="295.447092472389"/></g>
  <g id="92">
   <use class="kv220" height="30" transform="rotate(90,1139.11,118.914) scale(-1.01852,0.740741) translate(-2257.4,37.7309)" width="12" x="1133.000006569756" xlink:href="#GroundDisconnector:地刀12_0" y="107.8025686802381" zvalue="824"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450235400197" ObjectName="220kV农盈线25267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450235400197"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1139.11,118.914) scale(-1.01852,0.740741) translate(-2257.4,37.7309)" width="12" x="1133.000006569756" y="107.8025686802381"/></g>
  <g id="150">
   <use class="kv220" height="30" transform="rotate(270,1298.46,445.289) scale(-1.01852,-0.740741) translate(-2573.2,-1050.32)" width="12" x="1292.34723578559" xlink:href="#GroundDisconnector:地刀12_0" y="434.1775686802381" zvalue="852"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450235858949" ObjectName="#1主变220kV侧20167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450235858949"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1298.46,445.289) scale(-1.01852,-0.740741) translate(-2573.2,-1050.32)" width="12" x="1292.34723578559" y="434.1775686802381"/></g>
  <g id="161">
   <use class="kv220" height="40" transform="rotate(0,1393.11,495.297) scale(-0.494345,-0.89881) translate(-4221.32,-1048.38)" width="40" x="1383.22619047619" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="477.3211233211234" zvalue="861"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450236055557" ObjectName="#1主变220kV侧2010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450236055557"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1393.11,495.297) scale(-0.494345,-0.89881) translate(-4221.32,-1048.38)" width="40" x="1383.22619047619" y="477.3211233211234"/></g>
  <g id="169">
   <use class="kv110" height="40" transform="rotate(0,1395.9,568.853) scale(-0.494345,-0.89881) translate(-4229.75,-1203.77)" width="40" x="1386.013888888889" xlink:href="#GroundDisconnector:主变中性点接地刀_0" y="550.8766788766789" zvalue="866"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450236317701" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450236317701"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1395.9,568.853) scale(-0.494345,-0.89881) translate(-4229.75,-1203.77)" width="40" x="1386.013888888889" y="550.8766788766789"/></g>
  <g id="10">
   <use class="kv220" height="30" transform="rotate(90,1139.11,166.111) scale(-1.01852,0.740741) translate(-2257.4,54.25)" width="12" x="1133.000007629395" xlink:href="#GroundDisconnector:地刀12_0" y="155" zvalue="903"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450245427205" ObjectName="220kV农盈线25260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450245427205"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1139.11,166.111) scale(-1.01852,0.740741) translate(-2257.4,54.25)" width="12" x="1133.000007629395" y="155"/></g>
  <g id="24">
   <use class="kv220" height="30" transform="rotate(90,1139.11,228.111) scale(-1.01852,0.740741) translate(-2257.4,75.95)" width="12" x="1133.000006781684" xlink:href="#GroundDisconnector:地刀12_0" y="217" zvalue="907"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450245558277" ObjectName="220kV农盈线25217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450245558277"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1139.11,228.111) scale(-1.01852,0.740741) translate(-2257.4,75.95)" width="12" x="1133.000006781684" y="217"/></g>
  <g id="43">
   <use class="kv220" height="30" transform="rotate(90,876.111,118.914) scale(-1.01852,0.740741) translate(-1736.18,37.7309)" width="12" x="870.0000065697564" xlink:href="#GroundDisconnector:地刀12_0" y="107.8025686802381" zvalue="918"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450245951493" ObjectName="220kV备用线25267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450245951493"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,876.111,118.914) scale(-1.01852,0.740741) translate(-1736.18,37.7309)" width="12" x="870.0000065697564" y="107.8025686802381"/></g>
  <g id="38">
   <use class="kv220" height="30" transform="rotate(90,876.111,166.111) scale(-1.01852,0.740741) translate(-1736.18,54.25)" width="12" x="870.0000076293945" xlink:href="#GroundDisconnector:地刀12_0" y="155" zvalue="924"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450245820421" ObjectName="220kV备用线25260接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450245820421"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,876.111,166.111) scale(-1.01852,0.740741) translate(-1736.18,54.25)" width="12" x="870.0000076293945" y="155"/></g>
  <g id="35">
   <use class="kv220" height="30" transform="rotate(90,876.111,228.111) scale(-1.01852,0.740741) translate(-1736.18,75.95)" width="12" x="870.0000067816841" xlink:href="#GroundDisconnector:地刀12_0" y="217" zvalue="928"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450245689349" ObjectName="220kV备用线25217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450245689349"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,876.111,228.111) scale(-1.01852,0.740741) translate(-1736.18,75.95)" width="12" x="870.0000067816841" y="217"/></g>
  <g id="50">
   <use class="kv220" height="30" transform="rotate(90,1079.73,369.111) scale(1.01852,-0.740741) translate(-19.5203,-871.3)" width="12" x="1073.618545532227" xlink:href="#GroundDisconnector:地刀12_0" y="358" zvalue="932"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450246279173" ObjectName="220kVⅠ母电压互感器29017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450246279173"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1079.73,369.111) scale(1.01852,-0.740741) translate(-19.5203,-871.3)" width="12" x="1073.618545532227" y="358"/></g>
  <g id="56">
   <use class="kv220" height="30" transform="rotate(270,1298.46,328.333) scale(-1.01852,-0.740741) translate(-2573.2,-775.472)" width="12" x="1292.347236209446" xlink:href="#GroundDisconnector:地刀12_0" y="317.2222222222222" zvalue="935"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450246410245" ObjectName="#1主变220kV侧20117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450246410245"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1298.46,328.333) scale(-1.01852,-0.740741) translate(-2573.2,-775.472)" width="12" x="1292.347236209446" y="317.2222222222222"/></g>
  <g id="58">
   <use class="kv220" height="30" transform="rotate(270,1298.46,391.222) scale(-1.01852,-0.740741) translate(-2573.2,-923.261)" width="12" x="1292.347235361735" xlink:href="#GroundDisconnector:地刀12_0" y="380.1111111111111" zvalue="937"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450246541317" ObjectName="#1主变220kV侧20160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450246541317"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1298.46,391.222) scale(-1.01852,-0.740741) translate(-2573.2,-923.261)" width="12" x="1292.347235361735" y="380.1111111111111"/></g>
  <g id="117">
   <use class="kv35" height="45" transform="rotate(0,1059.72,752.611) scale(0.901235,-0.901235) translate(113.912,-1589.92)" width="45" x="1039.444444656372" xlink:href="#GroundDisconnector:12547_0" y="732.3333333333334" zvalue="961"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450246737925" ObjectName="#1SVG36607接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450246737925"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,1059.72,752.611) scale(0.901235,-0.901235) translate(113.912,-1589.92)" width="45" x="1039.444444656372" y="732.3333333333334"/></g>
  <g id="159">
   <use class="kv35" height="45" transform="rotate(0,1059.72,812.056) scale(0.901235,-0.901235) translate(113.912,-1715.33)" width="45" x="1039.444444815318" xlink:href="#GroundDisconnector:12547_0" y="791.7777777777779" zvalue="972"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450246934533" ObjectName="#1SVG36667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450246934533"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,1059.72,812.056) scale(0.901235,-0.901235) translate(113.912,-1715.33)" width="45" x="1039.444444815318" y="791.7777777777779"/></g>
  <g id="198">
   <use class="kv35" height="45" transform="rotate(0,1192.72,752.611) scale(0.901235,-0.901235) translate(128.487,-1589.92)" width="45" x="1172.444444656372" xlink:href="#GroundDisconnector:12547_0" y="732.3333333333334" zvalue="986"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450247393285" ObjectName="#2SVG37507接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450247393285"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,1192.72,752.611) scale(0.901235,-0.901235) translate(128.487,-1589.92)" width="45" x="1172.444444656372" y="732.3333333333334"/></g>
  <g id="194">
   <use class="kv35" height="45" transform="rotate(0,1192.72,812.056) scale(0.901235,-0.901235) translate(128.487,-1715.33)" width="45" x="1172.444444815318" xlink:href="#GroundDisconnector:12547_0" y="791.7777777777779" zvalue="992"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450247196677" ObjectName="#2SVG37567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450247196677"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,1192.72,812.056) scale(0.901235,-0.901235) translate(128.487,-1715.33)" width="45" x="1172.444444815318" y="791.7777777777779"/></g>
  <g id="206">
   <use class="kv35" height="45" transform="rotate(0,962.97,752.389) scale(0.901235,-0.901235) translate(103.309,-1589.45)" width="45" x="942.6922621166897" xlink:href="#GroundDisconnector:12547_0" y="732.1111111111111" zvalue="1002"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450247589893" ObjectName="#1站用变36567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450247589893"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,962.97,752.389) scale(0.901235,-0.901235) translate(103.309,-1589.45)" width="45" x="942.6922621166897" y="732.1111111111111"/></g>
  <g id="219">
   <use class="kv35" height="45" transform="rotate(0,879.97,752.389) scale(0.901235,-0.901235) translate(94.2129,-1589.45)" width="45" x="859.6922621166897" xlink:href="#GroundDisconnector:12547_0" y="732.1111111111111" zvalue="1011"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450247852037" ObjectName="#4集电线路太平片区4UL36467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450247852037"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,879.97,752.389) scale(0.901235,-0.901235) translate(94.2129,-1589.45)" width="45" x="859.6922621166897" y="732.1111111111111"/></g>
  <g id="236">
   <use class="kv35" height="45" transform="rotate(0,780.97,752.389) scale(0.901235,-0.901235) translate(83.3635,-1589.45)" width="45" x="760.6922621166898" xlink:href="#GroundDisconnector:12547_0" y="732.1111111111111" zvalue="1025"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450248245253" ObjectName="#3集电线路太平片区3UL36367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450248245253"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,780.97,752.389) scale(0.901235,-0.901235) translate(83.3635,-1589.45)" width="45" x="760.6922621166898" y="732.1111111111111"/></g>
  <g id="249">
   <use class="kv35" height="45" transform="rotate(0,681.97,752.389) scale(0.901235,-0.901235) translate(72.5142,-1589.45)" width="45" x="661.6922621166898" xlink:href="#GroundDisconnector:12547_0" y="732.1111111111111" zvalue="1037"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450248507397" ObjectName="#2集电线路太平片区2UL36267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450248507397"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,681.97,752.389) scale(0.901235,-0.901235) translate(72.5142,-1589.45)" width="45" x="661.6922621166898" y="732.1111111111111"/></g>
  <g id="261">
   <use class="kv35" height="45" transform="rotate(0,578.97,752.389) scale(0.901235,-0.901235) translate(61.2265,-1589.45)" width="45" x="558.6922621166898" xlink:href="#GroundDisconnector:12547_0" y="732.1111111111111" zvalue="1049"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450248769541" ObjectName="#1集电线路太平片区1UL36167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450248769541"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,578.97,752.389) scale(0.901235,-0.901235) translate(61.2265,-1589.45)" width="45" x="558.6922621166898" y="732.1111111111111"/></g>
  <g id="272">
   <use class="kv35" height="45" transform="rotate(0,1291.97,752.389) scale(0.901235,-0.901235) translate(139.364,-1589.45)" width="45" x="1271.69226211669" xlink:href="#GroundDisconnector:12547_0" y="732.1111111111111" zvalue="1061"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450249031685" ObjectName="#5集电线路铜避关片区A回37167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450249031685"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,1291.97,752.389) scale(0.901235,-0.901235) translate(139.364,-1589.45)" width="45" x="1271.69226211669" y="732.1111111111111"/></g>
  <g id="284">
   <use class="kv35" height="45" transform="rotate(0,1380.97,752.389) scale(0.901235,-0.901235) translate(149.117,-1589.45)" width="45" x="1360.69226211669" xlink:href="#GroundDisconnector:12547_0" y="732.1111111111111" zvalue="1073"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450249293829" ObjectName="#6集电线路铜避关片区B回37267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450249293829"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,1380.97,752.389) scale(0.901235,-0.901235) translate(149.117,-1589.45)" width="45" x="1360.69226211669" y="732.1111111111111"/></g>
  <g id="300">
   <use class="kv35" height="45" transform="rotate(0,1473.97,752.389) scale(0.901235,-0.901235) translate(159.309,-1589.45)" width="45" x="1453.69226211669" xlink:href="#GroundDisconnector:12547_0" y="732.1111111111111" zvalue="1085"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450249555973" ObjectName="#7集电线路铜避关片区C回37367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450249555973"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,1473.97,752.389) scale(0.901235,-0.901235) translate(159.309,-1589.45)" width="45" x="1453.69226211669" y="732.1111111111111"/></g>
  <g id="312">
   <use class="kv35" height="45" transform="rotate(0,1562.97,752.389) scale(0.901235,-0.901235) translate(169.062,-1589.45)" width="45" x="1542.69226211669" xlink:href="#GroundDisconnector:12547_0" y="732.1111111111111" zvalue="1097"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450249818117" ObjectName="#8集电线路铜避关片区D回37467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450249818117"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,1562.97,752.389) scale(0.901235,-0.901235) translate(169.062,-1589.45)" width="45" x="1542.69226211669" y="732.1111111111111"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="214">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="214" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127724318724" ObjectName="F"/>
   </metadata>
  </g>
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127731134468" ObjectName="F"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127731200004" ObjectName="F"/>
   </metadata>
  </g>
  <g id="66">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="66" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127731003396" ObjectName="F"/>
   </metadata>
  </g>
  <g id="65">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127731068932" ObjectName="F"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="51">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="278">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="278" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,356.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="361.3" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="水位"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="7" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1242,72.119) scale(1,1) translate(3.9868e-13,0)" writing-mode="lr" x="1198.87" xml:space="preserve" y="76.48" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127724449796" ObjectName="P"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="8" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1242,90.8333) scale(1,1) translate(3.9868e-13,0)" writing-mode="lr" x="1198.87" xml:space="preserve" y="95.19" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127724515332" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="9" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1242,109.548) scale(1,1) translate(3.9868e-13,0)" writing-mode="lr" x="1198.87" xml:space="preserve" y="113.91" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127724580868" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1065.83,461.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1022.71" xml:space="preserve" y="465.93" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127724187652" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="16" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,817.833,479.571) scale(1,1) translate(0,0)" writing-mode="lr" x="774.71" xml:space="preserve" y="483.93" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127763968004" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="17" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1523.83,482.571) scale(1,1) translate(0,0)" writing-mode="lr" x="1480.71" xml:space="preserve" y="486.93" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127764492292" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="18" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1415.26,315.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1372.14" xml:space="preserve" y="320.06" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127726546948" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="19" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1415.26,345.194) scale(1,1) translate(0,0)" writing-mode="lr" x="1372.14" xml:space="preserve" y="349.56" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127726612487" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="63" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1415.26,374.694) scale(1,1) translate(0,0)" writing-mode="lr" x="1372.14" xml:space="preserve" y="379.06" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127726678020" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="76">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="76" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,554.692,942.994) scale(1,1) translate(0,0)" writing-mode="lr" x="511.57" xml:space="preserve" y="947.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127774715908" ObjectName="P"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="80" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,657.692,942.994) scale(1,1) translate(0,0)" writing-mode="lr" x="614.5700000000001" xml:space="preserve" y="947.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127773274116" ObjectName="P"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="86" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,756.692,942.994) scale(1,1) translate(0,0)" writing-mode="lr" x="713.5700000000001" xml:space="preserve" y="947.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127771832324" ObjectName="P"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="91" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,855.692,942.994) scale(1,1) translate(9.00049e-14,0)" writing-mode="lr" x="812.5700000000001" xml:space="preserve" y="947.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127770390532" ObjectName="P"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="102" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1267.69,942.994) scale(1,1) translate(0,0)" writing-mode="lr" x="1224.57" xml:space="preserve" y="947.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127776157700" ObjectName="P"/>
   </metadata>
  </g>
  <g id="111">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="111" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1356.69,942.994) scale(1,1) translate(0,0)" writing-mode="lr" x="1313.57" xml:space="preserve" y="947.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127777599492" ObjectName="P"/>
   </metadata>
  </g>
  <g id="114">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="114" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1449.69,942.994) scale(1,1) translate(-9.35713e-13,0)" writing-mode="lr" x="1406.57" xml:space="preserve" y="947.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127779041284" ObjectName="P"/>
   </metadata>
  </g>
  <g id="115">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="115" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1538.69,942.994) scale(1,1) translate(0,0)" writing-mode="lr" x="1495.57" xml:space="preserve" y="947.36" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127780483076" ObjectName="P"/>
   </metadata>
  </g>
  <g id="125">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="125" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,554.692,959.994) scale(1,1) translate(0,0)" writing-mode="lr" x="511.57" xml:space="preserve" y="964.36" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127774781444" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="131">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="131" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,657.692,959.994) scale(1,1) translate(0,0)" writing-mode="lr" x="614.5700000000001" xml:space="preserve" y="964.36" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127773339652" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="136">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="136" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,756.692,959.994) scale(1,1) translate(0,0)" writing-mode="lr" x="713.5700000000001" xml:space="preserve" y="964.36" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127771897860" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="137">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="137" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,855.692,959.994) scale(1,1) translate(9.00049e-14,0)" writing-mode="lr" x="812.5700000000001" xml:space="preserve" y="964.36" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127770456068" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="138">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="138" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1267.69,959.994) scale(1,1) translate(0,0)" writing-mode="lr" x="1224.57" xml:space="preserve" y="964.36" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127776223236" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="141" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1356.69,959.994) scale(1,1) translate(0,0)" writing-mode="lr" x="1313.57" xml:space="preserve" y="964.36" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127777665028" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="145" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1449.69,959.994) scale(1,1) translate(-9.35713e-13,0)" writing-mode="lr" x="1406.57" xml:space="preserve" y="964.36" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127779106820" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="147">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="147" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1538.69,959.994) scale(1,1) translate(0,0)" writing-mode="lr" x="1495.57" xml:space="preserve" y="964.36" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127780548612" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="155">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="155" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,554.692,976.994) scale(1,1) translate(0,0)" writing-mode="lr" x="511.57" xml:space="preserve" y="981.36" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127774846980" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="164">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="164" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,657.692,976.994) scale(1,1) translate(0,0)" writing-mode="lr" x="614.5700000000001" xml:space="preserve" y="981.36" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127773405188" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="165">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="165" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,756.692,976.994) scale(1,1) translate(0,0)" writing-mode="lr" x="713.5700000000001" xml:space="preserve" y="981.36" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127771963396" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="167">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="167" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,855.692,976.994) scale(1,1) translate(9.00049e-14,0)" writing-mode="lr" x="812.5700000000001" xml:space="preserve" y="981.36" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127770521604" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="168">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="168" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1267.69,976.994) scale(1,1) translate(0,0)" writing-mode="lr" x="1224.57" xml:space="preserve" y="981.36" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127776288772" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="170">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="170" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1356.69,976.994) scale(1,1) translate(0,0)" writing-mode="lr" x="1313.57" xml:space="preserve" y="981.36" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127777730564" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="171">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="171" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1449.69,976.994) scale(1,1) translate(-9.35713e-13,0)" writing-mode="lr" x="1406.57" xml:space="preserve" y="981.36" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127779172356" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="173">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="173" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1538.69,976.994) scale(1,1) translate(0,0)" writing-mode="lr" x="1495.57" xml:space="preserve" y="981.36" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127780614148" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="71">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="71" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1033,1016.75) scale(1,1) translate(0,0)" writing-mode="lr" x="989.88" xml:space="preserve" y="1021.11" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127767375876" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="73" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1033,1033.75) scale(1,1) translate(0,0)" writing-mode="lr" x="989.88" xml:space="preserve" y="1038.11" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127767441412" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="74">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="74" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1165,1016.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1121.88" xml:space="preserve" y="1021.11" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127768686596" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="174">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="174" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1165,1033.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1121.88" xml:space="preserve" y="1038.11" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127768752132" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="177">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="177" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,977.5,586.75) scale(1,1) translate(0,0)" writing-mode="lr" x="934.38" xml:space="preserve" y="591.11" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127766523908" ObjectName="P"/>
   </metadata>
  </g>
  <g id="316">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="316" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,977.5,616.25) scale(1,1) translate(0,0)" writing-mode="lr" x="934.38" xml:space="preserve" y="620.61" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127766589444" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="332">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="332" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,977.5,645.75) scale(1,1) translate(0,0)" writing-mode="lr" x="934.38" xml:space="preserve" y="650.11" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127766327300" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="333">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="333" prefix="P:" stroke="rgb(0,255,0)" text-anchor="start" transform="rotate(0,1231.25,586.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1188.13" xml:space="preserve" y="591.11" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127767048196" ObjectName="P"/>
   </metadata>
  </g>
  <g id="334">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="334" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="start" transform="rotate(0,1231.25,616.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1188.13" xml:space="preserve" y="620.61" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127767113732" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="335">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="335" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="start" transform="rotate(0,1231.25,645.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1188.13" xml:space="preserve" y="650.11" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127766851588" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="213">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="730"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374896398339" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="731"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562953757851657" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
  <g id="84">
   <use height="30" transform="rotate(0,314.812,131.464) scale(1.27778,1.03333) translate(-55.9375,-3.74077)" width="90" x="257.31" xlink:href="#State:全站检修_0" y="115.96" zvalue="781"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549595930626" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,314.812,131.464) scale(1.27778,1.03333) translate(-55.9375,-3.74077)" width="90" x="257.31" y="115.96"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="122">
   <path class="kv220" d="M 1069.12 306.55 L 1051.92 306.55" stroke-width="1" zvalue="783"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="561@0" LinkObjectIDznd="21" MaxPinNum="2"/>
   </metadata>
  <path d="M 1069.12 306.55 L 1051.92 306.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="142">
   <path class="kv220" d="M 1322.12 290.13 L 1322.12 275.57" stroke-width="1" zvalue="840"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="362@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.12 290.13 L 1322.12 275.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv220" d="M 1322.12 343.27 L 1322.1 311.79" stroke-width="1" zvalue="843"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="143@0" LinkObjectIDznd="140@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.12 343.27 L 1322.1 311.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv220" d="M 1322.21 409.05 L 1322.29 372.99" stroke-width="1" zvalue="850"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="148@0" LinkObjectIDznd="143@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.21 409.05 L 1322.29 372.99" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv220" d="M 1322.19 477.92 L 1322.19 430.72" stroke-width="1" zvalue="854"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@2" LinkObjectIDznd="148@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.19 477.92 L 1322.19 430.72" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="kv220" d="M 1309.07 445.28 L 1322.19 445.28" stroke-width="1" zvalue="855"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="153" MaxPinNum="2"/>
   </metadata>
  <path d="M 1309.07 445.28 L 1322.19 445.28" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="12">
   <path class="kv220" d="M 1172.91 210.86 L 1172.91 240.5" stroke-width="1" zvalue="892"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@1" LinkObjectIDznd="105@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1172.91 210.86 L 1172.91 240.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="13">
   <path class="kv220" d="M 1171.98 262.17 L 1171.98 275.57" stroke-width="1" zvalue="893"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="105@1" LinkObjectIDznd="362@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1171.98 262.17 L 1171.98 275.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv220" d="M 1173.94 99.86 L 1174 131.93" stroke-width="1" zvalue="894"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="104@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1173.94 99.86 L 1174 131.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv220" d="M 1173.98 153.59 L 1173.98 181.14" stroke-width="1" zvalue="895"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="104@1" LinkObjectIDznd="106@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1173.98 153.59 L 1173.98 181.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="kv220" d="M 1051.98 388.11 L 1051.9 349.88" stroke-width="1" zvalue="900"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@0" LinkObjectIDznd="128@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1051.98 388.11 L 1051.9 349.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="21">
   <path class="kv220" d="M 1051.92 328.21 L 1051.92 275.57" stroke-width="1" zvalue="901"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@0" LinkObjectIDznd="362@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1051.92 328.21 L 1051.92 275.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="11">
   <path class="kv220" d="M 1149.72 118.93 L 1173.98 118.93" stroke-width="1" zvalue="904"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="14" MaxPinNum="2"/>
   </metadata>
  <path d="M 1149.72 118.93 L 1173.98 118.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="kv220" d="M 1149.72 166.12 L 1173.98 166.12" stroke-width="1" zvalue="905"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@0" LinkObjectIDznd="15" MaxPinNum="2"/>
   </metadata>
  <path d="M 1149.72 166.12 L 1173.98 166.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="26">
   <path class="kv220" d="M 1149.72 228.12 L 1172.91 228.12" stroke-width="1" zvalue="908"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@0" LinkObjectIDznd="12" MaxPinNum="2"/>
   </metadata>
  <path d="M 1149.72 228.12 L 1172.91 228.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv220" d="M 909.91 210.86 L 909.91 240.5" stroke-width="1" zvalue="920"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="49@1" LinkObjectIDznd="47@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 909.91 210.86 L 909.91 240.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv220" d="M 908.98 262.17 L 908.98 275.57" stroke-width="1" zvalue="921"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="47@1" LinkObjectIDznd="362@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 908.98 262.17 L 908.98 275.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv220" d="M 911 99.95 L 911 131.93" stroke-width="1" zvalue="922"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="45@0" LinkObjectIDznd="46@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 911 99.95 L 911 131.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv220" d="M 910.98 153.59 L 910.98 181.14" stroke-width="1" zvalue="923"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@1" LinkObjectIDznd="49@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 910.98 153.59 L 910.98 181.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv220" d="M 886.72 118.93 L 911 118.93" stroke-width="1" zvalue="926"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="43@0" LinkObjectIDznd="40" MaxPinNum="2"/>
   </metadata>
  <path d="M 886.72 118.93 L 911 118.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv220" d="M 886.72 166.12 L 910.98 166.12" stroke-width="1" zvalue="927"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="38@0" LinkObjectIDznd="39" MaxPinNum="2"/>
   </metadata>
  <path d="M 886.72 166.12 L 910.98 166.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="kv220" d="M 886.72 228.12 L 909.91 228.12" stroke-width="1" zvalue="929"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="42" MaxPinNum="2"/>
   </metadata>
  <path d="M 886.72 228.12 L 909.91 228.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv220" d="M 1069.12 369.1 L 1051.94 369.1" stroke-width="1" zvalue="933"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="50@0" LinkObjectIDznd="20" MaxPinNum="2"/>
   </metadata>
  <path d="M 1069.12 369.1 L 1051.94 369.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv220" d="M 1309.07 391.21 L 1322.25 391.21" stroke-width="1" zvalue="938"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@0" LinkObjectIDznd="149" MaxPinNum="2"/>
   </metadata>
  <path d="M 1309.07 391.21 L 1322.25 391.21" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="kv220" d="M 1309.07 328.32 L 1322.11 328.32" stroke-width="1" zvalue="939"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@0" LinkObjectIDznd="144" MaxPinNum="2"/>
   </metadata>
  <path d="M 1309.07 328.32 L 1322.11 328.32" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="62">
   <path class="kv220" d="M 1321.39 496.95 L 1371.11 475.11 L 1392.22 475.11 L 1391.83 484.33" stroke-width="1" zvalue="940"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@0" LinkObjectIDznd="161@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1321.39 496.95 L 1371.11 475.11 L 1392.22 475.11 L 1391.83 484.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="70">
   <path class="kv110" d="M 1322.19 536.59 L 1394.44 548.44 L 1394.62 557.89" stroke-width="1" zvalue="941"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@1" LinkObjectIDznd="169@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1322.19 536.59 L 1394.44 548.44 L 1394.62 557.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv35" d="M 1284.44 496.95 L 1249.94 525.64" stroke-width="1" zvalue="943"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@5" LinkObjectIDznd="158@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1284.44 496.95 L 1249.94 525.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="75">
   <path class="kv35" d="M 1263.02 496.95 L 1106 496.95 L 1106 582.89" stroke-width="1" zvalue="946"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="151@4" LinkObjectIDznd="88" MaxPinNum="2"/>
   </metadata>
  <path d="M 1263.02 496.95 L 1106 496.95 L 1106 582.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv35" d="M 1044.36 666.57 L 1044.36 642.06" stroke-width="1" zvalue="949"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@0" LinkObjectIDznd="77@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1044.36 666.57 L 1044.36 642.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv35" d="M 1044.36 606.58 L 1044.36 582.89 L 1159.58 582.89 L 1159.58 611.01" stroke-width="1" zvalue="953"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@0" LinkObjectIDznd="87@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1044.36 606.58 L 1044.36 582.89 L 1159.58 582.89 L 1159.58 611.01" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv35" d="M 1159.58 646.49 L 1159.58 666.57" stroke-width="1" zvalue="954"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="87@1" LinkObjectIDznd="5@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1159.58 646.49 L 1159.58 666.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="127">
   <path class="kv35" d="M 1052.51 738.19 L 1035.07 738.19" stroke-width="1" zvalue="962"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@0" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 1052.51 738.19 L 1035.07 738.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv35" d="M 1035.07 767.26 L 1035.07 722.16" stroke-width="1" zvalue="966"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="129@0" LinkObjectIDznd="112@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1035.07 767.26 L 1035.07 722.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv35" d="M 1052.51 797.64 L 1035.05 797.64" stroke-width="1" zvalue="973"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="181" MaxPinNum="2"/>
   </metadata>
  <path d="M 1052.51 797.64 L 1035.05 797.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="172">
   <path class="kv110" d="M 1338.68 601.9 L 1322.19 601.9 L 1322.19 556.95" stroke-width="1" zvalue="975"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175@0" LinkObjectIDznd="151@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1338.68 601.9 L 1322.19 601.9 L 1322.19 556.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv35" d="M 1035.44 873.48 L 1035.44 867.1" stroke-width="1" zvalue="978"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@0" LinkObjectIDznd="178@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1035.44 873.48 L 1035.44 867.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="181">
   <path class="kv35" d="M 1035.05 832.19 L 1035.05 788.92" stroke-width="1" zvalue="979"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="178@0" LinkObjectIDznd="129@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1035.05 832.19 L 1035.05 788.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="199">
   <path class="kv35" d="M 1168.44 687.68 L 1168.44 666.57" stroke-width="1" zvalue="985"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="200@0" LinkObjectIDznd="5@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1168.44 687.68 L 1168.44 666.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="kv35" d="M 1185.51 738.19 L 1168.07 738.19" stroke-width="1" zvalue="988"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@0" LinkObjectIDznd="195" MaxPinNum="2"/>
   </metadata>
  <path d="M 1185.51 738.19 L 1168.07 738.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv35" d="M 1168.07 767.26 L 1168.07 723.16" stroke-width="1" zvalue="991"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="200@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1168.07 767.26 L 1168.07 723.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv35" d="M 1185.51 797.64 L 1168.05 797.64" stroke-width="1" zvalue="994"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="194@0" LinkObjectIDznd="190" MaxPinNum="2"/>
   </metadata>
  <path d="M 1185.51 797.64 L 1168.05 797.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="kv35" d="M 1168.44 870.48 L 1168.44 867.1" stroke-width="1" zvalue="997"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="201@0" LinkObjectIDznd="192@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1168.44 870.48 L 1168.44 867.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="kv35" d="M 1168.05 832.19 L 1168.05 788.92" stroke-width="1" zvalue="998"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@0" LinkObjectIDznd="196@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1168.05 832.19 L 1168.05 788.92" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="205">
   <path class="kv35" d="M 955.76 737.97 L 938.69 737.97" stroke-width="1" zvalue="1004"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="206@0" LinkObjectIDznd="204" MaxPinNum="2"/>
   </metadata>
  <path d="M 955.76 737.97 L 938.69 737.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="204">
   <path class="kv35" d="M 938.69 839.03 L 938.69 722.94" stroke-width="1" zvalue="1005"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="209@0" LinkObjectIDznd="207@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 938.69 839.03 L 938.69 722.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="208">
   <path class="kv35" d="M 938.69 687.46 L 938.69 666.57" stroke-width="1" zvalue="1006"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="1@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 938.69 687.46 L 938.69 666.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="218">
   <path class="kv35" d="M 872.76 737.97 L 855.69 737.97" stroke-width="1" zvalue="1012"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="219@0" LinkObjectIDznd="217" MaxPinNum="2"/>
   </metadata>
  <path d="M 872.76 737.97 L 855.69 737.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="217">
   <path class="kv35" d="M 855.69 854.77 L 855.69 722.94" stroke-width="1" zvalue="1013"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="215@0" LinkObjectIDznd="220@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 855.69 854.77 L 855.69 722.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="216">
   <path class="kv35" d="M 855.69 687.46 L 855.69 666.57" stroke-width="1" zvalue="1014"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@0" LinkObjectIDznd="1@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 855.69 687.46 L 855.69 666.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="kv35" d="M 939 786 L 938.69 786" stroke-width="1" zvalue="1018"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="204" MaxPinNum="2"/>
   </metadata>
  <path d="M 939 786 L 938.69 786" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="kv35" d="M 855.5 786 L 855.69 786" stroke-width="1" zvalue="1021"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="223@0" LinkObjectIDznd="217" MaxPinNum="2"/>
   </metadata>
  <path d="M 855.5 786 L 855.69 786" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="235">
   <path class="kv35" d="M 773.76 737.97 L 756.69 737.97" stroke-width="1" zvalue="1026"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="236@0" LinkObjectIDznd="233" MaxPinNum="2"/>
   </metadata>
  <path d="M 773.76 737.97 L 756.69 737.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="kv35" d="M 756.69 854.77 L 756.69 722.94" stroke-width="1" zvalue="1028"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@0" LinkObjectIDznd="237@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 756.69 854.77 L 756.69 722.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="232">
   <path class="kv35" d="M 756.69 687.46 L 756.69 666.57" stroke-width="1" zvalue="1029"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@0" LinkObjectIDznd="1@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 756.69 687.46 L 756.69 666.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="229">
   <path class="kv35" d="M 756.5 786 L 756.69 786" stroke-width="1" zvalue="1033"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="233" MaxPinNum="2"/>
   </metadata>
  <path d="M 756.5 786 L 756.69 786" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="248">
   <path class="kv35" d="M 674.76 737.97 L 657.69 737.97" stroke-width="1" zvalue="1038"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@0" LinkObjectIDznd="247" MaxPinNum="2"/>
   </metadata>
  <path d="M 674.76 737.97 L 657.69 737.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="247">
   <path class="kv35" d="M 657.69 854.77 L 657.69 722.94" stroke-width="1" zvalue="1040"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="243@0" LinkObjectIDznd="251@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 657.69 854.77 L 657.69 722.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="246">
   <path class="kv35" d="M 657.69 687.46 L 657.69 666.57" stroke-width="1" zvalue="1041"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="251@0" LinkObjectIDznd="1@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 657.69 687.46 L 657.69 666.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="241">
   <path class="kv35" d="M 657.5 786 L 657.69 786" stroke-width="1" zvalue="1045"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="242@0" LinkObjectIDznd="247" MaxPinNum="2"/>
   </metadata>
  <path d="M 657.5 786 L 657.69 786" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="260">
   <path class="kv35" d="M 571.76 737.97 L 554.69 737.97" stroke-width="1" zvalue="1050"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@0" LinkObjectIDznd="259" MaxPinNum="2"/>
   </metadata>
  <path d="M 571.76 737.97 L 554.69 737.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="259">
   <path class="kv35" d="M 554.69 854.77 L 554.69 722.94" stroke-width="1" zvalue="1052"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="257@0" LinkObjectIDznd="262@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 554.69 854.77 L 554.69 722.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="258">
   <path class="kv35" d="M 554.69 687.46 L 554.69 666.57" stroke-width="1" zvalue="1053"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="262@0" LinkObjectIDznd="1@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 554.69 687.46 L 554.69 666.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="255">
   <path class="kv35" d="M 554.5 786 L 554.69 786" stroke-width="1" zvalue="1057"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="256@0" LinkObjectIDznd="259" MaxPinNum="2"/>
   </metadata>
  <path d="M 554.5 786 L 554.69 786" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="271">
   <path class="kv35" d="M 1284.76 737.97 L 1267.69 737.97" stroke-width="1" zvalue="1062"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="272@0" LinkObjectIDznd="270" MaxPinNum="2"/>
   </metadata>
  <path d="M 1284.76 737.97 L 1267.69 737.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="270">
   <path class="kv35" d="M 1267.69 854.77 L 1267.69 722.94" stroke-width="1" zvalue="1064"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="268@0" LinkObjectIDznd="273@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1267.69 854.77 L 1267.69 722.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="269">
   <path class="kv35" d="M 1267.69 687.46 L 1267.69 666.57" stroke-width="1" zvalue="1065"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="273@0" LinkObjectIDznd="5@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1267.69 687.46 L 1267.69 666.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="266">
   <path class="kv35" d="M 1267.5 786 L 1267.69 786" stroke-width="1" zvalue="1069"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="267@0" LinkObjectIDznd="270" MaxPinNum="2"/>
   </metadata>
  <path d="M 1267.5 786 L 1267.69 786" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="283">
   <path class="kv35" d="M 1373.76 737.97 L 1356.69 737.97" stroke-width="1" zvalue="1074"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="284@0" LinkObjectIDznd="282" MaxPinNum="2"/>
   </metadata>
  <path d="M 1373.76 737.97 L 1356.69 737.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="282">
   <path class="kv35" d="M 1356.69 854.77 L 1356.69 722.94" stroke-width="1" zvalue="1076"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="280@0" LinkObjectIDznd="285@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1356.69 854.77 L 1356.69 722.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="281">
   <path class="kv35" d="M 1356.69 687.46 L 1356.69 666.57" stroke-width="1" zvalue="1077"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="285@0" LinkObjectIDznd="5@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1356.69 687.46 L 1356.69 666.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="277">
   <path class="kv35" d="M 1356.5 786 L 1356.69 786" stroke-width="1" zvalue="1081"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="279@0" LinkObjectIDznd="282" MaxPinNum="2"/>
   </metadata>
  <path d="M 1356.5 786 L 1356.69 786" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="299">
   <path class="kv35" d="M 1466.76 737.97 L 1449.69 737.97" stroke-width="1" zvalue="1086"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="300@0" LinkObjectIDznd="298" MaxPinNum="2"/>
   </metadata>
  <path d="M 1466.76 737.97 L 1449.69 737.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="298">
   <path class="kv35" d="M 1449.69 854.77 L 1449.69 722.94" stroke-width="1" zvalue="1088"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="291@0" LinkObjectIDznd="302@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1449.69 854.77 L 1449.69 722.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="292">
   <path class="kv35" d="M 1449.69 687.46 L 1449.69 666.57" stroke-width="1" zvalue="1089"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="302@0" LinkObjectIDznd="5@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1449.69 687.46 L 1449.69 666.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="289">
   <path class="kv35" d="M 1449.5 786 L 1449.69 786" stroke-width="1" zvalue="1093"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="290@0" LinkObjectIDznd="298" MaxPinNum="2"/>
   </metadata>
  <path d="M 1449.5 786 L 1449.69 786" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="311">
   <path class="kv35" d="M 1555.76 737.97 L 1538.69 737.97" stroke-width="1" zvalue="1098"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="312@0" LinkObjectIDznd="310" MaxPinNum="2"/>
   </metadata>
  <path d="M 1555.76 737.97 L 1538.69 737.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="310">
   <path class="kv35" d="M 1538.69 856.17 L 1538.69 722.94" stroke-width="1" zvalue="1100"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="308@0" LinkObjectIDznd="313@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1538.69 856.17 L 1538.69 722.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="309">
   <path class="kv35" d="M 1538.69 687.46 L 1538.69 666.57" stroke-width="1" zvalue="1101"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="313@0" LinkObjectIDznd="5@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1538.69 687.46 L 1538.69 666.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="306">
   <path class="kv35" d="M 1538.5 786 L 1538.69 786" stroke-width="1" zvalue="1105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="307@0" LinkObjectIDznd="310" MaxPinNum="2"/>
   </metadata>
  <path d="M 1538.5 786 L 1538.69 786" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="319">
   <path class="kv35" d="M 812.73 592.33 L 812.73 614.91" stroke-width="1" zvalue="1109"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="314@0" LinkObjectIDznd="317@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 812.73 592.33 L 812.73 614.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="320">
   <path class="kv35" d="M 812.73 645.82 L 812.73 666.57" stroke-width="1" zvalue="1110"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="317@1" LinkObjectIDznd="1@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 812.73 645.82 L 812.73 666.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="323">
   <path class="kv35" d="M 1518.64 591.42 L 1518.64 614" stroke-width="1" zvalue="1114"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="325@0" LinkObjectIDznd="324@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1518.64 591.42 L 1518.64 614" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="322">
   <path class="kv35" d="M 1518.64 644.91 L 1518.64 666.57" stroke-width="1" zvalue="1116"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="324@1" LinkObjectIDznd="5@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1518.64 644.91 L 1518.64 666.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="329">
   <path class="kv220" d="M 1331.79 455.14 L 1322.19 455.14" stroke-width="1" zvalue="1118"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="328@0" LinkObjectIDznd="153" MaxPinNum="2"/>
   </metadata>
  <path d="M 1331.79 455.14 L 1322.19 455.14" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="331">
   <path class="kv35" d="M 1091.07 509.43 L 1106 509.43" stroke-width="1" zvalue="1121"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="330@0" LinkObjectIDznd="75" MaxPinNum="2"/>
   </metadata>
  <path d="M 1091.07 509.43 L 1106 509.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="380">
   <path class="kv35" d="M 1036.07 686.68 L 1036.07 666.57" stroke-width="1" zvalue="1222"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="1@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1036.07 686.68 L 1036.07 666.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="128">
   <use class="kv220" height="30" transform="rotate(0,1051.85,338.958) scale(0.814815,0.740741) translate(237.669,114.747)" width="15" x="1045.741738117463" xlink:href="#Disconnector:刀闸_0" y="327.8472222222222" zvalue="785"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450234810373" ObjectName="220kVⅠ母电压互感器2901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450234810373"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1051.85,338.958) scale(0.814815,0.740741) translate(237.669,114.747)" width="15" x="1045.741738117463" y="327.8472222222222"/></g>
  <g id="105">
   <use class="kv220" height="30" transform="rotate(0,1171.93,251.248) scale(0.814815,0.740741) translate(264.959,84.0478)" width="15" x="1165.818647358057" xlink:href="#Disconnector:刀闸_0" y="240.1366614135177" zvalue="818"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450235596805" ObjectName="220kV农盈线2521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450235596805"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1171.93,251.248) scale(0.814815,0.740741) translate(264.959,84.0478)" width="15" x="1165.818647358057" y="240.1366614135177"/></g>
  <g id="104">
   <use class="kv220" height="30" transform="rotate(0,1173.93,142.673) scale(0.814815,0.740741) translate(265.413,46.0467)" width="15" x="1167.815278768929" xlink:href="#Disconnector:刀闸_0" y="131.5620954122228" zvalue="819"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450235531269" ObjectName="220kV农盈线2526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450235531269"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1173.93,142.673) scale(0.814815,0.740741) translate(265.413,46.0467)" width="15" x="1167.815278768929" y="131.5620954122228"/></g>
  <g id="140">
   <use class="kv220" height="30" transform="rotate(0,1322.05,300.873) scale(0.814815,0.740741) translate(299.077,101.417)" width="15" x="1315.939191136672" xlink:href="#Disconnector:刀闸_0" y="289.7616614135177" zvalue="837"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450235662341" ObjectName="#1主变220kV侧2011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450235662341"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1322.05,300.873) scale(0.814815,0.740741) translate(299.077,101.417)" width="15" x="1315.939191136672" y="289.7616614135177"/></g>
  <g id="148">
   <use class="kv220" height="30" transform="rotate(0,1322.14,419.798) scale(0.814815,0.740741) translate(299.097,143.04)" width="15" x="1316.025919634485" xlink:href="#Disconnector:刀闸_0" y="408.6870954122228" zvalue="847"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450235727877" ObjectName="#1主变220kV侧2016隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450235727877"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1322.14,419.798) scale(0.814815,0.740741) translate(299.097,143.04)" width="15" x="1316.025919634485" y="408.6870954122228"/></g>
  <g id="47">
   <use class="kv220" height="30" transform="rotate(0,908.93,251.248) scale(0.814815,0.740741) translate(205.186,84.0478)" width="15" x="902.8186473580568" xlink:href="#Disconnector:刀闸_0" y="240.1366614135177" zvalue="912"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450246148101" ObjectName="220kV备用线2521隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450246148101"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,908.93,251.248) scale(0.814815,0.740741) translate(205.186,84.0478)" width="15" x="902.8186473580568" y="240.1366614135177"/></g>
  <g id="46">
   <use class="kv220" height="30" transform="rotate(0,910.926,142.673) scale(0.814815,0.740741) translate(205.64,46.0467)" width="15" x="904.815278768929" xlink:href="#Disconnector:刀闸_0" y="131.5620954122228" zvalue="913"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450246082565" ObjectName="220kV备用线2526隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450246082565"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,910.926,142.673) scale(0.814815,0.740741) translate(205.64,46.0467)" width="15" x="904.815278768929" y="131.5620954122228"/></g>
  <g id="129">
   <use class="kv35" height="30" transform="rotate(0,1035,778) scale(0.814815,0.740741) translate(233.838,268.411)" width="15" x="1028.888888888889" xlink:href="#Disconnector:刀闸_0" y="766.8888888888889" zvalue="964"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450246803461" ObjectName="#1SVG3666隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450246803461"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1035,778) scale(0.814815,0.740741) translate(233.838,268.411)" width="15" x="1028.888888888889" y="766.8888888888889"/></g>
  <g id="178">
   <use class="kv35" height="24" transform="rotate(0,1035.05,849.643) scale(1.51786,1.51786) translate(-346.92,-283.664)" width="24" x="1016.835551964615" xlink:href="#Disconnector:SVG隔离刀闸_0" y="831.4285714285714" zvalue="977"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450247000069" ObjectName="#1SVG3663隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450247000069"/></metadata>
  <rect fill="white" height="24" opacity="0" stroke="white" transform="rotate(0,1035.05,849.643) scale(1.51786,1.51786) translate(-346.92,-283.664)" width="24" x="1016.835551964615" y="831.4285714285714"/></g>
  <g id="196">
   <use class="kv35" height="30" transform="rotate(0,1168,778) scale(0.814815,0.740741) translate(264.066,268.411)" width="15" x="1161.888888888889" xlink:href="#Disconnector:刀闸_0" y="766.8888888888889" zvalue="989"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450247262213" ObjectName="#2SVG3756隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450247262213"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1168,778) scale(0.814815,0.740741) translate(264.066,268.411)" width="15" x="1161.888888888889" y="766.8888888888889"/></g>
  <g id="192">
   <use class="kv35" height="24" transform="rotate(0,1168.05,849.643) scale(1.51786,1.51786) translate(-392.297,-283.664)" width="24" x="1149.835551964615" xlink:href="#Disconnector:SVG隔离刀闸_0" y="831.4285714285714" zvalue="995"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450247065605" ObjectName="#2SVG3753隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450247065605"/></metadata>
  <rect fill="white" height="24" opacity="0" stroke="white" transform="rotate(0,1168.05,849.643) scale(1.51786,1.51786) translate(-392.297,-283.664)" width="24" x="1149.835551964615" y="831.4285714285714"/></g>
  <g id="317">
   <use class="kv35" height="36" transform="rotate(0,812.727,630.364) scale(0.909091,0.909091) translate(80.6364,61.4)" width="14" x="806.3636363636363" xlink:href="#Disconnector:手车刀闸_0" y="614" zvalue="1108"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450249949189" ObjectName="35kVⅠ段母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450249949189"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,812.727,630.364) scale(0.909091,0.909091) translate(80.6364,61.4)" width="14" x="806.3636363636363" y="614"/></g>
  <g id="324">
   <use class="kv35" height="36" transform="rotate(0,1518.64,629.455) scale(0.909091,0.909091) translate(151.227,61.3091)" width="14" x="1512.272727272727" xlink:href="#Disconnector:手车刀闸_0" y="613.0909090909091" zvalue="1113"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450250014725" ObjectName="35kVⅡ段母线电压互感器3902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450250014725"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1518.64,629.455) scale(0.909091,0.909091) translate(151.227,61.3091)" width="14" x="1512.272727272727" y="613.0909090909091"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="133">
   <use class="kv220" height="30" transform="rotate(0,1062.44,398.833) scale(1.5,1.5) translate(-344.148,-125.444)" width="40" x="1032.444748360066" xlink:href="#Accessory:五绕组带地刀PT_0" y="376.3333333333333" zvalue="786"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450234875909" ObjectName="220kVⅠ母电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1062.44,398.833) scale(1.5,1.5) translate(-344.148,-125.444)" width="40" x="1032.444748360066" y="376.3333333333333"/></g>
  <g id="158">
   <use class="kv35" height="26" transform="rotate(0,1249.94,541.396) scale(1.28654,1.28654) translate(-276.667,-116.855)" width="12" x="1242.217739929999" xlink:href="#Accessory:接地变接地设备_0" y="524.6706349206348" zvalue="858"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450235924485" ObjectName="35kV接地电阻柜"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1249.94,541.396) scale(1.28654,1.28654) translate(-276.667,-116.855)" width="12" x="1242.217739929999" y="524.6706349206348"/></g>
  <g id="221">
   <use class="kv35" height="21" transform="rotate(0,939,786) scale(1,1) translate(0,0)" width="8" x="935" xlink:href="#Accessory:中间电缆_0" y="775.5" zvalue="1017"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450247917573" ObjectName="#1站用变电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,939,786) scale(1,1) translate(0,0)" width="8" x="935" y="775.5"/></g>
  <g id="223">
   <use class="kv35" height="21" transform="rotate(0,855.5,786) scale(1,1) translate(0,0)" width="8" x="851.5" xlink:href="#Accessory:中间电缆_0" y="775.5000000000001" zvalue="1020"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450247983109" ObjectName="#4集电线路太平片区4UL电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,855.5,786) scale(1,1) translate(0,0)" width="8" x="851.5" y="775.5000000000001"/></g>
  <g id="230">
   <use class="kv35" height="21" transform="rotate(0,756.5,786) scale(1,1) translate(0,0)" width="8" x="752.5" xlink:href="#Accessory:中间电缆_0" y="775.5000000000001" zvalue="1032"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450248048645" ObjectName="#3集电线路太平片区3UL电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,756.5,786) scale(1,1) translate(0,0)" width="8" x="752.5" y="775.5000000000001"/></g>
  <g id="242">
   <use class="kv35" height="21" transform="rotate(0,657.5,786) scale(1,1) translate(0,0)" width="8" x="653.5" xlink:href="#Accessory:中间电缆_0" y="775.5000000000001" zvalue="1044"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450248310789" ObjectName="#2集电线路太平片区2UL电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,657.5,786) scale(1,1) translate(0,0)" width="8" x="653.5" y="775.5000000000001"/></g>
  <g id="256">
   <use class="kv35" height="21" transform="rotate(0,554.5,786) scale(1,1) translate(0,0)" width="8" x="550.5" xlink:href="#Accessory:中间电缆_0" y="775.5000000000001" zvalue="1056"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450248572933" ObjectName="#1集电线路太平片区1UL电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,554.5,786) scale(1,1) translate(0,0)" width="8" x="550.5" y="775.5000000000001"/></g>
  <g id="267">
   <use class="kv35" height="21" transform="rotate(0,1267.5,786) scale(1,1) translate(0,0)" width="8" x="1263.5" xlink:href="#Accessory:中间电缆_0" y="775.5000000000001" zvalue="1068"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450248835077" ObjectName="#5集电线路铜避关片区A回电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,1267.5,786) scale(1,1) translate(0,0)" width="8" x="1263.5" y="775.5000000000001"/></g>
  <g id="279">
   <use class="kv35" height="21" transform="rotate(0,1356.5,786) scale(1,1) translate(0,0)" width="8" x="1352.5" xlink:href="#Accessory:中间电缆_0" y="775.5000000000001" zvalue="1080"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450249097221" ObjectName="#6集电线路铜避关片区B回电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,1356.5,786) scale(1,1) translate(0,0)" width="8" x="1352.5" y="775.5000000000001"/></g>
  <g id="290">
   <use class="kv35" height="21" transform="rotate(0,1449.5,786) scale(1,1) translate(0,0)" width="8" x="1445.5" xlink:href="#Accessory:中间电缆_0" y="775.5000000000001" zvalue="1092"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450249359365" ObjectName="#7集电线路铜避关片区C回电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,1449.5,786) scale(1,1) translate(0,0)" width="8" x="1445.5" y="775.5000000000001"/></g>
  <g id="307">
   <use class="kv35" height="21" transform="rotate(0,1538.5,786) scale(1,1) translate(0,0)" width="8" x="1534.5" xlink:href="#Accessory:中间电缆_0" y="775.5000000000001" zvalue="1104"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450249621509" ObjectName="#8集电线路铜避关片区D回电缆"/>
   </metadata>
  <rect fill="white" height="21" opacity="0" stroke="white" transform="rotate(0,1538.5,786) scale(1,1) translate(0,0)" width="8" x="1534.5" y="775.5000000000001"/></g>
  <g id="314">
   <use class="kv35" height="42" transform="rotate(0,824.268,567.636) scale(1.21212,1.21212) translate(-141.065,-94.8818)" width="30" x="806.0859088588364" xlink:href="#Accessory:5卷PT带容断器_0" y="542.1818181818181" zvalue="1106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450249883653" ObjectName="35kVⅠ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,824.268,567.636) scale(1.21212,1.21212) translate(-141.065,-94.8818)" width="30" x="806.0859088588364" y="542.1818181818181"/></g>
  <g id="325">
   <use class="kv35" height="42" transform="rotate(0,1530.18,566.727) scale(1.21212,1.21212) translate(-264.599,-94.7227)" width="30" x="1511.994999767928" xlink:href="#Accessory:5卷PT带容断器_0" y="541.2727272727273" zvalue="1112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450250080261" ObjectName="35kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1530.18,566.727) scale(1.21212,1.21212) translate(-264.599,-94.7227)" width="30" x="1511.994999767928" y="541.2727272727273"/></g>
  <g id="328">
   <use class="kv220" height="20" transform="rotate(270,1344.29,455.143) scale(-1,1.42857) translate(-2688.57,-132.257)" width="20" x="1334.285714285714" xlink:href="#Accessory:线路PT3_0" y="440.8571428571429" zvalue="1117"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450250145797" ObjectName="#1主变220kV避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1344.29,455.143) scale(-1,1.42857) translate(-2688.57,-132.257)" width="20" x="1334.285714285714" y="440.8571428571429"/></g>
  <g id="330">
   <use class="kv35" height="20" transform="rotate(90,1078.57,509.429) scale(1,1.42857) translate(-1.42362e-12,-148.543)" width="20" x="1068.571428571428" xlink:href="#Accessory:线路PT3_0" y="495.1428571428571" zvalue="1120"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450250211333" ObjectName="#1主变35kV避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(90,1078.57,509.429) scale(1,1.42857) translate(-1.42362e-12,-148.543)" width="20" x="1068.571428571428" y="495.1428571428571"/></g>
 </g>
 <g id="BreakerClass">
  <g id="106">
   <use class="kv220" height="20" transform="rotate(0,1172.8,196.016) scale(1.71024,1.55476) translate(-483.495,-64.3937)" width="10" x="1164.246428571429" xlink:href="#Breaker:开关_0" y="180.4682539682536" zvalue="817"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924583948292" ObjectName="220kV农盈线252断路器"/>
   <cge:TPSR_Ref TObjectID="6473924583948292"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1172.8,196.016) scale(1.71024,1.55476) translate(-483.495,-64.3937)" width="10" x="1164.246428571429" y="180.4682539682536"/></g>
  <g id="143">
   <use class="kv220" height="20" transform="rotate(0,1322.17,358.141) scale(1.71024,1.55476) translate(-545.529,-122.242)" width="10" x="1313.621428571428" xlink:href="#Breaker:开关_0" y="342.5932539682536" zvalue="842"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924584013828" ObjectName="#1主变220kV侧201断路器"/>
   <cge:TPSR_Ref TObjectID="6473924584013828"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1322.17,358.141) scale(1.71024,1.55476) translate(-545.529,-122.242)" width="10" x="1313.621428571428" y="342.5932539682536"/></g>
  <g id="49">
   <use class="kv220" height="20" transform="rotate(0,909.798,196.016) scale(1.71024,1.55476) translate(-374.275,-64.3937)" width="10" x="901.2464285714285" xlink:href="#Breaker:开关_0" y="180.4682539682536" zvalue="910"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924585586692" ObjectName="220kV备用线252断路器"/>
   <cge:TPSR_Ref TObjectID="6473924585586692"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,909.798,196.016) scale(1.71024,1.55476) translate(-374.275,-64.3937)" width="10" x="901.2464285714285" y="180.4682539682536"/></g>
  <g id="77">
   <use class="kv35" height="20" transform="rotate(0,1044.36,624.56) scale(2.13845,1.94405) translate(-550.298,-293.851)" width="10" x="1033.671031746032" xlink:href="#Breaker:小车断路器_0" y="605.1190476190475" zvalue="948"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924585652228" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924585652228"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1044.36,624.56) scale(2.13845,1.94405) translate(-550.298,-293.851)" width="10" x="1033.671031746032" y="605.1190476190475"/></g>
  <g id="87">
   <use class="kv35" height="20" transform="rotate(0,1159.58,628.996) scale(2.13845,1.94405) translate(-611.636,-296.006)" width="10" x="1148.888888888889" xlink:href="#Breaker:小车断路器_0" y="609.5555555555555" zvalue="952"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924585717764" ObjectName="#1主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473924585717764"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1159.58,628.996) scale(2.13845,1.94405) translate(-611.636,-296.006)" width="10" x="1148.888888888889" y="609.5555555555555"/></g>
  <g id="112">
   <use class="kv35" height="20" transform="rotate(0,1036.07,704.663) scale(2.13845,1.94405) translate(-545.883,-332.75)" width="10" x="1025.37926013668" xlink:href="#Breaker:小车断路器_0" y="685.2222222222223" zvalue="958"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924585783300" ObjectName="#1SVG366断路器"/>
   <cge:TPSR_Ref TObjectID="6473924585783300"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1036.07,704.663) scale(2.13845,1.94405) translate(-545.883,-332.75)" width="10" x="1025.37926013668" y="685.2222222222223"/></g>
  <g id="200">
   <use class="kv35" height="20" transform="rotate(0,1168.44,705.663) scale(2.13845,1.94405) translate(-616.355,-333.236)" width="10" x="1157.752182539682" xlink:href="#Breaker:小车断路器_0" y="686.2222222222223" zvalue="983"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924585848836" ObjectName="#2SVG375断路器"/>
   <cge:TPSR_Ref TObjectID="6473924585848836"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1168.44,705.663) scale(2.13845,1.94405) translate(-616.355,-333.236)" width="10" x="1157.752182539682" y="686.2222222222223"/></g>
  <g id="207">
   <use class="kv35" height="20" transform="rotate(0,938.692,705.44) scale(2.13845,1.94405) translate(-494.041,-333.128)" width="10" x="928" xlink:href="#Breaker:小车断路器_0" y="686" zvalue="1000"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924585914372" ObjectName="#1站用变365断路器"/>
   <cge:TPSR_Ref TObjectID="6473924585914372"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,938.692,705.44) scale(2.13845,1.94405) translate(-494.041,-333.128)" width="10" x="928" y="686"/></g>
  <g id="220">
   <use class="kv35" height="20" transform="rotate(0,855.692,705.44) scale(2.13845,1.94405) translate(-449.854,-333.128)" width="10" x="845" xlink:href="#Breaker:小车断路器_0" y="686" zvalue="1009"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924585979908" ObjectName="#4集电线路太平片区4UL364断路器"/>
   <cge:TPSR_Ref TObjectID="6473924585979908"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,855.692,705.44) scale(2.13845,1.94405) translate(-449.854,-333.128)" width="10" x="845" y="686"/></g>
  <g id="237">
   <use class="kv35" height="20" transform="rotate(0,756.692,705.44) scale(2.13845,1.94405) translate(-397.15,-333.128)" width="10" x="746" xlink:href="#Breaker:小车断路器_0" y="686" zvalue="1023"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924586045444" ObjectName="#3集电线路太平片区3UL363断路器"/>
   <cge:TPSR_Ref TObjectID="6473924586045444"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,756.692,705.44) scale(2.13845,1.94405) translate(-397.15,-333.128)" width="10" x="746" y="686"/></g>
  <g id="251">
   <use class="kv35" height="20" transform="rotate(0,657.692,705.44) scale(2.13845,1.94405) translate(-344.445,-333.128)" width="10" x="647" xlink:href="#Breaker:小车断路器_0" y="686" zvalue="1035"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924586110980" ObjectName="#2集电线路太平片区2UL362断路器"/>
   <cge:TPSR_Ref TObjectID="6473924586110980"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,657.692,705.44) scale(2.13845,1.94405) translate(-344.445,-333.128)" width="10" x="647" y="686"/></g>
  <g id="262">
   <use class="kv35" height="20" transform="rotate(0,554.692,705.44) scale(2.13845,1.94405) translate(-289.61,-333.128)" width="10" x="544" xlink:href="#Breaker:小车断路器_0" y="686" zvalue="1047"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924586176516" ObjectName="#1集电线路太平片区1UL361断路器"/>
   <cge:TPSR_Ref TObjectID="6473924586176516"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,554.692,705.44) scale(2.13845,1.94405) translate(-289.61,-333.128)" width="10" x="544" y="686"/></g>
  <g id="273">
   <use class="kv35" height="20" transform="rotate(0,1267.69,705.44) scale(2.13845,1.94405) translate(-669.192,-333.128)" width="10" x="1257" xlink:href="#Breaker:小车断路器_0" y="686" zvalue="1059"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924586242052" ObjectName="#5集电线路铜避关片区A回371断路器"/>
   <cge:TPSR_Ref TObjectID="6473924586242052"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1267.69,705.44) scale(2.13845,1.94405) translate(-669.192,-333.128)" width="10" x="1257" y="686"/></g>
  <g id="285">
   <use class="kv35" height="20" transform="rotate(0,1356.69,705.44) scale(2.13845,1.94405) translate(-716.573,-333.128)" width="10" x="1346" xlink:href="#Breaker:小车断路器_0" y="686" zvalue="1071"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924586307588" ObjectName="#6集电线路铜避关片区B回372断路器"/>
   <cge:TPSR_Ref TObjectID="6473924586307588"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1356.69,705.44) scale(2.13845,1.94405) translate(-716.573,-333.128)" width="10" x="1346" y="686"/></g>
  <g id="302">
   <use class="kv35" height="20" transform="rotate(0,1449.69,705.44) scale(2.13845,1.94405) translate(-766.083,-333.128)" width="10" x="1439" xlink:href="#Breaker:小车断路器_0" y="686" zvalue="1083"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924586373124" ObjectName="#7集电线路铜避关片区C回373断路器"/>
   <cge:TPSR_Ref TObjectID="6473924586373124"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1449.69,705.44) scale(2.13845,1.94405) translate(-766.083,-333.128)" width="10" x="1439" y="686"/></g>
  <g id="313">
   <use class="kv35" height="20" transform="rotate(0,1538.69,705.44) scale(2.13845,1.94405) translate(-813.465,-333.128)" width="10" x="1528" xlink:href="#Breaker:小车断路器_0" y="686" zvalue="1095"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924586438660" ObjectName="#8集电线路铜避关片区D回374断路器"/>
   <cge:TPSR_Ref TObjectID="6473924586438660"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1538.69,705.44) scale(2.13845,1.94405) translate(-813.465,-333.128)" width="10" x="1528" y="686"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="103">
   <use class="kv220" height="45" transform="rotate(0,1174,78.127) scale(1.08401,0.987654) translate(-89.2626,0.69881)" width="41" x="1151.77568969926" xlink:href="#ACLineSegment:农业光伏_0" y="55.90476190476176" zvalue="820"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249327796227" ObjectName="220kV农盈线"/>
   <cge:TPSR_Ref TObjectID="8444249327796227_5066549595930626"/></metadata>
  <rect fill="white" height="45" opacity="0" stroke="white" transform="rotate(0,1174,78.127) scale(1.08401,0.987654) translate(-89.2626,0.69881)" width="41" x="1151.77568969926" y="55.90476190476176"/></g>
 </g>
 <g id="PowerTransformer3Class">
  <g id="151">
   <g id="1510">
    <use class="kv220" height="30" transform="rotate(0,1304.26,516.778) scale(-3.17222,3.17222) translate(-1682.83,-321.287)" width="30" x="1256.68" xlink:href="#PowerTransformer3:可调Y-Y-Y带4卷_0" y="469.19" zvalue="853"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874465607683" ObjectName="220"/>
    </metadata>
   </g>
   <g id="1511">
    <use class="kv110" height="30" transform="rotate(0,1304.26,516.778) scale(-3.17222,3.17222) translate(-1682.83,-321.287)" width="30" x="1256.68" xlink:href="#PowerTransformer3:可调Y-Y-Y带4卷_1" y="469.19" zvalue="853"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874465673219" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1512">
    <use class="kv35" height="30" transform="rotate(0,1304.26,516.778) scale(-3.17222,3.17222) translate(-1682.83,-321.287)" width="30" x="1256.68" xlink:href="#PowerTransformer3:可调Y-Y-Y带4卷_2" y="469.19" zvalue="853"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874465738755" ObjectName="35"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399464452099" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399464452099"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1304.26,516.778) scale(-3.17222,3.17222) translate(-1682.83,-321.287)" width="30" x="1256.68" y="469.19"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="175">
   <use class="kv110" height="30" transform="rotate(90,1346.61,601.905) scale(0.595238,0.587302) translate(913.264,416.77)" width="12" x="1343.035714285714" xlink:href="#EnergyConsumer:负荷_0" y="593.0952380952381" zvalue="874"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450236383237" ObjectName="至110kV设备"/>
   <cge:TPSR_Ref TObjectID="6192450236383237"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,1346.61,601.905) scale(0.595238,0.587302) translate(913.264,416.77)" width="12" x="1343.035714285714" y="593.0952380952381"/></g>
  <g id="209">
   <use class="kv35" height="39" transform="rotate(0,943.558,863.25) scale(1.26923,1.26923) translate(-197.052,-177.864)" width="23" x="928.9614926739927" xlink:href="#EnergyConsumer:站用变D-Y_0" y="838.5" zvalue="1007"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450247655429" ObjectName="#1站用变"/>
   <cge:TPSR_Ref TObjectID="6192450247655429"/></metadata>
  <rect fill="white" height="39" opacity="0" stroke="white" transform="rotate(0,943.558,863.25) scale(1.26923,1.26923) translate(-197.052,-177.864)" width="23" x="928.9614926739927" y="838.5"/></g>
  <g id="308">
   <use class="kv35" height="30" transform="rotate(0,1538.69,871.247) scale(1.64664,-1.11685) translate(-600.369,-1649.59)" width="12" x="1528.812419871795" xlink:href="#EnergyConsumer:负荷_0" y="854.4944496154785" zvalue="1102"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450249687045" ObjectName="#8集电线路铜避关片区D回"/>
   <cge:TPSR_Ref TObjectID="6192450249687045"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1538.69,871.247) scale(1.64664,-1.11685) translate(-600.369,-1649.59)" width="12" x="1528.812419871795" y="854.4944496154785"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="93">
   <use class="kv35" height="35" transform="rotate(0,1035.44,918.685) scale(3.19111,2.65926) translate(-683.578,-544.182)" width="25" x="995.5555555555557" xlink:href="#Compensator:SVG20230208_0" y="872.1481481481485" zvalue="955"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450246606853" ObjectName="#1SVG"/>
   <cge:TPSR_Ref TObjectID="6192450246606853"/></metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1035.44,918.685) scale(3.19111,2.65926) translate(-683.578,-544.182)" width="25" x="995.5555555555557" y="872.1481481481485"/></g>
  <g id="201">
   <use class="kv35" height="35" transform="rotate(0,1168.44,915.685) scale(3.19111,2.65926) translate(-774.9,-542.31)" width="25" x="1128.555555555556" xlink:href="#Compensator:SVG20230208_0" y="869.1481481481485" zvalue="981"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450247458821" ObjectName="#2SVG"/>
   <cge:TPSR_Ref TObjectID="6192450247458821"/></metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1168.44,915.685) scale(3.19111,2.65926) translate(-774.9,-542.31)" width="25" x="1128.555555555556" y="869.1481481481485"/></g>
 </g>
</svg>