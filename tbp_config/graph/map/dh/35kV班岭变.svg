<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549679226881" height="1045" id="thSvg" source="NR-PCS9000" viewBox="0 0 1900 1045" width="1900">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:接地可调两卷变（规范制图）_0" viewBox="0,0,15,20">
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.407783003462016" x2="5.342474279636929" y1="6.303393899561708" y2="4.443405305784673"/>
   <use terminal-index="0" type="1" x="7.400350919212375" xlink:href="#terminal" y="2.255340193223605"/>
   <use terminal-index="2" type="2" x="7.5" xlink:href="#terminal" y="6.249999999999999"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="9.546899427352102" x2="7.404066961337196" y1="4.317578864636594" y2="6.303393899561695"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.401384443428335" x2="7.401384443428335" y1="6.310758227164332" y2="8.34034281757342"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.91666666666667" x2="12.74547107366039" y1="3.553047675216829" y2="3.553047675216829"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.083333333333337" x2="12.75" y1="9.046663465350886" y2="3.552242201885312"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.7491871157852" x2="11.75" y1="3.553047675216865" y2="5.083333333333333"/>
   <ellipse cx="7.44" cy="6.46" fill-opacity="0" rx="4.25" ry="4.21" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:接地可调两卷变（规范制图）_1" viewBox="0,0,15,20">
   <path d="M 5.2379 14.7101 L 9.85166 14.7101 L 7.43896 11.0192 z" fill-opacity="0" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <use terminal-index="1" type="1" x="7.396634877087553" xlink:href="#terminal" y="17.07970653458569"/>
   <ellipse cx="7.44" cy="12.87" fill-opacity="0" rx="4.25" ry="4.21" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="ACLineSegment:线路_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="Accessory:线路PT235_0" viewBox="0,0,17,36">
   <use terminal-index="0" type="0" x="8.5" xlink:href="#terminal" y="0.4166666666666643"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,8.5,9.75) scale(1,1) translate(0,0)" width="4" x="6.5" y="6.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="11" y1="21.58333333333333" y2="21.58333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="8.5" y1="17.25" y2="0.25"/>
   <ellipse cx="8.4" cy="22.18" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.4" cy="30.28" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="11" y1="30.33333333333334" y2="30.33333333333334"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变11_0" viewBox="0,0,20,30">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="0.8499999999999996"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="9.25" y2="0.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="17.5" y1="27" y2="21.65"/>
   <ellipse cx="9.880000000000001" cy="13.47" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="9.94" cy="19.46" fill-opacity="0" rx="4.2" ry="4.18" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="10.00951742627347" y1="18.1368007916835" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.6895889186774" x2="10.00951742627347" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="21.48031840279781" y2="19.80855959724065"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.6" x2="15.39982126899018" y1="25.06619780557639" y2="25.06619780557639"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.75996425379804" x2="16.23985701519214" y1="25.90207720835499" y2="25.90207720835499"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.91992850759607" x2="17.07989276139411" y1="26.73795661113357" y2="26.73795661113357"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.00951742627347" x2="17.5" y1="19.80855959724066" y2="19.80855959724066"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.49991063449509" x2="17.49991063449509" y1="19.83333333333333" y2="24.91666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="10.10311503267975" x2="10.10311503267975" y1="26.83333333333333" y2="23.64357429718876"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.0928507596068" x2="10.0928507596068" y1="10.97013412501683" y2="12.64189293057399"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.77292225201073" x2="10.0928507596068" y1="14.31365173613114" y2="12.64189293057398"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.329445933869529" x2="10.00951742627346" y1="14.23031840279781" y2="12.55855959724065"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:间隔模板_0" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(85,170,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
  <symbol id="State:间隔模板_1" viewBox="0,0,80,30">
   <rect Plane="0" fill="none" fill-opacity="0" height="28.8" stroke="rgb(185,185,185)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" transform="rotate(0,40,15) scale(1,1) translate(0,0)" width="79.2" x="0.4" y="0.6"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV班岭变" InitShowingPlane="" fill="rgb(0,0,0)" height="1045" width="1900" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="82.56999999999999" id="1" preserveAspectRatio="xMidYMid slice" width="249.69" x="71.31" xlink:href="logo.png" y="12.93"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="138" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,196.153,54.2136) scale(1,1) translate(-1.58333e-14,0)" writing-mode="lr" x="196.15" xml:space="preserve" y="57.71" zvalue="1155"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,225,54.1903) scale(1,1) translate(0,0)" writing-mode="lr" x="225" xml:space="preserve" y="63.19" zvalue="1156">35kV班岭变</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="228" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,319.438,386.75) scale(1,1) translate(0,0)" width="72.88" x="283" y="374.75" zvalue="1339"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,319.438,386.75) scale(1,1) translate(0,0)" writing-mode="lr" x="319.44" xml:space="preserve" y="391.25" zvalue="1339">小电流接地</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="227" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,217.531,386.75) scale(1,1) translate(0,0)" width="72.88" x="181.09" y="374.75" zvalue="1341"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,217.531,386.75) scale(1,1) translate(0,0)" writing-mode="lr" x="217.53" xml:space="preserve" y="391.25" zvalue="1341">光字巡检</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="226" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,115.625,386.75) scale(1,1) translate(0,0)" width="72.88" x="79.19" y="374.75" zvalue="1342"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,115.625,386.75) scale(1,1) translate(0,0)" writing-mode="lr" x="115.63" xml:space="preserve" y="391.25" zvalue="1342">AVC功能</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="225" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,115.625,346.25) scale(1,1) translate(0,0)" width="72.88" x="79.19" y="334.25" zvalue="1343"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,115.625,346.25) scale(1,1) translate(0,0)" writing-mode="lr" x="115.63" xml:space="preserve" y="350.75" zvalue="1343">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,722.778,331.841) scale(1,1) translate(0,0)" writing-mode="lr" x="722.78" xml:space="preserve" y="336.34" zvalue="7">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="32" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1340.25,137.488) scale(1,1) translate(0,0)" writing-mode="lr" x="1340.25" xml:space="preserve" y="141.99" zvalue="60">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,876.175,214.659) scale(1,1) translate(0,0)" writing-mode="lr" x="876.1799999999999" xml:space="preserve" y="219.16" zvalue="62">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1101.59,108.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1101.59" xml:space="preserve" y="113.25" zvalue="299">35kV弄班线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1128.3,255.582) scale(1,1) translate(0,0)" writing-mode="lr" x="1128.3" xml:space="preserve" y="260.08" zvalue="300">371</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1128.3,200.692) scale(1,1) translate(0,0)" writing-mode="lr" x="1128.3" xml:space="preserve" y="205.19" zvalue="302">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1062.23,183.253) scale(1,1) translate(0,0)" writing-mode="lr" x="1062.23" xml:space="preserve" y="187.75" zvalue="304">7</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1128.3,314.487) scale(1,1) translate(0,0)" writing-mode="lr" x="1128.3" xml:space="preserve" y="318.99" zvalue="307">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1067.23,292.253) scale(1,1) translate(0,0)" writing-mode="lr" x="1067.23" xml:space="preserve" y="296.75" zvalue="316">7</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="104" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1313.84,267.07) scale(1,1) translate(0,3.71749e-13)" writing-mode="lr" x="1313.84" xml:space="preserve" y="271.57" zvalue="354">39017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1313.84,334.07) scale(1,1) translate(0,0)" writing-mode="lr" x="1313.84" xml:space="preserve" y="338.57" zvalue="358">39010</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="307" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1377.12,286.128) scale(1,1) translate(0,0)" writing-mode="lr" x="1377.12" xml:space="preserve" y="290.63" zvalue="568">3901</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="143" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1140,156.873) scale(1,1) translate(0,0)" writing-mode="lr" x="1140" xml:space="preserve" y="161.37" zvalue="727">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="176" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1136.05,604.292) scale(1,1) translate(0,0)" writing-mode="lr" x="1136.05" xml:space="preserve" y="608.79" zvalue="737">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="175" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1131.76,436.62) scale(1,1) translate(0,0)" writing-mode="lr" x="1131.76" xml:space="preserve" y="441.12" zvalue="739">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="168" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1127.22,382.715) scale(1,1) translate(0,0)" writing-mode="lr" x="1127.22" xml:space="preserve" y="387.22" zvalue="741">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="159" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1203.82,514.205) scale(1,1) translate(-7.87584e-13,0)" writing-mode="lr" x="1203.82" xml:space="preserve" y="518.7" zvalue="747">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="158" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1128.02,642.53) scale(1,1) translate(0,4.91963e-13)" writing-mode="lr" x="1128.02" xml:space="preserve" y="647.03" zvalue="752">1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,724.5,667.75) scale(1,1) translate(0,0)" writing-mode="lr" x="724.5" xml:space="preserve" y="672.25" zvalue="791">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="90" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,886.603,839.224) scale(1,1) translate(7.83909e-13,0)" writing-mode="lr" x="886.6" xml:space="preserve" y="843.72" zvalue="872">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,891.739,786.868) scale(1,1) translate(0,0)" writing-mode="lr" x="891.74" xml:space="preserve" y="791.37" zvalue="874">071</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,870.179,947.375) scale(1,1) translate(0,0)" writing-mode="lr" x="870.1799999999999" xml:space="preserve" y="951.88" zvalue="878">10kV围角线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,888.103,743.224) scale(1,1) translate(1.08093e-12,0)" writing-mode="lr" x="888.1" xml:space="preserve" y="747.72" zvalue="882">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,901.434,643.846) scale(1,1) translate(0,4.92986e-13)" writing-mode="lr" x="901.4299999999999" xml:space="preserve" y="648.35" zvalue="979">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,876.925,516.25) scale(1,1) translate(0,0)" writing-mode="lr" x="876.9299999999999" xml:space="preserve" y="520.75" zvalue="983">10kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,837.357,630.003) scale(1,1) translate(-3.63202e-13,0)" writing-mode="lr" x="837.36" xml:space="preserve" y="634.5" zvalue="1087">09017</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1067,422.981) scale(1,1) translate(0,0)" writing-mode="lr" x="1067" xml:space="preserve" y="427.48" zvalue="1093">7</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1043.6,839.224) scale(1,1) translate(0,0)" writing-mode="lr" x="1043.6" xml:space="preserve" y="843.72" zvalue="1097">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1048.74,786.868) scale(1,1) translate(0,0)" writing-mode="lr" x="1048.74" xml:space="preserve" y="791.37" zvalue="1099">072</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1027.18,947.375) scale(1,1) translate(0,0)" writing-mode="lr" x="1027.18" xml:space="preserve" y="951.88" zvalue="1103">10kV等武线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1045.1,743.224) scale(1,1) translate(0,0)" writing-mode="lr" x="1045.1" xml:space="preserve" y="747.72" zvalue="1107">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,853.565,892.981) scale(1,1) translate(0,0)" writing-mode="lr" x="853.5700000000001" xml:space="preserve" y="897.48" zvalue="1127">7</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1010.57,892.981) scale(1,1) translate(0,0)" writing-mode="lr" x="1010.57" xml:space="preserve" y="897.48" zvalue="1130">7</text>
  <line fill="none" id="135" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.00000000000045" x2="388" y1="139.3704926140824" y2="139.3704926140824" zvalue="1158"/>
  <line fill="none" id="134" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="392.75" x2="392.75" y1="2.5" y2="1032.5" zvalue="1159"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="203" y1="151.5" y2="151.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="203" y1="177.5" y2="177.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="22" y1="151.5" y2="177.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="203" y1="151.5" y2="177.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="384" y1="151.5" y2="151.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="384" y1="177.5" y2="177.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="203" y1="151.5" y2="177.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="384" x2="384" y1="151.5" y2="177.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="203" y1="177.5" y2="177.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="203" y1="201.75" y2="201.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="22" y1="177.5" y2="201.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="203" y1="177.5" y2="201.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="384" y1="177.5" y2="177.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="384" y1="201.75" y2="201.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="203" y1="177.5" y2="201.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="384" x2="384" y1="177.5" y2="201.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="203" y1="201.75" y2="201.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="203" y1="224.5" y2="224.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="22" y1="201.75" y2="224.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="203" y1="201.75" y2="224.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="384" y1="201.75" y2="201.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="384" y1="224.5" y2="224.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="203" y1="201.75" y2="224.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="384" x2="384" y1="201.75" y2="224.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="203" y1="224.5" y2="224.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="203" y1="247.25" y2="247.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="22" y1="224.5" y2="247.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="203" y1="224.5" y2="247.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="384" y1="224.5" y2="224.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="384" y1="247.25" y2="247.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="203" y1="224.5" y2="247.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="384" x2="384" y1="224.5" y2="247.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="203" y1="247.25" y2="247.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="203" y1="270" y2="270"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="22" x2="22" y1="247.25" y2="270"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="203" y1="247.25" y2="270"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="384" y1="247.25" y2="247.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="384" y1="270" y2="270"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="203" x2="203" y1="247.25" y2="270"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="384" x2="384" y1="247.25" y2="270"/>
  <line fill="none" id="106" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.00000000000045" x2="388" y1="609.3704926140824" y2="609.3704926140824" zvalue="1161"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="431.5" y2="431.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="469.7823" y2="469.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="73" y1="431.5" y2="469.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="431.5" y2="469.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="431.5" y2="431.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="469.7823" y2="469.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="431.5" y2="469.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="431.5" y2="469.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="431.5" y2="431.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="469.7823" y2="469.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="431.5" y2="469.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3873" x2="236.3873" y1="431.5" y2="469.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="431.5" y2="431.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="469.7823" y2="469.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="236.3872" y1="431.5" y2="469.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="431.5" y2="469.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="431.5" y2="431.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="469.7823" y2="469.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="431.5" y2="469.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354" x2="354" y1="431.5" y2="469.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="469.7823" y2="469.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="494.4617" y2="494.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="73" y1="469.7823" y2="494.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="469.7823" y2="494.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="469.7823" y2="469.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="494.4617" y2="494.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="469.7823" y2="494.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="469.7823" y2="494.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="469.7823" y2="469.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="494.4617" y2="494.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="469.7823" y2="494.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3873" x2="236.3873" y1="469.7823" y2="494.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="469.7823" y2="469.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="494.4617" y2="494.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="236.3872" y1="469.7823" y2="494.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="469.7823" y2="494.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="469.7823" y2="469.7823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="494.4617" y2="494.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="469.7823" y2="494.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354" x2="354" y1="469.7823" y2="494.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="494.4617" y2="494.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="519.1411000000001" y2="519.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="73" y1="494.4617" y2="519.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="494.4617" y2="519.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="494.4617" y2="494.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="519.1411000000001" y2="519.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="494.4617" y2="519.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="494.4617" y2="519.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="494.4617" y2="494.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="519.1411000000001" y2="519.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="494.4617" y2="519.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3873" x2="236.3873" y1="494.4617" y2="519.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="494.4617" y2="494.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="519.1411000000001" y2="519.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="236.3872" y1="494.4617" y2="519.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="494.4617" y2="519.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="494.4617" y2="494.4617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="519.1411000000001" y2="519.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="494.4617" y2="519.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354" x2="354" y1="494.4617" y2="519.1411000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="519.1410999999999" y2="519.1410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="543.8205" y2="543.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="73" y1="519.1410999999999" y2="543.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="519.1410999999999" y2="543.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="519.1410999999999" y2="519.1410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="543.8205" y2="543.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="519.1410999999999" y2="543.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="519.1410999999999" y2="543.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="519.1410999999999" y2="519.1410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="543.8205" y2="543.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="519.1410999999999" y2="543.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3873" x2="236.3873" y1="519.1410999999999" y2="543.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="519.1410999999999" y2="519.1410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="543.8205" y2="543.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="236.3872" y1="519.1410999999999" y2="543.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="519.1410999999999" y2="543.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="519.1410999999999" y2="519.1410999999999"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="543.8205" y2="543.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="519.1410999999999" y2="543.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354" x2="354" y1="519.1410999999999" y2="543.8205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="543.8206" y2="543.8206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="568.5" y2="568.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="73" y1="543.8206" y2="568.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="543.8206" y2="568.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="543.8206" y2="543.8206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="568.5" y2="568.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="543.8206" y2="568.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="543.8206" y2="568.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="543.8206" y2="543.8206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="568.5" y2="568.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="543.8206" y2="568.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3873" x2="236.3873" y1="543.8206" y2="568.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="543.8206" y2="543.8206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="568.5" y2="568.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="236.3872" y1="543.8206" y2="568.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="543.8206" y2="568.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="543.8206" y2="543.8206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="568.5" y2="568.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="543.8206" y2="568.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354" x2="354" y1="543.8206" y2="568.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="568.5" y2="568.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="118.7745" y1="593.1794" y2="593.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="73" x2="73" y1="568.5" y2="593.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="568.5" y2="593.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="568.5" y2="568.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="177.5809" y1="593.1794" y2="593.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="118.7745" x2="118.7745" y1="568.5" y2="593.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="568.5" y2="593.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="568.5" y2="568.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="236.3873" y1="593.1794" y2="593.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="177.5809" x2="177.5809" y1="568.5" y2="593.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3873" x2="236.3873" y1="568.5" y2="593.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="568.5" y2="568.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="295.1936000000001" y1="593.1794" y2="593.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="236.3872" x2="236.3872" y1="568.5" y2="593.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="568.5" y2="593.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="568.5" y2="568.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="354" y1="593.1794" y2="593.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="295.1936000000001" x2="295.1936000000001" y1="568.5" y2="593.1794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="354" x2="354" y1="568.5" y2="593.1794"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="21" x2="111" y1="924.5" y2="924.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="21" x2="111" y1="963.6632999999999" y2="963.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="21" x2="21" y1="924.5" y2="963.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="111" y1="924.5" y2="963.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="381" y1="924.5" y2="924.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="381" y1="963.6632999999999" y2="963.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="111" y1="924.5" y2="963.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="381" x2="381" y1="924.5" y2="963.6632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="21" x2="111" y1="963.66327" y2="963.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="21" x2="111" y1="991.58167" y2="991.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="21" x2="21" y1="963.66327" y2="991.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="111" y1="963.66327" y2="991.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="201" y1="963.66327" y2="963.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="201" y1="991.58167" y2="991.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="111" y1="963.66327" y2="991.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="201" x2="201" y1="963.66327" y2="991.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="201.0000000000001" x2="291.0000000000001" y1="963.66327" y2="963.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="201.0000000000001" x2="291.0000000000001" y1="991.58167" y2="991.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="201.0000000000001" x2="201.0000000000001" y1="963.66327" y2="991.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="291.0000000000001" x2="291.0000000000001" y1="963.66327" y2="991.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="291" x2="381" y1="963.66327" y2="963.66327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="291" x2="381" y1="991.58167" y2="991.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="291" x2="291" y1="963.66327" y2="991.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="381" x2="381" y1="963.66327" y2="991.58167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="21" x2="111" y1="991.5816" y2="991.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="21" x2="111" y1="1019.5" y2="1019.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="21" x2="21" y1="991.5816" y2="1019.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="111" y1="991.5816" y2="1019.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="201" y1="991.5816" y2="991.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="201" y1="1019.5" y2="1019.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="111" x2="111" y1="991.5816" y2="1019.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="201" x2="201" y1="991.5816" y2="1019.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="201.0000000000001" x2="291.0000000000001" y1="991.5816" y2="991.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="201.0000000000001" x2="291.0000000000001" y1="1019.5" y2="1019.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="201.0000000000001" x2="201.0000000000001" y1="991.5816" y2="1019.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="291.0000000000001" x2="291.0000000000001" y1="991.5816" y2="1019.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="291" x2="381" y1="991.5816" y2="991.5816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="291" x2="381" y1="1019.5" y2="1019.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="291" x2="291" y1="991.5816" y2="1019.5"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="381" x2="381" y1="991.5816" y2="1019.5"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="93" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,66,944.5) scale(1,1) translate(0,0)" writing-mode="lr" x="66" xml:space="preserve" y="950.5" zvalue="1165">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="92" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63,978.5) scale(1,1) translate(0,0)" writing-mode="lr" x="63" xml:space="preserve" y="984.5" zvalue="1166">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="91" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,246,979.5) scale(1,1) translate(0,0)" writing-mode="lr" x="246" xml:space="preserve" y="985.5" zvalue="1167">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63,1007.5) scale(1,1) translate(0,0)" writing-mode="lr" x="63" xml:space="preserve" y="1013.5" zvalue="1168">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,245,1007.5) scale(1,1) translate(0,0)" writing-mode="lr" x="245" xml:space="preserve" y="1013.5" zvalue="1169">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" x="149.265625" xml:space="preserve" y="447.8993055555555" zvalue="1170">35kV 母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="149.265625" xml:space="preserve" y="464.8993055555555" zvalue="1170">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.5,639) scale(1,1) translate(0,0)" writing-mode="lr" x="86.5" xml:space="preserve" y="643.5" zvalue="1172">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,219.399,306.341) scale(1,1) translate(0,0)" writing-mode="lr" x="219.4" xml:space="preserve" y="310.84" zvalue="1173">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,324.399,306.341) scale(1,1) translate(0,0)" writing-mode="lr" x="324.4" xml:space="preserve" y="310.84" zvalue="1174">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" x="265.96875" xml:space="preserve" y="447.3368055555555" zvalue="1175">10kV 母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="265.96875" xml:space="preserve" y="464.3368055555555" zvalue="1175">线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,98,482.25) scale(1,1) translate(0,0)" writing-mode="lr" x="98" xml:space="preserve" y="486.75" zvalue="1177">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,98,507.75) scale(1,1) translate(0,0)" writing-mode="lr" x="98" xml:space="preserve" y="512.25" zvalue="1178">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,98,530.75) scale(1,1) translate(0,0)" writing-mode="lr" x="98" xml:space="preserve" y="535.25" zvalue="1179">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="61" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,98,553.75) scale(1,1) translate(0,0)" writing-mode="lr" x="98" xml:space="preserve" y="558.25" zvalue="1180">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,98,580.75) scale(1,1) translate(0,0)" writing-mode="lr" x="98" xml:space="preserve" y="585.25" zvalue="1181">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,249.027,946.5) scale(1,1) translate(0,0)" writing-mode="lr" x="249.03" xml:space="preserve" y="952.5" zvalue="1182">BanLing-01-2024</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="58" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,156.054,978.5) scale(1,1) translate(0,0)" writing-mode="lr" x="156.05" xml:space="preserve" y="984.5" zvalue="1183">许方杰</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,337.054,979.5) scale(1,1) translate(0,0)" writing-mode="lr" x="337.05" xml:space="preserve" y="985.5" zvalue="1184">20240117</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60,165.5) scale(1,1) translate(0,0)" writing-mode="lr" x="60" xml:space="preserve" y="170" zvalue="1185">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240,165.5) scale(1,1) translate(0,0)" writing-mode="lr" x="240" xml:space="preserve" y="170" zvalue="1186">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63.6875,189.75) scale(1,1) translate(0,0)" writing-mode="lr" x="63.69" xml:space="preserve" y="194.25" zvalue="1187">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.1875,237.5) scale(1,1) translate(0,0)" writing-mode="lr" x="67.19" xml:space="preserve" y="242" zvalue="1188">1号主变油温</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,67.1875,260.5) scale(1,1) translate(0,0)" writing-mode="lr" x="67.19" xml:space="preserve" y="265" zvalue="1190">1号主变档位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,64.6875,213.75) scale(1,1) translate(0,0)" writing-mode="lr" x="64.69" xml:space="preserve" y="218.25" zvalue="1192">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="160" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1193.5,537) scale(1,1) translate(0,0)" writing-mode="lr" x="1193.5" xml:space="preserve" y="541.5" zvalue="1223">(SZ11-5000/35)</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1205.92,839.974) scale(1,1) translate(4.27009e-12,0)" writing-mode="lr" x="1205.92" xml:space="preserve" y="844.47" zvalue="1231">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1211.06,787.618) scale(1,1) translate(0,0)" writing-mode="lr" x="1211.06" xml:space="preserve" y="792.12" zvalue="1233">073</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1189.5,948.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1189.5" xml:space="preserve" y="952.63" zvalue="1237">10kV木瓜坝线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="29" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1207.42,743.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1207.42" xml:space="preserve" y="748.47" zvalue="1241">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="21" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1172.89,893.731) scale(1,1) translate(0,0)" writing-mode="lr" x="1172.89" xml:space="preserve" y="898.23" zvalue="1245">7</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="177" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1369.92,838.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1369.92" xml:space="preserve" y="843.47" zvalue="1249">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="174" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1375.06,786.618) scale(1,1) translate(0,0)" writing-mode="lr" x="1375.06" xml:space="preserve" y="791.12" zvalue="1251">074</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="173" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1353.5,947.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1353.5" xml:space="preserve" y="951.63" zvalue="1255">10kV三家村线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1371.42,742.974) scale(1,1) translate(0,0)" writing-mode="lr" x="1371.42" xml:space="preserve" y="747.47" zvalue="1259">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="169" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1336.89,892.731) scale(1,1) translate(0,0)" writing-mode="lr" x="1336.89" xml:space="preserve" y="897.23" zvalue="1263">7</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="199" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,873,189.5) scale(1,1) translate(0,0)" writing-mode="lr" x="873" xml:space="preserve" y="194" zvalue="1266">(S9-30/35)</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="183" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,115.286,306.039) scale(1,1) translate(0,3.27449e-13)" writing-mode="lr" x="115.2857055664062" xml:space="preserve" y="310.5388641357421" zvalue="1350">全站公用</text>
 </g>
 <g id="ButtonClass">
  <g href="小电流装置20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="283" y="374.75" zvalue="1339"/></g>
  <g href="全站巡检20210708.svg"><rect fill-opacity="0" height="24" width="72.88" x="181.09" y="374.75" zvalue="1341"/></g>
  <g href="单厂站信息-20210617.svg"><rect fill-opacity="0" height="24" width="72.88" x="79.19" y="374.75" zvalue="1342"/></g>
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="79.19" y="334.25" zvalue="1343"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="128">
   <path class="kv35" d="M 735.5 344.84 L 1489.98 344.84" stroke-width="6" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674400075779" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674400075779"/></metadata>
  <path d="M 735.5 344.84 L 1489.98 344.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="542">
   <path class="kv10" d="M 731.43 688.75 L 1502.86 688.75" stroke-width="4" zvalue="790"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674400141315" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674400141315"/></metadata>
  <path d="M 731.43 688.75 L 1502.86 688.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="218">
   <use class="kv35" height="18" transform="rotate(0,1343.26,183.17) scale(-2.74557,-2.66175) translate(-1819.41,-237.031)" width="15" x="1322.663743358187" xlink:href="#Accessory:PT8_0" y="159.2147407557327" zvalue="59"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453803311107" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1343.26,183.17) scale(-2.74557,-2.66175) translate(-1819.41,-237.031)" width="15" x="1322.663743358187" y="159.2147407557327"/></g>
  <g id="35">
   <use class="kv35" height="36" transform="rotate(270,1193.09,170.312) scale(2.03589,1.24741) translate(-598.256,-29.3264)" width="17" x="1175.785013520182" xlink:href="#Accessory:线路PT235_0" y="147.8587951832262" zvalue="319"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453803900931" ObjectName="35kV弄班线3719PT"/>
   </metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(270,1193.09,170.312) scale(2.03589,1.24741) translate(-598.256,-29.3264)" width="17" x="1175.785013520182" y="147.8587951832262"/></g>
  <g id="477">
   <use class="kv10" height="26" transform="rotate(0,895.959,899.969) scale(-0.838049,0.927421) translate(-1966.03,69.4871)" width="12" x="890.9303009724933" xlink:href="#Accessory:避雷器1_0" y="887.9126333680678" zvalue="875"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453805015043" ObjectName="10kV围角线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,895.959,899.969) scale(-0.838049,0.927421) translate(-1966.03,69.4871)" width="12" x="890.9303009724933" y="887.9126333680678"/></g>
  <g id="245">
   <use class="kv10" height="18" transform="rotate(180,876.306,568.992) scale(-3.11737,3.19532) translate(-1141.53,-371.164)" width="15" x="852.9252689688683" xlink:href="#Accessory:PT8_0" y="540.2342222269656" zvalue="982"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453804752899" ObjectName="10kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(180,876.306,568.992) scale(-3.11737,3.19532) translate(-1141.53,-371.164)" width="15" x="852.9252689688683" y="540.2342222269656"/></g>
  <g id="73">
   <use class="kv10" height="26" transform="rotate(0,1052.96,899.969) scale(-0.838049,0.927421) translate(-2310.37,69.4871)" width="12" x="1047.930300972493" xlink:href="#Accessory:避雷器1_0" y="887.9126333680678" zvalue="1100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453805670403" ObjectName="10kV等武线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1052.96,899.969) scale(-0.838049,0.927421) translate(-2310.37,69.4871)" width="12" x="1047.930300972493" y="887.9126333680678"/></g>
  <g id="165">
   <use class="kv10" height="26" transform="rotate(0,1215.28,900.719) scale(-0.838049,0.927421) translate(-2666.38,69.5458)" width="12" x="1210.25159487896" xlink:href="#Accessory:避雷器1_0" y="888.6626333680678" zvalue="1234"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453805932547" ObjectName="10kV木瓜坝线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1215.28,900.719) scale(-0.838049,0.927421) translate(-2666.38,69.5458)" width="12" x="1210.25159487896" y="888.6626333680678"/></g>
  <g id="196">
   <use class="kv10" height="26" transform="rotate(0,1379.28,899.719) scale(-0.838049,0.927421) translate(-3026.07,69.4676)" width="12" x="1374.25159487896" xlink:href="#Accessory:避雷器1_0" y="887.6626333680678" zvalue="1252"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453806718979" ObjectName="10kV三家村线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1379.28,899.719) scale(-0.838049,0.927421) translate(-3026.07,69.4676)" width="12" x="1374.25159487896" y="887.6626333680678"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="533">
   <use class="kv35" height="30" transform="rotate(180,871.417,260.638) scale(2.22219,2.03365) translate(-467.052,-116.971)" width="20" x="849.1947195908842" xlink:href="#EnergyConsumer:站用变11_0" y="230.1331160161405" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453804425219" ObjectName="35kV#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,871.417,260.638) scale(2.22219,2.03365) translate(-467.052,-116.971)" width="20" x="849.1947195908842" y="230.1331160161405"/></g>
  <g id="476">
   <use class="kv10" height="30" transform="rotate(0,868.929,913.5) scale(1.25,-1.25) translate(-172.286,-1640.55)" width="12" x="861.4287060935333" xlink:href="#EnergyConsumer:负荷_0" y="894.75" zvalue="876"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453804949507" ObjectName="10kV围角线"/>
   <cge:TPSR_Ref TObjectID="6192453804949507"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,868.929,913.5) scale(1.25,-1.25) translate(-172.286,-1640.55)" width="12" x="861.4287060935333" y="894.75"/></g>
  <g id="72">
   <use class="kv10" height="30" transform="rotate(0,1025.93,913.5) scale(1.25,-1.25) translate(-203.686,-1640.55)" width="12" x="1018.428706093533" xlink:href="#EnergyConsumer:负荷_0" y="894.75" zvalue="1101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453805604867" ObjectName="10kV等武线"/>
   <cge:TPSR_Ref TObjectID="6192453805604867"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1025.93,913.5) scale(1.25,-1.25) translate(-203.686,-1640.55)" width="12" x="1018.428706093533" y="894.75"/></g>
  <g id="164">
   <use class="kv10" height="30" transform="rotate(0,1188.25,914.25) scale(1.25,-1.25) translate(-236.15,-1641.9)" width="12" x="1180.75" xlink:href="#EnergyConsumer:负荷_0" y="895.5" zvalue="1235"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453805867011" ObjectName="10kV木瓜坝线"/>
   <cge:TPSR_Ref TObjectID="6192453805867011"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1188.25,914.25) scale(1.25,-1.25) translate(-236.15,-1641.9)" width="12" x="1180.75" y="895.5"/></g>
  <g id="194">
   <use class="kv10" height="30" transform="rotate(0,1352.25,913.25) scale(1.25,-1.25) translate(-268.95,-1640.1)" width="12" x="1344.75" xlink:href="#EnergyConsumer:负荷_0" y="894.5" zvalue="1253"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453806653443" ObjectName="10kV三家村线"/>
   <cge:TPSR_Ref TObjectID="6192453806653443"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1352.25,913.25) scale(1.25,-1.25) translate(-268.95,-1640.1)" width="12" x="1344.75" y="894.5"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="53">
   <use class="kv35" height="30" transform="rotate(0,871.5,314.373) scale(1,-1) translate(0,-628.747)" width="15" x="864.0000000000001" xlink:href="#Disconnector:令克_0" y="299.3733757564003" zvalue="171"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453804359683" ObjectName="35kV#1站用变熔断器"/>
   <cge:TPSR_Ref TObjectID="6192453804359683"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,871.5,314.373) scale(1,-1) translate(0,-628.747)" width="15" x="864.0000000000001" y="299.3733757564003"/></g>
  <g id="68">
   <use class="kv35" height="30" transform="rotate(0,1102.21,198.526) scale(-0.947693,-0.6712) translate(-2265.65,-499.234)" width="15" x="1095.103492892157" xlink:href="#Disconnector:刀闸_0" y="188.4575673790615" zvalue="301"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453803638787" ObjectName="35kV弄班线3716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453803638787"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1102.21,198.526) scale(-0.947693,-0.6712) translate(-2265.65,-499.234)" width="15" x="1095.103492892157" y="188.4575673790615"/></g>
  <g id="20">
   <use class="kv35" height="30" transform="rotate(180,1102.39,313.794) scale(0.947693,0.6712) translate(60.4533,148.786)" width="15" x="1095.284133425995" xlink:href="#Disconnector:刀闸_0" y="303.7263051659212" zvalue="306"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453803442179" ObjectName="35kV弄班线3711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453803442179"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1102.39,313.794) scale(0.947693,0.6712) translate(60.4533,148.786)" width="15" x="1095.284133425995" y="303.7263051659212"/></g>
  <g id="304">
   <use class="kv35" height="30" transform="rotate(180,1345.89,287.128) scale(0.947693,-0.6712) translate(73.893,-719.842)" width="15" x="1338.784133425996" xlink:href="#Disconnector:刀闸_0" y="277.0596384992546" zvalue="567"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453804490755" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453804490755"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1345.89,287.128) scale(0.947693,-0.6712) translate(73.893,-719.842)" width="15" x="1338.784133425996" y="277.0596384992546"/></g>
  <g id="4">
   <use class="kv35" height="30" transform="rotate(270,1142.5,170.373) scale(1,1) translate(0,0)" width="15" x="1135" xlink:href="#Disconnector:刀闸_0" y="155.3733757564003" zvalue="726"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453805146115" ObjectName="35kV弄班线3719隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453805146115"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1142.5,170.373) scale(1,1) translate(0,0)" width="15" x="1135" y="155.3733757564003"/></g>
  <g id="220">
   <use class="kv35" height="30" transform="rotate(0,1102.25,382.408) scale(0.947693,-0.6712) translate(60.4455,-957.078)" width="15" x="1095.142982644967" xlink:href="#Disconnector:刀闸_0" y="372.3400530849659" zvalue="740"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453804687363" ObjectName="#1主变35kV侧3011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453804687363"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1102.25,382.408) scale(0.947693,-0.6712) translate(60.4455,-957.078)" width="15" x="1095.142982644967" y="372.3400530849659"/></g>
  <g id="192">
   <use class="kv10" height="30" transform="rotate(180,1104.42,643.53) scale(-0.947693,0.6712) translate(-2270.18,310.313)" width="15" x="1097.309117425243" xlink:href="#Disconnector:刀闸_0" y="633.4619653011533" zvalue="750"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454787858434" ObjectName="#1主变10kV侧0011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454787858434"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1104.42,643.53) scale(-0.947693,0.6712) translate(-2270.18,310.313)" width="15" x="1097.309117425243" y="633.4619653011533"/></g>
  <g id="479">
   <use class="kv10" height="30" transform="rotate(180,868.871,838.641) scale(-0.947693,-0.6712) translate(-1786.09,-2093.04)" width="15" x="861.7630441166826" xlink:href="#Disconnector:刀闸_0" y="828.573089599609" zvalue="871"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453805080579" ObjectName="10kV围角线0716隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453805080579"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,868.871,838.641) scale(-0.947693,-0.6712) translate(-1786.09,-2093.04)" width="15" x="861.7630441166826" y="828.573089599609"/></g>
  <g id="472">
   <use class="kv10" height="30" transform="rotate(180,867.189,742.641) scale(-0.947693,-0.6712) translate(-1782.63,-1854.01)" width="15" x="860.0809425293291" xlink:href="#Disconnector:刀闸_0" y="732.5730764122643" zvalue="881"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453804883971" ObjectName="10kV围角线0711隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453804883971"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,867.189,742.641) scale(-0.947693,-0.6712) translate(-1782.63,-1854.01)" width="15" x="860.0809425293291" y="732.5730764122643"/></g>
  <g id="248">
   <use class="kv10" height="30" transform="rotate(180,873.465,643.154) scale(0.947693,0.6712) translate(47.8178,310.128)" width="15" x="866.3569348729383" xlink:href="#Disconnector:刀闸_0" y="633.0855178058908" zvalue="978"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192454787923970" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192454787923970"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,873.465,643.154) scale(0.947693,0.6712) translate(47.8178,310.128)" width="15" x="866.3569348729383" y="633.0855178058908"/></g>
  <g id="75">
   <use class="kv10" height="30" transform="rotate(180,1025.87,838.641) scale(-0.947693,-0.6712) translate(-2108.76,-2093.04)" width="15" x="1018.763044116683" xlink:href="#Disconnector:刀闸_0" y="828.573089599609" zvalue="1096"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453805735939" ObjectName="10kV等武线0726隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453805735939"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1025.87,838.641) scale(-0.947693,-0.6712) translate(-2108.76,-2093.04)" width="15" x="1018.763044116683" y="828.573089599609"/></g>
  <g id="44">
   <use class="kv10" height="30" transform="rotate(180,1024.19,742.641) scale(-0.947693,-0.6712) translate(-2105.3,-1854.01)" width="15" x="1017.080942529329" xlink:href="#Disconnector:刀闸_0" y="732.5730764122643" zvalue="1106"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453805539331" ObjectName="10kV等武线0721隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453805539331"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1024.19,742.641) scale(-0.947693,-0.6712) translate(-2105.3,-1854.01)" width="15" x="1017.080942529329" y="732.5730764122643"/></g>
  <g id="167">
   <use class="kv10" height="30" transform="rotate(180,1188.19,839.391) scale(-0.947693,-0.6712) translate(-2442.36,-2094.9)" width="15" x="1181.084338023149" xlink:href="#Disconnector:刀闸_0" y="829.323089599609" zvalue="1230"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453805998083" ObjectName="10kV木瓜坝线0736隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453805998083"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1188.19,839.391) scale(-0.947693,-0.6712) translate(-2442.36,-2094.9)" width="15" x="1181.084338023149" y="829.323089599609"/></g>
  <g id="150">
   <use class="kv10" height="30" transform="rotate(180,1186.51,743.391) scale(-0.947693,-0.6712) translate(-2438.9,-1855.88)" width="15" x="1179.402236435796" xlink:href="#Disconnector:刀闸_0" y="733.3230764122643" zvalue="1240"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453805801475" ObjectName="10kV木瓜坝线0731隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453805801475"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1186.51,743.391) scale(-0.947693,-0.6712) translate(-2438.9,-1855.88)" width="15" x="1179.402236435796" y="733.3230764122643"/></g>
  <g id="198">
   <use class="kv10" height="30" transform="rotate(180,1352.19,838.391) scale(-0.947693,-0.6712) translate(-2779.41,-2092.42)" width="15" x="1345.084338023149" xlink:href="#Disconnector:刀闸_0" y="828.323089599609" zvalue="1248"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453806784515" ObjectName="10kV三家村线0746隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453806784515"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1352.19,838.391) scale(-0.947693,-0.6712) translate(-2779.41,-2092.42)" width="15" x="1345.084338023149" y="828.323089599609"/></g>
  <g id="182">
   <use class="kv10" height="30" transform="rotate(180,1350.51,742.391) scale(-0.947693,-0.6712) translate(-2775.95,-1853.39)" width="15" x="1343.402236435796" xlink:href="#Disconnector:刀闸_0" y="732.3230764122643" zvalue="1258"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453806587907" ObjectName="10kV三家村线0741隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192453806587907"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(180,1350.51,742.391) scale(-0.947693,-0.6712) translate(-2775.95,-1853.39)" width="15" x="1343.402236435796" y="732.3230764122643"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="70">
   <use class="kv35" height="30" transform="rotate(0,1102.13,128.782) scale(1.98323,0.522926) translate(-542.963,110.333)" width="7" x="1095.186704179698" xlink:href="#ACLineSegment:线路_0" y="120.937764596597" zvalue="297"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249321635844" ObjectName="35kV弄班线"/>
   <cge:TPSR_Ref TObjectID="8444249321635844_5066549679226881"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1102.13,128.782) scale(1.98323,0.522926) translate(-542.963,110.333)" width="7" x="1095.186704179698" y="120.937764596597"/></g>
 </g>
 <g id="BreakerClass">
  <g id="69">
   <use class="kv35" height="20" transform="rotate(0,1102.21,255.958) scale(1.5542,1.35421) translate(-390.255,-63.4068)" width="10" x="1094.434034534565" xlink:href="#Breaker:开关_0" y="242.415750510661" zvalue="298"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925095718916" ObjectName="35kV弄班线371断路器"/>
   <cge:TPSR_Ref TObjectID="6473925095718916"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1102.21,255.958) scale(1.5542,1.35421) translate(-390.255,-63.4068)" width="10" x="1094.434034534565" y="242.415750510661"/></g>
  <g id="235">
   <use class="kv10" height="20" transform="rotate(180,1104.27,600.292) scale(1.5542,1.35421) translate(-390.991,-153.472)" width="10" x="1096.497344267822" xlink:href="#Breaker:开关_0" y="586.75" zvalue="736"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925216174083" ObjectName="#1主变10kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473925216174083"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1104.27,600.292) scale(1.5542,1.35421) translate(-390.991,-153.472)" width="10" x="1096.497344267822" y="586.75"/></g>
  <g id="234">
   <use class="kv35" height="20" transform="rotate(180,1104.13,436.245) scale(1.5542,1.35421) translate(-390.943,-110.563)" width="10" x="1096.361137244279" xlink:href="#Breaker:开关_0" y="422.7024104979477" zvalue="738"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925095784452" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473925095784452"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1104.13,436.245) scale(1.5542,1.35421) translate(-390.943,-110.563)" width="10" x="1096.361137244279" y="422.7024104979477"/></g>
  <g id="478">
   <use class="kv10" height="20" transform="rotate(180,868.902,786.118) scale(1.5542,1.35421) translate(-307.064,-202.077)" width="10" x="861.1311232494973" xlink:href="#Breaker:开关_0" y="772.5758941321849" zvalue="873"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925095915524" ObjectName="10kV围角线071断路器"/>
   <cge:TPSR_Ref TObjectID="6473925095915524"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,868.902,786.118) scale(1.5542,1.35421) translate(-307.064,-202.077)" width="10" x="861.1311232494973" y="772.5758941321849"/></g>
  <g id="74">
   <use class="kv10" height="20" transform="rotate(180,1025.9,786.118) scale(1.5542,1.35421) translate(-363.047,-202.077)" width="10" x="1018.131123249497" xlink:href="#Breaker:开关_0" y="772.5758941321849" zvalue="1098"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925095981060" ObjectName="10kV等武线072断路器"/>
   <cge:TPSR_Ref TObjectID="6473925095981060"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1025.9,786.118) scale(1.5542,1.35421) translate(-363.047,-202.077)" width="10" x="1018.131123249497" y="772.5758941321849"/></g>
  <g id="166">
   <use class="kv10" height="20" transform="rotate(180,1188.22,786.868) scale(1.5542,1.35421) translate(-420.928,-202.273)" width="10" x="1180.452417155964" xlink:href="#Breaker:开关_0" y="773.3258941321849" zvalue="1232"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925096046596" ObjectName="10kV木瓜坝线073断路器"/>
   <cge:TPSR_Ref TObjectID="6473925096046596"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1188.22,786.868) scale(1.5542,1.35421) translate(-420.928,-202.273)" width="10" x="1180.452417155964" y="773.3258941321849"/></g>
  <g id="197">
   <use class="kv10" height="20" transform="rotate(180,1352.22,785.868) scale(1.5542,1.35421) translate(-479.407,-202.011)" width="10" x="1344.452417155964" xlink:href="#Breaker:开关_0" y="772.3258941321849" zvalue="1250"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473925096112132" ObjectName="10kV三家村线074断路器"/>
   <cge:TPSR_Ref TObjectID="6473925096112132"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1352.22,785.868) scale(1.5542,1.35421) translate(-479.407,-202.011)" width="10" x="1344.452417155964" y="772.3258941321849"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="67">
   <use class="kv35" height="20" transform="rotate(270,1061,169.252) scale(-1.24619,-1.0068) translate(-1911.16,-337.293)" width="10" x="1054.768227141073" xlink:href="#GroundDisconnector:地刀_0" y="159.1842824715292" zvalue="303"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453803573251" ObjectName="35kV弄班线37167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453803573251"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1061,169.252) scale(-1.24619,-1.0068) translate(-1911.16,-337.293)" width="10" x="1054.768227141073" y="159.1842824715292"/></g>
  <g id="33">
   <use class="kv35" height="20" transform="rotate(270,1062,278.252) scale(-1.24619,-1.0068) translate(-1912.97,-554.557)" width="10" x="1055.768227141073" xlink:href="#GroundDisconnector:地刀_0" y="268.1842824715293" zvalue="315"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453803835395" ObjectName="35kV弄班线37117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453803835395"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1062,278.252) scale(-1.24619,-1.0068) translate(-1912.97,-554.557)" width="10" x="1055.768227141073" y="268.1842824715293"/></g>
  <g id="103">
   <use class="kv35" height="20" transform="rotate(270,1311.82,249.07) scale(-1.24619,-1.0068) translate(-2363.25,-496.391)" width="10" x="1305.58640133916" xlink:href="#GroundDisconnector:地刀_0" y="239.002464289711" zvalue="353"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453804032003" ObjectName="35kV母线电压互感器39017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453804032003"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1311.82,249.07) scale(-1.24619,-1.0068) translate(-2363.25,-496.391)" width="10" x="1305.58640133916" y="239.002464289711"/></g>
  <g id="114">
   <use class="kv35" height="20" transform="rotate(270,1311.82,317.07) scale(-1.24619,-1.0068) translate(-2363.25,-631.931)" width="10" x="1305.58640133916" xlink:href="#GroundDisconnector:地刀_0" y="307.002464289711" zvalue="357"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453804163075" ObjectName="35kV母线电压互感器39010接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453804163075"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1311.82,317.07) scale(-1.24619,-1.0068) translate(-2363.25,-631.931)" width="10" x="1305.58640133916" y="307.002464289711"/></g>
  <g id="25">
   <use class="kv10" height="20" transform="rotate(270,836.334,612.981) scale(-1.24619,-1.0068) translate(-1506.22,-1221.75)" width="10" x="830.1032469692943" xlink:href="#GroundDisconnector:地刀_0" y="602.912927274121" zvalue="1086"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453805342723" ObjectName="10kV母线电压互感器09017接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453805342723"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,836.334,612.981) scale(-1.24619,-1.0068) translate(-1506.22,-1221.75)" width="10" x="830.1032469692943" y="602.912927274121"/></g>
  <g id="41">
   <use class="kv35" height="20" transform="rotate(270,1064.98,404.981) scale(-1.24619,-1.0068) translate(-1918.33,-807.158)" width="10" x="1058.746163808365" xlink:href="#GroundDisconnector:地刀_0" y="394.9129272741209" zvalue="1092"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453805473795" ObjectName="#1主变35kV侧30117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453805473795"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1064.98,404.981) scale(-1.24619,-1.0068) translate(-1918.33,-807.158)" width="10" x="1058.746163808365" y="394.9129272741209"/></g>
  <g id="111">
   <use class="kv10" height="20" transform="rotate(180,837.334,893.981) scale(-1.24619,-1.0068) translate(-1508.02,-1781.86)" width="10" x="831.1032469692944" xlink:href="#GroundDisconnector:地刀_0" y="883.9129272741209" zvalue="1126"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453806129155" ObjectName="10kV围角线07167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453806129155"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,837.334,893.981) scale(-1.24619,-1.0068) translate(-1508.02,-1781.86)" width="10" x="831.1032469692944" y="883.9129272741209"/></g>
  <g id="117">
   <use class="kv10" height="20" transform="rotate(180,994.334,893.981) scale(-1.24619,-1.0068) translate(-1791,-1781.86)" width="10" x="988.1032469692944" xlink:href="#GroundDisconnector:地刀_0" y="883.9129272741209" zvalue="1129"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453806260227" ObjectName="10kV等武线07267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453806260227"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,994.334,893.981) scale(-1.24619,-1.0068) translate(-1791,-1781.86)" width="10" x="988.1032469692944" y="883.9129272741209"/></g>
  <g id="113">
   <use class="kv10" height="20" transform="rotate(180,1156.66,894.731) scale(-1.24619,-1.0068) translate(-2083.58,-1783.35)" width="10" x="1150.424540875761" xlink:href="#GroundDisconnector:地刀_0" y="884.6629272741209" zvalue="1244"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453806391299" ObjectName="10kV木瓜坝线07367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453806391299"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1156.66,894.731) scale(-1.24619,-1.0068) translate(-2083.58,-1783.35)" width="10" x="1150.424540875761" y="884.6629272741209"/></g>
  <g id="179">
   <use class="kv10" height="20" transform="rotate(180,1320.66,893.731) scale(-1.24619,-1.0068) translate(-2379.18,-1781.36)" width="10" x="1314.424540875761" xlink:href="#GroundDisconnector:地刀_0" y="883.6629272741209" zvalue="1262"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192453806522371" ObjectName="10kV三家村线07467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192453806522371"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1320.66,893.731) scale(-1.24619,-1.0068) translate(-2379.18,-1781.36)" width="10" x="1314.424540875761" y="883.6629272741209"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="305">
   <path class="kv35" d="M 1345.81 205.43 L 1345.81 277.39" stroke-width="1" zvalue="568"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="218@0" LinkObjectIDznd="304@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1345.81 205.43 L 1345.81 277.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="306">
   <path class="kv35" d="M 1345.83 297.02 L 1345.83 344.84" stroke-width="1" zvalue="569"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="304@1" LinkObjectIDznd="128@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1345.83 297.02 L 1345.83 344.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="195">
   <path class="kv35" d="M 1104.18 478.05 L 1104.18 449.2" stroke-width="1" zvalue="748"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="202@0" LinkObjectIDznd="234@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1104.18 478.05 L 1104.18 449.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="193">
   <path class="kv10" d="M 1104.16 587.36 L 1104.16 554.2" stroke-width="1" zvalue="749"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="235@1" LinkObjectIDznd="202@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1104.16 587.36 L 1104.16 554.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="191">
   <path class="kv10" d="M 1104.47 633.63 L 1104.47 613.25" stroke-width="1" zvalue="751"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@1" LinkObjectIDznd="235@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1104.47 633.63 L 1104.47 613.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="475">
   <path class="kv10" d="M 868.93 848.54 L 868.93 896.63" stroke-width="1" zvalue="877"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="479@1" LinkObjectIDznd="476@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 868.93 848.54 L 868.93 896.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="474">
   <path class="kv10" d="M 895.93 888.5 L 895.93 874.58 L 868.93 874.58" stroke-width="1" zvalue="879"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="477@0" LinkObjectIDznd="475" MaxPinNum="2"/>
   </metadata>
  <path d="M 895.93 888.5 L 895.93 874.58 L 868.93 874.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="473">
   <path class="kv10" d="M 868.95 799.07 L 868.95 828.91" stroke-width="1" zvalue="880"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="478@0" LinkObjectIDznd="479@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 868.95 799.07 L 868.95 828.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="471">
   <path class="kv10" d="M 867.25 752.54 L 867.25 773.19" stroke-width="1" zvalue="883"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="472@1" LinkObjectIDznd="478@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 867.25 752.54 L 867.25 773.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="470">
   <path class="kv10" d="M 867.27 732.91 L 867.27 688.75" stroke-width="1" zvalue="884"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="472@0" LinkObjectIDznd="542@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 867.27 732.91 L 867.27 688.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="566">
   <path class="kv10" d="M 873.41 633.26 L 873.41 595.71" stroke-width="1" zvalue="991"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@1" LinkObjectIDznd="245@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 873.41 633.26 L 873.41 595.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="567">
   <path class="kv10" d="M 873.38 652.89 L 873.38 688.75" stroke-width="1" zvalue="992"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="248@0" LinkObjectIDznd="542@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 873.38 652.89 L 873.38 688.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="568">
   <path class="kv10" d="M 1104.5 653.27 L 1104.5 688.75" stroke-width="1" zvalue="993"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="192@0" LinkObjectIDznd="542@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1104.5 653.27 L 1104.5 688.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv10" d="M 846.15 613.04 L 873.41 613.04" stroke-width="1" zvalue="1090"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="25@0" LinkObjectIDznd="566" MaxPinNum="2"/>
   </metadata>
  <path d="M 846.15 613.04 L 873.41 613.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv10" d="M 1025.93 848.54 L 1025.93 896.63" stroke-width="1" zvalue="1102"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="75@1" LinkObjectIDznd="72@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1025.93 848.54 L 1025.93 896.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="kv10" d="M 1052.93 888.5 L 1052.93 874.58 L 1025.93 874.58" stroke-width="1" zvalue="1104"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73@0" LinkObjectIDznd="57" MaxPinNum="2"/>
   </metadata>
  <path d="M 1052.93 888.5 L 1052.93 874.58 L 1025.93 874.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv10" d="M 1025.95 799.07 L 1025.95 828.91" stroke-width="1" zvalue="1105"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="75@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1025.95 799.07 L 1025.95 828.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv10" d="M 1024.25 752.54 L 1024.25 773.19" stroke-width="1" zvalue="1108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@1" LinkObjectIDznd="74@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1024.25 752.54 L 1024.25 773.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="37">
   <path class="kv10" d="M 1024.27 732.91 L 1024.27 688.75" stroke-width="1" zvalue="1109"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="542@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1024.27 732.91 L 1024.27 688.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv10" d="M 837.4 884.16 L 837.4 875.5 L 868.93 875.5" stroke-width="1" zvalue="1127"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="111@0" LinkObjectIDznd="475" MaxPinNum="2"/>
   </metadata>
  <path d="M 837.4 884.16 L 837.4 875.5 L 868.93 875.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="118">
   <path class="kv10" d="M 994.4 884.16 L 994.4 874.58 L 1028.5 874.58" stroke-width="1" zvalue="1131"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="117@0" LinkObjectIDznd="55" MaxPinNum="2"/>
   </metadata>
  <path d="M 994.4 884.16 L 994.4 874.58 L 1028.5 874.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv35" d="M 1171.16 170.31 L 1157.24 170.31" stroke-width="1" zvalue="1208"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="35@0" LinkObjectIDznd="4@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1171.16 170.31 L 1157.24 170.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv35" d="M 1071.82 278.31 L 1102.32 278.31" stroke-width="1" zvalue="1212"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="33@0" LinkObjectIDznd="148" MaxPinNum="2"/>
   </metadata>
  <path d="M 1071.82 278.31 L 1102.32 278.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv35" d="M 1102.31 323.53 L 1102.31 344.84" stroke-width="1" zvalue="1213"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@0" LinkObjectIDznd="128@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1102.31 323.53 L 1102.31 344.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="148">
   <path class="kv35" d="M 1102.33 303.9 L 1102.31 268.89" stroke-width="1" zvalue="1214"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="20@1" LinkObjectIDznd="69@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1102.33 303.9 L 1102.31 268.89" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv35" d="M 1102.15 243 L 1102.13 208.26" stroke-width="1" zvalue="1215"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="68@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1102.15 243 L 1102.13 208.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv35" d="M 1102.15 188.63 L 1102.13 136.55" stroke-width="1" zvalue="1216"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@1" LinkObjectIDznd="70@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1102.15 188.63 L 1102.13 136.55" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="153">
   <path class="kv35" d="M 1102.31 372.51 L 1102.31 344.84" stroke-width="1" zvalue="1217"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@1" LinkObjectIDznd="128@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1102.31 372.51 L 1102.31 344.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="154">
   <path class="kv35" d="M 1102.33 392.14 L 1102.33 423.31" stroke-width="1" zvalue="1218"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="220@0" LinkObjectIDznd="234@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1102.33 392.14 L 1102.33 423.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="155">
   <path class="kv35" d="M 1074.79 405.04 L 1102.33 405.04" stroke-width="1" zvalue="1219"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="154" MaxPinNum="2"/>
   </metadata>
  <path d="M 1074.79 405.04 L 1102.33 405.04" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv35" d="M 1128 170.29 L 1102.14 170.29" stroke-width="1" zvalue="1220"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="4@0" LinkObjectIDznd="152" MaxPinNum="2"/>
   </metadata>
  <path d="M 1128 170.29 L 1102.14 170.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv35" d="M 1070.82 169.31 L 1102.14 169.31" stroke-width="1" zvalue="1221"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="67@0" LinkObjectIDznd="152" MaxPinNum="2"/>
   </metadata>
  <path d="M 1070.82 169.31 L 1102.14 169.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="14">
   <path class="kv35" d="M 1321.63 249.13 L 1345.81 249.13" stroke-width="1" zvalue="1224"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="305" MaxPinNum="2"/>
   </metadata>
  <path d="M 1321.63 249.13 L 1345.81 249.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="kv35" d="M 1321.63 317.13 L 1345.83 317.13" stroke-width="1" zvalue="1225"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="306" MaxPinNum="2"/>
   </metadata>
  <path d="M 1321.63 317.13 L 1345.83 317.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="18">
   <path class="kv35" d="M 871.58 327.62 L 871.58 336.23 L 871.58 336.23 L 871.58 344.84" stroke-width="1" zvalue="1227"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="128@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 871.58 327.62 L 871.58 336.23 L 871.58 336.23 L 871.58 344.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="19">
   <path class="kv35" d="M 871.42 302.12 L 871.42 289.41" stroke-width="1" zvalue="1228"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@1" LinkObjectIDznd="533@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 871.42 302.12 L 871.42 289.41" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="163">
   <path class="kv10" d="M 1188.25 849.29 L 1188.25 897.38" stroke-width="1" zvalue="1236"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="167@1" LinkObjectIDznd="164@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1188.25 849.29 L 1188.25 897.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="162">
   <path class="kv10" d="M 1215.25 889.25 L 1215.25 875.33 L 1188.25 875.33" stroke-width="1" zvalue="1238"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="165@0" LinkObjectIDznd="163" MaxPinNum="2"/>
   </metadata>
  <path d="M 1215.25 889.25 L 1215.25 875.33 L 1188.25 875.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="161">
   <path class="kv10" d="M 1188.28 799.82 L 1188.28 829.66" stroke-width="1" zvalue="1239"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@0" LinkObjectIDznd="167@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1188.28 799.82 L 1188.28 829.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="149">
   <path class="kv10" d="M 1186.57 753.29 L 1186.57 773.94" stroke-width="1" zvalue="1242"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@1" LinkObjectIDznd="166@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1186.57 753.29 L 1186.57 773.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv10" d="M 1186.59 733.66 L 1186.59 688.75" stroke-width="1" zvalue="1243"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="542@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1186.59 733.66 L 1186.59 688.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="109">
   <path class="kv10" d="M 1156.72 884.91 L 1156.72 875.33 L 1190.82 875.33" stroke-width="1" zvalue="1246"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="113@0" LinkObjectIDznd="162" MaxPinNum="2"/>
   </metadata>
  <path d="M 1156.72 884.91 L 1156.72 875.33 L 1190.82 875.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="190">
   <path class="kv10" d="M 1352.25 848.29 L 1352.25 896.38" stroke-width="1" zvalue="1254"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198@1" LinkObjectIDznd="194@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1352.25 848.29 L 1352.25 896.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="189">
   <path class="kv10" d="M 1379.25 888.25 L 1379.25 874.33 L 1352.25 874.33" stroke-width="1" zvalue="1256"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="196@0" LinkObjectIDznd="190" MaxPinNum="2"/>
   </metadata>
  <path d="M 1379.25 888.25 L 1379.25 874.33 L 1352.25 874.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="188">
   <path class="kv10" d="M 1352.28 798.82 L 1352.28 828.66" stroke-width="1" zvalue="1257"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="197@0" LinkObjectIDznd="198@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1352.28 798.82 L 1352.28 828.66" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="181">
   <path class="kv10" d="M 1350.57 752.29 L 1350.57 772.94" stroke-width="1" zvalue="1260"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@1" LinkObjectIDznd="197@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1350.57 752.29 L 1350.57 772.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="180">
   <path class="kv10" d="M 1350.59 732.66 L 1350.59 688.75" stroke-width="1" zvalue="1261"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="182@0" LinkObjectIDznd="542@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1350.59 732.66 L 1350.59 688.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="kv10" d="M 1320.72 883.91 L 1320.72 874.33 L 1354.82 874.33" stroke-width="1" zvalue="1264"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@0" LinkObjectIDznd="189" MaxPinNum="2"/>
   </metadata>
  <path d="M 1320.72 883.91 L 1320.72 874.33 L 1354.82 874.33" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="202">
   <g id="2020">
    <use class="kv35" height="20" transform="rotate(0,1104.74,517.833) scale(5.55129,5.13713) translate(-871.597,-375.66)" width="15" x="1063.1" xlink:href="#PowerTransformer2:接地可调两卷变（规范制图）_0" y="466.46" zvalue="746"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874566074370" ObjectName="35"/>
    </metadata>
   </g>
   <g id="2021">
    <use class="kv10" height="20" transform="rotate(0,1104.74,517.833) scale(5.55129,5.13713) translate(-871.597,-375.66)" width="15" x="1063.1" xlink:href="#PowerTransformer2:接地可调两卷变（规范制图）_1" y="466.46" zvalue="746"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874566139906" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399524220930" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399524220930"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1104.74,517.833) scale(5.55129,5.13713) translate(-871.597,-375.66)" width="15" x="1063.1" y="466.46"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="139">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="139" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1100.38,24.9378) scale(1,1) translate(0,0)" writing-mode="lr" x="1100.57" xml:space="preserve" y="29.85" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133109280771" ObjectName="P"/>
   </metadata>
  </g>
  <g id="140">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="140" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1100.38,47.9378) scale(1,1) translate(0,0)" writing-mode="lr" x="1100.57" xml:space="preserve" y="52.85" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133109346308" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="141">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="141" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1100.38,70.9378) scale(1,1) translate(0,0)" writing-mode="lr" x="1100.57" xml:space="preserve" y="75.84999999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133109411844" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="1" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,1102.13,93.1256) scale(1,1) translate(0,0)" writing-mode="lr" x="1101.67" xml:space="preserve" y="97.90000000000001" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133110132739" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,148.5,506.341) scale(1,1) translate(0,5.4652e-14)" writing-mode="lr" x="148.18" xml:space="preserve" y="511.06" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133108232195" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,268.429,509.25) scale(1,1) translate(0,-1.0995e-13)" writing-mode="lr" x="268.11" xml:space="preserve" y="513.97" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133115441156" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="8" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,148.5,532.341) scale(1,1) translate(0,-1.15077e-13)" writing-mode="lr" x="148.18" xml:space="preserve" y="537.0599999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133108297731" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="17" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,268.429,533.25) scale(1,1) translate(0,-1.15279e-13)" writing-mode="lr" x="268.11" xml:space="preserve" y="537.97" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133115506692" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="76">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,148.5,556.341) scale(1,1) translate(0,-1.20406e-13)" writing-mode="lr" x="148.18" xml:space="preserve" y="561.0599999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133108363267" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="77">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="77" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,268.429,558.25) scale(1,1) translate(0,-1.2083e-13)" writing-mode="lr" x="268.11" xml:space="preserve" y="562.97" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133115572228" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="78" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1349.5,108.841) scale(1,1) translate(0,0)" writing-mode="lr" x="1349.04" xml:space="preserve" y="113.62" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133108494340" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="79">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,882.429,489.75) scale(1,1) translate(0,0)" writing-mode="lr" x="881.97" xml:space="preserve" y="494.53" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133115703300" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="80">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,149.75,481.42) scale(1,1) translate(0,-1.03771e-13)" writing-mode="lr" x="149.43" xml:space="preserve" y="486.14" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133108494340" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="94" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,268.429,484.75) scale(1,1) translate(0,-1.0451e-13)" writing-mode="lr" x="268.11" xml:space="preserve" y="489.47" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133115703300" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,145.143,189.341) scale(1,1) translate(0,0)" writing-mode="lr" x="144.78" xml:space="preserve" y="194.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133108625412" ObjectName="F"/>
   </metadata>
  </g>
  <g id="98">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="98" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,145.143,213.25) scale(1,1) translate(0,0)" writing-mode="lr" x="144.78" xml:space="preserve" y="217.93" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133115834371" ObjectName="F"/>
   </metadata>
  </g>
  <g id="99">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="99" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,148.5,581.341) scale(1,1) translate(0,-1.25957e-13)" writing-mode="lr" x="148.18" xml:space="preserve" y="586.0599999999999" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133108690948" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="100">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="100" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,268.429,582.25) scale(1,1) translate(0,-1.26159e-13)" writing-mode="lr" x="268.11" xml:space="preserve" y="586.97" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133115899907" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="101">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="101" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1207.35,393.962) scale(1,1) translate(0,0)" writing-mode="lr" x="1206.8" xml:space="preserve" y="400.24" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133110919171" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="102">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="102" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1207.35,422.962) scale(1,1) translate(0,0)" writing-mode="lr" x="1206.8" xml:space="preserve" y="429.24" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133110984707" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="107" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1208.35,584.705) scale(1,1) translate(0,0)" writing-mode="lr" x="1207.8" xml:space="preserve" y="590.98" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133111050243" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="108">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="108" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1208.35,613.705) scale(1,1) translate(0,0)" writing-mode="lr" x="1207.8" xml:space="preserve" y="619.98" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133111115779" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="119">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="119" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1207.35,451.962) scale(1,1) translate(0,0)" writing-mode="lr" x="1206.8" xml:space="preserve" y="458.24" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133111181315" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="121">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="121" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,165.872,236.833) scale(1,1) translate(0,0)" writing-mode="lr" x="165.51" xml:space="preserve" y="243.02" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133111377923" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="122">
   <text Format="f3.0" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="122" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,164.821,260.833) scale(1,1) translate(0,0)" writing-mode="lr" x="164.46" xml:space="preserve" y="267.02" zvalue="1">ddd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133111443459" ObjectName="HTap"/>
   </metadata>
  </g>
  <g id="123">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="123" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1208.35,642.705) scale(1,1) translate(0,-1.38823e-13)" writing-mode="lr" x="1207.8" xml:space="preserve" y="648.98" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133111508995" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="124">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="124" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,868.929,975.25) scale(1,1) translate(0,0)" writing-mode="lr" x="868.47" xml:space="preserve" y="980.03" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133113999363" ObjectName="P"/>
   </metadata>
  </g>
  <g id="125">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="125" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1025.93,975.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1025.47" xml:space="preserve" y="980.03" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133115965443" ObjectName="P"/>
   </metadata>
  </g>
  <g id="127">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="127" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1188.25,976) scale(1,1) translate(0,0)" writing-mode="lr" x="1187.79" xml:space="preserve" y="980.78" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133117407235" ObjectName="P"/>
   </metadata>
  </g>
  <g id="129">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="129" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1352.25,975) scale(1,1) translate(0,0)" writing-mode="lr" x="1351.79" xml:space="preserve" y="979.78" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133118849028" ObjectName="P"/>
   </metadata>
  </g>
  <g id="130">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="130" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,868.929,992.25) scale(1,1) translate(0,0)" writing-mode="lr" x="868.47" xml:space="preserve" y="997.03" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133114064900" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="131">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="131" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1025.93,992.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1025.47" xml:space="preserve" y="997.03" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133116030979" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="132">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="132" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1188.25,993) scale(1,1) translate(0,0)" writing-mode="lr" x="1187.79" xml:space="preserve" y="997.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133117472771" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="133">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="133" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1352.25,992) scale(1,1) translate(0,0)" writing-mode="lr" x="1351.79" xml:space="preserve" y="996.78" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133118914564" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="142">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="142" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,868.929,1009.25) scale(1,1) translate(0,0)" writing-mode="lr" x="868.47" xml:space="preserve" y="1014.03" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133114130436" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="145">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="145" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1025.93,1009.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1025.47" xml:space="preserve" y="1014.03" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133116096515" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="170">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="170" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1188.25,1010) scale(1,1) translate(0,0)" writing-mode="lr" x="1187.79" xml:space="preserve" y="1014.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133117538308" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="171">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="171" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1352.25,1009) scale(1,1) translate(0,0)" writing-mode="lr" x="1351.79" xml:space="preserve" y="1013.78" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133118980100" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="5" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,134,164) scale(1,1) translate(0,0)" writing-mode="lr" x="134.15" xml:space="preserve" y="170.45" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133136281604" ObjectName=""/>
   </metadata>
  </g>
  <g id="1035">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="1035" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,317,165.5) scale(1,1) translate(0,0)" writing-mode="lr" x="317.15" xml:space="preserve" y="171.95" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133136347140" ObjectName=""/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="15" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,717.75,646.42) scale(1,1) translate(0,2.10612e-13)" writing-mode="lr" x="717.4299999999999" xml:space="preserve" y="651.14" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481133115703300" ObjectName="Uab"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="733">
   <use height="30" transform="rotate(0,352.673,305.857) scale(0.708333,0.665547) translate(140.843,148.684)" width="30" x="342.05" xlink:href="#State:红绿圆(方形)_0" y="295.87" zvalue="1310"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374883946500" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,352.673,305.857) scale(0.708333,0.665547) translate(140.843,148.684)" width="30" x="342.05" y="295.87"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,257.048,305.857) scale(0.708333,0.665547) translate(101.468,148.684)" width="30" x="246.42" xlink:href="#State:红绿圆(方形)_0" y="295.87" zvalue="1311"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562949970722824" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,257.048,305.857) scale(0.708333,0.665547) translate(101.468,148.684)" width="30" x="246.42" y="295.87"/></g>
  <g id="1082">
   <use height="30" transform="rotate(0,115.286,306.039) scale(0.910937,0.8) translate(7.709,73.5097)" width="80" x="78.84999999999999" xlink:href="#State:间隔模板_0" y="294.04" zvalue="1349"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5629499540635652" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,115.286,306.039) scale(0.910937,0.8) translate(7.709,73.5097)" width="80" x="78.84999999999999" y="294.04"/></g>
 </g>
</svg>