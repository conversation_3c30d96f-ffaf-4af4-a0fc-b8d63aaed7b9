<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549589835778" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:10kV母线PT带消谐装置_0" viewBox="0,0,35,30">
   <use terminal-index="0" type="0" x="17.56245852479325" xlink:href="#terminal" y="28.12373692455963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.61245852479333" x2="34.61245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.86245852479331" x2="30.86245852479331" y1="22.68299618381883" y2="19.84040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.6124585247933" x2="26.6124585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.61245852479332" x2="34.61245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666667" x2="31" y1="22.69225544307809" y2="22.69225544307809"/>
   <rect fill-opacity="0" height="3.55" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,30.81,15.38) scale(1,1) translate(0,0)" width="8.92" x="26.34" y="13.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.647721108666" x2="30.647721108666" y1="10.83674821859629" y2="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.71438777533266" x2="28.71438777533266" y1="7.51612768565195" y2="7.51612768565195"/>
   <ellipse cx="14.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.05048569403981" x2="22.05048569403981" y1="34.53084700683308" y2="34.53084700683308"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.7608382776216" x2="28.48402243293775" y1="7.754208141873304" y2="7.754208141873304"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.01083827762157" x2="29.40068909960439" y1="6.50420814187332" y2="6.50420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.51083827762159" x2="30.06735576627107" y1="5.004208141873296" y2="5.004208141873296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.89410730945214" x2="23.0877735452272" y1="24.15299989806297" y2="21.53208583485582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58713830216066" x2="22.81883560169719" y1="21.54040359122622" y2="21.54040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51457106407813" x2="19.32090482830307" y1="24.05654513693459" y2="21.43563107372743"/>
   <ellipse cx="14.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.3644003886642" x2="22.3644003886642" y1="30.19213090500035" y2="30.19213090500035"/>
   <ellipse cx="21.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.19220382559271" x2="17.19220382559271" y1="23.07076893362116" y2="23.07076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.4422038255927" x2="16.4422038255927" y1="16.82076893362115" y2="16.82076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.92264294445647" x2="20.87303257583197" y1="16.99992879670395" y2="15.47611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.873032575832" x2="20.37063985073817" y1="15.47611741162252" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.87303257583199" x2="23.32581493230132" y1="15.47611741162254" y2="16.60543752773795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.94220382559271" x2="16.94220382559271" y1="16.32076893362115" y2="16.32076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="16.07581493230131" y1="22.72611741162255" y2="23.85543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="13.12063985073816" y1="22.72611741162255" y2="20.07298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583197" y1="24.24992879670398" y2="22.72611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="16.07581493230131" y1="15.47611741162254" y2="16.60543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="13.12063985073816" y1="15.47611741162253" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583196" y1="16.99992879670396" y2="15.47611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.59103363843511" x2="17.59103363843511" y1="25.15822158129307" y2="28.16666666666667"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_0" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="3.333333333333332" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="24.08333333333333" y2="29.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="7" y1="19.5" y2="24.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_1" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="18.25" y2="32.5"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="Disconnector:手车隔离开关13_2" viewBox="0,0,14,33">
   <use terminal-index="0" type="0" x="6.988344027507038" xlink:href="#terminal" y="0.2003832584601106"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="32.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="4" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4" x2="10" y1="18.5" y2="25.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="0" y2="18.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="25.83333333333333" y2="32.41666666666667"/>
   <rect fill-opacity="0" height="7" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,10.75) scale(1,1) translate(0,0)" width="4" x="5" y="7.25"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.918004115226339" x2="1.459670781893005" y1="0.2638152760039745" y2="5.074160103590184"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.186947459912011" y2="7.997292287498221"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.1804819426706423" y2="4.990826770256851"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="32.57918883922238" y2="27.76884401163617"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="29.77315435646374" y2="24.96280952887754"/>
   <line fill="none" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="32.57918883922238" y2="27.76884401163617"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="ACLineSegment:线路d_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="11.39850427350427" xlink:href="#terminal" y="38.31103076678519"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.02350427350428" x2="34.02350427350427" y1="35.97222222222223" y2="35.97222222222223"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.02350427350428" x2="33.02350427350427" y1="36.97222222222223" y2="36.97222222222223"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.27350427350427" x2="19.27350427350427" y1="8.138888888888889" y2="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.02350427350428" x2="32.02350427350427" y1="37.97222222222223" y2="37.97222222222223"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.02350427350427" x2="22.02350427350427" y1="24.72222222222222" y2="24.72222222222222"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,19.44,14.56) scale(1,1) translate(0,0)" width="4.17" x="17.36" y="10.06"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.6068376068376" x2="22.6068376068376" y1="33.30555555555556" y2="33.30555555555556"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.52350427350428" x2="31.52350427350428" y1="26.72222222222222" y2="14.05555555555555"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.52350427350428" x2="31.52350427350428" y1="32.97222222222223" y2="35.97222222222223"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="11.43372764120183" x2="11.43372764120183" y1="38.38888888888889" y2="0.3888888888888857"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.60683760683761" x2="11.44017094017094" y1="8.147641719794434" y2="8.147641719794434"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.52350427350428" x2="32.52350427350427" y1="26.72222222222222" y2="20.72222222222222"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.52350427350428" x2="30.52350427350428" y1="26.72222222222222" y2="20.72222222222222"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="31.52895482715068" x2="31.52895482715068" y1="8.180277533364855" y2="14.30555555555556"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,31.57,25.89) scale(1,1) translate(0,0)" width="6.08" x="28.52" y="18.72"/>
   <ellipse cx="19.2" cy="24.62" fill-opacity="0" rx="4.57" ry="4.57" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.62" cy="33.14" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="26.59940156254585" x2="26.59940156254585" y1="8.136477011405916" y2="8.136477011405916"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV地美特地热电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="256" x="48" xlink:href="logo.png" y="43"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,176,73) scale(1,1) translate(0,0)" writing-mode="lr" x="176" xml:space="preserve" y="76.5" zvalue="54"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,176.5,72.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="176.5" xml:space="preserve" y="81.69" zvalue="55">10kV地美特地热电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="54" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,69.625,305.75) scale(1,1) translate(0,0)" width="72.88" x="33.19" y="293.75" zvalue="1886"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,69.625,305.75) scale(1,1) translate(0,0)" writing-mode="lr" x="69.63" xml:space="preserve" y="310.25" zvalue="1886">信号一览</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,756.841,491.032) scale(1,1) translate(0,0)" writing-mode="lr" x="756.84" xml:space="preserve" y="495.53" zvalue="7">10kV母线1</text>
  <line fill="none" id="67" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382" x2="382" y1="11" y2="1041" zvalue="58"/>
  <line fill="none" id="666" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="146.8704926140824" y2="146.8704926140824" zvalue="1056"/>
  <line fill="none" id="613" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.000000000000455" x2="375" y1="616.8704926140824" y2="616.8704926140824" zvalue="1058"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="932" y2="932"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="368" y1="971.1632999999999" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="932" y2="971.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="971.16327" y2="971.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.08167" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="971.16327" y2="999.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="98" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="8" x2="8" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="188" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="98" x2="98" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188" x2="188" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="278.0000000000001" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="188.0000000000001" x2="188.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278.0000000000001" x2="278.0000000000001" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="999.0816" y2="999.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="368" y1="1027" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="278" x2="278" y1="999.0816" y2="1027"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="368" x2="368" y1="999.0816" y2="1027"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="609" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,952) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="958" zvalue="1062">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="608" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,50,986) scale(1,1) translate(0,0)" writing-mode="lr" x="50" xml:space="preserve" y="992" zvalue="1063">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="607" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,232,986) scale(1,1) translate(0,0)" writing-mode="lr" x="232" xml:space="preserve" y="992" zvalue="1064">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="606" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,49,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="49" xml:space="preserve" y="1020" zvalue="1065">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="605" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,231,1014) scale(1,1) translate(0,0)" writing-mode="lr" x="231" xml:space="preserve" y="1020" zvalue="1066">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="602" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,73.5,646.5) scale(1,1) translate(0,0)" writing-mode="lr" x="73.5" xml:space="preserve" y="651" zvalue="1069">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.054,954) scale(1,1) translate(0,0)" writing-mode="lr" x="233.05" xml:space="preserve" y="960" zvalue="1080">LiXin-01-2020</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,143.054,986) scale(1,1) translate(0,0)" writing-mode="lr" x="143.05" xml:space="preserve" y="992" zvalue="1081">段勇</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,323.054,986) scale(1,1) translate(0,0)" writing-mode="lr" x="323.05" xml:space="preserve" y="992" zvalue="1082">20201106</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,819.397,441.508) scale(1,1) translate(0,0)" writing-mode="lr" x="819.4" xml:space="preserve" y="446.01" zvalue="1715">021</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="137" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1393.45,565.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1393.45" xml:space="preserve" y="570.36" zvalue="1761">022</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="136" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1381.19,642.286) scale(1,1) translate(0,0)" writing-mode="lr" x="1381.19" xml:space="preserve" y="646.79" zvalue="1766">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="149" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1424.74,858.857) scale(1,1) translate(0,0)" writing-mode="lr" x="1424.74" xml:space="preserve" y="863.36" zvalue="1770">10kV电站升压线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1228.83,779.556) scale(1,1) translate(0,0)" writing-mode="lr" x="1228.83" xml:space="preserve" y="784.0599999999999" zvalue="1820">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1207.44,602) scale(1,1) translate(-1.0591e-12,0)" writing-mode="lr" x="1207.44" xml:space="preserve" y="606.5" zvalue="1821">0901</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="150" y2="150"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="176" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="1.678571428571331" y1="150" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="150" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="150" y2="150"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="176" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="150" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.6785714285713" x2="363.6785714285713" y1="150" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="176" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="199.5" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="1.678571428571331" y1="176" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="176" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="176" y2="176"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="199.5" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="176" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.6785714285713" x2="363.6785714285713" y1="176" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="199.5" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="222.25" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="1.678571428571331" y1="199.5" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="199.5" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="199.5" y2="199.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="222.25" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="199.5" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.6785714285713" x2="363.6785714285713" y1="199.5" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="222.25" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="245" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="1.678571428571331" y1="222.25" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="222.25" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="222.25" y2="222.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="245" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="222.25" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.6785714285713" x2="363.6785714285713" y1="222.25" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="245" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="182.6785714285713" y1="267.75" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="1.678571428571331" x2="1.678571428571331" y1="245" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="245" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="245" y2="245"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="363.6785714285713" y1="267.75" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="182.6785714285713" x2="182.6785714285713" y1="245" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="363.6785714285713" x2="363.6785714285713" y1="245" y2="267.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="430.9166435058594" y2="430.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="468.4066435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="430.9166435058594" y2="430.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="468.4066435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="430.9166435058594" y2="430.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="468.4066435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="430.9166435058594" y2="430.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="468.4066435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="430.9166435058594" y2="430.9166435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="468.4066435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="430.9166435058594" y2="468.4066435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="468.4067435058594" y2="468.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="468.4067435058594" y2="468.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="468.4067435058594" y2="468.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="468.4067435058594" y2="468.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="468.4067435058594" y2="468.4067435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="468.4067435058594" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="516.7439435058594" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="516.7439435058594" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="516.7439435058594" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="516.7439435058594" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="492.5753435058595" y2="492.5753435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="516.7439435058594" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="492.5753435058595" y2="516.7439435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="516.7439835058594" y2="516.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="540.9125835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="516.7439835058594" y2="516.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="540.9125835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="516.7439835058594" y2="516.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="540.9125835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="516.7439835058594" y2="516.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="540.9125835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="516.7439835058594" y2="516.7439835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="540.9125835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="516.7439835058594" y2="540.9125835058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="540.9127435058595" y2="540.9127435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="540.9127435058595" y2="540.9127435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="540.9127435058595" y2="540.9127435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="540.9127435058595" y2="540.9127435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="540.9127435058595" y2="540.9127435058595"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="540.9127435058595" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="84.74563316591821" y1="589.2499435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="36.4009331659181" x2="36.4009331659181" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74563316591821" x2="84.74563316591821" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="146.8542331659182" y1="589.2499435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="84.74593316591813" x2="84.74593316591813" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8542331659182" x2="146.8542331659182" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="210.1785331659182" y1="589.2499435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="146.8537331659181" x2="146.8537331659181" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1785331659182" x2="210.1785331659182" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="272.2867331659181" y1="589.2499435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="210.1784331659181" x2="210.1784331659181" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="565.0813435058594" y2="565.0813435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="334.3950331659182" y1="589.2499435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="272.2867331659181" x2="272.2867331659181" y1="565.0813435058594" y2="589.2499435058594"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="334.3950331659182" x2="334.3950331659182" y1="565.0813435058594" y2="589.2499435058594"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,157.827,303.841) scale(1,1) translate(0,0)" writing-mode="lr" x="157.83" xml:space="preserve" y="308.34" zvalue="1873">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,262.827,303.841) scale(1,1) translate(0,0)" writing-mode="lr" x="262.83" xml:space="preserve" y="308.34" zvalue="1874">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="78" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,174.595,451.75) scale(1,1) translate(0,0)" writing-mode="lr" x="174.5952380952378" xml:space="preserve" y="456.2500000000001" zvalue="1875">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.4286,479.5) scale(1,1) translate(0,0)" writing-mode="lr" x="56.42857142857133" xml:space="preserve" y="484.0000000000001" zvalue="1877">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.4286,505) scale(1,1) translate(0,0)" writing-mode="lr" x="56.42857142857133" xml:space="preserve" y="509.5" zvalue="1878">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="74" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.4286,530.5) scale(1,1) translate(0,0)" writing-mode="lr" x="56.42857142857133" xml:space="preserve" y="535" zvalue="1879">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="72" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.4286,555) scale(1,1) translate(0,0)" writing-mode="lr" x="55.42857142857133" xml:space="preserve" y="559.5" zvalue="1880">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.4286,581.5) scale(1,1) translate(0,0)" writing-mode="lr" x="56.42857142857133" xml:space="preserve" y="586" zvalue="1881">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,38.4286,163) scale(1,1) translate(0,0)" writing-mode="lr" x="38.43" xml:space="preserve" y="168.5" zvalue="1882">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,218.429,163) scale(1,1) translate(0,0)" writing-mode="lr" x="218.43" xml:space="preserve" y="168.5" zvalue="1883">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.5536,188.5) scale(1,1) translate(0,0)" writing-mode="lr" x="55.55" xml:space="preserve" y="193" zvalue="1884">10kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,843.081,230.5) scale(1,1) translate(0,0)" writing-mode="lr" x="843.08" xml:space="preserve" y="235" zvalue="1895">10kV瑞丽大道线T地美特地热电站支线</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="33.19" y="293.75" zvalue="1886"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="6">
   <path class="kv10" d="M 673 510.81 L 1007 510.81" stroke-width="4" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674248818692" ObjectName="10kV母线1"/>
   <cge:TPSR_Ref TObjectID="9288674248818692"/></metadata>
  <path d="M 673 510.81 L 1007 510.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="kv10" d="M 1155 507.81 L 1489 507.81" stroke-width="4" zvalue="1896"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674248884228" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674248884228"/></metadata>
  <path d="M 1155 507.81 L 1489 507.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="236">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="236" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,181.222,511.167) scale(1,1) translate(-3.42689e-14,0)" writing-mode="lr" x="181.34" xml:space="preserve" y="516.08" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="237">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="237" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,181.222,533.667) scale(1,1) translate(0,0)" writing-mode="lr" x="181.34" xml:space="preserve" y="538.58" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="238">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="238" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,181.222,558.167) scale(1,1) translate(-3.42689e-14,0)" writing-mode="lr" x="181.34" xml:space="preserve" y="563.08" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="235">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="235" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,181.222,485.667) scale(1,1) translate(-3.42689e-14,0)" writing-mode="lr" x="181.34" xml:space="preserve" y="490.58" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="4">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="4" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,679,529.81) scale(1,1) translate(0,0)" writing-mode="lr" x="678.53" xml:space="preserve" y="534.59" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125912444932" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="24">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="24" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1418.16,883.153) scale(1,1) translate(0,1.64606e-12)" writing-mode="lr" x="1417.7" xml:space="preserve" y="887.83" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125913821188" ObjectName="P"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="32" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1418.16,906.432) scale(1,1) translate(0,1.69e-12)" writing-mode="lr" x="1417.7" xml:space="preserve" y="911.11" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125913886727" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="36">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="36" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1418.16,929.711) scale(1,1) translate(0,1.73394e-12)" writing-mode="lr" x="1417.7" xml:space="preserve" y="934.39" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125913952263" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="53" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,109,162) scale(1,1) translate(0,0)" writing-mode="lr" x="109.15" xml:space="preserve" y="168.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125917884420" ObjectName=""/>
   </metadata>
  </g>
  <g id="1035">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="1035" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,292,161) scale(1,1) translate(0,0)" writing-mode="lr" x="292.15" xml:space="preserve" y="167.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125917949956" ObjectName=""/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="55" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,140,187.71) scale(1,1) translate(0,0)" writing-mode="lr" x="139.54" xml:space="preserve" y="192.49" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="1">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="1" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,842.081,162.5) scale(1,1) translate(-1.76988e-13,0)" writing-mode="lr" x="841.61" xml:space="preserve" y="167.28" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125916835844" ObjectName="P"/>
   </metadata>
  </g>
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="2" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,842.081,183.5) scale(1,1) translate(-1.76988e-13,0)" writing-mode="lr" x="841.61" xml:space="preserve" y="188.28" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125916901380" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="3" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,842.081,204.5) scale(1,1) translate(-1.76988e-13,0)" writing-mode="lr" x="841.61" xml:space="preserve" y="209.28" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125916966916" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="BreakerClass">
  <g id="12">
   <use class="kv10" height="20" transform="rotate(0,850.683,442.508) scale(3.14286,3.14286) translate(-569.297,-280.281)" width="10" x="834.9682539682539" xlink:href="#Breaker:小车断路器_0" y="411.0793650793651" zvalue="1714"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924534861829" ObjectName="地美特地热电站支线021断路器"/>
   <cge:TPSR_Ref TObjectID="6473924534861829"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,850.683,442.508) scale(3.14286,3.14286) translate(-569.297,-280.281)" width="10" x="834.9682539682539" y="411.0793650793651"/></g>
  <g id="146">
   <use class="kv10" height="20" transform="rotate(0,1426.16,566.857) scale(3.14286,3.14286) translate(-961.67,-365.065)" width="10" x="1410.449998442321" xlink:href="#Breaker:小车断路器_0" y="535.4285714285714" zvalue="1760"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924534796293" ObjectName="10kV电站升压线022断路器"/>
   <cge:TPSR_Ref TObjectID="6473924534796293"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1426.16,566.857) scale(3.14286,3.14286) translate(-961.67,-365.065)" width="10" x="1410.449998442321" y="535.4285714285714"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="39">
   <path class="kv10" d="M 850.68 470.79 L 850.68 510.81" stroke-width="1" zvalue="1717"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="12@1" LinkObjectIDznd="6@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 850.68 470.79 L 850.68 510.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv10" d="M 850.68 296.31 L 850.68 413.44" stroke-width="1" zvalue="1722"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@0" LinkObjectIDznd="12@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 850.68 296.31 L 850.68 413.44" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="115">
   <path class="kv10" d="M 1215.36 650.75 L 1215.36 643 L 1236.09 643" stroke-width="1" zvalue="1740"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="114@0" LinkObjectIDznd="15" MaxPinNum="2"/>
   </metadata>
  <path d="M 1215.36 650.75 L 1215.36 643 L 1236.09 643" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="145">
   <path class="kv10" d="M 1426.16 537.79 L 1426.16 507.81" stroke-width="1" zvalue="1762"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@0" LinkObjectIDznd="60@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426.16 537.79 L 1426.16 507.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="15">
   <path class="kv10" d="M 1236.09 686.45 L 1236.09 621.02" stroke-width="1" zvalue="1821"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="9@0" LinkObjectIDznd="11@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1236.09 686.45 L 1236.09 621.02" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="41">
   <path class="kv10" d="M 1402.83 632.21 L 1402.83 618 L 1450.65 618 L 1450.65 630.19" stroke-width="1" zvalue="1846"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="139@0" LinkObjectIDznd="140@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1402.83 632.21 L 1402.83 618 L 1450.65 618 L 1450.65 630.19" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv10" d="M 1426.16 595.14 L 1426.16 618" stroke-width="1" zvalue="1847"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="146@1" LinkObjectIDznd="41" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426.16 595.14 L 1426.16 618" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv10" d="M 1426.16 618 L 1426.16 798.29" stroke-width="1" zvalue="1848"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41" LinkObjectIDznd="148@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1426.16 618 L 1426.16 798.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="kv10" d="M 1233.38 328.26 L 1233.38 507.81" stroke-width="1" zvalue="1900"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@0" LinkObjectIDznd="60@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1233.38 328.26 L 1233.38 507.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv10" d="M 962 510.81 L 962 381 L 1233.38 381" stroke-width="1" zvalue="1901"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="6@1" LinkObjectIDznd="86" MaxPinNum="2"/>
   </metadata>
  <path d="M 962 510.81 L 962 381 L 1233.38 381" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv10" d="M 1236.11 569.75 L 1236.11 507.81" stroke-width="1" zvalue="1902"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="11@1" LinkObjectIDznd="60@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1236.11 569.75 L 1236.11 507.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="AccessoryClass">
  <g id="114">
   <use class="kv10" height="20" transform="rotate(0,1215.36,674.5) scale(2.36905,3.39286) translate(-688.652,-451.771)" width="20" x="1191.666666666667" xlink:href="#Accessory:线路PT3_0" y="640.5714285714286" zvalue="1739"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449900642310" ObjectName="电压互感器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1215.36,674.5) scale(2.36905,3.39286) translate(-688.652,-451.771)" width="20" x="1191.666666666667" y="640.5714285714286"/></g>
  <g id="140">
   <use class="kv10" height="26" transform="rotate(0,1450.62,647.857) scale(1.07143,1.42857) translate(-96.2794,-188.786)" width="12" x="1444.190476190476" xlink:href="#Accessory:避雷器1_0" y="629.2857142857143" zvalue="1764"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449900838918" ObjectName="10kV电站升压线避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1450.62,647.857) scale(1.07143,1.42857) translate(-96.2794,-188.786)" width="12" x="1444.190476190476" y="629.2857142857143"/></g>
  <g id="9">
   <use class="kv10" height="30" transform="rotate(0,1244.07,721.444) scale(1.63265,-2.66667) translate(-471.005,-966.986)" width="35" x="1215.497450472627" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="681.4444444444443" zvalue="1819"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449900969990" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1244.07,721.444) scale(1.63265,-2.66667) translate(-471.005,-966.986)" width="35" x="1215.497450472627" y="681.4444444444443"/></g>
  <g id="77">
   <use class="kv10" height="40" transform="rotate(0,1233.38,294.5) scale(1.825,1.825) translate(-545.178,-116.63)" width="30" x="1206" xlink:href="#Accessory:带熔断器的线路PT1_0" y="258" zvalue="1899"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449901166598" ObjectName="站用变"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1233.38,294.5) scale(1.825,1.825) translate(-545.178,-116.63)" width="30" x="1206" y="258"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="139">
   <use class="kv10" height="20" transform="rotate(0,1402.76,646.143) scale(1.42857,1.42857) translate(-418.686,-189.557)" width="10" x="1395.619047619047" xlink:href="#GroundDisconnector:地刀_0" y="631.8571428571428" zvalue="1765"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449900773382" ObjectName="10kV电站升压线02267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449900773382"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1402.76,646.143) scale(1.42857,1.42857) translate(-418.686,-189.557)" width="10" x="1395.619047619047" y="631.8571428571428"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="148">
   <use class="kv10" height="30" transform="rotate(0,1426.16,817.571) scale(1.42857,-1.42857) translate(-425.278,-1383.44)" width="12" x="1417.592855585178" xlink:href="#EnergyConsumer:负荷_0" y="796.1428571428571" zvalue="1769"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449900904454" ObjectName="10kV电站升压线"/>
   <cge:TPSR_Ref TObjectID="6192449900904454"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1426.16,817.571) scale(1.42857,-1.42857) translate(-425.278,-1383.44)" width="12" x="1417.592855585178" y="796.1428571428571"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="11">
   <use class="kv10" height="33" transform="rotate(0,1236.11,595.143) scale(1.5873,-1.5873) translate(-453.25,-960.392)" width="14" x="1225" xlink:href="#Disconnector:手车隔离开关13_0" y="568.9523809523814" zvalue="1820"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449901035526" ObjectName="10kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449901035526"/></metadata>
  <rect fill="white" height="33" opacity="0" stroke="white" transform="rotate(0,1236.11,595.143) scale(1.5873,-1.5873) translate(-453.25,-960.392)" width="14" x="1225" y="568.9523809523814"/></g>
 </g>
 <g id="StateClass">
  <g id="897">
   <use height="30" transform="rotate(0,294.339,302.643) scale(0.708333,0.665547) translate(116.824,147.069)" width="30" x="283.71" xlink:href="#State:红绿圆(方形)_0" y="292.66" zvalue="1891"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374890631171" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,294.339,302.643) scale(0.708333,0.665547) translate(116.824,147.069)" width="30" x="283.71" y="292.66"/></g>
  <g id="56">
   <use height="30" transform="rotate(0,198.714,302.643) scale(0.708333,0.665547) translate(77.4485,147.069)" width="30" x="188.09" xlink:href="#State:红绿圆(方形)_0" y="292.66" zvalue="1892"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,198.714,302.643) scale(0.708333,0.665547) translate(77.4485,147.069)" width="30" x="188.09" y="292.66"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="58">
   <use class="kv10" height="40" transform="rotate(0,842.081,278) scale(-1,1) translate(-1684.16,0)" width="40" x="822.0810439560439" xlink:href="#ACLineSegment:线路d_0" y="258" zvalue="1894"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449901232134" ObjectName="10kV瑞丽大道线T地美特地热电站支线"/>
   <cge:TPSR_Ref TObjectID="6192449901232134_5066549589835778"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,842.081,278) scale(-1,1) translate(-1684.16,0)" width="40" x="822.0810439560439" y="258"/></g>
 </g>
</svg>