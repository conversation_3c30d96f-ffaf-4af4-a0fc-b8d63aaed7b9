<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549594095618" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="16" y2="23"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.499999999999999" x2="5.916666666666666" y1="3.583333333333334" y2="16"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_1" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.05" x2="6.05" y1="3.333333333333332" y2="22.83333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀12_2" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="5.988532960701467" xlink:href="#terminal" y="0.6763344575938497"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.91666666666667" x2="1.166666666666666" y1="4.416666666666668" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="0.9166666666666652" x2="11.08333333333334" y1="23.03810844520533" y2="23.03810844520533"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.001023303521627" x2="9.113962766934051" y1="26.00141011152559" y2="26.00141011152559"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="4.4959248360414" x2="7.552394567747612" y1="29.01471177784586" y2="29.01471177784586"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.057493035227839" x2="6.057493035227839" y1="23" y2="21.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="6.041506727682536" x2="6.041506727682536" y1="3.000000000000004" y2="1.005461830931415"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.666666666666666" x2="8.199999999999999" y1="3.07232884821164" y2="3.07232884821164"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.166666666666666" x2="10.91666666666667" y1="4.249999999999998" y2="20.16666666666667"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Accessory:带熔断器的线路PT1_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="3.166666666666664" y2="5.916666666666666"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.916666666666663" y2="7.833333333333329"/>
   <rect fill-opacity="0" height="10.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,28.67) scale(1,1) translate(0,0)" width="6" x="12" y="23.33"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.75" y2="38.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.916666666666666" y2="7.916666666666666"/>
   <ellipse cx="15.15" cy="13.77" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.67" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11.25" y2="13.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.75" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.75" y2="15.66666666666666"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="Accessory:PT带保险_0" viewBox="0,0,11,29">
   <use terminal-index="0" type="0" x="5.5" xlink:href="#terminal" y="0.1666666666666661"/>
   <rect fill-opacity="0" height="6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.5,7.5) scale(1,1) translate(0,0)" width="4" x="3.5" y="4.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.5" x2="5.5" y1="13.16666666666667" y2="0.5"/>
   <ellipse cx="5.4" cy="18.1" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.4" cy="23.78" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.2961056647909643" x2="6.696709211158367" y1="24.60160217070812" y2="13.72695975521216"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_1" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.78559810004726" x2="6.78559810004726" y1="26.16296296296296" y2="13.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289653" x2="29.64630681289653" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_2" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.066666666666666" x2="6.896709211158374" y1="24.26666666666667" y2="15.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_3" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <path d="M 38.0803 7.70707 L 6.77877 7.70707 L 6.77877 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
  </symbol>
  <symbol id="GroundDisconnector:中性点地刀12_4" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="6.6" xlink:href="#terminal" y="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.6" x2="22.6" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.66666666666667" x2="22.66666666666667" y1="16.2" y2="32.13333333333334"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.761096627696045" x2="6.761096627696045" y1="7.901258461196443" y2="4.405837684787034"/>
   <path d="M 38.0358 7.70707 L 6.73432 7.70707 L 6.73432 13.8241" fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="7.800000000000001" y2="16.86666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.6435120638601" x2="22.6435120638601" y1="12.36762945327518" y2="7.707068418062621"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="22.4" x2="38.06666666666666" y1="32.2" y2="32.2"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="38" x2="38" y1="32.2" y2="23.4"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.266666666666667" x2="9.296709211158374" y1="24.6" y2="15.06666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.655417212520412" x2="6.655417212520412" y1="26.25221753734589" y2="32.27210887449543"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="29.64630681289654" x2="29.64630681289654" y1="34.60238939210173" y2="34.60238939210173"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.553448152371283" x2="4.055597865519431" y1="4.282151077943599" y2="4.282151077943599"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.419299688863541" x2="4.754066164295653" y1="3.060406569754363" y2="3.060406569754363"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.753392399508419" x2="6.073493700748143" y1="2.144098188612368" y2="2.144098188612368"/>
   <rect fill-opacity="0" height="13.77" rx="0" ry="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,22.65,19.27) scale(-1,1) translate(-2044.43,0)" width="6.98" x="19.16" y="12.39"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.894641292589732" x2="10.33465909007663" y1="26.26503930598858" y2="26.26503930598858"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.199999999999994" x2="4.33333333333333" y1="24.66666666666666" y2="15"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.027974625923065" x2="10.46799242340996" y1="19.93170597265525" y2="19.93170597265525"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="14.63292181069959"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="9.583333333333334" y2="14.63582329690123"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00459493611119" x2="10.08333333333333" y1="14.63240401999107" y2="19.41666666666666"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.01979386477084" x2="20.08333333333333" y1="14.63518747193215" y2="19.41666666666666"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D带中性点_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 10.1667 40.3233 L 19.9167 40.3233 L 15.0833 32.3333 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV南司龙一级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="60" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="35" xlink:href="logo.png" y="40.67"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,184,70.6667) scale(1,1) translate(0,0)" writing-mode="lr" x="184" xml:space="preserve" y="74.17" zvalue="2"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,180.5,70.357) scale(1,1) translate(0,0)" writing-mode="lr" x="180.5" xml:space="preserve" y="79.36" zvalue="3">110kV南司龙一级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="1" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,92.4375,225) scale(1,1) translate(0,0)" width="72.88" x="56" y="213" zvalue="199"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.4375,225) scale(1,1) translate(0,0)" writing-mode="lr" x="92.44" xml:space="preserve" y="229.5" zvalue="199">信号一览</text>
  <line fill="none" id="35" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="377" x2="377" y1="8.666666666666629" y2="1038.666666666667" zvalue="4"/>
  <line fill="none" id="33" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.000000000000227" x2="369.9999999999998" y1="144.5371592807491" y2="144.5371592807491" zvalue="6"/>
  <line fill="none" id="31" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.000000000000227" x2="369.9999999999998" y1="614.5371592807491" y2="614.5371592807491" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3" x2="93" y1="929.6666666666665" y2="929.6666666666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3" x2="93" y1="968.8299666666666" y2="968.8299666666666"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3" x2="3" y1="929.6666666666665" y2="968.8299666666666"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93" x2="93" y1="929.6666666666665" y2="968.8299666666666"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93" x2="363" y1="929.6666666666665" y2="929.6666666666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93" x2="363" y1="968.8299666666666" y2="968.8299666666666"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93" x2="93" y1="929.6666666666665" y2="968.8299666666666"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363" x2="363" y1="929.6666666666665" y2="968.8299666666666"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3" x2="93" y1="968.8299366666665" y2="968.8299366666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3" x2="93" y1="996.7483366666665" y2="996.7483366666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3" x2="3" y1="968.8299366666665" y2="996.7483366666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93" x2="93" y1="968.8299366666665" y2="996.7483366666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93" x2="183" y1="968.8299366666665" y2="968.8299366666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93" x2="183" y1="996.7483366666665" y2="996.7483366666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93" x2="93" y1="968.8299366666665" y2="996.7483366666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183" x2="183" y1="968.8299366666665" y2="996.7483366666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000001" x2="273.0000000000001" y1="968.8299366666665" y2="968.8299366666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000001" x2="273.0000000000001" y1="996.7483366666665" y2="996.7483366666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="968.8299366666665" y2="996.7483366666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000001" x2="273.0000000000001" y1="968.8299366666665" y2="996.7483366666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273" x2="363" y1="968.8299366666665" y2="968.8299366666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273" x2="363" y1="996.7483366666665" y2="996.7483366666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273" x2="273" y1="968.8299366666665" y2="996.7483366666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363" x2="363" y1="968.8299366666665" y2="996.7483366666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3" x2="93" y1="996.7482666666665" y2="996.7482666666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3" x2="93" y1="1024.666666666667" y2="1024.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="3" x2="3" y1="996.7482666666665" y2="1024.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93" x2="93" y1="996.7482666666665" y2="1024.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93" x2="183" y1="996.7482666666665" y2="996.7482666666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93" x2="183" y1="1024.666666666667" y2="1024.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="93" x2="93" y1="996.7482666666665" y2="1024.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183" x2="183" y1="996.7482666666665" y2="1024.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000001" x2="273.0000000000001" y1="996.7482666666665" y2="996.7482666666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000001" x2="273.0000000000001" y1="1024.666666666667" y2="1024.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="183.0000000000001" x2="183.0000000000001" y1="996.7482666666665" y2="1024.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273.0000000000001" x2="273.0000000000001" y1="996.7482666666665" y2="1024.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273" x2="363" y1="996.7482666666665" y2="996.7482666666665"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273" x2="363" y1="1024.666666666667" y2="1024.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="273" x2="273" y1="996.7482666666665" y2="1024.666666666667"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="363" x2="363" y1="996.7482666666665" y2="1024.666666666667"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,48,949.667) scale(1,1) translate(0,0)" writing-mode="lr" x="48" xml:space="preserve" y="955.67" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="26" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,45,983.667) scale(1,1) translate(0,0)" writing-mode="lr" x="45" xml:space="preserve" y="989.67" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="25" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227,983.667) scale(1,1) translate(0,0)" writing-mode="lr" x="227" xml:space="preserve" y="989.67" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="24" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,44,1011.67) scale(1,1) translate(0,0)" writing-mode="lr" x="44" xml:space="preserve" y="1017.67" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="23" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,226,1011.67) scale(1,1) translate(0,0)" writing-mode="lr" x="226" xml:space="preserve" y="1017.67" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="20" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,68.5,644.167) scale(1,1) translate(0,0)" writing-mode="lr" x="68.5" xml:space="preserve" y="648.6666666666666" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,206.399,224.508) scale(1,1) translate(0,0)" writing-mode="lr" x="206.4" xml:space="preserve" y="229.01" zvalue="20">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,311.399,224.508) scale(1,1) translate(0,0)" writing-mode="lr" x="311.4" xml:space="preserve" y="229.01" zvalue="21">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="11" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,200.527,951.667) scale(1,1) translate(-2.66574e-14,0)" writing-mode="lr" x="200.53" xml:space="preserve" y="957.67" zvalue="28">NanSiLong-2014-1</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,138.054,983.667) scale(1,1) translate(0,0)" writing-mode="lr" x="138.05" xml:space="preserve" y="989.67" zvalue="29">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,318.054,983.667) scale(1,1) translate(0,0)" writing-mode="lr" x="318.05" xml:space="preserve" y="989.67" zvalue="30">20210302</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,555.5,582.375) scale(1,1) translate(0,0)" writing-mode="lr" x="555.5" xml:space="preserve" y="586.88" zvalue="43">6.3kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" x="622.2578125" xml:space="preserve" y="827.6962240134186" zvalue="46">#1发电机       </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="622.2578125" xml:space="preserve" y="843.6962240134186" zvalue="46">2MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,695.107,700.083) scale(1,1) translate(0,0)" writing-mode="lr" x="695.11" xml:space="preserve" y="704.58" zvalue="50">641</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,687.145,640.306) scale(1,1) translate(0,0)" writing-mode="lr" x="687.14" xml:space="preserve" y="644.8099999999999" zvalue="52">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,731.849,642.552) scale(1,1) translate(0,0)" writing-mode="lr" x="731.85" xml:space="preserve" y="647.05" zvalue="54">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,778.792,761.222) scale(1,1) translate(0,0)" writing-mode="lr" x="778.79" xml:space="preserve" y="765.72" zvalue="83">6911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,810.5,763.75) scale(1,1) translate(0,0)" writing-mode="lr" x="810.5" xml:space="preserve" y="768.25" zvalue="89">7</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="89" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1054.07,836.955) scale(1,1) translate(0,2.71848e-13)" writing-mode="lr" x="1054.067759012959" xml:space="preserve" y="841.4547398511552" zvalue="99">#2发电机    2MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="88" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1137.54,704.208) scale(1,1) translate(0,0)" writing-mode="lr" x="1137.54" xml:space="preserve" y="708.71" zvalue="102">642</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1129.58,648.431) scale(1,1) translate(0,0)" writing-mode="lr" x="1129.58" xml:space="preserve" y="652.9299999999999" zvalue="104">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1174.29,650.677) scale(1,1) translate(0,0)" writing-mode="lr" x="1174.29" xml:space="preserve" y="655.1799999999999" zvalue="106">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1221.23,769.347) scale(1,1) translate(0,0)" writing-mode="lr" x="1221.23" xml:space="preserve" y="773.85" zvalue="116">6921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1252.94,771.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1252.94" xml:space="preserve" y="776.37" zvalue="121">7</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1478.09,849.639) scale(1,1) translate(0,0)" writing-mode="lr" x="1478.09" xml:space="preserve" y="854.14" zvalue="149">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1562.92,706.472) scale(1,1) translate(0,0)" writing-mode="lr" x="1562.92" xml:space="preserve" y="710.97" zvalue="157">6431</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" x="818.25" xml:space="preserve" y="350.3346774193549" zvalue="168">#1主变       </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="818.25" xml:space="preserve" y="366.3346774193549" zvalue="168">5MVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="44" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,906.031,466.556) scale(1,1) translate(0,-2.03418e-13)" writing-mode="lr" x="906.03" xml:space="preserve" y="471.06" zvalue="175">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,897.403,554.111) scale(1,1) translate(0,0)" writing-mode="lr" x="897.4" xml:space="preserve" y="558.61" zvalue="176">1</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,950.611,514.123) scale(1,1) translate(0,0)" writing-mode="lr" x="950.61" xml:space="preserve" y="518.62" zvalue="180">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1031.5,348.107) scale(1,1) translate(0,0)" writing-mode="lr" x="1031.5" xml:space="preserve" y="352.61" zvalue="187">1010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,884,137.5) scale(1,1) translate(0,0)" writing-mode="lr" x="884" xml:space="preserve" y="142" zvalue="195">110kV南司龙一级线</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="273" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="299" y2="299"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="273" y2="299"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="273" y2="299"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="273" y2="273"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="299" y2="299"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="273" y2="299"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="273" y2="299"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="299" y2="299"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="323.25" y2="323.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="299" y2="323.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="299" y2="323.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="299" y2="299"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="323.25" y2="323.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="299" y2="323.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="299" y2="323.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="323.25" y2="323.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="346" y2="346"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="323.25" y2="346"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="323.25" y2="346"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="323.25" y2="323.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="346" y2="346"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="323.25" y2="346"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="323.25" y2="346"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="346" y2="346"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="368.75" y2="368.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="346" y2="368.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="346" y2="368.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="346" y2="346"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="368.75" y2="368.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="346" y2="368.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="346" y2="368.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="368.75" y2="368.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="391.5" y2="391.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="368.75" y2="391.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="368.75" y2="391.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="368.75" y2="368.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="391.5" y2="391.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="368.75" y2="391.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="368.75" y2="391.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="391.5" y2="391.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="414.25" y2="414.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="391.5" y2="414.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="391.5" y2="414.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="391.5" y2="391.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="414.25" y2="414.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="391.5" y2="414.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="391.5" y2="414.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="414.25" y2="414.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="188" y1="437" y2="437"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="7" x2="7" y1="414.25" y2="437"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="414.25" y2="437"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="414.25" y2="414.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="369" y1="437" y2="437"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="188" x2="188" y1="414.25" y2="437"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="369" x2="369" y1="414.25" y2="437"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="129" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,53.5,286) scale(1,1) translate(0,0)" writing-mode="lr" x="11" xml:space="preserve" y="290.5" zvalue="208">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="128" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,234,286) scale(1,1) translate(0,0)" writing-mode="lr" x="191.5" xml:space="preserve" y="290.5" zvalue="209">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="127" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.6875,359.25) scale(1,1) translate(0,0)" writing-mode="lr" x="56.69" xml:space="preserve" y="363.75" zvalue="210">6.3kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,52.5,312) scale(1,1) translate(0,0)" writing-mode="lr" x="10" xml:space="preserve" y="316.5" zvalue="216">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,233,312) scale(1,1) translate(0,0)" writing-mode="lr" x="190.5" xml:space="preserve" y="316.5" zvalue="217">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="96" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.6875,405.25) scale(1,1) translate(0,0)" writing-mode="lr" x="53.69" xml:space="preserve" y="409.75" zvalue="220">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,221.688,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="221.69" xml:space="preserve" y="408.75" zvalue="222">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.6875,428.25) scale(1,1) translate(0,0)" writing-mode="lr" x="53.69" xml:space="preserve" y="432.75" zvalue="223">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,221.688,427.25) scale(1,1) translate(0,0)" writing-mode="lr" x="221.69" xml:space="preserve" y="431.75" zvalue="224">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,52.5,335) scale(1,1) translate(0,0)" writing-mode="lr" x="10" xml:space="preserve" y="339.5" zvalue="225">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,232.5,334) scale(1,1) translate(0,0)" writing-mode="lr" x="190" xml:space="preserve" y="338.5" zvalue="227">厂用电率</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="56" y="213" zvalue="199"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="42">
   <path class="v6300" d="M 527 600.5 L 1693 600.5" stroke-width="6" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674257141764" ObjectName="6.3kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674257141764"/></metadata>
  <path d="M 527 600.5 L 1693 600.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v6300" height="30" transform="rotate(0,675.966,824.386) scale(1.85899,1.85899) translate(-299.461,-368.042)" width="30" x="648.0809586230847" xlink:href="#Generator:发电机_0" y="796.5010218856316" zvalue="45"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450073133061" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192450073133061"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,675.966,824.386) scale(1.85899,1.85899) translate(-299.461,-368.042)" width="30" x="648.0809586230847" y="796.5010218856316"/></g>
  <g id="111">
   <use class="v6300" height="30" transform="rotate(0,1118.4,832.511) scale(1.85899,1.85899) translate(-503.9,-371.796)" width="30" x="1090.518458623085" xlink:href="#Generator:发电机_0" y="804.6260218856315" zvalue="98"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450074247173" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192450074247173"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1118.4,832.511) scale(1.85899,1.85899) translate(-503.9,-371.796)" width="30" x="1090.518458623085" y="804.6260218856315"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="185">
   <use class="v6300" height="40" transform="rotate(0,850.02,859.48) scale(1.25,-0.90625) translate(-166.254,-1809.75)" width="30" x="831.2698833577778" xlink:href="#Accessory:带熔断器的线路PT1_0" y="841.3551567925347" zvalue="47"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450073067525" ObjectName="#1发电机励磁变"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,850.02,859.48) scale(1.25,-0.90625) translate(-166.254,-1809.75)" width="30" x="831.2698833577778" y="841.3551567925347"/></g>
  <g id="66">
   <use class="v6300" height="20" transform="rotate(0,604.656,794.906) scale(1.54688,1.54688) translate(-208.299,-275.559)" width="20" x="589.1875000000002" xlink:href="#Accessory:线路PT3_0" y="779.4374999999999" zvalue="79"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450073198597" ObjectName="#1发电机线路PT3"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,604.656,794.906) scale(1.54688,1.54688) translate(-208.299,-275.559)" width="20" x="589.1875000000002" y="779.4374999999999"/></g>
  <g id="80">
   <use class="v6300" height="29" transform="rotate(0,795.75,860.219) scale(1.04526,1.04526) translate(-34.2062,-36.5903)" width="11" x="790.001077586207" xlink:href="#Accessory:PT带保险_0" y="845.0624999999998" zvalue="93"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450073460741" ObjectName="#1发电机避雷器1"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,795.75,860.219) scale(1.04526,1.04526) translate(-34.2062,-36.5903)" width="11" x="790.001077586207" y="845.0624999999998"/></g>
  <g id="81">
   <use class="v6300" height="18" transform="rotate(0,759.969,863.187) scale(1.35417,1.35417) translate(-196.105,-222.569)" width="15" x="749.8125000000001" xlink:href="#Accessory:PT8_0" y="850.9999999999998" zvalue="94"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450073526277" ObjectName="#1发电机PT8"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,759.969,863.187) scale(1.35417,1.35417) translate(-196.105,-222.569)" width="15" x="749.8125000000001" y="850.9999999999998"/></g>
  <g id="110">
   <use class="v6300" height="40" transform="rotate(0,1292.46,867.605) scale(1.25,-0.90625) translate(-254.741,-1826.84)" width="30" x="1273.707383357778" xlink:href="#Accessory:带熔断器的线路PT1_0" y="849.4801567925344" zvalue="100"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450074181637" ObjectName="#2发电机励磁变"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,1292.46,867.605) scale(1.25,-0.90625) translate(-254.741,-1826.84)" width="30" x="1273.707383357778" y="849.4801567925344"/></g>
  <g id="101">
   <use class="v6300" height="20" transform="rotate(0,1047.09,803.031) scale(1.54688,1.54688) translate(-364.716,-278.431)" width="20" x="1031.625" xlink:href="#Accessory:线路PT3_0" y="787.5624999999998" zvalue="112"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450073919493" ObjectName="#2发电机线路PT3"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1047.09,803.031) scale(1.54688,1.54688) translate(-364.716,-278.431)" width="20" x="1031.625" y="787.5624999999998"/></g>
  <g id="92">
   <use class="v6300" height="29" transform="rotate(0,1238.19,868.344) scale(1.04526,1.04526) translate(-53.3633,-36.9421)" width="11" x="1232.438577586207" xlink:href="#Accessory:PT带保险_0" y="853.1874999999998" zvalue="123"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450073657349" ObjectName="#2发电机避雷器1"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1238.19,868.344) scale(1.04526,1.04526) translate(-53.3633,-36.9421)" width="11" x="1232.438577586207" y="853.1874999999998"/></g>
  <g id="91">
   <use class="v6300" height="18" transform="rotate(0,1203.31,870.687) scale(1.35417,1.35417) translate(-312.055,-224.531)" width="15" x="1193.151315642951" xlink:href="#Accessory:PT8_0" y="858.4999999999998" zvalue="124"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450073591813" ObjectName="#2发电机PT8"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1203.31,870.687) scale(1.35417,1.35417) translate(-312.055,-224.531)" width="15" x="1193.151315642951" y="858.4999999999998"/></g>
  <g id="90">
   <use class="v6300" height="18" transform="rotate(0,797.417,527.25) scale(-2.63889,2.63889) translate(-1087.3,-312.7)" width="15" x="777.625" xlink:href="#Accessory:PT8_0" y="503.5" zvalue="190"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450074771461" ObjectName="#1主变低压侧接地PT8"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,797.417,527.25) scale(-2.63889,2.63889) translate(-1087.3,-312.7)" width="15" x="777.625" y="503.5"/></g>
 </g>
 <g id="BreakerClass">
  <g id="259">
   <use class="v6300" height="20" transform="rotate(0,675.884,701.083) scale(1.22222,1.11111) translate(-121.777,-68.9972)" width="10" x="669.7732191828454" xlink:href="#Breaker:开关_0" y="689.9722220102948" zvalue="49"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924565073924" ObjectName="#1发电机641断路器"/>
   <cge:TPSR_Ref TObjectID="6473924565073924"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,675.884,701.083) scale(1.22222,1.11111) translate(-121.777,-68.9972)" width="10" x="669.7732191828454" y="689.9722220102948"/></g>
  <g id="109">
   <use class="v6300" height="20" transform="rotate(0,1118.32,705.208) scale(1.22222,1.11111) translate(-202.22,-69.4097)" width="10" x="1112.210719182845" xlink:href="#Breaker:开关_0" y="694.0972220102947" zvalue="101"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924565139460" ObjectName="#2发电机642断路器"/>
   <cge:TPSR_Ref TObjectID="6473924565139460"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1118.32,705.208) scale(1.22222,1.11111) translate(-202.22,-69.4097)" width="10" x="1112.210719182845" y="694.0972220102947"/></g>
  <g id="237">
   <use class="v6300" height="20" transform="rotate(0,884.698,467.556) scale(1.22222,1.11111) translate(-159.743,-45.6444)" width="10" x="878.5869769926908" xlink:href="#Breaker:开关_0" y="456.4444442325169" zvalue="173"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924565204996" ObjectName="#1主变6.3kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924565204996"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,884.698,467.556) scale(1.22222,1.11111) translate(-159.743,-45.6444)" width="10" x="878.5869769926908" y="456.4444442325169"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="261">
   <use class="v6300" height="30" transform="rotate(0,674.589,641.306) scale(-1.11111,-0.814815) translate(-1280.89,-1431.14)" width="15" x="666.2559944688888" xlink:href="#Disconnector:刀闸_0" y="629.0833466847738" zvalue="51"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450073001989" ObjectName="#1发电机6411隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450073001989"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,674.589,641.306) scale(-1.11111,-0.814815) translate(-1280.89,-1431.14)" width="15" x="666.2559944688888" y="629.0833466847738"/></g>
  <g id="68">
   <use class="v6300" height="30" transform="rotate(0,759.708,762.222) scale(-1.11111,-0.814815) translate(-1442.61,-1700.45)" width="15" x="751.3750000000001" xlink:href="#Disconnector:刀闸_0" y="749.9999999999999" zvalue="82"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450073264133" ObjectName="#1发电机6911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450073264133"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,759.708,762.222) scale(-1.11111,-0.814815) translate(-1442.61,-1700.45)" width="15" x="751.3750000000001" y="749.9999999999999"/></g>
  <g id="108">
   <use class="v6300" height="30" transform="rotate(0,1117.03,649.431) scale(-1.11111,-0.814815) translate(-2121.52,-1449.24)" width="15" x="1108.693494468889" xlink:href="#Disconnector:刀闸_0" y="637.2083466847738" zvalue="103"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450074116101" ObjectName="#2发电机6421隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450074116101"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1117.03,649.431) scale(-1.11111,-0.814815) translate(-2121.52,-1449.24)" width="15" x="1108.693494468889" y="637.2083466847738"/></g>
  <g id="99">
   <use class="v6300" height="30" transform="rotate(0,1202.15,770.347) scale(-1.11111,-0.814815) translate(-2283.24,-1718.55)" width="15" x="1193.8125" xlink:href="#Disconnector:刀闸_0" y="758.1249999999997" zvalue="114"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450073853957" ObjectName="#2发电机6921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450073853957"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1202.15,770.347) scale(-1.11111,-0.814815) translate(-2283.24,-1718.55)" width="15" x="1193.8125" y="758.1249999999997"/></g>
  <g id="52">
   <use class="v6300" height="30" transform="rotate(0,1535.08,707.472) scale(-1.11111,-0.814815) translate(-2915.82,-1578.51)" width="15" x="1526.75" xlink:href="#Disconnector:刀闸_0" y="695.25" zvalue="156"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450074378245" ObjectName="#1厂用变6431隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450074378245"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1535.08,707.472) scale(-1.11111,-0.814815) translate(-2915.82,-1578.51)" width="15" x="1526.75" y="695.25"/></g>
  <g id="236">
   <use class="v6300" height="30" transform="rotate(0,884.848,555.111) scale(-1.11111,-0.814815) translate(-1680.38,-1239.16)" width="15" x="876.5141967231787" xlink:href="#Disconnector:刀闸_0" y="542.8889022403295" zvalue="174"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450074705925" ObjectName="#1主变6.3kV侧6011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192450074705925"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,884.848,555.111) scale(-1.11111,-0.814815) translate(-1680.38,-1239.16)" width="15" x="876.5141967231787" y="542.8889022403295"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="266">
   <use class="v6300" height="30" transform="rotate(270,725.183,665.663) scale(1,1) translate(0,0)" width="12" x="719.1825396825398" xlink:href="#GroundDisconnector:地刀12_0" y="650.6626984126983" zvalue="53"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450072936453" ObjectName="#1发电机64117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450072936453"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,725.183,665.663) scale(1,1) translate(0,0)" width="12" x="719.1825396825398" y="650.6626984126983"/></g>
  <g id="74">
   <use class="v6300" height="30" transform="rotate(270,809.875,782.625) scale(1,1) translate(0,0)" width="12" x="803.8750000000001" xlink:href="#GroundDisconnector:地刀12_0" y="767.6249999999999" zvalue="88"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450073395206" ObjectName="#1发电机69117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450073395206"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,809.875,782.625) scale(1,1) translate(0,0)" width="12" x="803.8750000000001" y="767.6249999999999"/></g>
  <g id="107">
   <use class="v6300" height="30" transform="rotate(270,1167.62,673.788) scale(1,1) translate(0,0)" width="12" x="1161.62003968254" xlink:href="#GroundDisconnector:地刀12_0" y="658.7876984126982" zvalue="105"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450074050566" ObjectName="#2发电机64217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450074050566"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1167.62,673.788) scale(1,1) translate(0,0)" width="12" x="1161.62003968254" y="658.7876984126982"/></g>
  <g id="94">
   <use class="v6300" height="30" transform="rotate(270,1252.31,790.75) scale(1,1) translate(0,0)" width="12" x="1246.3125" xlink:href="#GroundDisconnector:地刀12_0" y="775.7499999999998" zvalue="120"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450073788422" ObjectName="#2发电机69217接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450073788422"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1252.31,790.75) scale(1,1) translate(0,0)" width="12" x="1246.3125" y="775.7499999999998"/></g>
  <g id="231">
   <use class="v6300" height="30" transform="rotate(270,949.611,528.123) scale(1,1) translate(0,0)" width="12" x="943.6111111111111" xlink:href="#GroundDisconnector:地刀12_0" y="513.1234535956221" zvalue="179"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450074640389" ObjectName="#1主变6.3kV侧60117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450074640389"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,949.611,528.123) scale(1,1) translate(0,0)" width="12" x="943.6111111111111" y="513.1234535956221"/></g>
  <g id="63">
   <use class="kv110" height="40" transform="rotate(0,995.5,349.107) scale(1,-1) translate(0,-698.214)" width="40" x="975.5" xlink:href="#GroundDisconnector:中性点地刀12_0" y="329.1071428571429" zvalue="186"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450074509317" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192450074509317"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,995.5,349.107) scale(1,-1) translate(0,-698.214)" width="40" x="975.5" y="329.1071428571429"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="62">
   <path class="v6300" d="M 674.52 629.29 L 674.52 600.5" stroke-width="1" zvalue="55"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@1" LinkObjectIDznd="42@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 674.52 629.29 L 674.52 600.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="61">
   <path class="v6300" d="M 674.49 653.12 L 674.49 690.45" stroke-width="1" zvalue="56"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@0" LinkObjectIDznd="259@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 674.49 653.12 L 674.49 690.45" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="60">
   <path class="v6300" d="M 710.86 665.67 L 674.49 665.67" stroke-width="1" zvalue="57"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="266@0" LinkObjectIDznd="61" MaxPinNum="2"/>
   </metadata>
  <path d="M 710.86 665.67 L 674.49 665.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="59">
   <path class="v6300" d="M 675.97 711.69 L 675.97 796.97" stroke-width="1" zvalue="58"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="259@1" LinkObjectIDznd="850@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 675.97 711.69 L 675.97 796.97" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="v6300" d="M 675.97 726 L 850.02 726 L 850.02 842.71" stroke-width="1" zvalue="77"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59" LinkObjectIDznd="185@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 675.97 726 L 850.02 726 L 850.02 842.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="v6300" d="M 675.97 726 L 604.66 726 L 604.66 784.08" stroke-width="1" zvalue="80"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="64" LinkObjectIDznd="66@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 675.97 726 L 604.66 726 L 604.66 784.08" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="69">
   <path class="v6300" d="M 759.64 750.21 L 759.64 726" stroke-width="1" zvalue="83"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@1" LinkObjectIDznd="64" MaxPinNum="2"/>
   </metadata>
  <path d="M 759.64 750.21 L 759.64 726" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="106">
   <path class="v6300" d="M 1116.96 637.42 L 1116.96 600.5" stroke-width="1" zvalue="107"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@1" LinkObjectIDznd="42@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1116.96 637.42 L 1116.96 600.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="v6300" d="M 1116.93 661.25 L 1116.93 694.58" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="109@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1116.93 661.25 L 1116.93 694.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="v6300" d="M 1153.3 673.8 L 1116.93 673.8" stroke-width="1" zvalue="109"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="105" MaxPinNum="2"/>
   </metadata>
  <path d="M 1153.3 673.8 L 1116.93 673.8" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="v6300" d="M 1118.4 715.82 L 1118.4 805.09" stroke-width="1" zvalue="110"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@1" LinkObjectIDznd="111@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1118.4 715.82 L 1118.4 805.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="102">
   <path class="v6300" d="M 1118.4 734.12 L 1292.46 734.12 L 1292.46 850.84" stroke-width="1" zvalue="111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103" LinkObjectIDznd="110@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1118.4 734.12 L 1292.46 734.12 L 1292.46 850.84" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="v6300" d="M 1118.4 734.12 L 1047.09 734.12 L 1047.09 792.2" stroke-width="1" zvalue="113"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102" LinkObjectIDznd="101@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1118.4 734.12 L 1047.09 734.12 L 1047.09 792.2" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="v6300" d="M 1202.08 758.33 L 1202.08 734.12" stroke-width="1" zvalue="115"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@1" LinkObjectIDznd="102" MaxPinNum="2"/>
   </metadata>
  <path d="M 1202.08 758.33 L 1202.08 734.12" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="v6300" d="M 1202.05 782.17 L 1202.05 820.77 L 1202.05 820.77 L 1202.05 859.37" stroke-width="1" zvalue="135"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="99@0" LinkObjectIDznd="91@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1202.05 782.17 L 1202.05 820.77 L 1202.05 820.77 L 1202.05 859.37" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="v6300" d="M 1238.19 853.36 L 1238.19 818.75 L 1202.05 818.75" stroke-width="1" zvalue="136"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="121" MaxPinNum="2"/>
   </metadata>
  <path d="M 1238.19 853.36 L 1238.19 818.75 L 1202.05 818.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="123">
   <path class="v6300" d="M 1237.99 790.76 L 1202.05 790.76" stroke-width="1" zvalue="137"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="121" MaxPinNum="2"/>
   </metadata>
  <path d="M 1237.99 790.76 L 1202.05 790.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="124">
   <path class="v6300" d="M 759.61 774.04 L 759.61 851.87" stroke-width="1" zvalue="138"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="68@0" LinkObjectIDznd="81@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 759.61 774.04 L 759.61 851.87" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="v6300" d="M 795.55 782.64 L 759.61 782.64" stroke-width="1" zvalue="139"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="74@0" LinkObjectIDznd="124" MaxPinNum="2"/>
   </metadata>
  <path d="M 795.55 782.64 L 759.61 782.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="v6300" d="M 795.75 845.24 L 795.75 809.75 L 759.61 809.75" stroke-width="1" zvalue="140"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@0" LinkObjectIDznd="124" MaxPinNum="2"/>
   </metadata>
  <path d="M 795.75 845.24 L 795.75 809.75 L 759.61 809.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="55">
   <path class="v6300" d="M 1535.02 695.46 L 1535.02 600.5" stroke-width="1" zvalue="158"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="52@1" LinkObjectIDznd="42@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1535.02 695.46 L 1535.02 600.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="56">
   <path class="v6300" d="M 1537.12 826.03 L 1534.99 719.29" stroke-width="1" zvalue="159"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="52@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1537.12 826.03 L 1534.99 719.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv110" d="M 885.25 204.64 L 885.25 309.34" stroke-width="1" zvalue="169"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="112@0" LinkObjectIDznd="166@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 885.25 204.64 L 885.25 309.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="v6300" d="M 884.75 600.5 L 884.75 566.93" stroke-width="1" zvalue="183"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="42@2" LinkObjectIDznd="236@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 884.75 600.5 L 884.75 566.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="71">
   <path class="v6300" d="M 884.78 478.17 L 884.78 543.1" stroke-width="1" zvalue="184"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="237@1" LinkObjectIDznd="236@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 884.78 478.17 L 884.78 543.1" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="v6300" d="M 935.29 528.13 L 884.78 528.13" stroke-width="1" zvalue="185"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="231@0" LinkObjectIDznd="71" MaxPinNum="2"/>
   </metadata>
  <path d="M 935.29 528.13 L 884.78 528.13" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv110" d="M 884.72 335.62 L 982.1 335.62" stroke-width="1" zvalue="188"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@2" LinkObjectIDznd="63@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 884.72 335.62 L 982.1 335.62" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="v6300" d="M 884.69 400.52 L 884.66 456.93" stroke-width="1" zvalue="189"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="166@1" LinkObjectIDznd="237@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 884.69 400.52 L 884.66 456.93" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="v6300" d="M 799.87 505.19 L 799.87 489.75 L 884.78 489.75" stroke-width="1" zvalue="192"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="71" MaxPinNum="2"/>
   </metadata>
  <path d="M 799.87 505.19 L 799.87 489.75 L 884.78 489.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="159">
   <use class="v6300" height="30" transform="rotate(0,1536.96,850.639) scale(1.69643,1.70833) translate(-621.213,-342.079)" width="28" x="1513.210910997585" xlink:href="#EnergyConsumer:站用变DY接地_0" y="825.0138888888889" zvalue="148"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192450074312709" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1536.96,850.639) scale(1.69643,1.70833) translate(-621.213,-342.079)" width="28" x="1513.210910997585" y="825.0138888888889"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="166">
   <g id="1660">
    <use class="kv110" height="50" transform="rotate(0,884.687,354.839) scale(2.15,1.85355) translate(-455.955,-142.062)" width="30" x="852.4400000000001" xlink:href="#PowerTransformer2:Y-D带中性点_0" y="308.5" zvalue="167"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874459774979" ObjectName="110"/>
    </metadata>
   </g>
   <g id="1661">
    <use class="v6300" height="50" transform="rotate(0,884.687,354.839) scale(2.15,1.85355) translate(-455.955,-142.062)" width="30" x="852.4400000000001" xlink:href="#PowerTransformer2:Y-D带中性点_1" y="308.5" zvalue="167"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874459840515" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399461765123" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399461765123"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,884.687,354.839) scale(2.15,1.85355) translate(-455.955,-142.062)" width="30" x="852.4400000000001" y="308.5"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="2">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="2" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,676.466,871.771) scale(1,1) translate(0,0)" writing-mode="lr" x="675.89" xml:space="preserve" y="878.05" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126974914568" ObjectName="P"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="3" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1118.9,879.896) scale(1,1) translate(0,0)" writing-mode="lr" x="1118.33" xml:space="preserve" y="886.17" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126976094212" ObjectName="P"/>
   </metadata>
  </g>
  <g id="5">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="5" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,676.466,908.771) scale(1,1) translate(0,0)" writing-mode="lr" x="675.89" xml:space="preserve" y="915.05" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126974980102" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="6">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="6" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1118.9,916.896) scale(1,1) translate(0,0)" writing-mode="lr" x="1118.33" xml:space="preserve" y="923.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126976159748" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="30">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="30" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,676.466,945.771) scale(1,1) translate(0,0)" writing-mode="lr" x="675.89" xml:space="preserve" y="952.05" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126975045638" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="48">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="48" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1118.9,953.896) scale(1,1) translate(0,0)" writing-mode="lr" x="1118.33" xml:space="preserve" y="960.17" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126976225284" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="119">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="119" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,153.611,358.167) scale(1,1) translate(0,0)" writing-mode="lr" x="153.77" xml:space="preserve" y="363.08" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="167">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="167" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,153.611,286.167) scale(1,1) translate(0,0)" writing-mode="lr" x="153.77" xml:space="preserve" y="291.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127012663300" ObjectName="F"/>
   </metadata>
  </g>
  <g id="118">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="118" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,331.222,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="331.38" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127012728836" ObjectName="F"/>
   </metadata>
  </g>
  <g id="114">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="114" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,153.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="153.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127012532228" ObjectName="F"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,331.222,312.167) scale(1,1) translate(0,0)" writing-mode="lr" x="331.38" xml:space="preserve" y="317.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127012597764" ObjectName="F"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="93" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,153.611,403.389) scale(1,1) translate(0,0)" writing-mode="lr" x="153.77" xml:space="preserve" y="408.3" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="73">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="73" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,153.611,335.167) scale(1,1) translate(0,0)" writing-mode="lr" x="153.77" xml:space="preserve" y="340.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127012532228" ObjectName="F"/>
   </metadata>
  </g>
  <g id="51">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,333.611,334.167) scale(1,1) translate(0,0)" writing-mode="lr" x="333.77" xml:space="preserve" y="339.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127012532228" ObjectName="F"/>
   </metadata>
  </g>
  <g id="130">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="130" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,153.611,425.389) scale(1,1) translate(0,0)" writing-mode="lr" x="153.77" xml:space="preserve" y="430.3" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="坝下水位"/>
   </metadata>
  </g>
  <g id="135">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="135" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,329.611,403.389) scale(1,1) translate(0,0)" writing-mode="lr" x="329.77" xml:space="preserve" y="408.3" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="雨量采集"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="146">
   <use height="30" transform="rotate(0,317.812,125.464) scale(1.22222,1.03092) translate(-47.7841,-3.29938)" width="90" x="262.81" xlink:href="#State:全站检修_0" y="110" zvalue="232"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549594095618" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,317.812,125.464) scale(1.22222,1.03092) translate(-47.7841,-3.29938)" width="90" x="262.81" y="110"/></g>
 </g>
</svg>