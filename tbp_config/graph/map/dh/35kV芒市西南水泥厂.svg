<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549583282178" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Compensator:并联电容器12121_0" viewBox="0,0,60,30">
   <use terminal-index="0" type="0" x="3.373372509142698" xlink:href="#terminal" y="12.54754746794735"/>
   <rect fill-opacity="0" height="2.8" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,35.96,12.55) scale(1,1) translate(0,0)" width="8.41" x="31.75" y="11.15"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="45.41541591575949" x2="50.34627285851084" y1="12.54754746794735" y2="12.54754746794735"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="47.20609554233762" x2="50.34627285851084" y1="22.75961171517187" y2="22.75961171517187"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="32.10210217033084" x2="29.01382861144973" y1="22.70770795787974" y2="22.70770795787974"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.92592676741369" x2="55.92592676741369" y1="1.569902800664089" y2="23.73646842528693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="55.92592676741369" x2="53.12312387363924" y1="23.73646842528693" y2="23.73646842528693"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="50.32032097986479" x2="55.92592676741369" y1="12.54754746794735" y2="12.54754746794735"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="53.12312387363924" x2="55.92592676741369" y1="1.569902800664083" y2="1.569902800664083"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="50.32032097986479" x2="50.32032097986479" y1="12.54754746794735" y2="22.72068389720278"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="31.75175180860903" x2="48.21821880953394" y1="29.01401446887226" y2="29.01401446887226"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.94894891483458" x2="28.94894891483458" y1="22.66878013991066" y2="12.54754746794735"/>
   <path d="M 37.1455 22.7077 A 4.15364 2.547 -90 0 1 32.0515 22.7077" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 42.2395 22.7077 A 4.15364 2.547 -90 0 1 37.1455 22.7077" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <path d="M 47.1934 22.7077 A 4.15364 2.547 -90 0 1 42.0994 22.7077" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="44.01401446887226" x2="44.01401446887226" y1="10.44544529761651" y2="14.64964963827819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="45.41541591575949" x2="45.41541591575949" y1="10.44544529761651" y2="14.64964963827819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.789455239606355" x2="8.803803115830696" y1="12.51251243177518" y2="12.51251243177518"/>
   <path d="M 15.7174 19.1458 A 6.89022 6.65666 -180 1 0 8.82716 12.4892" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.8108 19.0524 L 15.9276 12.5125 L 44.1308 12.5125" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(230,230,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:小车母联_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="17.58333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车母联_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="0.8333333333333304" y2="5.749999999999999"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车母联_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="18.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3" x2="7" y1="14" y2="6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.666666666666666" x2="7.25" y1="5.833333333333333" y2="14.16666666666667"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.5" y2="18.93130873029626"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.166666666666669" y2="5.750000000000002"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 15.3223 L 4.98274 17.6463 L 1.33333 15.3223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.15358 L 5.06482 1.9311 L 1.45119 4.15358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:母线电压互感器11_0" viewBox="0,0,45,48">
   <use terminal-index="0" type="0" x="25.25" xlink:href="#terminal" y="47.78457520218115"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,13.5,30.95) scale(1,-1) translate(0,-1334.05)" width="6" x="10.5" y="23.95"/>
   <path d="M 8.64167 11.4096 L 4.64167 11.4096 L 6.50709 7.52624 L 7.67376 9.52624 L 8.64167 11.4096" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.5" x2="13.5" y1="19.78457520218114" y2="40.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.25" x2="25.25" y1="40.5" y2="47.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="37.25" x2="13.58333333333333" y1="40.5" y2="40.5"/>
   <ellipse cx="13.56" cy="5.42" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799663" x2="13.56620079799663" y1="3.155388266900378" y2="5.82957386674455"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799661" x2="10.88240159599323" y1="5.829573866744575" y2="7.166666666666657"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.56620079799662" x2="16.25" y1="5.829573866744575" y2="7.166666666666657"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,36.96,25.94) scale(1,-1) translate(0,-1133.72)" width="7.58" x="33.17" y="17.87"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="34.01790853551448" y2="24.11790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="40.43457520218114" y2="34"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="35.7861111111111" x2="39.3" y1="11.60124186884782" y2="11.60124186884782"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="34.82777777777777" x2="39.93888888888888" y1="12.98282081621623" y2="12.98282081621623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="33.55" x2="41.21666666666666" y1="14.36439976358464" y2="14.36439976358464"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="34.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="37.21666666666667" y1="18.01790853551448" y2="14.41790853551448"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="37.21666666666667" x2="40.21666666666667" y1="24.01790853551448" y2="27.01790853551448"/>
   <ellipse cx="13.59" cy="15.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="20.51" cy="10.09" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.67" cy="10.01" fill-opacity="0" rx="4.76" ry="4.76" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490864" x2="10.91228003290526" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="13.59607923490865" y1="12.74178419246191" y2="15.41596979230608"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51274590157532" x2="23.19654510357869" y1="10.49930312563944" y2="11.83639592556152"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51274590157532" x2="20.51274590157532" y1="7.825117525795246" y2="10.49930312563942"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.59607923490865" x2="16.27987843691203" y1="15.41596979230611" y2="16.75306259222819"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.5127459015753" x2="17.82894669957193" y1="10.49930312563944" y2="11.83639592556152"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="Accessory:腊撒线路PT_0" viewBox="0,0,12,32">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="0.6666666666666661"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="3" y1="27" y2="25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="27" y2="30"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="6" y1="13.66666666666667" y2="1"/>
   <path d="M 6 15 L 3 20 L 9 20 L 6 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6" x2="9" y1="27" y2="25"/>
   <rect fill-opacity="0" height="6" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,6,8) scale(1,1) translate(0,0)" width="4" x="4" y="5"/>
   <ellipse cx="5.9" cy="18.6" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="5.9" cy="26.28" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:带电显示器_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.41666666666667" x2="8.750000000000002" y1="11.08333333333333" y2="14.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.08333333333333" x2="10.08333333333333" y1="1.5" y2="6.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.08333333333333" x2="10.08333333333333" y1="8.5" y2="10.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333334" x2="12.58333333333333" y1="6.5" y2="6.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.583333333333334" x2="12.58333333333333" y1="8.5" y2="8.5"/>
   <ellipse cx="10.08" cy="12.75" fill-opacity="0" rx="2.24" ry="2.24" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.083333333333334" x2="11.08333333333333" y1="19.5" y2="19.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.666666666666668" x2="11.66666666666667" y1="11.08333333333333" y2="14.08333333333333"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.583333333333334" x2="11.58333333333333" y1="18.5" y2="18.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.08333333333333" x2="10.08333333333333" y1="17.5" y2="15.08333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.083333333333334" x2="12.08333333333333" y1="17.5" y2="17.5"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV芒市西南水泥厂" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="49" y="276" zvalue="492"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="41" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,85.4375,288) scale(1,1) translate(0,0)" width="72.88" x="49" y="276" zvalue="492"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,85.4375,288) scale(1,1) translate(0,0)" writing-mode="lr" x="85.44" xml:space="preserve" y="292.5" zvalue="492">信号一览</text>
  <image height="60" id="2" preserveAspectRatio="xMidYMid slice" width="296.73" x="59" xlink:href="logo.png" y="32"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="62" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,207.365,62) scale(1,1) translate(-2.62013e-14,0)" writing-mode="lr" x="207.37" xml:space="preserve" y="65.5" zvalue="494"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,213.875,60.6903) scale(1,1) translate(0,0)" writing-mode="lr" x="213.88" xml:space="preserve" y="69.69" zvalue="495">35kV芒市西南水泥厂</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1038.03,280.295) scale(1,1) translate(0,3.59439e-13)" writing-mode="lr" x="1038.03" xml:space="preserve" y="284.8" zvalue="3">35kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="69" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1688.98,709.517) scale(1,1) translate(0,0)" writing-mode="lr" x="1688.98" xml:space="preserve" y="714.02" zvalue="5">6.3kVⅡ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,723.359,659.489) scale(1,1) translate(-3.0325e-13,0)" writing-mode="lr" x="723.36" xml:space="preserve" y="663.99" zvalue="8">6.3kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="60" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1340.45,360.489) scale(1,1) translate(0,0)" writing-mode="lr" x="1340.45" xml:space="preserve" y="364.99" zvalue="30">3902</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1150,383.045) scale(1,1) translate(0,0)" writing-mode="lr" x="1150" xml:space="preserve" y="387.55" zvalue="41">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="54" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1153.12,611.378) scale(1,1) translate(0,0)" writing-mode="lr" x="1153.12" xml:space="preserve" y="615.88" zvalue="47">601</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,872.952,731.045) scale(1,1) translate(0,0)" writing-mode="lr" x="872.95" xml:space="preserve" y="735.55" zvalue="50">672</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1320.89,625.045) scale(1,1) translate(0,0)" writing-mode="lr" x="1320.89" xml:space="preserve" y="629.55" zvalue="69">6122</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1216.04,515.347) scale(1,1) translate(0,0)" writing-mode="lr" x="1216.035768918581" xml:space="preserve" y="519.8467117441388" zvalue="107">#1主变  12500kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1034.92,752.156) scale(1,1) translate(0,0)" writing-mode="lr" x="1034.92" xml:space="preserve" y="756.66" zvalue="235">671</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,785.831,806.267) scale(1,1) translate(0,0)" writing-mode="lr" x="785.83" xml:space="preserve" y="810.77" zvalue="239">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,849.811,907.323) scale(1,1) translate(0,0)" writing-mode="lr" x="849.8099999999999" xml:space="preserve" y="911.8200000000001" zvalue="255">#1无功补偿装置</text>
  <line fill="none" id="318" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.75000000000023" x2="381.7499999999998" y1="148.6204926140824" y2="148.6204926140824" zvalue="306"/>
  <line fill="none" id="317" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="382.75" x2="382.75" y1="6.75" y2="1036.75" zvalue="307"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="196.75" y1="162.0000000000001" y2="162.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="196.75" y1="188.0000000000001" y2="188.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="15.75" y1="162.0000000000001" y2="188.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="196.75" y1="162.0000000000001" y2="188.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="377.75" y1="162.0000000000001" y2="162.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="377.75" y1="188.0000000000001" y2="188.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="196.75" y1="162.0000000000001" y2="188.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="377.75" x2="377.75" y1="162.0000000000001" y2="188.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="196.75" y1="188.0000000000001" y2="188.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="196.75" y1="212.2500000000001" y2="212.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="15.75" y1="188.0000000000001" y2="212.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="196.75" y1="188.0000000000001" y2="212.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="377.75" y1="188.0000000000001" y2="188.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="377.75" y1="212.2500000000001" y2="212.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="196.75" y1="188.0000000000001" y2="212.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="377.75" x2="377.75" y1="188.0000000000001" y2="212.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="196.75" y1="212.2500000000001" y2="212.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="196.75" y1="235.0000000000001" y2="235.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="15.75" x2="15.75" y1="212.2500000000001" y2="235.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="196.75" y1="212.2500000000001" y2="235.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="377.75" y1="212.2500000000001" y2="212.2500000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="377.75" y1="235.0000000000001" y2="235.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="196.75" x2="196.75" y1="212.2500000000001" y2="235.0000000000001"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="377.75" x2="377.75" y1="212.2500000000001" y2="235.0000000000001"/>
  <line fill="none" id="314" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.75000000000023" x2="381.7499999999998" y1="618.6204926140824" y2="618.6204926140824" zvalue="309"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.75" x2="104.75" y1="933.75" y2="933.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.75" x2="104.75" y1="972.9132999999999" y2="972.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.75" x2="14.75" y1="933.75" y2="972.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="104.75" y1="933.75" y2="972.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="374.75" y1="933.75" y2="933.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="374.75" y1="972.9132999999999" y2="972.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="104.75" y1="933.75" y2="972.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="374.75" x2="374.75" y1="933.75" y2="972.9132999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.75" x2="104.75" y1="972.91327" y2="972.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.75" x2="104.75" y1="1000.83167" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.75" x2="14.75" y1="972.91327" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="104.75" y1="972.91327" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="194.75" y1="972.91327" y2="972.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="194.75" y1="1000.83167" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="104.75" y1="972.91327" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.75" x2="194.75" y1="972.91327" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.7500000000001" x2="284.7500000000001" y1="972.91327" y2="972.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.7500000000001" x2="284.7500000000001" y1="1000.83167" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.7500000000001" x2="194.7500000000001" y1="972.91327" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.7500000000001" x2="284.7500000000001" y1="972.91327" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.75" x2="374.75" y1="972.91327" y2="972.91327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.75" x2="374.75" y1="1000.83167" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.75" x2="284.75" y1="972.91327" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="374.75" x2="374.75" y1="972.91327" y2="1000.83167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.75" x2="104.75" y1="1000.8316" y2="1000.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.75" x2="104.75" y1="1028.75" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="14.75" x2="14.75" y1="1000.8316" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="104.75" y1="1000.8316" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="194.75" y1="1000.8316" y2="1000.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="194.75" y1="1028.75" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="104.75" x2="104.75" y1="1000.8316" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.75" x2="194.75" y1="1000.8316" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.7500000000001" x2="284.7500000000001" y1="1000.8316" y2="1000.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.7500000000001" x2="284.7500000000001" y1="1028.75" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="194.7500000000001" x2="194.7500000000001" y1="1000.8316" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.7500000000001" x2="284.7500000000001" y1="1000.8316" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.75" x2="374.75" y1="1000.8316" y2="1000.8316"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.75" x2="374.75" y1="1028.75" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="284.75" x2="284.75" y1="1000.8316" y2="1028.75"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="374.75" x2="374.75" y1="1000.8316" y2="1028.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="311" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,60.75,954.75) scale(1,1) translate(0,0)" writing-mode="lr" x="60.75" xml:space="preserve" y="960.75" zvalue="312">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="310" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.75,988.75) scale(1,1) translate(0,0)" writing-mode="lr" x="57.75" xml:space="preserve" y="994.75" zvalue="313">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="309" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,239.75,988.75) scale(1,1) translate(0,0)" writing-mode="lr" x="239.75" xml:space="preserve" y="994.75" zvalue="314">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="308" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,56.75,1016.75) scale(1,1) translate(0,0)" writing-mode="lr" x="56.75" xml:space="preserve" y="1022.75" zvalue="315">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="307" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,237.75,1015.75) scale(1,1) translate(0,0)" writing-mode="lr" x="237.75" xml:space="preserve" y="1021.75" zvalue="316">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="304" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,80.25,648.25) scale(1,1) translate(0,2.09582e-13)" writing-mode="lr" x="80.25" xml:space="preserve" y="652.7500000000001" zvalue="319">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="303" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,194.149,282.591) scale(1,1) translate(0,0)" writing-mode="lr" x="194.15" xml:space="preserve" y="287.09" zvalue="320">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="302" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,299.149,282.591) scale(1,1) translate(0,0)" writing-mode="lr" x="299.15" xml:space="preserve" y="287.09" zvalue="321">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="294" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,239.804,955.75) scale(1,1) translate(0,0)" writing-mode="lr" x="239.8" xml:space="preserve" y="961.75" zvalue="329">XiNanShuiNiChang01-2019</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="293" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,152.804,987.75) scale(1,1) translate(0,0)" writing-mode="lr" x="152.8" xml:space="preserve" y="993.75" zvalue="330">杨立超</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="292" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53.75,174.75) scale(1,1) translate(0,0)" writing-mode="lr" x="53.75" xml:space="preserve" y="180.25" zvalue="331">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="291" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.75,174.75) scale(1,1) translate(0,0)" writing-mode="lr" x="233.75" xml:space="preserve" y="180.25" zvalue="332">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="290" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57.4375,199) scale(1,1) translate(0,0)" writing-mode="lr" x="57.44" xml:space="preserve" y="203.5" zvalue="333">35kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="285" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,61.4375,223) scale(1,1) translate(0,0)" writing-mode="lr" x="61.44" xml:space="preserve" y="227.5" zvalue="338">6.3kVⅠ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="284" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,243.5,223.75) scale(1,1) translate(0,0)" writing-mode="lr" x="243.5" xml:space="preserve" y="228.25" zvalue="339">6.3kVⅡ母频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="329" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,542.5,272.489) scale(1,1) translate(0,3.4904e-13)" writing-mode="lr" x="542.5" xml:space="preserve" y="276.99" zvalue="347">35kVⅠ段母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="331" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,732,114.5) scale(1,1) translate(0,0)" writing-mode="lr" x="732" xml:space="preserve" y="119" zvalue="348">35kV芒市西南水泥厂线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="338" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,674.143,356.795) scale(1,1) translate(0,0)" writing-mode="lr" x="674.14" xml:space="preserve" y="361.3" zvalue="354">3811</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="345" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,628.232,482.5) scale(1,1) translate(0,0)" writing-mode="lr" x="628.23" xml:space="preserve" y="487" zvalue="360">#1厂用变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="350" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1010.34,363.045) scale(1,1) translate(0,0)" writing-mode="lr" x="1010.34" xml:space="preserve" y="367.55" zvalue="367">312</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="57" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1073.56,492.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1073.56" xml:space="preserve" y="496.75" zvalue="378">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1538,362.045) scale(1,1) translate(0,0)" writing-mode="lr" x="1538" xml:space="preserve" y="366.55" zvalue="390">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1541.12,590.378) scale(1,1) translate(0,0)" writing-mode="lr" x="1541.12" xml:space="preserve" y="594.88" zvalue="392">602</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1612.87,497.035) scale(1,1) translate(0,0)" writing-mode="lr" x="1612.865314373127" xml:space="preserve" y="501.5352439839575" zvalue="395">#2主变  4000kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1461.56,471.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1461.56" xml:space="preserve" y="475.75" zvalue="400">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1002.66,904.046) scale(1,1) translate(0,0)" writing-mode="lr" x="1002.659353426647" xml:space="preserve" y="908.5459654882704" zvalue="416">5000kV     #1F</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="40" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,242.438,200) scale(1,1) translate(0,0)" writing-mode="lr" x="242.44" xml:space="preserve" y="204.5" zvalue="427">35kVⅡ母频率</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45" x2="90.7745000000001" y1="374" y2="374"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45" x2="90.7745000000001" y1="412.2823" y2="412.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45" x2="45" y1="374" y2="412.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="90.7745000000001" y1="374" y2="412.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="149.5809" y1="374" y2="374"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="149.5809" y1="412.2823" y2="412.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="90.7745000000001" y1="374" y2="412.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5809" x2="149.5809" y1="374" y2="412.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5808000000001" x2="208.3872000000001" y1="374" y2="374"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5808000000001" x2="208.3872000000001" y1="412.2823" y2="412.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5808000000001" x2="149.5808000000001" y1="374" y2="412.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="208.3872000000001" x2="208.3872000000001" y1="374" y2="412.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45" x2="90.7745000000001" y1="412.2823" y2="412.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45" x2="90.7745000000001" y1="436.9617" y2="436.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45" x2="45" y1="412.2823" y2="436.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="90.7745000000001" y1="412.2823" y2="436.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="149.5809" y1="412.2823" y2="412.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="149.5809" y1="436.9617" y2="436.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="90.7745000000001" y1="412.2823" y2="436.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5809" x2="149.5809" y1="412.2823" y2="436.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5808000000001" x2="208.3872000000001" y1="412.2823" y2="412.2823"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5808000000001" x2="208.3872000000001" y1="436.9617" y2="436.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5808000000001" x2="149.5808000000001" y1="412.2823" y2="436.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="208.3872000000001" x2="208.3872000000001" y1="412.2823" y2="436.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45" x2="90.7745000000001" y1="436.9617" y2="436.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45" x2="90.7745000000001" y1="461.6411" y2="461.6411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45" x2="45" y1="436.9617" y2="461.6411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="90.7745000000001" y1="436.9617" y2="461.6411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="149.5809" y1="436.9617" y2="436.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="149.5809" y1="461.6411" y2="461.6411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="90.7745000000001" y1="436.9617" y2="461.6411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5809" x2="149.5809" y1="436.9617" y2="461.6411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5808000000001" x2="208.3872000000001" y1="436.9617" y2="436.9617"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5808000000001" x2="208.3872000000001" y1="461.6411" y2="461.6411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5808000000001" x2="149.5808000000001" y1="436.9617" y2="461.6411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="208.3872000000001" x2="208.3872000000001" y1="436.9617" y2="461.6411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45" x2="90.7745000000001" y1="461.6411" y2="461.6411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45" x2="90.7745000000001" y1="486.3205" y2="486.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45" x2="45" y1="461.6411" y2="486.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="90.7745000000001" y1="461.6411" y2="486.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="149.5809" y1="461.6411" y2="461.6411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="149.5809" y1="486.3205" y2="486.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="90.7745000000001" y1="461.6411" y2="486.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5809" x2="149.5809" y1="461.6411" y2="486.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5808000000001" x2="208.3872000000001" y1="461.6411" y2="461.6411"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5808000000001" x2="208.3872000000001" y1="486.3205" y2="486.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5808000000001" x2="149.5808000000001" y1="461.6411" y2="486.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="208.3872000000001" x2="208.3872000000001" y1="461.6411" y2="486.3205"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45" x2="90.7745000000001" y1="486.3206" y2="486.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45" x2="90.7745000000001" y1="511" y2="511"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45" x2="45" y1="486.3206" y2="511"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="90.7745000000001" y1="486.3206" y2="511"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="149.5809" y1="486.3206" y2="486.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="149.5809" y1="511" y2="511"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="90.7745000000001" y1="486.3206" y2="511"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5809" x2="149.5809" y1="486.3206" y2="511"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5808000000001" x2="208.3872000000001" y1="486.3206" y2="486.3206"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5808000000001" x2="208.3872000000001" y1="511" y2="511"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5808000000001" x2="149.5808000000001" y1="486.3206" y2="511"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="208.3872000000001" x2="208.3872000000001" y1="486.3206" y2="511"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45" x2="90.7745000000001" y1="511" y2="511"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45" x2="90.7745000000001" y1="535.6794" y2="535.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="45" x2="45" y1="511" y2="535.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="90.7745000000001" y1="511" y2="535.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="149.5809" y1="511" y2="511"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="149.5809" y1="535.6794" y2="535.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="90.7745000000001" x2="90.7745000000001" y1="511" y2="535.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5809" x2="149.5809" y1="511" y2="535.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5808000000001" x2="208.3872000000001" y1="511" y2="511"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5808000000001" x2="208.3872000000001" y1="535.6794" y2="535.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="149.5808000000001" x2="149.5808000000001" y1="511" y2="535.6794"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="208.3872000000001" x2="208.3872000000001" y1="511" y2="535.6794"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" x="122" xml:space="preserve" y="389" zvalue="500">35kV     Ⅰ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="77" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="122" xml:space="preserve" y="405" zvalue="500">母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" x="180.671875" xml:space="preserve" y="389" zvalue="501">35kV     Ⅱ</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="76" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="180.671875" xml:space="preserve" y="405" zvalue="501">母</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70,424.5) scale(1,1) translate(0,0)" writing-mode="lr" x="70" xml:space="preserve" y="429" zvalue="504">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70,450) scale(1,1) translate(0,0)" writing-mode="lr" x="70" xml:space="preserve" y="454.5" zvalue="505">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70,475.5) scale(1,1) translate(0,0)" writing-mode="lr" x="70" xml:space="preserve" y="480" zvalue="506">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="65" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70,501) scale(1,1) translate(0,0)" writing-mode="lr" x="70" xml:space="preserve" y="505.5" zvalue="507">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,70,526.5) scale(1,1) translate(0,0)" writing-mode="lr" x="70" xml:space="preserve" y="531" zvalue="508">3Uo</text>
 </g>
 <g id="BusbarSectionClass">
  <g id="278">
   <path class="kv35" d="M 961.67 309.82 L 1665 309.82" stroke-width="4" zvalue="2"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674237612036" ObjectName="35kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674237612036"/></metadata>
  <path d="M 961.67 309.82 L 1665 309.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="277">
   <path class="v6300" d="M 1302.89 684.16 L 1718.67 684.16" stroke-width="4" zvalue="4"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674237546501" ObjectName="6.3kVⅡ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674237546501"/></metadata>
  <path d="M 1302.89 684.16 L 1718.67 684.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="276">
   <path class="v6300" d="M 744 685.82 L 1251.89 685.82" stroke-width="4" zvalue="6"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674237480965" ObjectName="6.3kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674237480965"/></metadata>
  <path d="M 744 685.82 L 1251.89 685.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="328">
   <path class="kv35" d="M 522 310.49 L 882 310.49" stroke-width="4" zvalue="346"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674237677572" ObjectName="35kVⅠ段母线"/>
   <cge:TPSR_Ref TObjectID="9288674237677572"/></metadata>
  <path d="M 522 310.49 L 882 310.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="261">
   <use class="kv35" height="30" transform="rotate(0,1315.12,361.489) scale(-1.11111,-0.814815) translate(-2497.89,-807.913)" width="15" x="1306.781748453776" xlink:href="#Disconnector:刀闸_0" y="349.2672522314244" zvalue="29"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449673822214" ObjectName="35kVⅡ段母线电压互感器3902隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449673822214"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1315.12,361.489) scale(-1.11111,-0.814815) translate(-2497.89,-807.913)" width="15" x="1306.781748453776" y="349.2672522314244"/></g>
  <g id="337">
   <use class="kv35" height="30" transform="rotate(0,627.226,349.045) scale(1.11111,0.814815) translate(-61.8893,76.5506)" width="15" x="618.8928673153832" xlink:href="#Disconnector:刀闸_0" y="336.8227941176483" zvalue="353"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449674018821" ObjectName="#1厂用变侧3811隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449674018821"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,627.226,349.045) scale(1.11111,0.814815) translate(-61.8893,76.5506)" width="15" x="618.8928673153832" y="336.8227941176483"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="259">
   <use class="kv35" height="48" transform="rotate(0,1311.12,429.35) scale(1.38692,-1.13771) translate(-357.066,-803.427)" width="45" x="1279.909403357504" xlink:href="#Accessory:母线电压互感器11_0" y="402.0450163398702" zvalue="33"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449673756678" ObjectName="35kVⅡ段母线电压互感器"/>
   </metadata>
  <rect fill="white" height="48" opacity="0" stroke="white" transform="rotate(0,1311.12,429.35) scale(1.38692,-1.13771) translate(-357.066,-803.427)" width="45" x="1279.909403357504" y="402.0450163398702"/></g>
  <g id="249">
   <use class="v6300" height="26" transform="rotate(0,1178.89,588.823) scale(1,1) translate(0,0)" width="12" x="1172.892857142857" xlink:href="#Accessory:避雷器1_0" y="575.8227941176483" zvalue="48"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449673691142" ObjectName="#1主变6.3kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1178.89,588.823) scale(1,1) translate(0,0)" width="12" x="1172.892857142857" y="575.8227941176483"/></g>
  <g id="179">
   <use class="v6300" height="26" transform="rotate(0,1057.96,833.078) scale(1.17313,1.14784) translate(-155.096,-105.378)" width="12" x="1050.92242672814" xlink:href="#Accessory:避雷器1_0" y="818.1561325372445" zvalue="213"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449673625605" ObjectName="#1发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1057.96,833.078) scale(1.17313,1.14784) translate(-155.096,-105.378)" width="12" x="1050.92242672814" y="818.1561325372445"/></g>
  <g id="207">
   <use class="v6300" height="26" transform="rotate(0,881.568,850.078) scale(1.2386,1.37861) translate(-168.393,-228.537)" width="12" x="874.1367465688716" xlink:href="#Accessory:避雷器1_0" y="832.1561342326654" zvalue="240"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449673428998" ObjectName="6.3kV#1无功补偿装置避雷器1"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,881.568,850.078) scale(1.2386,1.37861) translate(-168.393,-228.537)" width="12" x="874.1367465688716" y="832.1561342326654"/></g>
  <g id="333">
   <use class="kv35" height="20" transform="rotate(0,774.25,242.25) scale(2.075,2.075) translate(-390.367,-114.753)" width="20" x="753.5" xlink:href="#Accessory:线路PT3_0" y="221.5" zvalue="349"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449673953285" ObjectName="线路PT1"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,774.25,242.25) scale(2.075,2.075) translate(-390.367,-114.753)" width="20" x="753.5" y="221.5"/></g>
  <g id="344">
   <use class="kv35" height="32" transform="rotate(0,627.294,424.5) scale(1.84375,1.84375) translate(-282.004,-180.763)" width="12" x="616.2316611199453" xlink:href="#Accessory:腊撒线路PT_0" y="395" zvalue="359"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449674084358" ObjectName="#1厂用变"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,627.294,424.5) scale(1.84375,1.84375) translate(-282.004,-180.763)" width="12" x="616.2316611199453" y="395"/></g>
  <g id="347">
   <use class="kv35" height="32" transform="rotate(0,867.294,392.5) scale(1.84375,1.84375) translate(-391.835,-166.119)" width="12" x="856.2316611199453" xlink:href="#Accessory:腊撒线路PT_0" y="363" zvalue="362"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449674149894" ObjectName="PT"/>
   </metadata>
  <rect fill="white" height="32" opacity="0" stroke="white" transform="rotate(0,867.294,392.5) scale(1.84375,1.84375) translate(-391.835,-166.119)" width="12" x="856.2316611199453" y="363"/></g>
  <g id="348">
   <use class="kv35" height="20" transform="rotate(0,935,398.5) scale(2.15,-2.15) translate(-488.616,-572.349)" width="20" x="913.5" xlink:href="#Accessory:线路PT3_0" y="377" zvalue="364"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449674215430" ObjectName="线路PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,935,398.5) scale(2.15,-2.15) translate(-488.616,-572.349)" width="20" x="913.5" y="377"/></g>
  <g id="55">
   <use class="kv35" height="20" transform="rotate(0,1175.25,451.25) scale(2.075,2.075) translate(-598.114,-223.03)" width="20" x="1154.5" xlink:href="#Accessory:线路PT3_0" y="430.5" zvalue="379"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449674412038" ObjectName="#1主变35kV侧线路PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1175.25,451.25) scale(2.075,2.075) translate(-598.114,-223.03)" width="20" x="1154.5" y="430.5"/></g>
  <g id="92">
   <use class="v6300" height="26" transform="rotate(0,1566.89,567.823) scale(1,1) translate(0,0)" width="12" x="1560.892857142857" xlink:href="#Accessory:避雷器1_0" y="554.8227941176483" zvalue="393"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449674674182" ObjectName="#2主变6.3kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1566.89,567.823) scale(1,1) translate(0,0)" width="12" x="1560.892857142857" y="554.8227941176483"/></g>
  <g id="86">
   <use class="kv35" height="20" transform="rotate(0,1563.25,430.25) scale(2.075,2.075) translate(-799.127,-212.151)" width="20" x="1542.5" xlink:href="#Accessory:线路PT3_0" y="409.5" zvalue="401"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449674477573" ObjectName="#2主变35kV侧线路PT"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1563.25,430.25) scale(2.075,2.075) translate(-799.127,-212.151)" width="20" x="1542.5" y="409.5"/></g>
  <g id="44">
   <use class="v6300" height="20" transform="rotate(270,878.462,784.154) scale(0.769231,1.07692) translate(261.231,-55.2418)" width="20" x="870.7692307692307" xlink:href="#Accessory:带电显示器_0" y="773.3846153846152" zvalue="432"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449674805254" ObjectName="带电显示器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,878.462,784.154) scale(0.769231,1.07692) translate(261.231,-55.2418)" width="20" x="870.7692307692307" y="773.3846153846152"/></g>
 </g>
 <g id="BreakerClass">
  <g id="254">
   <use class="kv35" height="20" transform="rotate(0,1129.23,384.045) scale(1.22222,1.11111) translate(-204.203,-37.2934)" width="10" x="1123.115089550851" xlink:href="#Breaker:开关_0" y="372.9339052287594" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924488527877" ObjectName="#1主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924488527877"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1129.23,384.045) scale(1.22222,1.11111) translate(-204.203,-37.2934)" width="10" x="1123.115089550851" y="372.9339052287594"/></g>
  <g id="250">
   <use class="v6300" height="20" transform="rotate(0,1129.34,612.378) scale(2,2) translate(-559.669,-296.189)" width="10" x="1119.337301587301" xlink:href="#Breaker:小车断路器_0" y="592.3783496732038" zvalue="46"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924488462341" ObjectName="#1主变10kV侧601断路器"/>
   <cge:TPSR_Ref TObjectID="6473924488462341"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1129.34,612.378) scale(2,2) translate(-559.669,-296.189)" width="10" x="1119.337301587301" y="592.3783496732038"/></g>
  <g id="325">
   <use class="v6300" height="20" transform="rotate(0,847.952,732.045) scale(2,2) translate(-418.976,-356.023)" width="10" x="837.9519963134235" xlink:href="#Breaker:小车断路器_0" y="712.0450197307126" zvalue="49"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924488396805" ObjectName="#1无功补偿装置672断路器"/>
   <cge:TPSR_Ref TObjectID="6473924488396805"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,847.952,732.045) scale(2,2) translate(-418.976,-356.023)" width="10" x="837.9519963134235" y="712.0450197307126"/></g>
  <g id="572">
   <use class="v6300" height="20" transform="rotate(0,1360.89,623.823) scale(2,2) translate(-675.446,-301.911)" width="10" x="1350.892857142857" xlink:href="#Breaker:小车母联_0" y="603.8227941176482" zvalue="68"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924488331269" ObjectName="6.3kV母联6122隔离开关"/>
   <cge:TPSR_Ref TObjectID="6473924488331269"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1360.89,623.823) scale(2,2) translate(-675.446,-301.911)" width="10" x="1350.892857142857" y="603.8227941176482"/></g>
  <g id="210">
   <use class="v6300" height="20" transform="rotate(0,1003.92,754.156) scale(2,2) translate(-496.961,-367.078)" width="10" x="993.9224267281402" xlink:href="#Breaker:小车断路器_0" y="734.1561342326656" zvalue="234"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924488265733" ObjectName="#1发电机671断路器"/>
   <cge:TPSR_Ref TObjectID="6473924488265733"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1003.92,754.156) scale(2,2) translate(-496.961,-367.078)" width="10" x="993.9224267281402" y="734.1561342326656"/></g>
  <g id="349">
   <use class="kv35" height="20" transform="rotate(0,992.226,364.045) scale(1.22222,1.11111) translate(-179.294,-35.2934)" width="10" x="986.1150895508507" xlink:href="#Breaker:开关_0" y="352.9339052287594" zvalue="366"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924488593413" ObjectName="35kV母联312断路器"/>
   <cge:TPSR_Ref TObjectID="6473924488593413"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,992.226,364.045) scale(1.22222,1.11111) translate(-179.294,-35.2934)" width="10" x="986.1150895508507" y="352.9339052287594"/></g>
  <g id="94">
   <use class="kv35" height="20" transform="rotate(0,1517.23,363.045) scale(1.22222,1.11111) translate(-274.748,-35.1934)" width="10" x="1511.115089550851" xlink:href="#Breaker:开关_0" y="351.9339052287594" zvalue="389"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924488724485" ObjectName="#2主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473924488724485"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1517.23,363.045) scale(1.22222,1.11111) translate(-274.748,-35.1934)" width="10" x="1511.115089550851" y="351.9339052287594"/></g>
  <g id="93">
   <use class="v6300" height="20" transform="rotate(0,1517.34,591.378) scale(2,2) translate(-753.669,-285.689)" width="10" x="1507.337301587301" xlink:href="#Breaker:小车断路器_0" y="571.3783496732038" zvalue="391"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924488658949" ObjectName="#2主变10kV侧602断路器"/>
   <cge:TPSR_Ref TObjectID="6473924488658949"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1517.34,591.378) scale(2,2) translate(-753.669,-285.689)" width="10" x="1507.337301587301" y="571.3783496732038"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="680">
   <g id="6800">
    <use class="kv35" height="60" transform="rotate(0,1128.56,506.323) scale(1.38333,1.38333) translate(-305.067,-128.806)" width="40" x="1100.89" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="464.82" zvalue="105"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874426875908" ObjectName="35"/>
    </metadata>
   </g>
   <g id="6801">
    <use class="v6300" height="60" transform="rotate(0,1128.56,506.323) scale(1.38333,1.38333) translate(-305.067,-128.806)" width="40" x="1100.89" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="464.82" zvalue="105"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874426941444" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399445643268" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399445643268"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1128.56,506.323) scale(1.38333,1.38333) translate(-305.067,-128.806)" width="40" x="1100.89" y="464.82"/></g>
  <g id="91">
   <g id="910">
    <use class="kv35" height="60" transform="rotate(0,1516.56,485.323) scale(1.38333,1.38333) translate(-412.585,-122.987)" width="40" x="1488.89" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="443.82" zvalue="394"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874427006980" ObjectName="35"/>
    </metadata>
   </g>
   <g id="911">
    <use class="v6300" height="60" transform="rotate(0,1516.56,485.323) scale(1.38333,1.38333) translate(-412.585,-122.987)" width="40" x="1488.89" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="443.82" zvalue="394"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874427072516" ObjectName="6.3"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399445708804" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399445708804"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1516.56,485.323) scale(1.38333,1.38333) translate(-412.585,-122.987)" width="40" x="1488.89" y="443.82"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="225">
   <path class="kv35" d="M 1129.31 394.66 L 1129.31 465.57" stroke-width="1" zvalue="108"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="254@1" LinkObjectIDznd="680@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1129.31 394.66 L 1129.31 465.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="v6300" d="M 1129.34 593.88 L 1129.34 547.24" stroke-width="1" zvalue="109"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@0" LinkObjectIDznd="680@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1129.34 593.88 L 1129.34 547.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="v6300" d="M 1178.93 576.46 L 1178.93 563.82 L 1129.34 563.82" stroke-width="1" zvalue="111"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="249@0" LinkObjectIDznd="224" MaxPinNum="2"/>
   </metadata>
  <path d="M 1178.93 576.46 L 1178.93 563.82 L 1129.34 563.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="332">
   <path class="kv35" d="M 732 175.76 L 732 310.49" stroke-width="1" zvalue="348"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="330@0" LinkObjectIDznd="328@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 732 175.76 L 732 310.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="334">
   <path class="kv35" d="M 774.25 224.09 L 774.25 206 L 732 206" stroke-width="1" zvalue="350"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="333@0" LinkObjectIDznd="332" MaxPinNum="2"/>
   </metadata>
  <path d="M 774.25 224.09 L 774.25 206 L 732 206" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="339">
   <path class="kv35" d="M 627.32 337.23 L 627.32 310.49" stroke-width="1" zvalue="354"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="337@0" LinkObjectIDznd="328@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 627.32 337.23 L 627.32 310.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="346">
   <path class="kv35" d="M 627.29 396.23 L 627.29 361.06" stroke-width="1" zvalue="360"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="344@0" LinkObjectIDznd="337@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 627.29 396.23 L 627.29 361.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="352">
   <path class="kv35" d="M 992.31 374.66 L 992.31 443 L 821 443 L 821 310.49" stroke-width="1" zvalue="368"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="349@1" LinkObjectIDznd="328@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 992.31 374.66 L 992.31 443 L 821 443 L 821 310.49" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="353">
   <path class="kv35" d="M 867.29 364.23 L 821 364.23" stroke-width="1" zvalue="369"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="347@0" LinkObjectIDznd="352" MaxPinNum="2"/>
   </metadata>
  <path d="M 867.29 364.23 L 821 364.23" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="357">
   <path class="kv35" d="M 1129.19 373.42 L 1129.19 309.82" stroke-width="1" zvalue="375"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="254@0" LinkObjectIDznd="278@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1129.19 373.42 L 1129.19 309.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv35" d="M 1175.25 436.73 L 1175.25 414 L 1129.31 414" stroke-width="1" zvalue="382"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="225" MaxPinNum="2"/>
   </metadata>
  <path d="M 1175.25 436.73 L 1175.25 414 L 1129.31 414" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="74">
   <path class="kv35" d="M 1129.31 414 L 1073 414 L 1073 434.64" stroke-width="1" zvalue="383"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="73" LinkObjectIDznd="37@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1129.31 414 L 1073 414 L 1073 434.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="90">
   <path class="kv35" d="M 1517.31 373.66 L 1517.31 444.57" stroke-width="1" zvalue="396"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@1" LinkObjectIDznd="91@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517.31 373.66 L 1517.31 444.57" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="v6300" d="M 1517.34 572.88 L 1517.34 526.24" stroke-width="1" zvalue="397"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@0" LinkObjectIDznd="91@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517.34 572.88 L 1517.34 526.24" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="v6300" d="M 1566.93 555.46 L 1566.93 542.82 L 1517.34 542.82" stroke-width="1" zvalue="398"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="92@0" LinkObjectIDznd="89" MaxPinNum="2"/>
   </metadata>
  <path d="M 1566.93 555.46 L 1566.93 542.82 L 1517.34 542.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="kv35" d="M 1563.25 415.73 L 1563.25 393 L 1517.31 393" stroke-width="1" zvalue="402"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="86@0" LinkObjectIDznd="90" MaxPinNum="2"/>
   </metadata>
  <path d="M 1563.25 415.73 L 1563.25 393 L 1517.31 393" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="kv35" d="M 1517.31 393 L 1461 393 L 1461 413.64" stroke-width="1" zvalue="403"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="85" LinkObjectIDznd="87@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517.31 393 L 1461 393 L 1461 413.64" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="95">
   <path class="kv35" d="M 1517.19 352.42 L 1517.19 309.82" stroke-width="1" zvalue="404"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="94@0" LinkObjectIDznd="278@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517.19 352.42 L 1517.19 309.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="96">
   <path class="v6300" d="M 1517.34 609.38 L 1517.34 684.16" stroke-width="1" zvalue="405"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="93@1" LinkObjectIDznd="277@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1517.34 609.38 L 1517.34 684.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="97">
   <path class="v6300" d="M 1129.34 630.38 L 1129.34 685.82" stroke-width="1" zvalue="406"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="250@1" LinkObjectIDznd="276@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1129.34 630.38 L 1129.34 685.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="98">
   <path class="v6300" d="M 847.95 713.55 L 847.95 685.82" stroke-width="1" zvalue="407"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="325@0" LinkObjectIDznd="276@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 847.95 713.55 L 847.95 685.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="v6300" d="M 847.95 750.05 L 847.95 818.76" stroke-width="1" zvalue="410"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="325@1" LinkObjectIDznd="214@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 847.95 750.05 L 847.95 818.76" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="103">
   <path class="v6300" d="M 881.61 833.03 L 881.61 805 L 847.95 805" stroke-width="1" zvalue="412"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="207@0" LinkObjectIDznd="101" MaxPinNum="2"/>
   </metadata>
  <path d="M 881.61 833.03 L 881.61 805 L 847.95 805" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="v6300" d="M 847.95 784.4 L 812.85 784.4 L 812.85 799.43" stroke-width="1" zvalue="413"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="101" LinkObjectIDznd="208@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 847.95 784.4 L 812.85 784.4 L 812.85 799.43" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="5">
   <path class="v6300" d="M 1003.92 735.66 L 1003.92 685.82" stroke-width="1" zvalue="417"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="210@0" LinkObjectIDznd="276@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 1003.92 735.66 L 1003.92 685.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="v6300" d="M 1005 815.47 L 1005 772.16" stroke-width="1" zvalue="418"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="850@0" LinkObjectIDznd="210@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1005 815.47 L 1005 772.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="31">
   <path class="v6300" d="M 1058 818.88 L 1058 801 L 1005 801" stroke-width="1" zvalue="419"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="179@0" LinkObjectIDznd="30" MaxPinNum="2"/>
   </metadata>
  <path d="M 1058 818.88 L 1058 801 L 1005 801" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="v6300" d="M 1360.89 641.32 L 1360.89 684.16" stroke-width="1" zvalue="421"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="572@1" LinkObjectIDznd="277@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1360.89 641.32 L 1360.89 684.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="34">
   <path class="v6300" d="M 1360.89 605.82 L 1360.89 579 L 1220 579 L 1220 685.82" stroke-width="1" zvalue="422"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="572@0" LinkObjectIDznd="276@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1360.89 605.82 L 1360.89 579 L 1220 579 L 1220 685.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="kv35" d="M 935 413.55 L 935 443" stroke-width="1" zvalue="424"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="348@0" LinkObjectIDznd="352" MaxPinNum="2"/>
   </metadata>
  <path d="M 935 413.55 L 935 443" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="38">
   <path class="kv35" d="M 992.19 353.42 L 992.19 309.82" stroke-width="1" zvalue="425"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="349@0" LinkObjectIDznd="278@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 992.19 353.42 L 992.19 309.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="42">
   <path class="kv35" d="M 1315.05 349.48 L 1315.05 309.82" stroke-width="1" zvalue="430"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="261@1" LinkObjectIDznd="278@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1315.05 349.48 L 1315.05 309.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv35" d="M 1314.93 402.29 L 1314.93 373.31" stroke-width="1" zvalue="431"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="259@0" LinkObjectIDznd="261@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1314.93 402.29 L 1314.93 373.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="v6300" d="M 868.77 784.15 L 847.95 784.15" stroke-width="1" zvalue="433"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="104" MaxPinNum="2"/>
   </metadata>
  <path d="M 868.77 784.15 L 847.95 784.15" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="208">
   <use class="v6300" height="20" transform="rotate(0,812.771,810.267) scale(1.55423,1.11111) translate(-287.059,-79.9156)" width="10" x="804.9999999999998" xlink:href="#GroundDisconnector:地刀_0" y="799.1561342326656" zvalue="238"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449673560070" ObjectName="#1无功补偿装置67267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449673560070"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,812.771,810.267) scale(1.55423,1.11111) translate(-287.059,-79.9156)" width="10" x="804.9999999999998" y="799.1561342326656"/></g>
  <g id="37">
   <use class="kv35" height="20" transform="rotate(180,1073.07,451.25) scale(1.39847,-1.7033) translate(-303.762,-709.143)" width="10" x="1066.074788868779" xlink:href="#GroundDisconnector:地刀_0" y="434.2169526664271" zvalue="377"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449674346502" ObjectName="#1主变35kV侧30167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449674346502"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1073.07,451.25) scale(1.39847,-1.7033) translate(-303.762,-709.143)" width="10" x="1066.074788868779" y="434.2169526664271"/></g>
  <g id="87">
   <use class="kv35" height="20" transform="rotate(180,1461.07,430.25) scale(1.39847,-1.7033) translate(-414.316,-675.814)" width="10" x="1454.074788868779" xlink:href="#GroundDisconnector:地刀_0" y="413.2169526664271" zvalue="399"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449674608646" ObjectName="#2主变35kV侧30267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449674608646"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(180,1461.07,430.25) scale(1.39847,-1.7033) translate(-414.316,-675.814)" width="10" x="1454.074788868779" y="413.2169526664271"/></g>
 </g>
 <g id="CompensatorClass">
  <g id="214">
   <use class="v6300" height="30" transform="rotate(90,843.623,849.823) scale(1.16667,1.33333) translate(-115.518,-207.456)" width="60" x="808.6229204334534" xlink:href="#Compensator:并联电容器12121_0" y="829.8227941176483" zvalue="254"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449673363462" ObjectName="#1无功补偿装置"/>
   <cge:TPSR_Ref TObjectID="6192449673363462"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(90,843.623,849.823) scale(1.16667,1.33333) translate(-115.518,-207.456)" width="60" x="808.6229204334534" y="829.8227941176483"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="GeneratorClass">
  <g id="850">
   <use class="v6300" height="30" transform="rotate(0,1005,842.886) scale(1.85899,1.85899) translate(-451.5,-376.59)" width="30" x="977.1161969178694" xlink:href="#Generator:发电机_0" y="815.0010218856319" zvalue="415"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449674739718" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449674739718"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1005,842.886) scale(1.85899,1.85899) translate(-451.5,-376.59)" width="30" x="977.1161969178694" y="815.0010218856319"/></g>
 </g>
 <g id="MeasurementClass">
  <g id="9">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="9" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,732,37.5) scale(1,1) translate(0,0)" writing-mode="lr" x="731.53" xml:space="preserve" y="42.28" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124593729540" ObjectName="P"/>
   </metadata>
  </g>
  <g id="10">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="10" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,732,59.5) scale(1,1) translate(0,0)" writing-mode="lr" x="731.53" xml:space="preserve" y="64.28" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124593795079" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="11">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="11" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,734.5,82.75) scale(1,1) translate(0,0)" writing-mode="lr" x="734.03" xml:space="preserve" y="87.53" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124593860615" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="3">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="3" prefix="Ux:" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,733.25,102.5) scale(1,1) translate(0,0)" writing-mode="lr" x="732.78" xml:space="preserve" y="107.28" zvalue="1">Ux:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124594581510" ObjectName="Ux"/>
   </metadata>
  </g>
  <g id="7">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="7" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,843.623,941.323) scale(1,1) translate(0,0)" writing-mode="lr" x="843.15" xml:space="preserve" y="946.1" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124585865220" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="8">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="8" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,846.123,967.073) scale(1,1) translate(0,0)" writing-mode="lr" x="845.65" xml:space="preserve" y="971.85" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124585930756" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="16">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="16" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1008,927.771) scale(1,1) translate(0,0)" writing-mode="lr" x="1007.42" xml:space="preserve" y="932.55" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124598382596" ObjectName="P"/>
   </metadata>
  </g>
  <g id="17">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="17" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1008,950.396) scale(1,1) translate(0,0)" writing-mode="lr" x="1007.42" xml:space="preserve" y="955.17" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124598448135" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="18">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="18" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1008,973.021) scale(1,1) translate(0,0)" writing-mode="lr" x="1007.42" xml:space="preserve" y="977.8" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124598513668" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="19">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="19" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,749.5,708.073) scale(1,1) translate(0,0)" writing-mode="lr" x="749.03" xml:space="preserve" y="712.85" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124591828998" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="20">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="20" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1673.39,663.906) scale(1,1) translate(0,0)" writing-mode="lr" x="1672.92" xml:space="preserve" y="668.6799999999999" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124592353286" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="21">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="21" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1040.92,239.573) scale(1,1) translate(0,0)" writing-mode="lr" x="1040.45" xml:space="preserve" y="244.35" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124592877572" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="22">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="22" prefix="Uca:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,538.75,337.739) scale(1,1) translate(0,0)" writing-mode="lr" x="538.28" xml:space="preserve" y="342.52" zvalue="1">Uca:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124593401860" ObjectName="Uca"/>
   </metadata>
  </g>
  <g id="23">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="23" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1613.31,338.475) scale(1,1) translate(0,2.16209e-13)" writing-mode="lr" x="1612.73" xml:space="preserve" y="343.2" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124595302404" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="24">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="24" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1613.31,361.911) scale(1,1) translate(0,2.3182e-13)" writing-mode="lr" x="1612.73" xml:space="preserve" y="366.63" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124595367940" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="25">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="25" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1445.81,557.739) scale(1,1) translate(0,-1.20753e-13)" writing-mode="lr" x="1445.23" xml:space="preserve" y="562.46" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124595433476" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="26">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="26" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1445.81,587.161) scale(1,1) translate(0,-1.27286e-13)" writing-mode="lr" x="1445.23" xml:space="preserve" y="591.88" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124595499012" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="27">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="27" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1613.31,385.347) scale(1,1) translate(0,2.47432e-13)" writing-mode="lr" x="1612.73" xml:space="preserve" y="390.07" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124595564548" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="28">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="28" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1445.81,616.584) scale(1,1) translate(-6.17421e-13,3.34548e-13)" writing-mode="lr" x="1445.23" xml:space="preserve" y="621.3" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124595892231" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="29">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="29" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1059.06,341.823) scale(1,1) translate(0,0)" writing-mode="lr" x="1058.48" xml:space="preserve" y="346.6" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124587110404" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="32">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="32" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1061.56,368.823) scale(1,1) translate(1.11695e-13,0)" writing-mode="lr" x="1060.98" xml:space="preserve" y="373.6" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124587175940" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="35" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1060.31,395.823) scale(1,1) translate(1.11556e-13,0)" writing-mode="lr" x="1059.73" xml:space="preserve" y="400.6" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124587372548" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="46">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="46" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1042.81,574.823) scale(1,1) translate(0,0)" writing-mode="lr" x="1042.23" xml:space="preserve" y="579.6" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124587241476" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="47">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="47" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1042.81,602.448) scale(1,1) translate(0,0)" writing-mode="lr" x="1042.23" xml:space="preserve" y="607.23" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124587307012" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="49">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="49" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1042.81,630.073) scale(1,1) translate(0,0)" writing-mode="lr" x="1042.23" xml:space="preserve" y="634.85" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124587700228" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="12">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="12" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,145.607,175) scale(1,1) translate(0,0)" writing-mode="lr" x="145.76" xml:space="preserve" y="181.27" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124600938500" ObjectName=""/>
   </metadata>
  </g>
  <g id="13">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,314.722,172.194) scale(1,1) translate(-2.30236e-13,0)" writing-mode="lr" x="314.96" xml:space="preserve" y="178.68" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124601004036" ObjectName="HYW1"/>
   </metadata>
  </g>
  <g id="14">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,137.972,198.944) scale(1,1) translate(-7.325e-14,0)" writing-mode="lr" x="138.21" xml:space="preserve" y="205.43" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124593598468" ObjectName="F"/>
   </metadata>
  </g>
  <g id="15">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,316.722,200.194) scale(1,1) translate(-2.32012e-13,0)" writing-mode="lr" x="316.96" xml:space="preserve" y="206.68" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124593074180" ObjectName="F"/>
   </metadata>
  </g>
  <g id="50">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,141.722,221.444) scale(1,1) translate(-7.65807e-14,0)" writing-mode="lr" x="141.96" xml:space="preserve" y="227.93" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124592025607" ObjectName="F"/>
   </metadata>
  </g>
  <g id="51">
   <text Format="f5.1" Plane="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,320.472,222.694) scale(1,1) translate(-2.35343e-13,0)" writing-mode="lr" x="320.71" xml:space="preserve" y="229.18" zvalue="1">dddd.d</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124592549892" ObjectName="F"/>
   </metadata>
  </g>
  <g id="63">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,123.375,449.611) scale(1,1) translate(0,0)" writing-mode="lr" x="123.49" xml:space="preserve" y="454.52" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124593205252" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="180">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="180" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,123.5,473.861) scale(1,1) translate(0,1.5433e-13)" writing-mode="lr" x="123.62" xml:space="preserve" y="478.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124593270788" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="181">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="181" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,123.5,498.111) scale(1,1) translate(0,1.08271e-13)" writing-mode="lr" x="123.62" xml:space="preserve" y="503.02" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124593336324" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="185">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="185" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,121.062,523.861) scale(1,1) translate(0,0)" writing-mode="lr" x="121.19" xml:space="preserve" y="528.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124593664004" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="186">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="186" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,123.5,424.861) scale(1,1) translate(0,0)" writing-mode="lr" x="123.62" xml:space="preserve" y="429.77" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124593467398" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="187">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="187" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,183.188,450) scale(1,1) translate(0,0)" writing-mode="lr" x="183.31" xml:space="preserve" y="454.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124592680964" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="188">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,181.312,474.25) scale(1,1) translate(0,0)" writing-mode="lr" x="181.45" xml:space="preserve" y="479.16" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124592746503" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="189">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="189" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,182.562,497.25) scale(1,1) translate(0,0)" writing-mode="lr" x="182.68" xml:space="preserve" y="502.16" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124592812038" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="193">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="193" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,180.688,424) scale(1,1) translate(0,0)" writing-mode="lr" x="180.81" xml:space="preserve" y="428.91" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124592943108" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="195">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="195" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,183.25,524.25) scale(1,1) translate(0,0)" writing-mode="lr" x="183.37" xml:space="preserve" y="529.16" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481124593139716" ObjectName="U0"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="427">
   <use height="30" transform="rotate(0,330.25,284.983) scale(0.708333,0.665547) translate(131.61,138.194)" width="30" x="319.63" xlink:href="#State:红绿圆(方形)_0" y="275" zvalue="489"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374887092227" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,330.25,284.983) scale(0.708333,0.665547) translate(131.61,138.194)" width="30" x="319.63" y="275"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,234.625,284.983) scale(0.708333,0.665547) translate(92.2353,138.194)" width="30" x="224" xlink:href="#State:红绿圆(方形)_0" y="275" zvalue="490"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562950291980292" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,234.625,284.983) scale(0.708333,0.665547) translate(92.2353,138.194)" width="30" x="224" y="275"/></g>
  <g id="830">
   <use height="30" transform="rotate(0,318.812,127.464) scale(1.27778,1.03333) translate(-56.8071,-3.61174)" width="90" x="261.31" xlink:href="#State:全站检修_0" y="111.96" zvalue="520"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549583282178" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,318.812,127.464) scale(1.27778,1.03333) translate(-56.8071,-3.61174)" width="90" x="261.31" y="111.96"/></g>
 </g>
</svg>