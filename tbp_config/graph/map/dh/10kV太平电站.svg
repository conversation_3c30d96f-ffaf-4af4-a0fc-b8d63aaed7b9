<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549586624514" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_0" viewBox="0,0,40,60">
   <use terminal-index="0" type="1" x="20.03950617434655" xlink:href="#terminal" y="0.5422210984971336"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00564423073277" x2="20.00564423073277" y1="11.49999929428098" y2="17.5629874818471"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.00551392354377" x2="14.09999977493285" y1="17.55888434939838" y2="23.29999974441527"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="20.0237526386311" x2="26.10000023269654" y1="17.5622244918551" y2="23.29999974441527"/>
   <ellipse cx="20.07" cy="18.11" fill-opacity="0" rx="17.73" ry="17.73" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D不带中性点_1" viewBox="0,0,40,60">
   <use terminal-index="1" type="1" x="20" xlink:href="#terminal" y="59.57685298011926"/>
   <path d="M 14.2 39.4 L 25.9 39.4 L 20.1 48.9879 z" fill-opacity="0" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="19.99" cy="42.01" fill-opacity="0" rx="17.61" ry="17.61" stroke="rgb(85,170,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="10kV太平电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="50.21" xlink:href="logo.png" y="47.57"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,199.214,79.5714) scale(1,1) translate(0,0)" writing-mode="lr" x="199.21" xml:space="preserve" y="83.06999999999999" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,201.714,77.2618) scale(1,1) translate(0,0)" writing-mode="lr" x="201.71" xml:space="preserve" y="86.26000000000001" zvalue="5">10kV太平电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="19" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,78.625,217) scale(1,1) translate(0,0)" width="72.88" x="42.19" y="205" zvalue="108"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,78.625,217) scale(1,1) translate(0,0)" writing-mode="lr" x="78.63" xml:space="preserve" y="221.5" zvalue="108">信号一览</text>
  <line fill="none" id="44" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384.2142857142856" x2="384.2142857142856" y1="15.57142857142867" y2="1045.571428571429" zvalue="6"/>
  <line fill="none" id="41" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="151.441921185511" y2="151.441921185511" zvalue="8"/>
  <line fill="none" id="40" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="621.441921185511" y2="621.441921185511" zvalue="9"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="936.5714285714287" y2="936.5714285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="975.7347285714286" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="370.2142857142856" y1="936.5714285714287" y2="936.5714285714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="370.2142857142856" y1="975.7347285714286" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="936.5714285714287" y2="975.7347285714286"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142856" x2="190.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="190.2142857142857" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142857" x2="280.2142857142857" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="975.7346985714287" y2="975.7346985714287"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1003.653098571429" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="280.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="975.7346985714287" y2="1003.653098571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="100.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="10.21428571428555" x2="10.21428571428555" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="190.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="100.2142857142856" x2="100.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142856" x2="190.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="280.2142857142857" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="190.2142857142857" x2="190.2142857142857" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142857" x2="280.2142857142857" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1003.653028571429" y2="1003.653028571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="370.2142857142856" y1="1031.571428571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="280.2142857142856" x2="280.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="370.2142857142856" x2="370.2142857142856" y1="1003.653028571429" y2="1031.571428571429"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,55.2143,956.571) scale(1,1) translate(0,0)" writing-mode="lr" x="55.21" xml:space="preserve" y="962.5700000000001" zvalue="12">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="36" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,52.2143,990.571) scale(1,1) translate(0,0)" writing-mode="lr" x="52.21" xml:space="preserve" y="996.5700000000001" zvalue="13">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,234.214,990.571) scale(1,1) translate(0,0)" writing-mode="lr" x="234.21" xml:space="preserve" y="996.5700000000001" zvalue="14">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="34" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.2143,1018.57) scale(1,1) translate(0,0)" writing-mode="lr" x="51.21" xml:space="preserve" y="1024.57" zvalue="15">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="33" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,233.214,1018.57) scale(1,1) translate(0,0)" writing-mode="lr" x="233.21" xml:space="preserve" y="1024.57" zvalue="16">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="30" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,75.7143,651.071) scale(1,1) translate(0,2.10522e-13)" writing-mode="lr" x="75.71428571428555" xml:space="preserve" y="655.5714285714286" zvalue="19">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235.268,958.571) scale(1,1) translate(0,0)" writing-mode="lr" x="235.27" xml:space="preserve" y="964.5700000000001" zvalue="27">TaiPing-01-2019</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,684.386,526.019) scale(1,1) translate(0,0)" writing-mode="lr" x="684.39" xml:space="preserve" y="530.52" zvalue="31">#1主变750kVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,668.061,885.74) scale(1,1) translate(0,2.88097e-13)" writing-mode="lr" x="668.0606713030319" xml:space="preserve" y="890.2404541368701" zvalue="33">#1发电机250kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,677.524,747.175) scale(1,1) translate(0,0)" writing-mode="lr" x="677.52" xml:space="preserve" y="751.67" zvalue="36">401</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,684.271,670.564) scale(1,1) translate(0,0)" writing-mode="lr" x="684.27" xml:space="preserve" y="675.0599999999999" zvalue="37">4011</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1242.97,371.167) scale(1,1) translate(0,0)" writing-mode="lr" x="1242.97" xml:space="preserve" y="375.67" zvalue="39">10kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1154.56,893.24) scale(1,1) translate(0,2.90595e-13)" writing-mode="lr" x="1154.560671303032" xml:space="preserve" y="897.7404541368701" zvalue="41">#3发电机125kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1168.14,706.452) scale(1,1) translate(0,0)" writing-mode="lr" x="1168.14" xml:space="preserve" y="710.95" zvalue="44">403</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1170.89,627.841) scale(1,1) translate(0,0)" writing-mode="lr" x="1170.89" xml:space="preserve" y="632.34" zvalue="45">4031</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,910.01,165.444) scale(1,1) translate(0,0)" writing-mode="lr" x="910.01" xml:space="preserve" y="169.94" zvalue="49">10kV太平街道Ⅱ回线太平电站支线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="52" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,839.311,894.49) scale(1,1) translate(0,2.91011e-13)" writing-mode="lr" x="839.3106713030318" xml:space="preserve" y="898.9904541368701" zvalue="62">#2发电机250kW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="51" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,848.774,751.925) scale(1,1) translate(0,0)" writing-mode="lr" x="848.77" xml:space="preserve" y="756.42" zvalue="65">402</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,855.521,679.314) scale(1,1) translate(0,0)" writing-mode="lr" x="855.52" xml:space="preserve" y="683.8099999999999" zvalue="66">4021</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="56" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1068.02,529.019) scale(1,1) translate(1.13356e-13,0)" writing-mode="lr" x="1068.02" xml:space="preserve" y="533.52" zvalue="69">#2主变160kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="59" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,728,414.5) scale(1,1) translate(0,0)" writing-mode="lr" x="728" xml:space="preserve" y="419" zvalue="71">0011</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="80" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1109,423.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1109" xml:space="preserve" y="427.75" zvalue="85">0021</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="186" y1="278.75" y2="278.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="186" y1="304.75" y2="304.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="5" y1="278.75" y2="304.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="186" y1="278.75" y2="304.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="367" y1="278.75" y2="278.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="367" y1="304.75" y2="304.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="186" y1="278.75" y2="304.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367" x2="367" y1="278.75" y2="304.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="186" y1="304.75" y2="304.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="186" y1="329" y2="329"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="5" y1="304.75" y2="329"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="186" y1="304.75" y2="329"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="367" y1="304.75" y2="304.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="367" y1="329" y2="329"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="186" y1="304.75" y2="329"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367" x2="367" y1="304.75" y2="329"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="186" y1="329" y2="329"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="186" y1="351.75" y2="351.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="5" y1="329" y2="351.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="186" y1="329" y2="351.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="367" y1="329" y2="329"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="367" y1="351.75" y2="351.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="186" y1="329" y2="351.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367" x2="367" y1="329" y2="351.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="186" y1="351.75" y2="351.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="186" y1="374.5" y2="374.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="5" y1="351.75" y2="374.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="186" y1="351.75" y2="374.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="367" y1="351.75" y2="351.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="367" y1="374.5" y2="374.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="186" y1="351.75" y2="374.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367" x2="367" y1="351.75" y2="374.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="186" y1="374.5" y2="374.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="186" y1="397.25" y2="397.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="5" y1="374.5" y2="397.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="186" y1="374.5" y2="397.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="367" y1="374.5" y2="374.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="367" y1="397.25" y2="397.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="186" y1="374.5" y2="397.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367" x2="367" y1="374.5" y2="397.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="186" y1="397.25" y2="397.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="186" y1="420" y2="420"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="5" y1="397.25" y2="420"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="186" y1="397.25" y2="420"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="367" y1="397.25" y2="397.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="367" y1="420" y2="420"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="186" y1="397.25" y2="420"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367" x2="367" y1="397.25" y2="420"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="186" y1="420" y2="420"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="186" y1="442.75" y2="442.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="5" x2="5" y1="420" y2="442.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="186" y1="420" y2="442.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="367" y1="420" y2="420"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="367" y1="442.75" y2="442.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="186" x2="186" y1="420" y2="442.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="367" x2="367" y1="420" y2="442.75"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="71" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,194.399,217.591) scale(1,1) translate(0,0)" writing-mode="lr" x="194.4" xml:space="preserve" y="222.09" zvalue="98">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,299.399,217.591) scale(1,1) translate(0,0)" writing-mode="lr" x="299.4" xml:space="preserve" y="222.09" zvalue="99">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="68" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,51.5,291.75) scale(1,1) translate(0,0)" writing-mode="lr" x="9" xml:space="preserve" y="296.25" zvalue="100">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="67" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,232,291.75) scale(1,1) translate(0,0)" writing-mode="lr" x="189.5" xml:space="preserve" y="296.25" zvalue="101">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="66" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.6875,365) scale(1,1) translate(0,0)" writing-mode="lr" x="54.69" xml:space="preserve" y="369.5" zvalue="102">0.4kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="18" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,50.5,317.75) scale(1,1) translate(0,0)" writing-mode="lr" x="8" xml:space="preserve" y="322.25" zvalue="109">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="17" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,231,317.75) scale(1,1) translate(0,0)" writing-mode="lr" x="188.5" xml:space="preserve" y="322.25" zvalue="110">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="16" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.6875,411) scale(1,1) translate(0,0)" writing-mode="lr" x="51.69" xml:space="preserve" y="415.5" zvalue="113">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,219.688,410) scale(1,1) translate(0,0)" writing-mode="lr" x="219.69" xml:space="preserve" y="414.5" zvalue="114">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="14" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,51.6875,434) scale(1,1) translate(0,0)" writing-mode="lr" x="51.69" xml:space="preserve" y="438.5" zvalue="115">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="13" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,219.688,433) scale(1,1) translate(0,0)" writing-mode="lr" x="219.69" xml:space="preserve" y="437.5" zvalue="116">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,50.5,340.75) scale(1,1) translate(0,0)" writing-mode="lr" x="8" xml:space="preserve" y="345.25" zvalue="117">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,230.5,339.75) scale(1,1) translate(0,0)" writing-mode="lr" x="188" xml:space="preserve" y="344.25" zvalue="119">厂用电率</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="42.19" y="205" zvalue="108"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="PowerTransformer2Class">
  <g id="414">
   <g id="4140">
    <use class="kv10" height="60" transform="rotate(0,754.85,517.771) scale(1.16106,1.11219) translate(-101.492,-48.8633)" width="40" x="731.63" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="484.41" zvalue="30"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874436771844" ObjectName="10"/>
    </metadata>
   </g>
   <g id="4141">
    <use class="v400" height="60" transform="rotate(0,754.85,517.771) scale(1.16106,1.11219) translate(-101.492,-48.8633)" width="40" x="731.63" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="484.41" zvalue="30"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874436837380" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399450624004" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399450624004"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,754.85,517.771) scale(1.16106,1.11219) translate(-101.492,-48.8633)" width="40" x="731.63" y="484.41"/></g>
  <g id="57">
   <g id="570">
    <use class="kv10" height="60" transform="rotate(0,1144.74,531.521) scale(1.16106,1.11219) translate(-155.577,-50.2503)" width="40" x="1121.52" xlink:href="#PowerTransformer2:Y-D不带中性点_0" y="498.16" zvalue="68"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874436902916" ObjectName="10"/>
    </metadata>
   </g>
   <g id="571">
    <use class="v400" height="60" transform="rotate(0,1144.74,531.521) scale(1.16106,1.11219) translate(-155.577,-50.2503)" width="40" x="1121.52" xlink:href="#PowerTransformer2:Y-D不带中性点_1" y="498.16" zvalue="68"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874436968452" ObjectName="0.4"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399450689540" ObjectName="#2主变"/>
   <cge:TPSR_Ref TObjectID="6755399450689540"/></metadata>
  <rect fill="white" height="60" opacity="0" stroke="white" transform="rotate(0,1144.74,531.521) scale(1.16106,1.11219) translate(-155.577,-50.2503)" width="40" x="1121.52" y="498.16"/></g>
 </g>
 <g id="GeneratorClass">
  <g id="212">
   <use class="v400" height="30" transform="rotate(0,658.215,823.366) scale(1.52192,1.52192) translate(-217.897,-274.534)" width="30" x="635.3863709131574" xlink:href="#Generator:发电机_0" y="800.5367361713461" zvalue="32"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449798078470" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449798078470"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,658.215,823.366) scale(1.52192,1.52192) translate(-217.897,-274.534)" width="30" x="635.3863709131574" y="800.5367361713461"/></g>
  <g id="62">
   <use class="v400" height="30" transform="rotate(0,1143.75,829.899) scale(1.45749,1.45749) translate(-352.148,-253.634)" width="30" x="1121.886370913157" xlink:href="#Generator:发电机_0" y="808.0367361713459" zvalue="40"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449797947398" ObjectName="#3发电机"/>
   <cge:TPSR_Ref TObjectID="6192449797947398"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1143.75,829.899) scale(1.45749,1.45749) translate(-352.148,-253.634)" width="30" x="1121.886370913157" y="808.0367361713459"/></g>
  <g id="55">
   <use class="v400" height="30" transform="rotate(0,829.465,832.116) scale(1.52192,1.52192) translate(-276.625,-277.534)" width="30" x="806.6363709131574" xlink:href="#Generator:发电机_0" y="809.2867361713461" zvalue="61"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449798209542" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449798209542"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,829.465,832.116) scale(1.52192,1.52192) translate(-276.625,-277.534)" width="30" x="806.6363709131574" y="809.2867361713461"/></g>
 </g>
 <g id="BreakerClass">
  <g id="108">
   <use class="v400" height="20" transform="rotate(0,658.163,748.175) scale(1.22222,1.11111) translate(-118.555,-73.7063)" width="10" x="652.0522020423972" xlink:href="#Breaker:开关_0" y="737.0634920634918" zvalue="34"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924511137797" ObjectName="#1发电机401断路器"/>
   <cge:TPSR_Ref TObjectID="6473924511137797"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,658.163,748.175) scale(1.22222,1.11111) translate(-118.555,-73.7063)" width="10" x="652.0522020423972" y="737.0634920634918"/></g>
  <g id="61">
   <use class="v400" height="20" transform="rotate(0,1144.78,707.452) scale(1.22222,1.11111) translate(-207.03,-69.6341)" width="10" x="1138.667259530653" xlink:href="#Breaker:开关_0" y="696.3412698412696" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924511072261" ObjectName="#3发电机403断路器"/>
   <cge:TPSR_Ref TObjectID="6473924511072261"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1144.78,707.452) scale(1.22222,1.11111) translate(-207.03,-69.6341)" width="10" x="1138.667259530653" y="696.3412698412696"/></g>
  <g id="54">
   <use class="v400" height="20" transform="rotate(0,829.413,752.925) scale(1.22222,1.11111) translate(-149.691,-74.1813)" width="10" x="823.3022020423972" xlink:href="#Breaker:开关_0" y="741.8134920634918" zvalue="63"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924511203333" ObjectName="#2发电机402断路器"/>
   <cge:TPSR_Ref TObjectID="6473924511203333"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,829.413,752.925) scale(1.22222,1.11111) translate(-149.691,-74.1813)" width="10" x="823.3022020423972" y="741.8134920634918"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="109">
   <use class="v400" height="30" transform="rotate(0,658.313,671.564) scale(-1.11111,-0.814815) translate(-1249.96,-1498.53)" width="15" x="649.9794217728852" xlink:href="#Disconnector:刀闸_0" y="659.3412835106017" zvalue="35"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449798012934" ObjectName="#1发电机4011隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449798012934"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,658.313,671.564) scale(-1.11111,-0.814815) translate(-1249.96,-1498.53)" width="15" x="649.9794217728852" y="659.3412835106017"/></g>
  <g id="60">
   <use class="v400" height="30" transform="rotate(0,1144.93,628.841) scale(-1.11111,-0.814815) translate(-2174.53,-1403.38)" width="15" x="1136.594479261141" xlink:href="#Disconnector:刀闸_0" y="616.6190612883794" zvalue="43"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449797881862" ObjectName="#3发电机4031隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449797881862"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1144.93,628.841) scale(-1.11111,-0.814815) translate(-2174.53,-1403.38)" width="15" x="1136.594479261141" y="616.6190612883794"/></g>
  <g id="53">
   <use class="v400" height="30" transform="rotate(0,829.563,680.314) scale(-1.11111,-0.814815) translate(-1575.34,-1518.02)" width="15" x="821.2294217728852" xlink:href="#Disconnector:刀闸_0" y="668.0912835106017" zvalue="64"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449798144006" ObjectName="#2发电机4021隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449798144006"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,829.563,680.314) scale(-1.11111,-0.814815) translate(-1575.34,-1518.02)" width="15" x="821.2294217728852" y="668.0912835106017"/></g>
  <g id="58">
   <use class="kv10" height="30" transform="rotate(0,755,415.5) scale(1.25,1.25) translate(-149.125,-79.35)" width="15" x="745.625" xlink:href="#Disconnector:令克_0" y="396.75" zvalue="70"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449798275078" ObjectName="#1主变10kV侧0011"/>
   <cge:TPSR_Ref TObjectID="6192449798275078"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,755,415.5) scale(1.25,1.25) translate(-149.125,-79.35)" width="15" x="745.625" y="396.75"/></g>
  <g id="77">
   <use class="kv10" height="30" transform="rotate(0,1145,424.25) scale(1.25,1.25) translate(-227.125,-81.1)" width="15" x="1135.625" xlink:href="#Disconnector:令克_0" y="405.5" zvalue="84"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449798340614" ObjectName="#2主变10kV侧0021隔离刀闸"/>
   <cge:TPSR_Ref TObjectID="6192449798340614"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1145,424.25) scale(1.25,1.25) translate(-227.125,-81.1)" width="15" x="1135.625" y="405.5"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="43">
   <path class="kv10" d="M 683.19 341.06 L 1269.86 341.06" stroke-width="6" zvalue="38"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674243444740" ObjectName="10kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674243444740"/></metadata>
  <path d="M 683.19 341.06 L 1269.86 341.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="49">
   <path class="kv10" d="M 910.01 211.51 L 910.01 341.06" stroke-width="1" zvalue="59"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="43@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 910.01 211.51 L 910.01 341.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="63">
   <path class="kv10" d="M 755.1 398.94 L 755.1 341.06" stroke-width="1" zvalue="71"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="58@0" LinkObjectIDznd="43@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.1 398.94 L 755.1 341.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv10" d="M 754.9 485.01 L 754.9 430.81" stroke-width="1" zvalue="72"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@0" LinkObjectIDznd="58@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 754.9 485.01 L 754.9 430.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="78">
   <path class="kv10" d="M 1144.78 498.76 L 1144.78 439.56" stroke-width="1" zvalue="85"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="57@0" LinkObjectIDznd="77@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1144.78 498.76 L 1144.78 439.56" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="79">
   <path class="kv10" d="M 1145.1 407.69 L 1145.1 341.06" stroke-width="1" zvalue="86"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="77@0" LinkObjectIDznd="43@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1145.1 407.69 L 1145.1 341.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="81">
   <path class="v400" d="M 1144.86 616.83 L 1144.86 564.42" stroke-width="1" zvalue="87"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@1" LinkObjectIDznd="57@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1144.86 616.83 L 1144.86 564.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="82">
   <path class="v400" d="M 1144.83 640.66 L 1144.83 696.82" stroke-width="1" zvalue="88"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="61@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1144.83 640.66 L 1144.83 696.82" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="83">
   <path class="v400" d="M 1143.75 808.4 L 1143.75 718.06" stroke-width="1" zvalue="89"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="62@0" LinkObjectIDznd="61@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1143.75 808.4 L 1143.75 718.06" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="84">
   <path class="v400" d="M 658.22 800.92 L 658.24 758.79" stroke-width="1" zvalue="90"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="212@0" LinkObjectIDznd="108@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 658.22 800.92 L 658.24 758.79" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="85">
   <path class="v400" d="M 658.12 737.54 L 658.22 683.38" stroke-width="1" zvalue="91"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="108@0" LinkObjectIDznd="109@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 658.12 737.54 L 658.22 683.38" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="86">
   <path class="v400" d="M 829.47 692.13 L 829.37 742.29" stroke-width="1" zvalue="92"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="53@0" LinkObjectIDznd="54@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 829.47 692.13 L 829.37 742.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="87">
   <path class="v400" d="M 829.49 763.54 L 829.47 809.67" stroke-width="1" zvalue="93"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="54@1" LinkObjectIDznd="55@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 829.49 763.54 L 829.47 809.67" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="v400" d="M 658.24 659.55 L 658.24 616.46 L 829.49 616.46 L 829.49 668.3" stroke-width="1" zvalue="94"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="109@1" LinkObjectIDznd="53@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 658.24 659.55 L 658.24 616.46 L 829.49 616.46 L 829.49 668.3" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="v400" d="M 754.85 550.67 L 754.85 616.46" stroke-width="1" zvalue="95"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="414@1" LinkObjectIDznd="88" MaxPinNum="2"/>
   </metadata>
  <path d="M 754.85 550.67 L 754.85 616.46" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="MeasurementClass">
  <g id="274">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="274" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,151.611,363.917) scale(1,1) translate(0,0)" writing-mode="lr" x="151.77" xml:space="preserve" y="368.83" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="F"/>
   </metadata>
  </g>
  <g id="273">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="273" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,151.611,291.917) scale(1,1) translate(0,0)" writing-mode="lr" x="151.77" xml:space="preserve" y="296.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125301190660" ObjectName="F"/>
   </metadata>
  </g>
  <g id="272">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="272" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,329.222,292.917) scale(1,1) translate(0,0)" writing-mode="lr" x="329.38" xml:space="preserve" y="297.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125301256196" ObjectName="F"/>
   </metadata>
  </g>
  <g id="260">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="260" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,151.611,316.917) scale(1,1) translate(0,0)" writing-mode="lr" x="151.77" xml:space="preserve" y="321.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125301059590" ObjectName="F"/>
   </metadata>
  </g>
  <g id="234">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="234" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,329.222,317.917) scale(1,1) translate(0,0)" writing-mode="lr" x="329.38" xml:space="preserve" y="322.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125301125126" ObjectName="F"/>
   </metadata>
  </g>
  <g id="209">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="209" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,151.611,340.917) scale(1,1) translate(0,0)" writing-mode="lr" x="151.77" xml:space="preserve" y="345.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125301059590" ObjectName="F"/>
   </metadata>
  </g>
  <g id="198">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="198" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,331.611,339.917) scale(1,1) translate(0,0)" writing-mode="lr" x="331.77" xml:space="preserve" y="344.83" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125301059590" ObjectName="F"/>
   </metadata>
  </g>
  <g id="287">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="287" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,151.611,407.917) scale(1,1) translate(0,2.64733e-13)" writing-mode="lr" x="151.77" xml:space="preserve" y="412.83" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="288">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="288" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,327.611,407.917) scale(1,1) translate(0,2.64733e-13)" writing-mode="lr" x="327.77" xml:space="preserve" y="412.83" zvalue="1">????</text>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName="雨量采集"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="271">
   <use height="30" transform="rotate(0,326.673,218.107) scale(0.708333,0.665547) translate(130.137,104.587)" width="30" x="316.05" xlink:href="#State:红绿圆(方形)_0" y="208.12" zvalue="107"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,326.673,218.107) scale(0.708333,0.665547) translate(130.137,104.587)" width="30" x="316.05" y="208.12"/></g>
  <g id="286">
   <use height="30" transform="rotate(0,231.048,218.107) scale(0.708333,0.665547) translate(90.7623,104.587)" width="30" x="220.42" xlink:href="#State:红绿圆(方形)_0" y="208.12" zvalue="124"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,231.048,218.107) scale(0.708333,0.665547) translate(90.7623,104.587)" width="30" x="220.42" y="208.12"/></g>
 </g>
</svg>