<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549585379330" height="1056" id="thSvg" source="NR-PCS9000" viewBox="0 0 1920 1056" width="1920">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Breaker:开关_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Breaker:开关_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="19.42" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" transform="rotate(0,5.04,9.96) scale(1,1) translate(0,0)" width="9.08" x="0.5" y="0.25"/>
  </symbol>
  <symbol id="Breaker:开关_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="4.966666666666667" xlink:href="#terminal" y="0.43333333333333"/>
   <use terminal-index="1" type="0" x="5.066666666666666" xlink:href="#terminal" y="19.55"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.75" x2="9.583333333333334" y1="0.5833333333333321" y2="19.58333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.333333333333334" x2="0.833333333333333" y1="0.5" y2="19.35"/>
   <rect fill-opacity="0" height="19.08" rx="0" ry="0" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,5.08,9.96) scale(1,1) translate(0,0)" width="8.83" x="0.67" y="0.42"/>
  </symbol>
  <symbol id="Disconnector:刀闸_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.75" x2="7.583333333333333" y1="6.666666666666668" y2="24"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
  </symbol>
  <symbol id="Disconnector:刀闸_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.6" x2="7.6" y1="6.083333333333334" y2="29.99999999999999"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="Disconnector:刀闸_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.587777050860793" xlink:href="#terminal" y="0.4959386100732566"/>
   <use terminal-index="1" type="0" x="7.561164424105821" xlink:href="#terminal" y="29.74350339427581"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="23.91666666666666" y2="29.66666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.75" x2="12.5" y1="6.083333333333336" y2="24.16666666666666"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="12.58333333333333" x2="2.75" y1="6.166666666666666" y2="23.91666666666666"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.966666666666667" x2="8.933333333333334" y1="6.005662181544974" y2="6.005662181544974"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.591506727682535" x2="7.591506727682535" y1="6.000000000000002" y2="0.6054618309314215"/>
  </symbol>
  <symbol id="EnergyConsumer:负荷_0" viewBox="0,0,12,30">
   <use terminal-index="0" type="0" x="6" xlink:href="#terminal" y="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="6.000411522633746" x2="6.000411522633746" y1="4.499999999999996" y2="28.48902606310014"/>
   <path d="M 5.91667 0.833333 L 4.5 7.75 L 6 4.25 L 7.5 7.91667 L 5.95238 0.616667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Generator:发电机_0" viewBox="0,0,30,30">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="0.25"/>
   <ellipse cx="15" cy="15" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0667 15 A 5.09167 5.41667 0 0 0 25.25 15" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <path d="M 15.0239 14.8488 A 5.26235 5.29167 -180 0 0 4.50079 15.0345" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_0" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="9.583333333333332" y2="1.050000000000001"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="26.41666666666667" y2="34.83333333333334"/>
   <rect fill-opacity="0" height="17" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,18) scale(1,1) translate(0,0)" width="8" x="3" y="9.5"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_1" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7" x2="7" y1="35" y2="1.166666666666664"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <rect fill-opacity="0" height="17" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7,18) scale(1,1) translate(0,0)" width="8" x="3" y="9.5"/>
  </symbol>
  <symbol id="Disconnector:联体小车刀闸2_2" viewBox="0,0,14,36">
   <use terminal-index="0" type="0" x="7" xlink:href="#terminal" y="1"/>
   <use terminal-index="1" type="0" x="7" xlink:href="#terminal" y="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.083333333333333" x2="7.083333333333333" y1="9.999999999999995" y2="1.299999999999994"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7" x2="7" y1="27.08333333333334" y2="35"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="3.936947459912011" y2="8.747292287498221"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.00133744855967" x2="12.459670781893" y1="0.9304819426706423" y2="5.740826770256851"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559672" x2="1.543004115226339" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="31.99082677025685" y2="27.18048194267064"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="7.001337448559669" x2="12.459670781893" y1="34.99729228749822" y2="30.18694745991201"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12" x2="1.499999999999999" y1="26" y2="9.966666666666669"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.999999999999998" x2="12.41666666666667" y1="26.25" y2="10.05"/>
  </symbol>
  <symbol id="Accessory:PT12321_0" viewBox="0,0,30,29">
   <use terminal-index="0" type="0" x="15" xlink:href="#terminal" y="28.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="2.916666666666664" y2="5.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="5.666666666666663" y2="7.583333333333329"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="18.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="5.666666666666664" y2="7.666666666666664"/>
   <ellipse cx="15.15" cy="13.52" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="15.15" cy="5.42" fill-opacity="0" rx="5" ry="5" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="11" y2="13.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="12.66666666666667" y1="13.5" y2="15.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="17.5" y1="13.5" y2="15.41666666666666"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:五卷PT_0" viewBox="0,0,40,40">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="25.16666666666667" y2="1"/>
   <rect fill-opacity="0" height="14" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,14) scale(1,1) translate(0,0)" width="6" x="7" y="7"/>
   <path d="M 5.11667 33.0667 L 5.11667 37.0667 L 8.11667 35.0667 L 5.11667 33.0667" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.66666666666667" x2="9.999999999999998" y1="4" y2="4"/>
   <ellipse cx="13.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="27.48744029990987" y2="26.24990031555776"/>
   <ellipse cx="13.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="6.48" cy="27.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.44906826265991" x2="6.44906826265991" y1="29.96252026861409" y2="27.48744029990989"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659912" x2="8.849068262659907" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="6.449068262659907" x2="4.049068262659912" y1="27.48744029990987" y2="26.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="15.84906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="11.04906826265991" y1="34.48744029990986" y2="33.24990031555776"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.44906826265991" x2="13.44906826265991" y1="36.96252026861409" y2="34.48744029990989"/>
   <ellipse cx="6.48" cy="34.7" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="26.11944444444444" x2="29.63333333333333" y1="28.26666666666667" y2="28.26666666666667"/>
   <rect fill-opacity="0" height="16.15" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,27.29,13.93) scale(1,1) translate(0,0)" width="7.58" x="23.5" y="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="3.93333333333333" y2="5.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="5.85" y2="15.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="24.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="30.55" y1="15.85" y2="12.85"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="25.16111111111111" x2="30.27222222222222" y1="26.88508771929826" y2="26.88508771929826"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="27.55" x2="27.55" y1="21.85" y2="25.45"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="23.88333333333333" x2="31.55" y1="25.50350877192984" y2="25.50350877192984"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <use terminal-index="2" type="2" x="15.0164609053498" xlink:href="#terminal" y="12.0194189818494"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00470352543122" x2="15.00470352543122" y1="5.416666666666668" y2="12.02321291373541"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.92126160277786" x2="6.666666666666666" y1="12.10207526123773" y2="18.50000000000001"/>
   <line fill="none" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.18646053143751" x2="24.5" y1="12.18904818695178" y2="19.00000000000001"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(170,85,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:Y-D_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <path d="M 7.33333 43.5833 L 23 43.5833 L 15 31.5 z" fill-opacity="0" stroke="rgb(130,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(130,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="Accessory:避雷器1_0" viewBox="0,0,12,26">
   <use terminal-index="0" type="0" x="6.033333333333333" xlink:href="#terminal" y="0.6333333333333364"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="3.05" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="0.6833333333333318" y2="2.599999999999998"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="9.050000000000001" y1="12.6" y2="9.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="2.600000000000001" y2="12.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="6.05" x2="6.05" y1="18.6" y2="22.2"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="2.383333333333333" x2="10.05" y1="22.25350877192984" y2="22.25350877192984"/>
   <rect fill-opacity="0" height="16" rx="0" ry="0" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" transform="rotate(0,6.03,10.6) scale(1,1) translate(0,0)" width="10.05" x="1" y="2.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="4.619444444444438" x2="8.133333333333333" y1="25.01666666666667" y2="25.01666666666667"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="3.661111111111109" x2="8.772222222222222" y1="23.63508771929826" y2="23.63508771929826"/>
  </symbol>
  <symbol id="PowerTransformer2:586_0" viewBox="0,0,30,50">
   <use terminal-index="0" type="1" x="15.03292181069959" xlink:href="#terminal" y="0.4518518518518491"/>
   <line fill="none" stroke="rgb(130,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="19.75" x2="10.08333333333333" y1="17.11381925541057" y2="17.11381925541057"/>
   <line fill="none" stroke="rgb(130,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="14.98518194534241" x2="9.999999999999998" y1="9.386991192770131" y2="16.99800064973551"/>
   <line fill="none" stroke="rgb(130,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="15.00299079104462" x2="19.66666666666667" y1="9.301854138598824" y2="16.8218426122152"/>
   <ellipse cx="15.06" cy="15.09" fill-opacity="0" rx="14.78" ry="14.78" stroke="rgb(130,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
  </symbol>
  <symbol id="PowerTransformer2:586_1" viewBox="0,0,30,50">
   <use terminal-index="1" type="1" x="15" xlink:href="#terminal" y="49.64737654320988"/>
   <ellipse cx="14.99" cy="35.01" fill-opacity="0" rx="14.67" ry="14.67" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="15" y1="30.58333333333333" y2="36.5"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="21" y1="36.5" y2="40.5"/>
   <line fill="none" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="15" x2="10" y1="36.5" y2="40.5"/>
  </symbol>
  <symbol id="Accessory:PT8_0" viewBox="0,0,15,18">
   <use terminal-index="0" type="0" x="6.570083175780933" xlink:href="#terminal" y="0.6391120299271513"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="3.5" y1="9" y2="7"/>
   <path d="M 2.5 8.75 L 2.58333 4.75 L 2.5 0.75 L 10.5 0.75 L 10.5 10.3333" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.5" x2="1.5" y1="9" y2="7"/>
   <rect fill-opacity="0" height="4.58" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10.51,6.04) scale(1,1) translate(0,0)" width="2.22" x="9.4" y="3.75"/>
   <path d="M 8.25 16.5 L 9.75 16 L 7.75 14 L 7.41667 15.75" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="11.75" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="9.416666666666666" y1="12" y2="12.75"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.5" x2="10.5" y1="10.83333333333333" y2="12"/>
   <rect fill-opacity="0" height="3.03" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,2.58,8.99) scale(1,1) translate(0,0)" width="7.05" x="-0.9399999999999999" y="7.47"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.619763607738507" x2="2.619763607738507" y1="12.66666666666667" y2="15.19654089589786"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.052427517957054" x2="4.18709969751996" y1="15.28704961606615" y2="15.28704961606615"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="1.649066340209899" x2="3.738847793251836" y1="15.98364343374679" y2="15.98364343374679"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="2.103758628586885" x2="3.061575127897769" y1="16.50608879700729" y2="16.50608879700729"/>
   <ellipse cx="10.4" cy="12.53" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="12.4" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="8.65" cy="15.28" fill-opacity="0" rx="2.16" ry="2.16" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333334" x2="13.58333333333334" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="11.25" y1="15.5" y2="16.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.33333333333333" x2="12.33333333333333" y1="14.33333333333333" y2="15.5"/>
  </symbol>
  <symbol id="EnergyConsumer:站用变DY接地_0" viewBox="0,0,28,30">
   <use terminal-index="0" type="0" x="14.09187259747391" xlink:href="#terminal" y="0.5964275707480997"/>
   <path d="M 20.625 20.375 L 14 26" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <ellipse cx="13.84" cy="6.58" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="13.92" cy="15.17" fill-opacity="0" rx="5.91" ry="5.99" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="16.37805675512246" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971597" x2="16.37805675512247" y1="7.278558961992625" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.0158963574192" x2="11.65373595971596" y1="2.484555672949975" y2="7.278558961992625"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.01589635741922" x2="14.01589635741922" y1="13.27106307329593" y2="15.66806471781726"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="16.37805675512246" x2="14.01589635741922" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.65373595971596" x2="14.0158963574192" y1="18.06506636233857" y2="15.66806471781724"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.35" x2="21.44459900574187" y1="20.69738744416858" y2="20.69738744416858"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.16891980114837" x2="22.6256792045935" y1="21.89588826642927" y2="21.89588826642927"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.98783960229675" x2="23.80675940344512" y1="23.09438908868992" y2="23.09438908868992"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.86589635741922" x2="24.39742514970058" y1="15.66806471781725" y2="15.66806471781725"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.39729950287094" x2="24.39729950287094" y1="15.70358580253216" y2="20.69738744416856"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="13.99749347109703" x2="13.99749347109703" y1="27.74434593889296" y2="21.16678649034915"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.64729950287094" x2="20.64729950287094" y1="15.70358580253216" y2="20.41666666666667"/>
  </symbol>
  <symbol id="Disconnector:令克_0" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="0.5833333333333304" x2="3.33333333333333" y1="10.66666666666666" y2="8.999999999999998"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="0.25" y1="21.08333333333334" y2="5.999999999999998"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="5.916666666666664" y2="1.916666666666663"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="21.08333333333333" y2="27"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.883333333333333" x2="7.5" y1="19" y2="17.41666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.799999999999999" x2="0.583333333333333" y1="19" y2="10.66666666666667"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.65" x2="3.333333333333334" y1="17.33333333333333" y2="8.833333333333332"/>
  </symbol>
  <symbol id="Disconnector:令克_1" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <rect fill-opacity="0" height="10.92" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,7.46,14.29) scale(1,1) translate(0,0)" width="3.75" x="5.58" y="8.83"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="18.25" y2="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.5" x2="7.5" y1="6.16666666666667" y2="2.166666666666668"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.483333333333333" x2="7.483333333333333" y1="18.16666666666667" y2="6.166666666666668"/>
  </symbol>
  <symbol id="Disconnector:令克_2" viewBox="0,0,15,30">
   <use terminal-index="0" type="0" x="7.583333333333334" xlink:href="#terminal" y="1.749999999999995"/>
   <use terminal-index="1" type="0" x="7.416666666666667" xlink:href="#terminal" y="27.25"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="6.25" y2="2.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="10.58333333333333" x2="4.583333333333333" y1="7.333333333333337" y2="18.33333333333334"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.583333333333333" x2="7.583333333333333" y1="18.33333333333334" y2="27.33333333333334"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.666666666666666" x2="10.58333333333333" y1="7.583333333333337" y2="18.33333333333334"/>
  </symbol>
  <symbol id=":线路负荷1_0" viewBox="0,0,7,30">
   <use terminal-index="0" type="0" x="3.5" xlink:href="#terminal" y="29.85"/>
   <line fill="none" stroke="rgb(170,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.5" x2="3.5" y1="0.1000000000000032" y2="29.76666666666667"/>
  </symbol>
  <symbol id="State:全站检修_0" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(85,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.07500000286102">全站检修:否</text>
  </symbol>
  <symbol id="State:全站检修_1" viewBox="0,0,90,30">
   <text Plane="0" fill="none" font-family="微软雅黑" font-size="21" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="3" text-anchor="middle" x="45.07500000286102" xml:space="preserve" y="21.30000001144409">全站检修:是</text>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="110kV南宛河二级电站" InitShowingPlane="" fill="rgb(0,0,0)" height="1056" width="1920" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="OtherClass">
  <image height="64" id="1" preserveAspectRatio="xMidYMid slice" width="298" x="49.21" xlink:href="logo.png" y="34.57"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="102" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,198.214,66.5714) scale(1,1) translate(0,0)" writing-mode="lr" x="198.21" xml:space="preserve" y="70.06999999999999" zvalue="4"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="2" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,195.714,66.2618) scale(1,1) translate(0,0)" writing-mode="lr" x="195.71" xml:space="preserve" y="75.26000000000001" zvalue="5">110kV南宛河二级电站</text>
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="203" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" width="72.88" x="50.19" y="175.25" zvalue="361"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,86.625,187.25) scale(1,1) translate(0,0)" writing-mode="lr" x="86.63" xml:space="preserve" y="191.75" zvalue="361">信号一览</text>
  <line fill="none" id="100" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="384.2142857142856" x2="384.2142857142856" y1="15.57142857142867" y2="1045.571428571429" zvalue="6"/>
  <line fill="none" id="98" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9.214285714286234" x2="377.2142857142858" y1="151.441921185511" y2="151.441921185511" zvalue="8"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="249" y2="249"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="249" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="275" y2="275"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="275" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="299.25" y2="299.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="299.25" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="322" y2="322"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="322" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="344.75" y2="344.75"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="344.75" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="367.5" y2="367.5"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="367.5" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="194" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="13" x2="13" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="390.25" y2="390.25"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="375" y1="413" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="194" x2="194" y1="390.25" y2="413"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="375" x2="375" y1="390.25" y2="413"/>
  <line fill="none" id="268" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.00000000000045" x2="379" y1="494.8704926140824" y2="494.8704926140824" zvalue="335"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="934" y2="934"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="372" y1="973.1632999999999" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="934" y2="973.1632999999999"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="973.16327" y2="973.16327"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.08167" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="973.16327" y2="1001.08167"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="102" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="12" x2="12" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="192" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="102" x2="102" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192" x2="192" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="282.0000000000001" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="192.0000000000001" x2="192.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282.0000000000001" x2="282.0000000000001" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1001.0816" y2="1001.0816"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="372" y1="1029" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="282" x2="282" y1="1001.0816" y2="1029"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="372" x2="372" y1="1001.0816" y2="1029"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="266" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,57,954) scale(1,1) translate(0,0)" writing-mode="lr" x="57" xml:space="preserve" y="960" zvalue="337">参考图号</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="265" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54,988) scale(1,1) translate(0,0)" writing-mode="lr" x="54" xml:space="preserve" y="994" zvalue="338">制图</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="264" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,236,988) scale(1,1) translate(0,0)" writing-mode="lr" x="236" xml:space="preserve" y="994" zvalue="339">绘制日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="263" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,53,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="53" xml:space="preserve" y="1022" zvalue="340">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="262" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,235,1016) scale(1,1) translate(0,0)" writing-mode="lr" x="235" xml:space="preserve" y="1022" zvalue="341">更新日期</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="260" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,77.5,568.5) scale(1,1) translate(0,0)" writing-mode="lr" x="77.5" xml:space="preserve" y="573" zvalue="343">危险点(双击编辑）：</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="259" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,202.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="202.4" xml:space="preserve" y="192.34" zvalue="344">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="258" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,307.399,187.841) scale(1,1) translate(0,0)" writing-mode="lr" x="307.4" xml:space="preserve" y="192.34" zvalue="345">通道</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="254" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,147.054,988) scale(1,1) translate(0,0)" writing-mode="lr" x="147.05" xml:space="preserve" y="994" zvalue="347">李文杰</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="16" id="253" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,327.054,988) scale(1,1) translate(0,0)" writing-mode="lr" x="327.05" xml:space="preserve" y="994" zvalue="348">20210922</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="252" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,59.5,262) scale(1,1) translate(0,0)" writing-mode="lr" x="17" xml:space="preserve" y="266.5" zvalue="349">发电机总有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="251" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,240,262) scale(1,1) translate(0,0)" writing-mode="lr" x="197.5" xml:space="preserve" y="266.5" zvalue="350">发电机总无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="249" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,63.2361,336.361) scale(1,1) translate(0,0)" writing-mode="lr" x="63.24" xml:space="preserve" y="340.86" zvalue="352">10.5kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="202" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,288) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="292.5" zvalue="362">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="200" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,239,288) scale(1,1) translate(0,0)" writing-mode="lr" x="196.5" xml:space="preserve" y="292.5" zvalue="363">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="195" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,381.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="385.75" zvalue="366">坝上水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="193" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,380.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="384.75" zvalue="368">降雨量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="190" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,59.6875,404.25) scale(1,1) translate(0,0)" writing-mode="lr" x="59.69" xml:space="preserve" y="408.75" zvalue="370">坝下水位</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="187" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,227.688,403.25) scale(1,1) translate(0,0)" writing-mode="lr" x="227.69" xml:space="preserve" y="407.75" zvalue="372">入库流量</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="184" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,58.5,311) scale(1,1) translate(0,0)" writing-mode="lr" x="16" xml:space="preserve" y="315.5" zvalue="374">装机利用率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="182" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,238.5,310) scale(1,1) translate(0,0)" writing-mode="lr" x="196" xml:space="preserve" y="314.5" zvalue="376">厂用电率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="2" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,859.713,462) scale(1,1) translate(0,0)" writing-mode="lr" x="859.71" xml:space="preserve" y="466.5" zvalue="379">#1主变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,865.5,486.75) scale(1,1) translate(0,0)" writing-mode="lr" x="865.5" xml:space="preserve" y="491.25" zvalue="379">31.5MVA</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,551,614) scale(1,1) translate(0,0)" writing-mode="lr" x="551" xml:space="preserve" y="618.5" zvalue="383">10.5kV母线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,812.875,380) scale(1,1) translate(0,0)" writing-mode="lr" x="812.88" xml:space="preserve" y="384.5" zvalue="384">161</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="27" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,791,183) scale(1,1) translate(0,0)" writing-mode="lr" x="791" xml:space="preserve" y="187.5" zvalue="402">110kV南别线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="49" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,755,898) scale(1,1) translate(0,0)" writing-mode="lr" x="755" xml:space="preserve" y="902.5" zvalue="419">#1发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="50" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,755.5,923.25) scale(1,1) translate(0,0)" writing-mode="lr" x="755.5" xml:space="preserve" y="927.75" zvalue="420">10MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="12" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,785.073,693.585) scale(1,1) translate(0,0)" writing-mode="lr" x="785.0700000000001" xml:space="preserve" y="698.09" zvalue="507">061</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="10" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,675.75,810.504) scale(1,1) translate(0,0)" writing-mode="lr" x="675.75" xml:space="preserve" y="815" zvalue="514">0911</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,856.215,809.504) scale(1,1) translate(0,0)" writing-mode="lr" x="856.21" xml:space="preserve" y="814" zvalue="518">0912</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,818.773,550.756) scale(1,1) translate(0,0)" writing-mode="lr" x="818.77" xml:space="preserve" y="555.26" zvalue="528">001</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="35" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,849,617) scale(1,1) translate(0,0)" writing-mode="lr" x="849" xml:space="preserve" y="621.5" zvalue="531">17</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,802.162,311) scale(1,1) translate(0,0)" writing-mode="lr" x="802.16" xml:space="preserve" y="315.5" zvalue="533">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,849.25,370) scale(1,1) translate(0,0)" writing-mode="lr" x="849.25" xml:space="preserve" y="374.5" zvalue="537">60</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="45" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,845,282) scale(1,1) translate(0,0)" writing-mode="lr" x="845" xml:space="preserve" y="286.5" zvalue="539">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,914,283) scale(1,1) translate(0,0)" writing-mode="lr" x="914" xml:space="preserve" y="287.5" zvalue="541">97</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="63" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,865.75,272) scale(1,1) translate(0,0)" writing-mode="lr" x="865.75" xml:space="preserve" y="276.5" zvalue="542">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,715,477) scale(1,1) translate(0,0)" writing-mode="lr" x="715" xml:space="preserve" y="481.5" zvalue="549">1010</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="84" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1539.71,462) scale(1,1) translate(0,0)" writing-mode="lr" x="1539.71" xml:space="preserve" y="466.5" zvalue="554">#1隔离变</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="83" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1493.44,380) scale(1,1) translate(0,0)" writing-mode="lr" x="1493.44" xml:space="preserve" y="384.5" zvalue="557">065</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="82" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1471,183) scale(1,1) translate(0,0)" writing-mode="lr" x="1471" xml:space="preserve" y="187.5" zvalue="560">10kV国南线</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="81" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1500.39,566.756) scale(1,1) translate(0,0)" writing-mode="lr" x="1500.39" xml:space="preserve" y="571.26" zvalue="562">064</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="165" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1529.75,537) scale(1,1) translate(0,0)" writing-mode="lr" x="1529.75" xml:space="preserve" y="541.5" zvalue="565">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="79" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1482.16,311) scale(1,1) translate(0,0)" writing-mode="lr" x="1482.16" xml:space="preserve" y="315.5" zvalue="567">6</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="75" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1517.75,272) scale(1,1) translate(0,0)" writing-mode="lr" x="1517.75" xml:space="preserve" y="276.5" zvalue="578">9</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="172" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,809.25,747) scale(1,1) translate(0,0)" writing-mode="lr" x="809.25" xml:space="preserve" y="751.5" zvalue="597">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="191" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1091,898) scale(1,1) translate(0,0)" writing-mode="lr" x="1091" xml:space="preserve" y="902.5" zvalue="606">#2发电机</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="215" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1091.5,923.25) scale(1,1) translate(0,0)" writing-mode="lr" x="1091.5" xml:space="preserve" y="927.75" zvalue="607">10MW</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="188" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1121.07,693.585) scale(1,1) translate(0,0)" writing-mode="lr" x="1121.07" xml:space="preserve" y="698.09" zvalue="609">062</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="185" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1013.62,810.504) scale(1,1) translate(0,0)" writing-mode="lr" x="1013.63" xml:space="preserve" y="815" zvalue="613">0921</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="180" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1193.86,809.504) scale(1,1) translate(0,0)" writing-mode="lr" x="1193.86" xml:space="preserve" y="814" zvalue="616">0922</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="179" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1145.25,747) scale(1,1) translate(0,0)" writing-mode="lr" x="1145.25" xml:space="preserve" y="751.5" zvalue="621">67</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="218" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1315.5,705.114) scale(1,1) translate(0,0)" writing-mode="lr" x="1315.5" xml:space="preserve" y="709.61" zvalue="629">0901</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="220" stroke="rgb(255,255,255)" text-anchor="middle" x="1295.1484375" xml:space="preserve" y="808" zvalue="631">10.5kV母线电压</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="220" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1295.1484375" xml:space="preserve" y="824" zvalue="631">互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="226" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1449.07,693.585) scale(1,1) translate(0,0)" writing-mode="lr" x="1449.07" xml:space="preserve" y="698.09" zvalue="638">063</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="225" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1473.25,747) scale(1,1) translate(0,0)" writing-mode="lr" x="1473.25" xml:space="preserve" y="751.5" zvalue="641">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="232" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1424.11,830) scale(1,1) translate(0,0)" writing-mode="lr" x="1424.11" xml:space="preserve" y="834.5" zvalue="644">#1站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1641.5,905.083) scale(1,1) translate(0,0)" writing-mode="lr" x="1641.5" xml:space="preserve" y="909.58" zvalue="653">#2站用变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="28" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1639.08,683) scale(1,1) translate(0,0)" writing-mode="lr" x="1639.08" xml:space="preserve" y="687.5" zvalue="658">10.5kV市电</text>
 </g>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="50.19" y="175.25" zvalue="361"/></g>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="MeasurementClass">
  <g id="244">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="244" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,262.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="267.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125046190085" ObjectName="F"/>
   </metadata>
  </g>
  <g id="245">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="245" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,263.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="268.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125046255621" ObjectName="F"/>
   </metadata>
  </g>
  <g id="246">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="246" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,158.333,336.278) scale(1,1) translate(0,0)" writing-mode="lr" x="158.49" xml:space="preserve" y="341.19" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125034983429" ObjectName="F"/>
   </metadata>
  </g>
  <g id="256">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="256" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,159.611,287.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="292.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125046059013" ObjectName="F"/>
   </metadata>
  </g>
  <g id="257">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="257" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,337.222,288.167) scale(1,1) translate(0,0)" writing-mode="lr" x="337.38" xml:space="preserve" y="293.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125046124549" ObjectName="F"/>
   </metadata>
  </g>
  <g id="194">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="194" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126659227652" ObjectName="坝上水位"/>
   </metadata>
  </g>
  <g id="192">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="192" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,337.611,379.389) scale(1,1) translate(0,0)" writing-mode="lr" x="337.77" xml:space="preserve" y="384.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126659358724" ObjectName="雨量采集"/>
   </metadata>
  </g>
  <g id="189">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="189" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,159.611,402.389) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="407.3" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481126659293188" ObjectName="坝下水位"/>
   </metadata>
  </g>
  <g id="183">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="183" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,159.611,311.167) scale(1,1) translate(0,0)" writing-mode="lr" x="159.77" xml:space="preserve" y="316.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127187841029" ObjectName="装机利用率"/>
   </metadata>
  </g>
  <g id="181">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="181" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,339.611,310.167) scale(1,1) translate(0,0)" writing-mode="lr" x="339.77" xml:space="preserve" y="315.08" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481127187775493" ObjectName="厂用电率"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="52" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,787,104.75) scale(1,1) translate(0,0)" writing-mode="lr" x="786.53" xml:space="preserve" y="109.4" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125035638789" ObjectName="P"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="53" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1464,103.125) scale(1,1) translate(0,0)" writing-mode="lr" x="1463.53" xml:space="preserve" y="107.81" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125038784517" ObjectName="P"/>
   </metadata>
  </g>
  <g id="55">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="55" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,788,126) scale(1,1) translate(0,0)" writing-mode="lr" x="787.53" xml:space="preserve" y="130.66" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125035704325" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="56">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="56" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1464,123.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1463.53" xml:space="preserve" y="128.54" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125038850053" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="58">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="58" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,788,145.625) scale(1,1) translate(0,0)" writing-mode="lr" x="787.53" xml:space="preserve" y="150.29" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125035769861" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="59">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="59" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1464,146.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1463.53" xml:space="preserve" y="151.31" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125038915589" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="62" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,953.046,347.5) scale(1,1) translate(0,0)" writing-mode="lr" x="952.49" xml:space="preserve" y="353.68" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125032558597" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="74">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="74" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,953.046,371.5) scale(1,1) translate(0,0)" writing-mode="lr" x="952.49" xml:space="preserve" y="377.68" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125032624133" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="76">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="76" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,953.046,534.5) scale(1,1) translate(0,0)" writing-mode="lr" x="952.49" xml:space="preserve" y="540.6799999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125032689669" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="77">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="77" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,953.046,555.5) scale(1,1) translate(0,0)" writing-mode="lr" x="952.49" xml:space="preserve" y="561.6799999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125032755205" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="78">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="78" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,953.046,395.5) scale(1,1) translate(0,0)" writing-mode="lr" x="952.49" xml:space="preserve" y="401.68" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125032820741" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="85">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="85" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,953.046,576.5) scale(1,1) translate(0,0)" writing-mode="lr" x="952.49" xml:space="preserve" y="582.6799999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125033148421" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="86">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="86" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1619.05,347.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1618.49" xml:space="preserve" y="353.68" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125040226309" ObjectName="HP"/>
   </metadata>
  </g>
  <g id="87">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="87" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1619.05,371.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1618.49" xml:space="preserve" y="377.68" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125040291845" ObjectName="HQ"/>
   </metadata>
  </g>
  <g id="88">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="16" id="88" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,1619.05,534.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1618.49" xml:space="preserve" y="540.6799999999999" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125040357381" ObjectName="LP"/>
   </metadata>
  </g>
  <g id="89">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="89" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,1619.05,555.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1618.49" xml:space="preserve" y="561.6799999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125040422917" ObjectName="LQ"/>
   </metadata>
  </g>
  <g id="90">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="90" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1619.05,395.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1618.49" xml:space="preserve" y="401.68" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125040488453" ObjectName="HIa"/>
   </metadata>
  </g>
  <g id="91">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="91" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1619.05,576.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1618.49" xml:space="preserve" y="582.6799999999999" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125040816133" ObjectName="LIa"/>
   </metadata>
  </g>
  <g id="92">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="92" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,747.498,952.75) scale(1,1) translate(-1.5421e-13,0)" writing-mode="lr" x="746.95" xml:space="preserve" y="958.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125036556293" ObjectName="P"/>
   </metadata>
  </g>
  <g id="93">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="93" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,747.498,976.875) scale(1,1) translate(-1.5421e-13,0)" writing-mode="lr" x="746.95" xml:space="preserve" y="983.0599999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125036621829" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="94">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="94" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,747.498,1001) scale(1,1) translate(-1.5421e-13,0)" writing-mode="lr" x="746.95" xml:space="preserve" y="1007.19" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125036687365" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="95">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="95" prefix="P:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1083.5,952.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1082.95" xml:space="preserve" y="958.9400000000001" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125042782213" ObjectName="P"/>
   </metadata>
  </g>
  <g id="96">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="96" prefix="Q:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1083.5,976.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1082.95" xml:space="preserve" y="983.0599999999999" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125042847749" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="97">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="97" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1083.5,1001) scale(1,1) translate(0,0)" writing-mode="lr" x="1082.95" xml:space="preserve" y="1007.19" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125042913285" ObjectName="Ia"/>
   </metadata>
  </g>
  <g id="103">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="103" prefix="Ua:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1292,849) scale(1,1) translate(0,0)" writing-mode="lr" x="1291.53" xml:space="preserve" y="853.66" zvalue="1">Ua:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125034590213" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="104">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="104" prefix="Ub:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1292,869.875) scale(1,1) translate(0,0)" writing-mode="lr" x="1291.53" xml:space="preserve" y="874.53" zvalue="1">Ub:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125034655749" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="105">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="105" prefix="Uc:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1292,890.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1291.53" xml:space="preserve" y="895.41" zvalue="1">Uc:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125034721285" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="106">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="106" prefix="Uab:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1292,911.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1291.53" xml:space="preserve" y="916.28" zvalue="1">Uab:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125034852357" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="107">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="107" prefix="F:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,1292,932.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1291.53" xml:space="preserve" y="937.16" zvalue="1">F:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125034983429" ObjectName="F"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="211">
   <use height="30" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="359"/>
   <metadata>
    <cge:Meas_Ref ObjectID="1407374889582595" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,334.673,188.357) scale(0.708333,0.665547) translate(133.431,89.6372)" width="30" x="324.05" y="178.37"/></g>
  <g id="732">
   <use height="30" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" xlink:href="#State:红绿圆(方形)_0" y="178.37" zvalue="360"/>
   <metadata>
    <cge:Meas_Ref ObjectID="562951093485573" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,239.048,188.357) scale(0.708333,0.665547) translate(94.0564,89.6372)" width="30" x="228.42" y="178.37"/></g>
  <g id="830">
   <use height="30" transform="rotate(0,319.812,129.464) scale(1.27778,1.03333) translate(-57.0245,-3.67625)" width="90" x="262.31" xlink:href="#State:全站检修_0" y="113.96" zvalue="694"/>
   <metadata>
    <cge:Meas_Ref ObjectID="5066549585379330" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,319.812,129.464) scale(1.27778,1.03333) translate(-57.0245,-3.67625)" width="90" x="262.31" y="113.96"/></g>
 </g>
 <g id="PowerTransformer2Class">
  <g id="1">
   <g id="10">
    <use class="kv110" height="50" transform="rotate(0,792.046,466) scale(1.82222,1.64) translate(-345.053,-165.854)" width="30" x="764.71" xlink:href="#PowerTransformer2:Y-D_0" y="425" zvalue="378"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874433167364" ObjectName="110"/>
    </metadata>
   </g>
   <g id="11">
    <use class="v10500" height="50" transform="rotate(0,792.046,466) scale(1.82222,1.64) translate(-345.053,-165.854)" width="30" x="764.71" xlink:href="#PowerTransformer2:Y-D_1" y="425" zvalue="378"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874433232900" ObjectName="10.5"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399448854532" ObjectName="#1主变"/>
   <cge:TPSR_Ref TObjectID="6755399448854532"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,792.046,466) scale(1.82222,1.64) translate(-345.053,-165.854)" width="30" x="764.71" y="425"/></g>
  <g id="163">
   <g id="1630">
    <use class="v10500" height="50" transform="rotate(0,1472.05,466) scale(1.82222,-1.64) translate(-651.883,-734.146)" width="30" x="1444.71" xlink:href="#PowerTransformer2:586_0" y="425" zvalue="553"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874433298436" ObjectName="10.5"/>
    </metadata>
   </g>
   <g id="1631">
    <use class="kv10" height="50" transform="rotate(0,1472.05,466) scale(1.82222,-1.64) translate(-651.883,-734.146)" width="30" x="1444.71" xlink:href="#PowerTransformer2:586_1" y="425" zvalue="553"/>
    <metadata>
     <cge:PSR_Ref ObjectID="7036874433363972" ObjectName="10"/>
    </metadata>
   </g>
   <metadata>
    <cge:PSR_Ref ObjectID="6755399448920068" ObjectName="#1隔离变"/>
   <cge:TPSR_Ref TObjectID="6755399448920068"/></metadata>
  <rect fill="white" height="50" opacity="0" stroke="white" transform="rotate(0,1472.05,466) scale(1.82222,-1.64) translate(-651.883,-734.146)" width="30" x="1444.71" y="425"/></g>
 </g>
 <g id="BusbarSectionClass">
  <g id="6">
   <path class="v10500" d="M 539 636 L 1706 636" stroke-width="6" zvalue="382"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674241347588" ObjectName="10.5kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674241347588"/></metadata>
  <path d="M 539 636 L 1706 636" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="BreakerClass">
  <g id="8">
   <use class="kv110" height="20" transform="rotate(0,792,381) scale(1.5,1.35) translate(-261.5,-95.2778)" width="10" x="784.5" xlink:href="#Breaker:开关_0" y="367.5" zvalue="383"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924504256517" ObjectName="110kV南别线161断路器"/>
   <cge:TPSR_Ref TObjectID="6473924504256517"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,792,381) scale(1.5,1.35) translate(-261.5,-95.2778)" width="10" x="784.5" y="367.5"/></g>
  <g id="157">
   <use class="v10500" height="20" transform="rotate(0,755.498,694.673) scale(2.17559,2.17559) translate(-402.358,-363.613)" width="10" x="744.6201573688402" xlink:href="#Breaker:小车断路器_0" y="672.9173635942258" zvalue="506"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924504322053" ObjectName="#1发电机061断路器"/>
   <cge:TPSR_Ref TObjectID="6473924504322053"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,755.498,694.673) scale(2.17559,2.17559) translate(-402.358,-363.613)" width="10" x="744.6201573688402" y="672.9173635942258"/></g>
  <g id="30">
   <use class="v10500" height="20" transform="rotate(0,792.046,551.756) scale(2.17559,2.17559) translate(-422.107,-286.387)" width="10" x="781.1680771806472" xlink:href="#Breaker:小车断路器_0" y="530" zvalue="527"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924504387589" ObjectName="#1主变10.5kV侧001断路器"/>
   <cge:TPSR_Ref TObjectID="6473924504387589"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,792.046,551.756) scale(2.17559,2.17559) translate(-422.107,-286.387)" width="10" x="781.1680771806472" y="530"/></g>
  <g id="161">
   <use class="kv10" height="20" transform="rotate(0,1472,381) scale(1.5,1.35) translate(-488.167,-95.2778)" width="10" x="1464.5" xlink:href="#Breaker:开关_0" y="367.5" zvalue="556"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924504518661" ObjectName="10kV国南线065断路器"/>
   <cge:TPSR_Ref TObjectID="6473924504518661"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1472,381) scale(1.5,1.35) translate(-488.167,-95.2778)" width="10" x="1464.5" y="367.5"/></g>
  <g id="158">
   <use class="v10500" height="20" transform="rotate(0,1472.05,567.756) scale(2.17559,2.17559) translate(-789.548,-295.033)" width="10" x="1461.168077180647" xlink:href="#Breaker:小车断路器_0" y="546" zvalue="561"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924504453125" ObjectName="#1隔离变10.5kV侧064断路器"/>
   <cge:TPSR_Ref TObjectID="6473924504453125"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1472.05,567.756) scale(2.17559,2.17559) translate(-789.548,-295.033)" width="10" x="1461.168077180647" y="546"/></g>
  <g id="214">
   <use class="v10500" height="20" transform="rotate(0,1091.5,694.673) scale(2.17559,2.17559) translate(-583.917,-363.613)" width="10" x="1080.62015736884" xlink:href="#Breaker:小车断路器_0" y="672.9173635942258" zvalue="608"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924504584197" ObjectName="#2发电机062断路器"/>
   <cge:TPSR_Ref TObjectID="6473924504584197"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1091.5,694.673) scale(2.17559,2.17559) translate(-583.917,-363.613)" width="10" x="1080.62015736884" y="672.9173635942258"/></g>
  <g id="230">
   <use class="v10500" height="20" transform="rotate(0,1419.5,694.673) scale(2.17559,2.17559) translate(-761.153,-363.613)" width="10" x="1408.62015736884" xlink:href="#Breaker:小车断路器_0" y="672.9173635942258" zvalue="637"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924504649733" ObjectName="#1站用变063断路器"/>
   <cge:TPSR_Ref TObjectID="6473924504649733"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1419.5,694.673) scale(2.17559,2.17559) translate(-761.153,-363.613)" width="10" x="1408.62015736884" y="672.9173635942258"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="14">
   <path class="kv110" d="M 792.1 393.89 L 792.11 425.74" stroke-width="1" zvalue="387"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="8@1" LinkObjectIDznd="1@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 792.1 393.89 L 792.11 425.74" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="23">
   <path class="v10500" d="M 652.69 820.62 L 652.69 832.29" stroke-width="1" zvalue="516"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="80@1" LinkObjectIDznd="29@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 652.69 820.62 L 652.69 832.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="20">
   <path class="v10500" d="M 825.92 820.62 L 825.92 825.39" stroke-width="1" zvalue="520"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@1" LinkObjectIDznd="25@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.92 820.62 L 825.92 825.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="32">
   <path class="v10500" d="M 792.05 636 L 792.05 571.34" stroke-width="1" zvalue="528"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="6@0" LinkObjectIDznd="30@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 792.05 636 L 792.05 571.34" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="33">
   <path class="v10500" d="M 792.05 531.63 L 792.05 506.42" stroke-width="1" zvalue="529"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="30@0" LinkObjectIDznd="1@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 792.05 531.63 L 792.05 506.42" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="36">
   <path class="v10500" d="M 838.25 595.95 L 792.05 595.95" stroke-width="1" zvalue="531"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="34@0" LinkObjectIDznd="32" MaxPinNum="2"/>
   </metadata>
  <path d="M 838.25 595.95 L 792.05 595.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="39">
   <path class="kv110" d="M 791 238.32 L 791 301.36" stroke-width="1" zvalue="533"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="26@0" LinkObjectIDznd="37@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 791 238.32 L 791 301.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="40">
   <path class="kv110" d="M 790.97 322.81 L 790.97 368.09" stroke-width="1" zvalue="534"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="37@1" LinkObjectIDznd="8@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 790.97 322.81 L 790.97 368.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="43">
   <path class="kv110" d="M 838.25 351.95 L 790.97 351.95" stroke-width="1" zvalue="537"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="41@0" LinkObjectIDznd="40" MaxPinNum="2"/>
   </metadata>
  <path d="M 838.25 351.95 L 790.97 351.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="64">
   <path class="kv110" d="M 856.36 249.91 L 791 249.91" stroke-width="1" zvalue="542"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@0" LinkObjectIDznd="39" MaxPinNum="2"/>
   </metadata>
  <path d="M 856.36 249.91 L 791 249.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv110" d="M 831.05 273.25 L 831.05 249.91" stroke-width="1" zvalue="543"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="44@0" LinkObjectIDznd="64" MaxPinNum="2"/>
   </metadata>
  <path d="M 831.05 273.25 L 831.05 249.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="67">
   <path class="kv110" d="M 877.81 249.94 L 912.88 249.94" stroke-width="1" zvalue="545"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="60@1" LinkObjectIDznd="66@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 877.81 249.94 L 912.88 249.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="68">
   <path class="kv110" d="M 898.05 273.27 L 898.05 249.94" stroke-width="1" zvalue="546"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="46@0" LinkObjectIDznd="67" MaxPinNum="2"/>
   </metadata>
  <path d="M 898.05 273.27 L 898.05 249.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="72">
   <path class="kv110" d="M 792.08 444.71 L 652.03 444.71 L 652.03 464.63" stroke-width="1" zvalue="550"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="1@2" LinkObjectIDznd="71@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 792.08 444.71 L 652.03 444.71 L 652.03 464.63" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="73">
   <path class="kv110" d="M 695.05 467.28 L 695.05 444.71" stroke-width="1" zvalue="551"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="69@0" LinkObjectIDznd="72" MaxPinNum="2"/>
   </metadata>
  <path d="M 695.05 467.28 L 695.05 444.71" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="160">
   <path class="kv10" d="M 1472.1 393.89 L 1472.05 425.58" stroke-width="1" zvalue="558"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="161@1" LinkObjectIDznd="163@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1472.1 393.89 L 1472.05 425.58" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="v10500" d="M 1472.05 547.63 L 1472.11 506.26" stroke-width="1" zvalue="563"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="163@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1472.05 547.63 L 1472.11 506.26" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv10" d="M 1471 238.32 L 1471 301.36" stroke-width="1" zvalue="568"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="153@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1471 238.32 L 1471 301.36" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="150">
   <path class="kv10" d="M 1470.97 322.81 L 1470.97 368.09" stroke-width="1" zvalue="569"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="153@1" LinkObjectIDznd="161@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1470.97 322.81 L 1470.97 368.09" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="144">
   <path class="kv10" d="M 1508.36 249.91 L 1471 249.91" stroke-width="1" zvalue="579"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="152" MaxPinNum="2"/>
   </metadata>
  <path d="M 1508.36 249.91 L 1471 249.91" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="141">
   <path class="kv10" d="M 1529.81 249.94 L 1552.88 249.94" stroke-width="1" zvalue="582"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@1" LinkObjectIDznd="142@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1529.81 249.94 L 1552.88 249.94" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="166">
   <path class="v10500" d="M 1518.25 519.95 L 1472.09 519.95" stroke-width="1" zvalue="590"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="156" MaxPinNum="2"/>
   </metadata>
  <path d="M 1518.25 519.95 L 1472.09 519.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="168">
   <path class="v10500" d="M 1420.37 519.94 L 1472.09 519.95" stroke-width="1" zvalue="592"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="138@0" LinkObjectIDznd="156" MaxPinNum="2"/>
   </metadata>
  <path d="M 1420.37 519.94 L 1472.09 519.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="169">
   <path class="v10500" d="M 755.5 674.55 L 755.5 636" stroke-width="1" zvalue="593"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@0" LinkObjectIDznd="6@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.5 674.55 L 755.5 636" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="170">
   <path class="v10500" d="M 755.5 714.25 L 755.5 827.88" stroke-width="1" zvalue="594"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="157@1" LinkObjectIDznd="48@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.5 714.25 L 755.5 827.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="173">
   <path class="v10500" d="M 796.25 729.95 L 755.5 729.95" stroke-width="1" zvalue="597"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="171@0" LinkObjectIDznd="170" MaxPinNum="2"/>
   </metadata>
  <path d="M 796.25 729.95 L 755.5 729.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="175">
   <path class="v10500" d="M 825.92 790.18 L 825.92 772 L 755.5 772" stroke-width="1" zvalue="600"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="22@0" LinkObjectIDznd="170" MaxPinNum="2"/>
   </metadata>
  <path d="M 825.92 790.18 L 825.92 772 L 755.5 772" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="176">
   <path class="v10500" d="M 755.5 772 L 652.69 772 L 652.69 790.18" stroke-width="1" zvalue="601"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="175" LinkObjectIDznd="80@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 755.5 772 L 652.69 772 L 652.69 790.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="177">
   <path class="v10500" d="M 714.03 795.63 L 714.03 772" stroke-width="1" zvalue="602"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="174@0" LinkObjectIDznd="176" MaxPinNum="2"/>
   </metadata>
  <path d="M 714.03 795.63 L 714.03 772" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="178">
   <path class="v10500" d="M 1472.05 587.34 L 1472.05 636" stroke-width="1" zvalue="603"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@1" LinkObjectIDznd="6@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1472.05 587.34 L 1472.05 636" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="209">
   <path class="v10500" d="M 988.69 820.62 L 988.69 832.29" stroke-width="1" zvalue="614"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="210@1" LinkObjectIDznd="213@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 988.69 820.62 L 988.69 832.29" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="207">
   <path class="v10500" d="M 1161.92 820.62 L 1161.92 825.39" stroke-width="1" zvalue="617"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@1" LinkObjectIDznd="212@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1161.92 820.62 L 1161.92 825.39" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="206">
   <path class="v10500" d="M 1091.5 674.55 L 1091.5 636" stroke-width="1" zvalue="618"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@0" LinkObjectIDznd="6@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 1091.5 674.55 L 1091.5 636" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="205">
   <path class="v10500" d="M 1091.5 714.25 L 1091.5 827.88" stroke-width="1" zvalue="619"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="214@1" LinkObjectIDznd="216@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1091.5 714.25 L 1091.5 827.88" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="201">
   <path class="v10500" d="M 1132.25 729.95 L 1091.5 729.95" stroke-width="1" zvalue="622"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="204@0" LinkObjectIDznd="205" MaxPinNum="2"/>
   </metadata>
  <path d="M 1132.25 729.95 L 1091.5 729.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="198">
   <path class="v10500" d="M 1161.92 790.18 L 1161.92 772 L 1091.5 772" stroke-width="1" zvalue="624"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="208@0" LinkObjectIDznd="205" MaxPinNum="2"/>
   </metadata>
  <path d="M 1161.92 790.18 L 1161.92 772 L 1091.5 772" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="197">
   <path class="v10500" d="M 1091.5 772 L 988.69 772 L 988.69 790.18" stroke-width="1" zvalue="625"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="198" LinkObjectIDznd="210@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1091.5 772 L 988.69 772 L 988.69 790.18" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="196">
   <path class="v10500" d="M 1050.03 795.63 L 1050.03 772" stroke-width="1" zvalue="626"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="199@0" LinkObjectIDznd="197" MaxPinNum="2"/>
   </metadata>
  <path d="M 1050.03 795.63 L 1050.03 772" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="222">
   <path class="v10500" d="M 1291.27 688.9 L 1291.27 636" stroke-width="1" zvalue="633"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217@0" LinkObjectIDznd="6@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1291.27 688.9 L 1291.27 636" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="223">
   <path class="v10500" d="M 1291.27 719.33 L 1291.27 757.16" stroke-width="1" zvalue="634"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="217@1" LinkObjectIDznd="219@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1291.27 719.33 L 1291.27 757.16" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="224">
   <path class="v10500" d="M 1261.03 673.63 L 1261.03 663 L 1291.27 663" stroke-width="1" zvalue="635"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="221@0" LinkObjectIDznd="222" MaxPinNum="2"/>
   </metadata>
  <path d="M 1261.03 673.63 L 1261.03 663 L 1291.27 663" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="233">
   <path class="v10500" d="M 1419.5 714.25 L 1419.5 769.6" stroke-width="1" zvalue="644"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@1" LinkObjectIDznd="231@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1419.5 714.25 L 1419.5 769.6" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="234">
   <path class="v10500" d="M 1460.25 729.95 L 1419.5 729.95" stroke-width="1" zvalue="645"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="228@0" LinkObjectIDznd="233" MaxPinNum="2"/>
   </metadata>
  <path d="M 1460.25 729.95 L 1419.5 729.95" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="235">
   <path class="v10500" d="M 1419.5 674.55 L 1419.5 636" stroke-width="1" zvalue="646"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="230@0" LinkObjectIDznd="6@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1419.5 674.55 L 1419.5 636" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="16">
   <path class="v10500" d="M 1636.92 792.25 L 1636.89 844.69" stroke-width="1" zvalue="654"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="21@1" LinkObjectIDznd="17@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1636.92 792.25 L 1636.89 844.69" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="v10500" d="M 1637.08 739.65 L 1637.08 766.75" stroke-width="1" zvalue="658"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="24@0" LinkObjectIDznd="21@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 1637.08 739.65 L 1637.08 766.75" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="GeneratorClass">
  <g id="48">
   <use class="v10500" height="30" transform="rotate(0,755.498,850) scale(1.5,1.5) translate(-244.333,-275.833)" width="30" x="732.998088416586" xlink:href="#Generator:发电机_0" y="827.5" zvalue="418"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449765703686" ObjectName="#1发电机"/>
   <cge:TPSR_Ref TObjectID="6192449765703686"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,755.498,850) scale(1.5,1.5) translate(-244.333,-275.833)" width="30" x="732.998088416586" y="827.5"/></g>
  <g id="216">
   <use class="v10500" height="30" transform="rotate(0,1091.5,850) scale(1.5,1.5) translate(-356.333,-275.833)" width="30" x="1068.998088416586" xlink:href="#Generator:发电机_0" y="827.5" zvalue="605"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449768062982" ObjectName="#2发电机"/>
   <cge:TPSR_Ref TObjectID="6192449768062982"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1091.5,850) scale(1.5,1.5) translate(-356.333,-275.833)" width="30" x="1068.998088416586" y="827.5"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="29">
   <use class="v10500" height="18" transform="rotate(0,654.71,847.453) scale(2.17559,1.81299) translate(-344.958,-372.702)" width="15" x="638.3930949600175" xlink:href="#Accessory:PT8_0" y="831.1358584514667" zvalue="510"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449765965830" ObjectName="#1发电机PT1"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,654.71,847.453) scale(2.17559,1.81299) translate(-344.958,-372.702)" width="15" x="638.3930949600175" y="831.1358584514667"/></g>
  <g id="25">
   <use class="v10500" height="29" transform="rotate(0,825.917,840.891) scale(1.08779,-1.08779) translate(-65.3408,-1612.64)" width="30" x="809.5996989327974" xlink:href="#Accessory:PT12321_0" y="825.1180015592805" zvalue="512"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449765900294" ObjectName="#1发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,825.917,840.891) scale(1.08779,-1.08779) translate(-65.3408,-1612.64)" width="30" x="809.5996989327974" y="825.1180015592805"/></g>
  <g id="66">
   <use class="kv110" height="40" transform="rotate(270,939,236.189) scale(1.375,1.375) translate(-248.591,-56.9151)" width="40" x="911.5" xlink:href="#Accessory:五卷PT_0" y="208.6888355758942" zvalue="544"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449766686726" ObjectName="110kV南别线线路PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,939,236.189) scale(1.375,1.375) translate(-248.591,-56.9151)" width="40" x="911.5" y="208.6888355758942"/></g>
  <g id="71">
   <use class="kv110" height="26" transform="rotate(0,652,477) scale(1,1) translate(0,0)" width="12" x="646" xlink:href="#Accessory:避雷器1_0" y="464" zvalue="549"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449766883334" ObjectName="#1主变中性点避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,652,477) scale(1,1) translate(0,0)" width="12" x="646" y="464"/></g>
  <g id="142">
   <use class="kv10" height="40" transform="rotate(270,1579,236.189) scale(1.375,1.375) translate(-423.136,-56.9151)" width="40" x="1551.5" xlink:href="#Accessory:五卷PT_0" y="208.6888355758942" zvalue="581"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449767014406" ObjectName="10kV国南线线路PT"/>
   </metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(270,1579,236.189) scale(1.375,1.375) translate(-423.136,-56.9151)" width="40" x="1551.5" y="208.6888355758942"/></g>
  <g id="138">
   <use class="v10500" height="26" transform="rotate(90,1408,519.911) scale(1,1) translate(0,0)" width="12" x="1402" xlink:href="#Accessory:避雷器1_0" y="506.9105406023799" zvalue="585"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449766948870" ObjectName="#1隔离变10kV侧避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(90,1408,519.911) scale(1,1) translate(0,0)" width="12" x="1402" y="506.9105406023799"/></g>
  <g id="174">
   <use class="v10500" height="26" transform="rotate(0,714,808) scale(1,1) translate(0,0)" width="12" x="708" xlink:href="#Accessory:避雷器1_0" y="795" zvalue="599"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449767538694" ObjectName="#1发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,714,808) scale(1,1) translate(0,0)" width="12" x="708" y="795"/></g>
  <g id="213">
   <use class="v10500" height="18" transform="rotate(0,990.71,847.453) scale(2.17559,1.81299) translate(-526.517,-372.702)" width="15" x="974.3930949600175" xlink:href="#Accessory:PT8_0" y="831.1358584514667" zvalue="610"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449767997446" ObjectName="#2发电机PT1"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,990.71,847.453) scale(2.17559,1.81299) translate(-526.517,-372.702)" width="15" x="974.3930949600175" y="831.1358584514667"/></g>
  <g id="212">
   <use class="v10500" height="29" transform="rotate(0,1161.92,840.891) scale(1.08779,-1.08779) translate(-92.4585,-1612.64)" width="30" x="1145.599698932797" xlink:href="#Accessory:PT12321_0" y="825.1180015592805" zvalue="611"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449767931910" ObjectName="#2发电机PT3"/>
   </metadata>
  <rect fill="white" height="29" opacity="0" stroke="white" transform="rotate(0,1161.92,840.891) scale(1.08779,-1.08779) translate(-92.4585,-1612.64)" width="30" x="1145.599698932797" y="825.1180015592805"/></g>
  <g id="199">
   <use class="v10500" height="26" transform="rotate(0,1050,808) scale(1,1) translate(0,0)" width="12" x="1044" xlink:href="#Accessory:避雷器1_0" y="795" zvalue="623"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449767604230" ObjectName="#2发电机避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1050,808) scale(1,1) translate(0,0)" width="12" x="1044" y="795"/></g>
  <g id="219">
   <use class="v10500" height="18" transform="rotate(0,1292.32,772.317) scale(2.17559,1.81299) translate(-689.491,-339.009)" width="15" x="1276" xlink:href="#Accessory:PT8_0" y="756" zvalue="630"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449768194054" ObjectName="10.5kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="18" opacity="0" stroke="white" transform="rotate(0,1292.32,772.317) scale(2.17559,1.81299) translate(-689.491,-339.009)" width="15" x="1276" y="756"/></g>
  <g id="221">
   <use class="v10500" height="26" transform="rotate(0,1261,686) scale(1,1) translate(0,0)" width="12" x="1255" xlink:href="#Accessory:避雷器1_0" y="673" zvalue="632"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449768259590" ObjectName="10.5kV母线电压互感器避雷器"/>
   </metadata>
  <rect fill="white" height="26" opacity="0" stroke="white" transform="rotate(0,1261,686) scale(1,1) translate(0,0)" width="12" x="1255" y="673"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="80">
   <use class="v10500" height="36" transform="rotate(0,652.687,805.399) scale(0.895246,0.895246) translate(75.6384,92.3551)" width="14" x="646.4201537147731" xlink:href="#Disconnector:联体小车刀闸2_0" y="789.2843723313406" zvalue="513"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449765834758" ObjectName="#1发电机0911隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449765834758"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,652.687,805.399) scale(0.895246,0.895246) translate(75.6384,92.3551)" width="14" x="646.4201537147731" y="789.2843723313406"/></g>
  <g id="22">
   <use class="v10500" height="36" transform="rotate(0,825.917,805.399) scale(0.895246,0.895246) translate(95.9082,92.3551)" width="14" x="819.6498719043561" xlink:href="#Disconnector:联体小车刀闸2_0" y="789.2843723313406" zvalue="517"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449765769222" ObjectName="#1发电机0912隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449765769222"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,825.917,805.399) scale(0.895246,0.895246) translate(95.9082,92.3551)" width="14" x="819.6498719043561" y="789.2843723313406"/></g>
  <g id="37">
   <use class="kv110" height="30" transform="rotate(0,790.912,312) scale(1,0.733333) translate(0,109.455)" width="15" x="783.4122229491392" xlink:href="#Disconnector:刀闸_0" y="301" zvalue="532"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449766162438" ObjectName="110kV南别线1616隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449766162438"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,790.912,312) scale(1,0.733333) translate(0,109.455)" width="15" x="783.4122229491392" y="301"/></g>
  <g id="60">
   <use class="kv110" height="30" transform="rotate(270,867,250) scale(1,0.733333) translate(0,86.9091)" width="15" x="859.5" xlink:href="#Disconnector:刀闸_0" y="239" zvalue="541"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449766621190" ObjectName="110kV南别线1619隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449766621190"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,867,250) scale(1,0.733333) translate(0,86.9091)" width="15" x="859.5" y="239"/></g>
  <g id="153">
   <use class="kv10" height="30" transform="rotate(0,1470.91,312) scale(1,0.733333) translate(0,109.455)" width="15" x="1463.412222949139" xlink:href="#Disconnector:刀闸_0" y="301" zvalue="566"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449767145478" ObjectName="10kV国南线0656隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449767145478"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1470.91,312) scale(1,0.733333) translate(0,109.455)" width="15" x="1463.412222949139" y="301"/></g>
  <g id="145">
   <use class="kv10" height="30" transform="rotate(270,1519,250) scale(1,0.733333) translate(0,86.9091)" width="15" x="1511.5" xlink:href="#Disconnector:刀闸_0" y="239" zvalue="577"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449767079942" ObjectName="10kV国南线0659隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449767079942"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(270,1519,250) scale(1,0.733333) translate(0,86.9091)" width="15" x="1511.5" y="239"/></g>
  <g id="210">
   <use class="v10500" height="36" transform="rotate(0,988.687,805.399) scale(0.895246,0.895246) translate(114.954,92.3551)" width="14" x="982.4201537147731" xlink:href="#Disconnector:联体小车刀闸2_0" y="789.2843723313406" zvalue="612"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449767866374" ObjectName="#2发电机0921隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449767866374"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,988.687,805.399) scale(0.895246,0.895246) translate(114.954,92.3551)" width="14" x="982.4201537147731" y="789.2843723313406"/></g>
  <g id="208">
   <use class="v10500" height="36" transform="rotate(0,1161.92,805.399) scale(0.895246,0.895246) translate(135.224,92.3551)" width="14" x="1155.649871904356" xlink:href="#Disconnector:联体小车刀闸2_0" y="789.2843723313406" zvalue="615"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449767800838" ObjectName="#2发电机0922隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449767800838"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1161.92,805.399) scale(0.895246,0.895246) translate(135.224,92.3551)" width="14" x="1155.649871904356" y="789.2843723313406"/></g>
  <g id="217">
   <use class="v10500" height="36" transform="rotate(0,1291.27,704.114) scale(0.895246,0.895246) translate(150.359,80.5037)" width="14" x="1285" xlink:href="#Disconnector:联体小车刀闸2_0" y="688" zvalue="628"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449768128518" ObjectName="10.5kV母线电压互感器0901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449768128518"/></metadata>
  <rect fill="white" height="36" opacity="0" stroke="white" transform="rotate(0,1291.27,704.114) scale(0.895246,0.895246) translate(150.359,80.5037)" width="14" x="1285" y="688"/></g>
  <g id="21">
   <use class="v10500" height="30" transform="rotate(0,1637,780) scale(1,1) translate(0,0)" width="15" x="1629.5" xlink:href="#Disconnector:令克_0" y="765" zvalue="656"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449768587270" ObjectName="#2站用变跌落保险"/>
   <cge:TPSR_Ref TObjectID="6192449768587270"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1637,780) scale(1,1) translate(0,0)" width="15" x="1629.5" y="765"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="34">
   <use class="v10500" height="20" transform="rotate(270,848,596) scale(1,1) translate(0,0)" width="10" x="843" xlink:href="#GroundDisconnector:地刀_0" y="586" zvalue="530"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449766096902" ObjectName="#1主变10.5kV侧00117接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449766096902"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,848,596) scale(1,1) translate(0,0)" width="10" x="843" y="586"/></g>
  <g id="41">
   <use class="kv110" height="20" transform="rotate(270,848,352) scale(1,1) translate(0,0)" width="10" x="843" xlink:href="#GroundDisconnector:地刀_0" y="342" zvalue="536"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449766293510" ObjectName="110kV南别线16160接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449766293510"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,848,352) scale(1,1) translate(0,0)" width="10" x="843" y="342"/></g>
  <g id="44">
   <use class="kv110" height="20" transform="rotate(0,831,283) scale(1,1) translate(0,0)" width="10" x="826" xlink:href="#GroundDisconnector:地刀_0" y="273" zvalue="538"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449766424582" ObjectName="110kV南别线16167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449766424582"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,831,283) scale(1,1) translate(0,0)" width="10" x="826" y="273"/></g>
  <g id="46">
   <use class="kv110" height="20" transform="rotate(0,898,284) scale(1,1.1) translate(0,-24.8182)" width="10" x="893" xlink:href="#GroundDisconnector:地刀_0" y="273" zvalue="540"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449766555654" ObjectName="110kV南别线16197接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449766555654"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,898,284) scale(1,1.1) translate(0,-24.8182)" width="10" x="893" y="273"/></g>
  <g id="69">
   <use class="kv110" height="20" transform="rotate(0,695,478) scale(1,1.1) translate(0,-42.4545)" width="10" x="690" xlink:href="#GroundDisconnector:地刀_0" y="467" zvalue="548"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449766817798" ObjectName="#1主变110kV侧1010中性点接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449766817798"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,695,478) scale(1,1.1) translate(0,-42.4545)" width="10" x="690" y="467"/></g>
  <g id="155">
   <use class="v10500" height="20" transform="rotate(270,1528,520) scale(1,1) translate(0,0)" width="10" x="1523" xlink:href="#GroundDisconnector:地刀_0" y="510" zvalue="564"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449767276550" ObjectName="#1隔离变10.5kV侧06467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449767276550"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1528,520) scale(1,1) translate(0,0)" width="10" x="1523" y="510"/></g>
  <g id="171">
   <use class="v10500" height="20" transform="rotate(270,806,730) scale(1,1) translate(0,0)" width="10" x="801" xlink:href="#GroundDisconnector:地刀_0" y="720" zvalue="596"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449767473158" ObjectName="#1发电机06167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449767473158"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,806,730) scale(1,1) translate(0,0)" width="10" x="801" y="720"/></g>
  <g id="204">
   <use class="v10500" height="20" transform="rotate(270,1142,730) scale(1,1) translate(0,0)" width="10" x="1137" xlink:href="#GroundDisconnector:地刀_0" y="720" zvalue="620"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449767735302" ObjectName="#2发电机06267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449767735302"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1142,730) scale(1,1) translate(0,0)" width="10" x="1137" y="720"/></g>
  <g id="228">
   <use class="v10500" height="20" transform="rotate(270,1470,730) scale(1,1) translate(0,0)" width="10" x="1465" xlink:href="#GroundDisconnector:地刀_0" y="720" zvalue="640"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449768390662" ObjectName="#1站用变06367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449768390662"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(270,1470,730) scale(1,1) translate(0,0)" width="10" x="1465" y="720"/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="231">
   <use class="v10500" height="30" transform="rotate(0,1419.33,796.25) scale(1.83711,1.85) translate(-635.021,-353.095)" width="28" x="1393.609796336384" xlink:href="#EnergyConsumer:站用变DY接地_0" y="768.5" zvalue="643"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449768456198" ObjectName="#1站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1419.33,796.25) scale(1.83711,1.85) translate(-635.021,-353.095)" width="28" x="1393.609796336384" y="768.5"/></g>
  <g id="17">
   <use class="v10500" height="30" transform="rotate(0,1636.72,871.333) scale(1.83711,1.85) translate(-734.078,-387.592)" width="28" x="1611" xlink:href="#EnergyConsumer:站用变DY接地_0" y="843.5826364057742" zvalue="652"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449768521734" ObjectName="#2站用变"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1636.72,871.333) scale(1.83711,1.85) translate(-734.078,-387.592)" width="28" x="1611" y="843.5826364057742"/></g>
  <g id="24">
   <use class="v10500" height="30" transform="rotate(0,1637.08,723) scale(1.25,1.23333) translate(-325.917,-133.284)" width="12" x="1629.583333333333" xlink:href="#EnergyConsumer:负荷_0" y="704.5" zvalue="657"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449768652806" ObjectName="10.5kV市电"/>
   <cge:TPSR_Ref TObjectID="6192449768652806"/></metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1637.08,723) scale(1.25,1.23333) translate(-325.917,-133.284)" width="12" x="1629.583333333333" y="704.5"/></g>
 </g>
</svg>