<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:cge="http://www.cim.com" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="5066549587345410" height="1052" id="thSvg" source="NR-PCS9000" viewBox="0 0 1912 1052" width="1912">
 <defs>
  <style type="text/css"><![CDATA[
.kv525{stroke:rgb(85,0,127);fill:none}
.kv500{stroke:rgb(255,0,0);fill:none}
.kv400{stroke:rgb(85,170,255);fill:none}
.kv350{stroke:rgb(170,85,127);fill:none}
.kv230{stroke:rgb(255,0,0);fill:none}
.kv220{stroke:rgb(255,255,255);fill:none}
.kv115{stroke:rgb(0,255,0);fill:none}
.kv110{stroke:rgb(170,85,127);fill:none}
.kv66{stroke:rgb(101,220,244);fill:none}
.kv35{stroke:rgb(230,230,0);fill:none}
.v34500{stroke:rgb(255,255,0);fill:none}
.kv24{stroke:rgb(255,170,0);fill:none}
.kv22{stroke:rgb(255,170,0);fill:none}
.kv20{stroke:rgb(255,170,0);fill:none}
.kv19{stroke:rgb(255,170,0);fill:none}
.kv18{stroke:rgb(255,170,0);fill:none}
.kv16{stroke:rgb(255,255,127);fill:none}
.v15750{stroke:rgb(0,255,255);fill:none}
.v13800{stroke:rgb(255,170,0);fill:none}
.v10500{stroke:rgb(130,0,0);fill:none}
.kv10{stroke:rgb(0,255,0);fill:none}
.v6300{stroke:rgb(0,0,255);fill:none}
.kv6{stroke:rgb(0,0,255);fill:none}
.kv1{stroke:rgb(122,122,122);fill:none}
.v400{stroke:rgb(85,170,127);fill:none}
.kv0{stroke:rgb(122,122,122);fill:none}
]]></style>
  <symbol id="terminal" preserveAspectRatio="xMidYMid meet">
   <circle cx="0" cy="0" fill="rgb(0,255,0)" r="1" stroke="rgb(0,255,0)" stroke-width="1"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_0" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(255,0,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="State:红绿圆(方形)_1" viewBox="0,0,30,30">
   <rect Plane="0" fill="rgb(0,255,0)" fill-opacity="1" height="29.08" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,15,15.04) scale(1,1) translate(0,0)" width="29.33" x="0.33" y="0.5"/>
  </symbol>
  <symbol id="Breaker:小车断路器_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.982326301579349" x2="4.982326301579349" y1="14.50000000000001" y2="17.08333333333334"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.066617489318812" x2="5.066617489318812" y1="2.58333333333333" y2="5.75"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <rect fill="rgb(255,0,0)" fill-opacity="1" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
  </symbol>
  <symbol id="Breaker:小车断路器_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5" xlink:href="#terminal" y="0.75"/>
   <use terminal-index="1" type="0" x="5" xlink:href="#terminal" y="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="14.41666666666667" y2="19"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5" x2="5" y1="0.5833333333333357" y2="5.833333333333336"/>
   <path d="M 8.63215 16.6556 L 4.98274 18.9797 L 1.33333 16.6556" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 2.90358 L 5.06482 0.681096 L 1.45119 2.90358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.63215 14.8223 L 4.98274 17.1463 L 1.33333 14.8223" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <path d="M 8.75 4.65358 L 5.06482 2.4311 L 1.45119 4.65358" fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="2.583333333333334" x2="7.5" y1="5.666666666666667" y2="14.33333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="7.333333333333334" x2="2.583333333333333" y1="5.833333333333333" y2="14.5"/>
   <rect fill-opacity="0" height="8.83" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5,10.08) scale(1,1) translate(0,0)" width="5" x="2.5" y="5.67"/>
  </symbol>
  <symbol id="ACLineSegment:单相PT线路_0" viewBox="0,0,30,40">
   <use terminal-index="0" type="0" x="14.95833333333333" xlink:href="#terminal" y="0.9221418778962942"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="6.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="5.75" y1="28.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.5" x2="25.5" y1="28.5" y2="28.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="3.25" x2="8.25" y1="31.25" y2="31.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.25" x2="6.25" y1="33.25" y2="33.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19" x2="25" y1="20" y2="20"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="4.25" x2="7.25" y1="32.25" y2="32.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="5.75" x2="4.75" y1="22" y2="16"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="14.91022336769755" x2="14.91022336769755" y1="38.83333333333334" y2="0.8333333333333321"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.33333333333334" x2="14.98769568537606" y1="9.258752830905545" y2="9.258752830905545"/>
   <path d="M 14.75 9.25 L 5.75 9.25 L 5.75 21.25 L 5.75 21.25" fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.25545055364641" x2="22.25545055364641" y1="9.291388644475965" y2="15.41666666666666"/>
   <rect fill-opacity="0" height="14.33" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,5.79,21.17) scale(1,1) translate(0,0)" width="6.08" x="2.75" y="14"/>
   <ellipse cx="22.26" cy="20.07" fill-opacity="0" rx="4.78" ry="4.78" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="22.51" cy="28.33" fill-opacity="0" rx="4.48" ry="4.67" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.32589728904158" x2="17.32589728904158" y1="9.247588122517026" y2="9.247588122517026"/>
  </symbol>
  <symbol id="EnergyConsumer:Y-d厂用变_0" viewBox="0,0,20,35">
   <use terminal-index="0" type="0" x="10.16666666666667" xlink:href="#terminal" y="1.166666666666671"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="13" y1="15.25" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13" x2="7" y1="20.25" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="34.5" y2="31.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="24.5" y2="34.5"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="7" y1="15.25" y2="20.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="9" y1="34.5" y2="31.5"/>
   <ellipse cx="9.92" cy="7.6" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="10" cy="18.17" fill-opacity="0" rx="6.48" ry="6.5" stroke="rgb(0,255,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10.11171811872235" x2="10.11171811872235" y1="4.753560677018264" y2="7.352548777035198"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="12.7052656081938" x2="10.11171811872235" y1="9.951536877052112" y2="7.352548777035178"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="7.518170629250881" x2="10.11171811872233" y1="9.951536877052112" y2="7.352548777035178"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_0" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.081410256410256" x2="5.081410256410256" y1="16.01666666666667" y2="13.61429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.666666666666666" x2="8" y1="16.09694619969017" y2="16.09694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.083333333333333" x2="7" y1="17.93157590710537" y2="17.93157590710537"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.774021844661626" x2="6.43079938068318" y1="19.68348701738202" y2="19.68348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="0.916666666666667" x2="5.081410256410257" y1="3.766666666666667" y2="13.6142916052417"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.117489181241998" x2="5.117489181241998" y1="3.600000000000003" y2="0.5083301378115159"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.416666666666666" x2="6.75" y1="3.64249548976499" y2="3.64249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_1" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.533333333333333" x2="7.866666666666667" y1="15.81361286635684" y2="15.81361286635684"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="2.95" x2="6.866666666666666" y1="17.64824257377203" y2="17.64824257377203"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.640688511328293" x2="6.297466047349847" y1="19.40015368404869" y2="19.40015368404869"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="4.966666666666667" x2="4.966666666666667" y1="3.483333333333333" y2="15.73333333333333"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.400000000000003" y2="0.3083301378115166"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.44249548976499" y2="3.44249548976499"/>
  </symbol>
  <symbol id="GroundDisconnector:地刀_2" viewBox="0,0,10,20">
   <use terminal-index="0" type="0" x="5.05" xlink:href="#terminal" y="0.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="8.449999999999999" x2="1.45" y1="4" y2="13.25"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.031410256410256" x2="5.031410256410256" y1="15.91666666666667" y2="13.51429160524171"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="1.616666666666666" x2="7.95" y1="15.99694619969017" y2="15.99694619969017"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.033333333333333" x2="6.949999999999999" y1="17.83157590710536" y2="17.83157590710536"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" x1="3.724021844661626" x2="6.38079938068318" y1="19.58348701738202" y2="19.58348701738202"/>
   <line fill="none" stroke="rgb(0,0,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="1.45" x2="8.699999999999999" y1="3.883333333333333" y2="13.3"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="3.366666666666666" x2="6.7" y1="3.54249548976499" y2="3.54249548976499"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="5.067489181241998" x2="5.067489181241998" y1="3.500000000000003" y2="0.4083301378115163"/>
  </symbol>
  <symbol id="Accessory:线路PT3_0" viewBox="0,0,20,20">
   <use terminal-index="0" type="0" x="10" xlink:href="#terminal" y="1.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="11" y1="11.25" y2="10.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="11" y1="19.25" y2="19.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="9" x2="10" y1="10.25" y2="11.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.5" x2="11.5" y1="18.25" y2="18.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8" x2="12" y1="17.25" y2="17.25"/>
   <rect fill-opacity="0" height="9" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,10,9.75) scale(1,1) translate(0,0)" width="6" x="7" y="5.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="14.25" y2="17.25"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="10" x2="10" y1="1.25" y2="11.25"/>
  </symbol>
  <symbol id="Accessory:10kV母线PT带消谐装置_0" viewBox="0,0,35,30">
   <use terminal-index="0" type="0" x="17.56245852479325" xlink:href="#terminal" y="28.12373692455963"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="34.61245852479333" x2="34.61245852479333" y1="11.84040359122622" y2="9.84040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.86245852479331" x2="30.86245852479331" y1="22.68299618381883" y2="19.84040359122624"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.6124585247933" x2="26.6124585247933" y1="20.84040359122623" y2="18.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="26.61245852479332" x2="34.61245852479332" y1="18.84040359122622" y2="11.84040359122623"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25.16666666666667" x2="31" y1="22.69225544307809" y2="22.69225544307809"/>
   <rect fill-opacity="0" height="3.55" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(-90,30.81,15.38) scale(1,1) translate(0,0)" width="8.92" x="26.34" y="13.6"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="30.647721108666" x2="30.647721108666" y1="10.83674821859629" y2="7.666666666666664"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="28.71438777533266" x2="28.71438777533266" y1="7.51612768565195" y2="7.51612768565195"/>
   <ellipse cx="14.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.05048569403981" x2="22.05048569403981" y1="34.53084700683308" y2="34.53084700683308"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.7608382776216" x2="28.48402243293775" y1="7.754208141873304" y2="7.754208141873304"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="32.01083827762157" x2="29.40068909960439" y1="6.50420814187332" y2="6.50420814187332"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="31.51083827762159" x2="30.06735576627107" y1="5.004208141873296" y2="5.004208141873296"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="21.89410730945214" x2="23.0877735452272" y1="24.15299989806297" y2="21.53208583485582"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="19.58713830216066" x2="22.81883560169719" y1="21.54040359122622" y2="21.54040359122622"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.51457106407813" x2="19.32090482830307" y1="24.05654513693459" y2="21.43563107372743"/>
   <ellipse cx="14.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <ellipse cx="21.04" cy="22.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="22.3644003886642" x2="22.3644003886642" y1="30.19213090500035" y2="30.19213090500035"/>
   <ellipse cx="21.04" cy="15.63" fill-opacity="0" rx="4.17" ry="4.17" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="17.19220382559271" x2="17.19220382559271" y1="23.07076893362116" y2="23.07076893362116"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.4422038255927" x2="16.4422038255927" y1="16.82076893362115" y2="16.82076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="18.92264294445647" x2="20.87303257583197" y1="16.99992879670395" y2="15.47611741162254"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.873032575832" x2="20.37063985073817" y1="15.47611741162252" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="20.87303257583199" x2="23.32581493230132" y1="15.47611741162254" y2="16.60543752773795"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="2" x1="16.94220382559271" x2="16.94220382559271" y1="16.32076893362115" y2="16.32076893362115"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="16.07581493230131" y1="22.72611741162255" y2="23.85543752773797"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="13.12063985073816" y1="22.72611741162255" y2="20.07298591042571"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583197" y1="24.24992879670398" y2="22.72611741162257"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583199" x2="16.07581493230131" y1="15.47611741162254" y2="16.60543752773796"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="13.62303257583198" x2="13.12063985073816" y1="15.47611741162253" y2="12.82298591042569"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.67264294445646" x2="13.62303257583196" y1="16.99992879670396" y2="15.47611741162255"/>
   <line fill="none" stroke="rgb(0,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="17.59103363843511" x2="17.59103363843511" y1="25.15822158129307" y2="28.16666666666667"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_0" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(0,255,127)" fill-opacity="1" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <rect height="12.17" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.75,25.42) scale(1,1) translate(0,0)" width="6" x="8.75" y="19.33"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="11.5" y2="37.41666666666667"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_1" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <rect fill-opacity="0" height="8" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(255,0,0)" fill-opacity="1" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="7.333333333333334" y2="41.5"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(255,0,0)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
   <rect height="12.17" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,11.75,25.42) scale(1,1) translate(0,0)" width="6" x="8.75" y="19.33"/>
  </symbol>
  <symbol id="Disconnector:手车刀闸带避雷器_2" viewBox="0,0,30,42">
   <use terminal-index="0" type="0" x="11.75" xlink:href="#terminal" y="7.25"/>
   <use terminal-index="1" type="0" x="11.75" xlink:href="#terminal" y="41.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="8.75" x2="14.75" y1="17" y2="32"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="14.65" x2="8.65" y1="17.1" y2="32.1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="16.5" y2="8.5"/>
   <rect fill-opacity="0" height="8" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,25,7.5) scale(1,1) translate(0,0)" width="4" x="23" y="3.5"/>
   <path d="M 24 8.5 L 26 8.5 L 25 5.5 L 24 8.5 L 24 8.5 z" fill="rgb(0,255,127)" fill-opacity="1" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="25" x2="25" y1="2.5" y2="3.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="23" x2="27" y1="2.5" y2="2.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24" x2="26" y1="1.75" y2="1.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="24.5" x2="25.5" y1="0.75" y2="0.75"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="24.975" y1="16.475" y2="16.475"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.75" x2="11.75" y1="32.35" y2="41.5"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="11.71666666666667" x2="11.71666666666667" y1="17.05" y2="7.466666666666663"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="6.343004115226339" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="11.43694745991201" y2="16.24729228749822"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.80133744855967" x2="17.259670781893" y1="7.430481942670642" y2="12.24082677025685"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="41.49729228749822" y2="36.68694745991201"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="6.293004115226339" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="37.49082677025685" y2="32.68048194267064"/>
   <line fill="none" stroke="rgb(0,255,127)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" x1="11.75133744855967" x2="17.209670781893" y1="41.49729228749822" y2="36.68694745991201"/>
  </symbol>
 </defs>
 <g id="HeadClass">
  <rect FacName="35kV骏逸总降变" InitShowingPlane="" fill="rgb(0,0,0)" height="1052" width="1912" x="0" y="0"/>
 </g>
 <g exclusive="0" id="PlaneClass"/>
 <g id="ButtonClass">
  <g href="自动化值班_一览表20210707.svg"><rect fill-opacity="0" height="24" width="72.88" x="71.94" y="327" zvalue="31"/></g>
 </g>
 <g id="OtherClass">
  <rect fill="rgb(0,0,0)" fill-opacity="0" height="24" id="1" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" width="72.88" x="71.94" y="327" zvalue="31"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="1" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,108.375,339) scale(1,1) translate(0,0)" writing-mode="lr" x="108.38" xml:space="preserve" y="343.5" zvalue="31">信号一览</text>
  <image height="60" id="2" preserveAspectRatio="xMidYMid slice" width="269.25" x="66.95999999999999" xlink:href="logo.png" y="45.64"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="11" id="70" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,201.589,75.6429) scale(1,1) translate(0,0)" writing-mode="lr" x="201.59" xml:space="preserve" y="79.14" zvalue="138"/>
  <text fill="rgb(0,0,0)" font-family="FangSong" font-size="24" id="3" stroke="rgb(0,0,0)" text-anchor="middle" transform="rotate(0,203.298,75.3332) scale(1,1) translate(9.43293e-15,0)" writing-mode="lr" x="203.3" xml:space="preserve" y="84.33" zvalue="139">35kV骏逸总降变</text>
  <line fill="none" id="26" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="388.75" x2="388.75" y1="11" y2="1039.25" zvalue="6"/>
  <line fill="none" id="24" stroke="rgb(178,184,182)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="27.22692454998003" x2="331.8381846035992" y1="168.8779458827686" y2="168.8779458827686" zvalue="8"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="927.8127909390723" y2="927.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="979.1522909390724" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="361.3219285714284" y1="927.8127909390723" y2="927.8127909390723"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="361.3219285714284" y1="979.1522909390724" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3219285714284" x2="361.3219285714284" y1="927.8127909390723" y2="979.1522909390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5593285714284" x2="279.5593285714284" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="979.1522709390724" y2="979.1522709390724"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1006.629770939072" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="279.5592285714284" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3213285714285" x2="361.3213285714285" y1="979.1522709390724" y2="1006.629770939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="126.7614285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="55.32142857142844" x2="55.32142857142844" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7614285714285" x2="126.7614285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="197.7972285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="126.7619285714285" x2="126.7619285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="279.5593285714284" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="197.7972285714285" x2="197.7972285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5593285714284" x2="279.5593285714284" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1006.629690939072" y2="1006.629690939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="361.3213285714285" y1="1034.107190939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="279.5592285714284" x2="279.5592285714284" y1="1006.629690939072" y2="1034.107190939072"/>
  <line stroke="rgb(255,255,255)" stroke-width="1" x1="361.3213285714285" x2="361.3213285714285" y1="1006.629690939072" y2="1034.107190939072"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="22" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,202.374,957.386) scale(1,1) translate(-1.34729e-14,1.05089e-13)" writing-mode="lr" x="60.68" xml:space="preserve" y="963.39" zvalue="10">参考图号      JunYi-01-2018</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="21" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,132.899,994.755) scale(1,1) translate(-6.25312e-14,-1.52933e-12)" writing-mode="lr" x="70.40000000000001" xml:space="preserve" y="1000.75" zvalue="11">制图             </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="20" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,276.202,995.755) scale(1,1) translate(1.38222e-13,-1.53089e-12)" writing-mode="lr" x="207.5" xml:space="preserve" y="1001.75" zvalue="12">绘制日期    </text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="19" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,83.9923,1024.31) scale(1,1) translate(-6.10858e-14,-1.57527e-12)" writing-mode="lr" x="83.98999999999999" xml:space="preserve" y="1030.31" zvalue="13">更新</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="16" id="18" stroke="rgb(255,255,255)" text-anchor="start" transform="rotate(0,280.836,1022.31) scale(1,1) translate(-4.58902e-14,1.12298e-13)" writing-mode="lr" x="206.67" xml:space="preserve" y="1028.31" zvalue="14">更新日期  </text>
  <line fill="none" id="17" stroke="rgb(197,197,197)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" x1="54.60611355802337" x2="359.2173736116425" y1="623.6445306693694" y2="623.6445306693694" zvalue="15"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="15" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,97.6707,640.686) scale(1,1) translate(5.99964e-15,-1.38109e-13)" writing-mode="lr" x="97.67070806204492" xml:space="preserve" y="645.1863811688671" zvalue="17">危险点(双击编辑）：</text>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="172.1071428571429" y2="172.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="172.1071428571429" y2="172.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="172.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="198.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="198.1071428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="222.3571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="222.3571428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="245.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="245.1071428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="197.3214285714284" y1="290.6071428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="16.32142857142844" x2="16.32142857142844" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="267.8571428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="378.3214285714284" y1="290.6071428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="197.3214285714284" x2="197.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="378.3214285714284" x2="378.3214285714284" y1="267.8571428571429" y2="290.6071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="452.1071428571429" y2="452.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="452.1071428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="490.3894428571429" y2="490.3894428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="490.3894428571429" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="515.0688428571428" y2="515.0688428571428"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="515.0688428571428" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="539.7482428571429" y2="539.7482428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="564.4276428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="539.7482428571429" y2="564.4276428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="564.4277428571429" y2="564.4277428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="564.4277428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="113.0959285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="67.32142857142844" x2="67.32142857142844" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="171.9023285714285" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="113.0959285714284" x2="113.0959285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="230.7087285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="171.9023285714285" x2="171.9023285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7087285714284" x2="230.7087285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="289.5150285714285" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="230.7086285714284" x2="230.7086285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="589.1071428571429" y2="589.1071428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="348.3214285714284" y1="613.7865428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="289.5150285714285" x2="289.5150285714285" y1="589.1071428571429" y2="613.7865428571429"/>
  <line stroke="rgb(178,184,182)" stroke-width="1" x1="348.3214285714284" x2="348.3214285714284" y1="589.1071428571429" y2="613.7865428571429"/>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="9" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,502.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="507.1071428571429" zvalue="23">Uab</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="8" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,528.107) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="532.6071428571429" zvalue="24">Ua</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="7" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,553.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="558.1071428571429" zvalue="25">Ub</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="6" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,579.107) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="583.6071428571429" zvalue="26">Uc</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="5" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,92.3214,604.607) scale(1,1) translate(0,0)" writing-mode="lr" x="92.32142857142844" xml:space="preserve" y="609.1071428571429" zvalue="27">3Uo</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="4" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,54.3214,186.107) scale(1,1) translate(0,0)" writing-mode="lr" x="54.32" xml:space="preserve" y="191.61" zvalue="28">全站有功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="15" id="3" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,240.571,186.107) scale(1,1) translate(0,0)" writing-mode="lr" x="240.57" xml:space="preserve" y="191.61" zvalue="29">全站无功</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="43" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,529.375,472.5) scale(1,1) translate(0,0)" writing-mode="lr" x="529.38" xml:space="preserve" y="477" zvalue="43">35kV母线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" x="200.7890625" xml:space="preserve" y="468.359375" zvalue="114">35kV母</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="114" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="200.7890625" xml:space="preserve" y="484.359375" zvalue="114">线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="37" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,250.009,210.357) scale(1,1) translate(0,0)" writing-mode="lr" x="250.01" xml:space="preserve" y="214.86" zvalue="122">35kV母线频率</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="167" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,208.399,340.5) scale(1,1) translate(0,0)" writing-mode="lr" x="208.4" xml:space="preserve" y="345" zvalue="379">事故总</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="153" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,296.399,340.5) scale(1,1) translate(0,0)" writing-mode="lr" x="296.4" xml:space="preserve" y="345" zvalue="380">通道</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="297" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,677.75,424) scale(1,1) translate(0,0)" writing-mode="lr" x="677.75" xml:space="preserve" y="428.5" zvalue="390">331</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="299" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,647,143.125) scale(1,1) translate(0,0)" writing-mode="lr" x="647" xml:space="preserve" y="147.63" zvalue="391">35kV梁骏线</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" x="530.75" xml:space="preserve" y="806.25" zvalue="396">1号主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="11" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="530.75" xml:space="preserve" y="822.25" zvalue="396">3150kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="31" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,616.725,562.5) scale(1,1) translate(0,0)" writing-mode="lr" x="616.73" xml:space="preserve" y="567" zvalue="399">301</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="39" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,541.5,646) scale(1,1) translate(0,0)" writing-mode="lr" x="541.5" xml:space="preserve" y="650.5" zvalue="402">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" x="680.75" xml:space="preserve" y="806.25" zvalue="408">2号主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="48" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="680.75" xml:space="preserve" y="822.25" zvalue="408">3150kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="47" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,766.725,562.5) scale(1,1) translate(0,0)" writing-mode="lr" x="766.73" xml:space="preserve" y="567" zvalue="410">302</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="46" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,689,646) scale(1,1) translate(0,0)" writing-mode="lr" x="689" xml:space="preserve" y="650.5" zvalue="415">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" x="836.75" xml:space="preserve" y="806.25" zvalue="443">3号主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="87" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="836.75" xml:space="preserve" y="822.25" zvalue="443">3150kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="86" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,922.725,562.5) scale(1,1) translate(0,0)" writing-mode="lr" x="922.73" xml:space="preserve" y="567" zvalue="445">303</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="85" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,845,646) scale(1,1) translate(0,0)" writing-mode="lr" x="845" xml:space="preserve" y="650.5" zvalue="450">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" x="990.75" xml:space="preserve" y="806.25" zvalue="455">4号主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="99" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="990.75" xml:space="preserve" y="822.25" zvalue="455">3150kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="98" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1076.72,562.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1076.72" xml:space="preserve" y="567" zvalue="457">304</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="97" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,999,646) scale(1,1) translate(0,0)" writing-mode="lr" x="999" xml:space="preserve" y="650.5" zvalue="462">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" x="1153.75" xml:space="preserve" y="806.25" zvalue="467">5号主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="120" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1153.75" xml:space="preserve" y="822.25" zvalue="467">3150kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="119" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1239.72,562.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1239.72" xml:space="preserve" y="567" zvalue="469">305</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="118" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1164.5,646) scale(1,1) translate(0,0)" writing-mode="lr" x="1164.5" xml:space="preserve" y="650.5" zvalue="474">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" x="1303.75" xml:space="preserve" y="806.25" zvalue="478">6号主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="117" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1303.75" xml:space="preserve" y="822.25" zvalue="478">3150kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="116" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1389.72,562.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1389.73" xml:space="preserve" y="567" zvalue="480">306</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="115" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1312,646) scale(1,1) translate(0,0)" writing-mode="lr" x="1312" xml:space="preserve" y="650.5" zvalue="485">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" x="1459.75" xml:space="preserve" y="806.25" zvalue="489">7号主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="113" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1459.75" xml:space="preserve" y="822.25" zvalue="489">3150kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="112" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1545.72,562.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1545.73" xml:space="preserve" y="567" zvalue="491">307</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="111" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1468,646) scale(1,1) translate(0,0)" writing-mode="lr" x="1468" xml:space="preserve" y="650.5" zvalue="496">67</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" x="1613.75" xml:space="preserve" y="806.25" zvalue="500">8号主变</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="110" stroke="rgb(255,255,255)" text-anchor="middle" writing-mode="lr" x="1613.75" xml:space="preserve" y="822.25" zvalue="500">3150kVA</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="109" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1699.72,562.5) scale(1,1) translate(0,0)" writing-mode="lr" x="1699.73" xml:space="preserve" y="567" zvalue="502">308</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="108" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1622,646) scale(1,1) translate(0,0)" writing-mode="lr" x="1622" xml:space="preserve" y="650.5" zvalue="507">67</text>
  <rect fill="none" fill-opacity="0" height="157.5" id="160" stroke="rgb(255,255,255)" stroke-dasharray="none" stroke-linecap="round" stroke-linejoin="bevel" stroke-width="1" transform="rotate(0,1673.12,811.25) scale(1,1) translate(0,0)" width="181.25" x="1582.5" y="732.5" zvalue="510"/>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="161" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1675.16,913.357) scale(1,1) translate(-3.57619e-13,0)" writing-mode="lr" x="1675.160714285714" xml:space="preserve" y="917.8571428571429" zvalue="512">8号主变暂未安装</text>
  <text fill="rgb(255,255,255)" font-family="FangSong" font-size="13" id="41" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1131.66,301.625) scale(1,1) translate(0,0)" writing-mode="lr" x="1131.66" xml:space="preserve" y="306.13" zvalue="514">35kV母线电压互感器</text>
  <text fill="rgb(255,255,255)" font-family="SimSun" font-size="13" id="64" stroke="rgb(255,255,255)" text-anchor="middle" transform="rotate(0,1100.25,423.75) scale(1,1) translate(0,0)" writing-mode="lr" x="1100.25" xml:space="preserve" y="428.25" zvalue="516">3901</text>
 </g>
 <g id="ClockClass">
  
 </g>
 <g id="BusbarSectionClass">
  <g id="36">
   <path class="kv35" d="M 505.75 493.5 L 1751 493.5" stroke-width="4" zvalue="42"/>
   <metadata>
    <cge:PSR_Ref ObjectID="9288674244624388" ObjectName="35kV母线"/>
   <cge:TPSR_Ref TObjectID="9288674244624388"/></metadata>
  <path d="M 505.75 493.5 L 1751 493.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="MeasurementClass">
  <g id="33">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="16" id="33" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,161.571,186) scale(1,1) translate(0,0)" writing-mode="lr" x="161.72" xml:space="preserve" y="192.43" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125369413638" ObjectName=""/>
   </metadata>
  </g>
  <g id="35">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="16" id="35" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,343.571,185.857) scale(1,1) translate(0,0)" writing-mode="lr" x="343.72" xml:space="preserve" y="192.29" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125369479174" ObjectName=""/>
   </metadata>
  </g>
  <g id="42">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="42" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,202.071,528.593) scale(1,1) translate(0,0)" writing-mode="lr" x="202.2" xml:space="preserve" y="533.5" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125354733574" ObjectName="Ua"/>
   </metadata>
  </g>
  <g id="49">
   <text Format="f5.2" Plane="0" fill="rgb(85,255,0)" font-family="SimSun" font-size="13" id="49" stroke="rgb(85,255,0)" text-anchor="middle" transform="rotate(0,202.071,553.718) scale(1,1) translate(0,-1.20619e-13)" writing-mode="lr" x="202.2" xml:space="preserve" y="558.63" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125354799110" ObjectName="Ub"/>
   </metadata>
  </g>
  <g id="52">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="52" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,203.071,577.843) scale(1,1) translate(0,0)" writing-mode="lr" x="203.2" xml:space="preserve" y="582.75" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125354864646" ObjectName="Uc"/>
   </metadata>
  </g>
  <g id="38">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="38" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,203.5,503.468) scale(1,1) translate(0,1.09461e-13)" writing-mode="lr" x="203.63" xml:space="preserve" y="508.38" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125354995718" ObjectName="Uab"/>
   </metadata>
  </g>
  <g id="53">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,255)" font-family="SimSun" font-size="13" id="53" stroke="rgb(0,255,255)" text-anchor="middle" transform="rotate(0,202.321,602.968) scale(1,1) translate(0,0)" writing-mode="lr" x="202.45" xml:space="preserve" y="607.88" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125355192329" ObjectName="U0"/>
   </metadata>
  </g>
  <g id="54">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="16" id="54" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,343.571,209.968) scale(1,1) translate(0,0)" writing-mode="lr" x="343.72" xml:space="preserve" y="216.4" zvalue="1">ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125355126790" ObjectName="F"/>
   </metadata>
  </g>
  <g id="62">
   <text Format="f5.2" Plane="0" fill="rgb(0,255,0)" font-family="SimSun" font-size="13" id="62" prefix="P:" stroke="rgb(0,255,0)" text-anchor="middle" transform="rotate(0,647,69.5) scale(1,1) translate(0,0)" writing-mode="lr" x="646.53" xml:space="preserve" y="74.28" zvalue="1">P:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125355782152" ObjectName="P"/>
   </metadata>
  </g>
  <g id="67">
   <text Format="f5.2" Plane="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="13" id="67" prefix="Q:" stroke="rgb(255,0,0)" text-anchor="middle" transform="rotate(0,647,94.5) scale(1,1) translate(0,0)" writing-mode="lr" x="646.53" xml:space="preserve" y="99.28" zvalue="1">Q:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125355847689" ObjectName="Q"/>
   </metadata>
  </g>
  <g id="68">
   <text Format="f5.2" Plane="0" fill="rgb(255,255,0)" font-family="SimSun" font-size="13" id="68" prefix="Ia:" stroke="rgb(255,255,0)" text-anchor="middle" transform="rotate(0,647,115.5) scale(1,1) translate(0,0)" writing-mode="lr" x="646.53" xml:space="preserve" y="120.28" zvalue="1">Ia:ddd.dd</text>
   <metadata>
    <cge:Meas_Ref ObjectID="15481125355913225" ObjectName="Ia"/>
   </metadata>
  </g>
 </g>
 <g id="StateClass">
  <g id="132">
   <use height="30" transform="rotate(0,329.768,340.5) scale(0.708333,0.665547) translate(131.412,166.093)" width="30" x="319.14" xlink:href="#State:红绿圆(方形)_0" y="330.52" zvalue="381"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,329.768,340.5) scale(0.708333,0.665547) translate(131.412,166.093)" width="30" x="319.14" y="330.52"/></g>
  <g id="94">
   <use height="30" transform="rotate(0,247.143,340.5) scale(0.708333,0.665547) translate(97.3897,166.093)" width="30" x="236.52" xlink:href="#State:红绿圆(方形)_0" y="330.52" zvalue="382"/>
   <metadata>
    <cge:Meas_Ref ObjectID="" ObjectName=""/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,247.143,340.5) scale(0.708333,0.665547) translate(97.3897,166.093)" width="30" x="236.52" y="330.52"/></g>
 </g>
 <g id="BreakerClass">
  <g id="296">
   <use class="kv35" height="20" transform="rotate(0,647,423.75) scale(2.75,2.75) translate(-402.977,-252.159)" width="10" x="633.25" xlink:href="#Breaker:小车断路器_0" y="396.25" zvalue="389"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924514414597" ObjectName="35kV梁骏线331断路器"/>
   <cge:TPSR_Ref TObjectID="6473924514414597"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,647,423.75) scale(2.75,2.75) translate(-402.977,-252.159)" width="10" x="633.25" y="396.25"/></g>
  <g id="28">
   <use class="kv35" height="20" transform="rotate(0,590.975,563.5) scale(2.75,2.75) translate(-367.325,-341.091)" width="10" x="577.225" xlink:href="#Breaker:小车断路器_0" y="536" zvalue="398"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924514480133" ObjectName="35kV1号主变35kV侧301断路器"/>
   <cge:TPSR_Ref TObjectID="6473924514480133"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,590.975,563.5) scale(2.75,2.75) translate(-367.325,-341.091)" width="10" x="577.225" y="536"/></g>
  <g id="59">
   <use class="kv35" height="20" transform="rotate(0,740.975,563.5) scale(2.75,2.75) translate(-462.78,-341.091)" width="10" x="727.225" xlink:href="#Breaker:小车断路器_0" y="536" zvalue="409"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924514545669" ObjectName="35kV2号主变35kV侧302断路器"/>
   <cge:TPSR_Ref TObjectID="6473924514545669"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,740.975,563.5) scale(2.75,2.75) translate(-462.78,-341.091)" width="10" x="727.225" y="536"/></g>
  <g id="95">
   <use class="kv35" height="20" transform="rotate(0,896.975,563.5) scale(2.75,2.75) translate(-562.052,-341.091)" width="10" x="883.225" xlink:href="#Breaker:小车断路器_0" y="536" zvalue="444"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924514611205" ObjectName="35kV3号主变35kV侧303断路器"/>
   <cge:TPSR_Ref TObjectID="6473924514611205"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,896.975,563.5) scale(2.75,2.75) translate(-562.052,-341.091)" width="10" x="883.225" y="536"/></g>
  <g id="106">
   <use class="kv35" height="20" transform="rotate(0,1050.97,563.5) scale(2.75,2.75) translate(-660.052,-341.091)" width="10" x="1037.225" xlink:href="#Breaker:小车断路器_0" y="536" zvalue="456"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924514676741" ObjectName="35kV4号主变35kV侧304断路器"/>
   <cge:TPSR_Ref TObjectID="6473924514676741"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1050.97,563.5) scale(2.75,2.75) translate(-660.052,-341.091)" width="10" x="1037.225" y="536"/></g>
  <g id="158">
   <use class="kv35" height="20" transform="rotate(0,1213.97,563.5) scale(2.75,2.75) translate(-763.78,-341.091)" width="10" x="1200.225" xlink:href="#Breaker:小车断路器_0" y="536" zvalue="468"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924514938886" ObjectName="35kV5号主变35kV侧305断路器"/>
   <cge:TPSR_Ref TObjectID="6473924514938886"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1213.97,563.5) scale(2.75,2.75) translate(-763.78,-341.091)" width="10" x="1200.225" y="536"/></g>
  <g id="149">
   <use class="kv35" height="20" transform="rotate(0,1363.97,563.5) scale(2.75,2.75) translate(-859.234,-341.091)" width="10" x="1350.225" xlink:href="#Breaker:小车断路器_0" y="536" zvalue="479"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924514873349" ObjectName="35kV6号主变35kV侧306断路器"/>
   <cge:TPSR_Ref TObjectID="6473924514873349"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1363.97,563.5) scale(2.75,2.75) translate(-859.234,-341.091)" width="10" x="1350.225" y="536"/></g>
  <g id="136">
   <use class="kv35" height="20" transform="rotate(0,1519.97,563.5) scale(2.75,2.75) translate(-958.507,-341.091)" width="10" x="1506.225" xlink:href="#Breaker:小车断路器_0" y="536" zvalue="490"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924514807813" ObjectName="35kV7号主变35kV侧307断路器"/>
   <cge:TPSR_Ref TObjectID="6473924514807813"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1519.97,563.5) scale(2.75,2.75) translate(-958.507,-341.091)" width="10" x="1506.225" y="536"/></g>
  <g id="127">
   <use class="kv35" height="20" transform="rotate(0,1673.97,563.5) scale(2.75,2.75) translate(-1056.51,-341.091)" width="10" x="1660.225" xlink:href="#Breaker:小车断路器_0" y="536" zvalue="501"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6473924514742277" ObjectName="35kV8号主变35kV侧308断路器"/>
   <cge:TPSR_Ref TObjectID="6473924514742277"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1673.97,563.5) scale(2.75,2.75) translate(-1056.51,-341.091)" width="10" x="1660.225" y="536"/></g>
 </g>
 <g id="ACLineSegmentClass">
  <g id="298">
   <use class="kv35" height="40" transform="rotate(0,647,184) scale(1.25,-1.25) translate(-125.65,-326.2)" width="30" x="628.25" xlink:href="#ACLineSegment:单相PT线路_0" y="159" zvalue="390"/>
   <metadata>
    <cge:PSR_Ref ObjectID="8444249304137733" ObjectName="35kV梁骏线"/>
   <cge:TPSR_Ref TObjectID="8444249304137733_5066549587345410"/></metadata>
  <rect fill="white" height="40" opacity="0" stroke="white" transform="rotate(0,647,184) scale(1.25,-1.25) translate(-125.65,-326.2)" width="30" x="628.25" y="159"/></g>
 </g>
 <g id="ConnectiveNodeClass">
  <g id="302">
   <path class="kv35" d="M 646.95 207.85 L 647 398.31" stroke-width="1" zvalue="393"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="298@0" LinkObjectIDznd="296@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 646.95 207.85 L 647 398.31" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="303">
   <path class="kv35" d="M 647 448.5 L 647 493.5" stroke-width="1" zvalue="394"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="296@1" LinkObjectIDznd="36@0" MaxPinNum="2"/>
   </metadata>
  <path d="M 647 448.5 L 647 493.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="29">
   <path class="kv35" d="M 591.98 773.83 L 591.98 588.25" stroke-width="1" zvalue="399"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="10@0" LinkObjectIDznd="28@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 591.98 773.83 L 591.98 588.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="30">
   <path class="kv35" d="M 590.98 538.06 L 590.98 493.5" stroke-width="1" zvalue="400"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="28@0" LinkObjectIDznd="36@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 590.98 538.06 L 590.98 493.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="44">
   <path class="kv35" d="M 563.59 629.42 L 563.59 608 L 591.98 608" stroke-width="1" zvalue="404"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="32@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 563.59 629.42 L 563.59 608 L 591.98 608" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="45">
   <path class="kv35" d="M 617.63 628.23 L 617.63 610 L 591.98 610" stroke-width="1" zvalue="405"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="40@0" LinkObjectIDznd="29" MaxPinNum="2"/>
   </metadata>
  <path d="M 617.63 628.23 L 617.63 610 L 591.98 610" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="58">
   <path class="kv35" d="M 741.98 773.83 L 741.98 588.25" stroke-width="1" zvalue="411"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="61@0" LinkObjectIDznd="59@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 741.98 773.83 L 741.98 588.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="57">
   <path class="kv35" d="M 740.98 538.06 L 740.98 493.5" stroke-width="1" zvalue="412"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="59@0" LinkObjectIDznd="36@2" MaxPinNum="2"/>
   </metadata>
  <path d="M 740.98 538.06 L 740.98 493.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="51">
   <path class="kv35" d="M 713.59 629.42 L 713.59 608 L 741.98 608" stroke-width="1" zvalue="416"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="56@0" LinkObjectIDznd="58" MaxPinNum="2"/>
   </metadata>
  <path d="M 713.59 629.42 L 713.59 608 L 741.98 608" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="50">
   <path class="kv35" d="M 767.63 628.23 L 767.63 610 L 741.98 610" stroke-width="1" zvalue="417"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="55@0" LinkObjectIDznd="58" MaxPinNum="2"/>
   </metadata>
  <path d="M 767.63 628.23 L 767.63 610 L 741.98 610" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="93">
   <path class="kv35" d="M 897.98 773.83 L 897.98 588.25" stroke-width="1" zvalue="446"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="96@0" LinkObjectIDznd="95@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 897.98 773.83 L 897.98 588.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="92">
   <path class="kv35" d="M 896.98 538.06 L 896.98 493.5" stroke-width="1" zvalue="447"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="95@0" LinkObjectIDznd="36@3" MaxPinNum="2"/>
   </metadata>
  <path d="M 896.98 538.06 L 896.98 493.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="89">
   <path class="kv35" d="M 869.59 629.42 L 869.59 608 L 897.98 608" stroke-width="1" zvalue="451"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="91@0" LinkObjectIDznd="93" MaxPinNum="2"/>
   </metadata>
  <path d="M 869.59 629.42 L 869.59 608 L 897.98 608" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="88">
   <path class="kv35" d="M 923.62 628.23 L 923.62 610 L 897.97 610" stroke-width="1" zvalue="452"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="90@0" LinkObjectIDznd="93" MaxPinNum="2"/>
   </metadata>
  <path d="M 923.62 628.23 L 923.62 610 L 897.97 610" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="105">
   <path class="kv35" d="M 1051.97 773.83 L 1051.97 588.25" stroke-width="1" zvalue="458"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="107@0" LinkObjectIDznd="106@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1051.97 773.83 L 1051.97 588.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="104">
   <path class="kv35" d="M 1050.97 538.06 L 1050.97 493.5" stroke-width="1" zvalue="459"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="106@0" LinkObjectIDznd="36@4" MaxPinNum="2"/>
   </metadata>
  <path d="M 1050.97 538.06 L 1050.97 493.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="101">
   <path class="kv35" d="M 1023.59 629.42 L 1023.59 608 L 1051.97 608" stroke-width="1" zvalue="463"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="103@0" LinkObjectIDznd="105" MaxPinNum="2"/>
   </metadata>
  <path d="M 1023.59 629.42 L 1023.59 608 L 1051.97 608" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="100">
   <path class="kv35" d="M 1077.63 628.23 L 1077.63 610 L 1051.97 610" stroke-width="1" zvalue="464"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="102@0" LinkObjectIDznd="105" MaxPinNum="2"/>
   </metadata>
  <path d="M 1077.63 628.23 L 1077.63 610 L 1051.97 610" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="157">
   <path class="kv35" d="M 1214.97 773.83 L 1214.97 588.25" stroke-width="1" zvalue="470"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="159@0" LinkObjectIDznd="158@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1214.97 773.83 L 1214.97 588.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="156">
   <path class="kv35" d="M 1213.97 538.06 L 1213.97 493.5" stroke-width="1" zvalue="471"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="158@0" LinkObjectIDznd="36@8" MaxPinNum="2"/>
   </metadata>
  <path d="M 1213.97 538.06 L 1213.97 493.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="152">
   <path class="kv35" d="M 1186.59 629.42 L 1186.59 608 L 1214.97 608" stroke-width="1" zvalue="475"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="155@0" LinkObjectIDznd="157" MaxPinNum="2"/>
   </metadata>
  <path d="M 1186.59 629.42 L 1186.59 608 L 1214.97 608" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="151">
   <path class="kv35" d="M 1240.63 628.23 L 1240.63 610 L 1214.97 610" stroke-width="1" zvalue="476"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="154@0" LinkObjectIDznd="157" MaxPinNum="2"/>
   </metadata>
  <path d="M 1240.63 628.23 L 1240.63 610 L 1214.97 610" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="147">
   <path class="kv35" d="M 1364.98 773.83 L 1364.98 588.25" stroke-width="1" zvalue="481"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="150@0" LinkObjectIDznd="149@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1364.98 773.83 L 1364.98 588.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="146">
   <path class="kv35" d="M 1363.98 538.06 L 1363.98 493.5" stroke-width="1" zvalue="482"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="149@0" LinkObjectIDznd="36@7" MaxPinNum="2"/>
   </metadata>
  <path d="M 1363.98 538.06 L 1363.98 493.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="139">
   <path class="kv35" d="M 1336.59 629.42 L 1336.59 608 L 1364.98 608" stroke-width="1" zvalue="486"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="145@0" LinkObjectIDznd="147" MaxPinNum="2"/>
   </metadata>
  <path d="M 1336.59 629.42 L 1336.59 608 L 1364.98 608" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="138">
   <path class="kv35" d="M 1390.63 628.23 L 1390.63 610 L 1364.98 610" stroke-width="1" zvalue="487"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="140@0" LinkObjectIDznd="147" MaxPinNum="2"/>
   </metadata>
  <path d="M 1390.63 628.23 L 1390.63 610 L 1364.98 610" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="135">
   <path class="kv35" d="M 1520.98 773.83 L 1520.98 588.25" stroke-width="1" zvalue="492"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="137@0" LinkObjectIDznd="136@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1520.98 773.83 L 1520.98 588.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="134">
   <path class="kv35" d="M 1519.98 538.06 L 1519.98 493.5" stroke-width="1" zvalue="493"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="136@0" LinkObjectIDznd="36@6" MaxPinNum="2"/>
   </metadata>
  <path d="M 1519.98 538.06 L 1519.98 493.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="130">
   <path class="kv35" d="M 1492.59 629.42 L 1492.59 608 L 1520.98 608" stroke-width="1" zvalue="497"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="133@0" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 1492.59 629.42 L 1492.59 608 L 1520.98 608" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="129">
   <path class="kv35" d="M 1546.63 628.23 L 1546.63 610 L 1520.98 610" stroke-width="1" zvalue="498"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="131@0" LinkObjectIDznd="135" MaxPinNum="2"/>
   </metadata>
  <path d="M 1546.63 628.23 L 1546.63 610 L 1520.98 610" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="126">
   <path class="kv35" d="M 1674.98 773.83 L 1674.98 588.25" stroke-width="1" zvalue="503"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="128@0" LinkObjectIDznd="127@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1674.98 773.83 L 1674.98 588.25" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="125">
   <path class="kv35" d="M 1673.98 538.06 L 1673.98 493.5" stroke-width="1" zvalue="504"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="127@0" LinkObjectIDznd="36@5" MaxPinNum="2"/>
   </metadata>
  <path d="M 1673.98 538.06 L 1673.98 493.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="122">
   <path class="kv35" d="M 1646.59 629.42 L 1646.59 608 L 1674.98 608" stroke-width="1" zvalue="508"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="124@0" LinkObjectIDznd="126" MaxPinNum="2"/>
   </metadata>
  <path d="M 1646.59 629.42 L 1646.59 608 L 1674.98 608" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="121">
   <path class="kv35" d="M 1700.63 628.23 L 1700.63 610 L 1674.98 610" stroke-width="1" zvalue="509"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="123@0" LinkObjectIDznd="126" MaxPinNum="2"/>
   </metadata>
  <path d="M 1700.63 628.23 L 1700.63 610 L 1674.98 610" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="65">
   <path class="kv35" d="M 1134.44 378.81 L 1134.44 398.81" stroke-width="1" zvalue="516"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="27@0" LinkObjectIDznd="63@1" MaxPinNum="2"/>
   </metadata>
  <path d="M 1134.44 378.81 L 1134.44 398.81" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
  <g id="66">
   <path class="kv35" d="M 1134.44 441.94 L 1134.44 493.5" stroke-width="1" zvalue="517"/>
   <metadata>
    <cge:CN_Ref LinkObjectIDnd="63@0" LinkObjectIDznd="36@9" MaxPinNum="2"/>
   </metadata>
  <path d="M 1134.44 441.94 L 1134.44 493.5" fill="none" opacity="0" stroke="white" stroke-width="10" transform=""/></g>
 </g>
 <g id="EnergyConsumerClass">
  <g id="10">
   <use class="kv35" height="35" transform="rotate(0,591.5,820.375) scale(2.85,2.85) translate(-365.456,-500.149)" width="20" x="563" xlink:href="#EnergyConsumer:Y-d厂用变_0" y="770.5" zvalue="395"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449813413894" ObjectName="35kV1号主变"/>
   <cge:TPSR_Ref TObjectID="6192449813413894"/></metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,591.5,820.375) scale(2.85,2.85) translate(-365.456,-500.149)" width="20" x="563" y="770.5"/></g>
  <g id="61">
   <use class="kv35" height="35" transform="rotate(0,741.5,820.375) scale(2.85,2.85) translate(-462.825,-500.149)" width="20" x="713" xlink:href="#EnergyConsumer:Y-d厂用变_0" y="770.5" zvalue="407"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449813872646" ObjectName="35kV2号主变"/>
   <cge:TPSR_Ref TObjectID="6192449813872646"/></metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,741.5,820.375) scale(2.85,2.85) translate(-462.825,-500.149)" width="20" x="713" y="770.5"/></g>
  <g id="96">
   <use class="kv35" height="35" transform="rotate(0,897.5,820.375) scale(2.85,2.85) translate(-564.088,-500.149)" width="20" x="869" xlink:href="#EnergyConsumer:Y-d厂用变_0" y="770.5" zvalue="442"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449814134789" ObjectName="35kV3号主变"/>
   <cge:TPSR_Ref TObjectID="6192449814134789"/></metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,897.5,820.375) scale(2.85,2.85) translate(-564.088,-500.149)" width="20" x="869" y="770.5"/></g>
  <g id="107">
   <use class="kv35" height="35" transform="rotate(0,1051.5,820.375) scale(2.85,2.85) translate(-664.053,-500.149)" width="20" x="1023" xlink:href="#EnergyConsumer:Y-d厂用变_0" y="770.5" zvalue="454"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449814396933" ObjectName="35kV4号主变"/>
   <cge:TPSR_Ref TObjectID="6192449814396933"/></metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1051.5,820.375) scale(2.85,2.85) translate(-664.053,-500.149)" width="20" x="1023" y="770.5"/></g>
  <g id="159">
   <use class="kv35" height="35" transform="rotate(0,1214.5,820.375) scale(2.85,2.85) translate(-769.86,-500.149)" width="20" x="1186" xlink:href="#EnergyConsumer:Y-d厂用变_0" y="770.5" zvalue="466"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449815445510" ObjectName="35kV5号主变"/>
   <cge:TPSR_Ref TObjectID="6192449815445510"/></metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1214.5,820.375) scale(2.85,2.85) translate(-769.86,-500.149)" width="20" x="1186" y="770.5"/></g>
  <g id="150">
   <use class="kv35" height="35" transform="rotate(0,1364.5,820.375) scale(2.85,2.85) translate(-867.228,-500.149)" width="20" x="1336" xlink:href="#EnergyConsumer:Y-d厂用变_0" y="770.5" zvalue="477"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449815183366" ObjectName="35kV6号主变"/>
   <cge:TPSR_Ref TObjectID="6192449815183366"/></metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1364.5,820.375) scale(2.85,2.85) translate(-867.228,-500.149)" width="20" x="1336" y="770.5"/></g>
  <g id="137">
   <use class="kv35" height="35" transform="rotate(0,1520.5,820.375) scale(2.85,2.85) translate(-968.491,-500.149)" width="20" x="1492" xlink:href="#EnergyConsumer:Y-d厂用变_0" y="770.5" zvalue="488"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449814921222" ObjectName="35kV7号主变"/>
   <cge:TPSR_Ref TObjectID="6192449814921222"/></metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1520.5,820.375) scale(2.85,2.85) translate(-968.491,-500.149)" width="20" x="1492" y="770.5"/></g>
  <g id="128">
   <use class="kv35" height="35" transform="rotate(0,1674.5,820.375) scale(2.85,2.85) translate(-1068.46,-500.149)" width="20" x="1646" xlink:href="#EnergyConsumer:Y-d厂用变_0" y="770.5" zvalue="499"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449814659078" ObjectName="35kV8号主变"/>
   <cge:TPSR_Ref TObjectID="6192449814659078"/></metadata>
  <rect fill="white" height="35" opacity="0" stroke="white" transform="rotate(0,1674.5,820.375) scale(2.85,2.85) translate(-1068.46,-500.149)" width="20" x="1646" y="770.5"/></g>
 </g>
 <g id="GroundDisconnectorClass">
  <g id="32">
   <use class="kv35" height="20" transform="rotate(0,563.5,646) scale(1.7,1.7) translate(-228.529,-259)" width="10" x="555" xlink:href="#GroundDisconnector:地刀_0" y="629" zvalue="401"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449813544966" ObjectName="35kV1号主变35kV侧30167接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449813544966"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,563.5,646) scale(1.7,1.7) translate(-228.529,-259)" width="10" x="555" y="629"/></g>
  <g id="56">
   <use class="kv35" height="20" transform="rotate(0,713.5,646) scale(1.7,1.7) translate(-290.294,-259)" width="10" x="705" xlink:href="#GroundDisconnector:地刀_0" y="629" zvalue="413"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449813807110" ObjectName="35kV2号主变35kV侧30267接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449813807110"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,713.5,646) scale(1.7,1.7) translate(-290.294,-259)" width="10" x="705" y="629"/></g>
  <g id="91">
   <use class="kv35" height="20" transform="rotate(0,869.5,646) scale(1.7,1.7) translate(-354.529,-259)" width="10" x="861" xlink:href="#GroundDisconnector:地刀_0" y="629" zvalue="448"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449814069254" ObjectName="35kV3号主变35kV侧30367接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449814069254"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,869.5,646) scale(1.7,1.7) translate(-354.529,-259)" width="10" x="861" y="629"/></g>
  <g id="103">
   <use class="kv35" height="20" transform="rotate(0,1023.5,646) scale(1.7,1.7) translate(-417.941,-259)" width="10" x="1015" xlink:href="#GroundDisconnector:地刀_0" y="629" zvalue="460"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449814331397" ObjectName="35kV4号主变35kV侧30467接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449814331397"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1023.5,646) scale(1.7,1.7) translate(-417.941,-259)" width="10" x="1015" y="629"/></g>
  <g id="155">
   <use class="kv35" height="20" transform="rotate(0,1186.5,646) scale(1.7,1.7) translate(-485.059,-259)" width="10" x="1178" xlink:href="#GroundDisconnector:地刀_0" y="629" zvalue="472"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449815379974" ObjectName="35kV5号主变35kV侧30567接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449815379974"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1186.5,646) scale(1.7,1.7) translate(-485.059,-259)" width="10" x="1178" y="629"/></g>
  <g id="145">
   <use class="kv35" height="20" transform="rotate(0,1336.5,646) scale(1.7,1.7) translate(-546.824,-259)" width="10" x="1328" xlink:href="#GroundDisconnector:地刀_0" y="629" zvalue="483"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449815117830" ObjectName="35kV6号主变35kV侧30667接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449815117830"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1336.5,646) scale(1.7,1.7) translate(-546.824,-259)" width="10" x="1328" y="629"/></g>
  <g id="133">
   <use class="kv35" height="20" transform="rotate(0,1492.5,646) scale(1.7,1.7) translate(-611.059,-259)" width="10" x="1484" xlink:href="#GroundDisconnector:地刀_0" y="629" zvalue="494"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449814855686" ObjectName="35kV7号主变35kV侧30767接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449814855686"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1492.5,646) scale(1.7,1.7) translate(-611.059,-259)" width="10" x="1484" y="629"/></g>
  <g id="124">
   <use class="kv35" height="20" transform="rotate(0,1646.5,646) scale(1.7,1.7) translate(-674.471,-259)" width="10" x="1638" xlink:href="#GroundDisconnector:地刀_0" y="629" zvalue="505"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449814593542" ObjectName="35kV8号主变35kV侧30867接地开关"/>
   <cge:TPSR_Ref TObjectID="6192449814593542"/></metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1646.5,646) scale(1.7,1.7) translate(-674.471,-259)" width="10" x="1638" y="629"/></g>
 </g>
 <g id="AccessoryClass">
  <g id="40">
   <use class="kv35" height="20" transform="rotate(0,617.625,646.25) scale(1.8375,2.575) translate(-273.128,-379.529)" width="20" x="599.25" xlink:href="#Accessory:线路PT3_0" y="620.5" zvalue="402"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449813610502" ObjectName="35kV1号主变避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,617.625,646.25) scale(1.8375,2.575) translate(-273.128,-379.529)" width="20" x="599.25" y="620.5"/></g>
  <g id="55">
   <use class="kv35" height="20" transform="rotate(0,767.625,646.25) scale(1.8375,2.575) translate(-341.495,-379.529)" width="20" x="749.25" xlink:href="#Accessory:线路PT3_0" y="620.5" zvalue="414"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449813676038" ObjectName="35kV2号主变避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,767.625,646.25) scale(1.8375,2.575) translate(-341.495,-379.529)" width="20" x="749.25" y="620.5"/></g>
  <g id="90">
   <use class="kv35" height="20" transform="rotate(0,923.625,646.25) scale(1.8375,2.575) translate(-412.597,-379.529)" width="20" x="905.25" xlink:href="#Accessory:线路PT3_0" y="620.5" zvalue="449"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449813938182" ObjectName="35kV3号主变避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,923.625,646.25) scale(1.8375,2.575) translate(-412.597,-379.529)" width="20" x="905.25" y="620.5"/></g>
  <g id="102">
   <use class="kv35" height="20" transform="rotate(0,1077.62,646.25) scale(1.8375,2.575) translate(-482.787,-379.529)" width="20" x="1059.25" xlink:href="#Accessory:线路PT3_0" y="620.5" zvalue="461"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449814200325" ObjectName="35kV4号主变避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1077.62,646.25) scale(1.8375,2.575) translate(-482.787,-379.529)" width="20" x="1059.25" y="620.5"/></g>
  <g id="154">
   <use class="kv35" height="20" transform="rotate(0,1240.62,646.25) scale(1.8375,2.575) translate(-557.08,-379.529)" width="20" x="1222.25" xlink:href="#Accessory:线路PT3_0" y="620.5" zvalue="473"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449815248902" ObjectName="35kV5号主变避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1240.62,646.25) scale(1.8375,2.575) translate(-557.08,-379.529)" width="20" x="1222.25" y="620.5"/></g>
  <g id="140">
   <use class="kv35" height="20" transform="rotate(0,1390.62,646.25) scale(1.8375,2.575) translate(-625.447,-379.529)" width="20" x="1372.25" xlink:href="#Accessory:线路PT3_0" y="620.5" zvalue="484"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449814986758" ObjectName="35kV6号主变避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1390.62,646.25) scale(1.8375,2.575) translate(-625.447,-379.529)" width="20" x="1372.25" y="620.5"/></g>
  <g id="131">
   <use class="kv35" height="20" transform="rotate(0,1546.62,646.25) scale(1.8375,2.575) translate(-696.549,-379.529)" width="20" x="1528.25" xlink:href="#Accessory:线路PT3_0" y="620.5" zvalue="495"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449814724614" ObjectName="35kV7号主变避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1546.62,646.25) scale(1.8375,2.575) translate(-696.549,-379.529)" width="20" x="1528.25" y="620.5"/></g>
  <g id="123">
   <use class="kv35" height="20" transform="rotate(0,1700.62,646.25) scale(1.8375,2.575) translate(-766.74,-379.529)" width="20" x="1682.25" xlink:href="#Accessory:线路PT3_0" y="620.5" zvalue="506"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449814462470" ObjectName="35kV8号主变避雷器"/>
   </metadata>
  <rect fill="white" height="20" opacity="0" stroke="white" transform="rotate(0,1700.62,646.25) scale(1.8375,2.575) translate(-766.74,-379.529)" width="20" x="1682.25" y="620.5"/></g>
  <g id="27">
   <use class="kv35" height="30" transform="rotate(0,1134.28,346) scale(2.44987,2.5) translate(-645.914,-185.1)" width="35" x="1091.411753887364" xlink:href="#Accessory:10kV母线PT带消谐装置_0" y="308.5" zvalue="513"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449815511046" ObjectName="35kV母线电压互感器"/>
   </metadata>
  <rect fill="white" height="30" opacity="0" stroke="white" transform="rotate(0,1134.28,346) scale(2.44987,2.5) translate(-645.914,-185.1)" width="35" x="1091.411753887364" y="308.5"/></g>
 </g>
 <g id="DisconnectorClass">
  <g id="63">
   <use class="kv35" height="42" transform="rotate(0,1138.5,424.75) scale(1.25,-1.25) translate(-223.95,-759.3)" width="30" x="1119.75" xlink:href="#Disconnector:手车刀闸带避雷器_0" y="398.5" zvalue="515"/>
   <metadata>
    <cge:PSR_Ref ObjectID="6192449815576582" ObjectName="35kV母线电压互感器3901隔离开关"/>
   <cge:TPSR_Ref TObjectID="6192449815576582"/></metadata>
  <rect fill="white" height="42" opacity="0" stroke="white" transform="rotate(0,1138.5,424.75) scale(1.25,-1.25) translate(-223.95,-759.3)" width="30" x="1119.75" y="398.5"/></g>
 </g>
</svg>